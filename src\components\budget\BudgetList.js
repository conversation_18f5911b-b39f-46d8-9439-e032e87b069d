/**
 * Bütçe Listesi Bileşeni
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md Stage 1 implementasyonu
 * 
 * Bütçe listesini görüntülemek için kullanılan liste bileşeni
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useAppContext } from '../../context/AppContext';
import BudgetCard from './BudgetCard';

/**
 * Bütçe listesi bileşeni
 * @param {Object} props - Bileşen özellikleri
 * @param {Array} props.budgets - Bütçe listesi
 * @param {boolean} props.loading - Yükleme durumu
 * @param {boolean} props.refreshing - Yenileme durumu
 * @param {Function} props.onRefresh - Yenileme fonksiyonu
 * @param {Function} props.onBudgetPress - Bütçe tıklama fonksiyonu
 * @param {Function} props.onEditBudget - Bütçe düzenleme fonksiyonu
 * @param {Function} props.onDeleteBudget - Bütçe silme fonksiyonu
 * @param {Function} props.onAddBudget - Yeni bütçe ekleme fonksiyonu
 * @returns {JSX.Element} Bütçe listesi
 */
const BudgetList = ({
  budgets = [],
  loading = false,
  refreshing = false,
  onRefresh,
  onBudgetPress,
  onEditBudget,
  onDeleteBudget,
  onAddBudget,
  style
}) => {
  const { theme, isDarkMode } = useAppContext();
  const [filter, setFilter] = useState('all'); // all, active, completed

  // Filtrelenmiş bütçeler
  const filteredBudgets = budgets.filter(budget => {
    switch (filter) {
      case 'active':
        return budget.status === 'active';
      case 'completed':
        return budget.status === 'completed';
      case 'paused':
        return budget.status === 'paused';
      default:
        return true;
    }
  });

  // Filtre butonları
  const filterOptions = [
    { key: 'all', label: 'Tümü', count: budgets.length },
    { key: 'active', label: 'Aktif', count: budgets.filter(b => b.status === 'active').length },
    { key: 'completed', label: 'Tamamlanan', count: budgets.filter(b => b.status === 'completed').length },
    { key: 'paused', label: 'Duraklatılan', count: budgets.filter(b => b.status === 'paused').length }
  ];

  // Yükleme durumu
  if (loading) {
    return (
      <View style={[styles.centerContainer, { backgroundColor: theme.BACKGROUND }]}>
        <ActivityIndicator size="large" color={theme.PRIMARY} />
        <Text style={[styles.loadingText, { color: theme.TEXT_SECONDARY }]}>
          Bütçeler yükleniyor...
        </Text>
      </View>
    );
  }

  // Boş liste durumu
  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <MaterialIcons 
        name="account-balance-wallet" 
        size={64} 
        color={theme.TEXT_SECONDARY} 
      />
      <Text style={[styles.emptyTitle, { color: theme.TEXT_PRIMARY }]}>
        Henüz bütçe yok
      </Text>
      <Text style={[styles.emptyDescription, { color: theme.TEXT_SECONDARY }]}>
        İlk bütçenizi oluşturmak için aşağıdaki butona tıklayın
      </Text>
      <TouchableOpacity
        style={[styles.addButton, { backgroundColor: theme.PRIMARY }]}
        onPress={onAddBudget}
      >
        <MaterialIcons name="add" size={24} color={theme.WHITE} />
        <Text style={[styles.addButtonText, { color: theme.WHITE }]}>
          Bütçe Oluştur
        </Text>
      </TouchableOpacity>
    </View>
  );

  // Liste öğesi render
  const renderBudgetItem = ({ item }) => (
    <BudgetCard
      budget={item}
      onPress={() => onBudgetPress?.(item)}
      onEdit={() => onEditBudget?.(item)}
      onDelete={() => onDeleteBudget?.(item)}
    />
  );

  // Liste başlığı
  const renderHeader = () => (
    <View style={styles.headerContainer}>
      {/* Filtre Butonları */}
      <View style={styles.filterContainer}>
        {filterOptions.map((option) => (
          <TouchableOpacity
            key={option.key}
            style={[
              styles.filterButton,
              {
                backgroundColor: filter === option.key ? theme.PRIMARY : theme.SURFACE,
                borderColor: filter === option.key ? theme.PRIMARY : theme.BORDER,
              }
            ]}
            onPress={() => setFilter(option.key)}
          >
            <Text style={[
              styles.filterButtonText,
              {
                color: filter === option.key ? theme.WHITE : theme.TEXT_PRIMARY
              }
            ]}>
              {option.label}
            </Text>
            {option.count > 0 && (
              <View style={[
                styles.filterBadge,
                {
                  backgroundColor: filter === option.key ? theme.WHITE : theme.PRIMARY
                }
              ]}>
                <Text style={[
                  styles.filterBadgeText,
                  {
                    color: filter === option.key ? theme.PRIMARY : theme.WHITE
                  }
                ]}>
                  {option.count}
                </Text>
              </View>
            )}
          </TouchableOpacity>
        ))}
      </View>

      {/* Özet Bilgiler */}
      {filteredBudgets.length > 0 && (
        <View style={[styles.summaryContainer, { backgroundColor: theme.SURFACE }]}>
          <View style={styles.summaryItem}>
            <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>
              Toplam Bütçe
            </Text>
            <Text style={[styles.summaryValue, { color: theme.TEXT_PRIMARY }]}>
              {filteredBudgets.length}
            </Text>
          </View>
          <View style={styles.summaryItem}>
            <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>
              Aktif
            </Text>
            <Text style={[styles.summaryValue, { color: theme.SUCCESS }]}>
              {filteredBudgets.filter(b => b.status === 'active').length}
            </Text>
          </View>
          <View style={styles.summaryItem}>
            <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>
              Limit Aşan
            </Text>
            <Text style={[styles.summaryValue, { color: theme.DANGER }]}>
              {filteredBudgets.filter(b => b.is_over_budget).length}
            </Text>
          </View>
        </View>
      )}
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.BACKGROUND }, style]}>
      <FlatList
        data={filteredBudgets}
        renderItem={renderBudgetItem}
        keyExtractor={(item) => item.id.toString()}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmptyState}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.PRIMARY]}
            tintColor={theme.PRIMARY}
          />
        }
        showsVerticalScrollIndicator={false}
        contentContainerStyle={
          filteredBudgets.length === 0 ? styles.emptyContentContainer : styles.contentContainer
        }
      />

      {/* Floating Action Button */}
      {filteredBudgets.length > 0 && (
        <TouchableOpacity
          style={[styles.fab, { backgroundColor: theme.PRIMARY }]}
          onPress={onAddBudget}
        >
          <MaterialIcons name="add" size={24} color={theme.WHITE} />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  headerContainer: {
    padding: 16,
  },
  filterContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    gap: 8,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    gap: 6,
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  filterBadge: {
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    minWidth: 20,
    alignItems: 'center',
  },
  filterBadgeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  summaryContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 16,
    borderRadius: 12,
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: '600',
  },
  contentContainer: {
    paddingBottom: 80,
  },
  emptyContentContainer: {
    flexGrow: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 25,
    gap: 8,
  },
  addButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  fab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
});

export default BudgetList;
