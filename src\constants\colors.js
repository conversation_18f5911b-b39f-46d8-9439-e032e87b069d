/**
 * <PERSON>y<PERSON>lama genelinde kullanılacak merkezi renk sistemi.
 * Bu dosya tüm renk tanımlamalarını içerir ve global bir Colors nesnesi oluşturur.
 */

// Ana renk paleti - Pastel ve modern renkler
export const palette = {
  white: '#FFFFFF',
  black: '#000000',
  transparent: 'transparent',

  // Pastel mavi
  blue: {
    50: '#e6f3ff',
    100: '#cce7ff',
    200: '#99ceff',
    300: '#66b5ff',
    400: '#339cff',
    500: '#6c5ce7', // primary - daha modern mor-mavi
    600: '#5a4bd1', // primary-dark
    700: '#483abb',
    800: '#3629a5',
    900: '#24188f'
  },

  // Pastel yeşil
  green: {
    50: '#e6fff2',
    100: '#ccffe6',
    200: '#99ffcc',
    300: '#66ffb3',
    400: '#33ff99',
    500: '#00cec9', // success - turkuaz
    600: '#00b5a7', // success-dark
    700: '#009c8f',
    800: '#008377',
    900: '#006a5f'
  },

  // Pastel kırmızı
  red: {
    50: '#ffe6e6',
    100: '#ffcccc',
    200: '#ff9999',
    300: '#ff6666',
    400: '#ff3333',
    500: '#ff7675', // danger - yumuşak kırmızı
    600: '#e84a5f', // danger-dark
    700: '#d13b4f',
    800: '#ba2c3f',
    900: '#a31d2f'
  },

  // Pastel sarı/turuncu
  yellow: {
    50: '#fff9e6',
    100: '#fff3cc',
    200: '#ffe799',
    300: '#ffdb66',
    400: '#ffcf33',
    500: '#fdcb6e', // warning - pastel sarı
    600: '#fab1a0', // warning-dark - şeftali
    700: '#f3a683',
    800: '#e9815e',
    900: '#e05c3b'
  },

  // Gri tonları
  gray: {
    50: '#f8f9fa',
    100: '#f1f3f5',
    200: '#e9ecef',
    300: '#dee2e6',
    400: '#ced4da',
    500: '#adb5bd',
    600: '#868e96',
    700: '#495057',
    800: '#343a40',
    900: '#212529'
  },

  // Karanlık mod için gri tonları
  darkGray: {
    50: '#1a1a1a',
    100: '#2d2d2d',
    150: '#363636', // Yeni ara ton
    200: '#404040',
    300: '#525252',
    400: '#666666',
    500: '#808080',
    600: '#999999',
    700: '#b3b3b3',
    800: '#cccccc',
    900: '#e6e6e6'
  },

  // Ek pastel renkler
  pastel: {
    purple: '#a29bfe',
    pink: '#fd79a8',
    mint: '#55efc4',
    lavender: '#dfe6e9',
    peach: '#ffeaa7',
    coral: '#fab1a0'
  }
};

/**
 * Açık tema renkleri
 */
export const lightTheme = {
  // Temel renkler
  WHITE: palette.white,
  BLACK: palette.black,
  TRANSPARENT: palette.transparent,

  // Ana renkler
  PRIMARY: palette.blue[500],
  PRIMARY_LIGHT: palette.blue[400],
  PRIMARY_DARK: palette.blue[600],

  // Anlamsal renkler
  SUCCESS: palette.green[500],
  SUCCESS_LIGHT: palette.green[400],
  SUCCESS_DARK: palette.green[600],

  DANGER: palette.red[500],
  DANGER_LIGHT: palette.red[400],
  DANGER_DARK: palette.red[600],

  WARNING: palette.yellow[500],
  WARNING_LIGHT: palette.yellow[400],
  WARNING_DARK: palette.yellow[600],

  INFO: palette.blue[500],
  INFO_LIGHT: palette.blue[400],
  INFO_DARK: palette.blue[600],

  // Arka plan renkleri
  BACKGROUND: palette.white,
  SURFACE: palette.gray[50],
  SURFACE_VARIANT: palette.gray[100],
  CARD: palette.white,

  // Metin renkleri
  TEXT_PRIMARY: palette.gray[900],
  TEXT_SECONDARY: palette.gray[600],
  TEXT_DISABLED: palette.gray[400],

  // Kenarlık renkleri
  BORDER: palette.gray[200],
  DIVIDER: palette.gray[100],
  SHADOW: palette.black,

  // Metin üzerinde gösterilecek renkler
  ON_PRIMARY: palette.white,
  ON_SURFACE: palette.gray[900],
  ON_BACKGROUND: palette.gray[900],

  // Gri tonları
  GRAY_50: palette.gray[50],
  GRAY_100: palette.gray[100],
  GRAY_200: palette.gray[200],
  GRAY_300: palette.gray[300],
  GRAY_400: palette.gray[400],
  GRAY_500: palette.gray[500],
  GRAY_600: palette.gray[600],
  GRAY_700: palette.gray[700],
  GRAY_800: palette.gray[800],
  GRAY_900: palette.gray[900],

  // İşlem renkleri
  INCOME: palette.green[500],
  INCOME_LIGHT: palette.green[400],
  INCOME_DARK: palette.green[600],

  EXPENSE: palette.red[500],
  EXPENSE_LIGHT: palette.red[400],
  EXPENSE_DARK: palette.red[600],

  // Modern colors objesi - bazı bileşenler için
  colors: {
    background: palette.white,
    backgroundSecondary: palette.gray[50],
    surface: palette.gray[50],
    card: palette.white,
    text: palette.gray[900],
    textSecondary: palette.gray[600],
    textDisabled: palette.gray[400],
    border: palette.gray[200],
    divider: palette.gray[100],
    primary: palette.blue[500],
    primaryLight: palette.blue[400],
    primaryDark: palette.blue[600],
    success: palette.green[500],
    danger: palette.red[500],
    warning: palette.yellow[500],
    info: palette.blue[500],
    income: palette.green[500],
    expense: palette.red[500],
    white: palette.white,
    error: palette.red[500],
  }
};

/**
 * Karanlık tema renkleri
 */
export const darkTheme = {
  // Temel renkler
  WHITE: palette.darkGray[50],
  BLACK: palette.white,
  TRANSPARENT: palette.transparent,

  // Ana renkler
  PRIMARY: palette.blue[400],
  PRIMARY_LIGHT: palette.blue[300],
  PRIMARY_DARK: palette.blue[500],

  // Anlamsal renkler
  SUCCESS: palette.green[400],
  SUCCESS_LIGHT: palette.green[300],
  SUCCESS_DARK: palette.green[500],

  DANGER: palette.red[400],
  DANGER_LIGHT: palette.red[300],
  DANGER_DARK: palette.red[500],

  WARNING: palette.yellow[400],
  WARNING_LIGHT: palette.yellow[300],
  WARNING_DARK: palette.yellow[500],

  INFO: palette.blue[400],
  INFO_LIGHT: palette.blue[300],
  INFO_DARK: palette.blue[500],

  // Arka plan renkleri
  BACKGROUND: palette.darkGray[50],
  SURFACE: palette.darkGray[100],
  SURFACE_VARIANT: palette.darkGray[150], // Daha açık yaptık
  CARD: palette.darkGray[100],

  // Metin renkleri
  TEXT_PRIMARY: palette.darkGray[900],
  TEXT_SECONDARY: palette.darkGray[700],
  TEXT_DISABLED: palette.darkGray[500],

  // Kenarlık renkleri
  BORDER: palette.darkGray[300],
  DIVIDER: palette.darkGray[200],
  SHADOW: palette.black,

  // Metin üzerinde gösterilecek renkler
  ON_PRIMARY: palette.darkGray[50],
  ON_SURFACE: palette.darkGray[900],
  ON_BACKGROUND: palette.darkGray[900],

  // Gri tonları
  GRAY_50: palette.darkGray[50],
  GRAY_100: palette.darkGray[100],
  GRAY_200: palette.darkGray[200],
  GRAY_300: palette.darkGray[300],
  GRAY_400: palette.darkGray[400],
  GRAY_500: palette.darkGray[500],
  GRAY_600: palette.darkGray[600],
  GRAY_700: palette.darkGray[700],
  GRAY_800: palette.darkGray[800],
  GRAY_900: palette.darkGray[900],

  // İşlem renkleri
  INCOME: palette.green[400],
  INCOME_LIGHT: palette.green[300],
  INCOME_DARK: palette.green[500],

  EXPENSE: palette.red[400],
  EXPENSE_LIGHT: palette.red[300],
  EXPENSE_DARK: palette.red[500],

  // Modern colors objesi - bazı bileşenler için
  colors: {
    background: palette.darkGray[50],
    backgroundSecondary: palette.darkGray[100],
    surface: palette.darkGray[100],
    card: palette.darkGray[100],
    text: palette.darkGray[900],
    textSecondary: palette.darkGray[700],
    textDisabled: palette.darkGray[500],
    border: palette.darkGray[300],
    divider: palette.darkGray[200],
    primary: palette.blue[400],
    primaryLight: palette.blue[300],
    primaryDark: palette.blue[500],
    success: palette.green[400],
    danger: palette.red[400],
    warning: palette.yellow[400],
    info: palette.blue[400],
    income: palette.green[400],
    expense: palette.red[400],
    white: palette.white,
    error: palette.red[400],
  }
};

/**
 * Varsayılan renk sistemi (açık tema)
 */
export const Colors = {
  ...lightTheme,

  // Tema uyumluluğu için eski stil renk nesnesi
  common: {
    white: palette.white,
    black: palette.black,
    transparent: palette.transparent
  },

  // Pastel renkler
  pastel: palette.pastel,

  primary: {
    main: palette.blue[500],
    light: palette.blue[400],
    dark: palette.blue[600]
  },

  neutral: palette.gray,

  income: {
    main: palette.green[500],
    light: palette.green[400],
    dark: palette.green[600]
  },

  expense: {
    main: palette.red[500],
    light: palette.red[400],
    dark: palette.red[600]
  },

  info: {
    main: palette.blue[500],
    light: palette.blue[400],
    dark: palette.blue[600]
  },

  warning: {
    main: palette.yellow[500],
    light: palette.yellow[400],
    dark: palette.yellow[600]
  },

  success: {
    main: palette.green[500],
    light: palette.green[400],
    dark: palette.green[600]
  },

  error: {
    main: palette.red[500],
    light: palette.red[400],
    dark: palette.red[600]
  },

  danger: {
    main: palette.red[500],
    light: palette.red[400],
    dark: palette.red[600]
  },

  // Chart renkleri
  CHART_COLORS: [
    '#4CAF50', // Yeşil
    '#2196F3', // Mavi
    '#FF9800', // Turuncu
    '#9C27B0', // Mor
    '#F44336', // Kırmızı
    '#00BCD4', // Cyan
    '#FFEB3B', // Sarı
    '#795548', // Kahverengi
    '#607D8B', // Blue Grey
    '#E91E63', // Pink
    '#3F51B5', // Indigo
    '#009688', // Teal
  ]
};

// Immediately set global Colors
if (typeof global !== 'undefined') {
  global.Colors = Colors;
}

// Eski tema sistemleri için uyumluluk sağlayan nesne
export const COLORS = Colors;

export default Colors;