import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Share
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import * as budgetService from '../services/budgetService';
import * as budgetReportService from '../services/budgetReportService';
import * as budgetAlertService from '../services/budgetAlertService';
import * as exchangeRateService from '../services/exchangeRateService';
import { formatCurrency, getCurrencySymbol } from '../utils/formatters';
import { formatDate } from '../utils/dateFormatters';

/**
 * Bütçe Detay Ekranı
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @param {Object} props.route - Route nesnesi
 * @returns {JSX.Element} Bütçe Detay Ekranı
 */
export default function BudgetDetailScreen({ navigation, route }) {
  const db = useSQLiteContext();
  const { budgetId } = route.params;

  // Durum değişkenleri
  const [loading, setLoading] = useState(true);
  const [budget, setBudget] = useState(null);
  const [transactions, setTransactions] = useState([]);

  // Verileri yükle
  const loadData = useCallback(async () => {
    try {
      setLoading(true);

      // Bütçe detaylarını getir
      const budgetDetails = await budgetService.getBudgetDetails(db, budgetId);
      setBudget(budgetDetails);

      // Bütçe ile ilgili işlemleri getir
      const budgetTransactions = await budgetService.getBudgetTransactions(db, budgetId);
      setTransactions(budgetTransactions);

      setLoading(false);
    } catch (error) {
      console.error('Bütçe detayları yükleme hatası:', error);
      Alert.alert('Hata', 'Bütçe detayları yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  }, [budgetId, db]);

  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [loadData])
  );

  // Bütçeyi düzenle
  const editBudget = () => {
    navigation.navigate('BudgetForm', { budget });
  };

  // Bütçeyi sil
  const deleteBudget = () => {
    Alert.alert(
      'Bütçeyi Sil',
      'Bu bütçeyi silmek istediğinize emin misiniz? Bu işlem geri alınamaz.',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await budgetService.deleteBudget(db, budgetId);
              Alert.alert('Başarılı', 'Bütçe başarıyla silindi.');
              navigation.goBack();
            } catch (error) {
              console.error('Bütçe silme hatası:', error);
              Alert.alert('Hata', 'Bütçe silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };

  // Bütçe raporu oluştur
  const createBudgetReport = async () => {
    if (!budget) return;

    try {
      // Bütçe bilgilerini getir
      const budgetData = await budgetService.getBudgetById(db, budget.id);
      if (!budgetData) {
        Alert.alert('Hata', 'Bütçe bulunamadı.');
        return;
      }

      // Rapor oluştur
      await budgetReportService.createBudgetReport(db, budgetData);
      Alert.alert('Başarılı', 'Bütçe raporu oluşturuldu.');
    } catch (error) {
      console.error('Bütçe raporu oluşturma hatası:', error);
      Alert.alert('Hata', 'Bütçe raporu oluşturulurken bir hata oluştu.');
    }
  };

  // Bütçe uyarısı ekle/düzenle
  const manageBudgetAlert = async () => {
    if (!budget) return;

    try {
      // Mevcut uyarıları getir
      const alerts = await budgetAlertService.getBudgetAlerts(db, budget.id);

      if (alerts.length > 0) {
        // Uyarıları göster ve düzenleme seçeneği sun
        Alert.alert(
          'Bütçe Uyarıları',
          `Bu bütçe için ${alerts.length} uyarı bulunuyor.`,
          [
            { text: 'İptal', style: 'cancel' },
            {
              text: 'Düzenle',
              onPress: () => navigation.navigate('BudgetAlertForm', { budgetId: budget.id })
            }
          ]
        );
      } else {
        // Yeni uyarı ekleme seçeneği sun
        Alert.alert(
          'Bütçe Uyarısı',
          'Bu bütçe için uyarı eklemek ister misiniz?',
          [
            { text: 'İptal', style: 'cancel' },
            {
              text: 'Ekle',
              onPress: () => navigation.navigate('BudgetAlertForm', { budgetId: budget.id })
            }
          ]
        );
      }
    } catch (error) {
      console.error('Bütçe uyarıları getirme hatası:', error);
      Alert.alert('Hata', 'Bütçe uyarıları alınırken bir hata oluştu.');
    }
  };

  // Bütçeyi paylaş
  const shareBudget = async () => {
    if (!budget) return;

    try {
      // Bütçe özeti oluştur
      let message = `Bütçe: ${budget.name}\n`;
      message += `Dönem: ${formatPeriod(budget.period)}\n`;
      message += `Tarih: ${formatDate(budget.start_date)}`;

      if (budget.end_date) {
        message += ` - ${formatDate(budget.end_date)}`;
      }

      const currency = budget.currency || 'TRY';
      message += `\n\nToplam Bütçe: ${formatCurrency(budget.totalBudget, currency)}`;
      message += `\nToplam Harcama: ${formatCurrency(budget.totalSpent, currency)}`;
      message += `\nKalan: ${formatCurrency(budget.totalRemaining, currency)}`;
      message += `\nKullanım: %${Math.round(budget.totalProgress)}`;

      message += '\n\nKategori Dağılımı:\n';

      budget.categories.forEach(category => {
        message += `- ${category.category_name}: ${formatCurrency(category.amount, currency)} (Harcanan: ${formatCurrency(category.spent_amount || 0, currency)})\n`;
      });

      // Paylaşım dialogunu aç
      await Share.share({
        message,
        title: `${budget.name} Bütçe Özeti`
      });
    } catch (error) {
      console.error('Bütçe paylaşım hatası:', error);
      Alert.alert('Hata', 'Bütçe paylaşılırken bir hata oluştu.');
    }
  };

  // Periyot formatını görüntüle
  const formatPeriod = (period) => {
    switch (period) {
      case 'daily': return 'Günlük';
      case 'weekly': return 'Haftalık';
      case 'monthly': return 'Aylık';
      case 'yearly': return 'Yıllık';
      default: return period;
    }
  };

  // İlerleme çubuğu rengi
  const getProgressColor = (progress) => {
    if (progress < 50) return Colors.SUCCESS;
    if (progress < 80) return Colors.WARNING;
    return Colors.DANGER;
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={styles.loadingText}>Bütçe detayları yükleniyor...</Text>
      </View>
    );
  }

  if (!budget) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <MaterialIcons name="error-outline" size={48} color={Colors.DANGER} />
        <Text style={styles.errorText}>Bütçe bulunamadı.</Text>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>Geri Dön</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{budget.name}</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerAction}
            onPress={shareBudget}
          >
            <MaterialIcons name="share" size={24} color="#fff" />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerAction}
            onPress={createBudgetReport}
          >
            <MaterialIcons name="assessment" size={24} color="#fff" />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerAction}
            onPress={manageBudgetAlert}
          >
            <MaterialIcons name="notifications" size={24} color="#fff" />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerAction}
            onPress={editBudget}
          >
            <MaterialIcons name="edit" size={24} color="#fff" />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerAction}
            onPress={deleteBudget}
          >
            <MaterialIcons name="delete" size={24} color="#fff" />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.budgetInfoCard}>
          <View style={styles.budgetPeriod}>
            <MaterialIcons name="date-range" size={20} color={Colors.GRAY_600} />
            <Text style={styles.budgetPeriodText}>
              {formatPeriod(budget.period)} ({formatDate(budget.start_date)}
              {budget.end_date ? ` - ${formatDate(budget.end_date)}` : ''})
            </Text>
          </View>

          <View style={styles.budgetCurrency}>
            <MaterialIcons name="attach-money" size={20} color={Colors.GRAY_600} />
            <Text style={styles.budgetCurrencyText}>
              Para Birimi: {getCurrencySymbol(budget.currency || 'TRY')} {budget.currency || 'TRY'}
            </Text>
          </View>

          <View style={styles.budgetStatus}>
            <Text style={styles.budgetStatusLabel}>Durum:</Text>
            <View style={[
              styles.budgetStatusBadge,
              { backgroundColor: getProgressColor(budget.totalProgress) }
            ]}>
              <Text style={styles.budgetStatusText}>
                {budget.totalProgress < 50 ? 'İyi' : budget.totalProgress < 80 ? 'Dikkat' : 'Aşıldı'}
              </Text>
            </View>
          </View>

          {budget.notes && (
            <View style={styles.budgetNotes}>
              <Text style={styles.budgetNotesLabel}>Notlar:</Text>
              <Text style={styles.budgetNotesText}>{budget.notes}</Text>
            </View>
          )}
        </View>

        <View style={styles.budgetSummaryCard}>
          <Text style={styles.cardTitle}>Bütçe Özeti</Text>

          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Toplam Bütçe:</Text>
            <Text style={styles.summaryValue}>
              {formatCurrency(budget.totalBudget, budget.currency || 'TRY')}
            </Text>
          </View>

          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Toplam Harcama:</Text>
            <Text style={styles.summaryValue}>
              {formatCurrency(budget.totalSpent, budget.currency || 'TRY')}
            </Text>
          </View>

          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Kalan:</Text>
            <Text style={[
              styles.summaryValue,
              { color: budget.totalRemaining >= 0 ? Colors.SUCCESS : Colors.DANGER }
            ]}>
              {formatCurrency(budget.totalRemaining, budget.currency || 'TRY')}
            </Text>
          </View>

          {budget.currency !== 'TRY' && (
            <View style={styles.exchangeRateInfo}>
              <MaterialIcons name="swap-horiz" size={16} color={Colors.GRAY_600} />
              <Text style={styles.exchangeRateText}>
                1 {budget.currency} = {formatCurrency(1 / (budget.exchange_rate || 1), 'TRY', 4)}
              </Text>
            </View>
          )}

          <View style={styles.progressContainer}>
            <View style={styles.progressBar}>
              <View
                style={[
                  styles.progressFill,
                  {
                    width: `${budget.totalProgress}%`,
                    backgroundColor: getProgressColor(budget.totalProgress)
                  }
                ]}
              />
            </View>
            <Text style={styles.progressText}>
              {Math.round(budget.totalProgress)}% Kullanıldı
            </Text>
          </View>
        </View>

        <View style={styles.categoriesCard}>
          <Text style={styles.cardTitle}>Kategori Dağılımı</Text>

          {budget.categories.map((category) => (
            <View key={category.category_id} style={styles.categoryItem}>
              <View style={styles.categoryHeader}>
                <View style={styles.categoryInfo}>
                  <MaterialIcons
                    name={category.icon || "category"}
                    size={20}
                    color={category.color || Colors.PRIMARY}
                  />
                  <Text style={styles.categoryName}>{category.category_name}</Text>
                </View>
                <Text style={styles.categoryAmount}>
                  {formatCurrency(category.amount, budget.currency || 'TRY')}
                </Text>
              </View>

              <View style={styles.categoryProgress}>
                <View style={styles.progressBar}>
                  <View
                    style={[
                      styles.progressFill,
                      {
                        width: `${category.progress}%`,
                        backgroundColor: getProgressColor(category.progress)
                      }
                    ]}
                  />
                </View>
                <View style={styles.categoryProgressInfo}>
                  <Text style={styles.categorySpent}>
                    {formatCurrency(category.spent_amount || 0, budget.currency || 'TRY')} harcandı
                  </Text>
                  <Text style={[
                    styles.categoryRemaining,
                    { color: category.remaining >= 0 ? Colors.SUCCESS : Colors.DANGER }
                  ]}>
                    {formatCurrency(category.remaining, budget.currency || 'TRY')} kaldı
                  </Text>
                </View>
              </View>
            </View>
          ))}
        </View>

        <View style={styles.transactionsCard}>
          <Text style={styles.cardTitle}>Son İşlemler</Text>

          {transactions.length === 0 ? (
            <View style={styles.emptyTransactions}>
              <MaterialIcons name="receipt-long" size={48} color={Colors.GRAY_400} />
              <Text style={styles.emptyTransactionsText}>
                Bu bütçe için henüz işlem bulunmuyor
              </Text>
            </View>
          ) : (
            transactions.slice(0, 5).map((transaction) => (
              <View key={transaction.id} style={styles.transactionItem}>
                <View style={styles.transactionInfo}>
                  <View style={styles.transactionCategory}>
                    <MaterialIcons
                      name={transaction.category_icon || "category"}
                      size={20}
                      color={transaction.category_color || Colors.PRIMARY}
                    />
                    <Text style={styles.transactionCategoryName}>
                      {transaction.category_name}
                    </Text>
                  </View>
                  <Text style={styles.transactionDate}>
                    {new Date(transaction.date).toLocaleDateString('tr-TR')}
                  </Text>
                </View>

                <View style={styles.transactionDetails}>
                  <Text style={styles.transactionDescription}>
                    {transaction.description || 'İsimsiz işlem'}
                  </Text>
                  <Text style={styles.transactionAmount}>
                    {formatCurrency(transaction.amount, transaction.currency || budget.currency || 'TRY')}
                  </Text>
                </View>
              </View>
            ))
          )}

          {transactions.length > 5 && (
            <TouchableOpacity
              style={styles.viewAllButton}
              onPress={() => navigation.navigate('Transactions', {
                screen: 'TransactionsList',
                params: {
                  filter: {
                    startDate: budget.start_date,
                    endDate: budget.end_date,
                    categories: budget.categories.map(c => c.category_id)
                  }
                }
              })}
            >
              <Text style={styles.viewAllButtonText}>Tüm İşlemleri Görüntüle</Text>
              <MaterialIcons name="chevron-right" size={20} color={Colors.PRIMARY} />
            </TouchableOpacity>
          )}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa'
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.GRAY_600
  },
  errorText: {
    marginTop: 16,
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.GRAY_800
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 16,
    paddingHorizontal: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2
  },
  backButton: {
    padding: 8,
    marginRight: 8
  },
  headerTitle: {
    flex: 1,
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    marginLeft: 8,
    marginRight: 8
  },
  headerActions: {
    flexDirection: 'row'
  },
  headerAction: {
    padding: 8,
    marginLeft: 4
  },
  content: {
    flex: 1,
    padding: 16
  },
  budgetInfoCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1
  },
  budgetPeriod: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12
  },
  budgetPeriodText: {
    fontSize: 16,
    color: Colors.GRAY_700,
    marginLeft: 8
  },
  budgetCurrency: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12
  },
  budgetCurrencyText: {
    fontSize: 16,
    color: Colors.GRAY_700,
    marginLeft: 8
  },
  budgetStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12
  },
  budgetStatusLabel: {
    fontSize: 16,
    color: Colors.GRAY_700,
    marginRight: 8
  },
  budgetStatusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
    backgroundColor: Colors.SUCCESS
  },
  budgetStatusText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#fff'
  },
  budgetNotes: {
    marginTop: 8
  },
  budgetNotesLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.GRAY_700,
    marginBottom: 4
  },
  budgetNotesText: {
    fontSize: 14,
    color: Colors.GRAY_600,
    lineHeight: 20
  },
  budgetSummaryCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.GRAY_800,
    marginBottom: 16
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12
  },
  summaryLabel: {
    fontSize: 16,
    color: Colors.GRAY_700
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.GRAY_800
  },
  progressContainer: {
    marginTop: 8
  },
  progressBar: {
    height: 12,
    backgroundColor: Colors.GRAY_200,
    borderRadius: 6,
    overflow: 'hidden',
    marginBottom: 8
  },
  progressFill: {
    height: '100%',
    backgroundColor: Colors.SUCCESS
  },
  progressText: {
    fontSize: 14,
    color: Colors.GRAY_600,
    textAlign: 'right'
  },
  exchangeRateInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginTop: 8,
    marginBottom: 8
  },
  exchangeRateText: {
    fontSize: 14,
    color: Colors.GRAY_600,
    marginLeft: 4
  },
  categoriesCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1
  },
  categoryItem: {
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8
  },
  categoryInfo: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.GRAY_800,
    marginLeft: 8
  },
  categoryAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.PRIMARY
  },
  categoryProgress: {
    marginTop: 8
  },
  categoryProgressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8
  },
  categorySpent: {
    fontSize: 14,
    color: Colors.GRAY_600
  },
  categoryRemaining: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.SUCCESS
  },
  transactionsCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1
  },
  emptyTransactions: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 32
  },
  emptyTransactionsText: {
    fontSize: 16,
    color: Colors.GRAY_600,
    marginTop: 16
  },
  transactionItem: {
    marginBottom: 12,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200
  },
  transactionInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4
  },
  transactionCategory: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  transactionCategoryName: {
    fontSize: 14,
    color: Colors.GRAY_700,
    marginLeft: 4
  },
  transactionDate: {
    fontSize: 12,
    color: Colors.GRAY_500
  },
  transactionDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  transactionDescription: {
    fontSize: 16,
    color: Colors.GRAY_800
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.DANGER
  },
  viewAllButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 12,
    marginTop: 8
  },
  viewAllButtonText: {
    fontSize: 16,
    color: Colors.PRIMARY,
    marginRight: 4
  },
  backButtonText: {
    fontSize: 16,
    color: Colors.PRIMARY,
    fontWeight: '500',
    marginTop: 16
  }
});
