/**
 * Veritabanı şema tanımları
 * <PERSON><PERSON> dosya, uygulamanın veritabanı tablolarını ve ilişkilerini tanımlar
 */

/**
 * <PERSON>gor<PERSON> tablosu
 * <PERSON><PERSON><PERSON>, gider ve yatırım kategorilerini tanımlar
 */
export const CATEGORIES_TABLE = `
CREATE TABLE IF NOT EXISTS categories (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK(type IN ('income', 'expense', 'both', 'investment')),
  icon TEXT,
  color TEXT,
  is_default INTEGER DEFAULT 0,
  is_active INTEGER DEFAULT 1,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
)
`;

/**
 * İşlemler tablosu
 * Tüm gelir ve gider işlemlerini içerir
 */
export const TRANSACTIONS_TABLE = `
CREATE TABLE IF NOT EXISTS transactions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  amount DECIMAL(10,2) NOT NULL,
  category_id INTEGER,
  type TEXT NOT NULL CHECK(type IN ('income', 'expense')),
  date DATE NOT NULL,
  description TEXT,
  metadata TEXT,
  currency TEXT DEFAULT 'TRY',
  exchange_rate DECIMAL(10,6),
  preferred_currency TEXT,
  preferred_currency_amount DECIMAL(10,2),
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY(category_id) REFERENCES categories(id)
)
`;

/**
 * Döviz kurları tablosu
 * Farklı para birimlerinin günlük kurlarını saklar
 */
export const EXCHANGE_RATES_TABLE = `
CREATE TABLE IF NOT EXISTS exchange_rates (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  currency TEXT NOT NULL,
  rate DECIMAL(10,4) NOT NULL,
  fetch_date DATE NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(currency, fetch_date)
)
`;

/**
 * Finansal hedefler tablosu
 * Kullanıcının finansal hedeflerini saklar
 */
export const GOALS_TABLE = `
CREATE TABLE IF NOT EXISTS goals (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  target_amount DECIMAL(10,2) NOT NULL,
  current_amount DECIMAL(10,2) DEFAULT 0,
  start_date DATE NOT NULL,
  target_date DATE,
  category_id INTEGER,
  icon TEXT,
  color TEXT,
  is_completed INTEGER DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY(category_id) REFERENCES categories(id)
)
`;

/**
 * Yatırım varlıkları tablosu
 * Takip edilen yatırım araçlarını saklar (hisse senedi, kripto para, altın, vb.)
 */
export const INVESTMENT_ASSETS_TABLE = `
CREATE TABLE IF NOT EXISTS investment_assets (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  symbol TEXT NOT NULL,
  type TEXT NOT NULL CHECK(type IN ('stock', 'crypto', 'gold', 'silver', 'forex', 'bond', 'fund', 'other')),
  description TEXT,
  icon TEXT,
  color TEXT,
  is_active INTEGER DEFAULT 1,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
)
`;

/**
 * Yatırım işlemleri tablosu
 * Yatırım alım-satım işlemlerini saklar
 */
export const INVESTMENT_TRANSACTIONS_TABLE = `
CREATE TABLE IF NOT EXISTS investment_transactions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  asset_id INTEGER NOT NULL,
  type TEXT NOT NULL CHECK(type IN ('buy', 'sell', 'dividend', 'interest', 'fee', 'transfer', 'other')),
  quantity DECIMAL(18,8) NOT NULL,
  price_per_unit DECIMAL(18,8) NOT NULL,
  total_amount DECIMAL(18,2) NOT NULL,
  fee DECIMAL(10,2) DEFAULT 0,
  date DATE NOT NULL,
  currency TEXT DEFAULT 'TRY',
  exchange_rate DECIMAL(10,6),
  preferred_currency TEXT,
  preferred_currency_amount DECIMAL(10,2),
  notes TEXT,
  metadata TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY(asset_id) REFERENCES investment_assets(id)
)
`;

/**
 * Yatırım portföyü tablosu
 * Kullanıcının mevcut yatırım portföyünü saklar
 */
export const INVESTMENT_PORTFOLIO_TABLE = `
CREATE TABLE IF NOT EXISTS investment_portfolio (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  asset_id INTEGER NOT NULL,
  quantity DECIMAL(18,8) NOT NULL,
  average_buy_price DECIMAL(18,8) NOT NULL,
  current_price DECIMAL(18,8),
  current_value DECIMAL(18,2),
  profit_loss DECIMAL(18,2),
  profit_loss_percentage DECIMAL(10,2),
  last_updated DATETIME,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY(asset_id) REFERENCES investment_assets(id)
)
`;

/**
 * Yatırım fiyat geçmişi tablosu
 * Yatırım araçlarının fiyat geçmişini saklar
 */
export const INVESTMENT_PRICE_HISTORY_TABLE = `
CREATE TABLE IF NOT EXISTS investment_price_history (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  asset_id INTEGER NOT NULL,
  price DECIMAL(18,8) NOT NULL,
  date DATE NOT NULL,
  currency TEXT DEFAULT 'TRY',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY(asset_id) REFERENCES investment_assets(id)
)
`;

/**
 * Bildirim ayarları tablosu
 * Kullanıcının bildirim tercihlerini saklar
 */
export const NOTIFICATION_SETTINGS_TABLE = `
CREATE TABLE IF NOT EXISTS notification_settings (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  type TEXT NOT NULL,
  enabled INTEGER NOT NULL DEFAULT 1,
  time TEXT,
  days TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
)
`;

/**
 * Bildirimler tablosu
 * Planlanan ve gönderilen bildirimleri saklar
 */
export const NOTIFICATIONS_TABLE = `
CREATE TABLE IF NOT EXISTS notifications (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  title TEXT NOT NULL,
  body TEXT,
  type TEXT NOT NULL,
  scheduled_date DATETIME,
  is_sent INTEGER DEFAULT 0,
  is_read INTEGER DEFAULT 0,
  related_id INTEGER,
  related_type TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
)
`;

/**
 * Tab bar yapılandırma tablosu
 * Tab bar'ın görünümünü ve davranışını yapılandırır
 */
export const TAB_BAR_CONFIG_TABLE = `
CREATE TABLE IF NOT EXISTS tab_bar_config (
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  icon TEXT NOT NULL,
  position INTEGER NOT NULL,
  is_visible INTEGER DEFAULT 1,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
)
`;

/**
 * Ayarlar tablosu
 * Uygulama ayarlarını saklar
 */
export const SETTINGS_TABLE = `
CREATE TABLE IF NOT EXISTS settings (
  id TEXT PRIMARY KEY,
  value TEXT,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
)
`;

/**
 * Yedeklemeler tablosu
 * Yedekleme bilgilerini saklar
 */
export const BACKUPS_TABLE = `
CREATE TABLE IF NOT EXISTS backups (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  filename TEXT NOT NULL,
  size INTEGER,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  notes TEXT
)
`;

/**
 * Bütçeler tablosu
 * Kullanıcının aylık bütçelerini saklar
 */
export const BUDGETS_TABLE = `
CREATE TABLE IF NOT EXISTS budgets (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  category_id INTEGER,
  amount DECIMAL(10,2) NOT NULL,
  month INTEGER NOT NULL,
  year INTEGER NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY(category_id) REFERENCES categories(id),
  UNIQUE(category_id, month, year)
)
`;

/**
 * Notlar tablosu
 * Kullanıcının finansal notlarını saklar
 */
export const NOTES_TABLE = `
CREATE TABLE IF NOT EXISTS notes (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  title TEXT NOT NULL,
  content TEXT,
  related_id INTEGER,
  related_type TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
)
`;

/**
 * Mesai ayarları tablosu
 * Kullanıcının mesai ayarlarını saklar
 */
export const WORK_SETTINGS_TABLE = `
CREATE TABLE IF NOT EXISTS work_settings (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  hourly_rate DECIMAL(10,2) NOT NULL DEFAULT 0,
  overtime_rate DECIMAL(10,2) NOT NULL DEFAULT 0,
  weekly_work_hours INTEGER NOT NULL DEFAULT 45,
  daily_work_hours INTEGER NOT NULL DEFAULT 9,
  currency TEXT DEFAULT 'TRY',
  work_days TEXT DEFAULT '1,2,3,4,5', -- Pazartesi-Cuma
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
)
`;

/**
 * Mesai kayıtları tablosu
 * Kullanıcının mesai kayıtlarını saklar
 */
export const WORK_SHIFTS_TABLE = `
CREATE TABLE IF NOT EXISTS work_shifts (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  date DATE NOT NULL,
  start_time TEXT NOT NULL,
  end_time TEXT,
  break_duration INTEGER DEFAULT 0, -- Dakika cinsinden
  is_overtime INTEGER DEFAULT 0,
  overtime_multiplier DECIMAL(3,1) DEFAULT 1.5,
  notes TEXT,
  status TEXT DEFAULT 'completed' CHECK(status IN ('planned', 'in_progress', 'completed', 'cancelled')),
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
)
`;

/**
 * Mesai ödemeleri tablosu
 * Mesai ödemelerini saklar
 */
export const WORK_PAYMENTS_TABLE = `
CREATE TABLE IF NOT EXISTS work_payments (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  period_start_date DATE NOT NULL,
  period_end_date DATE NOT NULL,
  regular_hours DECIMAL(10,2) NOT NULL DEFAULT 0,
  overtime_hours DECIMAL(10,2) NOT NULL DEFAULT 0,
  regular_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
  overtime_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
  total_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
  payment_date DATE,
  is_paid INTEGER DEFAULT 0,
  notes TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
)
`;

/**
 * Mesai ödemeleri indeksleri
 */
export const WORK_PAYMENTS_INDEXES = `
CREATE INDEX IF NOT EXISTS idx_work_payments_period_dates
ON work_payments(period_start_date, period_end_date);

CREATE INDEX IF NOT EXISTS idx_work_payments_payment_date
ON work_payments(payment_date);

CREATE INDEX IF NOT EXISTS idx_work_payments_is_paid
ON work_payments(is_paid);
`;

// Tüm tabloları içeren dizi
export const ALL_TABLES = [
  CATEGORIES_TABLE,
  TRANSACTIONS_TABLE,
  EXCHANGE_RATES_TABLE,
  GOALS_TABLE,
  INVESTMENT_ASSETS_TABLE,
  INVESTMENT_TRANSACTIONS_TABLE,
  INVESTMENT_PORTFOLIO_TABLE,
  INVESTMENT_PRICE_HISTORY_TABLE,
  NOTIFICATION_SETTINGS_TABLE,
  NOTIFICATIONS_TABLE,
  TAB_BAR_CONFIG_TABLE,
  SETTINGS_TABLE,
  BACKUPS_TABLE,
  BUDGETS_TABLE,
  NOTES_TABLE,
  WORK_SETTINGS_TABLE,
  WORK_SHIFTS_TABLE,
  WORK_PAYMENTS_TABLE
];

// Varsayılan kategoriler
export const DEFAULT_CATEGORIES = [
  // Gider kategorileri
  { name: 'Yemek', type: 'expense', icon: 'restaurant', color: '#FF6384', is_default: 1 },
  { name: 'Market', type: 'expense', icon: 'shopping-cart', color: '#FF9F40', is_default: 1 },
  { name: 'Ulaşım', type: 'expense', icon: 'directions-car', color: '#36A2EB', is_default: 1 },
  { name: 'Konut', type: 'expense', icon: 'home', color: '#FFCE56', is_default: 1 },
  { name: 'Faturalar', type: 'expense', icon: 'receipt', color: '#E74C3C', is_default: 1 },
  { name: 'Eğlence', type: 'expense', icon: 'movie', color: '#4BC0C0', is_default: 1 },
  { name: 'Sağlık', type: 'expense', icon: 'healing', color: '#9966FF', is_default: 1 },
  { name: 'Eğitim', type: 'expense', icon: 'school', color: '#FF9F40', is_default: 1 },
  { name: 'Giyim', type: 'expense', icon: 'checkroom', color: '#8E44AD', is_default: 1 },

  // Gelir kategorileri
  { name: 'Maaş', type: 'income', icon: 'attach-money', color: '#2ECC71', is_default: 1 },
  { name: 'Ek Gelir', type: 'income', icon: 'add-circle', color: '#27ae60', is_default: 1 },
  { name: 'Yatırım Geliri', type: 'income', icon: 'trending-up', color: '#3498db', is_default: 1 },
  { name: 'Hediye', type: 'income', icon: 'card-giftcard', color: '#E91E63', is_default: 1 },

  // Yatırım kategorileri
  { name: 'Hisse Senedi', type: 'investment', icon: 'show-chart', color: '#6c5ce7', is_default: 1 },
  { name: 'Kripto Para', type: 'investment', icon: 'currency-bitcoin', color: '#fd79a8', is_default: 1 },
  { name: 'Altın', type: 'investment', icon: 'monetization-on', color: '#fdcb6e', is_default: 1 },
  { name: 'Döviz', type: 'investment', icon: 'currency-exchange', color: '#00cec9', is_default: 1 },
  { name: 'Fon', type: 'investment', icon: 'account-balance', color: '#74b9ff', is_default: 1 },
  { name: 'Tahvil/Bono', type: 'investment', icon: 'receipt-long', color: '#a29bfe', is_default: 1 }
];

// Varsayılan tab bar yapılandırması
export const DEFAULT_TAB_BAR_CONFIG = [
  { id: 'home', title: 'Ana Sayfa', icon: 'home', position: 0 },
  { id: 'transactions', title: 'İşlemler', icon: 'receipt-long', position: 1 },
  { id: 'stats', title: 'İstatistikler', icon: 'bar-chart', position: 2 },
  { id: 'settings', title: 'Ayarlar', icon: 'settings', position: 3 }
];

// Varsayılan ayarlar
export const DEFAULT_SETTINGS = [
  { id: 'theme', value: 'light' },
  { id: 'currency', value: 'TRY' },
  { id: 'show_currency_conversion', value: 'true' },
  { id: 'biometric_login', value: 'false' },
  { id: 'pin_code_enabled', value: 'false' },
  { id: 'auto_backup', value: 'true' },
  { id: 'backup_frequency', value: '7' } // Günlük olarak
];

// Varsayılan bildirim ayarları
export const DEFAULT_NOTIFICATION_SETTINGS = [
  { type: 'daily_summary', enabled: 1, time: '19:00', days: '1,2,3,4,5,6,7' },
  { type: 'transaction_reminder', enabled: 1, time: '09:00', days: '1,2,3,4,5,6,7' },
  { type: 'budget_alert', enabled: 1, time: '20:00', days: '1,2,3,4,5,6,7' },
  { type: 'work_shift_reminder', enabled: 1, time: '08:00', days: '1,2,3,4,5' }
];

// Varsayılan mesai ayarları
export const DEFAULT_WORK_SETTINGS = {
  hourly_rate: 100.00,
  overtime_rate: 150.00,
  weekly_work_hours: 45,
  daily_work_hours: 9,
  currency: 'TRY',
  work_days: '1,2,3,4,5'
};

// Varsayılan yatırım varlıkları
export const DEFAULT_INVESTMENT_ASSETS = [
  // Hisse senetleri
  { name: 'Borsa İstanbul', symbol: 'BIST', type: 'stock', icon: 'show-chart', color: '#6c5ce7' },
  { name: 'Apple Inc.', symbol: 'AAPL', type: 'stock', icon: 'show-chart', color: '#6c5ce7' },
  { name: 'Microsoft Corporation', symbol: 'MSFT', type: 'stock', icon: 'show-chart', color: '#6c5ce7' },
  { name: 'Amazon.com Inc.', symbol: 'AMZN', type: 'stock', icon: 'show-chart', color: '#6c5ce7' },

  // Kripto paralar
  { name: 'Bitcoin', symbol: 'BTC', type: 'crypto', icon: 'currency-bitcoin', color: '#fd79a8' },
  { name: 'Ethereum', symbol: 'ETH', type: 'crypto', icon: 'currency-bitcoin', color: '#fd79a8' },
  { name: 'Binance Coin', symbol: 'BNB', type: 'crypto', icon: 'currency-bitcoin', color: '#fd79a8' },
  { name: 'Ripple', symbol: 'XRP', type: 'crypto', icon: 'currency-bitcoin', color: '#fd79a8' },

  // Değerli metaller
  { name: 'Altın (Gram)', symbol: 'XAU', type: 'gold', icon: 'monetization-on', color: '#fdcb6e' },
  { name: 'Altın (Ons)', symbol: 'XAUUSD', type: 'gold', icon: 'monetization-on', color: '#fdcb6e' },
  { name: 'Gümüş', symbol: 'XAG', type: 'silver', icon: 'monetization-on', color: '#b2bec3' },

  // Dövizler
  { name: 'Amerikan Doları', symbol: 'USD', type: 'forex', icon: 'currency-exchange', color: '#00cec9' },
  { name: 'Euro', symbol: 'EUR', type: 'forex', icon: 'currency-exchange', color: '#00cec9' },
  { name: 'İngiliz Sterlini', symbol: 'GBP', type: 'forex', icon: 'currency-exchange', color: '#00cec9' },

  // Fonlar
  { name: 'Para Piyasası Fonu', symbol: 'PPF', type: 'fund', icon: 'account-balance', color: '#74b9ff' },
  { name: 'Hisse Senedi Fonu', symbol: 'HSF', type: 'fund', icon: 'account-balance', color: '#74b9ff' },
  { name: 'Tahvil Fonu', symbol: 'TF', type: 'fund', icon: 'account-balance', color: '#74b9ff' },

  // Tahvil/Bonolar
  { name: 'Devlet Tahvili', symbol: 'DT', type: 'bond', icon: 'receipt-long', color: '#a29bfe' },
  { name: 'Hazine Bonosu', symbol: 'HB', type: 'bond', icon: 'receipt-long', color: '#a29bfe' }
];
