import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ScrollView, 
  Animated,
  SafeAreaView 
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { useAppContext } from '../context/AppContext';
import { useTheme } from '../context/ThemeContext';

/**
 * Modern Özellikler Ekranı - Kategorize edilmiş, minimal tasarım
 * Tema uyumlu, açıklamalı ve kullanıcı dostu navigation ekranı
 * 
 * @param {Object} props Component props
 * @param {Object} props.navigation Navigation objesi
 * @returns {JSX.Element} Modern özellikler ekranı
 */
const ModernFeaturesScreen = ({ navigation }) => {
  const insets = useSafeAreaInsets();
  const { theme, isDarkMode } = useTheme();
  const [animatedValue] = useState(new Animated.Value(0));

  // Feature kategorileri ve açıklamaları
  const featureCategories = [
    {
      id: 'main',
      title: '<PERSON>',
      features: [
        {
          id: 'statistics',
          title: 'İstatistikler',
          description: 'Gelir-gider analizi ve grafikler',
          icon: 'analytics',
          route: 'Statistics'
        },
        {
          id: 'reports',
          title: 'Raporlar',
          description: 'Detaylı finansal raporlar',
          icon: 'assessment',
          route: 'Reports'
        },
        {
          id: 'budget',
          title: 'Bütçe Yönetimi',
          description: 'Aylık bütçe planlama ve takip',
          icon: 'account-balance-wallet',
          route: 'Budgets'
        }
      ]
    },
    {
      id: 'tracking',
      title: 'Gelir & Takip',
      features: [
        {
          id: 'salary',
          title: 'Maaş Takibi',
          description: 'Maaş ödemeleri ve bordro yönetimi',
          icon: 'payments',
          route: 'Salaries'
        },
        {
          id: 'overtime',
          title: 'Mesai Takibi',
          description: 'Mesai saatleri ve ek ödeme hesaplama',
          icon: 'schedule',
          route: 'Overtime'
        },
        {
          id: 'investment',
          title: 'Yatırım Takibi',
          description: 'Hisse, döviz ve altın yatırımları',
          icon: 'trending-up',
          route: 'Investment'
        }
      ]
    },
    {
      id: 'tools',
      title: 'Araçlar',
      features: [
        {
          id: 'currency',
          title: 'Döviz Çevirici',
          description: 'Güncel kurlarla para birimi çevirisi',
          icon: 'currency-exchange',
          route: 'Currency'
        },
        {
          id: 'shopping',
          title: 'Alışveriş Listesi',
          description: 'Planlı alışveriş ve harcama kontrolü',
          icon: 'shopping-cart',
          route: 'Shopping'
        },
        {
          id: 'reminders',
          title: 'Hatırlatıcılar',
          description: 'Fatura ve ödeme hatırlatmaları',
          icon: 'alarm',
          route: 'Reminders'
        }
      ]
    },
    {
      id: 'management',
      title: 'Yönetim',
      features: [
        {
          id: 'categories',
          title: 'Kategori Yönetimi',
          description: 'Gelir-gider kategorilerini düzenle',
          icon: 'category',
          route: 'Categories'
        },
        {
          id: 'security',
          title: 'Güvenlik Ayarları',
          description: 'PIN, biyometrik ve gizlilik ayarları',
          icon: 'security',
          route: 'SecuritySettings'
        },
        {
          id: 'notifications',
          title: 'Bildirim Ayarları',
          description: 'Uyarı ve bildirim tercihleri',
          icon: 'notifications',
          route: 'NotificationSettings'
        }
      ]
    }
  ];

  /**
   * Özelliğe navigate et
   * @param {string} route - Hedef route
   */
  const navigateToFeature = (route) => {
    try {
      // Önce direkt navigation dene
      navigation.navigate(route);
    } catch (error) {
      console.error('Navigation error:', error);
      
      // Fallback navigation stratejileri
      try {
        // Özellikler tab'ı altındaki screen'ler için
        const featuresScreens = [
          'Statistics', 'Reports', 'Budgets', 'Salaries', 'Overtime', 
          'Investment', 'Currency', 'Shopping', 'Reminders', 'Categories'
        ];
        
        if (featuresScreens.includes(route)) {
          navigation.getParent()?.navigate('Özellikler', { screen: route });
          return;
        }
        
        // Özel durumlar için routing
        switch (route) {
          case 'SecuritySettings':
            navigation.navigate('Profile', { screen: 'Security' });
            break;
          case 'NotificationSettings':
            navigation.navigate('Profile', { screen: 'Notifications' });
            break;
          default:
            console.warn(`Unknown route: ${route}`);
        }
      } catch (fallbackError) {
        console.error('Fallback navigation failed:', fallbackError);
      }
    }
  };

  // Mount animasyonu
  useEffect(() => {
    Animated.timing(animatedValue, {
      toValue: 1,
      duration: 600,
      useNativeDriver: true,
    }).start();
  }, []);

  /**
   * Kategori header component'i
   */
  const CategoryHeader = ({ title, index }) => (
    <Animated.View
      style={[
        styles.categoryHeader,
        {
          opacity: animatedValue,
          transform: [
            {
              translateY: animatedValue.interpolate({
                inputRange: [0, 1],
                outputRange: [20, 0],
              }),
            },
          ],
        },
      ]}
    >
      <View style={[styles.categoryLine, { backgroundColor: theme.BORDER }]} />
      <Text style={[styles.categoryTitle, { color: theme.TEXT_SECONDARY }]}>
        {String(title || 'Kategori')}
      </Text>
      <View style={[styles.categoryLine, { backgroundColor: theme.BORDER }]} />
    </Animated.View>
  );

  /**
   * Feature item component'i
   */
  const FeatureItem = ({ feature, categoryIndex, featureIndex }) => {
    if (!feature || !feature.id) {
      return null;
    }

    return (
      <Animated.View
        style={[
          {
            opacity: animatedValue,
            transform: [
              {
                translateY: animatedValue.interpolate({
                  inputRange: [0, 1],
                  outputRange: [30, 0],
                }),
              },
            ],
          },
        ]}
      >
        <TouchableOpacity
          style={[styles.featureItem, { backgroundColor: theme.SURFACE || theme.colors?.surface || '#f9f9f9' }]}
          onPress={() => navigateToFeature(feature.route)}
          activeOpacity={0.7}
        >
          <View style={styles.featureContent}>
            <View style={[styles.featureIcon, { backgroundColor: theme.PRIMARY + '15' }]}>
              <MaterialIcons 
                name={feature.icon || 'help-outline'} 
                size={24} 
                color={theme.PRIMARY} 
              />
            </View>
            <View style={styles.featureText}>
              <Text style={[styles.featureTitle, { color: theme.TEXT_PRIMARY }]}>
                {String(feature.title || 'Özellik')}
              </Text>
              <Text style={[styles.featureDescription, { color: theme.TEXT_SECONDARY }]}>
                {String(feature.description || 'Açıklama yok')}
              </Text>
            </View>
            <MaterialIcons 
              name="chevron-right" 
              size={20} 
              color={theme.TEXT_SECONDARY} 
            />
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  const getStyles = (theme) => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.BACKGROUND,
    },
    header: {
      backgroundColor: theme.PRIMARY,
      paddingTop: insets.top + 10,
      paddingBottom: 20,
      paddingHorizontal: 20,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 8,
      elevation: 4,
    },
    backButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: 'rgba(255,255,255,0.2)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: '700',
      color: '#fff',
      flex: 1,
      textAlign: 'center',
      marginHorizontal: 20,
    },
    placeholder: {
      width: 40,
    },
    themeButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: 'rgba(255,255,255,0.2)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    content: {
      flex: 1,
      paddingHorizontal: 20,
      paddingTop: 24,
    },
    categoryHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginVertical: 20,
      marginTop: 32,
    },
    categoryLine: {
      flex: 1,
      height: 1,
    },
    categoryTitle: {
      fontSize: 14,
      fontWeight: '600',
      marginHorizontal: 16,
      textTransform: 'uppercase',
      letterSpacing: 0.5,
    },
    featureItem: {
      borderRadius: 12,
      marginBottom: 8,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 4,
      elevation: 2,
    },
    featureContent: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
    },
    featureIcon: {
      width: 44,
      height: 44,
      borderRadius: 22,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 16,
    },
    featureText: {
      flex: 1,
    },
    featureTitle: {
      fontSize: 16,
      fontWeight: '600',
      marginBottom: 4,
    },
    featureDescription: {
      fontSize: 12,
      lineHeight: 16,
    },
  });

  const styles = getStyles(theme);

  return (
    <View style={styles.container}>
      {/* Modern Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
          activeOpacity={0.7}
        >
          <MaterialIcons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Özellikler</Text>
        
        <View style={styles.placeholder} />
      </View>

      {/* Features List */}
      <ScrollView 
        style={styles.content}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 40 }}
      >
        {featureCategories && Array.isArray(featureCategories) && featureCategories.map((category, categoryIndex) => {
          if (!category || !category.id) {
            return null;
          }
          
          return (
            <View key={category.id}>
              {/* İlk kategori için header yok */}
              {categoryIndex > 0 && (
                <CategoryHeader title={category.title} index={categoryIndex} />
              )}
              
              {/* İlk kategori başlığı */}
              {categoryIndex === 0 && (
                <CategoryHeader title={category.title} index={categoryIndex} />
              )}

              {/* Category Features */}
              {category.features && Array.isArray(category.features) && category.features.map((feature, featureIndex) => (
                <FeatureItem
                  key={feature.id || `feature-${featureIndex}`}
                  feature={feature}
                  categoryIndex={categoryIndex}
                  featureIndex={featureIndex}
                />
              ))}
            </View>
          );
        })}
      </ScrollView>
    </View>
  );
};

export default ModernFeaturesScreen;
