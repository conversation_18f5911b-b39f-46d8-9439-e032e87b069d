/**
 * Report Service - Ana Rapor Servisi
 * Tüm rapor işlemlerini yöneten merkezi servis
 */

import { 
  createReportsSchema, 
  seedReportTemplates, 
  seedReportTags 
} from '../../db/migrations/reportsMigration.js';

let dbInstance = null;

/**
 * Database instance'ı set eder
 * @param {Object} db - Database instance
 */
export const setDatabaseInstance = (db) => {
  dbInstance = db;
};

/**
 * Database instance'ı getirir
 * @returns {Object} Database instance
 */
const getDb = () => {
  if (!dbInstance) {
    throw new Error('Database instance not set. Call setDatabaseInstance first.');
  }
  return dbInstance;
};

/**
 * Rapor servisini başlatır ve gerekli tabloları oluşturur
 */
export const initializeReportService = async (db) => {
  try {
    // Database instance'ı set et
    setDatabaseInstance(db);
    
    // Schema'ları oluştur
    await createReportsSchema(db);
    
    // Başlangıç verilerini ekle
    await seedReportTemplates(db);
    await seedReportTags(db);
    
    console.log('📊 Rapor servisi başarıyla başlatıldı');
    return true;
  } catch (error) {
    console.error('❌ Rapor servisi başlatılırken hata:', error);
    throw error;
  }
};

/**
 * Tüm rapor şablonlarını getirir
 * @param {Object} filters - Filtreleme seçenekleri
 * @returns {Array} Rapor şablonları listesi
 */
export const getReportTemplates = async (filters = {}) => {
  try {
    const db = getDb();
    let query = 'SELECT * FROM report_templates WHERE 1=1';
    const params = [];

    // Kategori filtresi
    if (filters.category) {
      query += ' AND category = ?';
      params.push(filters.category);
    }

    // Sistem şablonu filtresi
    if (filters.isSystem !== undefined) {
      query += ' AND is_system = ?';
      params.push(filters.isSystem ? 1 : 0);
    }

    // İnteraktif filtresi
    if (filters.isInteractive !== undefined) {
      query += ' AND is_interactive = ?';
      params.push(filters.isInteractive ? 1 : 0);
    }

    query += ' ORDER BY is_system DESC, name ASC';

    const result = await db.getAllAsync(query, params);
    
    // JSON alanlarını parse et
    return result.map(template => ({
      ...template,
      config: JSON.parse(template.config),
      tags: JSON.parse(template.tags || '[]')
    }));
  } catch (error) {
    console.error('❌ Rapor şablonları getirilirken hata:', error);
    throw error;
  }
};

/**
 * Belirli bir rapor şablonunu getirir
 * @param {number} templateId - Şablon ID'si
 * @returns {Object} Rapor şablonu
 */
export const getReportTemplate = async (templateId) => {
  try {
    const db = getDb();
    const result = await db.getFirstAsync(
      'SELECT * FROM report_templates WHERE id = ?',
      [templateId]
    );

    if (!result) {
      throw new Error('Rapor şablonu bulunamadı');
    }

    // JSON alanlarını parse et
    return {
      ...result,
      config: JSON.parse(result.config),
      tags: JSON.parse(result.tags || '[]')
    };
  } catch (error) {
    console.error('❌ Rapor şablonu getirilirken hata:', error);
    throw error;
  }
};

/**
 * Yeni rapor şablonu oluşturur
 * @param {Object} templateData - Şablon verileri
 * @returns {Object} Oluşturulan şablon
 */
export const createReportTemplate = async (templateData) => {
  try {
    const db = getDb();
    const result = await db.runAsync(
      `INSERT INTO report_templates 
       (name, description, category, config, is_system, is_interactive, tags) 
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        templateData.name,
        templateData.description,
        templateData.category || 'custom',
        JSON.stringify(templateData.config),
        templateData.isSystem ? 1 : 0,
        templateData.isInteractive ? 1 : 0,
        JSON.stringify(templateData.tags || [])
      ]
    );

    return await getReportTemplate(result.lastInsertRowId);
  } catch (error) {
    console.error('❌ Rapor şablonu oluşturulurken hata:', error);
    throw error;
  }
};

/**
 * Kaydedilen raporları getirir
 * @param {Object} filters - Filtreleme seçenekleri
 * @returns {Array} Kaydedilen raporlar listesi
 */
export const getSavedReports = async (filters = {}) => {
  try {
    const db = getDb();
    let query = `
      SELECT sr.*, rt.name as template_name, rt.category as template_category
      FROM saved_reports sr
      LEFT JOIN report_templates rt ON sr.template_id = rt.id
      WHERE 1=1
    `;
    const params = [];

    // Favori filtresi
    if (filters.isFavorite) {
      query += ' AND sr.is_favorite = 1';
    }

    // Şablon kategorisi filtresi
    if (filters.category) {
      query += ' AND rt.category = ?';
      params.push(filters.category);
    }

    // Sıralama
    if (filters.orderBy === 'name') {
      query += ' ORDER BY sr.name ASC';
    } else if (filters.orderBy === 'created') {
      query += ' ORDER BY sr.created_at DESC';
    } else {
      query += ' ORDER BY sr.last_viewed DESC NULLS LAST, sr.created_at DESC';
    }

    // Limit
    if (filters.limit) {
      query += ' LIMIT ?';
      params.push(filters.limit);
    }

    const result = await db.getAllAsync(query, params);
    
    // JSON alanlarını parse et
    return result.map(report => ({
      ...report,
      config: JSON.parse(report.config),
      data_sources: JSON.parse(report.data_sources || '[]'),
      filters: JSON.parse(report.filters || '{}'),
      layout: JSON.parse(report.layout || '{}')
    }));
  } catch (error) {
    console.error('❌ Kaydedilen raporlar getirilirken hata:', error);
    throw error;
  }
};

/**
 * Yeni rapor kaydeder
 * @param {Object} reportData - Rapor verileri
 * @returns {Object} Kaydedilen rapor
 */
export const saveReport = async (reportData) => {
  try {
    const db = getDb();
    const result = await db.runAsync(
      `INSERT INTO saved_reports 
       (template_id, name, description, config, data_sources, filters, layout) 
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        reportData.templateId,
        reportData.name,
        reportData.description,
        JSON.stringify(reportData.config),
        JSON.stringify(reportData.dataSources || []),
        JSON.stringify(reportData.filters || {}),
        JSON.stringify(reportData.layout || {})
      ]
    );

    return await getSavedReport(result.lastInsertRowId);
  } catch (error) {
    console.error('❌ Rapor kaydedilirken hata:', error);
    throw error;
  }
};

/**
 * Belirli bir kaydedilen raporu getirir
 * @param {number} reportId - Rapor ID'si
 * @returns {Object} Kaydedilen rapor
 */
export const getSavedReport = async (reportId) => {
  try {
    const db = getDb();
    const result = await db.getFirstAsync(
      `SELECT sr.*, rt.name as template_name, rt.category as template_category
       FROM saved_reports sr
       LEFT JOIN report_templates rt ON sr.template_id = rt.id
       WHERE sr.id = ?`,
      [reportId]
    );

    if (!result) {
      throw new Error('Kaydedilen rapor bulunamadı');
    }

    // JSON alanlarını parse et
    return {
      ...result,
      config: JSON.parse(result.config),
      data_sources: JSON.parse(result.data_sources || '[]'),
      filters: JSON.parse(result.filters || '{}'),
      layout: JSON.parse(result.layout || '{}')
    };
  } catch (error) {
    console.error('❌ Kaydedilen rapor getirilirken hata:', error);
    throw error;
  }
};

/**
 * Rapor görüntülenme sayısını artırır
 * @param {number} reportId - Rapor ID'si
 */
export const incrementReportView = async (reportId) => {
  try {
    const db = getDb();
    await db.runAsync(
      `UPDATE saved_reports 
       SET view_count = view_count + 1, last_viewed = CURRENT_TIMESTAMP 
       WHERE id = ?`,
      [reportId]
    );
  } catch (error) {
    console.error('❌ Rapor görüntülenme sayısı artırılırken hata:', error);
    throw error;
  }
};

/**
 * Raporu favorilere ekler/çıkarır
 * @param {number} reportId - Rapor ID'si
 * @param {boolean} isFavorite - Favori durumu
 */
export const toggleReportFavorite = async (reportId, isFavorite) => {
  try {
    const db = getDb();
    await db.runAsync(
      'UPDATE saved_reports SET is_favorite = ? WHERE id = ?',
      [isFavorite ? 1 : 0, reportId]
    );
  } catch (error) {
    console.error('❌ Rapor favori durumu güncellenirken hata:', error);
    throw error;
  }
};

/**
 * Rapor kullanım istatistiklerini getirir
 * @returns {Object} İstatistikler
 */
export const getReportStats = async () => {
  try {
    const db = getDb();
    
    // Temel istatistikler
    const totalReports = await db.getFirstAsync(
      'SELECT COUNT(*) as count FROM saved_reports'
    );
    
    const favoritesCount = await db.getFirstAsync(
      'SELECT COUNT(*) as count FROM saved_reports WHERE is_favorite = 1'
    );
    
    const totalExports = await db.getFirstAsync(
      'SELECT COUNT(*) as count FROM report_usage_stats WHERE action_type = "export"'
    );
    
    const scheduledCount = await db.getFirstAsync(
      'SELECT COUNT(*) as count FROM report_schedules WHERE is_active = 1'
    );

    return {
      totalReports: totalReports?.count || 0,
      favoritesCount: favoritesCount?.count || 0,
      totalExports: totalExports?.count || 0,
      scheduledCount: scheduledCount?.count || 0
    };
  } catch (error) {
    console.error('❌ Rapor istatistikleri getirilirken hata:', error);
    return {
      totalReports: 0,
      favoritesCount: 0,
      totalExports: 0,
      scheduledCount: 0
    };
  }
};

/**
 * Rapor kullanım logunu kaydeder
 * @param {number} reportId - Rapor ID'si
 * @param {string} actionType - İşlem türü (view, export, share, edit)
 * @param {Object} details - İşlem detayları
 */
export const logReportUsage = async (reportId, actionType, details = {}) => {
  try {
    const db = getDb();
    await db.runAsync(
      `INSERT INTO report_usage_stats 
       (report_id, action_type, action_details, duration) 
       VALUES (?, ?, ?, ?)`,
      [
        reportId,
        actionType,
        JSON.stringify(details),
        details.duration || null
      ]
    );
  } catch (error) {
    console.error('❌ Rapor kullanım logu kaydedilirken hata:', error);
    // Log hatası uygulamayı durdurmamalı
  }
};

/**
 * Rapor etiketlerini getirir
 * @returns {Array} Etiketler listesi
 */
export const getReportTags = async () => {
  try {
    const db = getDb();
    const result = await db.getAllAsync(
      'SELECT * FROM report_tags ORDER BY usage_count DESC, name ASC'
    );
    
    return result;
  } catch (error) {
    console.error('❌ Rapor etiketleri getirilirken hata:', error);
    throw error;
  }
};

/**
 * Rapor silme
 * @param {number} reportId - Rapor ID'si
 */
export const deleteReport = async (reportId) => {
  try {
    const db = getDb();
    await db.runAsync('DELETE FROM saved_reports WHERE id = ?', [reportId]);
    console.log('📊 Rapor başarıyla silindi');
  } catch (error) {
    console.error('❌ Rapor silinirken hata:', error);
    throw error;
  }
};
