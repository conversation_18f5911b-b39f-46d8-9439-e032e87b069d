import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Switch,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useSQLiteContext } from 'expo-sqlite';
import { Colors } from '../constants/colors';
import DateTimePicker from '@react-native-community/datetimepicker';
import * as workService from '../services/workService';

/**
 * Mesai ödemesi detay/düzenleme ekranı
 * @param {Object} props Component props
 * @param {Object} props.navigation Navigation objesi
 * @param {Object} props.route Route objesi
 */
const WorkPaymentDetailScreen = ({ navigation, route }) => {
  const { paymentId } = route.params || {};
  const db = useSQLiteContext();

  const [title, setTitle] = useState('');
  const [periodStartDate, setPeriodStartDate] = useState(new Date());
  const [periodEndDate, setPeriodEndDate] = useState(new Date());
  const [amount, setAmount] = useState('');
  const [totalHours, setTotalHours] = useState('');
  const [isPaid, setIsPaid] = useState(false);
  const [paymentDate, setPaymentDate] = useState(new Date());
  const [notes, setNotes] = useState('');

  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [showPaymentDatePicker, setShowPaymentDatePicker] = useState(false);

  const [isLoading, setIsLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(paymentId ? true : false);

  // Ödeme detaylarını yükle
  useEffect(() => {
    const loadPayment = async () => {
      if (!paymentId) return;

      try {
        setInitialLoading(true);

        const payment = await workService.getWorkPayment(db, paymentId);

        if (payment) {
          setTitle(payment.title || '');
          setPeriodStartDate(new Date(payment.period_start_date));
          setPeriodEndDate(new Date(payment.period_end_date));
          setAmount(payment.total_amount?.toString() || '');
          setTotalHours((payment.regular_hours + payment.overtime_hours)?.toString() || '');
          setIsPaid(payment.is_paid === 1);
          setPaymentDate(payment.payment_date ? new Date(payment.payment_date) : new Date());
          setNotes(payment.notes || '');
        }
      } catch (error) {
        console.error('Ödeme yükleme hatası:', error);
        Alert.alert('Hata', 'Ödeme detayları yüklenirken bir hata oluştu.');
      } finally {
        setInitialLoading(false);
      }
    };

    loadPayment();
  }, [db, paymentId]);

  // Ödemeyi kaydet
  const savePayment = async () => {
    try {
      // Validasyon
      if (!title.trim()) {
        Alert.alert('Hata', 'Lütfen bir başlık girin.');
        return;
      }

      if (periodStartDate >= periodEndDate) {
        Alert.alert('Hata', 'Bitiş tarihi başlangıç tarihinden sonra olmalıdır.');
        return;
      }

      if (!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
        Alert.alert('Hata', 'Lütfen geçerli bir tutar girin.');
        return;
      }

      if (!totalHours || isNaN(parseFloat(totalHours)) || parseFloat(totalHours) <= 0) {
        Alert.alert('Hata', 'Lütfen geçerli bir toplam saat girin.');
        return;
      }

      setIsLoading(true);

      const parsedAmount = parseFloat(amount);
      const parsedTotalHours = parseFloat(totalHours);

      // Ödeme verilerini hazırla
      const paymentData = {
        period_start_date: periodStartDate.toISOString().split('T')[0],
        period_end_date: periodEndDate.toISOString().split('T')[0],
        regular_hours: parsedTotalHours,
        overtime_hours: 0,
        regular_amount: parsedAmount,
        overtime_amount: 0,
        total_amount: parsedAmount,
        is_paid: isPaid ? 1 : 0,
        payment_date: isPaid ? paymentDate.toISOString().split('T')[0] : null,
        notes
      };

      if (paymentId) {
        // Mevcut ödemeyi güncelle
        await workService.updateWorkPayment(db, paymentId, paymentData);
      } else {
        // Yeni ödeme ekle
        await workService.addWorkPayment(db, paymentData);
      }

      navigation.goBack();
    } catch (error) {
      console.error('Ödeme kaydetme hatası:', error);
      Alert.alert('Hata', 'Ödeme kaydedilirken bir hata oluştu.');
    } finally {
      setIsLoading(false);
    }
  };

  // Tarih formatla
  const formatDate = (date) => {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}.${month}.${year}`;
  };

  // Başlangıç tarihi değiştiğinde
  const handleStartDateChange = (_, selectedDate) => {
    setShowStartDatePicker(false);
    if (selectedDate) setPeriodStartDate(selectedDate);
  };

  // Bitiş tarihi değiştiğinde
  const handleEndDateChange = (_, selectedDate) => {
    setShowEndDatePicker(false);
    if (selectedDate) setPeriodEndDate(selectedDate);
  };

  // Ödeme tarihi değiştiğinde
  const handlePaymentDateChange = (_, selectedDate) => {
    setShowPaymentDatePicker(false);
    if (selectedDate) setPaymentDate(selectedDate);
  };

  if (initialLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <MaterialIcons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.title}>
          {paymentId ? 'Ödeme Düzenle' : 'Yeni Ödeme Ekle'}
        </Text>
        <View style={{ width: 24 }} />
      </View>

      <KeyboardAvoidingView
        style={styles.container}
        behavior={'padding'}
        keyboardVerticalOffset={64}
      >
        <ScrollView style={styles.content}>
          <View style={styles.formGroup}>
            <Text style={styles.label}>Başlık</Text>
            <TextInput
              style={styles.input}
              value={title}
              onChangeText={setTitle}
              placeholder="Örn: Ocak 2023 Maaşı"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Dönem Başlangıcı</Text>
            <TouchableOpacity
              style={styles.dateSelector}
              onPress={() => setShowStartDatePicker(true)}
            >
              <Text style={styles.dateText}>{formatDate(periodStartDate)}</Text>
              <MaterialIcons name="calendar-today" size={20} color={Colors.PRIMARY} />
            </TouchableOpacity>

            {showStartDatePicker && (
              <DateTimePicker
                value={periodStartDate}
                mode="date"
                display="default"
                onChange={handleStartDateChange}
              />
            )}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Dönem Bitişi</Text>
            <TouchableOpacity
              style={styles.dateSelector}
              onPress={() => setShowEndDatePicker(true)}
            >
              <Text style={styles.dateText}>{formatDate(periodEndDate)}</Text>
              <MaterialIcons name="calendar-today" size={20} color={Colors.PRIMARY} />
            </TouchableOpacity>

            {showEndDatePicker && (
              <DateTimePicker
                value={periodEndDate}
                mode="date"
                display="default"
                onChange={handleEndDateChange}
              />
            )}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Toplam Tutar</Text>
            <View style={styles.inputWithIcon}>
              <Text style={styles.inputIcon}>₺</Text>
              <TextInput
                style={styles.inputWithIconField}
                value={amount}
                onChangeText={setAmount}
                keyboardType="numeric"
                placeholder="0.00"
              />
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Toplam Saat</Text>
            <TextInput
              style={styles.input}
              value={totalHours}
              onChangeText={setTotalHours}
              keyboardType="numeric"
              placeholder="0.00"
            />
          </View>

          <View style={styles.formGroup}>
            <View style={styles.switchContainer}>
              <View style={styles.switchInfo}>
                <MaterialIcons name="payments" size={24} color={Colors.INCOME} />
                <Text style={styles.switchLabel}>Ödendi</Text>
              </View>
              <Switch
                value={isPaid}
                onValueChange={setIsPaid}
                trackColor={{ false: '#ddd', true: Colors.INCOME }}
                thumbColor="#fff"
              />
            </View>
          </View>

          {isPaid && (
            <View style={styles.formGroup}>
              <Text style={styles.label}>Ödeme Tarihi</Text>
              <TouchableOpacity
                style={styles.dateSelector}
                onPress={() => setShowPaymentDatePicker(true)}
              >
                <Text style={styles.dateText}>{formatDate(paymentDate)}</Text>
                <MaterialIcons name="calendar-today" size={20} color={Colors.PRIMARY} />
              </TouchableOpacity>

              {showPaymentDatePicker && (
                <DateTimePicker
                  value={paymentDate}
                  mode="date"
                  display="default"
                  onChange={handlePaymentDateChange}
                />
              )}
            </View>
          )}

          <View style={styles.formGroup}>
            <Text style={styles.label}>Notlar</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={notes}
              onChangeText={setNotes}
              placeholder="Ödeme hakkında notlar..."
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>
        </ScrollView>

        <View style={styles.footer}>
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.cancelButtonText}>İptal</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.saveButton}
            onPress={savePayment}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Text style={styles.saveButtonText}>Kaydet</Text>
            )}
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: Colors.PRIMARY,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  title: {
    flex: 1,
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    marginLeft: 16,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  textArea: {
    minHeight: 100,
  },
  dateSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    backgroundColor: '#fff',
  },
  dateText: {
    fontSize: 16,
    color: '#333',
  },
  inputWithIcon: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  inputIcon: {
    paddingHorizontal: 12,
    fontSize: 16,
    color: '#666',
  },
  inputWithIconField: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  switchInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  switchLabel: {
    fontSize: 16,
    color: '#333',
    marginLeft: 8,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
    backgroundColor: '#fff',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    marginRight: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#333',
  },
  saveButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
    borderRadius: 8,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
  },
});

export default WorkPaymentDetailScreen;
