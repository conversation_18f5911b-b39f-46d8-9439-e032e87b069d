import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  TextInput,
  Switch,
  TouchableWithoutFeedback,
  ActivityIndicator
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Colors } from '../../../constants/colors';
import { formatDate, formatTime, calculateDuration, calculateEarnings } from '../utils/shiftUtils';
import { shiftStyles } from '../styles/shiftStyles';

/**
 * Vardiya Form Modalı Bileşeni
 *
 * Bu bileşen, vardiya eklemek veya düzenlemek için kullanılan bir modal sunar.
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {boolean} props.visible - Modalın görünür olup olmadığı
 * @param {Function} props.onClose - Modal kapatıldığında çalışacak fonksiyon
 * @param {Function} props.onSave - Vardiya kaydedildiğinde çalışacak fonksiyon
 * @param {Object} props.shift - Düzenlenecek vardiya (varsa)
 * @param {Array} props.shiftTypes - Vardiya türleri listesi
 * @param {Object} props.settings - Vardiya ayarları
 * @returns {JSX.Element} Vardiya form modalı
 */
const ShiftFormModal = ({ visible, onClose, onSave, shift, shiftTypes, settings }) => {
  // Form durumları
  const [date, setDate] = useState(new Date());
  const [startTime, setStartTime] = useState(new Date());
  const [endTime, setEndTime] = useState(new Date(new Date().getTime() + 8 * 60 * 60 * 1000));
  const [selectedShiftType, setSelectedShiftType] = useState(null);
  const [breakDuration, setBreakDuration] = useState('0');
  const [isOvertime, setIsOvertime] = useState(false);
  const [isHoliday, setIsHoliday] = useState(false);
  const [notes, setNotes] = useState('');
  const [hourlyRate, setHourlyRate] = useState('');

  // DateTimePicker durumları
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showStartTimePicker, setShowStartTimePicker] = useState(false);
  const [showEndTimePicker, setShowEndTimePicker] = useState(false);

  // Hesaplanan değerler
  const [duration, setDuration] = useState(0);
  const [earnings, setEarnings] = useState(0);

  // Yükleniyor durumu
  const [loading, setLoading] = useState(false);

  // Mevcut vardiyayı yükle
  useEffect(() => {
    if (shift && visible) {
      // Tarih ve saatleri ayarla
      const shiftDate = new Date(shift.date);

      const startParts = shift.start_time.split(':');
      const startDateTime = new Date(shiftDate);
      startDateTime.setHours(parseInt(startParts[0]), parseInt(startParts[1]), 0, 0);

      const endParts = shift.end_time.split(':');
      const endDateTime = new Date(shiftDate);
      endDateTime.setHours(parseInt(endParts[0]), parseInt(endParts[1]), 0, 0);

      // Eğer bitiş saati başlangıç saatinden küçükse, ertesi güne ayarla
      if (endDateTime < startDateTime) {
        endDateTime.setDate(endDateTime.getDate() + 1);
      }

      setDate(shiftDate);
      setStartTime(startDateTime);
      setEndTime(endDateTime);
      setSelectedShiftType(shift.shift_type_id);
      setBreakDuration(shift.break_duration?.toString() || '0');
      setIsOvertime(shift.is_overtime === 1);
      setIsHoliday(shift.is_holiday === 1);
      setNotes(shift.notes || '');
      setHourlyRate(shift.hourly_rate?.toString() || settings?.hourly_rate?.toString() || '100');
    } else if (visible) {
      // Yeni vardiya için varsayılan değerler
      const now = new Date();
      setDate(now);
      setStartTime(now);
      setEndTime(new Date(now.getTime() + 8 * 60 * 60 * 1000));
      setSelectedShiftType(shiftTypes.length > 0 ? shiftTypes[0].id : null);
      setBreakDuration('0');
      setIsOvertime(false);
      setIsHoliday(false);
      setNotes('');
      setHourlyRate(settings?.hourly_rate?.toString() || '100');
    }
  }, [shift, visible, settings, shiftTypes]);

  // Süre ve kazanç hesapla
  useEffect(() => {
    if (startTime && endTime) {
      const durationHours = calculateDuration(startTime, endTime, parseInt(breakDuration) || 0);
      setDuration(durationHours);

      let rate = parseFloat(hourlyRate) || 0;

      if (isOvertime) {
        rate *= settings?.overtime_rate / 100 || 1.5;
      }

      if (isHoliday) {
        rate *= 2; // Tatil günü çarpanı
      }

      const calculatedEarnings = calculateEarnings(durationHours, rate);
      setEarnings(calculatedEarnings);
    }
  }, [startTime, endTime, breakDuration, hourlyRate, isOvertime, isHoliday, settings]);

  // Tarih değişikliği
  const handleDateChange = (event, selectedDate) => {
    setShowDatePicker(false);
    if (selectedDate) {
      // Tarih değiştiğinde, başlangıç ve bitiş saatlerini koru
      const newDate = new Date(selectedDate);

      const newStartTime = new Date(newDate);
      newStartTime.setHours(
        startTime.getHours(),
        startTime.getMinutes(),
        0,
        0
      );

      const newEndTime = new Date(newDate);
      newEndTime.setHours(
        endTime.getHours(),
        endTime.getMinutes(),
        0,
        0
      );

      // Eğer bitiş saati başlangıç saatinden küçükse, ertesi güne ayarla
      if (newEndTime < newStartTime) {
        newEndTime.setDate(newEndTime.getDate() + 1);
      }

      setDate(newDate);
      setStartTime(newStartTime);
      setEndTime(newEndTime);
    }
  };

  // Başlangıç saati değişikliği
  const handleStartTimeChange = (event, selectedTime) => {
    setShowStartTimePicker(false);
    if (selectedTime) {
      const newStartTime = new Date(date);
      newStartTime.setHours(
        selectedTime.getHours(),
        selectedTime.getMinutes(),
        0,
        0
      );

      setStartTime(newStartTime);

      // Eğer bitiş saati başlangıç saatinden küçükse, bitiş saatini güncelle
      if (endTime < newStartTime) {
        const newEndTime = new Date(newStartTime);
        newEndTime.setHours(newStartTime.getHours() + 8);
        setEndTime(newEndTime);
      }
    }
  };

  // Bitiş saati değişikliği
  const handleEndTimeChange = (event, selectedTime) => {
    setShowEndTimePicker(false);
    if (selectedTime) {
      const newEndTime = new Date(date);
      newEndTime.setHours(
        selectedTime.getHours(),
        selectedTime.getMinutes(),
        0,
        0
      );

      // Eğer bitiş saati başlangıç saatinden küçükse, ertesi güne ayarla
      if (newEndTime < startTime) {
        newEndTime.setDate(newEndTime.getDate() + 1);
      }

      setEndTime(newEndTime);
    }
  };

  // Vardiya türü değişikliği
  const handleShiftTypeChange = (typeId) => {
    setSelectedShiftType(typeId);

    // Seçilen vardiya türünün bilgilerini al
    const shiftType = shiftTypes.find(type => type.id === typeId);

    if (shiftType) {
      // Vardiya türünün saatlik ücretini ayarla (varsa)
      if (shiftType.hourly_rate) {
        setHourlyRate(shiftType.hourly_rate.toString());
      }

      // Vardiya türünün mola süresini ayarla (varsa)
      if (shiftType.break_duration) {
        setBreakDuration(shiftType.break_duration.toString());
      }

      // Vardiya türünün başlangıç ve bitiş saatlerini ayarla (varsa)
      if (shiftType.start_time && shiftType.end_time) {
        const startParts = shiftType.start_time.split(':');
        const endParts = shiftType.end_time.split(':');

        const newStartTime = new Date(date);
        newStartTime.setHours(parseInt(startParts[0]), parseInt(startParts[1]), 0, 0);

        const newEndTime = new Date(date);
        newEndTime.setHours(parseInt(endParts[0]), parseInt(endParts[1]), 0, 0);

        // Eğer bitiş saati başlangıç saatinden küçükse, ertesi güne ayarla
        if (newEndTime < newStartTime) {
          newEndTime.setDate(newEndTime.getDate() + 1);
        }

        setStartTime(newStartTime);
        setEndTime(newEndTime);
      }
    }
  };

  // Vardiyayı kaydet
  const handleSave = () => {
    setLoading(true);

    // Tarih formatını ayarla (YYYY-MM-DD)
    const dateStr = date.toISOString().split('T')[0];

    // Saat formatını ayarla (HH:MM:SS)
    const startTimeStr = `${startTime.getHours().toString().padStart(2, '0')}:${startTime.getMinutes().toString().padStart(2, '0')}:00`;
    const endTimeStr = `${endTime.getHours().toString().padStart(2, '0')}:${endTime.getMinutes().toString().padStart(2, '0')}:00`;

    // Vardiya verilerini hazırla
    const shiftData = {
      date: dateStr,
      start_time: startTimeStr,
      end_time: endTimeStr,
      break_duration: parseInt(breakDuration) || 0,
      is_overtime: isOvertime ? 1 : 0,
      is_holiday: isHoliday ? 1 : 0,
      notes: notes,
      status: shift?.status || 'planned',
      shift_type_id: selectedShiftType,
      hourly_rate: parseFloat(hourlyRate) || (settings?.hourly_rate ? parseFloat(settings.hourly_rate) : 100),
      earnings: earnings
    };

    // Vardiyayı kaydet
    onSave(shift?.id, shiftData);

    setLoading(false);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={shiftStyles.modalOverlay}>
        <View style={shiftStyles.modalContent}>
          <Text style={shiftStyles.modalTitle}>
            {shift ? 'Vardiya Düzenle' : 'Yeni Vardiya Ekle'}
          </Text>

          <ScrollView>
            {/* Tarih Seçici */}
            <View style={styles.formGroup}>
              <Text style={styles.label}>Tarih</Text>
              <TouchableOpacity
                style={styles.dateSelector}
                onPress={() => setShowDatePicker(true)}
              >
                <MaterialIcons name="event" size={20} color={Colors.PRIMARY} />
                <Text style={styles.dateText}>{formatDate(date)}</Text>
              </TouchableOpacity>

              {showDatePicker && (
                <DateTimePicker
                  value={date}
                  mode="date"
                  display="default"
                  onChange={handleDateChange}
                />
              )}
            </View>

            {/* Saat Seçiciler */}
            <View style={styles.timeContainer}>
              {/* Başlangıç Saati */}
              <View style={[styles.formGroup, { flex: 1, marginRight: 8 }]}>
                <Text style={styles.label}>Başlangıç</Text>
                <TouchableOpacity
                  style={styles.timeSelector}
                  onPress={() => setShowStartTimePicker(true)}
                >
                  <MaterialIcons name="access-time" size={20} color={Colors.PRIMARY} />
                  <Text style={styles.timeText}>{formatTime(startTime)}</Text>
                </TouchableOpacity>

                {showStartTimePicker && (
                  <DateTimePicker
                    value={startTime}
                    mode="time"
                    display="default"
                    onChange={handleStartTimeChange}
                  />
                )}
              </View>

              {/* Bitiş Saati */}
              <View style={[styles.formGroup, { flex: 1 }]}>
                <Text style={styles.label}>Bitiş</Text>
                <TouchableOpacity
                  style={styles.timeSelector}
                  onPress={() => setShowEndTimePicker(true)}
                >
                  <MaterialIcons name="access-time" size={20} color={Colors.PRIMARY} />
                  <Text style={styles.timeText}>{formatTime(endTime)}</Text>
                </TouchableOpacity>

                {showEndTimePicker && (
                  <DateTimePicker
                    value={endTime}
                    mode="time"
                    display="default"
                    onChange={handleEndTimeChange}
                  />
                )}
              </View>
            </View>

            {/* Vardiya Türü */}
            <View style={styles.formGroup}>
              <Text style={styles.label}>Vardiya Türü</Text>
              <View style={styles.shiftTypesContainer}>
                {shiftTypes.map(type => (
                  <TouchableOpacity
                    key={type.id}
                    style={[
                      styles.shiftTypeOption,
                      selectedShiftType === type.id && styles.selectedShiftType,
                      { borderColor: type.color }
                    ]}
                    onPress={() => handleShiftTypeChange(type.id)}
                  >
                    <View style={[styles.colorDot, { backgroundColor: type.color }]} />
                    <Text style={styles.shiftTypeText}>{type.name}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Saatlik Ücret */}
            <View style={styles.formGroup}>
              <Text style={styles.label}>Saatlik Ücret</Text>
              <View style={styles.inputWithIcon}>
                <Text style={styles.inputIcon}>₺</Text>
                <TextInput
                  style={styles.inputWithIconField}
                  value={hourlyRate}
                  onChangeText={setHourlyRate}
                  keyboardType="numeric"
                  placeholder="0.00"
                />
              </View>
            </View>

            {/* Mola Süresi */}
            <View style={styles.formGroup}>
              <Text style={styles.label}>Mola Süresi (dakika)</Text>
              <TextInput
                style={styles.input}
                value={breakDuration}
                onChangeText={setBreakDuration}
                keyboardType="numeric"
                placeholder="0"
              />
            </View>

            {/* Mesai ve Tatil Günü Seçenekleri */}
            <View style={styles.formGroup}>
              <View style={styles.switchContainer}>
                <View style={styles.switchInfo}>
                  <MaterialIcons name="alarm-on" size={24} color={isOvertime ? Colors.PRIMARY : '#666'} />
                  <Text style={styles.switchLabel}>Mesai</Text>
                </View>
                <Switch
                  value={isOvertime}
                  onValueChange={setIsOvertime}
                  trackColor={{ false: '#ddd', true: Colors.PRIMARY }}
                  thumbColor="#fff"
                />
              </View>

              <View style={styles.switchContainer}>
                <View style={styles.switchInfo}>
                  <MaterialIcons name="event" size={24} color={isHoliday ? '#e74c3c' : '#666'} />
                  <Text style={styles.switchLabel}>Tatil Günü</Text>
                </View>
                <Switch
                  value={isHoliday}
                  onValueChange={setIsHoliday}
                  trackColor={{ false: '#ddd', true: '#e74c3c' }}
                  thumbColor="#fff"
                />
              </View>
            </View>

            {/* Notlar */}
            <View style={styles.formGroup}>
              <Text style={styles.label}>Notlar</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={notes}
                onChangeText={setNotes}
                placeholder="Vardiya hakkında notlar..."
                multiline
                numberOfLines={4}
                textAlignVertical="top"
              />
            </View>

            {/* Hesaplanan Değerler */}
            <View style={styles.calculatedValues}>
              <View style={styles.calculatedItem}>
                <Text style={styles.calculatedLabel}>Süre:</Text>
                <Text style={styles.calculatedValue}>{duration.toFixed(2)} saat</Text>
              </View>

              <View style={styles.calculatedItem}>
                <Text style={styles.calculatedLabel}>Kazanç:</Text>
                <Text style={[styles.calculatedValue, styles.earningsValue]}>
                  {new Intl.NumberFormat('tr-TR', {
                    style: 'currency',
                    currency: 'TRY',
                  }).format(earnings)}
                </Text>
              </View>
            </View>
          </ScrollView>

          {/* Butonlar */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, styles.cancelButton]}
              onPress={onClose}
              disabled={loading}
            >
              <Text style={styles.cancelButtonText}>İptal</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.button, styles.saveButton]}
              onPress={handleSave}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <Text style={styles.saveButtonText}>Kaydet</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  dateSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    backgroundColor: '#fff',
  },
  dateText: {
    fontSize: 16,
    color: '#333',
    marginLeft: 8,
    fontWeight: '500',
  },
  timeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  timeSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    backgroundColor: '#fff',
  },
  timeText: {
    fontSize: 16,
    color: '#333',
    marginLeft: 8,
    fontWeight: '500',
  },
  shiftTypesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  shiftTypeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
    marginBottom: 8,
  },
  selectedShiftType: {
    backgroundColor: 'rgba(0, 120, 255, 0.1)',
    borderWidth: 2,
  },
  colorDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 8,
  },
  shiftTypeText: {
    fontSize: 14,
    color: '#333',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
  inputWithIcon: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 12,
    backgroundColor: '#fff',
  },
  inputIcon: {
    paddingHorizontal: 16,
    fontSize: 16,
    color: '#666',
  },
  inputWithIconField: {
    flex: 1,
    paddingVertical: 14,
    fontSize: 16,
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
    backgroundColor: '#f9f9f9',
    borderRadius: 12,
    padding: 12,
  },
  switchInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  switchLabel: {
    fontSize: 16,
    color: '#333',
    marginLeft: 8,
    fontWeight: '500',
  },
  calculatedValues: {
    backgroundColor: '#f9f9f9',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  calculatedItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  calculatedLabel: {
    fontSize: 16,
    color: '#666',
  },
  calculatedValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  earningsValue: {
    color: Colors.INCOME,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
    paddingTop: 16,
  },
  button: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButton: {
    backgroundColor: '#f0f0f0',
    marginRight: 8,
  },
  saveButton: {
    backgroundColor: Colors.PRIMARY,
    marginLeft: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#666',
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
  },
});

export default ShiftFormModal;
