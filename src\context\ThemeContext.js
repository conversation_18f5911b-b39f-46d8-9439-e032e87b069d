import React, { createContext, useState, useContext, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useColorScheme } from 'react-native';
import { Colors, lightTheme, darkTheme } from '../constants/colors';

// Tema context'i oluştur - varsayılan değerle başlat
const ThemeContext = createContext({
  theme: lightTheme, // Varsayılan olarak light tema kullan
  isDarkMode: false,
  themeType: 'light',
  isSystemTheme: true,
  isLoading: false,
  changeTheme: () => {},
  toggleUseSystemTheme: () => {}
});

/**
 * Tema sağlayıcı bileşeni
 * @param {Object} props - Bileşen props'ları
 * @returns {JSX.Element} ThemeProvider bileşeni
 */
export const ThemeProvider = ({ children }) => {
  // Sistem teması tercihi
  const systemColorScheme = useColorScheme();
  
  // Tema ve ayarlar state'i
  const [theme, setTheme] = useState('light');
  const [isSystemTheme, setIsSystemTheme] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  
  // Mevcut tema - varsayılan light tema kullan
  const themeType = isSystemTheme 
    ? (systemColorScheme || 'light') 
    : (theme || 'light');
    
  const isDarkMode = themeType === 'dark';
  
  // Tema objesini belirle - her zaman geçerli bir tema objesi döndür
  const currentTheme = isDarkMode ? darkTheme : lightTheme;

  // Tema ayarlarını yükle
  useEffect(() => {
    const loadThemeSettings = async () => {
      try {
        setIsLoading(true);
        const savedTheme = await AsyncStorage.getItem('userTheme');
        const savedIsSystemTheme = await AsyncStorage.getItem('useSystemTheme');
        
        if (savedTheme) {
          setTheme(savedTheme);
        }
        
        if (savedIsSystemTheme !== null) {
          setIsSystemTheme(savedIsSystemTheme === 'true');
        }
      } catch (error) {
        // Tema ayarları yükleme hatası
      } finally {
        setIsLoading(false);
      }
    };
    
    loadThemeSettings();
  }, []);

  /**
   * Temayı değiştirme işlevi
   * @param {string} newTheme - 'dark' veya 'light'
   */
  const changeTheme = async (newTheme) => {
    setTheme(newTheme);
    setIsSystemTheme(false);
    try {
      await AsyncStorage.setItem('userTheme', newTheme);
      await AsyncStorage.setItem('useSystemTheme', 'false');
    } catch (error) {
      // Tema ayarları kaydetme hatası
    }
  };

  /**
   * Sistem temasını kullanmayı aç/kapa
   * @param {boolean} useSystem - Sistem teması kullanılsın mı
   */
  const toggleUseSystemTheme = async (useSystem) => {
    setIsSystemTheme(useSystem);
    try {
      await AsyncStorage.setItem('useSystemTheme', String(useSystem));
    } catch (error) {
      // Tema ayarları kaydetme hatası
    }
  };

  // Context değeri - her zaman geçerli bir tema objesi sağla
  const value = {
    theme: currentTheme, // Her zaman geçerli tema objesi
    isDarkMode,
    themeType,
    isSystemTheme,
    isLoading,
    changeTheme,
    toggleUseSystemTheme,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

/**
 * Tema objesini doğrulama ve güvenli hale getirme fonksiyonu
 * @param {Object} theme - Doğrulanacak tema objesi
 * @returns {Object} Güvenli tema objesi
 */
const validateAndSafeTheme = (theme) => {
  if (!theme || typeof theme !== 'object') {
    // Geçersiz tema objesi, varsayılan tema kullan
    return lightTheme;
  }
  
  // Temel özelliklerin varlığını kontrol et
  const requiredProperties = ['SURFACE', 'PRIMARY', 'BACKGROUND', 'TEXT_PRIMARY', 'TEXT_SECONDARY'];
  const missingProperties = requiredProperties.filter(prop => !theme[prop]);
  
  if (missingProperties.length > 0) {
    // Eksik özellikleri varsayılan temadan tamamla
    return { ...lightTheme, ...theme };
  }
  
  return theme;
};

/**
 * Tema context'ini kullanmak için hook
 * @returns {Object} Tema context değerleri
 */
export const useTheme = () => {
  const context = useContext(ThemeContext);
  
  // Eğer context yoksa, varsayılan değerleri döndür
  if (!context) {
    // ThemeProvider dışında kullanılıyorsa varsayılan tema döndür
    return {
      theme: validateAndSafeTheme(lightTheme),
      isDarkMode: false,
      themeType: 'light',
      isSystemTheme: true,
      isLoading: false,
      changeTheme: () => {},
      toggleUseSystemTheme: () => {}
    };
  }
  
  // Context var ama theme undefined ise, varsayılan tema kullan
  const safeTheme = validateAndSafeTheme(context.theme || lightTheme);
  
  // Theme objesi kontrolü
  if (!context.theme) {
    // Theme objesi yoksa varsayılan tema kullan
  }
  
  return {
    ...context,
    theme: safeTheme
  };
};
