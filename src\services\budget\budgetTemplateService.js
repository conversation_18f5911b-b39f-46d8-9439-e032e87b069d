/**
 * Bütçe Şablonu Servisi
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md Stage 1 implementasyonu
 * 
 * Bu servis bütçe şablonları yönetimi için gerekli fonksiyonları sağlar
 */

/**
 * Tüm bütçe şablonlarını getirir
 * @param {Object} db - SQLite database instance
 * @param {Object} options - Filtreleme seçenekleri
 * @returns {Promise<Array>} Şablon listesi
 */
export const getBudgetTemplates = async (db, options = {}) => {
  try {
    let query = `
      SELECT * FROM budget_templates 
      WHERE is_active = 1
    `;

    const params = [];

    if (options.type) {
      query += ' AND type = ?';
      params.push(options.type);
    }

    if (options.period_type) {
      query += ' AND period_type = ?';
      params.push(options.period_type);
    }

    if (options.system_only) {
      query += ' AND is_system = 1';
    } else if (options.user_only) {
      query += ' AND is_system = 0';
    }

    query += ' ORDER BY is_system DESC, name ASC';

    const templates = await db.getAllAsync(query, params);

    return templates.map(template => ({
      ...template,
      template_data: JSON.parse(template.template_data || '{}')
    }));

  } catch (error) {
    console.error('❌ Bütçe şablonları getirme hatası:', error);
    throw error;
  }
};

/**
 * Belirli bir şablonun detaylarını getirir
 * @param {Object} db - SQLite database instance
 * @param {number} templateId - Şablon ID'si
 * @returns {Promise<Object>} Şablon detayları
 */
export const getBudgetTemplate = async (db, templateId) => {
  try {
    const template = await db.getFirstAsync(`
      SELECT * FROM budget_templates WHERE id = ?
    `, [templateId]);

    if (!template) {
      throw new Error('Şablon bulunamadı');
    }

    return {
      ...template,
      template_data: JSON.parse(template.template_data || '{}')
    };

  } catch (error) {
    console.error('❌ Bütçe şablonu getirme hatası:', error);
    throw error;
  }
};

/**
 * Yeni bütçe şablonu oluşturur
 * @param {Object} db - SQLite database instance
 * @param {Object} templateData - Şablon verileri
 * @returns {Promise<number>} Oluşturulan şablon ID'si
 */
export const createBudgetTemplate = async (db, templateData) => {
  try {
    const result = await db.runAsync(`
      INSERT INTO budget_templates 
      (name, description, type, period_type, template_data, is_system, is_active)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [
      templateData.name,
      templateData.description || null,
      templateData.type || 'category_based',
      templateData.period_type || 'monthly',
      JSON.stringify(templateData.template_data || {}),
      0, // User template
      1  // Active
    ]);

    const templateId = result.lastInsertRowId;
    console.log(`✅ Yeni bütçe şablonu oluşturuldu: ${templateData.name} (ID: ${templateId})`);
    return templateId;

  } catch (error) {
    console.error('❌ Bütçe şablonu oluşturma hatası:', error);
    throw error;
  }
};

/**
 * Şablondan bütçe oluşturur
 * @param {Object} db - SQLite database instance
 * @param {number} templateId - Şablon ID'si
 * @param {Object} budgetOptions - Bütçe seçenekleri
 * @returns {Promise<number>} Oluşturulan bütçe ID'si
 */
export const createBudgetFromTemplate = async (db, templateId, budgetOptions) => {
  try {
    // Şablon verilerini getir
    const template = await getBudgetTemplate(db, templateId);
    
    // Kullanıcı kategorilerini getir
    const userCategories = await db.getAllAsync(`
      SELECT * FROM categories WHERE type = 'expense' ORDER BY name
    `);

    return await db.withTransactionAsync(async () => {
      // Ana bütçe kaydını oluştur
      const budgetResult = await db.runAsync(`
        INSERT INTO budgets_enhanced 
        (name, description, type, period_type, start_date, end_date, total_limit, currency, status)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        budgetOptions.name || template.name,
        budgetOptions.description || template.description,
        template.type,
        template.period_type,
        budgetOptions.start_date,
        budgetOptions.end_date || null,
        budgetOptions.total_limit || 0,
        budgetOptions.currency || 'TRY',
        'active'
      ]);

      const budgetId = budgetResult.lastInsertRowId;

      // Şablon kategorilerini bütçeye ekle
      if (template.template_data.categories) {
        for (const templateCategory of template.template_data.categories) {
          // Kategoriyi kullanıcı kategorilerinde bul
          const userCategory = userCategories.find(cat => 
            cat.name.toLowerCase() === templateCategory.name.toLowerCase()
          );

          if (userCategory) {
            // Yüzde veya sabit miktar hesapla
            let limitAmount = 0;
            if (templateCategory.percentage && budgetOptions.total_limit) {
              limitAmount = (budgetOptions.total_limit * templateCategory.percentage) / 100;
            } else if (templateCategory.amount) {
              limitAmount = templateCategory.amount;
            }

            await db.runAsync(`
              INSERT INTO budget_categories_enhanced 
              (budget_id, category_id, limit_amount, currency)
              VALUES (?, ?, ?, ?)
            `, [
              budgetId,
              userCategory.id,
              limitAmount,
              budgetOptions.currency || 'TRY'
            ]);
          }
        }
      }

      // Varsayılan uyarı ayarlarını oluştur
      await db.runAsync(`
        INSERT INTO budget_alert_settings 
        (budget_id, threshold_75, threshold_90, threshold_100, daily_limit_exceeded, category_limit_exceeded)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [budgetId, 1, 1, 1, 1, 1]);

      console.log(`✅ Şablondan bütçe oluşturuldu: ${budgetOptions.name} (ID: ${budgetId})`);
      return budgetId;
    });

  } catch (error) {
    console.error('❌ Şablondan bütçe oluşturma hatası:', error);
    throw error;
  }
};

/**
 * Mevcut bütçeyi şablon olarak kaydet
 * @param {Object} db - SQLite database instance
 * @param {number} budgetId - Bütçe ID'si
 * @param {Object} templateOptions - Şablon seçenekleri
 * @returns {Promise<number>} Oluşturulan şablon ID'si
 */
export const saveBudgetAsTemplate = async (db, budgetId, templateOptions) => {
  try {
    // Bütçe detaylarını getir
    const budget = await db.getFirstAsync(`
      SELECT * FROM budgets_enhanced WHERE id = ?
    `, [budgetId]);

    if (!budget) {
      throw new Error('Bütçe bulunamadı');
    }

    // Bütçe kategorilerini getir
    const categories = await db.getAllAsync(`
      SELECT 
        bc.*,
        c.name as category_name
      FROM budget_categories_enhanced bc
      JOIN categories c ON bc.category_id = c.id
      WHERE bc.budget_id = ?
    `, [budgetId]);

    // Şablon verilerini hazırla
    const templateData = {
      categories: categories.map(cat => ({
        name: cat.category_name,
        amount: cat.limit_amount,
        percentage: budget.total_limit > 0 ? 
          Math.round((cat.limit_amount / budget.total_limit) * 100) : 0
      }))
    };

    // Şablonu oluştur
    const result = await db.runAsync(`
      INSERT INTO budget_templates 
      (name, description, type, period_type, template_data, is_system, is_active)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [
      templateOptions.name || `${budget.name} Şablonu`,
      templateOptions.description || `${budget.name} bütçesinden oluşturulan şablon`,
      budget.type,
      budget.period_type,
      JSON.stringify(templateData),
      0, // User template
      1  // Active
    ]);

    const templateId = result.lastInsertRowId;
    console.log(`✅ Bütçe şablon olarak kaydedildi: ${templateOptions.name} (ID: ${templateId})`);
    return templateId;

  } catch (error) {
    console.error('❌ Bütçeyi şablon olarak kaydetme hatası:', error);
    throw error;
  }
};

/**
 * Bütçe şablonunu günceller
 * @param {Object} db - SQLite database instance
 * @param {number} templateId - Şablon ID'si
 * @param {Object} updateData - Güncellenecek veriler
 * @returns {Promise<boolean>} Başarı durumu
 */
export const updateBudgetTemplate = async (db, templateId, updateData) => {
  try {
    // Sistem şablonlarının güncellenmesini engelle
    const template = await db.getFirstAsync(`
      SELECT is_system FROM budget_templates WHERE id = ?
    `, [templateId]);

    if (template?.is_system) {
      throw new Error('Sistem şablonları güncellenemez');
    }

    const result = await db.runAsync(`
      UPDATE budget_templates 
      SET name = ?, description = ?, type = ?, period_type = ?, 
          template_data = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND is_system = 0
    `, [
      updateData.name,
      updateData.description,
      updateData.type,
      updateData.period_type,
      JSON.stringify(updateData.template_data || {}),
      templateId
    ]);

    console.log(`✅ Bütçe şablonu güncellendi (ID: ${templateId})`);
    return result.changes > 0;

  } catch (error) {
    console.error('❌ Bütçe şablonu güncelleme hatası:', error);
    throw error;
  }
};

/**
 * Bütçe şablonunu siler
 * @param {Object} db - SQLite database instance
 * @param {number} templateId - Şablon ID'si
 * @returns {Promise<boolean>} Başarı durumu
 */
export const deleteBudgetTemplate = async (db, templateId) => {
  try {
    // Sistem şablonlarının silinmesini engelle
    const result = await db.runAsync(`
      DELETE FROM budget_templates 
      WHERE id = ? AND is_system = 0
    `, [templateId]);

    if (result.changes === 0) {
      throw new Error('Şablon bulunamadı veya sistem şablonu silinemez');
    }

    console.log(`✅ Bütçe şablonu silindi (ID: ${templateId})`);
    return true;

  } catch (error) {
    console.error('❌ Bütçe şablonu silme hatası:', error);
    throw error;
  }
};
