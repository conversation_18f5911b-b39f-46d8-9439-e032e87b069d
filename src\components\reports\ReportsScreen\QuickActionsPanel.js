import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from 'react-native';

/**
 * Quick Actions Panel Component
 * Provides quick access to main report creation and management functions
 * Follows REPORTS_SCREEN_REDESIGN_PLAN.md specifications
 */
const QuickActionsPanel = ({
  onCreateTable,
  onOpenLibrary,
  onCreateCustomReport,
  onOpenDashboardBuilder,
  onOpenReportBuilder,
  onOpenTemplateSelector,
  onAIAnalysis,
  onSmartInsights,
  onPredictiveAnalytics,
  theme
}) => {
  const quickActions = [
    {
      id: 'table',
      title: 'Ta<PERSON><PERSON>',
      subtitle: 'İnteraktif tablo editörü',
      icon: '📊',
      color: theme.PRIMARY,
      onPress: onCreateTable,
    },
    {
      id: 'library',
      title: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
      subtitle: 'Kaydedilen raporlar',
      icon: '📚',
      color: theme.SUCCESS,
      onPress: onOpenLibrary,
    },
    {
      id: 'custom',
      title: '<PERSON><PERSON> Rapor',
      subtitle: '<PERSON>ü<PERSON>ü<PERSON>-b<PERSON><PERSON> editör',
      icon: '🎨',
      color: theme.WARNING,
      onPress: onCreateCustomReport,
    },
    {
      id: 'dashboard',
      title: 'Dashboard',
      subtitle: 'İnteraktif dashboard',
      icon: '📈',
      color: theme.INFO,
      onPress: onOpenDashboardBuilder,
    },
    {
      id: 'builder',
      title: 'Rapor Builder',
      subtitle: 'Gelişmiş rapor editörü',
      icon: '🔧',
      color: theme.SECONDARY,
      onPress: onOpenReportBuilder,
    },
    {
      id: 'templates',
      title: 'Şablonlar',
      subtitle: 'Hazır rapor şablonları',
      icon: '📋',
      color: theme.ACCENT,
      onPress: onOpenTemplateSelector,
    },
    {
      id: 'ai_analysis',
      title: 'AI Analiz',
      subtitle: 'Akıllı finansal analiz',
      icon: '🧠',
      color: '#6366F1',
      onPress: onAIAnalysis,
    },
    {
      id: 'smart_insights',
      title: 'Akıllı İçgörüler',
      subtitle: 'AI destekli öneriler',
      icon: '💡',
      color: '#8B5CF6',
      onPress: onSmartInsights,
    },
    {
      id: 'predictive',
      title: 'Tahmin Analizi',
      subtitle: 'Gelecek projeksiyonları',
      icon: '🔮',
      color: '#EC4899',
      onPress: onPredictiveAnalytics,
    },
  ];

  return (
    <View style={[styles.container, { backgroundColor: theme.SURFACE }]}>
      <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
        🚀 Hızlı Aksiyonlar
      </Text>
      
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.actionsScrollView}
      >
        {quickActions.map((action) => (
          <TouchableOpacity
            key={action.id}
            style={[styles.actionCard, { backgroundColor: action.color }]}
            onPress={action.onPress}
            activeOpacity={0.8}
          >
            <Text style={[styles.actionIcon, { color: theme.SURFACE }]}>
              {action.icon}
            </Text>
            <Text style={[styles.actionTitle, { color: theme.SURFACE }]}>
              {action.title}
            </Text>
            <Text style={[styles.actionSubtitle, { color: theme.SURFACE }]}>
              {action.subtitle}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Secondary Actions */}
      <View style={styles.secondaryActions}>
        <TouchableOpacity 
          style={[styles.secondaryAction, { backgroundColor: theme.BACKGROUND }]}
          onPress={() => {/* Handle import */}}
        >
          <Text style={[styles.secondaryActionIcon, { color: theme.TEXT_PRIMARY }]}>
            📥
          </Text>
          <Text style={[styles.secondaryActionText, { color: theme.TEXT_PRIMARY }]}>
            İçe Aktar
          </Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.secondaryAction, { backgroundColor: theme.BACKGROUND }]}
          onPress={() => {/* Handle export all */}}
        >
          <Text style={[styles.secondaryActionIcon, { color: theme.TEXT_PRIMARY }]}>
            📤
          </Text>
          <Text style={[styles.secondaryActionText, { color: theme.TEXT_PRIMARY }]}>
            Toplu Dışa Aktar
          </Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.secondaryAction, { backgroundColor: theme.BACKGROUND }]}
          onPress={() => {/* Handle schedule */}}
        >
          <Text style={[styles.secondaryActionIcon, { color: theme.TEXT_PRIMARY }]}>
            ⏰
          </Text>
          <Text style={[styles.secondaryActionText, { color: theme.TEXT_PRIMARY }]}>
            Zamanlama
          </Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.secondaryAction, { backgroundColor: theme.BACKGROUND }]}
          onPress={() => {/* Handle settings */}}
        >
          <Text style={[styles.secondaryActionIcon, { color: theme.TEXT_PRIMARY }]}>
            ⚙️
          </Text>
          <Text style={[styles.secondaryActionText, { color: theme.TEXT_PRIMARY }]}>
            Ayarlar
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  actionsScrollView: {
    marginHorizontal: -4,
    marginBottom: 16,
  },
  actionCard: {
    width: 120,
    height: 100,
    borderRadius: 12,
    padding: 12,
    marginHorizontal: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  actionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 4,
  },
  actionSubtitle: {
    fontSize: 10,
    textAlign: 'center',
    opacity: 0.9,
  },
  secondaryActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  secondaryAction: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    marginHorizontal: 2,
    borderRadius: 8,
  },
  secondaryActionIcon: {
    fontSize: 16,
    marginRight: 6,
  },
  secondaryActionText: {
    fontSize: 12,
    fontWeight: '600',
  },
});

export default QuickActionsPanel;
