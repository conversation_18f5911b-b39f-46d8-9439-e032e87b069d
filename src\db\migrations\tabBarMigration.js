/**
 * Tab bar özelleştirme için veritabanı migrasyonu
 * @param {Object} db - SQLite veritabanı bağlantısı
 */
export const migrateTabBarSettings = async (db) => {
  try {
    // Tab bar ayarları tablosu var mı kontrol et
    const tableExists = await db.getFirstAsync(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name='tab_bar_config'
    `);

    if (!tableExists) {
      // Tablo yoksa oluştur
      await db.execAsync(`
        CREATE TABLE tab_bar_config (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          position INTEGER NOT NULL,
          tab_id TEXT NOT NULL,
          title TEXT NOT NULL,
          icon TEXT NOT NULL,
          is_visible INTEGER DEFAULT 1,
          is_default INTEGER DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Varsayılan tab konfigürasyonunu ekle
      const defaultTabs = [
        { position: 0, tabId: 'index', title: 'Ana Sayfa', icon: 'home', isDefault: 1 },
        { position: 1, tabId: 'dashboard', title: 'Dashboard', icon: 'dashboard', isDefault: 1 },
        { position: 2, tabId: 'settings', title: 'Ayarlar', icon: 'settings', isDefault: 1 }
      ];

      for (const tab of defaultTabs) {
        await db.runAsync(`
          INSERT INTO tab_bar_config (position, tab_id, title, icon, is_visible, is_default)
          VALUES (?, ?, ?, ?, 1, ?)
        `, [tab.position, tab.tabId, tab.title, tab.icon, tab.isDefault]);
      }
    }
    
    console.log('Tab bar migrasyonu tamamlandı');
    return true;
  } catch (error) {
    console.error('Tab bar migrasyonu sırasında hata:', error);
    return false;
  }
};
