/**
 * Para Birimi Seçici Komponenti
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md - Stage 2, Phase 1
 * 
 * TRY/USD/EUR seçimi
 * Maksimum 200 satır - Tek sorum<PERSON>luk prensibi
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../../../context/ThemeContext';

/**
 * Para birimi seçici komponenti
 * @param {Object} props - Component props
 * @param {string} props.selectedCurrency - Seçili para birimi kodu ('TRY', 'USD', 'EUR')
 * @param {Function} props.onCurrencySelect - Para birimi seçim callback fonksiyonu
 * @param {boolean} props.showLabel - Label gösterilip gösterilmeyeceği
 * @param {Object} props.theme - Tema objesi (opsiyonel, context'ten alınır)
 */
const CurrencySelector = ({ 
  selectedCurrency, 
  onCurrencySelect, 
  showLabel = true,
  theme: propTheme 
}) => {
  const { theme } = useTheme();
  const currentTheme = propTheme || theme;

  // State yönetimi
  const [showModal, setShowModal] = useState(false);

  // Para birimleri tanımları
  const currencies = [
    {
      code: 'TRY',
      symbol: '₺',
      name: 'Türk Lirası',
      flag: '🇹🇷',
      description: 'Türkiye\'nin resmi para birimi',
      recommended: true
    },
    {
      code: 'USD',
      symbol: '$',
      name: 'Amerikan Doları',
      flag: '🇺🇸',
      description: 'Dünya\'nın en yaygın rezerv para birimi',
      recommended: false
    },
    {
      code: 'EUR',
      symbol: '€',
      name: 'Euro',
      flag: '🇪🇺',
      description: 'Avrupa Birliği\'nin resmi para birimi',
      recommended: false
    }
  ];

  /**
   * Para birimi seçim işleyicisi
   * @param {string} currencyCode - Seçilen para birimi kodu
   */
  const handleCurrencySelect = (currencyCode) => {
    if (onCurrencySelect) {
      onCurrencySelect(currencyCode);
    }
    setShowModal(false);
  };

  /**
   * Modal açma işleyicisi
   */
  const handleOpenModal = () => {
    setShowModal(true);
  };

  /**
   * Modal kapatma işleyicisi
   */
  const handleCloseModal = () => {
    setShowModal(false);
  };

  // Seçili para birimi bilgisi
  const selectedCurrencyInfo = currencies.find(c => c.code === selectedCurrency) || currencies[0];

  /**
   * Para birimi listesi render fonksiyonu
   * @param {Object} item - Para birimi objesi
   * @returns {JSX.Element} Para birimi list item
   */
  const renderCurrencyItem = ({ item }) => {
    const isSelected = item.code === selectedCurrency;

    return (
      <TouchableOpacity
        style={[
          styles.currencyItem,
          {
            backgroundColor: isSelected 
              ? currentTheme.PRIMARY + '20' 
              : currentTheme.SURFACE,
            borderColor: isSelected 
              ? currentTheme.PRIMARY 
              : currentTheme.BORDER,
          }
        ]}
        onPress={() => handleCurrencySelect(item.code)}
        activeOpacity={0.7}
      >
        {/* Önerilen rozeti */}
        {item.recommended && (
          <View style={[styles.recommendedBadge, { backgroundColor: currentTheme.SUCCESS }]}>
            <Text style={[styles.recommendedText, { color: currentTheme.WHITE }]}>
              Önerilen
            </Text>
          </View>
        )}

        <View style={styles.currencyHeader}>
          <View style={styles.currencyIcon}>
            <Text style={styles.flagEmoji}>{item.flag}</Text>
          </View>
          
          <View style={styles.currencyInfo}>
            <View style={styles.currencyTitleRow}>
              <Text style={[styles.currencyCode, { color: currentTheme.TEXT_PRIMARY }]}>
                {item.code}
              </Text>
              <Text style={[styles.currencySymbol, { color: currentTheme.PRIMARY }]}>
                {item.symbol}
              </Text>
            </View>
            <Text style={[styles.currencyName, { color: currentTheme.TEXT_PRIMARY }]}>
              {item.name}
            </Text>
            <Text style={[styles.currencyDescription, { color: currentTheme.TEXT_SECONDARY }]}>
              {item.description}
            </Text>
          </View>

          <MaterialIcons
            name={isSelected ? 'radio-button-checked' : 'radio-button-unchecked'}
            size={24}
            color={isSelected ? currentTheme.PRIMARY : currentTheme.TEXT_SECONDARY}
          />
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      {/* Label */}
      {showLabel && (
        <Text style={[styles.label, { color: currentTheme.TEXT_SECONDARY }]}>
          Para Birimi
        </Text>
      )}

      {/* Seçim butonu */}
      <TouchableOpacity
        style={[
          styles.selectorButton,
          {
            backgroundColor: currentTheme.SURFACE,
            borderColor: currentTheme.BORDER,
          }
        ]}
        onPress={handleOpenModal}
        activeOpacity={0.7}
      >
        <View style={styles.selectedCurrency}>
          <Text style={styles.selectedFlag}>{selectedCurrencyInfo.flag}</Text>
          <View style={styles.selectedInfo}>
            <Text style={[styles.selectedCode, { color: currentTheme.TEXT_PRIMARY }]}>
              {selectedCurrencyInfo.code}
            </Text>
            <Text style={[styles.selectedName, { color: currentTheme.TEXT_SECONDARY }]}>
              {selectedCurrencyInfo.name}
            </Text>
          </View>
          <Text style={[styles.selectedSymbol, { color: currentTheme.PRIMARY }]}>
            {selectedCurrencyInfo.symbol}
          </Text>
        </View>
        
        <MaterialIcons 
          name="expand-more" 
          size={24} 
          color={currentTheme.TEXT_SECONDARY} 
        />
      </TouchableOpacity>

      {/* Para birimi seçim modalı */}
      <Modal
        visible={showModal}
        animationType="slide"
        transparent={true}
        onRequestClose={handleCloseModal}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: currentTheme.SURFACE }]}>
            {/* Modal header */}
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: currentTheme.TEXT_PRIMARY }]}>
                Para Birimi Seçin
              </Text>
              <TouchableOpacity onPress={handleCloseModal}>
                <MaterialIcons name="close" size={24} color={currentTheme.TEXT_SECONDARY} />
              </TouchableOpacity>
            </View>

            {/* Para birimi listesi */}
            <FlatList
              data={currencies}
              renderItem={renderCurrencyItem}
              keyExtractor={(item) => item.code}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.currencyList}
            />
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  selectorButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  selectedCurrency: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 12,
  },
  selectedFlag: {
    fontSize: 24,
  },
  selectedInfo: {
    flex: 1,
  },
  selectedCode: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  selectedName: {
    fontSize: 13,
  },
  selectedSymbol: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 16,
    maxHeight: '70%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  currencyList: {
    padding: 16,
  },
  currencyItem: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    position: 'relative',
  },
  recommendedBadge: {
    position: 'absolute',
    top: -6,
    right: 12,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
    zIndex: 1,
  },
  recommendedText: {
    fontSize: 10,
    fontWeight: 'bold',
  },
  currencyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  currencyIcon: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  flagEmoji: {
    fontSize: 24,
  },
  currencyInfo: {
    flex: 1,
  },
  currencyTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 2,
  },
  currencyCode: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  currencySymbol: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  currencyName: {
    fontSize: 14,
    marginBottom: 2,
  },
  currencyDescription: {
    fontSize: 12,
    lineHeight: 16,
  },
});

export default CurrencySelector;
