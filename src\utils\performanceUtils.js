/**
 * Performance Utilities
 * Database ve genel performance optimizasyonları
 */

/**
 * Database Query Optimizer
 * SQL query'lerini optimize eder
 */
export class QueryOptimizer {
  constructor(db) {
    this.db = db;
    this.queryCache = new Map();
    this.indexCache = new Set();
  }

  /**
   * Query'yi cache'ler ve optimize eder
   * @param {string} query - SQL query
   * @param {Array} params - Query parametreleri
   * @param {number} ttl - Cache TTL (saniye)
   * @returns {Promise} Query sonucu
   */
  async executeOptimizedQuery(query, params = [], ttl = 300) {
    const cacheKey = this.generateCacheKey(query, params);
    
    // Cache'den kontrol et
    if (this.queryCache.has(cacheKey)) {
      const cached = this.queryCache.get(cacheKey);
      if (Date.now() - cached.timestamp < ttl * 1000) {
        return cached.data;
      }
    }

    // Query'yi çalıştır
    const result = await this.db.getAllAsync(query, params);
    
    // Cache'e kaydet
    this.queryCache.set(cacheKey, {
      data: result,
      timestamp: Date.now()
    });

    return result;
  }

  /**
   * Batch query executor
   * Birden fazla query'yi batch olarak çalıştırır
   * @param {Array} queries - Query listesi
   * @returns {Promise<Array>} Sonuçlar
   */
  async executeBatchQueries(queries) {
    const transaction = await this.db.withTransactionAsync(async () => {
      const results = [];
      for (const { query, params } of queries) {
        const result = await this.db.getAllAsync(query, params);
        results.push(result);
      }
      return results;
    });
    return transaction;
  }

  /**
   * Index oluşturur (eğer yoksa)
   * @param {string} tableName - Tablo adı
   * @param {Array} columns - Index kolonları
   * @param {boolean} unique - Unique index mi
   */
  async ensureIndex(tableName, columns, unique = false) {
    const indexName = `idx_${tableName}_${columns.join('_')}`;
    
    if (this.indexCache.has(indexName)) {
      return;
    }

    try {
      const uniqueKeyword = unique ? 'UNIQUE' : '';
      const query = `CREATE ${uniqueKeyword} INDEX IF NOT EXISTS ${indexName} ON ${tableName} (${columns.join(', ')})`;
      await this.db.execAsync(query);
      this.indexCache.add(indexName);
      console.log(`Index created: ${indexName}`);
    } catch (error) {
      console.error(`Index creation failed: ${indexName}`, error);
    }
  }

  /**
   * Cache key generator
   * @param {string} query - SQL query
   * @param {Array} params - Parametreler
   * @returns {string} Cache key
   */
  generateCacheKey(query, params) {
    return `${query}_${JSON.stringify(params)}`;
  }

  /**
   * Cache'i temizler
   */
  clearCache() {
    this.queryCache.clear();
  }

  /**
   * Cache istatistikleri
   * @returns {Object} Cache stats
   */
  getCacheStats() {
    return {
      size: this.queryCache.size,
      indexes: this.indexCache.size,
      memory: JSON.stringify([...this.queryCache.entries()]).length
    };
  }
}

/**
 * Memory Manager
 * Bellek kullanımını optimize eder
 */
export class MemoryManager {
  constructor() {
    this.objectPool = new Map();
    this.weakRefs = new Set();
  }

  /**
   * Object pool'dan obje alır
   * @param {string} type - Obje tipi
   * @param {Function} factory - Factory fonksiyon
   * @returns {Object} Pooled obje
   */
  getPooledObject(type, factory) {
    if (!this.objectPool.has(type)) {
      this.objectPool.set(type, []);
    }

    const pool = this.objectPool.get(type);
    
    if (pool.length > 0) {
      return pool.pop();
    }

    return factory();
  }

  /**
   * Objeyi pool'a geri döndürür
   * @param {string} type - Obje tipi
   * @param {Object} obj - Döndürülecek obje
   */
  returnToPool(type, obj) {
    if (!this.objectPool.has(type)) {
      this.objectPool.set(type, []);
    }

    // Objeyi temizle
    if (obj && typeof obj.reset === 'function') {
      obj.reset();
    }

    this.objectPool.get(type).push(obj);
  }

  /**
   * Weak reference ekler
   * @param {Object} obj - Referans objesi
   */
  addWeakRef(obj) {
    this.weakRefs.add(new WeakRef(obj));
  }

  /**
   * Garbage collection tetikler
   */
  forceGC() {
    if (global.gc) {
      global.gc();
    }
  }

  /**
   * Pool'ları temizler
   */
  clearPools() {
    this.objectPool.clear();
    this.weakRefs.clear();
  }
}

/**
 * Performance Monitor
 * Performance metrikleri toplar
 */
export class PerformanceMonitor {
  constructor() {
    this.metrics = new Map();
    this.startTimes = new Map();
  }

  /**
   * Performance ölçümü başlatır
   * @param {string} name - Ölçüm adı
   */
  start(name) {
    this.startTimes.set(name, Date.now());
  }

  /**
   * Performance ölçümü bitirir
   * @param {string} name - Ölçüm adı
   * @returns {number} Geçen süre (ms)
   */
  end(name) {
    const startTime = this.startTimes.get(name);
    if (!startTime) {
      console.warn(`Performance measurement '${name}' was not started`);
      return 0;
    }

    const duration = Date.now() - startTime;
    this.startTimes.delete(name);

    // Metriği kaydet
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    this.metrics.get(name).push(duration);

    return duration;
  }

  /**
   * Metrik istatistikleri
   * @param {string} name - Metrik adı
   * @returns {Object} İstatistikler
   */
  getStats(name) {
    const measurements = this.metrics.get(name);
    if (!measurements || measurements.length === 0) {
      return null;
    }

    const sorted = [...measurements].sort((a, b) => a - b);
    const sum = measurements.reduce((a, b) => a + b, 0);

    return {
      count: measurements.length,
      min: sorted[0],
      max: sorted[sorted.length - 1],
      avg: sum / measurements.length,
      median: sorted[Math.floor(sorted.length / 2)],
      p95: sorted[Math.floor(sorted.length * 0.95)],
      p99: sorted[Math.floor(sorted.length * 0.99)]
    };
  }

  /**
   * Tüm metrikleri döndürür
   * @returns {Object} Tüm metrikler
   */
  getAllStats() {
    const stats = {};
    for (const [name] of this.metrics) {
      stats[name] = this.getStats(name);
    }
    return stats;
  }

  /**
   * Metrikleri temizler
   */
  clear() {
    this.metrics.clear();
    this.startTimes.clear();
  }
}

/**
 * Batch Processor
 * İşlemleri batch'ler ve optimize eder
 */
export class BatchProcessor {
  constructor(options = {}) {
    this.batchSize = options.batchSize || 50;
    this.delay = options.delay || 100;
    this.queue = [];
    this.processing = false;
    this.timeoutId = null;
  }

  /**
   * İşlemi queue'ya ekler
   * @param {Function} operation - İşlem fonksiyonu
   * @param {*} data - İşlem verisi
   * @returns {Promise} İşlem promise'i
   */
  add(operation, data) {
    return new Promise((resolve, reject) => {
      this.queue.push({ operation, data, resolve, reject });
      this.scheduleProcessing();
    });
  }

  /**
   * İşlemi zamanlar
   */
  scheduleProcessing() {
    if (this.processing) return;

    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
    }

    this.timeoutId = setTimeout(() => {
      this.process();
    }, this.delay);
  }

  /**
   * Queue'yu işler
   */
  async process() {
    if (this.processing || this.queue.length === 0) return;

    this.processing = true;

    try {
      const batch = this.queue.splice(0, this.batchSize);
      
      // Batch'i paralel olarak işle
      await Promise.allSettled(
        batch.map(async ({ operation, data, resolve, reject }) => {
          try {
            const result = await operation(data);
            resolve(result);
          } catch (error) {
            reject(error);
          }
        })
      );

      // Eğer queue'da daha fazla işlem varsa devam et
      if (this.queue.length > 0) {
        setTimeout(() => this.process(), 0);
      }
    } catch (error) {
      console.error('Batch processing error:', error);
    } finally {
      this.processing = false;
    }
  }

  /**
   * Queue'yu temizler
   */
  clear() {
    this.queue = [];
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
  }
}

// Global instances
export const queryOptimizer = new QueryOptimizer();
export const memoryManager = new MemoryManager();
export const performanceMonitor = new PerformanceMonitor();
export const batchProcessor = new BatchProcessor();

export default {
  QueryOptimizer,
  MemoryManager,
  PerformanceMonitor,
  BatchProcessor,
  queryOptimizer,
  memoryManager,
  performanceMonitor,
  batchProcessor,
};
