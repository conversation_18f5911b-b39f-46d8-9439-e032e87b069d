/**
 * Bütçe raporları için servis fonksiyonları
 */
import { format, parseISO, startOfMonth, endOfMonth, addMonths, subMonths } from 'date-fns';
import * as budgetService from './budgetService';

/**
 * Bir bütçenin raporlarını getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} budgetId - Bütçe ID
 * @returns {Promise<Array>} Bütçe raporları listesi
 */
export const getBudgetReports = async (db, budgetId) => {
  try {
    const reports = await db.getAllAsync(`
      SELECT *
      FROM budget_reports
      WHERE budget_id = ?
      ORDER BY report_date DESC
    `, [budgetId]);

    return reports;
  } catch (error) {
    console.error('Bütçe raporlarını getirme hatası:', error);
    throw error;
  }
};

/**
 * Bir bütçe raporunu getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} reportId - Rapor ID
 * @returns {Promise<Object|null>} Bütçe raporu
 */
export const getBudgetReportById = async (db, reportId) => {
  try {
    const report = await db.getFirstAsync(`
      SELECT br.*, b.name as budget_name
      FROM budget_reports br
      JOIN budgets b ON br.budget_id = b.id
      WHERE br.id = ?
    `, [reportId]);

    return report;
  } catch (error) {
    console.error('Bütçe raporu getirme hatası:', error);
    throw error;
  }
};

/**
 * Bütçe raporunu oluşturur
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} budget - Bütçe nesnesi
 * @param {string} reportDate - Rapor tarihi
 * @param {string} notes - Notlar
 * @returns {Promise<number>} Oluşturulan raporun ID'si
 */
export const createBudgetReport = async (db, budget, reportDate = new Date(), notes = '') => {
  try {
    if (!budget) {
      throw new Error('Bütçe bulunamadı');
    }

    // Bütçe detaylarını getir
    const budgetDetails = await budgetService.getBudgetDetails(db, budget.id);

    if (!budgetDetails) {
      throw new Error('Bütçe detayları bulunamadı');
    }

    // Rapor verilerini hazırla
    const formattedDate = typeof reportDate === 'string' ? reportDate : format(reportDate, 'yyyy-MM-dd');
    const plannedAmount = budgetDetails.totalBudget || 0;
    const actualAmount = budgetDetails.totalSpent || 0;
    const varianceAmount = plannedAmount - actualAmount;
    const variancePercent = plannedAmount > 0 ? (varianceAmount / plannedAmount) * 100 : 0;

    // Raporu kaydet
    const result = await db.runAsync(`
      INSERT INTO budget_reports (
        budget_id, report_date, planned_amount, actual_amount,
        variance_amount, variance_percent, notes
      )
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [
      budget.id,
      formattedDate,
      plannedAmount,
      actualAmount,
      varianceAmount,
      variancePercent,
      notes
    ]);

    return result.lastInsertRowId;
  } catch (error) {
    console.error('Bütçe raporu oluşturma hatası:', error);
    throw error;
  }
};

/**
 * Bir bütçe raporunu günceller
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} reportId - Rapor ID
 * @param {Object} report - Güncellenecek rapor verileri
 * @returns {Promise<boolean>} Başarılı ise true
 */
export const updateBudgetReport = async (db, reportId, report) => {
  try {
    await db.runAsync(`
      UPDATE budget_reports
      SET notes = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [
      report.notes || '',
      reportId
    ]);

    return true;
  } catch (error) {
    console.error('Bütçe raporu güncelleme hatası:', error);
    throw error;
  }
};

/**
 * Bir bütçe raporunu siler
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} reportId - Rapor ID
 * @returns {Promise<boolean>} Başarılı ise true
 */
export const deleteBudgetReport = async (db, reportId) => {
  try {
    await db.runAsync(`
      DELETE FROM budget_reports
      WHERE id = ?
    `, [reportId]);

    return true;
  } catch (error) {
    console.error('Bütçe raporu silme hatası:', error);
    throw error;
  }
};

/**
 * Aylık bütçe raporlarını oluşturur
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Date} date - Rapor tarihi
 * @returns {Promise<Array>} Oluşturulan raporların ID'leri
 */
export const generateMonthlyBudgetReports = async (db, date = new Date()) => {
  try {
    const reportDate = format(date, 'yyyy-MM-dd');
    const monthStart = format(startOfMonth(date), 'yyyy-MM-dd');
    const monthEnd = format(endOfMonth(date), 'yyyy-MM-dd');

    // Ay içindeki aktif bütçeleri getir
    const budgets = await db.getAllAsync(`
      SELECT * FROM budgets
      WHERE start_date <= ? AND (end_date IS NULL OR end_date >= ?)
      AND repeat_type = 'monthly'
    `, [monthEnd, monthStart]);

    const reportIds = [];

    // Her bütçe için rapor oluştur
    for (const budget of budgets) {
      // Daha önce bu ay için rapor oluşturulmuş mu kontrol et
      const existingReport = await db.getFirstAsync(`
        SELECT id FROM budget_reports
        WHERE budget_id = ? AND strftime('%Y-%m', report_date) = strftime('%Y-%m', ?)
      `, [budget.id, reportDate]);

      if (existingReport) {
        continue; // Bu ay için zaten rapor var, atla
      }

      // Rapor oluştur
      const reportId = await createBudgetReport(db, budget, reportDate, 'Otomatik oluşturulan aylık rapor');
      reportIds.push(reportId);
    }

    return reportIds;
  } catch (error) {
    console.error('Aylık bütçe raporları oluşturma hatası:', error);
    throw error;
  }
};

/**
 * Bütçe karşılaştırma raporu oluşturur
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} budgetId - Bütçe ID
 * @param {number} months - Karşılaştırılacak ay sayısı
 * @returns {Promise<Object>} Karşılaştırma raporu
 */
export const generateBudgetComparisonReport = async (db, budgetId, months = 3) => {
  try {
    // Bütçe bilgilerini getir
    const budget = await budgetService.getBudgetById(db, budgetId);

    if (!budget) {
      throw new Error('Bütçe bulunamadı');
    }

    const currentDate = new Date();
    const comparison = {
      budget_id: budgetId,
      budget_name: budget.name,
      current_month: {
        date: format(currentDate, 'yyyy-MM'),
        planned: budget.amount,
        actual: budget.usage.total_spent,
        variance: budget.amount - budget.usage.total_spent,
        variance_percent: budget.amount > 0 ? ((budget.amount - budget.usage.total_spent) / budget.amount) * 100 : 0
      },
      previous_months: []
    };

    // Önceki aylar için verileri getir
    for (let i = 1; i <= months; i++) {
      const prevMonth = subMonths(currentDate, i);
      const prevMonthStart = format(startOfMonth(prevMonth), 'yyyy-MM-dd');
      const prevMonthEnd = format(endOfMonth(prevMonth), 'yyyy-MM-dd');

      // Bu ay için rapor var mı kontrol et
      const report = await db.getFirstAsync(`
        SELECT * FROM budget_reports
        WHERE budget_id = ? AND report_date BETWEEN ? AND ?
        ORDER BY report_date DESC LIMIT 1
      `, [budgetId, prevMonthStart, prevMonthEnd]);

      if (report) {
        comparison.previous_months.push({
          date: format(parseISO(report.report_date), 'yyyy-MM'),
          planned: report.planned_amount,
          actual: report.actual_amount,
          variance: report.variance_amount,
          variance_percent: report.variance_percent
        });
      } else {
        // Rapor yoksa, o ay için harcamaları hesapla
        const expenses = await db.getFirstAsync(`
          SELECT SUM(t.amount) as total
          FROM transactions t
          JOIN budget_categories bc ON t.category_id = bc.category_id
          WHERE bc.budget_id = ?
            AND t.type = 'expense'
            AND t.date BETWEEN ? AND ?
        `, [budgetId, prevMonthStart, prevMonthEnd]);

        const actualAmount = expenses?.total || 0;
        const varianceAmount = budget.amount - actualAmount;
        const variancePercent = budget.amount > 0 ? (varianceAmount / budget.amount) * 100 : 0;

        comparison.previous_months.push({
          date: format(prevMonth, 'yyyy-MM'),
          planned: budget.amount,
          actual: actualAmount,
          variance: varianceAmount,
          variance_percent: variancePercent
        });
      }
    }

    return comparison;
  } catch (error) {
    console.error('Bütçe karşılaştırma raporu oluşturma hatası:', error);
    throw error;
  }
};
