/**
 * Bütçe Durum Kartı Komponenti
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md - Stage 3, Phase 1
 * 
 * Ana bütçe durumu özeti ve genel bilgiler
 * Maksimum 250 satır - Tek sorumluluk prensibi
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../../../context/ThemeContext';
import { formatTRY, formatUSD, formatEUR } from '../../../utils/formatters';

/**
 * Bütçe durum kartı komponenti
 * @param {Object} props - Component props
 * @param {Object} props.budget - Bütçe objesi
 * @param {number} props.currentSpending - Mevcut harcama miktarı
 * @param {number} props.remainingAmount - <PERSON><PERSON> miktar
 * @param {Function} props.onPress - Kart tıklama callback
 * @param {Object} props.theme - Tema objesi (opsiyonel, context'ten alınır)
 */
const BudgetStatusCard = ({ 
  budget, 
  currentSpending = 0, 
  remainingAmount = 0, 
  onPress,
  theme: propTheme 
}) => {
  const { theme, isDarkMode } = useTheme();
  const currentTheme = propTheme || theme;

  /**
   * Para birimi formatı
   * @param {number} value - Formatlanacak değer
   * @param {string} currency - Para birimi
   * @returns {string} Formatlanmış değer
   */
  const formatCurrency = (value, currency) => {
    switch (currency) {
      case 'USD':
        return formatUSD(value);
      case 'EUR':
        return formatEUR(value);
      default:
        return formatTRY(value);
    }
  };

  /**
   * Harcama yüzdesi hesaplama
   * @returns {number} Harcama yüzdesi (0-100)
   */
  const getSpendingPercentage = () => {
    if (!budget?.total_limit || budget.total_limit === 0) return 0;
    return Math.min((currentSpending / budget.total_limit) * 100, 100);
  };

  /**
   * Durum rengi belirleme
   * @returns {string} Durum rengi
   */
  const getStatusColor = () => {
    const percentage = getSpendingPercentage();
    
    if (percentage >= 90) return currentTheme.ERROR;
    if (percentage >= 75) return currentTheme.WARNING;
    if (percentage >= 50) return currentTheme.INFO;
    return currentTheme.SUCCESS;
  };

  /**
   * Durum ikonu belirleme
   * @returns {string} İkon adı
   */
  const getStatusIcon = () => {
    const percentage = getSpendingPercentage();
    
    if (percentage >= 90) return 'warning';
    if (percentage >= 75) return 'info';
    return 'check-circle';
  };

  /**
   * Durum mesajı belirleme
   * @returns {string} Durum mesajı
   */
  const getStatusMessage = () => {
    const percentage = getSpendingPercentage();
    
    if (percentage >= 100) return 'Bütçe aşıldı!';
    if (percentage >= 90) return 'Bütçe sınırına yaklaşıldı';
    if (percentage >= 75) return 'Dikkatli harcama yapın';
    if (percentage >= 50) return 'Bütçe yarısı kullanıldı';
    return 'Bütçe durumu iyi';
  };

  if (!budget) {
    return (
      <View style={[styles.container, styles.emptyContainer, { backgroundColor: currentTheme.SURFACE }]}>
        <MaterialIcons name="account-balance-wallet" size={48} color={currentTheme.TEXT_SECONDARY} />
        <Text style={[styles.emptyTitle, { color: currentTheme.TEXT_PRIMARY }]}>
          Bütçe Bulunamadı
        </Text>
        <Text style={[styles.emptySubtitle, { color: currentTheme.TEXT_SECONDARY }]}>
          Henüz aktif bir bütçe bulunmuyor
        </Text>
      </View>
    );
  }

  const spendingPercentage = getSpendingPercentage();
  const statusColor = getStatusColor();
  const statusIcon = getStatusIcon();
  const statusMessage = getStatusMessage();

  return (
    <TouchableOpacity
      style={[
        styles.container,
        {
          backgroundColor: currentTheme.SURFACE,
          borderColor: currentTheme.BORDER,
        }
      ]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.titleSection}>
          <Text style={[styles.budgetName, { color: currentTheme.TEXT_PRIMARY }]}>
            {budget.name}
          </Text>
          <Text style={[styles.budgetPeriod, { color: currentTheme.TEXT_SECONDARY }]}>
            {budget.period_type === 'monthly' ? 'Aylık' : 
             budget.period_type === 'weekly' ? 'Haftalık' : 'Özel'} Bütçe
          </Text>
        </View>
        
        <View style={[styles.statusIcon, { backgroundColor: statusColor + '20' }]}>
          <MaterialIcons name={statusIcon} size={24} color={statusColor} />
        </View>
      </View>

      {/* Ana bilgiler */}
      <View style={styles.mainInfo}>
        <View style={styles.amountSection}>
          <Text style={[styles.spentLabel, { color: currentTheme.TEXT_SECONDARY }]}>
            Harcanan
          </Text>
          <Text style={[styles.spentAmount, { color: currentTheme.TEXT_PRIMARY }]}>
            {formatCurrency(currentSpending, budget.currency)}
          </Text>
        </View>

        <View style={styles.divider}>
          <Text style={[styles.dividerText, { color: currentTheme.TEXT_SECONDARY }]}>
            /
          </Text>
        </View>

        <View style={styles.amountSection}>
          <Text style={[styles.limitLabel, { color: currentTheme.TEXT_SECONDARY }]}>
            Limit
          </Text>
          <Text style={[styles.limitAmount, { color: currentTheme.TEXT_PRIMARY }]}>
            {formatCurrency(budget.total_limit, budget.currency)}
          </Text>
        </View>
      </View>

      {/* İlerleme çubuğu */}
      <View style={styles.progressSection}>
        <View style={[styles.progressTrack, { backgroundColor: currentTheme.BORDER }]}>
          <View 
            style={[
              styles.progressFill,
              {
                backgroundColor: statusColor,
                width: `${Math.min(spendingPercentage, 100)}%`
              }
            ]} 
          />
        </View>
        <Text style={[styles.progressText, { color: currentTheme.TEXT_SECONDARY }]}>
          %{spendingPercentage.toFixed(1)} kullanıldı
        </Text>
      </View>

      {/* Kalan miktar */}
      <View style={styles.remainingSection}>
        <MaterialIcons name="account-balance" size={16} color={currentTheme.PRIMARY} />
        <Text style={[styles.remainingText, { color: currentTheme.TEXT_SECONDARY }]}>
          Kalan: 
        </Text>
        <Text style={[styles.remainingAmount, { color: remainingAmount >= 0 ? currentTheme.SUCCESS : currentTheme.ERROR }]}>
          {formatCurrency(Math.abs(remainingAmount), budget.currency)}
        </Text>
        {remainingAmount < 0 && (
          <Text style={[styles.overBudgetText, { color: currentTheme.ERROR }]}>
            (aşım)
          </Text>
        )}
      </View>

      {/* Durum mesajı */}
      <View style={[styles.statusMessage, { backgroundColor: statusColor + '10' }]}>
        <Text style={[styles.statusText, { color: statusColor }]}>
          {statusMessage}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    padding: 20,
    marginHorizontal: 16,
    marginVertical: 8,
    borderWidth: 1,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  titleSection: {
    flex: 1,
  },
  budgetName: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  budgetPeriod: {
    fontSize: 14,
  },
  statusIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mainInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  amountSection: {
    flex: 1,
    alignItems: 'center',
  },
  spentLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  spentAmount: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  divider: {
    paddingHorizontal: 16,
  },
  dividerText: {
    fontSize: 24,
    fontWeight: '300',
  },
  limitLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  limitAmount: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  progressSection: {
    marginBottom: 12,
  },
  progressTrack: {
    height: 8,
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 12,
    textAlign: 'center',
  },
  remainingSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
    gap: 4,
  },
  remainingText: {
    fontSize: 14,
  },
  remainingAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  overBudgetText: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  statusMessage: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  statusText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default BudgetStatusCard;
