import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { useTheme } from '../../../context/ThemeContext';
import { useDataIntegration } from '../../../context/DataIntegrationContext';
import { ExportManager } from '../Export';

/**
 * <PERSON><PERSON><PERSON>ılım Şablonu
 * Harcama kategorilerinin dağılım raporu
 */
const CategoryDistributionTemplate = ({ 
  templateConfig, 
  customParams = {}, 
  onExport, 
  onSave 
}) => {
  const { theme } = useTheme();
  const { reportDataService, isLoading: contextLoading } = useDataIntegration();

  const [loading, setLoading] = useState(true);
  const [reportData, setReportData] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadReportData();
  }, [reportDataService]);

  /**
   * <PERSON>or verileri<PERSON> y<PERSON>
   */
  const loadReportData = async () => {
    if (!reportDataService) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const params = {
        dateRange: customParams.dateRange || 'current_month',
        categoryType: customParams.categoryType || 'expense',
        ...customParams
      };
      
      const data = await reportDataService.getCategoryDistributionData(params);
      setReportData(data);
    } catch (error) {
      setError('Kategori dağılım verileri yüklenemedi: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Loading state
  if (loading || contextLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.BACKGROUND }]}>
        <ActivityIndicator size="large" color={theme.PRIMARY} />
        <Text style={[styles.loadingText, { color: theme.TEXT_PRIMARY }]}>
          Kategori dağılım verileri yükleniyor...
        </Text>
      </View>
    );
  }

  // Error state
  if (error) {
    return (
      <View style={[styles.errorContainer, { backgroundColor: theme.BACKGROUND }]}>
        <Text style={[styles.errorText, { color: theme.ERROR }]}>
          {error}
        </Text>
        <TouchableOpacity 
          style={[styles.retryButton, { backgroundColor: theme.PRIMARY }]}
          onPress={loadReportData}
        >
          <Text style={[styles.retryText, { color: theme.SURFACE }]}>
            Tekrar Dene
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Use real data or fallback to empty state
  const categoryData = reportData || { totalAmount: 0, categories: [] };

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      <View style={[styles.header, { backgroundColor: theme.SURFACE || theme.colors?.surface || '#f9f9f9' }]}>
        <Text style={[styles.title, { color: theme.TEXT_PRIMARY || theme.colors?.text || '#000' }]}>
          🏷️ Kategori Dağılım Raporu
        </Text>
        <Text style={[styles.subtitle, { color: theme.TEXT_SECONDARY || theme.colors?.textSecondary || '#666' }]}>
          Harcama kategorilerinin detaylı analizi
        </Text>
      </View>

      {/* Toplam Tutar */}
      <View style={[styles.totalCard, { backgroundColor: theme.PRIMARY || theme.colors?.primary || '#007bff' }]}>
        <Text style={[styles.totalLabel, { color: theme.SURFACE || theme.colors?.surface || '#f9f9f9' }]}>
          Toplam Harcama
        </Text>
        <Text style={[styles.totalAmount, { color: theme.SURFACE || theme.colors?.surface || '#f9f9f9' }]}>
          ₺{categoryData.totalAmount.toLocaleString('tr-TR')}
        </Text>
      </View>

      {/* Kategori Listesi */}
      <View style={[styles.categoriesContainer, { backgroundColor: theme.SURFACE || theme.colors?.surface || '#f9f9f9' }]}>
        <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY || theme.colors?.text || '#000' }]}>
          Kategori Dağılımı
        </Text>
        
        {categoryData.categories.length === 0 ? (
          <View style={styles.emptyState}>
            <Text style={[styles.emptyText, { color: theme.TEXT_SECONDARY }]}>
              Seçilen dönem için kategori verisi bulunamadı.
            </Text>
          </View>
        ) : (
          categoryData.categories.map((category, index) => (
            <View key={index} style={styles.categoryRow}>
              <View style={styles.categoryInfo}>
                <View style={[styles.categoryColor, { backgroundColor: category.color }]} />
                <Text style={[styles.categoryName, { color: theme.TEXT_PRIMARY }]}>
                  {category.name}
                </Text>
              </View>
              
              <View style={styles.categoryStats}>
                <Text style={[styles.categoryAmount, { color: theme.TEXT_PRIMARY }]}>
                  ₺{category.amount.toLocaleString('tr-TR')}
                </Text>
                <Text style={[styles.categoryPercentage, { color: theme.TEXT_SECONDARY }]}>
                  %{category.percentage}
                </Text>
              </View>
            </View>
          ))
        )}
      </View>

      {/* Özet İstatistikler */}
      <View style={[styles.statsContainer, { backgroundColor: theme.SURFACE || theme.colors?.surface || '#f9f9f9' }]}>
        <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY || theme.colors?.text || '#000' }]}>
          📈 İstatistikler
        </Text>
        
        {categoryData.categories.length > 0 && (
          <>
            <View style={styles.statRow}>
              <Text style={[styles.statLabel, { color: theme.TEXT_SECONDARY }]}>
                En Yüksek Harcama:
              </Text>
              <Text style={[styles.statValue, { color: theme.TEXT_PRIMARY }]}>
                {categoryData.categories[0]?.name} (₺{categoryData.categories[0]?.amount.toLocaleString('tr-TR')})
              </Text>
            </View>
            
            <View style={styles.statRow}>
              <Text style={[styles.statLabel, { color: theme.TEXT_SECONDARY }]}>
                Ortalama Harcama:
              </Text>
              <Text style={[styles.statValue, { color: theme.TEXT_PRIMARY }]}>
                ₺{Math.round(categoryData.totalAmount / categoryData.categories.length).toLocaleString('tr-TR')}
              </Text>
            </View>
            
            <View style={styles.statRow}>
              <Text style={[styles.statLabel, { color: theme.TEXT_SECONDARY }]}>
                Toplam Kategori:
              </Text>
              <Text style={[styles.statValue, { color: theme.TEXT_PRIMARY }]}>
                {categoryData.categories.length} adet
              </Text>
            </View>
          </>
        )}
      </View>

      {/* Aksiyon Butonları */}
      <View style={styles.actionButtons}>
        <ExportManager 
          reportData={categoryData}
          reportTitle="Kategori Dağılım Raporu"
          reportType="category_distribution"
          buttonStyle={styles.exportButton}
          buttonTextStyle={styles.exportButtonText}
          theme={theme}
        />
        
        <TouchableOpacity 
          style={[styles.actionButton, { backgroundColor: theme.ACCENT }]}
          onPress={onSave}
        >
          <Text style={[styles.actionButtonText, { color: theme.SURFACE }]}>
            💾 Raporu Kaydet
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    padding: 20,
    marginBottom: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    textAlign: 'center',
  },
  totalCard: {
    padding: 20,
    marginBottom: 16,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  totalAmount: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  categoriesContainer: {
    padding: 16,
    marginBottom: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  emptyState: {
    padding: 20,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
  },
  categoryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 4,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  categoryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryColor: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 12,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '500',
  },
  categoryStats: {
    alignItems: 'flex-end',
  },
  categoryAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  categoryPercentage: {
    fontSize: 14,
  },
  statsContainer: {
    padding: 16,
    marginBottom: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  statLabel: {
    fontSize: 14,
    flex: 1,
  },
  statValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  actionButtons: {
    flexDirection: 'column',
    gap: 12,
    marginBottom: 16,
  },
  exportButton: {
    marginBottom: 8,
  },
  exportButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  actionButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  // Loading and error states
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryText: {
    fontSize: 14,
    fontWeight: '600',
  },
});

export default CategoryDistributionTemplate;
