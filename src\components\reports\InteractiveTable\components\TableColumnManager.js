import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  TextInput,
  Modal,
  Alert,
} from 'react-native';
import TableFormulaBuilder from './TableFormulaBuilder';

/**
 * Table Column Manager Component
 * Manages table columns with add, edit, remove, and reorder functionality
 * Supports formula columns and various data types
 */
const TableColumnManager = ({ 
  visible,
  onClose,
  columns = [],
  onAddColumn,
  onUpdateColumn,
  onRemoveColumn,
  onReorderColumns,
  availableDataSources = [],
  theme 
}) => {
  const [editingColumn, setEditingColumn] = useState(null);
  const [showFormulaBuilder, setShowFormulaBuilder] = useState(false);
  const [newColumn, setNewColumn] = useState({
    name: '',
    type: 'text',
    source: '',
    formula: '',
    width: 120,
    sortable: true,
    filterable: true,
    visible: true,
  });

  // Column types
  const columnTypes = [
    { value: 'text', label: 'Metin', icon: '📝' },
    { value: 'number', label: 'Sayı', icon: '🔢' },
    { value: 'currency', label: 'Para', icon: '💰' },
    { value: 'date', label: 'Tarih', icon: '📅' },
    { value: 'boolean', label: 'Evet/Hayır', icon: '✅' },
    { value: 'formula', label: 'Formül', icon: '🧮' },
  ];

  const handleAddColumn = () => {
    if (!newColumn.name.trim()) {
      Alert.alert('Hata', 'Sütun adı gereklidir.');
      return;
    }

    const column = {
      ...newColumn,
      id: Date.now().toString(),
    };

    onAddColumn(column);
    setNewColumn({
      name: '',
      type: 'text',
      source: '',
      formula: '',
      width: 120,
      sortable: true,
      filterable: true,
      visible: true,
    });
  };

  const handleEditColumn = (column) => {
    setEditingColumn(column);
    setNewColumn({ ...column });
  };

  const handleUpdateColumn = () => {
    if (!newColumn.name.trim()) {
      Alert.alert('Hata', 'Sütun adı gereklidir.');
      return;
    }

    onUpdateColumn(editingColumn.id, newColumn);
    setEditingColumn(null);
    setNewColumn({
      name: '',
      type: 'text',
      source: '',
      formula: '',
      width: 120,
      sortable: true,
      filterable: true,
      visible: true,
    });
  };

  const handleRemoveColumn = (columnId) => {
    Alert.alert(
      'Sütunu Sil',
      'Bu sütunu silmek istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        { 
          text: 'Sil', 
          style: 'destructive',
          onPress: () => onRemoveColumn(columnId)
        },
      ]
    );
  };

  const handleFormulaChange = (formula) => {
    setNewColumn(prev => ({ ...prev, formula }));
  };

  const renderColumnForm = () => (
    <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
      <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
        {editingColumn ? 'Sütunu Düzenle' : 'Yeni Sütun Ekle'}
      </Text>

      {/* Column Name */}
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
          Sütun Adı *
        </Text>
        <TextInput
          style={[
            styles.input,
            { 
              backgroundColor: theme.BACKGROUND,
              color: theme.TEXT_PRIMARY,
              borderColor: theme.BORDER
            }
          ]}
          value={newColumn.name}
          onChangeText={(text) => setNewColumn(prev => ({ ...prev, name: text }))}
          placeholder="Sütun adını girin..."
          placeholderTextColor={theme.TEXT_SECONDARY}
        />
      </View>

      {/* Column Type */}
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
          Sütun Tipi
        </Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {columnTypes.map((type) => (
            <TouchableOpacity
              key={type.value}
              style={[
                styles.typeChip,
                {
                  backgroundColor: newColumn.type === type.value 
                    ? theme.PRIMARY 
                    : theme.BACKGROUND,
                  borderColor: theme.BORDER
                }
              ]}
              onPress={() => setNewColumn(prev => ({ ...prev, type: type.value }))}
            >
              <Text style={styles.typeIcon}>{type.icon}</Text>
              <Text style={[
                styles.typeLabel,
                { 
                  color: newColumn.type === type.value 
                    ? theme.SURFACE 
                    : theme.TEXT_PRIMARY 
                }
              ]}>
                {type.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Formula Input for Formula Type */}
      {newColumn.type === 'formula' && (
        <View style={styles.formGroup}>
          <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
            Formül
          </Text>
          <TouchableOpacity
            style={[
              styles.formulaButton,
              { 
                backgroundColor: theme.BACKGROUND,
                borderColor: theme.BORDER
              }
            ]}
            onPress={() => setShowFormulaBuilder(true)}
          >
            <Text style={[
              styles.formulaText,
              { color: newColumn.formula ? theme.TEXT_PRIMARY : theme.TEXT_SECONDARY }
            ]}>
              {newColumn.formula || 'Formül eklemek için tıklayın...'}
            </Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Data Source for Non-Formula Types */}
      {newColumn.type !== 'formula' && (
        <View style={styles.formGroup}>
          <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
            Veri Kaynağı
          </Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {availableDataSources.map((source, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.sourceChip,
                  {
                    backgroundColor: newColumn.source === source.field 
                      ? theme.INFO 
                      : theme.BACKGROUND,
                    borderColor: theme.BORDER
                  }
                ]}
                onPress={() => setNewColumn(prev => ({ ...prev, source: source.field }))}
              >
                <Text style={[
                  styles.sourceLabel,
                  { 
                    color: newColumn.source === source.field 
                      ? theme.SURFACE 
                      : theme.TEXT_PRIMARY 
                  }
                ]}>
                  {source.label}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}

      {/* Column Width */}
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
          Genişlik (px)
        </Text>
        <TextInput
          style={[
            styles.input,
            { 
              backgroundColor: theme.BACKGROUND,
              color: theme.TEXT_PRIMARY,
              borderColor: theme.BORDER
            }
          ]}
          value={newColumn.width.toString()}
          onChangeText={(text) => setNewColumn(prev => ({ 
            ...prev, 
            width: parseInt(text) || 120 
          }))}
          placeholder="120"
          placeholderTextColor={theme.TEXT_SECONDARY}
          keyboardType="numeric"
        />
      </View>

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <TouchableOpacity
          style={[styles.cancelButton, { backgroundColor: theme.ERROR }]}
          onPress={() => {
            setEditingColumn(null);
            setNewColumn({
              name: '',
              type: 'text',
              source: '',
              formula: '',
              width: 120,
              sortable: true,
              filterable: true,
              visible: true,
            });
          }}
        >
          <Text style={[styles.buttonText, { color: theme.SURFACE }]}>
            İptal
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.saveButton, { backgroundColor: theme.SUCCESS }]}
          onPress={editingColumn ? handleUpdateColumn : handleAddColumn}
        >
          <Text style={[styles.buttonText, { color: theme.SURFACE }]}>
            {editingColumn ? 'Güncelle' : 'Ekle'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
        {/* Header */}
        <View style={[styles.header, { backgroundColor: theme.SURFACE }]}>
          <TouchableOpacity onPress={onClose}>
            <Text style={[styles.closeButton, { color: theme.ERROR }]}>
              Kapat
            </Text>
          </TouchableOpacity>
          
          <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
            Sütun Yöneticisi
          </Text>
          
          <View style={{ width: 50 }} />
        </View>

        <ScrollView style={styles.content}>
          {/* Column Form */}
          {renderColumnForm()}

          {/* Existing Columns */}
          <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
            <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
              Mevcut Sütunlar ({columns.length})
            </Text>

            {columns.map((column, index) => (
              <View 
                key={column.id} 
                style={[styles.columnItem, { backgroundColor: theme.BACKGROUND }]}
              >
                <View style={styles.columnInfo}>
                  <Text style={[styles.columnName, { color: theme.TEXT_PRIMARY }]}>
                    {column.name}
                  </Text>
                  <Text style={[styles.columnType, { color: theme.TEXT_SECONDARY }]}>
                    {columnTypes.find(t => t.value === column.type)?.label || column.type}
                    {column.type === 'formula' && ' • Formül'}
                  </Text>
                </View>

                <View style={styles.columnActions}>
                  <TouchableOpacity
                    style={[styles.actionButton, { backgroundColor: theme.INFO }]}
                    onPress={() => handleEditColumn(column)}
                  >
                    <Text style={[styles.actionButtonText, { color: theme.SURFACE }]}>
                      Düzenle
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[styles.actionButton, { backgroundColor: theme.ERROR }]}
                    onPress={() => handleRemoveColumn(column.id)}
                  >
                    <Text style={[styles.actionButtonText, { color: theme.SURFACE }]}>
                      Sil
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            ))}

            {columns.length === 0 && (
              <Text style={[styles.emptyText, { color: theme.TEXT_SECONDARY }]}>
                Henüz sütun eklenmemiş. Yukarıdaki formu kullanarak sütun ekleyebilirsiniz.
              </Text>
            )}
          </View>
        </ScrollView>

        {/* Formula Builder Modal */}
        <TableFormulaBuilder
          visible={showFormulaBuilder}
          onClose={() => setShowFormulaBuilder(false)}
          onSave={handleFormulaChange}
          initialFormula={newColumn.formula}
          availableColumns={columns}
          theme={theme}
        />
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  closeButton: {
    fontSize: 16,
    fontWeight: '600',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 16,
    padding: 16,
    borderRadius: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  typeChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 8,
  },
  typeIcon: {
    fontSize: 16,
    marginRight: 6,
  },
  typeLabel: {
    fontSize: 14,
    fontWeight: '600',
  },
  sourceChip: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    borderWidth: 1,
    marginRight: 8,
  },
  sourceLabel: {
    fontSize: 14,
    fontWeight: '600',
  },
  formulaButton: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    minHeight: 50,
    justifyContent: 'center',
  },
  formulaText: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    marginRight: 8,
  },
  saveButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    marginLeft: 8,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  columnItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  columnInfo: {
    flex: 1,
  },
  columnName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  columnType: {
    fontSize: 12,
  },
  columnActions: {
    flexDirection: 'row',
  },
  actionButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    marginLeft: 8,
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: '600',
  },
  emptyText: {
    textAlign: 'center',
    fontStyle: 'italic',
    padding: 20,
  },
});

export default TableColumnManager;
