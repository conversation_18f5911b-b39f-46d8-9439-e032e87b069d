/**
 * <PERSON><PERSON><PERSON> hedefleri tablosunu doğrudan oluşturan fonksiyon
 * 
 * @param {object} db - SQLite veritabanı nesnesi
 * @returns {Promise<void>}
 */
export const createSavingsGoalsTable = async (db) => {
  try {
    console.log('<PERSON><PERSON><PERSON> hedefleri tablosu doğrudan oluşturuluyor...');
    
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS savings_goals (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        target_amount DECIMAL(10,2) NOT NULL,
        current_amount DECIMAL(10,2) DEFAULT 0,
        target_date DATE NOT NULL,
        currency TEXT DEFAULT 'TRY',
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    console.log('<PERSON><PERSON><PERSON> hedefleri tablosu başar<PERSON>yla oluşturuldu.');
  } catch (error) {
    console.error('<PERSON><PERSON><PERSON> hedefleri tablosu oluşturma hatası:', error);
    throw error;
  }
};
