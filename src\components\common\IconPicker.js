import React from 'react';
import { View, ScrollView, TouchableOpacity, StyleSheet } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';

// Yaygın olarak kullanılan ikonların listesi
const COMMON_ICONS = [
  'category', 'shopping_cart', 'restaurant', 'local_gas_station',
  'home', 'work', 'commute', 'flight', 'school', 'movie',
  'fitness_center', 'pets', 'local_grocery_store', 'medical_services',
  'shopping_bag', 'fastfood', 'local_cafe', 'sports_bar',
  'local_mall', 'local_pharmacy', 'sports_esports', 'local_shipping'
];

/**
 * İkon seçici bileşeni
 * @param {Object} props Bileşen özellikleri
 * @param {string} props.selectedIcon Seçili ikon
 * @param {Function} props.onSelect İkon seçildiğinde çağrılacak fonksiyon
 * @returns {JSX.Element} IconPicker komponenti
 */
const IconPicker = ({ selectedIcon, onSelect }) => {
  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={styles.container}
    >
      {COMMON_ICONS.map(icon => (
        <TouchableOpacity
          key={icon}
          style={[
            styles.iconItem,
            selectedIcon === icon && styles.selectedIconItem
          ]}
          onPress={() => onSelect(icon)}
        >
          <MaterialIcons
            name={icon}
            size={28}
            color={selectedIcon === icon ? '#3498db' : '#6c757d'}
          />
        </TouchableOpacity>
      ))}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingVertical: 8
  },
  iconItem: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
    backgroundColor: '#f8f9fa'
  },
  selectedIconItem: {
    backgroundColor: '#e3f2fd',
    borderWidth: 2,
    borderColor: '#3498db'
  }
});

export default IconPicker;
