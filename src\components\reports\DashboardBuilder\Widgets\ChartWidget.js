import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

/**
 * Chart Widget - Grafik gösterimi için widget
 */
const ChartWidget = ({ widget, isPreviewMode, onUpdate, theme }) => {
  /**
   * G<PERSON>venli tema değeri alma
   * @param {string} property - <PERSON><PERSON>
   * @param {string} fallback - <PERSON><PERSON><PERSON><PERSON><PERSON>
   * @returns {string} <PERSON><PERSON><PERSON>li tema değeri
   */
  const getSafeThemeValue = (property, fallback) => {
    return theme[property] || theme.colors?.[property.toLowerCase()] || fallback;
  };

  return (
    <View style={[styles.container, { backgroundColor: getSafeThemeValue('BACKGROUND', '#fff') }]}>
      <View style={[styles.chartArea, { borderColor: getSafeThemeValue('BORDER', '#e0e0e0') }]}>
        <Text style={[styles.chartIcon, { color: getSafeThemeValue('PRIMARY', '#007AFF') }]}>
          📊
        </Text>
        <Text style={[styles.chartLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666') }]}>
          {widget.config?.chartType || 'Line'} Chart
        </Text>
        <Text style={[styles.chartSubLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666') }]}>
          Data Source: {widget.config?.dataSource || 'transactions'}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 4,
  },
  chartArea: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderStyle: 'dashed',
    borderRadius: 4,
  },
  chartIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  chartLabel: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 4,
  },
  chartSubLabel: {
    fontSize: 10,
  },
});

export default ChartWidget;
