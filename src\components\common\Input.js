import React from 'react';
import { TextInput, StyleSheet } from 'react-native';

const Input = ({ error, ...props }) => (
  <TextInput
    style={[styles.input, error && styles.inputError]}
    placeholderTextColor="#95a5a6"
    {...props}
  />
);

const styles = StyleSheet.create({
  input: {
    height: 48,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 16,
    color: '#2c3e50',
    backgroundColor: '#fff'
  },
  inputError: {
    borderColor: '#e74c3c'
  }
});

export default Input;
