/**
 * Bildir<PERSON> Zamanlayıcı Servisi
 * Bildirimleri periyodik olarak kontrol eder ve zamanlar
 */
import { AppState } from 'react-native';
import * as notificationDbService from './notificationDbService';
import * as notificationTriggerService from './notificationTriggerService';

let appStateSubscription = null;
let dailyCheckInterval = null;
let isInitialized = false;

/**
 * Bildirim zamanlayıcıyı başlatır
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 */
export const initNotificationScheduler = (db) => {
  if (isInitialized || !db) {
    console.log('Bildirim zamanlayıcı zaten başlatılmış veya veritabanı bağlantısı yok');
    return;
  }

  try {
    // Uygulama durumu değiştiğinde bildirimleri kontrol et
    appStateSubscription = AppState.addEventListener('change', (nextAppState) => {
      if (nextAppState === 'active') {
        // Uygulama ön plana geldiğinde bildirimleri kontrol et
        checkNotifications(db);
      }
    });

    // Günlük kontrol için zamanlayıcı başlat
    dailyCheckInterval = setInterval(() => {
      checkNotifications(db);
    }, 3600000); // Her saat kontrol et (3600000 ms = 1 saat)

    // İlk kontrol
    setTimeout(() => {
      checkNotifications(db);
    }, 5000); // 5 saniye sonra ilk kontrolü yap

    isInitialized = true;

    console.log('Bildirim zamanlayıcı başlatıldı');
  } catch (error) {
    console.error('Bildirim zamanlayıcı başlatma hatası:', error);
  }
};

/**
 * Bildirim zamanlayıcıyı durdurur
 */
export const stopNotificationScheduler = () => {
  if (appStateSubscription) {
    appStateSubscription.remove();
    appStateSubscription = null;
  }

  if (dailyCheckInterval) {
    clearInterval(dailyCheckInterval);
    dailyCheckInterval = null;
  }

  isInitialized = false;

  console.log('Bildirim zamanlayıcı durduruldu');
};

/**
 * Bildirimleri kontrol eder
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 */
export const checkNotifications = async (db) => {
  try {
    console.log('Bildirimler kontrol ediliyor...');

    // Bekleyen bildirimleri kontrol et
    await notificationDbService.checkPendingNotifications(db);

    // Günlük özet bildirimini oluştur
    await notificationTriggerService.createDailySummaryNotification(db);

    // Haftalık özet bildirimini oluştur
    await notificationTriggerService.createWeeklySummaryNotification(db);

    // Aylık özet bildirimini oluştur
    await notificationTriggerService.createMonthlySummaryNotification(db);

    // Bütçeleri kontrol et
    await checkBudgets(db);

    // Birikim hedeflerini kontrol et
    await checkSavingsGoals(db);

    // Maaşları kontrol et
    await checkSalaries(db);

    // Mesaileri kontrol et
    await checkOvertime(db);

    console.log('Bildirim kontrolü tamamlandı');
  } catch (error) {
    console.error('Bildirim kontrolü hatası:', error);
  }
};

/**
 * Bütçeleri kontrol eder
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 */
const checkBudgets = async (db) => {
  try {
    // Aktif bütçeleri getir
    const budgets = await db.getAllAsync(`
      SELECT * FROM budgets
      WHERE is_active = 1
    `);

    // Her bütçe için harcamaları kontrol et
    for (const budget of budgets) {
      // Bütçe harcamalarını getir
      const result = await db.getFirstAsync(`
        SELECT SUM(amount) as total_spent
        FROM transactions
        WHERE type = 'expense'
        AND date >= ? AND date <= ?
        AND category_id IN (
          SELECT category_id FROM budget_categories
          WHERE budget_id = ?
        )
      `, [budget.start_date, budget.end_date, budget.id]);

      const totalSpent = result?.total_spent || 0;
      const spentPercentage = (totalSpent / budget.amount) * 100;

      // Bütçe uyarı bildirimi oluştur
      await notificationTriggerService.createBudgetAlertNotification(db, budget, spentPercentage);
    }
  } catch (error) {
    console.error('Bütçe kontrolü hatası:', error);
  }
};

/**
 * Birikim hedeflerini kontrol eder
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 */
const checkSavingsGoals = async (db) => {
  try {
    // Aktif birikim hedeflerini getir
    const savingsGoals = await db.getAllAsync(`
      SELECT * FROM savings_goals
      WHERE is_completed = 0 OR is_completed IS NULL
    `);

    // Her hedef için ilerlemeyi kontrol et
    for (const goal of savingsGoals) {
      // Hedef için birikimleri getir
      const result = await db.getFirstAsync(`
        SELECT SUM(amount) as total_saved
        FROM savings_transactions
        WHERE goal_id = ?
      `, [goal.id]);

      const totalSaved = result?.total_saved || 0;
      const progressPercentage = (totalSaved / goal.target_amount) * 100;

      // Birikim hedefi uyarı bildirimi oluştur
      await notificationTriggerService.createSavingsGoalAlertNotification(db, goal, progressPercentage);
    }
  } catch (error) {
    console.error('Birikim hedefi kontrolü hatası:', error);
  }
};

/**
 * Maaşları kontrol eder
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 */
const checkSalaries = async (db) => {
  try {
    // Aktif maaşları getir
    const salaries = await db.getAllAsync(`
      SELECT * FROM salaries
      WHERE is_active = 1
    `);

    // Bugünün tarihi
    const today = new Date();
    const currentDay = today.getDate();

    // Her maaş için ödeme gününü kontrol et
    for (const salary of salaries) {
      const paymentDay = parseInt(salary.payment_day);

      // Maaş gününe kalan gün sayısını hesapla
      let daysUntilPayday = paymentDay - currentDay;

      // Eğer maaş günü geçtiyse, bir sonraki ayın maaş gününe kalan günü hesapla
      if (daysUntilPayday < 0) {
        // Bir sonraki ayın son günü
        const lastDayOfNextMonth = new Date(today.getFullYear(), today.getMonth() + 2, 0).getDate();

        // Eğer maaş günü bir sonraki ayın son gününden büyükse, ayın son günü olarak ayarla
        const nextMonthPaymentDay = Math.min(paymentDay, lastDayOfNextMonth);

        // Bir sonraki ayın maaş gününe kalan gün sayısı
        const daysInCurrentMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0).getDate();
        daysUntilPayday = daysInCurrentMonth - currentDay + nextMonthPaymentDay;
      }

      // Maaş hatırlatma bildirimi oluştur
      await notificationTriggerService.createSalaryReminderNotification(db, salary, daysUntilPayday);
    }
  } catch (error) {
    console.error('Maaş kontrolü hatası:', error);
  }
};

/**
 * Mesaileri kontrol eder
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 */
const checkOvertime = async (db) => {
  try {
    // Ödenmemiş mesaileri getir
    const overtimes = await db.getAllAsync(`
      SELECT * FROM overtime
      WHERE is_paid = 0
    `);

    // Her mesai için ödeme hatırlatması oluştur
    for (const overtime of overtimes) {
      await notificationTriggerService.createOvertimePaymentReminderNotification(db, overtime);
    }
  } catch (error) {
    console.error('Mesai kontrolü hatası:', error);
  }
};
