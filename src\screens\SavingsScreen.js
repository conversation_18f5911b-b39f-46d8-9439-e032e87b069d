import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  RefreshControl,
  ActivityIndicator,
  Alert,
  FlatList
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import { useAppContext } from '../context/AppContext';
import { formatCurrency } from '../utils/formatters';
import { createSavingsGoalsTable } from '../db/createSavingsGoalsTable';

/**
 * <PERSON><PERSON><PERSON>
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @returns {JSX.Element} <PERSON><PERSON><PERSON> e<PERSON>
 */
export default function SavingsScreen({ navigation }) {
  const db = useSQLiteContext();
  const insets = useSafeAreaInsets();
  const { theme } = useAppContext();

  // Durum değişkenleri
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [savingsGoals, setSavingsGoals] = useState([]);
  const [totalSaved, setTotalSaved] = useState(0);
  const [totalTarget, setTotalTarget] = useState(0);

  // Verileri yükle
  const loadData = useCallback(async () => {
    try {
      setLoading(true);

      // Önce tabloyu oluştur (eğer yoksa)
      await createSavingsGoalsTable(db);

      // Birikim hedeflerini getir
      const goals = await db.getAllAsync(`
        SELECT * FROM savings_goals
        ORDER BY target_date ASC
      `);

      setSavingsGoals(goals);

      // Toplam birikim ve hedef tutarlarını hesapla
      let savedTotal = 0;
      let targetTotal = 0;

      goals.forEach(goal => {
        savedTotal += goal.current_amount;
        targetTotal += goal.target_amount;
      });

      setTotalSaved(savedTotal);
      setTotalTarget(targetTotal);

      setLoading(false);
    } catch (error) {
      console.error('Birikim hedefleri yükleme hatası:', error);
      setLoading(false);
    }
  }, [db]);

  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [loadData])
  );

  // Yenile
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  }, [loadData]);

  // Yeni birikim hedefi ekle
  const addSavingsGoal = () => {
    navigation.navigate('SavingsGoalForm');
  };

  // Birikim hedefi detaylarını görüntüle
  const viewSavingsGoalDetails = (goalId) => {
    navigation.navigate('SavingsGoalDetail', { goalId });
  };

  // Birikim hedefi sil
  const deleteSavingsGoal = (goalId) => {
    Alert.alert(
      'Birikim Hedefi Sil',
      'Bu birikim hedefini silmek istediğinize emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await db.runAsync('DELETE FROM savings_goals WHERE id = ?', [goalId]);
              await loadData();
              Alert.alert('Başarılı', 'Birikim hedefi başarıyla silindi.');
            } catch (error) {
              console.error('Birikim hedefi silme hatası:', error);
              Alert.alert('Hata', 'Birikim hedefi silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };

  // İlerleme yüzdesini hesapla
  const calculateProgress = (current, target) => {
    if (target <= 0) return 0;
    const progress = (current / target) * 100;
    return Math.min(progress, 100);
  };

  // Tarih formatla
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR', { day: 'numeric', month: 'long', year: 'numeric' });
  };

  // Kalan gün sayısını hesapla
  const calculateRemainingDays = (targetDate) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const target = new Date(targetDate);
    target.setHours(0, 0, 0, 0);

    const diffTime = target - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return diffDays;
  };

  // Birikim hedefi öğesi
  const renderSavingsGoalItem = ({ item }) => {
    const progress = calculateProgress(item.current_amount, item.target_amount);
    const remainingDays = calculateRemainingDays(item.target_date);

    return (
      <TouchableOpacity
        style={[styles.goalItem, { backgroundColor: theme.SURFACE }]}
        onPress={() => viewSavingsGoalDetails(item.id)}
      >
        <View style={styles.goalHeader}>
          <Text style={[styles.goalName, { color: theme.TEXT_PRIMARY }]}>{item.name}</Text>
          <TouchableOpacity
            style={styles.deleteButton}
            onPress={() => deleteSavingsGoal(item.id)}
          >
            <MaterialIcons name="delete" size={20} color={Colors.DANGER} />
          </TouchableOpacity>
        </View>

        <View style={styles.goalAmounts}>
          <Text style={[styles.currentAmount, { color: Colors.PRIMARY }]}>
            {formatCurrency(item.current_amount, item.currency)}
          </Text>
          <Text style={[styles.targetAmount, { color: theme.TEXT_SECONDARY }]}>
            / {formatCurrency(item.target_amount, item.currency)}
          </Text>
        </View>

        <View style={[styles.progressBarContainer, { backgroundColor: theme.BORDER }]}>
          <View style={[styles.progressBar, { width: `${progress}%` }]} />
        </View>

        <View style={styles.goalFooter}>
          <View style={styles.goalDateContainer}>
            <MaterialIcons name="event" size={16} color={theme.TEXT_SECONDARY} />
            <Text style={[styles.goalDate, { color: theme.TEXT_SECONDARY }]}>{formatDate(item.target_date)}</Text>
          </View>

          <View style={[
            styles.remainingDaysContainer,
            remainingDays < 0 ? styles.overdueDays :
            remainingDays < 7 ? styles.urgentDays :
            remainingDays < 30 ? styles.warningDays :
            styles.normalDays
          ]}>
            <Text style={[styles.remainingDaysText, { color: theme.TEXT_PRIMARY }]}>
              {remainingDays < 0
                ? `${Math.abs(remainingDays)} gün gecikti`
                : remainingDays === 0
                  ? 'Bugün son gün!'
                  : `${remainingDays} gün kaldı`}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  if (loading && !refreshing) {
    return (
      <View style={[styles.container, styles.centerContent, { paddingTop: insets.top }]}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={styles.loadingText}>Birikim hedefleri yükleniyor...</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { paddingTop: insets.top, backgroundColor: theme.BACKGROUND }]}>
      <View style={[styles.header, { backgroundColor: theme.PRIMARY }]}>
        <Text style={[styles.headerTitle, { color: theme.WHITE }]}>Birikim Hedefleri</Text>
      </View>

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[Colors.PRIMARY]}
            tintColor={Colors.PRIMARY}
          />
        }
      >
        {/* Özet Kart */}
        <View style={[styles.summaryCard, { backgroundColor: theme.SURFACE }]}>
          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>Toplam Birikim</Text>
            <Text style={[styles.summaryValue, { color: theme.TEXT_PRIMARY }]}>
              {formatCurrency(totalSaved, 'TRY')}
            </Text>
          </View>

          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>Toplam Hedef</Text>
            <Text style={[styles.summaryValue, { color: theme.TEXT_PRIMARY }]}>
              {formatCurrency(totalTarget, 'TRY')}
            </Text>
          </View>

          <View style={[styles.progressBarContainer, { backgroundColor: theme.BORDER }]}>
            <View
              style={[
                styles.progressBar,
                { width: `${calculateProgress(totalSaved, totalTarget)}%` }
              ]}
            />
          </View>

          <Text style={[styles.progressText, { color: theme.TEXT_SECONDARY }]}>
            {`%${Math.round(calculateProgress(totalSaved, totalTarget))}`}
          </Text>
        </View>

        {/* Birikim Hedefleri Listesi */}
        <View style={styles.goalsContainer}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>Hedeflerim</Text>
            <TouchableOpacity
              onPress={addSavingsGoal}
            >
              <MaterialIcons name="add" size={24} color={Colors.PRIMARY} />
            </TouchableOpacity>
          </View>

          {savingsGoals.length === 0 ? (
            <View style={[styles.emptyState, { backgroundColor: theme.SURFACE }]}>
              <MaterialIcons name="savings" size={48} color={theme.TEXT_SECONDARY} />
              <Text style={[styles.emptyStateText, { color: theme.TEXT_SECONDARY }]}>Henüz birikim hedefi bulunmuyor.</Text>
              <TouchableOpacity
                style={[styles.emptyStateButton, { backgroundColor: Colors.PRIMARY }]}
                onPress={addSavingsGoal}
              >
                <Text style={[styles.emptyStateButtonText, { color: theme.WHITE }]}>Hedef Ekle</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <FlatList
              data={savingsGoals}
              renderItem={renderSavingsGoalItem}
              keyExtractor={(item) => item.id.toString()}
              scrollEnabled={false}
            />
          )}
        </View>
      </ScrollView>

      {/* Yeni Hedef Butonu */}
      <TouchableOpacity
        style={styles.fab}
        onPress={addSavingsGoal}
      >
        <MaterialIcons name="add" size={24} color="#fff" />
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.PRIMARY,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  scrollView: {
    flex: 1,
  },
  summaryCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    margin: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 16,
    color: '#666',
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  progressBarContainer: {
    height: 8,
    backgroundColor: '#f0f0f0',
    borderRadius: 4,
    marginVertical: 12,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: Colors.PRIMARY,
  },
  progressText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  goalsContainer: {
    padding: 16,
    paddingTop: 0,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  goalItem: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  goalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  goalName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  deleteButton: {
    padding: 4,
  },
  goalAmounts: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 8,
  },
  currentAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.PRIMARY,
  },
  targetAmount: {
    fontSize: 14,
    color: '#666',
    marginLeft: 4,
  },
  goalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  goalDateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  goalDate: {
    fontSize: 14,
    color: '#666',
    marginLeft: 4,
  },
  remainingDaysContainer: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  normalDays: {
    backgroundColor: '#e8f5e9',
  },
  warningDays: {
    backgroundColor: '#fff3e0',
  },
  urgentDays: {
    backgroundColor: '#ffebee',
  },
  overdueDays: {
    backgroundColor: '#ffcdd2',
  },
  remainingDaysText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#333',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
    backgroundColor: '#fff',
    borderRadius: 12,
  },
  emptyStateText: {
    marginTop: 16,
    fontSize: 16,
    color: '#999',
    textAlign: 'center',
  },
  emptyStateButton: {
    marginTop: 16,
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: Colors.PRIMARY,
    borderRadius: 8,
  },
  emptyStateButtonText: {
    color: '#fff',
    fontWeight: '500',
  },
  fab: {
    position: 'absolute',
    right: 16,
    bottom: 16,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
});
