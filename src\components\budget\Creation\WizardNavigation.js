/**
 * Wizard Navigation Komponenti
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md - Stage 2, Phase 4
 * 
 * Bütçe oluşturma wizard'ının navigation'ı
 * Maksimum 150 satır - <PERSON><PERSON> so<PERSON> prensibi
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../../../context/ThemeContext';

/**
 * Wizard navigation komponenti
 * @param {Object} props - Component props
 * @param {number} props.currentStep - Mevcut adım (0-based)
 * @param {number} props.totalSteps - Toplam adım sayısı
 * @param {Function} props.onPrevious - Önceki adım callback fonksiyonu
 * @param {Function} props.onNext - Sonraki adım callback fonksiyonu
 * @param {Function} props.onSave - Kaydet<PERSON> callback fonksiyonu
 * @param {boolean} props.isStepValid - Mevcut adım geçerli mi
 * @param {boolean} props.loading - Yükleme durumu
 * @param {Object} props.theme - Tema objesi (opsiyonel, context'ten alınır)
 */
const WizardNavigation = ({ 
  currentStep, 
  totalSteps, 
  onPrevious, 
  onNext, 
  onSave,
  isStepValid = true,
  loading = false,
  theme: propTheme 
}) => {
  const { theme } = useTheme();
  const currentTheme = propTheme || theme;

  // İlk adım mı
  const isFirstStep = currentStep === 0;
  
  // Son adım mı
  const isLastStep = currentStep === totalSteps - 1;

  /**
   * Önceki buton işleyicisi
   */
  const handlePrevious = () => {
    if (!isFirstStep && !loading && onPrevious) {
      onPrevious();
    }
  };

  /**
   * Sonraki/Kaydet buton işleyicisi
   */
  const handleNext = () => {
    if (!loading && isStepValid) {
      if (isLastStep && onSave) {
        onSave();
      } else if (!isLastStep && onNext) {
        onNext();
      }
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: currentTheme.SURFACE }]}>
      {/* Önceki buton */}
      <TouchableOpacity
        style={[
          styles.button,
          styles.previousButton,
          {
            backgroundColor: currentTheme.BACKGROUND,
            borderColor: currentTheme.BORDER,
            opacity: isFirstStep || loading ? 0.5 : 1,
          }
        ]}
        onPress={handlePrevious}
        disabled={isFirstStep || loading}
      >
        <MaterialIcons 
          name="chevron-left" 
          size={20} 
          color={currentTheme.TEXT_PRIMARY} 
        />
        <Text style={[styles.buttonText, { color: currentTheme.TEXT_PRIMARY }]}>
          Önceki
        </Text>
      </TouchableOpacity>

      {/* Adım göstergesi */}
      <View style={styles.stepInfo}>
        <Text style={[styles.stepInfoText, { color: currentTheme.TEXT_SECONDARY }]}>
          {currentStep + 1} / {totalSteps}
        </Text>
      </View>

      {/* Sonraki/Kaydet buton */}
      <TouchableOpacity
        style={[
          styles.button,
          styles.nextButton,
          {
            backgroundColor: isStepValid && !loading 
              ? currentTheme.PRIMARY 
              : currentTheme.TEXT_SECONDARY,
          }
        ]}
        onPress={handleNext}
        disabled={!isStepValid || loading}
      >
        {loading ? (
          <ActivityIndicator size="small" color={currentTheme.WHITE} />
        ) : (
          <>
            <Text style={[styles.buttonText, { color: currentTheme.WHITE }]}>
              {isLastStep ? 'Kaydet' : 'Sonraki'}
            </Text>
            <MaterialIcons 
              name={isLastStep ? 'check' : 'chevron-right'} 
              size={20} 
              color={currentTheme.WHITE} 
            />
          </>
        )}
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
    minWidth: 100,
  },
  previousButton: {
    borderWidth: 1,
  },
  nextButton: {
    // backgroundColor set via style prop
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  stepInfo: {
    flex: 1,
    alignItems: 'center',
  },
  stepInfoText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default WizardNavigation;
