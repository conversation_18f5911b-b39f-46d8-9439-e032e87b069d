import React from 'react';
import { SQLiteProvider } from 'expo-sqlite';
import { View } from 'react-native';

/**
 * Veritabanı sağlayıcı bileşeni
 */
const DatabaseProvider = ({ children }) => {
  return (
    <SQLiteProvider 
      databaseName="financeApp.db"
      onInit={async (db) => {
        try {
          // SQLite ayarlarını yap
          await db.execAsync('PRAGMA foreign_keys = ON');
          await db.execAsync('PRAGMA journal_mode = WAL');

          // Temel tabloları oluştur
          await db.execAsync(`
            CREATE TABLE IF NOT EXISTS settings (
              key TEXT PRIMARY KEY,
              value TEXT NOT NULL,
              created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );

            CREATE TABLE IF NOT EXISTS salaries (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              amount DECIMAL(10,2) NOT NULL,
              payment_date DATE NOT NULL,
              description TEXT,
              frequency TEXT CHECK(frequency IN ('monthly', 'yearly', 'one-time')) DEFAULT 'monthly',
              currency TEXT DEFAULT 'TRY',
              created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );

            CREATE TABLE IF NOT EXISTS exchange_rates (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              currency TEXT NOT NULL,
              rate DECIMAL(10,4) NOT NULL,
              fetch_date DATE NOT NULL,
              created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
              UNIQUE(currency, fetch_date)
            );

            CREATE TABLE IF NOT EXISTS expenses (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              amount DECIMAL(10,2) NOT NULL,
              category TEXT NOT NULL,
              sub_category TEXT,
              date DATE NOT NULL,
              description TEXT,
              is_fixed BOOLEAN DEFAULT 0,
              created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
          `);

          // Başlangıç verilerini ekle
          const result = await db.getFirstAsync('SELECT COUNT(*) as count FROM settings');
          const settingsCount = result ? Number(result.count) : 0;
          
          if (settingsCount === 0) {
            await db.execAsync(`
              INSERT INTO settings (key, value) VALUES 
              ('currency', 'TRY'),
              ('language', 'tr'),
              ('theme', 'light')
            `);
          }
        } catch (error) {
          console.error('Database initialization error:', error);
          return false; // Hata durumunda false dön
        }

        return true; // Her durumda true dön - bu satır önemli
      }}
    >
      <View style={{ flex: 1 }}>
        {children}
      </View>
    </SQLiteProvider>
  );
};

export default DatabaseProvider;
