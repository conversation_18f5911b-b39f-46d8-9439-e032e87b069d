/**
 * <PERSON>or Kütüphanesi Servisi
 * Kullanıcı raporlarını kaydetme, yükleme ve yönetme
 */

import AsyncStorage from '@react-native-async-storage/async-storage';

const STORAGE_KEYS = {
  SAVED_REPORTS: 'saved_reports',
  REPORT_TEMPLATES: 'report_templates',
  REPORT_SETTINGS: 'report_settings',
  FAVORITES: 'favorite_reports',
  RECENT_REPORTS: 'recent_reports'
};

export class ReportLibraryService {
  
  /**
   * Raporu kaydet
   * @param {Object} reportData - Rapor verisi
   * @param {Object} metadata - Rapor metadata'sı
   */
  static async saveReport(reportData, metadata = {}) {
    try {
      const reportId = this.generateReportId();
      const timestamp = new Date().toISOString();
      
      const reportEntry = {
        id: reportId,
        ...reportData,
        metadata: {
          ...metadata,
          createdAt: timestamp,
          lastModified: timestamp,
          version: '1.0.0'
        }
      };

      // Mevcut raporları al
      const savedReports = await this.getSavedReports();
      savedReports.push(reportEntry);

      // Kaydet
      await AsyncStorage.setItem(
        STORAGE_KEYS.SAVED_REPORTS, 
        JSON.stringify(savedReports)
      );

      // Son kullanılan raporlara ekle
      await this.addToRecentReports(reportEntry);

      return { success: true, reportId };
    } catch (error) {
      console.error('Rapor kaydetme hatası:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Kayıtlı raporları getir
   */
  static async getSavedReports() {
    try {
      const reportsData = await AsyncStorage.getItem(STORAGE_KEYS.SAVED_REPORTS);
      return reportsData ? JSON.parse(reportsData) : [];
    } catch (error) {
      console.error('Kayıtlı raporları getirme hatası:', error);
      return [];
    }
  }

  /**
   * Raporu sil
   * @param {string} reportId - Rapor ID'si
   */
  static async deleteReport(reportId) {
    try {
      const savedReports = await this.getSavedReports();
      const filteredReports = savedReports.filter(report => report.id !== reportId);
      
      await AsyncStorage.setItem(
        STORAGE_KEYS.SAVED_REPORTS, 
        JSON.stringify(filteredReports)
      );

      // Favorilerden ve son kullanılanlardan da sil
      await this.removeFromFavorites(reportId);
      await this.removeFromRecentReports(reportId);

      return { success: true };
    } catch (error) {
      console.error('Rapor silme hatası:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Raporu güncelle
   * @param {string} reportId - Rapor ID'si
   * @param {Object} updateData - Güncellenecek veri
   */
  static async updateReport(reportId, updateData) {
    try {
      const savedReports = await this.getSavedReports();
      const reportIndex = savedReports.findIndex(report => report.id === reportId);
      
      if (reportIndex === -1) {
        throw new Error('Rapor bulunamadı');
      }

      savedReports[reportIndex] = {
        ...savedReports[reportIndex],
        ...updateData,
        metadata: {
          ...savedReports[reportIndex].metadata,
          lastModified: new Date().toISOString()
        }
      };

      await AsyncStorage.setItem(
        STORAGE_KEYS.SAVED_REPORTS, 
        JSON.stringify(savedReports)
      );

      return { success: true };
    } catch (error) {
      console.error('Rapor güncelleme hatası:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Favorilere ekle/çıkar
   * @param {string} reportId - Rapor ID'si
   */
  static async toggleFavorite(reportId) {
    try {
      const favorites = await this.getFavorites();
      const isFavorite = favorites.includes(reportId);

      if (isFavorite) {
        const filteredFavorites = favorites.filter(id => id !== reportId);
        await AsyncStorage.setItem(
          STORAGE_KEYS.FAVORITES, 
          JSON.stringify(filteredFavorites)
        );
      } else {
        favorites.push(reportId);
        await AsyncStorage.setItem(
          STORAGE_KEYS.FAVORITES, 
          JSON.stringify(favorites)
        );
      }

      return { success: true, isFavorite: !isFavorite };
    } catch (error) {
      console.error('Favori işlemi hatası:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Favorileri getir
   */
  static async getFavorites() {
    try {
      const favoritesData = await AsyncStorage.getItem(STORAGE_KEYS.FAVORITES);
      return favoritesData ? JSON.parse(favoritesData) : [];
    } catch (error) {
      console.error('Favorileri getirme hatası:', error);
      return [];
    }
  }

  /**
   * Son kullanılan raporları getir
   */
  static async getRecentReports() {
    try {
      const recentData = await AsyncStorage.getItem(STORAGE_KEYS.RECENT_REPORTS);
      return recentData ? JSON.parse(recentData) : [];
    } catch (error) {
      console.error('Son kullanılan raporları getirme hatası:', error);
      return [];
    }
  }

  /**
   * Son kullanılan raporlara ekle
   * @param {Object} reportEntry - Rapor girdisi
   */
  static async addToRecentReports(reportEntry) {
    try {
      const recentReports = await this.getRecentReports();
      
      // Zaten varsa sil
      const filteredReports = recentReports.filter(report => report.id !== reportEntry.id);
      
      // Başa ekle
      filteredReports.unshift(reportEntry);
      
      // Maksimum 10 rapor tut
      const limitedReports = filteredReports.slice(0, 10);
      
      await AsyncStorage.setItem(
        STORAGE_KEYS.RECENT_REPORTS, 
        JSON.stringify(limitedReports)
      );

      return { success: true };
    } catch (error) {
      console.error('Son kullanılan raporlara ekleme hatası:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Rapor şablonlarını getir
   */
  static async getReportTemplates() {
    try {
      const templatesData = await AsyncStorage.getItem(STORAGE_KEYS.REPORT_TEMPLATES);
      return templatesData ? JSON.parse(templatesData) : [];
    } catch (error) {
      console.error('Rapor şablonlarını getirme hatası:', error);
      return [];
    }
  }

  /**
   * Özel rapor şablonu kaydet
   * @param {Object} templateData - Şablon verisi
   */
  static async saveReportTemplate(templateData) {
    try {
      const templateId = this.generateTemplateId();
      const timestamp = new Date().toISOString();
      
      const templateEntry = {
        id: templateId,
        ...templateData,
        createdAt: timestamp,
        isCustom: true
      };

      const templates = await this.getReportTemplates();
      templates.push(templateEntry);

      await AsyncStorage.setItem(
        STORAGE_KEYS.REPORT_TEMPLATES, 
        JSON.stringify(templates)
      );

      return { success: true, templateId };
    } catch (error) {
      console.error('Şablon kaydetme hatası:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Rapor istatistiklerini getir
   */
  static async getReportStats() {
    try {
      const savedReports = await this.getSavedReports();
      const favorites = await this.getFavorites();
      const templates = await this.getReportTemplates();
      
      return {
        totalReports: savedReports.length,
        totalExports: savedReports.reduce((sum, report) => 
          sum + (report.metadata?.exportCount || 0), 0),
        favoritesCount: favorites.length,
        customTemplatesCount: templates.filter(t => t.isCustom).length,
        lastReportDate: savedReports.length > 0 ? 
          savedReports[savedReports.length - 1].metadata.createdAt : null
      };
    } catch (error) {
      console.error('Rapor istatistikleri hatası:', error);
      return {
        totalReports: 0,
        totalExports: 0,
        favoritesCount: 0,
        customTemplatesCount: 0,
        lastReportDate: null
      };
    }
  }

  /**
   * Raporu dışa aktarma sayısını artır
   * @param {string} reportId - Rapor ID'si
   */
  static async incrementExportCount(reportId) {
    try {
      const savedReports = await this.getSavedReports();
      const reportIndex = savedReports.findIndex(report => report.id === reportId);
      
      if (reportIndex !== -1) {
        savedReports[reportIndex].metadata.exportCount = 
          (savedReports[reportIndex].metadata.exportCount || 0) + 1;
        
        await AsyncStorage.setItem(
          STORAGE_KEYS.SAVED_REPORTS, 
          JSON.stringify(savedReports)
        );
      }
    } catch (error) {
      console.error('Export sayısı artırma hatası:', error);
    }
  }

  /**
   * Favorilerden sil
   * @param {string} reportId - Rapor ID'si
   */
  static async removeFromFavorites(reportId) {
    try {
      const favorites = await this.getFavorites();
      const filteredFavorites = favorites.filter(id => id !== reportId);
      
      await AsyncStorage.setItem(
        STORAGE_KEYS.FAVORITES, 
        JSON.stringify(filteredFavorites)
      );
    } catch (error) {
      console.error('Favorilerden silme hatası:', error);
    }
  }

  /**
   * Son kullanılanlardan sil
   * @param {string} reportId - Rapor ID'si
   */
  static async removeFromRecentReports(reportId) {
    try {
      const recentReports = await this.getRecentReports();
      const filteredReports = recentReports.filter(report => report.id !== reportId);
      
      await AsyncStorage.setItem(
        STORAGE_KEYS.RECENT_REPORTS, 
        JSON.stringify(filteredReports)
      );
    } catch (error) {
      console.error('Son kullanılanlardan silme hatası:', error);
    }
  }

  /**
   * Benzersiz rapor ID'si oluştur
   */
  static generateReportId() {
    return `report_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Benzersiz şablon ID'si oluştur
   */
  static generateTemplateId() {
    return `template_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Tüm verileri temizle (test/debug için)
   */
  static async clearAllData() {
    try {
      await AsyncStorage.multiRemove([
        STORAGE_KEYS.SAVED_REPORTS,
        STORAGE_KEYS.REPORT_TEMPLATES,
        STORAGE_KEYS.REPORT_SETTINGS,
        STORAGE_KEYS.FAVORITES,
        STORAGE_KEYS.RECENT_REPORTS
      ]);
      
      return { success: true };
    } catch (error) {
      console.error('Tüm verileri temizleme hatası:', error);
      return { success: false, error: error.message };
    }
  }
}

export default ReportLibraryService;
