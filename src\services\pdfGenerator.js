import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import { formatCurrency } from '../utils/formatters';
import { getDateRangeTitle } from '../utils/dateFilterUtils';

/**
 * PDF oluşturmak için tema ayarları
 */
export const PDF_THEME = {
  light: {
    income: {
      primary: '#4ade80',
      secondary: '#bbf7d0',
      text: '#166534'
    },
    expense: {
      primary: '#f87171',
      secondary: '#fecaca',
      text: '#991b1b'
    }
  }
};

/**
 * PDF şablonu oluşturur
 * @param {Object} param0 Şablon parametreleri
 * @param {string} param0.type İşlem tipi (income/expense)
 * @param {Array} param0.transactions İşlem listesi
 * @param {number} param0.total Toplam tutar
 * @param {Object} param0.dateRange Tarih aralığı
 * @returns {string} HTML şablonu
 */
export const generateTransactionPdfTemplate = ({ type, transactions, total, dateRange }) => {
  const theme = PDF_THEME.light[type];
  const now = new Date();
  const reportDate = format(now, 'd MMMM yyyy', { locale: tr });
  const reportTime = format(now, 'HH:mm', { locale: tr });
  const dateRangeTitle = getDateRangeTitle(dateRange);
  
  return `
    // ...mevcut PDF şablonu...
    <div class="header-content">
      <h1 class="title">${type === 'income' ? 'Gelir' : 'Gider'} Raporu</h1>
      <p class="subtitle">${dateRangeTitle}</p>
      // ...rest of the template...
    </div>
  `;
};

/**
 * İşlem raporunu PDF olarak oluşturur ve paylaşır
 * @param {Object} param0 Rapor parametreleri
 * @param {string} param0.type İşlem tipi (income/expense)
 * @param {Array} param0.transactions İşlem listesi
 * @param {Function} param0.onStart Başlangıç callback
 * @param {Function} param0.onComplete Tamamlanma callback
 * @param {Function} param0.onError Hata callback
 */
export const generateAndShareTransactionPdf = async ({
  type,
  transactions,
  onStart,
  onComplete,
  onError
}) => {
  try {
    onStart?.();
    
    const total = transactions.reduce((sum, tx) => sum + Number(tx.amount), 0);
    const html = generateTransactionPdfTemplate({ type, transactions, total });
    
    const { uri } = await Print.printToFileAsync({ 
      html,
      base64: false
    });
    
    await Sharing.shareAsync(uri);
    onComplete?.();
  } catch (error) {
    console.error('PDF oluşturma hatası:', error);
    onError?.(error);
  }
};
