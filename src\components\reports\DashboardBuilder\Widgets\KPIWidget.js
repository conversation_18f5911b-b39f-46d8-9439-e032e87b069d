import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

/**
 * KPI Widget - Anahtar performans g<PERSON><PERSON>geleri
 */
const KPIWidget = ({ widget, isPreviewMode, onUpdate, theme }) => {
  /**
   * <PERSON><PERSON><PERSON>li tema de<PERSON> alma
   * @param {string} property - <PERSON><PERSON>
   * @param {string} fallback - <PERSON><PERSON><PERSON><PERSON><PERSON>
   * @returns {string} <PERSON><PERSON><PERSON>li tema değeri
   */
  const getSafeThemeValue = (property, fallback) => {
    return theme[property] || theme.colors?.[property.toLowerCase()] || fallback;
  };

  return (
    <View style={[styles.container, { backgroundColor: getSafeThemeValue('BACKGROUND', '#fff') }]}>
      <View style={styles.kpiContent}>
        <Text style={[styles.kpiIcon, { color: getSafeThemeValue('INFO', '#17a2b8') }]}>
          🎯
        </Text>
        <Text style={[styles.kpiValue, { color: getSafeThemeValue('TEXT_PRIMARY', '#000') }]}>
          ₺12,345
        </Text>
        <Text style={[styles.kpiLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666') }]}>
          {widget.config?.metric || 'Total Income'}
        </Text>
        <Text style={[styles.kpiTrend, { color: getSafeThemeValue('SUCCESS', '#28a745') }]}>
          ↗ +15.2%
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 8,
  },
  kpiContent: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  kpiIcon: {
    fontSize: 20,
    marginBottom: 8,
  },
  kpiValue: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
  },
  kpiLabel: {
    fontSize: 10,
    marginBottom: 4,
  },
  kpiTrend: {
    fontSize: 11,
    fontWeight: '600',
  },
});

export default KPIWidget;
