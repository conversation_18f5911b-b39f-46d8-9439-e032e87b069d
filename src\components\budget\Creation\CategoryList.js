/**
 * Kategori Listesi Komponenti
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md - Stage 2, Phase 2
 * 
 * Seçili kategorilerin listesi ve bütçe girişleri
 * Maksimum 200 satır - Tek sorumluluk prensibi
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../../../context/ThemeContext';
import CategoryBudgetInput from './CategoryBudgetInput';
import { formatTRY, formatUSD, formatEUR } from '../../../utils/formatters';

/**
 * Kategori listesi komponenti
 * @param {Object} props - Component props
 * @param {Array} props.categories - Kategori listesi
 * @param {Object} props.categoryBudgets - Kategori bütçeleri {categoryId: amount}
 * @param {string} props.currency - Para birimi ('TRY', 'USD', 'EUR')
 * @param {Function} props.onCategoryBudgetChange - Kategori bütçe değişim callback
 * @param {Function} props.onCategoryRemove - Kategori kaldırma callback
 * @param {Function} props.onAddCategory - Kategori ekleme callback
 * @param {Object} props.theme - Tema objesi (opsiyonel, context'ten alınır)
 */
const CategoryList = ({ 
  categories = [], 
  categoryBudgets = {}, 
  currency = 'TRY',
  onCategoryBudgetChange, 
  onCategoryRemove,
  onAddCategory,
  theme: propTheme 
}) => {
  const { theme } = useTheme();
  const currentTheme = propTheme || theme;

  /**
   * Toplam bütçe hesaplama
   * @returns {number} Toplam bütçe miktarı
   */
  const calculateTotalBudget = () => {
    return Object.values(categoryBudgets).reduce((total, amount) => total + (amount || 0), 0);
  };

  /**
   * Para birimi formatı
   * @param {number} value - Formatlanacak değer
   * @param {string} currencyCode - Para birimi kodu
   * @returns {string} Formatlanmış değer
   */
  const formatCurrency = (value, currencyCode) => {
    switch (currencyCode) {
      case 'USD':
        return formatUSD(value);
      case 'EUR':
        return formatEUR(value);
      default:
        return formatTRY(value);
    }
  };

  /**
   * Para birimi sembolü
   * @param {string} currencyCode - Para birimi kodu
   * @returns {string} Para birimi sembolü
   */
  const getCurrencySymbol = (currencyCode) => {
    switch (currencyCode) {
      case 'USD':
        return '$';
      case 'EUR':
        return '€';
      default:
        return '₺';
    }
  };

  /**
   * Kategori listesi render fonksiyonu
   * @param {Object} item - Kategori objesi
   * @returns {JSX.Element} Kategori list item
   */
  const renderCategoryItem = ({ item }) => {
    return (
      <CategoryBudgetInput
        category={item}
        amount={categoryBudgets[item.id] || 0}
        currency={currency}
        onAmountChange={onCategoryBudgetChange}
        onRemove={onCategoryRemove}
        theme={currentTheme}
      />
    );
  };

  /**
   * Boş liste komponenti
   */
  const EmptyListComponent = () => (
    <View style={styles.emptyContainer}>
      <MaterialIcons name="category" size={64} color={currentTheme.TEXT_SECONDARY} />
      <Text style={[styles.emptyTitle, { color: currentTheme.TEXT_PRIMARY }]}>
        Henüz kategori seçilmedi
      </Text>
      <Text style={[styles.emptySubtitle, { color: currentTheme.TEXT_SECONDARY }]}>
        Bütçe takibi yapmak için kategoriler ekleyin
      </Text>
      <TouchableOpacity
        style={[styles.addFirstButton, { backgroundColor: currentTheme.PRIMARY }]}
        onPress={onAddCategory}
      >
        <MaterialIcons name="add" size={20} color={currentTheme.WHITE} />
        <Text style={[styles.addFirstButtonText, { color: currentTheme.WHITE }]}>
          İlk Kategoriyi Ekle
        </Text>
      </TouchableOpacity>
    </View>
  );

  /**
   * Liste header komponenti
   */
  const ListHeaderComponent = () => {
    const totalBudget = calculateTotalBudget();
    const currencySymbol = getCurrencySymbol(currency);

    return (
      <View style={styles.headerContainer}>
        <View style={styles.headerContent}>
          <Text style={[styles.headerTitle, { color: currentTheme.TEXT_PRIMARY }]}>
            Kategori Bütçeleri
          </Text>
          <Text style={[styles.headerSubtitle, { color: currentTheme.TEXT_SECONDARY }]}>
            Her kategori için ayrı limit belirleyin
          </Text>
        </View>

        {/* Toplam bütçe özeti */}
        {totalBudget > 0 && (
          <View style={[styles.totalBudgetCard, { backgroundColor: currentTheme.PRIMARY + '20' }]}>
            <View style={styles.totalBudgetContent}>
              <MaterialIcons name="account-balance-wallet" size={24} color={currentTheme.PRIMARY} />
              <View style={styles.totalBudgetInfo}>
                <Text style={[styles.totalBudgetLabel, { color: currentTheme.TEXT_SECONDARY }]}>
                  Toplam Bütçe
                </Text>
                <Text style={[styles.totalBudgetAmount, { color: currentTheme.PRIMARY }]}>
                  {currencySymbol}{totalBudget.toLocaleString('tr-TR')}
                </Text>
                <Text style={[styles.totalBudgetFormatted, { color: currentTheme.TEXT_SECONDARY }]}>
                  {formatCurrency(totalBudget, currency)}
                </Text>
              </View>
            </View>
          </View>
        )}
      </View>
    );
  };

  /**
   * Liste footer komponenti
   */
  const ListFooterComponent = () => (
    <View style={styles.footerContainer}>
      <TouchableOpacity
        style={[styles.addCategoryButton, { backgroundColor: currentTheme.SURFACE, borderColor: currentTheme.PRIMARY }]}
        onPress={onAddCategory}
      >
        <MaterialIcons name="add" size={24} color={currentTheme.PRIMARY} />
        <Text style={[styles.addCategoryButtonText, { color: currentTheme.PRIMARY }]}>
          Kategori Ekle
        </Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: currentTheme.BACKGROUND }]}>
      <FlatList
        data={categories}
        renderItem={renderCategoryItem}
        keyExtractor={(item) => item.id.toString()}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContent}
        ListHeaderComponent={ListHeaderComponent}
        ListEmptyComponent={EmptyListComponent}
        ListFooterComponent={categories.length > 0 ? ListFooterComponent : null}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
  },
  listContent: {
    paddingBottom: 16,
  },
  headerContainer: {
    marginBottom: 16,
  },
  headerContent: {
    alignItems: 'center',
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  totalBudgetCard: {
    borderRadius: 12,
    padding: 16,
  },
  totalBudgetContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  totalBudgetInfo: {
    flex: 1,
  },
  totalBudgetLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  totalBudgetAmount: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  totalBudgetFormatted: {
    fontSize: 12,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  addFirstButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 24,
    gap: 8,
  },
  addFirstButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  footerContainer: {
    paddingTop: 16,
  },
  addCategoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    borderWidth: 2,
    borderStyle: 'dashed',
    gap: 8,
  },
  addCategoryButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
});

export default CategoryList;
