/**
 * Şablon seçici bileşeni
 * Kullanıcının rapor şablonlarını kategoriler halinde gö<PERSON>ntülemesini ve seçmesini sağlar
 */

import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Alert
} from 'react-native';
import { useTheme } from '../../../context/ThemeContext';
import { 
  getTemplatesByCategory, 
  searchTemplates, 
  CATEGORY_DISPLAY_INFO, 
  TEMPLATE_CATEGORIES 
} from './TemplateConfig';

/**
 * Şablon kategorisi kartı bileşeni
 */
const CategoryCard = ({ category, templates, onTemplateSelect, theme }) => {
  const [expanded, setExpanded] = useState(false);
  const categoryInfo = CATEGORY_DISPLAY_INFO[category];
  
  return (
    <View style={[styles.categoryCard, { backgroundColor: theme.colors.surface }]}>
      <TouchableOpacity
        style={[styles.categoryHeader, { borderBottomColor: theme.colors.border }]}
        onPress={() => setExpanded(!expanded)}
      >
        <View style={styles.categoryHeaderContent}>
          <View style={[styles.categoryIcon, { backgroundColor: categoryInfo.color + '20' }]}>
            <Text style={[styles.categoryIconText, { color: categoryInfo.color }]}>
              {categoryInfo.icon === 'analytics' ? '📊' : 
               categoryInfo.icon === 'trending-up' ? '📈' :
               categoryInfo.icon === 'shopping-cart' ? '🛒' :
               categoryInfo.icon === 'target' ? '🎯' : '👁️'}
            </Text>
          </View>
          <View style={styles.categoryInfo}>
            <Text style={[styles.categoryName, { color: theme.colors.text }]}>
              {categoryInfo.name}
            </Text>
            <Text style={[styles.categoryDescription, { color: theme.colors.textSecondary }]}>
              {categoryInfo.description}
            </Text>
          </View>
          <Text style={[styles.templateCount, { color: theme.colors.textSecondary }]}>
            {templates.length} şablon
          </Text>
        </View>
        <Text style={[styles.expandIcon, { color: theme.colors.textSecondary }]}>
          {expanded ? '▼' : '▶'}
        </Text>
      </TouchableOpacity>
      
      {expanded && (
        <View style={styles.templateList}>
          {templates.map((template, index) => (
            <TemplateCard
              key={template.id}
              template={template}
              onPress={() => onTemplateSelect(template)}
              theme={theme}
              isLast={index === templates.length - 1}
            />
          ))}
        </View>
      )}
    </View>
  );
};

/**
 * Şablon kartı bileşeni
 */
const TemplateCard = ({ template, onPress, theme, isLast }) => {
  const getComplexityColor = (complexity) => {
    switch (complexity) {
      case 'low': return '#10B981';
      case 'medium': return '#F59E0B';
      case 'high': return '#EF4444';
      default: return theme.colors.textSecondary;
    }
  };
  
  const getComplexityText = (complexity) => {
    switch (complexity) {
      case 'low': return 'Basit';
      case 'medium': return 'Orta';
      case 'high': return 'Gelişmiş';
      default: return 'Belirsiz';
    }
  };
  
  return (
    <TouchableOpacity
      style={[
        styles.templateCard,
        { 
          backgroundColor: theme.colors.background,
          borderColor: theme.colors.border,
          borderBottomWidth: isLast ? 0 : 1
        }
      ]}
      onPress={onPress}
    >
      <View style={styles.templateCardContent}>
        <View style={styles.templateHeader}>
          <Text style={[styles.templateName, { color: theme.colors.text }]}>
            {template.name}
          </Text>
          <View style={styles.templateMeta}>
            <Text style={[
              styles.complexityBadge,
              { 
                color: getComplexityColor(template.estimatedComplexity),
                backgroundColor: getComplexityColor(template.estimatedComplexity) + '20'
              }
            ]}>
              {getComplexityText(template.estimatedComplexity)}
            </Text>
          </View>
        </View>
        
        <Text style={[styles.templateDescription, { color: theme.colors.textSecondary }]}>
          {template.description}
        </Text>
        
        <View style={styles.templateFooter}>
          <View style={styles.templateTags}>
            {template.tags.slice(0, 3).map((tag, index) => (
              <Text key={index} style={[styles.tag, { color: theme.colors.textSecondary }]}>
                #{tag}
              </Text>
            ))}
          </View>
          <Text style={[styles.outputTypes, { color: theme.colors.textSecondary }]}>
            {template.outputTypes.join(', ')}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

/**
 * Ana şablon seçici bileşeni
 */
const TemplateSelector = ({ onTemplateSelect, onClose }) => {
  const { theme } = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState(null);
  
  const categorizedTemplates = useMemo(() => getTemplatesByCategory(), []);
  
  const filteredTemplates = useMemo(() => {
    if (!searchQuery) return categorizedTemplates;
    
    const searchResults = searchTemplates(searchQuery);
    const filtered = {};
    
    searchResults.forEach(template => {
      const category = template.category;
      if (!filtered[category]) {
        filtered[category] = [];
      }
      filtered[category].push(template);
    });
    
    return filtered;
  }, [searchQuery, categorizedTemplates]);
  
  const handleTemplateSelect = (template) => {
    Alert.alert(
      'Şablon Seçimi',
      `${template.name} şablonu seçildi. Bu şablon ${template.estimatedComplexity} düzeyinde karmaşıklığa sahip.`,
      [
        { text: 'İptal', style: 'cancel' },
        { 
          text: 'Devam Et', 
          onPress: () => onTemplateSelect(template)
        }
      ]
    );
  };
  
  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={[styles.header, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.title, { color: theme.colors.text }]}>
          Rapor Şablonu Seç
        </Text>
        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <Text style={[styles.closeButtonText, { color: theme.colors.primary }]}>
            ✕
          </Text>
        </TouchableOpacity>
      </View>
      
      <View style={[styles.searchContainer, { backgroundColor: theme.colors.surface }]}>
        <TextInput
          style={[styles.searchInput, { 
            backgroundColor: theme.colors.background,
            borderColor: theme.colors.border,
            color: theme.colors.text
          }]}
          placeholder="Şablon ara..."
          placeholderTextColor={theme.colors.textSecondary}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>
      
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.statsContainer}>
          <Text style={[styles.statsText, { color: theme.colors.textSecondary }]}>
            Toplam {Object.values(filteredTemplates).reduce((sum, templates) => sum + templates.length, 0)} şablon,{' '}
            {Object.keys(filteredTemplates).length} kategori
          </Text>
        </View>
        
        {Object.keys(filteredTemplates).map(category => (
          <CategoryCard
            key={category}
            category={category}
            templates={filteredTemplates[category]}
            onTemplateSelect={handleTemplateSelect}
            theme={theme}
          />
        ))}
        
        {Object.keys(filteredTemplates).length === 0 && (
          <View style={styles.emptyState}>
            <Text style={[styles.emptyStateText, { color: theme.colors.textSecondary }]}>
              Arama kriterlerinize uygun şablon bulunamadı
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  closeButton: {
    padding: 8,
  },
  closeButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  searchContainer: {
    padding: 16,
  },
  searchInput: {
    height: 44,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
  },
  content: {
    flex: 1,
  },
  statsContainer: {
    padding: 16,
    paddingBottom: 8,
  },
  statsText: {
    fontSize: 14,
    textAlign: 'center',
  },
  categoryCard: {
    margin: 16,
    marginTop: 8,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 2,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  categoryHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  categoryIconText: {
    fontSize: 16,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  categoryDescription: {
    fontSize: 13,
  },
  templateCount: {
    fontSize: 12,
    marginLeft: 8,
  },
  expandIcon: {
    fontSize: 12,
    marginLeft: 8,
  },
  templateList: {
    paddingBottom: 8,
  },
  templateCard: {
    padding: 16,
    borderBottomWidth: 1,
  },
  templateCardContent: {
    
  },
  templateHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  templateName: {
    fontSize: 15,
    fontWeight: '600',
    flex: 1,
  },
  templateMeta: {
    marginLeft: 8,
  },
  complexityBadge: {
    fontSize: 11,
    fontWeight: '500',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    overflow: 'hidden',
  },
  templateDescription: {
    fontSize: 13,
    lineHeight: 18,
    marginBottom: 12,
  },
  templateFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  templateTags: {
    flexDirection: 'row',
    flex: 1,
  },
  tag: {
    fontSize: 11,
    marginRight: 8,
  },
  outputTypes: {
    fontSize: 11,
    fontStyle: 'italic',
  },
  emptyState: {
    padding: 32,
    alignItems: 'center',
  },
  emptyStateText: {
    fontSize: 16,
    textAlign: 'center',
  },
});

export default TemplateSelector;
