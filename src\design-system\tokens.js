/**
 * Design System Tokens
 * Merkezi tasarım sistemi - tüm tasarım kararları burada
 */

import { Dimensions, Platform } from 'react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

/**
 * <PERSON><PERSON> - <PERSON> ve tutarlı
 */
export const colors = {
  // Ana renkler
  primary: {
    50: '#f0f9ff',
    100: '#e0f2fe',
    200: '#bae6fd',
    300: '#7dd3fc',
    400: '#38bdf8',
    500: '#0ea5e9', // Ana primary
    600: '#0284c7',
    700: '#0369a1',
    800: '#075985',
    900: '#0c4a6e',
  },

  // Gri tonlar<PERSON>
  gray: {
    50: '#f8fafc',
    100: '#f1f5f9',
    200: '#e2e8f0',
    300: '#cbd5e1',
    400: '#94a3b8',
    500: '#64748b',
    600: '#475569',
    700: '#334155',
    800: '#1e293b',
    900: '#0f172a',
  },

  // Anlamsal renkler
  success: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e', // Ana success
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
  },

  danger: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444', // Ana danger
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',
  },

  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b', // Ana warning
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
  },

  info: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6', // Ana info
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a',
  },

  // Özel renkler
  income: '#22c55e',
  expense: '#ef4444',
  neutral: '#64748b',
};

/**
 * Typography - Modern font sistemi
 */
export const typography = {
  fontFamily: {
    regular: Platform.select({
      ios: 'System',
      android: 'Roboto',
    }),
    medium: Platform.select({
      ios: 'System',
      android: 'Roboto-Medium',
    }),
    bold: Platform.select({
      ios: 'System',
      android: 'Roboto-Bold',
    }),
  },

  fontSize: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
    '5xl': 48,
  },

  fontWeight: {
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
  },

  lineHeight: {
    tight: 1.25,
    snug: 1.375,
    normal: 1.5,
    relaxed: 1.625,
    loose: 2,
  },

  letterSpacing: {
    tighter: -0.5,
    tight: -0.25,
    normal: 0,
    wide: 0.25,
    wider: 0.5,
    widest: 1,
  },
};

/**
 * Spacing - Tutarlı boşluk sistemi
 */
export const spacing = {
  0: 0,
  1: 4,
  2: 8,
  3: 12,
  4: 16,
  5: 20,
  6: 24,
  7: 28,
  8: 32,
  9: 36,
  10: 40,
  12: 48,
  16: 64,
  20: 80,
  24: 96,
  32: 128,
  40: 160,
  48: 192,
  56: 224,
  64: 256,
};

/**
 * Border Radius - Tutarlı köşe yuvarlaklığı
 */
export const borderRadius = {
  none: 0,
  sm: 2,
  base: 4,
  md: 6,
  lg: 8,
  xl: 12,
  '2xl': 16,
  '3xl': 24,
  full: 9999,
};

/**
 * Shadows - Tutarlı gölge sistemi
 */
export const shadows = {
  xs: {
    shadowColor: colors.gray[900],
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  sm: {
    shadowColor: colors.gray[900],
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  base: {
    shadowColor: colors.gray[900],
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  md: {
    shadowColor: colors.gray[900],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 4,
  },
  lg: {
    shadowColor: colors.gray[900],
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.15,
    shadowRadius: 15,
    elevation: 8,
  },
  xl: {
    shadowColor: colors.gray[900],
    shadowOffset: { width: 0, height: 20 },
    shadowOpacity: 0.25,
    shadowRadius: 25,
    elevation: 12,
  },
};

/**
 * Breakpoints - Responsive design
 */
export const breakpoints = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
};

/**
 * Z-Index - Katman yönetimi
 */
export const zIndex = {
  hide: -1,
  auto: 'auto',
  base: 0,
  docked: 10,
  dropdown: 1000,
  sticky: 1100,
  banner: 1200,
  overlay: 1300,
  modal: 1400,
  popover: 1500,
  skipLink: 1600,
  toast: 1700,
  tooltip: 1800,
};

/**
 * Animasyon süreleri
 */
export const duration = {
  fastest: 50,
  faster: 100,
  fast: 150,
  normal: 200,
  slow: 300,
  slower: 500,
  slowest: 1000,
};

/**
 * Easing fonksiyonları
 */
export const easing = {
  linear: 'linear',
  ease: 'ease',
  easeIn: 'ease-in',
  easeOut: 'ease-out',
  easeInOut: 'ease-in-out',
};

/**
 * Responsive yardımcıları
 */
export const responsive = {
  isSmallScreen: screenWidth < breakpoints.sm,
  isMediumScreen: screenWidth >= breakpoints.sm && screenWidth < breakpoints.lg,
  isLargeScreen: screenWidth >= breakpoints.lg,
  screenWidth,
  screenHeight,
};

/**
 * Tema tanımları
 */
export const lightTheme = {
  colors: {
    background: colors.gray[50],
    surface: '#ffffff',
    card: '#ffffff',
    primary: colors.primary[500],
    primaryLight: colors.primary[400],
    primaryDark: colors.primary[600],
    secondary: colors.gray[600],
    text: {
      primary: colors.gray[900],
      secondary: colors.gray[600],
      tertiary: colors.gray[500],
      inverse: '#ffffff',
    },
    border: colors.gray[200],
    divider: colors.gray[100],
    success: colors.success[500],
    danger: colors.danger[500],
    warning: colors.warning[500],
    info: colors.info[500],
    income: colors.income,
    expense: colors.expense,
  },
};

export const darkTheme = {
  colors: {
    background: colors.gray[900],
    surface: colors.gray[800],
    card: colors.gray[800],
    primary: colors.primary[400],
    primaryLight: colors.primary[300],
    primaryDark: colors.primary[500],
    secondary: colors.gray[400],
    text: {
      primary: colors.gray[100],
      secondary: colors.gray[300],
      tertiary: colors.gray[400],
      inverse: colors.gray[900],
    },
    border: colors.gray[700],
    divider: colors.gray[800],
    success: colors.success[400],
    danger: colors.danger[400],
    warning: colors.warning[400],
    info: colors.info[400],
    income: colors.success[400],
    expense: colors.danger[400],
  },
};

/**
 * Component variants
 */
export const variants = {
  button: {
    primary: {
      backgroundColor: colors.primary[500],
      color: '#ffffff',
    },
    secondary: {
      backgroundColor: colors.gray[100],
      color: colors.gray[900],
    },
    outline: {
      backgroundColor: 'transparent',
      borderColor: colors.primary[500],
      color: colors.primary[500],
    },
    ghost: {
      backgroundColor: 'transparent',
      color: colors.primary[500],
    },
  },
  text: {
    heading: {
      fontFamily: typography.fontFamily.bold,
      fontWeight: typography.fontWeight.bold,
      color: colors.gray[900],
    },
    body: {
      fontFamily: typography.fontFamily.regular,
      fontWeight: typography.fontWeight.normal,
      color: colors.gray[700],
    },
    caption: {
      fontFamily: typography.fontFamily.regular,
      fontWeight: typography.fontWeight.normal,
      fontSize: typography.fontSize.sm,
      color: colors.gray[500],
    },
  },
};

export default {
  colors,
  typography,
  spacing,
  borderRadius,
  shadows,
  breakpoints,
  zIndex,
  duration,
  easing,
  responsive,
  lightTheme,
  darkTheme,
  variants,
};
