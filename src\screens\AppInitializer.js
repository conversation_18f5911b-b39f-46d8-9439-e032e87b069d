import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  Alert
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../context/ThemeContext';
import { useAppContext } from '../context/AppContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import { checkThemeStatus } from '../utils/themeDebugUtils';

/**
 * Uygulama başlangıç yöneticisi
 * İlk açılış, PIN kontrolü ve yönlendirme işlemlerini yönetir
 */
export default function AppInitializer() {
  const navigation = useNavigation();
  const { theme, isDarkMode } = useTheme();
  const { defaultCurrency } = useAppContext();
  const [isLoading, setIsLoading] = useState(true);
  const [initializationStep, setInitializationStep] = useState('');

  useEffect(() => {
    // Tema yüklenmesini bekle
    const timer = setTimeout(() => {
      initializeApp();
    }, 100); // Kısa bir gecikme ile tema yüklenmesini garanti et
    
    return () => clearTimeout(timer);
  }, []);

  /**
   * Uygulamayı başlatır ve gerekli kontrolleri yapar
   */
  const initializeApp = async () => {
    try {
      setInitializationStep('Başlangıç kontrolleri yapılıyor...');
      
      // Tema ayarlarını kontrol et ve debug et
      const themeStatus = await checkThemeStatus();
      console.log('AppInitializer - Tema durumu:', { isDarkMode, themeStatus });
      
      // 1. İlk açılış kontrolü
      const isFirstLaunch = await checkFirstLaunch();
      
      if (isFirstLaunch) {
        // İlk açılış - Tutorial ekranına yönlendir
        setInitializationStep('İlk açılış hazırlanıyor...');
        await setupFirstLaunch();
        navigation.replace('Tutorial');
        return;
      }
      
      // 2. PIN güvenlik kontrolü
      setInitializationStep('Güvenlik kontrolleri yapılıyor...');
      const hasPinSecurity = await checkPinSecurity();
      
      if (hasPinSecurity) {
        // PIN koruması var - PIN doğrulama ekranına yönlendir
        navigation.replace('PinSecurity');
      } else {
        // PIN yok - Ana uygulamaya git
        navigation.replace('Main');
      }
      
    } catch (error) {
      console.error('Uygulama başlatma hatası:', error);
      Alert.alert('Hata', 'Uygulama başlatılırken bir hata oluştu.');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * İlk açılış kontrolü
   */
  const checkFirstLaunch = async () => {
    try {
      const firstLaunch = await AsyncStorage.getItem('isFirstLaunch');
      return firstLaunch === null; // null ise ilk açılış
    } catch (error) {
      console.error('İlk açılış kontrolü hatası:', error);
      return true; // Hata durumunda ilk açılış olarak kabul et
    }
  };

  /**
   * İlk açılış setup'ı
   */
  const setupFirstLaunch = async () => {
    try {
      await AsyncStorage.setItem('isFirstLaunch', 'false');
      
      // Varsayılan ayarları kur
      const defaultSettings = {
        theme: 'system',
        notifications: true,
        security: {
          pinEnabled: false,
          biometricEnabled: false
        }
      };
      
      await AsyncStorage.setItem('appSettings', JSON.stringify(defaultSettings));
    } catch (error) {
      console.error('İlk açılış setup hatası:', error);
    }
  };

  /**
   * PIN güvenlik kontrolü
   */
  const checkPinSecurity = async () => {
    try {
      const pinHash = await SecureStore.getItemAsync('pinHash');
      return pinHash !== null;
    } catch (error) {
      console.error('PIN kontrolü hatası:', error);
      return false;
    }
  };

  if (!isLoading) {
    return null; // Navigation replace edildi, bu component artık görünmeyecek
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.content}>
        <View style={[styles.logoContainer, { backgroundColor: theme.colors.primary }]}>
          <Text style={[styles.logoText, { color: theme.colors.white }]}>
            💰
          </Text>
        </View>
        
        <Text style={[styles.appName, { color: theme.colors.text }]}>
          Finansal Takip
        </Text>
        
        <Text style={[styles.appSlogan, { color: theme.colors.textSecondary }]}>
          Kişisel finans yöneticiniz
        </Text>
        
        <ActivityIndicator 
          size="large" 
          color={theme.colors.primary} 
          style={styles.loader}
        />
        
        <Text style={[styles.loadingText, { color: theme.colors.textSecondary }]}>
          {initializationStep}
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  logoContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 32,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 8,
  },
  logoText: {
    fontSize: 48,
  },
  appName: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  appSlogan: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 48,
  },
  loader: {
    marginBottom: 24,
  },
  loadingText: {
    fontSize: 14,
    textAlign: 'center',
  },
});
