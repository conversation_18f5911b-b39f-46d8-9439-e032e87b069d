import { useState, useCallback, useMemo } from 'react';

/**
 * Tablo filtrelerini yönetmek için custom hook
 * @param {Array} initialFilters - Başlangıç filtreleri
 * @returns {Object} Filtre yönetimi fonksiyonları ve durumu
 */
const useTableFilters = (initialFilters = []) => {
  const [filters, setFilters] = useState(initialFilters);
  const [history, setHistory] = useState([initialFilters]);
  const [historyIndex, setHistoryIndex] = useState(0);

  /**
   * Filtre ekle
   * @param {Object} filter - Eklenecek filtre
   */
  const addFilter = useCallback((filter) => {
    const newFilter = {
      id: filter.id || `filter_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      column: filter.column,
      operator: filter.operator || 'contains',
      value: filter.value,
      type: filter.type || 'text',
      enabled: filter.enabled !== false,
      caseSensitive: filter.caseSensitive || false,
      ...filter
    };

    setFilters(prev => {
      const newFilters = [...prev, newFilter];
      addToHistory(newFilters);
      return newFilters;
    });
  }, []);

  /**
   * Filtre sil
   * @param {string} filterId - Silinecek filtre ID'si
   */
  const removeFilter = useCallback((filterId) => {
    setFilters(prev => {
      const newFilters = prev.filter(filter => filter.id !== filterId);
      addToHistory(newFilters);
      return newFilters;
    });
  }, []);

  /**
   * Filtre güncelle
   * @param {string} filterId - Güncellenecek filtre ID'si
   * @param {Object} updates - Güncellenecek alanlar
   */
  const updateFilter = useCallback((filterId, updates) => {
    setFilters(prev => {
      const newFilters = prev.map(filter => 
        filter.id === filterId ? { ...filter, ...updates } : filter
      );
      addToHistory(newFilters);
      return newFilters;
    });
  }, []);

  /**
   * Filtre aktiflik durumunu değiştir
   * @param {string} filterId - Filtre ID'si
   * @param {boolean} enabled - Aktiflik durumu
   */
  const toggleFilter = useCallback((filterId, enabled) => {
    updateFilter(filterId, { enabled });
  }, [updateFilter]);

  /**
   * Tüm filtreleri temizle
   */
  const clearFilters = useCallback(() => {
    setFilters([]);
    addToHistory([]);
  }, []);

  /**
   * Tüm filtreleri aktif/pasif yap
   * @param {boolean} enabled - Aktiflik durumu
   */
  const toggleAllFilters = useCallback((enabled) => {
    setFilters(prev => {
      const newFilters = prev.map(filter => ({ ...filter, enabled }));
      addToHistory(newFilters);
      return newFilters;
    });
  }, []);

  /**
   * Belirli sütun için filtreleri getir
   * @param {string} columnId - Sütun ID'si
   * @returns {Array} Sütun filtreleri
   */
  const getFiltersForColumn = useCallback((columnId) => {
    return filters.filter(filter => filter.column === columnId);
  }, [filters]);

  /**
   * Veriyi filtrele
   * @param {Array} data - Filtrelenecek veri
   * @param {Array} columns - Sütun bilgileri
   * @returns {Array} Filtrelenmiş veri
   */
  const filterData = useCallback((data, columns = []) => {
    if (!data || !Array.isArray(data)) return [];
    
    const activeFilters = filters.filter(filter => filter.enabled);
    if (activeFilters.length === 0) return data;

    return data.filter(item => {
      return activeFilters.every(filter => {
        const value = item[filter.column];
        const filterValue = filter.value;
        
        if (value === null || value === undefined) return false;
        
        const column = columns.find(col => col.id === filter.column);
        return applyFilterCondition(value, filterValue, filter.operator, filter.type, column, filter.caseSensitive);
      });
    });
  }, [filters]);

  /**
   * Filtre koşulunu uygula
   * @param {*} value - Değer
   * @param {*} filterValue - Filtre değeri
   * @param {string} operator - Operatör
   * @param {string} type - Veri tipi
   * @param {Object} column - Sütun bilgisi
   * @param {boolean} caseSensitive - Büyük/küçük harf duyarlılığı
   * @returns {boolean} Koşul sonucu
   */
  const applyFilterCondition = useCallback((value, filterValue, operator, type, column, caseSensitive) => {
    if (value === null || value === undefined) return false;

    switch (type) {
      case 'text':
        return applyTextFilter(value, filterValue, operator, caseSensitive);
      case 'number':
        return applyNumberFilter(value, filterValue, operator);
      case 'date':
        return applyDateFilter(value, filterValue, operator);
      case 'boolean':
        return applyBooleanFilter(value, filterValue, operator);
      default:
        return applyTextFilter(value, filterValue, operator, caseSensitive);
    }
  }, []);

  /**
   * Metin filtresi uygula
   * @param {string} value - Değer
   * @param {string} filterValue - Filtre değeri
   * @param {string} operator - Operatör
   * @param {boolean} caseSensitive - Büyük/küçük harf duyarlılığı
   * @returns {boolean} Koşul sonucu
   */
  const applyTextFilter = useCallback((value, filterValue, operator, caseSensitive) => {
    const val = caseSensitive ? value.toString() : value.toString().toLowerCase();
    const filterVal = caseSensitive ? filterValue.toString() : filterValue.toString().toLowerCase();

    switch (operator) {
      case 'equals':
        return val === filterVal;
      case 'contains':
        return val.includes(filterVal);
      case 'startsWith':
        return val.startsWith(filterVal);
      case 'endsWith':
        return val.endsWith(filterVal);
      case 'notContains':
        return !val.includes(filterVal);
      case 'notEquals':
        return val !== filterVal;
      case 'isEmpty':
        return val === '';
      case 'isNotEmpty':
        return val !== '';
      default:
        return val.includes(filterVal);
    }
  }, []);

  /**
   * Sayı filtresi uygula
   * @param {number} value - Değer
   * @param {number} filterValue - Filtre değeri
   * @param {string} operator - Operatör
   * @returns {boolean} Koşul sonucu
   */
  const applyNumberFilter = useCallback((value, filterValue, operator) => {
    const val = parseFloat(value);
    const filterVal = parseFloat(filterValue);

    if (isNaN(val) || isNaN(filterVal)) return false;

    switch (operator) {
      case 'equals':
        return val === filterVal;
      case 'notEquals':
        return val !== filterVal;
      case 'greaterThan':
        return val > filterVal;
      case 'lessThan':
        return val < filterVal;
      case 'greaterThanOrEqual':
        return val >= filterVal;
      case 'lessThanOrEqual':
        return val <= filterVal;
      case 'between':
        // filterValue should be an array [min, max]
        if (Array.isArray(filterValue) && filterValue.length === 2) {
          return val >= parseFloat(filterValue[0]) && val <= parseFloat(filterValue[1]);
        }
        return false;
      default:
        return val === filterVal;
    }
  }, []);

  /**
   * Tarih filtresi uygula
   * @param {string|Date} value - Değer
   * @param {string|Date} filterValue - Filtre değeri
   * @param {string} operator - Operatör
   * @returns {boolean} Koşul sonucu
   */
  const applyDateFilter = useCallback((value, filterValue, operator) => {
    const val = new Date(value);
    const filterVal = new Date(filterValue);

    if (isNaN(val.getTime()) || isNaN(filterVal.getTime())) return false;

    switch (operator) {
      case 'equals':
        return val.toDateString() === filterVal.toDateString();
      case 'after':
        return val > filterVal;
      case 'before':
        return val < filterVal;
      case 'between':
        // filterValue should be an array [start, end]
        if (Array.isArray(filterValue) && filterValue.length === 2) {
          const startDate = new Date(filterValue[0]);
          const endDate = new Date(filterValue[1]);
          return val >= startDate && val <= endDate;
        }
        return false;
      case 'thisWeek':
        const today = new Date();
        const weekStart = new Date(today.setDate(today.getDate() - today.getDay()));
        const weekEnd = new Date(today.setDate(today.getDate() - today.getDay() + 6));
        return val >= weekStart && val <= weekEnd;
      case 'thisMonth':
        const now = new Date();
        const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
        const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        return val >= monthStart && val <= monthEnd;
      case 'thisYear':
        const currentYear = new Date().getFullYear();
        return val.getFullYear() === currentYear;
      default:
        return val.toDateString() === filterVal.toDateString();
    }
  }, []);

  /**
   * Boolean filtresi uygula
   * @param {boolean} value - Değer
   * @param {boolean} filterValue - Filtre değeri
   * @param {string} operator - Operatör
   * @returns {boolean} Koşul sonucu
   */
  const applyBooleanFilter = useCallback((value, filterValue, operator) => {
    const val = Boolean(value);
    const filterVal = Boolean(filterValue);

    switch (operator) {
      case 'equals':
        return val === filterVal;
      case 'notEquals':
        return val !== filterVal;
      case 'isTrue':
        return val === true;
      case 'isFalse':
        return val === false;
      default:
        return val === filterVal;
    }
  }, []);

  /**
   * Geçmişe yeni durum ekle
   * @param {Array} newFilters - Yeni filtre dizisi
   */
  const addToHistory = useCallback((newFilters) => {
    setHistory(prev => {
      const newHistory = prev.slice(0, historyIndex + 1);
      newHistory.push(newFilters);
      // Geçmişi 50 adımla sınırla
      if (newHistory.length > 50) {
        newHistory.shift();
      }
      return newHistory;
    });
    setHistoryIndex(prev => Math.min(prev + 1, 49));
  }, [historyIndex]);

  /**
   * Geri al
   */
  const undo = useCallback(() => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1;
      setHistoryIndex(newIndex);
      setFilters(history[newIndex]);
    }
  }, [history, historyIndex]);

  /**
   * Yinele
   */
  const redo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      const newIndex = historyIndex + 1;
      setHistoryIndex(newIndex);
      setFilters(history[newIndex]);
    }
  }, [history, historyIndex]);

  /**
   * Filtre istatistikleri
   */
  const filterStats = useMemo(() => {
    const active = filters.filter(f => f.enabled);
    const inactive = filters.filter(f => !f.enabled);
    
    return {
      total: filters.length,
      active: active.length,
      inactive: inactive.length,
      byType: {
        text: filters.filter(f => f.type === 'text').length,
        number: filters.filter(f => f.type === 'number').length,
        date: filters.filter(f => f.type === 'date').length,
        boolean: filters.filter(f => f.type === 'boolean').length,
      },
      byOperator: filters.reduce((acc, filter) => {
        acc[filter.operator] = (acc[filter.operator] || 0) + 1;
        return acc;
      }, {}),
    };
  }, [filters]);

  /**
   * Filtreleri JSON olarak dışa aktar
   * @returns {string} JSON string
   */
  const exportFilters = useCallback(() => {
    return JSON.stringify(filters, null, 2);
  }, [filters]);

  /**
   * JSON'dan filtreleri içe aktar
   * @param {string} jsonString - JSON string
   * @returns {boolean} Başarı durumu
   */
  const importFilters = useCallback((jsonString) => {
    try {
      const importedFilters = JSON.parse(jsonString);
      if (Array.isArray(importedFilters)) {
        setFilters(importedFilters);
        addToHistory(importedFilters);
        return true;
      }
      return false;
    } catch (error) {
      return false;
    }
  }, []);

  /**
   * Filtreleri veriye uygula (alias for filterData)
   * @param {Array} data - Filtrelenecek veri
   * @returns {Array} Filtrelenmiş veri
   */
  const applyFilters = useCallback((data) => {
    return filterData(data);
  }, [filterData]);

  return {
    // Durum
    filters,
    filterStats,
    canUndo: historyIndex > 0,
    canRedo: historyIndex < history.length - 1,
    
    // Filtre yönetimi
    addFilter,
    removeFilter,
    updateFilter,
    toggleFilter,
    clearFilters,
    toggleAllFilters,
    
    // Veri filtreleme
    filterData,
    applyFilters, // Alias for filterData
    getFiltersForColumn,
    
    // Geçmiş yönetimi
    undo,
    redo,
    
    // Yardımcı fonksiyonlar
    exportFilters,
    importFilters,
  };
};

export { useTableFilters };
