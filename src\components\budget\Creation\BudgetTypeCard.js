/**
 * <PERSON>ütçe Türü Kartı Komponenti
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md - Stage 2, Phase 1
 * 
 * <PERSON>k bütçe türünün görsel kartı
 * Maksimum 150 satır - <PERSON>k sorumluluk prensibi
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../../../context/ThemeContext';

/**
 * Bütçe türü kartı komponenti
 * @param {Object} props - Component props
 * @param {Object} props.type - Bütçe türü objesi
 * @param {boolean} props.isSelected - Seçili olup olmadığı
 * @param {Function} props.onSelect - Seçim callback fonksiyonu
 * @param {Object} props.theme - <PERSON>ma objesi (opsiyonel, context'ten alınır)
 */
const BudgetTypeCard = ({ 
  type, 
  isSelected, 
  onSelect, 
  theme: propTheme 
}) => {
  const { theme } = useTheme();
  const currentTheme = propTheme || theme;

  // Animasyon değeri
  const scaleValue = React.useRef(new Animated.Value(1)).current;

  /**
   * Kart basma animasyonu
   */
  const handlePressIn = () => {
    Animated.spring(scaleValue, {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  };

  /**
   * Kart bırakma animasyonu
   */
  const handlePressOut = () => {
    Animated.spring(scaleValue, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  /**
   * Seçim işleyicisi
   */
  const handleSelect = () => {
    if (onSelect) {
      onSelect(type.id);
    }
  };

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: [{ scale: scaleValue }],
        }
      ]}
    >
      <TouchableOpacity
        style={[
          styles.card,
          {
            backgroundColor: currentTheme.SURFACE,
            borderColor: isSelected 
              ? currentTheme.PRIMARY 
              : currentTheme.BORDER,
            borderWidth: isSelected ? 2 : 1,
          }
        ]}
        onPress={handleSelect}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={0.9}
      >
        {/* Önerilen rozeti */}
        {type.recommended && (
          <View style={[styles.recommendedBadge, { backgroundColor: currentTheme.SUCCESS }]}>
            <Text style={[styles.recommendedText, { color: currentTheme.WHITE }]}>
              Önerilen
            </Text>
          </View>
        )}

        {/* İkon ve seçim göstergesi */}
        <View style={styles.header}>
          <View style={[styles.iconContainer, { backgroundColor: currentTheme.PRIMARY + '20' }]}>
            <MaterialIcons 
              name={type.icon} 
              size={28} 
              color={currentTheme.PRIMARY} 
            />
          </View>
          
          <MaterialIcons
            name={isSelected ? 'radio-button-checked' : 'radio-button-unchecked'}
            size={20}
            color={isSelected ? currentTheme.PRIMARY : currentTheme.TEXT_SECONDARY}
          />
        </View>

        {/* Başlık ve açıklama */}
        <View style={styles.content}>
          <Text style={[styles.title, { color: currentTheme.TEXT_PRIMARY }]}>
            {type.title}
          </Text>
          <Text style={[styles.description, { color: currentTheme.TEXT_SECONDARY }]}>
            {type.description}
          </Text>
        </View>

        {/* Özellikler */}
        {type.features && type.features.length > 0 && (
          <View style={styles.features}>
            {type.features.slice(0, 2).map((feature, index) => (
              <View key={index} style={styles.featureItem}>
                <MaterialIcons 
                  name="check-circle" 
                  size={14} 
                  color={currentTheme.SUCCESS} 
                />
                <Text style={[styles.featureText, { color: currentTheme.TEXT_SECONDARY }]}>
                  {feature}
                </Text>
              </View>
            ))}
          </View>
        )}
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 12,
  },
  card: {
    borderRadius: 12,
    padding: 16,
    position: 'relative',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  recommendedBadge: {
    position: 'absolute',
    top: -6,
    right: 12,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
    zIndex: 1,
  },
  recommendedText: {
    fontSize: 10,
    fontWeight: 'bold',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    marginBottom: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  description: {
    fontSize: 13,
    lineHeight: 18,
  },
  features: {
    gap: 4,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  featureText: {
    fontSize: 12,
    flex: 1,
  },
});

export default BudgetTypeCard;
