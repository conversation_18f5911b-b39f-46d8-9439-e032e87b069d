import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  PanResponder,
  Dimensions,
} from 'react-native';
import { useTheme } from '../../../../context/ThemeContext';
import ChartWidget from '../Widgets/ChartWidget';
import TableWidget from '../Widgets/TableWidget';
import KPIWidget from '../Widgets/KPIWidget';
import TextWidget from '../Widgets/TextWidget';
import ImageWidget from '../Widgets/ImageWidget';
import FilterWidget from '../Widgets/FilterWidget';
import MetricWidget from '../Widgets/MetricWidget';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

/**
 * Dashboard Widget - Tek bir widget container
 */
const DashboardWidget = ({
  widget,
  isSelected = false,
  isDragged = false,
  isPreviewMode = false,
  onSelect,
  onMove,
  onResize,
  onRemove,
  onUpdate,
  onDragStart,
  onDragEnd,
  snapToGrid = true,
  theme,
}) => {
  const [position] = useState(new Animated.ValueXY(widget.position));
  const [size, setSize] = useState(widget.size);
  const [isResizing, setIsResizing] = useState(false);
  const panResponder = useRef(null);
  const resizeResponder = useRef(null);

  /**
   * Güvenli tema değeri alma
   * @param {string} property - Tema özelliği
   * @param {string} fallback - Varsayılan değer
   * @returns {string} Güvenli tema değeri
   */
  const getSafeThemeValue = (property, fallback) => {
    return theme[property] || theme.colors?.[property.toLowerCase()] || fallback;
  };

  // Pan Responder for dragging
  if (!panResponder.current) {
    panResponder.current = PanResponder.create({
      onMoveShouldSetPanResponder: (evt, gestureState) => {
        return !isPreviewMode && !isResizing && (Math.abs(gestureState.dx) > 5 || Math.abs(gestureState.dy) > 5);
      },
      onPanResponderGrant: (evt, gestureState) => {
        onDragStart();
        position.setOffset({
          x: position.x._value,
          y: position.y._value,
        });
        position.setValue({ x: 0, y: 0 });
      },
      onPanResponderMove: Animated.event(
        [null, { dx: position.x, dy: position.y }],
        { useNativeDriver: false }
      ),
      onPanResponderRelease: (evt, gestureState) => {
        position.flattenOffset();
        const newX = snapToGrid ? Math.round(gestureState.moveX / 20) * 20 : gestureState.moveX;
        const newY = snapToGrid ? Math.round(gestureState.moveY / 20) * 20 : gestureState.moveY;
        
        onMove({ x: newX, y: newY });
        onDragEnd();
      },
    });
  }

  // Resize Responder
  if (!resizeResponder.current) {
    resizeResponder.current = PanResponder.create({
      onMoveShouldSetPanResponder: () => !isPreviewMode,
      onPanResponderGrant: () => {
        setIsResizing(true);
      },
      onPanResponderMove: (evt, gestureState) => {
        const newWidth = Math.max(100, size.width + gestureState.dx);
        const newHeight = Math.max(80, size.height + gestureState.dy);
        setSize({ width: newWidth, height: newHeight });
      },
      onPanResponderRelease: (evt, gestureState) => {
        setIsResizing(false);
        const newWidth = Math.max(100, size.width + gestureState.dx);
        const newHeight = Math.max(80, size.height + gestureState.dy);
        const finalSize = {
          width: snapToGrid ? Math.round(newWidth / 20) * 20 : newWidth,
          height: snapToGrid ? Math.round(newHeight / 20) * 20 : newHeight,
        };
        setSize(finalSize);
        onResize(finalSize);
      },
    });
  }

  /**
   * Widget içeriği render
   */
  const renderWidgetContent = () => {
    const contentProps = {
      widget,
      isPreviewMode,
      onUpdate,
      theme,
    };

    switch (widget.type) {
      case 'chart':
        return <ChartWidget {...contentProps} />;
      case 'table':
        return <TableWidget {...contentProps} />;
      case 'kpi':
        return <KPIWidget {...contentProps} />;
      case 'text':
        return <TextWidget {...contentProps} />;
      case 'image':
        return <ImageWidget {...contentProps} />;
      case 'filter':
        return <FilterWidget {...contentProps} />;
      case 'metric':
        return <MetricWidget {...contentProps} />;
      default:
        return (
          <View style={styles.defaultContent}>
            <Text style={[styles.defaultText, { color: getSafeThemeValue('TEXT_SECONDARY', '#666') }]}>
              {widget.type} widget
            </Text>
          </View>
        );
    }
  };

  return (
    <Animated.View
      style={[
        styles.container,
        {
          left: widget.position.x,
          top: widget.position.y,
          width: size.width,
          height: size.height,
          backgroundColor: getSafeThemeValue('SURFACE', '#f9f9f9'),
          borderColor: isSelected ? getSafeThemeValue('PRIMARY', '#007AFF') : getSafeThemeValue('BORDER', '#e0e0e0'),
          borderWidth: isSelected ? 2 : 1,
          zIndex: widget.zIndex || 1,
          opacity: isDragged ? 0.8 : 1,
          transform: position.getTranslateTransform(),
        },
      ]}
      {...(isPreviewMode ? {} : panResponder.current.panHandlers)}
    >
      {/* Widget Header */}
      {!isPreviewMode && (
        <View style={[styles.widgetHeader, { backgroundColor: getSafeThemeValue('SURFACE', '#f9f9f9') }]}>
          <TouchableOpacity
            style={styles.widgetTitle}
            onPress={onSelect}
          >
            <Text style={[styles.widgetTitleText, { color: getSafeThemeValue('TEXT_PRIMARY', '#000') }]}>
              {widget.title}
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.widgetAction, { backgroundColor: getSafeThemeValue('ERROR', '#dc3545') }]}
            onPress={onRemove}
          >
            <Text style={[styles.widgetActionText, { color: getSafeThemeValue('SURFACE', '#fff') }]}>
              ×
            </Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Widget Content */}
      <View style={[styles.widgetContent, { backgroundColor: getSafeThemeValue('BACKGROUND', '#fff') }]}>
        {renderWidgetContent()}
      </View>

      {/* Resize Handle */}
      {isSelected && !isPreviewMode && (
        <View
          style={[
            styles.resizeHandle,
            { backgroundColor: getSafeThemeValue('PRIMARY', '#007AFF') },
          ]}
          {...resizeResponder.current.panHandlers}
        />
      )}

      {/* Selection Indicators */}
      {isSelected && !isPreviewMode && (
        <>
          <View style={[styles.selectionIndicator, styles.topLeft, { backgroundColor: getSafeThemeValue('PRIMARY', '#007AFF') }]} />
          <View style={[styles.selectionIndicator, styles.topRight, { backgroundColor: getSafeThemeValue('PRIMARY', '#007AFF') }]} />
          <View style={[styles.selectionIndicator, styles.bottomLeft, { backgroundColor: getSafeThemeValue('PRIMARY', '#007AFF') }]} />
          <View style={[styles.selectionIndicator, styles.bottomRight, { backgroundColor: getSafeThemeValue('PRIMARY', '#007AFF') }]} />
        </>
      )}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
  },
  widgetHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  widgetTitle: {
    flex: 1,
  },
  widgetTitleText: {
    fontSize: 12,
    fontWeight: '600',
  },
  widgetAction: {
    width: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  widgetActionText: {
    fontSize: 12,
    fontWeight: '700',
  },
  widgetContent: {
    flex: 1,
    padding: 8,
  },
  defaultContent: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  defaultText: {
    fontSize: 14,
    fontWeight: '500',
  },
  resizeHandle: {
    position: 'absolute',
    right: -3,
    bottom: -3,
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  selectionIndicator: {
    position: 'absolute',
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  topLeft: {
    top: -4,
    left: -4,
  },
  topRight: {
    top: -4,
    right: -4,
  },
  bottomLeft: {
    bottom: -4,
    left: -4,
  },
  bottomRight: {
    bottom: -4,
    right: -4,
  },
});

export default DashboardWidget;
