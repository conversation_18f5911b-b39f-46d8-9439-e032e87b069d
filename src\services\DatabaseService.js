import * as SQLite from 'expo-sqlite';
import { runMigrations } from '../db/migrations';
import { optimizeDatabase } from '../utils/dbUtils';
import { queryOptimizer, performanceMonitor } from '../utils/performanceUtils';

/**
 * Veritabanını başlatır ve gerekli tabloları oluşturur
 * @param {SQLite.SQLiteDatabase} db SQLite veritabanı nesnesi
 * @returns {Promise<void>}
 */
export const initializeDatabase = async (db) => {
  performanceMonitor.start('database_initialization');

  try {
    // Veritabanı bağlantısını global değişkene ata
    global.db = db;
    console.log('Veritabanı bağlantısı global değişkene atandı');

    // Query optimizer'ı başlat
    queryOptimizer.db = db;

    // Performance ayarları (transaction dışında) - Kaldırıldı
    // PRAGMA ayarları transaction içinde çalışmaz, bu yüzden kaldırıldı

    // Veritabanı sürümünü kontrol et
    const { user_version: currentDbVersion } = await db.getFirstAsync('PRAGMA user_version');

    // Eğer veritabanı zaten kuruluysa, güncelleme yapmadan çık
    if (currentDbVersion >= 1) {
      // Migrasyonları her durumda çalıştır
      await runMigrations(db);

      // Veritabanını optimize et
      await optimizeDatabase(db);

      // Index'leri oluştur
      await createOptimizedIndexes(db);

      const initTime = performanceMonitor.end('database_initialization');
      console.log(`Veritabanı başarıyla başlatıldı (${initTime}ms)`);
      return;
    }

    // WAL modunu etkinleştir (Write-Ahead Logging)
    await db.execAsync('PRAGMA journal_mode = WAL;');

    // Yabancı anahtar kısıtlamalarını etkinleştir
    await db.execAsync('PRAGMA foreign_keys = ON;');

    // Tüm tabloları oluştur
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        type TEXT NOT NULL CHECK(type IN ('income', 'expense', 'both')),
        color TEXT,
        icon TEXT,
        is_default INTEGER DEFAULT 0
      );

      CREATE TABLE IF NOT EXISTS transactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        type TEXT NOT NULL CHECK(type IN ('income', 'expense')),
        amount REAL NOT NULL,
        description TEXT,
        date TEXT NOT NULL,
        category_id INTEGER,
        currency TEXT DEFAULT 'TRY',
        preferred_currency TEXT DEFAULT 'USD',
        exchange_rate REAL,
        converted_amount REAL,
        usd_equivalent REAL DEFAULT 0,
        eur_equivalent REAL DEFAULT 0,
        custom_equivalent REAL DEFAULT 0,
        metadata TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE SET NULL
      );

      CREATE TABLE IF NOT EXISTS investment_assets (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        symbol TEXT NOT NULL,
        type TEXT NOT NULL,
        current_price REAL NOT NULL,
        purchase_price REAL NOT NULL,
        quantity REAL NOT NULL,
        decimal_places INTEGER DEFAULT 2,
        notes TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS investment_transactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        asset_id INTEGER NOT NULL,
        type TEXT NOT NULL CHECK(type IN ('buy', 'sell', 'dividend', 'interest', 'transfer')),
        quantity REAL NOT NULL,
        price REAL NOT NULL,
        date TEXT NOT NULL,
        notes TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (asset_id) REFERENCES investment_assets (id) ON DELETE CASCADE
      );

      CREATE TABLE IF NOT EXISTS work_shifts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        start_time TEXT NOT NULL,
        end_time TEXT NOT NULL,
        hourly_rate REAL NOT NULL,
        is_overtime INTEGER DEFAULT 0,
        is_holiday INTEGER DEFAULT 0,
        description TEXT,
        earnings REAL NOT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS work_payments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        period_start TEXT NOT NULL,
        period_end TEXT NOT NULL,
        amount REAL NOT NULL,
        total_hours REAL NOT NULL,
        is_paid INTEGER DEFAULT 0,
        payment_date TEXT,
        notes TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS work_settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        default_hourly_rate REAL NOT NULL DEFAULT 0,
        overtime_multiplier REAL NOT NULL DEFAULT 1.5,
        holiday_multiplier REAL NOT NULL DEFAULT 2.0
      );
    `);

    // Varsayılan kategorileri ekle
    await createDefaultCategories(db);

    // Varsayılan mesai ayarlarını ekle
    await createDefaultWorkSettings(db);

    // Migrasyonları çalıştır (transaction dışında)
    await runMigrations(db);

    // Veritabanını optimize et (transaction dışında)
    await optimizeDatabase(db);

    // Index'leri oluştur
    await createOptimizedIndexes(db);

    // Veritabanı sürümünü güncelle
    await db.execAsync('PRAGMA user_version = 1');

    const initTime = performanceMonitor.end('database_initialization');
    console.log(`Veritabanı başarıyla başlatıldı (${initTime}ms)`);
  } catch (error) {
    performanceMonitor.end('database_initialization');
    console.error('Veritabanı başlatma hatası:', error);
    throw error;
  }
};

/**
 * Varsayılan kategorileri oluşturur
 * @param {SQLite.SQLiteDatabase} db SQLite veritabanı nesnesi
 */
const createDefaultCategories = async (db) => {
  try {
    // Kategorileri kontrol et
    const categories = await db.getAllAsync('SELECT * FROM categories');

    if (categories.length === 0) {
      // Tüm kategorileri tek bir transaction içinde ekle
      await db.withTransactionAsync(async () => {
        // Gelir kategorileri
        await db.runAsync(
          'INSERT INTO categories (name, type, color, icon, is_default) VALUES (?, ?, ?, ?, ?)',
          'Maaş', 'income', '#2ecc71', 'account-balance-wallet', 1
        );

        await db.runAsync(
          'INSERT INTO categories (name, type, color, icon, is_default) VALUES (?, ?, ?, ?, ?)',
          'Ek Gelir', 'income', '#3498db', 'attach-money', 0
        );

        await db.runAsync(
          'INSERT INTO categories (name, type, color, icon, is_default) VALUES (?, ?, ?, ?, ?)',
          'Hediye', 'income', '#9b59b6', 'card-giftcard', 0
        );

        await db.runAsync(
          'INSERT INTO categories (name, type, color, icon, is_default) VALUES (?, ?, ?, ?, ?)',
          'Yatırım Geliri', 'income', '#f39c12', 'trending-up', 0
        );

        // Gider kategorileri
        await db.runAsync(
          'INSERT INTO categories (name, type, color, icon, is_default) VALUES (?, ?, ?, ?, ?)',
          'Market', 'expense', '#e74c3c', 'shopping-cart', 1
        );

        await db.runAsync(
          'INSERT INTO categories (name, type, color, icon, is_default) VALUES (?, ?, ?, ?, ?)',
          'Yemek', 'expense', '#e67e22', 'restaurant', 0
        );

        await db.runAsync(
          'INSERT INTO categories (name, type, color, icon, is_default) VALUES (?, ?, ?, ?, ?)',
          'Ulaşım', 'expense', '#1abc9c', 'directions-car', 0
        );

        await db.runAsync(
          'INSERT INTO categories (name, type, color, icon, is_default) VALUES (?, ?, ?, ?, ?)',
          'Faturalar', 'expense', '#34495e', 'receipt', 0
        );

        await db.runAsync(
          'INSERT INTO categories (name, type, color, icon, is_default) VALUES (?, ?, ?, ?, ?)',
          'Eğlence', 'expense', '#9b59b6', 'local-movies', 0
        );

        await db.runAsync(
          'INSERT INTO categories (name, type, color, icon, is_default) VALUES (?, ?, ?, ?, ?)',
          'Sağlık', 'expense', '#2980b9', 'local-hospital', 0
        );

        await db.runAsync(
          'INSERT INTO categories (name, type, color, icon, is_default) VALUES (?, ?, ?, ?, ?)',
          'Giyim', 'expense', '#16a085', 'local-mall', 0
        );

        // Her ikisi için kategoriler
        await db.runAsync(
          'INSERT INTO categories (name, type, color, icon, is_default) VALUES (?, ?, ?, ?, ?)',
          'Diğer', 'both', '#7f8c8d', 'category', 0
        );
      });
    }
  } catch (error) {
    console.error('Varsayılan kategorileri oluşturma hatası:', error);
    throw error;
  }
};

/**
 * Varsayılan mesai ayarlarını oluşturur
 * @param {SQLite.SQLiteDatabase} db SQLite veritabanı nesnesi
 */
const createDefaultWorkSettings = async (db) => {
  try {
    // Ayarları kontrol et
    const settings = await db.getAllAsync('SELECT * FROM work_settings');

    if (settings.length === 0) {
      await db.runAsync(
        'INSERT INTO work_settings (default_hourly_rate, overtime_multiplier, holiday_multiplier) VALUES (?, ?, ?)',
        100, 1.5, 2.0
      );
    }
  } catch (error) {
    console.error('Varsayılan mesai ayarlarını oluşturma hatası:', error);
    throw error;
  }
};

/**
 * Optimized database indexes oluşturur
 * @param {SQLite.SQLiteDatabase} db SQLite veritabanı nesnesi
 */
const createOptimizedIndexes = async (db) => {
  try {
    performanceMonitor.start('create_indexes');

    // Transaction tablosu için index'ler
    await queryOptimizer.ensureIndex('transactions', ['date']);
    await queryOptimizer.ensureIndex('transactions', ['category_id']);
    await queryOptimizer.ensureIndex('transactions', ['type']);
    await queryOptimizer.ensureIndex('transactions', ['date', 'type']);
    await queryOptimizer.ensureIndex('transactions', ['category_id', 'type']);

    // Investment tabloları için index'ler
    await queryOptimizer.ensureIndex('investment_transactions', ['asset_id']);
    await queryOptimizer.ensureIndex('investment_transactions', ['date']);
    await queryOptimizer.ensureIndex('investment_transactions', ['asset_id', 'date']);

    // Work tabloları için index'ler
    await queryOptimizer.ensureIndex('work_shifts', ['start_time']);
    await queryOptimizer.ensureIndex('work_shifts', ['is_overtime']);
    await queryOptimizer.ensureIndex('work_payments', ['period_start_date', 'period_end_date']);

    // Categories tablosu için index'ler
    await queryOptimizer.ensureIndex('categories', ['type']);
    await queryOptimizer.ensureIndex('categories', ['is_default']);

    const indexTime = performanceMonitor.end('create_indexes');
    console.log(`Database indexes oluşturuldu (${indexTime}ms)`);
  } catch (error) {
    performanceMonitor.end('create_indexes');
    console.error('Index oluşturma hatası:', error);
  }
};
