import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../../constants/colors';

/**
 * Boş durum bileşeni
 * Liste veya içerik boş olduğunda gösterilir
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {string} props.message - Gösterilecek mesaj
 * @param {string} [props.icon='inbox'] - Kullanılacak ikon
 * @param {string} [props.iconSize=64] - İkon boyutu
 * @param {string} [props.actionLabel] - Eylem düğmesi etiketi
 * @param {Function} [props.onAction] - Eylem tıklama işleyicisi
 * @param {Object} [props.style] - Ek stil özellikleri
 * @returns {JSX.Element} EmptyState bileşeni
 */
const EmptyState = ({
  message,
  icon = 'inbox',
  iconSize = 64,
  actionLabel,
  onAction,
  style
}) => {
  // Renk değişkenleri
  const primaryColor = Colors.PRIMARY;
  const safeWhite = '#FFFFFF';
  const textColor = Colors.GRAY_700;

  return (
    <View style={[styles.container, style]}>
      <MaterialIcons name={icon} size={iconSize} color={primaryColor} />
      <Text style={[styles.message, { color: textColor }]}>{message}</Text>

      {actionLabel && onAction && (
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: primaryColor }]}
          onPress={onAction}
        >
          <Text style={[styles.actionText, { color: safeWhite }]}>{actionLabel}</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 24
  },
  actionButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8
  },
  actionText: {
    fontWeight: '600',
    fontSize: 16
  }
});

export default EmptyState;
