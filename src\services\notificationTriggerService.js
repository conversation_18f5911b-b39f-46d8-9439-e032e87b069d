/**
 * <PERSON><PERSON><PERSON><PERSON> Tetikleyici Servisi
 * Çeşitli olaylara göre bildirimleri tetikler
 */
import { format, addDays, parseISO, isAfter, isBefore, differenceInDays } from 'date-fns';
import * as notificationDbService from './notificationDbService';

/**
 * Bütçe uyarı bildirimlerini oluşturur
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} budget - Bütçe
 * @param {number} spentPercentage - Harcanan yüzde
 * @returns {Promise<void>}
 */
export const createBudgetAlertNotification = async (db, budget, spentPercentage) => {
  try {
    // Bildirim ayarlarını kontrol et
    const settings = await notificationDbService.getNotificationSettings(db, 'budget');
    
    if (!settings || !settings.is_enabled) {
      return;
    }
    
    // Bütçe uyarı eşikleri
    const thresholds = [50, 75, 90, 100];
    
    // <PERSON>i eşiğe ulaşıldığını kontrol et
    let threshold = null;
    
    for (const t of thresholds) {
      if (spentPercentage >= t) {
        threshold = t;
      }
    }
    
    if (!threshold) {
      return;
    }
    
    // Daha önce bu eşik için bildirim gönderilmiş mi kontrol et
    const existingNotification = await db.getFirstAsync(`
      SELECT id FROM notifications
      WHERE type = 'budget' AND related_id = ? AND related_type = 'budget'
      AND data LIKE ?
    `, [budget.id, `%"threshold":${threshold}%`]);
    
    if (existingNotification) {
      return;
    }
    
    // Bildirim içeriğini hazırla
    let title = '';
    let message = '';
    let priority = 'normal';
    
    if (threshold === 50) {
      title = 'Bütçe Uyarısı';
      message = `"${budget.name}" bütçenizin %50'sini kullandınız.`;
    } else if (threshold === 75) {
      title = 'Bütçe Uyarısı';
      message = `"${budget.name}" bütçenizin %75'ini kullandınız. Dikkatli olun!`;
      priority = 'high';
    } else if (threshold === 90) {
      title = 'Bütçe Uyarısı';
      message = `"${budget.name}" bütçenizin %90'ını kullandınız. Çok az kaldı!`;
      priority = 'high';
    } else if (threshold === 100) {
      title = 'Bütçe Aşıldı!';
      message = `"${budget.name}" bütçenizi aştınız!`;
      priority = 'high';
    }
    
    // Bildirimi oluştur
    await notificationDbService.createNotification(db, {
      title,
      message,
      type: 'budget',
      priority,
      related_id: budget.id,
      related_type: 'budget',
      data: {
        budgetId: budget.id,
        budgetName: budget.name,
        threshold,
        spentPercentage
      }
    });
  } catch (error) {
    console.error('Bütçe uyarı bildirimi oluşturma hatası:', error);
  }
};

/**
 * Birikim hedefi uyarı bildirimlerini oluşturur
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} savingsGoal - Birikim hedefi
 * @param {number} progressPercentage - İlerleme yüzdesi
 * @returns {Promise<void>}
 */
export const createSavingsGoalAlertNotification = async (db, savingsGoal, progressPercentage) => {
  try {
    // Bildirim ayarlarını kontrol et
    const settings = await notificationDbService.getNotificationSettings(db, 'savings');
    
    if (!settings || !settings.is_enabled) {
      return;
    }
    
    // Birikim hedefi uyarı eşikleri
    const thresholds = [25, 50, 75, 100];
    
    // Hangi eşiğe ulaşıldığını kontrol et
    let threshold = null;
    
    for (const t of thresholds) {
      if (progressPercentage >= t) {
        threshold = t;
      }
    }
    
    if (!threshold) {
      return;
    }
    
    // Daha önce bu eşik için bildirim gönderilmiş mi kontrol et
    const existingNotification = await db.getFirstAsync(`
      SELECT id FROM notifications
      WHERE type = 'savings' AND related_id = ? AND related_type = 'savings_goal'
      AND data LIKE ?
    `, [savingsGoal.id, `%"threshold":${threshold}%`]);
    
    if (existingNotification) {
      return;
    }
    
    // Bildirim içeriğini hazırla
    let title = '';
    let message = '';
    let priority = 'normal';
    
    if (threshold === 25) {
      title = 'Birikim Hedefi İlerlemesi';
      message = `"${savingsGoal.name}" hedefinin %25'ine ulaştınız. Harika gidiyorsunuz!`;
    } else if (threshold === 50) {
      title = 'Birikim Hedefi İlerlemesi';
      message = `"${savingsGoal.name}" hedefinin %50'sine ulaştınız. Yarı yoldasınız!`;
    } else if (threshold === 75) {
      title = 'Birikim Hedefi İlerlemesi';
      message = `"${savingsGoal.name}" hedefinin %75'ine ulaştınız. Çok az kaldı!`;
    } else if (threshold === 100) {
      title = 'Birikim Hedefi Tamamlandı!';
      message = `"${savingsGoal.name}" hedefine ulaştınız! Tebrikler!`;
      priority = 'high';
    }
    
    // Bildirimi oluştur
    await notificationDbService.createNotification(db, {
      title,
      message,
      type: 'savings',
      priority,
      related_id: savingsGoal.id,
      related_type: 'savings_goal',
      data: {
        savingsGoalId: savingsGoal.id,
        savingsGoalName: savingsGoal.name,
        threshold,
        progressPercentage
      }
    });
  } catch (error) {
    console.error('Birikim hedefi uyarı bildirimi oluşturma hatası:', error);
  }
};

/**
 * Fatura hatırlatma bildirimlerini oluşturur
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} bill - Fatura
 * @param {number} daysUntilDue - Vadeye kalan gün sayısı
 * @returns {Promise<void>}
 */
export const createBillReminderNotification = async (db, bill, daysUntilDue) => {
  try {
    // Bildirim ayarlarını kontrol et
    const settings = await notificationDbService.getNotificationSettings(db, 'reminder');
    
    if (!settings || !settings.is_enabled) {
      return;
    }
    
    // Fatura hatırlatma eşikleri (gün)
    const thresholds = [7, 3, 1, 0];
    
    // Hangi eşiğe ulaşıldığını kontrol et
    let threshold = null;
    
    for (const t of thresholds) {
      if (daysUntilDue <= t) {
        threshold = t;
        break;
      }
    }
    
    if (threshold === null) {
      return;
    }
    
    // Daha önce bu eşik için bildirim gönderilmiş mi kontrol et
    const today = format(new Date(), 'yyyy-MM-dd');
    
    const existingNotification = await db.getFirstAsync(`
      SELECT id FROM notifications
      WHERE type = 'reminder' AND related_id = ? AND related_type = 'bill'
      AND data LIKE ? AND date(scheduled_at) = ?
    `, [bill.id, `%"threshold":${threshold}%`, today]);
    
    if (existingNotification) {
      return;
    }
    
    // Bildirim içeriğini hazırla
    let title = '';
    let message = '';
    let priority = 'normal';
    
    if (threshold === 7) {
      title = 'Fatura Hatırlatması';
      message = `"${bill.name}" faturanızın son ödeme tarihine 7 gün kaldı.`;
    } else if (threshold === 3) {
      title = 'Fatura Hatırlatması';
      message = `"${bill.name}" faturanızın son ödeme tarihine 3 gün kaldı.`;
      priority = 'high';
    } else if (threshold === 1) {
      title = 'Fatura Hatırlatması';
      message = `"${bill.name}" faturanızın son ödeme tarihi yarın!`;
      priority = 'high';
    } else if (threshold === 0) {
      title = 'Fatura Son Ödeme Günü!';
      message = `"${bill.name}" faturanızın son ödeme tarihi bugün!`;
      priority = 'high';
    }
    
    // Bildirimi oluştur
    await notificationDbService.createNotification(db, {
      title,
      message,
      type: 'reminder',
      priority,
      related_id: bill.id,
      related_type: 'bill',
      data: {
        billId: bill.id,
        billName: bill.name,
        threshold,
        daysUntilDue,
        dueDate: bill.due_date
      }
    });
  } catch (error) {
    console.error('Fatura hatırlatma bildirimi oluşturma hatası:', error);
  }
};

/**
 * Maaş hatırlatma bildirimlerini oluşturur
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} salary - Maaş
 * @param {number} daysUntilPayday - Maaş gününe kalan gün sayısı
 * @returns {Promise<void>}
 */
export const createSalaryReminderNotification = async (db, salary, daysUntilPayday) => {
  try {
    // Bildirim ayarlarını kontrol et
    const settings = await notificationDbService.getNotificationSettings(db, 'reminder');
    
    if (!settings || !settings.is_enabled) {
      return;
    }
    
    // Maaş hatırlatma eşikleri (gün)
    const thresholds = [3, 1, 0];
    
    // Hangi eşiğe ulaşıldığını kontrol et
    let threshold = null;
    
    for (const t of thresholds) {
      if (daysUntilPayday <= t) {
        threshold = t;
        break;
      }
    }
    
    if (threshold === null) {
      return;
    }
    
    // Daha önce bu eşik için bildirim gönderilmiş mi kontrol et
    const today = format(new Date(), 'yyyy-MM-dd');
    
    const existingNotification = await db.getFirstAsync(`
      SELECT id FROM notifications
      WHERE type = 'reminder' AND related_id = ? AND related_type = 'salary'
      AND data LIKE ? AND date(scheduled_at) = ?
    `, [salary.id, `%"threshold":${threshold}%`, today]);
    
    if (existingNotification) {
      return;
    }
    
    // Bildirim içeriğini hazırla
    let title = '';
    let message = '';
    
    if (threshold === 3) {
      title = 'Maaş Hatırlatması';
      message = `"${salary.name}" maaşınızın yatmasına 3 gün kaldı.`;
    } else if (threshold === 1) {
      title = 'Maaş Hatırlatması';
      message = `"${salary.name}" maaşınızın yatmasına 1 gün kaldı.`;
    } else if (threshold === 0) {
      title = 'Maaş Günü!';
      message = `"${salary.name}" maaşınızın yatacağı gün bugün!`;
    }
    
    // Bildirimi oluştur
    await notificationDbService.createNotification(db, {
      title,
      message,
      type: 'reminder',
      priority: 'normal',
      related_id: salary.id,
      related_type: 'salary',
      data: {
        salaryId: salary.id,
        salaryName: salary.name,
        threshold,
        daysUntilPayday,
        payday: salary.payment_day
      }
    });
  } catch (error) {
    console.error('Maaş hatırlatma bildirimi oluşturma hatası:', error);
  }
};

/**
 * Mesai ödemesi hatırlatma bildirimlerini oluşturur
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} overtime - Mesai
 * @returns {Promise<void>}
 */
export const createOvertimePaymentReminderNotification = async (db, overtime) => {
  try {
    // Bildirim ayarlarını kontrol et
    const settings = await notificationDbService.getNotificationSettings(db, 'reminder');
    
    if (!settings || !settings.is_enabled) {
      return;
    }
    
    // Mesai ödenmemiş mi kontrol et
    if (overtime.is_paid) {
      return;
    }
    
    // Mesai tarihi üzerinden 7 gün geçmiş mi kontrol et
    const overtimeDate = parseISO(overtime.date);
    const today = new Date();
    const daysSinceOvertime = differenceInDays(today, overtimeDate);
    
    if (daysSinceOvertime < 7) {
      return;
    }
    
    // Daha önce bu mesai için bildirim gönderilmiş mi kontrol et
    const existingNotification = await db.getFirstAsync(`
      SELECT id FROM notifications
      WHERE type = 'reminder' AND related_id = ? AND related_type = 'overtime'
    `, [overtime.id]);
    
    if (existingNotification) {
      return;
    }
    
    // Bildirim içeriğini hazırla
    const title = 'Mesai Ödemesi Hatırlatması';
    const message = `"${overtime.title}" mesainizin ödemesi henüz yapılmadı. ${daysSinceOvertime} gün geçti.`;
    
    // Bildirimi oluştur
    await notificationDbService.createNotification(db, {
      title,
      message,
      type: 'reminder',
      priority: 'normal',
      related_id: overtime.id,
      related_type: 'overtime',
      data: {
        overtimeId: overtime.id,
        overtimeTitle: overtime.title,
        daysSinceOvertime,
        overtimeDate: overtime.date,
        amount: overtime.duration * overtime.hourly_rate,
        currency: overtime.currency
      }
    });
  } catch (error) {
    console.error('Mesai ödemesi hatırlatma bildirimi oluşturma hatası:', error);
  }
};

/**
 * Günlük özet bildirimlerini oluşturur
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<void>}
 */
export const createDailySummaryNotification = async (db) => {
  try {
    // Bildirim ayarlarını kontrol et
    const settings = await notificationDbService.getNotificationSettings(db, 'system');
    
    if (!settings || !settings.is_enabled) {
      return;
    }
    
    // Bugünün tarihini al
    const today = format(new Date(), 'yyyy-MM-dd');
    
    // Daha önce bugün için özet bildirimi gönderilmiş mi kontrol et
    const existingNotification = await db.getFirstAsync(`
      SELECT id FROM notifications
      WHERE type = 'system' AND related_type = 'daily_summary'
      AND date(scheduled_at) = ?
    `, [today]);
    
    if (existingNotification) {
      return;
    }
    
    // Bugünkü işlemleri getir
    const transactions = await db.getAllAsync(`
      SELECT type, SUM(amount) as total_amount
      FROM transactions
      WHERE date = ?
      GROUP BY type
    `, [today]);
    
    // Bugünkü toplam gelir ve gideri hesapla
    let totalIncome = 0;
    let totalExpense = 0;
    
    transactions.forEach(transaction => {
      if (transaction.type === 'income') {
        totalIncome += transaction.total_amount;
      } else if (transaction.type === 'expense') {
        totalExpense += transaction.total_amount;
      }
    });
    
    // Bugün için planlanmış hatırlatmaları getir
    const reminders = await db.getAllAsync(`
      SELECT COUNT(*) as count
      FROM notifications
      WHERE type = 'reminder' AND date(scheduled_at) = ?
    `, [today]);
    
    const reminderCount = reminders[0]?.count || 0;
    
    // Bildirim içeriğini hazırla
    const title = 'Günlük Finansal Özet';
    let message = `Bugün: ${totalIncome.toFixed(2)} TL gelir, ${totalExpense.toFixed(2)} TL gider.`;
    
    if (reminderCount > 0) {
      message += ` ${reminderCount} hatırlatmanız var.`;
    }
    
    // Bildirimi oluştur
    await notificationDbService.createNotification(db, {
      title,
      message,
      type: 'system',
      priority: 'normal',
      related_type: 'daily_summary',
      data: {
        date: today,
        totalIncome,
        totalExpense,
        reminderCount
      },
      scheduled_at: format(new Date().setHours(20, 0, 0, 0), "yyyy-MM-dd'T'HH:mm:ss")
    });
  } catch (error) {
    console.error('Günlük özet bildirimi oluşturma hatası:', error);
  }
};

/**
 * Haftalık özet bildirimlerini oluşturur
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<void>}
 */
export const createWeeklySummaryNotification = async (db) => {
  try {
    // Bildirim ayarlarını kontrol et
    const settings = await notificationDbService.getNotificationSettings(db, 'system');
    
    if (!settings || !settings.is_enabled) {
      return;
    }
    
    // Bugün pazar günü mü kontrol et (0: Pazar, 1: Pazartesi, ...)
    const today = new Date();
    
    if (today.getDay() !== 0) {
      return;
    }
    
    // Bugünün tarihini al
    const todayStr = format(today, 'yyyy-MM-dd');
    
    // Daha önce bugün için haftalık özet bildirimi gönderilmiş mi kontrol et
    const existingNotification = await db.getFirstAsync(`
      SELECT id FROM notifications
      WHERE type = 'system' AND related_type = 'weekly_summary'
      AND date(scheduled_at) = ?
    `, [todayStr]);
    
    if (existingNotification) {
      return;
    }
    
    // Hafta başlangıcını hesapla (pazartesi)
    const startOfWeek = format(addDays(today, -6), 'yyyy-MM-dd');
    
    // Haftalık işlemleri getir
    const transactions = await db.getAllAsync(`
      SELECT type, SUM(amount) as total_amount
      FROM transactions
      WHERE date >= ? AND date <= ?
      GROUP BY type
    `, [startOfWeek, todayStr]);
    
    // Haftalık toplam gelir ve gideri hesapla
    let totalIncome = 0;
    let totalExpense = 0;
    
    transactions.forEach(transaction => {
      if (transaction.type === 'income') {
        totalIncome += transaction.total_amount;
      } else if (transaction.type === 'expense') {
        totalExpense += transaction.total_amount;
      }
    });
    
    // En çok harcama yapılan kategorileri getir
    const topCategories = await db.getAllAsync(`
      SELECT c.name, SUM(t.amount) as total_amount
      FROM transactions t
      JOIN categories c ON t.category_id = c.id
      WHERE t.type = 'expense' AND t.date >= ? AND t.date <= ?
      GROUP BY t.category_id
      ORDER BY total_amount DESC
      LIMIT 3
    `, [startOfWeek, todayStr]);
    
    // Bildirim içeriğini hazırla
    const title = 'Haftalık Finansal Özet';
    let message = `Bu hafta: ${totalIncome.toFixed(2)} TL gelir, ${totalExpense.toFixed(2)} TL gider.`;
    
    if (topCategories.length > 0) {
      message += ` En çok: ${topCategories[0].name}.`;
    }
    
    // Bildirimi oluştur
    await notificationDbService.createNotification(db, {
      title,
      message,
      type: 'system',
      priority: 'normal',
      related_type: 'weekly_summary',
      data: {
        startDate: startOfWeek,
        endDate: todayStr,
        totalIncome,
        totalExpense,
        topCategories: topCategories.map(c => ({
          name: c.name,
          amount: c.total_amount
        }))
      },
      scheduled_at: format(new Date().setHours(18, 0, 0, 0), "yyyy-MM-dd'T'HH:mm:ss")
    });
  } catch (error) {
    console.error('Haftalık özet bildirimi oluşturma hatası:', error);
  }
};

/**
 * Aylık özet bildirimlerini oluşturur
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<void>}
 */
export const createMonthlySummaryNotification = async (db) => {
  try {
    // Bildirim ayarlarını kontrol et
    const settings = await notificationDbService.getNotificationSettings(db, 'system');
    
    if (!settings || !settings.is_enabled) {
      return;
    }
    
    // Bugün ayın son günü mü kontrol et
    const today = new Date();
    const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0).getDate();
    
    if (today.getDate() !== lastDayOfMonth) {
      return;
    }
    
    // Bugünün tarihini al
    const todayStr = format(today, 'yyyy-MM-dd');
    
    // Daha önce bugün için aylık özet bildirimi gönderilmiş mi kontrol et
    const existingNotification = await db.getFirstAsync(`
      SELECT id FROM notifications
      WHERE type = 'system' AND related_type = 'monthly_summary'
      AND date(scheduled_at) = ?
    `, [todayStr]);
    
    if (existingNotification) {
      return;
    }
    
    // Ay başlangıcını hesapla
    const startOfMonth = format(new Date(today.getFullYear(), today.getMonth(), 1), 'yyyy-MM-dd');
    
    // Aylık işlemleri getir
    const transactions = await db.getAllAsync(`
      SELECT type, SUM(amount) as total_amount
      FROM transactions
      WHERE date >= ? AND date <= ?
      GROUP BY type
    `, [startOfMonth, todayStr]);
    
    // Aylık toplam gelir ve gideri hesapla
    let totalIncome = 0;
    let totalExpense = 0;
    
    transactions.forEach(transaction => {
      if (transaction.type === 'income') {
        totalIncome += transaction.total_amount;
      } else if (transaction.type === 'expense') {
        totalExpense += transaction.total_amount;
      }
    });
    
    // En çok harcama yapılan kategorileri getir
    const topCategories = await db.getAllAsync(`
      SELECT c.name, SUM(t.amount) as total_amount
      FROM transactions t
      JOIN categories c ON t.category_id = c.id
      WHERE t.type = 'expense' AND t.date >= ? AND t.date <= ?
      GROUP BY t.category_id
      ORDER BY total_amount DESC
      LIMIT 3
    `, [startOfMonth, todayStr]);
    
    // Bildirim içeriğini hazırla
    const title = 'Aylık Finansal Özet';
    let message = `Bu ay: ${totalIncome.toFixed(2)} TL gelir, ${totalExpense.toFixed(2)} TL gider.`;
    
    if (topCategories.length > 0) {
      message += ` En çok: ${topCategories.map(c => c.name).join(', ')}.`;
    }
    
    // Bildirimi oluştur
    await notificationDbService.createNotification(db, {
      title,
      message,
      type: 'system',
      priority: 'normal',
      related_type: 'monthly_summary',
      data: {
        startDate: startOfMonth,
        endDate: todayStr,
        totalIncome,
        totalExpense,
        topCategories: topCategories.map(c => ({
          name: c.name,
          amount: c.total_amount
        }))
      },
      scheduled_at: format(new Date().setHours(20, 0, 0, 0), "yyyy-MM-dd'T'HH:mm:ss")
    });
  } catch (error) {
    console.error('Aylık özet bildirimi oluşturma hatası:', error);
  }
};
