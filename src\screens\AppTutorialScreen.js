import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Image
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import { useTheme } from '../context/ThemeContext';
import { useAppContext } from '../context/AppContext';

const { width: screenWidth } = Dimensions.get('window');

/**
 * Uygulama Kullanım Rehberi Ekranı
 * Uygulamanın nasıl kullanılacağını anlatan interaktif rehber
 */
export default function AppTutorialScreen({ navigation }) {
  const insets = useSafeAreaInsets();
  const { theme } = useTheme();
  const { defaultCurrency } = useAppContext();
  const scrollViewRef = useRef(null);
  const [currentPage, setCurrentPage] = useState(0);

  // Tutorial sayfaları
  const tutorialPages = [
    {
      id: 1,
      title: 'Hoş Geldiniz!',
      subtitle: 'Finansal Takip Uygulamasına',
      description: 'Kişisel finans yönetiminizi kolaylaştıracak kapsamlı bir uygulama. Gelir, gider, maaş, mesai ve daha fazlasını takip edin.',
      icon: 'account-balance-wallet',
      color: Colors.PRIMARY,
      features: [
        'Gelir/Gider Takibi',
        'Maaş Yönetimi',
        'Mesai Takibi',
        'Bütçe Planlama'
      ]
    },
    {
      id: 2,
      title: 'Hızlı İşlem',
      subtitle: 'Anında Gelir/Gider Ekleyin',
      description: 'Ana sayfadaki hızlı işlem alanından sadece tutar yazarak anında işlem ekleyebilirsiniz. Örnek: "55 TL" yazın.',
      icon: 'flash-on',
      color: Colors.SUCCESS,
      features: [
        'Sadece tutar yazın',
        'Otomatik kategori seçimi',
        'Döviz desteği',
        'Anında kaydetme'
      ]
    },
    {
      id: 3,
      title: 'Maaş Takibi',
      subtitle: 'Aylık Gelirlerinizi Yönetin',
      description: 'Maaşınızı, primlerinizi ve ek ödemelerinizi takip edin. Otomatik hatırlatıcılar ile maaş günlerinizi kaçırmayın.',
      icon: 'work',
      color: Colors.INFO,
      features: [
        'Aylık maaş takibi',
        'Prim ve ek ödemeler',
        'Otomatik hatırlatıcılar',
        'Döviz karşılıkları'
      ]
    },
    {
      id: 4,
      title: 'Mesai Takibi',
      subtitle: 'Çalışma Saatlerinizi Kaydedin',
      description: 'Mesai saatlerinizi, overtime çalışmalarınızı ve vardiya planlarınızı detaylı şekilde takip edin.',
      icon: 'schedule',
      color: Colors.WARNING,
      features: [
        'Mesai saati takibi',
        'Overtime hesaplama',
        'Vardiya planlama',
        'Kazanç hesaplama'
      ]
    },
    {
      id: 5,
      title: 'Güvenlik',
      subtitle: 'Verilerinizi Koruyun',
      description: 'PIN kodu ve biometrik kimlik doğrulama ile verilerinizi güvende tutun. Tüm veriler cihazınızda saklanır.',
      icon: 'security',
      color: Colors.DANGER,
      features: [
        'PIN koruması',
        'Biometrik giriş',
        'Yerel veri saklama',
        'Otomatik kilit'
      ]
    },
    {
      id: 6,
      title: 'Başlayalım!',
      subtitle: 'Artık Hazırsınız',
      description: 'Tüm özellikler hazır! Finansal hedeflerinize ulaşmak için uygulamayı kullanmaya başlayabilirsiniz.',
      icon: 'rocket-launch',
      color: Colors.SUCCESS,
      features: [
        'Hemen başlayın',
        'Kolay kullanım',
        'Detaylı raporlar',
        'Sürekli geliştirme'
      ]
    }
  ];

  // Sayfa değiştirme
  const goToPage = (pageIndex) => {
    setCurrentPage(pageIndex);
    scrollViewRef.current?.scrollTo({
      x: pageIndex * screenWidth,
      animated: true
    });
  };

  // Sonraki sayfa
  const nextPage = () => {
    if (currentPage < tutorialPages.length - 1) {
      goToPage(currentPage + 1);
    } else {
      // Tutorial tamamlandı
      navigation.goBack();
    }
  };

  // Önceki sayfa
  const prevPage = () => {
    if (currentPage > 0) {
      goToPage(currentPage - 1);
    }
  };

  // Scroll event
  const handleScroll = (event) => {
    const pageIndex = Math.round(event.nativeEvent.contentOffset.x / screenWidth);
    setCurrentPage(pageIndex);
  };

  return (
    <View style={[styles.container, { paddingTop: insets.top, backgroundColor: theme.BACKGROUND }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.PRIMARY }]}>
        <TouchableOpacity
          style={styles.closeButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="close" size={24} color={theme.WHITE} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.WHITE }]}>Kullanım Rehberi</Text>
        <TouchableOpacity
          style={styles.skipButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={[styles.skipText, { color: theme.WHITE }]}>Atla</Text>
        </TouchableOpacity>
      </View>

      {/* Tutorial Content */}
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        style={styles.scrollView}
      >
        {tutorialPages.map((page, index) => (
          <View key={page.id} style={[styles.page, { width: screenWidth }]}>
            <View style={styles.pageContent}>
              {/* Icon */}
              <View style={[styles.iconContainer, { backgroundColor: `${page.color}20` }]}>
                <MaterialIcons name={page.icon} size={64} color={page.color} />
              </View>

              {/* Title */}
              <Text style={[styles.pageTitle, { color: theme.TEXT_PRIMARY }]}>
                {page.title}
              </Text>
              <Text style={[styles.pageSubtitle, { color: page.color }]}>
                {page.subtitle}
              </Text>

              {/* Description */}
              <Text style={[styles.pageDescription, { color: theme.TEXT_SECONDARY }]}>
                {page.description}
              </Text>

              {/* Features */}
              <View style={styles.featuresContainer}>
                {page.features.map((feature, featureIndex) => (
                  <View key={featureIndex} style={styles.featureItem}>
                    <MaterialIcons name="check-circle" size={20} color={page.color} />
                    <Text style={[styles.featureText, { color: theme.TEXT_PRIMARY }]}>
                      {feature}
                    </Text>
                  </View>
                ))}
              </View>
            </View>
          </View>
        ))}
      </ScrollView>

      {/* Page Indicators */}
      <View style={styles.indicatorContainer}>
        {tutorialPages.map((_, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.indicator,
              {
                backgroundColor: index === currentPage ? theme.PRIMARY : theme.BORDER,
                width: index === currentPage ? 24 : 8,
              }
            ]}
            onPress={() => goToPage(index)}
          />
        ))}
      </View>

      {/* Navigation Buttons */}
      <View style={styles.navigationContainer}>
        <TouchableOpacity
          style={[
            styles.navButton,
            styles.prevButton,
            { 
              backgroundColor: currentPage === 0 ? theme.BORDER : theme.SURFACE,
              opacity: currentPage === 0 ? 0.5 : 1
            }
          ]}
          onPress={prevPage}
          disabled={currentPage === 0}
        >
          <MaterialIcons name="arrow-back" size={24} color={theme.TEXT_PRIMARY} />
          <Text style={[styles.navButtonText, { color: theme.TEXT_PRIMARY }]}>Önceki</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.navButton, styles.nextButton, { backgroundColor: theme.PRIMARY }]}
          onPress={nextPage}
        >
          <Text style={[styles.navButtonText, { color: theme.WHITE }]}>
            {currentPage === tutorialPages.length - 1 ? 'Başla' : 'Sonraki'}
          </Text>
          <MaterialIcons 
            name={currentPage === tutorialPages.length - 1 ? 'check' : 'arrow-forward'} 
            size={24} 
            color={theme.WHITE} 
          />
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  closeButton: {
    padding: 8,
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  skipButton: {
    padding: 8,
  },
  skipText: {
    fontSize: 16,
    fontWeight: '500',
  },
  scrollView: {
    flex: 1,
  },
  page: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  pageContent: {
    alignItems: 'center',
    maxWidth: 320,
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 32,
  },
  pageTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  pageSubtitle: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 24,
  },
  pageDescription: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  featuresContainer: {
    width: '100%',
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  featureText: {
    fontSize: 16,
    marginLeft: 12,
    flex: 1,
  },
  indicatorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
  },
  indicator: {
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  navigationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  navButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 25,
    minWidth: 120,
    justifyContent: 'center',
  },
  prevButton: {
    borderWidth: 1,
    // Border color set dynamically with theme
  },
  nextButton: {
    // Primary color background
  },
  navButtonText: {
    fontSize: 16,
    fontWeight: '600',
    marginHorizontal: 8,
  },
});
