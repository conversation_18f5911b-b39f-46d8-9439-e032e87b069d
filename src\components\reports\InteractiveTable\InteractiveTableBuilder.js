import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { useTheme } from '../../../context/ThemeContext';
import { useNavigation } from '@react-navigation/native';

// Modular components
import TableBuilderHeader from './components/TableBuilderHeader';
import DataSourceSelector from './components/DataSourceSelector';
import ColumnManager from './components/ColumnManager';
import FilterManager from './components/FilterManager';
import TablePreview from './components/TablePreview';
import TableControls from './components/TableControls';
import PivotTableBuilder from './components/PivotTableBuilder';
import PivotTableView from './components/PivotTableView';
import TableColumnManager from './components/TableColumnManager';
import TableFilterManager from './components/TableFilterManager';
import TableFormulaBuilder from './components/TableFormulaBuilder';

// Hooks
import { useTableData } from './hooks/useTableData';
import { useTableColumns } from './hooks/useTableColumns';
import { useTableFilters } from './hooks/useTableFilters';
import { usePivotTable } from './hooks/usePivotTable';

/**
 * İnteraktif Tablo Oluşturucu - Modüler Yapı
 * Kullanıcı kendi tablolarını oluşturur
 * Sürükle-bırak, filtreleme, formül desteği
 */
const InteractiveTableBuilder = () => {
  const { theme } = useTheme();
  const navigation = useNavigation();
  
  // Theme kontrolü
  if (!theme) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text>Theme yükleniyor...</Text>
      </View>
    );
  }
  
  // State management
  const [currentStep, setCurrentStep] = useState(1);
  const [isPreviewVisible, setIsPreviewVisible] = useState(false);
  const [showColumnManager, setShowColumnManager] = useState(false);
  const [showFilterManager, setShowFilterManager] = useState(false);
  const [tableMode, setTableMode] = useState('standard'); // 'standard' | 'pivot'
  const [showPivotBuilder, setShowPivotBuilder] = useState(false);
  const [showAdvancedColumnManager, setShowAdvancedColumnManager] = useState(false);
  const [showAdvancedFilterManager, setShowAdvancedFilterManager] = useState(false);
  const [showFormulaBuilder, setShowFormulaBuilder] = useState(false);
  
  // Custom hooks
  const {
    tableData,
    loading,
    error,
    refreshData,
    setDataSources,
    setDateRange,
    dataSources,
    dateRange,
  } = useTableData();
  
  const {
    columns,
    addColumn,
    removeColumn,
    updateColumn,
    reorderColumns,
    resetColumns,
    canUndo: canUndoColumns,
    canRedo: canRedoColumns,
    undo: undoColumns,
    redo: redoColumns,
  } = useTableColumns();
  
  const {
    filters,
    addFilter,
    removeFilter,
    updateFilter,
    clearFilters,
    applyFilters,
    canUndo: canUndoFilters,
    canRedo: canRedoFilters,
    undo: undoFilters,
    redo: redoFilters,
  } = useTableFilters();

  // Pivot Table Hook
  const {
    config: pivotConfig,
    pivotData,
    isLoading: pivotLoading,
    error: pivotError,
    updateConfig: updatePivotConfig,
    resetConfig: resetPivotConfig,
    exportToCSV: exportPivotToCSV,
    prepareForExcel: preparePivotForExcel,
  } = usePivotTable(filteredData);

  // Filtered data based on current filters
  let filteredData = [];
  try {
    if (typeof applyFilters === 'function' && tableData) {
      filteredData = applyFilters(tableData);
    } else {
      // Filtre uygulama hatası
      filteredData = tableData || [];
    }
  } catch (error) {
    // Filtre uygulama hatası
    filteredData = tableData || [];
  }

  /**
   * Tablo kaydetme işlemi
   */
  const handleSaveTable = async (tableName, description) => {
    try {
      const tableConfig = {
        name: tableName,
        description,
        dataSources,
        dateRange,
        columns,
        filters,
        createdAt: new Date().toISOString(),
      };
      
      // TODO: Implement save to database
      
      // Navigate back on successful save
      navigation.goBack();
    } catch (error) {
      // Tablo kaydetme hatası
    }
  };

  /**
   * Tablo dışa aktarma işlemi
   */
  const handleExportTable = async (format) => {
    try {
      const exportData = {
        tableData: filteredData,
        columns,
        filters,
        metadata: {
          generatedAt: new Date().toISOString(),
          totalRows: filteredData.length,
          dataSources,
          dateRange,
        },
      };
      
      // TODO: Implement export service
    } catch (error) {
      // Tablo dışa aktarma hatası
    }
  };

  /**
   * Geri alma işlemi
   */
  const handleUndo = () => {
    if (canUndoColumns) {
      undoColumns();
    } else if (canUndoFilters) {
      undoFilters();
    }
  };

  /**
   * Yeniden yapma işlemi
   */
  const handleRedo = () => {
    if (canRedoColumns) {
      redoColumns();
    } else if (canRedoFilters) {
      redoFilters();
    }
  };

  /**
   * Tablo temizleme işlemi
   */
  const handleClearTable = () => {
    resetColumns();
    clearFilters();
    setCurrentStep(1);
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      <StatusBar barStyle={theme.STATUS_BAR_STYLE} backgroundColor={theme.STATUS_BAR_COLOR} />
      
      {/* Header */}
      <TableBuilderHeader
        title="📊 İnteraktif Tablo Oluşturucu"
        currentStep={currentStep}
        totalSteps={4}
        onClose={() => navigation.goBack()}
      />

      {/* Table Mode Selector */}
      <View style={[styles.modeSelector, { backgroundColor: theme.SURFACE }]}>
        <TouchableOpacity
          style={[
            styles.modeButton,
            { backgroundColor: tableMode === 'standard' ? theme.PRIMARY : 'transparent' }
          ]}
          onPress={() => setTableMode('standard')}
        >
          <Text style={[
            styles.modeButtonText,
            { color: tableMode === 'standard' ? theme.SURFACE : theme.TEXT_PRIMARY }
          ]}>
            📊 Standart Tablo
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.modeButton,
            { backgroundColor: tableMode === 'pivot' ? theme.PRIMARY : 'transparent' }
          ]}
          onPress={() => setTableMode('pivot')}
        >
          <Text style={[
            styles.modeButtonText,
            { color: tableMode === 'pivot' ? theme.SURFACE : theme.TEXT_PRIMARY }
          ]}>
            🔄 Pivot Tablo
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Step 1: Veri Kaynağı Seçimi */}
        {currentStep === 1 && (
          <DataSourceSelector
            dataSources={dataSources}
            dateRange={dateRange}
            onDataSourcesChange={setDataSources}
            onDateRangeChange={setDateRange}
            onNext={() => setCurrentStep(2)}
          />
        )}

        {/* Step 2: Sütun/Pivot Yapılandırması */}
        {currentStep === 2 && (
          <View style={[styles.section, { backgroundColor: theme.SURFACE || theme.colors?.surface || '#f9f9f9' }]}>
            {tableMode === 'standard' ? (
              <>
                <ColumnManager
                  isVisible={showColumnManager}
                  columns={columns}
                  onAddColumn={addColumn}
                  onRemoveColumn={removeColumn}
                  onUpdateColumn={updateColumn}
                  onReorderColumns={reorderColumns}
                  onClose={() => setShowColumnManager(false)}
                />

                <View style={styles.stepActions}>
                  <TouchableOpacity
                    style={[styles.stepButton, { backgroundColor: theme.PRIMARY || theme.colors?.primary || '#007AFF' }]}
                    onPress={() => setShowColumnManager(true)}
                  >
                    <Text style={[styles.stepButtonText, { color: theme.SURFACE || theme.colors?.surface || '#f9f9f9' }]}>
                      ⚙️ Sütunları Yönet
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[styles.stepButton, { backgroundColor: theme.SUCCESS || theme.colors?.success || '#28a745' }]}
                    onPress={() => setCurrentStep(3)}
                  >
                    <Text style={[styles.stepButtonText, { color: theme.SURFACE || theme.colors?.surface || '#f9f9f9' }]}>
                      İleri: Filtreler
                    </Text>
                  </TouchableOpacity>
                </View>
              </>
            ) : (
              <>
                <PivotTableBuilder
                  data={filteredData}
                  onPivotChange={updatePivotConfig}
                  initialConfig={pivotConfig}
                />

                <View style={styles.stepActions}>
                  <TouchableOpacity
                    style={[styles.stepButton, { backgroundColor: theme.WARNING || theme.colors?.warning || '#ffc107' }]}
                    onPress={resetPivotConfig}
                  >
                    <Text style={[styles.stepButtonText, { color: theme.SURFACE || theme.colors?.surface || '#f9f9f9' }]}>
                      🔄 Sıfırla
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[styles.stepButton, { backgroundColor: theme.SUCCESS || theme.colors?.success || '#28a745' }]}
                    onPress={() => setCurrentStep(3)}
                  >
                    <Text style={[styles.stepButtonText, { color: theme.SURFACE || theme.colors?.surface || '#f9f9f9' }]}>
                      İleri: Filtreler
                    </Text>
                  </TouchableOpacity>
                </View>
              </>
            )}
          </View>
        )}

        {/* Step 3: Filtre Yapılandırması */}
        {currentStep === 3 && (
          <View style={[styles.section, { backgroundColor: theme.SURFACE || theme.colors?.surface || '#f9f9f9' }]}>
            <FilterManager
              isVisible={showFilterManager}
              filters={filters}
              columns={columns}
              onAddFilter={addFilter}
              onRemoveFilter={removeFilter}
              onUpdateFilter={updateFilter}
              onClose={() => setShowFilterManager(false)}
            />
            
            <View style={styles.stepActions}>
              <TouchableOpacity
                style={[styles.stepButton, { backgroundColor: theme.PRIMARY || theme.colors?.primary || '#007AFF' }]}
                onPress={() => setShowFilterManager(true)}
              >
                <Text style={[styles.stepButtonText, { color: theme.SURFACE || theme.colors?.surface || '#f9f9f9' }]}>
                  🔍 Filtreleri Yönet
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.stepButton, { backgroundColor: theme.SUCCESS || theme.colors?.success || '#28a745' }]}
                onPress={() => setCurrentStep(4)}
              >
                <Text style={[styles.stepButtonText, { color: theme.SURFACE || theme.colors?.surface || '#f9f9f9' }]}>
                  İleri: Önizleme
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        {/* Step 4: Önizleme ve Kontroller */}
        {currentStep === 4 && (
          <>
            {tableMode === 'standard' ? (
              <TablePreview
                data={filteredData}
                columns={columns}
                loading={loading}
                error={error}
                isVisible={isPreviewVisible}
                onToggleVisibility={() => setIsPreviewVisible(!isPreviewVisible)}
              />
            ) : (
              <PivotTableView
                pivotData={pivotData}
                onExport={(format) => {
                  if (format === 'csv') {
                    return exportPivotToCSV();
                  } else if (format === 'excel') {
                    return preparePivotForExcel();
                  }
                }}
                showSummary={true}
                maxHeight={400}
              />
            )}

            <TableControls
              onSave={handleSaveTable}
              onExport={tableMode === 'pivot' ?
                (format) => {
                  if (format === 'csv') {
                    return exportPivotToCSV();
                  } else if (format === 'excel') {
                    return preparePivotForExcel();
                  }
                } :
                handleExportTable
              }
              onUndo={handleUndo}
              onRedo={handleRedo}
              onClear={tableMode === 'pivot' ? resetPivotConfig : handleClearTable}
              canUndo={canUndoColumns || canUndoFilters}
              canRedo={canRedoColumns || canRedoFilters}
              dataCount={filteredData.length}
              onOpenAdvancedColumnManager={() => setShowAdvancedColumnManager(true)}
              onOpenAdvancedFilterManager={() => setShowAdvancedFilterManager(true)}
              onOpenFormulaBuilder={() => setShowFormulaBuilder(true)}
            />
          </>
        )}
      </ScrollView>

      {/* Advanced Column Manager Modal */}
      <TableColumnManager
        visible={showAdvancedColumnManager}
        onClose={() => setShowAdvancedColumnManager(false)}
        columns={columns}
        onAddColumn={addColumn}
        onUpdateColumn={updateColumn}
        onRemoveColumn={removeColumn}
        onReorderColumns={reorderColumns}
        availableDataSources={[
          { field: 'amount', label: 'Tutar' },
          { field: 'date', label: 'Tarih' },
          { field: 'category', label: 'Kategori' },
          { field: 'description', label: 'Açıklama' },
        ]}
        theme={theme}
      />

      {/* Advanced Filter Manager Modal */}
      <TableFilterManager
        visible={showAdvancedFilterManager}
        onClose={() => setShowAdvancedFilterManager(false)}
        filters={filters}
        onAddFilter={addFilter}
        onUpdateFilter={updateFilter}
        onRemoveFilter={removeFilter}
        onClearFilters={clearFilters}
        availableColumns={columns}
        theme={theme}
      />

      {/* Formula Builder Modal */}
      <TableFormulaBuilder
        visible={showFormulaBuilder}
        onClose={() => setShowFormulaBuilder(false)}
        onSave={(formula) => {
          // Handle formula save
          console.log('Formula saved:', formula);
          setShowFormulaBuilder(false);
        }}
        initialFormula=""
        availableColumns={columns}
        theme={theme}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    margin: 16,
    padding: 16,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  modeSelector: {
    flexDirection: 'row',
    margin: 16,
    padding: 4,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  modeButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 2,
  },
  modeButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  stepActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
    gap: 12,
  },
  stepButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  stepButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
});

export default InteractiveTableBuilder;
