import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
  TextInput,
  Modal
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import { formatCurrency } from '../utils/formatters';
import { createSavingsGoalsTable } from '../db/createSavingsGoalsTable';

/**
 * <PERSON><PERSON><PERSON> Hedefi Detay Ekranı
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @param {Object} props.route - Route nesnesi
 * @returns {JSX.Element} Birikim Hedefi Detay Ekranı
 */
export default function SavingsGoalDetail({ navigation, route }) {
  const { goalId } = route.params || {};
  const db = useSQLiteContext();

  // Durum değişkenleri
  const [loading, setLoading] = useState(true);
  const [goal, setGoal] = useState(null);
  const [showDepositModal, setShowDepositModal] = useState(false);
  const [depositAmount, setDepositAmount] = useState('');
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);
  const [withdrawAmount, setWithdrawAmount] = useState('');

  // Hedefi yükle
  const loadGoal = useCallback(async () => {
    if (!goalId) {
      navigation.goBack();
      return;
    }

    try {
      setLoading(true);

      // Önce tabloyu oluştur (eğer yoksa)
      await createSavingsGoalsTable(db);

      const goalData = await db.getFirstAsync(`
        SELECT * FROM savings_goals
        WHERE id = ?
      `, [goalId]);

      if (!goalData) {
        Alert.alert('Hata', 'Birikim hedefi bulunamadı.');
        navigation.goBack();
        return;
      }

      setGoal(goalData);
      setLoading(false);
    } catch (error) {
      console.error('Birikim hedefi yükleme hatası:', error);
      Alert.alert('Hata', 'Birikim hedefi yüklenirken bir hata oluştu.');
      setLoading(false);
      navigation.goBack();
    }
  }, [goalId, db, navigation]);

  // Ekran odaklandığında hedefi yükle
  useFocusEffect(
    useCallback(() => {
      loadGoal();
    }, [loadGoal])
  );

  // Hedefi düzenle
  const editGoal = () => {
    navigation.navigate('SavingsGoalForm', { goalId });
  };

  // Para yatır
  const handleDeposit = async () => {
    if (!depositAmount || isNaN(parseFloat(depositAmount)) || parseFloat(depositAmount) <= 0) {
      Alert.alert('Hata', 'Lütfen geçerli bir tutar girin.');
      return;
    }

    const amount = parseFloat(depositAmount);
    const newAmount = goal.current_amount + amount;

    try {
      // Önce tabloyu oluştur (eğer yoksa)
      await createSavingsGoalsTable(db);

      await db.runAsync(`
        UPDATE savings_goals
        SET current_amount = ?,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `, [newAmount, goalId]);

      setShowDepositModal(false);
      setDepositAmount('');

      // Transactions tablosunda currency sütunu var mı kontrol et
      const columns = await db.getAllAsync(`PRAGMA table_info(transactions)`);
      const columnNames = columns.map(col => col.name);

      // İşlem kaydı ekle
      if (columnNames.includes('currency')) {
        await db.runAsync(`
          INSERT INTO transactions (
            type, amount, description, date, category_id, currency
          )
          VALUES (?, ?, ?, ?, ?, ?)
        `, [
          'expense',
          amount,
          `${goal.name} hedefine para yatırma`,
          new Date().toISOString().split('T')[0],
          null,
          goal.currency
        ]);
      } else {
        await db.runAsync(`
          INSERT INTO transactions (
            type, amount, description, date, category_id
          )
          VALUES (?, ?, ?, ?, ?)
        `, [
          'expense',
          amount,
          `${goal.name} hedefine para yatırma`,
          new Date().toISOString().split('T')[0],
          null
        ]);
      }

      Alert.alert('Başarılı', `${formatCurrency(amount, goal.currency)} tutarında para yatırıldı.`);
      loadGoal();
    } catch (error) {
      console.error('Para yatırma hatası:', error);
      Alert.alert('Hata', 'Para yatırılırken bir hata oluştu.');
    }
  };

  // Para çek
  const handleWithdraw = async () => {
    if (!withdrawAmount || isNaN(parseFloat(withdrawAmount)) || parseFloat(withdrawAmount) <= 0) {
      Alert.alert('Hata', 'Lütfen geçerli bir tutar girin.');
      return;
    }

    const amount = parseFloat(withdrawAmount);

    if (amount > goal.current_amount) {
      Alert.alert('Hata', 'Çekilecek tutar mevcut birikimden büyük olamaz.');
      return;
    }

    const newAmount = goal.current_amount - amount;

    try {
      // Önce tabloyu oluştur (eğer yoksa)
      await createSavingsGoalsTable(db);

      await db.runAsync(`
        UPDATE savings_goals
        SET current_amount = ?,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `, [newAmount, goalId]);

      setShowWithdrawModal(false);
      setWithdrawAmount('');

      // Transactions tablosunda currency sütunu var mı kontrol et
      const columns = await db.getAllAsync(`PRAGMA table_info(transactions)`);
      const columnNames = columns.map(col => col.name);

      // İşlem kaydı ekle
      if (columnNames.includes('currency')) {
        await db.runAsync(`
          INSERT INTO transactions (
            type, amount, description, date, category_id, currency
          )
          VALUES (?, ?, ?, ?, ?, ?)
        `, [
          'income',
          amount,
          `${goal.name} hedefinden para çekme`,
          new Date().toISOString().split('T')[0],
          null,
          goal.currency
        ]);
      } else {
        await db.runAsync(`
          INSERT INTO transactions (
            type, amount, description, date, category_id
          )
          VALUES (?, ?, ?, ?, ?)
        `, [
          'income',
          amount,
          `${goal.name} hedefinden para çekme`,
          new Date().toISOString().split('T')[0],
          null
        ]);
      }

      Alert.alert('Başarılı', `${formatCurrency(amount, goal.currency)} tutarında para çekildi.`);
      loadGoal();
    } catch (error) {
      console.error('Para çekme hatası:', error);
      Alert.alert('Hata', 'Para çekilirken bir hata oluştu.');
    }
  };

  // Hedefi sil
  const deleteGoal = () => {
    Alert.alert(
      'Birikim Hedefi Sil',
      'Bu birikim hedefini silmek istediğinize emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              // Önce tabloyu oluştur (eğer yoksa)
              await createSavingsGoalsTable(db);

              await db.runAsync('DELETE FROM savings_goals WHERE id = ?', [goalId]);
              Alert.alert('Başarılı', 'Birikim hedefi başarıyla silindi.');
              navigation.goBack();
            } catch (error) {
              console.error('Birikim hedefi silme hatası:', error);
              Alert.alert('Hata', 'Birikim hedefi silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };

  // İlerleme yüzdesini hesapla
  const calculateProgress = (current, target) => {
    if (target <= 0) return 0;
    const progress = (current / target) * 100;
    return Math.min(progress, 100);
  };

  // Tarih formatla
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR', { day: 'numeric', month: 'long', year: 'numeric' });
  };

  // Kalan gün sayısını hesapla
  const calculateRemainingDays = (targetDate) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const target = new Date(targetDate);
    target.setHours(0, 0, 0, 0);

    const diffTime = target - today;
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  // Günlük birikim hedefini hesapla
  const calculateDailyTarget = () => {
    if (!goal) return 0;

    const remainingAmount = goal.target_amount - goal.current_amount;
    if (remainingAmount <= 0) return 0;

    const remainingDays = calculateRemainingDays(goal.target_date);
    if (remainingDays <= 0) return remainingAmount;

    return remainingAmount / remainingDays;
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={styles.loadingText}>Birikim hedefi yükleniyor...</Text>
      </View>
    );
  }

  if (!goal) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <Text style={styles.errorText}>Birikim hedefi bulunamadı.</Text>
      </View>
    );
  }

  const progress = calculateProgress(goal.current_amount, goal.target_amount);
  const remainingDays = calculateRemainingDays(goal.target_date);
  const dailyTarget = calculateDailyTarget();

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{goal.name}</Text>
        <TouchableOpacity
          style={styles.editButton}
          onPress={editGoal}
        >
          <MaterialIcons name="edit" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        {/* Özet Kart */}
        <View style={styles.summaryCard}>
          <View style={styles.amountContainer}>
            <Text style={styles.currentAmountLabel}>Mevcut Birikim</Text>
            <Text style={styles.currentAmount}>
              {formatCurrency(goal.current_amount, goal.currency)}
            </Text>
          </View>

          <View style={styles.targetContainer}>
            <Text style={styles.targetAmountLabel}>Hedef Tutar</Text>
            <Text style={styles.targetAmount}>
              {formatCurrency(goal.target_amount, goal.currency)}
            </Text>
          </View>

          <View style={styles.progressBarContainer}>
            <View style={[styles.progressBar, { width: `${progress}%` }]} />
          </View>

          <Text style={styles.progressText}>
            {`%${Math.round(progress)} tamamlandı`}
          </Text>

          <View style={styles.remainingContainer}>
            <Text style={styles.remainingLabel}>Kalan Tutar</Text>
            <Text style={styles.remainingAmount}>
              {formatCurrency(goal.target_amount - goal.current_amount, goal.currency)}
            </Text>
          </View>
        </View>

        {/* Tarih Bilgileri */}
        <View style={styles.infoCard}>
          <View style={styles.infoRow}>
            <View style={styles.infoItem}>
              <MaterialIcons name="event" size={20} color={Colors.PRIMARY} />
              <Text style={styles.infoLabel}>Hedef Tarihi</Text>
              <Text style={styles.infoValue}>{formatDate(goal.target_date)}</Text>
            </View>

            <View style={styles.infoItem}>
              <MaterialIcons name="hourglass-empty" size={20} color={Colors.PRIMARY} />
              <Text style={styles.infoLabel}>Kalan Süre</Text>
              <Text style={[
                styles.infoValue,
                remainingDays < 0 ? styles.overdue :
                remainingDays < 7 ? styles.urgent :
                remainingDays < 30 ? styles.warning : null
              ]}>
                {remainingDays < 0
                  ? `${Math.abs(remainingDays)} gün gecikti`
                  : remainingDays === 0
                    ? 'Bugün son gün!'
                    : `${remainingDays} gün kaldı`}
              </Text>
            </View>
          </View>

          <View style={styles.infoRow}>
            <View style={styles.infoItem}>
              <MaterialIcons name="trending-up" size={20} color={Colors.PRIMARY} />
              <Text style={styles.infoLabel}>Günlük Hedef</Text>
              <Text style={styles.infoValue}>
                {formatCurrency(dailyTarget, goal.currency)}
              </Text>
            </View>

            <View style={styles.infoItem}>
              <MaterialIcons name="update" size={20} color={Colors.PRIMARY} />
              <Text style={styles.infoLabel}>Son Güncelleme</Text>
              <Text style={styles.infoValue}>
                {formatDate(goal.updated_at)}
              </Text>
            </View>
          </View>
        </View>

        {/* Notlar */}
        {goal.notes ? (
          <View style={styles.notesCard}>
            <Text style={styles.notesTitle}>Notlar</Text>
            <Text style={styles.notesContent}>{goal.notes}</Text>
          </View>
        ) : null}

        {/* İşlem Butonları */}
        <View style={styles.actionsContainer}>
          <TouchableOpacity
            style={[styles.actionButton, styles.depositButton]}
            onPress={() => setShowDepositModal(true)}
          >
            <MaterialIcons name="add" size={20} color="#fff" />
            <Text style={styles.actionButtonText}>Para Yatır</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.withdrawButton]}
            onPress={() => setShowWithdrawModal(true)}
            disabled={goal.current_amount <= 0}
          >
            <MaterialIcons name="remove" size={20} color="#fff" />
            <Text style={styles.actionButtonText}>Para Çek</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.deleteButton]}
            onPress={deleteGoal}
          >
            <MaterialIcons name="delete" size={20} color="#fff" />
            <Text style={styles.actionButtonText}>Hedefi Sil</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Para Yatırma Modal */}
      <Modal
        visible={showDepositModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowDepositModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <Text style={styles.modalTitle}>Para Yatır</Text>

            <Text style={styles.modalLabel}>Yatırılacak Tutar</Text>
            <View style={styles.inputWithIcon}>
              <Text style={styles.inputIcon}>{goal?.currency}</Text>
              <TextInput
                style={styles.modalInput}
                value={depositAmount}
                onChangeText={setDepositAmount}
                keyboardType="numeric"
                placeholder="0.00"
                autoFocus
              />
            </View>

            <View style={styles.modalActions}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => {
                  setShowDepositModal(false);
                  setDepositAmount('');
                }}
              >
                <Text style={styles.cancelButtonText}>İptal</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalButton, styles.confirmButton]}
                onPress={handleDeposit}
              >
                <Text style={styles.confirmButtonText}>Yatır</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Para Çekme Modal */}
      <Modal
        visible={showWithdrawModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowWithdrawModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <Text style={styles.modalTitle}>Para Çek</Text>

            <Text style={styles.modalLabel}>Çekilecek Tutar</Text>
            <View style={styles.inputWithIcon}>
              <Text style={styles.inputIcon}>{goal?.currency}</Text>
              <TextInput
                style={styles.modalInput}
                value={withdrawAmount}
                onChangeText={setWithdrawAmount}
                keyboardType="numeric"
                placeholder="0.00"
                autoFocus
              />
            </View>

            <View style={styles.modalActions}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => {
                  setShowWithdrawModal(false);
                  setWithdrawAmount('');
                }}
              >
                <Text style={styles.cancelButtonText}>İptal</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalButton, styles.confirmButton]}
                onPress={handleWithdraw}
              >
                <Text style={styles.confirmButtonText}>Çek</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  errorText: {
    fontSize: 16,
    color: Colors.DANGER,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.PRIMARY,
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
  editButton: {
    padding: 4,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  summaryCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  amountContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  currentAmountLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  currentAmount: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.PRIMARY,
  },
  targetContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  targetAmountLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  targetAmount: {
    fontSize: 20,
    fontWeight: '500',
    color: '#333',
  },
  progressBarContainer: {
    height: 8,
    backgroundColor: '#f0f0f0',
    borderRadius: 4,
    marginVertical: 12,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: Colors.PRIMARY,
  },
  progressText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 16,
  },
  remainingContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  remainingLabel: {
    fontSize: 14,
    color: '#666',
  },
  remainingAmount: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  infoCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  infoItem: {
    flex: 1,
    alignItems: 'center',
  },
  infoLabel: {
    fontSize: 12,
    color: '#666',
    marginVertical: 4,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    textAlign: 'center',
  },
  overdue: {
    color: Colors.DANGER,
  },
  urgent: {
    color: '#ff9800',
  },
  warning: {
    color: '#ffc107',
  },
  notesCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  notesTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  notesContent: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  depositButton: {
    backgroundColor: Colors.SUCCESS,
  },
  withdrawButton: {
    backgroundColor: Colors.WARNING,
  },
  deleteButton: {
    backgroundColor: Colors.DANGER,
  },
  actionButtonText: {
    color: '#fff',
    fontWeight: '500',
    marginLeft: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '80%',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
  },
  modalLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  modalInput: {
    flex: 1,
    paddingVertical: 8,
    fontSize: 16,
  },
  inputWithIcon: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    marginBottom: 20,
  },
  inputIcon: {
    fontSize: 16,
    color: '#666',
    marginRight: 8,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    paddingVertical: 10,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 4,
  },
  cancelButton: {
    backgroundColor: '#f0f0f0',
  },
  confirmButton: {
    backgroundColor: Colors.PRIMARY,
  },
  cancelButtonText: {
    color: '#333',
    fontWeight: '500',
  },
  confirmButtonText: {
    color: '#fff',
    fontWeight: '500',
  },
});
