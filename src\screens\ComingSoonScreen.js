import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Dimensions,
  Alert
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { useAppContext } from '../context/AppContext';
import { Colors } from '../constants/colors';
import {
  ALL_FEATURES,
  FEATURE_STATUS,
  FEATURE_CATEGORIES,
  getFeaturesByStatus,
  getFeaturesByCategory,
  getProgressStats,
  getStatusColors,
  getStatusLabel
} from '../services/featureStatusService';

const screenWidth = Dimensions.get('window').width;

/**
 * Gelişmiş Coming Soon Ekranı
 * Tüm özellik durumlarını ve ilerlemelerini gösterir
 */
const ComingSoonScreen = ({ navigation }) => {
  const insets = useSafeAreaInsets();
  const { theme } = useAppContext();
  
  const [refreshing, setRefreshing] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState('all'); // 'all', 'coming_soon', 'in_progress', 'planned'
  const [selectedCategory, setSelectedCategory] = useState('all');

  // İlerleme istatistikleri
  const progressStats = getProgressStats();

  // Filtrelenmiş özellikler
  const getFilteredFeatures = useCallback(() => {
    let features = ALL_FEATURES;

    // Durum filtresi
    if (selectedFilter !== 'all') {
      features = features.filter(feature => feature.status === selectedFilter);
    }

    // Kategori filtresi
    if (selectedCategory !== 'all') {
      features = features.filter(feature => feature.category === selectedCategory);
    }

    // Tamamlananları en sona koy, diğerlerini ilerleme durumuna göre sırala
    return features.sort((a, b) => {
      if (a.status === FEATURE_STATUS.COMPLETED && b.status !== FEATURE_STATUS.COMPLETED) {
        return 1;
      }
      if (b.status === FEATURE_STATUS.COMPLETED && a.status !== FEATURE_STATUS.COMPLETED) {
        return -1;
      }
      return b.progress - a.progress;
    });
  }, [selectedFilter, selectedCategory]);

  const filteredFeatures = getFilteredFeatures();

  // Yenileme
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    // Simüle edilmiş yenileme
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  }, []);

  // Özellik detayı göster
  const showFeatureDetail = useCallback((feature) => {
    const statusColors = getStatusColors(feature.status);
    const statusLabel = getStatusLabel(feature.status);
    
    let message = `${feature.description}\n\n`;
    message += `Durum: ${statusLabel}\n`;
    message += `İlerleme: %${feature.progress}\n`;
    
    if (feature.estimatedCompletion) {
      message += `Tahmini Tamamlanma: ${new Date(feature.estimatedCompletion).toLocaleDateString('tr-TR')}\n`;
    }
    
    if (feature.completedAt) {
      message += `Tamamlanma Tarihi: ${new Date(feature.completedAt).toLocaleDateString('tr-TR')}\n`;
    }

    Alert.alert(feature.title, message);
  }, []);

  // İlerleme çubuğu
  const renderProgressBar = useCallback((progress, status) => {
    const statusColors = getStatusColors(status);
    
    return (
      <View style={styles.progressBarContainer}>
        <View style={[styles.progressBarBackground, { backgroundColor: statusColors.light }]}>
          <View 
            style={[
              styles.progressBarFill, 
              { 
                backgroundColor: statusColors.primary,
                width: `${progress}%`
              }
            ]} 
          />
        </View>
        <Text style={[styles.progressText, { color: statusColors.text }]}>
          %{progress}
        </Text>
      </View>
    );
  }, []);

  // Özellik kartı
  const renderFeatureCard = useCallback((feature) => {
    const statusColors = getStatusColors(feature.status);
    const statusLabel = getStatusLabel(feature.status);

    return (
      <TouchableOpacity
        key={feature.id}
        style={[styles.featureCard, { backgroundColor: theme.CARD }]}
        onPress={() => showFeatureDetail(feature)}
      >
        <View style={styles.featureHeader}>
          <View style={styles.featureLeft}>
            <View style={[styles.featureIcon, { backgroundColor: statusColors.light }]}>
              <MaterialIcons name={feature.icon} size={24} color={statusColors.primary} />
            </View>
            <View style={styles.featureInfo}>
              <Text style={[styles.featureTitle, { color: theme.TEXT_PRIMARY }]}>
                {feature.title}
              </Text>
              <Text style={[styles.featureCategory, { color: theme.TEXT_SECONDARY }]}>
                {feature.category.charAt(0).toUpperCase() + feature.category.slice(1)}
              </Text>
            </View>
          </View>
          <View style={[styles.statusBadge, { backgroundColor: statusColors.primary }]}>
            <Text style={[styles.statusText, { color: '#fff' }]}>
              {statusLabel}
            </Text>
          </View>
        </View>

        <Text style={[styles.featureDescription, { color: theme.TEXT_SECONDARY }]}>
          {feature.description}
        </Text>

        {/* İlerleme çubuğu */}
        {renderProgressBar(feature.progress, feature.status)}

        {/* Tahmini tamamlanma tarihi */}
        {feature.estimatedCompletion && feature.status !== FEATURE_STATUS.COMPLETED && (
          <View style={styles.estimatedDate}>
            <MaterialIcons name="schedule" size={16} color={theme.TEXT_SECONDARY} />
            <Text style={[styles.estimatedDateText, { color: theme.TEXT_SECONDARY }]}>
              Tahmini: {new Date(feature.estimatedCompletion).toLocaleDateString('tr-TR')}
            </Text>
          </View>
        )}

        {/* Tamamlanma tarihi */}
        {feature.completedAt && feature.status === FEATURE_STATUS.COMPLETED && (
          <View style={styles.completedDate}>
            <MaterialIcons name="check-circle" size={16} color={Colors.SUCCESS} />
            <Text style={[styles.completedDateText, { color: Colors.SUCCESS }]}>
              Tamamlandı: {new Date(feature.completedAt).toLocaleDateString('tr-TR')}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    );
  }, [theme, renderProgressBar, showFeatureDetail]);

  // Filtre butonları
  const renderFilterButtons = useCallback(() => {
    const filters = [
      { key: 'all', label: 'Tümü', count: ALL_FEATURES.length },
      { key: FEATURE_STATUS.COMPLETED, label: 'Tamamlanan', count: progressStats.completed },
      { key: FEATURE_STATUS.IN_PROGRESS, label: 'Devam Eden', count: progressStats.inProgress },
      { key: FEATURE_STATUS.COMING_SOON, label: 'Yakında', count: progressStats.comingSoon },
      { key: FEATURE_STATUS.PLANNED, label: 'Planlanan', count: progressStats.planned }
    ];

    return (
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.filterContainer}
        contentContainerStyle={styles.filterContent}
      >
        {filters.map(filter => (
          <TouchableOpacity
            key={filter.key}
            style={[
              styles.filterButton,
              selectedFilter === filter.key && styles.filterButtonActive,
              { 
                backgroundColor: selectedFilter === filter.key ? theme.PRIMARY : theme.CARD,
                borderColor: theme.BORDER
              }
            ]}
            onPress={() => setSelectedFilter(filter.key)}
          >
            <Text style={[
              styles.filterButtonText,
              { color: selectedFilter === filter.key ? '#fff' : theme.TEXT_PRIMARY }
            ]}>
              {filter.label}
            </Text>
            <Text style={[
              styles.filterButtonCount,
              { color: selectedFilter === filter.key ? '#fff' : theme.TEXT_SECONDARY }
            ]}>
              {filter.count}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    );
  }, [selectedFilter, theme, progressStats]);

  // Genel ilerleme özeti
  const renderProgressSummary = useCallback(() => {
    return (
      <View style={[styles.progressSummary, { backgroundColor: theme.CARD }]}>
        <View style={styles.progressSummaryHeader}>
          <MaterialIcons name="analytics" size={24} color={theme.PRIMARY} />
          <Text style={[styles.progressSummaryTitle, { color: theme.TEXT_PRIMARY }]}>
            Genel İlerleme
          </Text>
        </View>
        
        <View style={styles.progressSummaryStats}>
          <View style={styles.progressStat}>
            <Text style={[styles.progressStatValue, { color: Colors.SUCCESS }]}>
              {progressStats.completed}
            </Text>
            <Text style={[styles.progressStatLabel, { color: theme.TEXT_SECONDARY }]}>
              Tamamlandı
            </Text>
          </View>
          <View style={styles.progressStat}>
            <Text style={[styles.progressStatValue, { color: Colors.WARNING }]}>
              {progressStats.inProgress}
            </Text>
            <Text style={[styles.progressStatLabel, { color: theme.TEXT_SECONDARY }]}>
              Devam Ediyor
            </Text>
          </View>
          <View style={styles.progressStat}>
            <Text style={[styles.progressStatValue, { color: Colors.PRIMARY }]}>
              {progressStats.comingSoon}
            </Text>
            <Text style={[styles.progressStatLabel, { color: theme.TEXT_SECONDARY }]}>
              Yakında
            </Text>
          </View>
          <View style={styles.progressStat}>
            <Text style={[styles.progressStatValue, { color: theme.TEXT_SECONDARY }]}>
              {progressStats.planned}
            </Text>
            <Text style={[styles.progressStatLabel, { color: theme.TEXT_SECONDARY }]}>
              Planlanan
            </Text>
          </View>
        </View>

        <View style={styles.overallProgress}>
          <Text style={[styles.overallProgressLabel, { color: theme.TEXT_PRIMARY }]}>
            Toplam Tamamlanma Oranı
          </Text>
          {renderProgressBar(progressStats.completionRate, FEATURE_STATUS.COMPLETED)}
        </View>
      </View>
    );
  }, [theme, progressStats, renderProgressBar]);

  return (
    <View style={[styles.container, { paddingTop: insets.top, backgroundColor: theme.BACKGROUND }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.PRIMARY }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Özellik Durumu</Text>
        <TouchableOpacity
          style={styles.headerButton}
          onPress={handleRefresh}
        >
          <MaterialIcons name="refresh" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.content}
        contentContainerStyle={styles.contentContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[Colors.PRIMARY]}
          />
        }
      >
        {/* Genel İlerleme Özeti */}
        {renderProgressSummary()}

        {/* Filtre Butonları */}
        {renderFilterButtons()}

        {/* Özellik Listesi */}
        <View style={styles.featuresContainer}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            Özellikler ({filteredFeatures.length})
          </Text>
          
          {filteredFeatures.map(renderFeatureCard)}
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={[styles.footerText, { color: theme.TEXT_SECONDARY }]}>
            Bu liste düzenli olarak güncellenmektedir. Yeni özellikler ve iyileştirmeler için takipte kalın!
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#fff',
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 16,
  },
  headerButton: {
    padding: 8,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 32,
  },
  progressSummary: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  progressSummaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  progressSummaryTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginLeft: 12,
  },
  progressSummaryStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  progressStat: {
    alignItems: 'center',
  },
  progressStatValue: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 4,
  },
  progressStatLabel: {
    fontSize: 12,
    fontWeight: '500',
  },
  overallProgress: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  overallProgressLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    textAlign: 'center',
  },
  filterContainer: {
    marginBottom: 20,
  },
  filterContent: {
    paddingHorizontal: 4,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginHorizontal: 4,
    borderWidth: 1,
    alignItems: 'center',
    minWidth: 80,
  },
  filterButtonActive: {
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  filterButtonCount: {
    fontSize: 12,
    fontWeight: '500',
    marginTop: 2,
  },
  featuresContainer: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 16,
  },
  featureCard: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  featureHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  featureLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  featureIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  featureInfo: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 2,
  },
  featureCategory: {
    fontSize: 12,
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  featureDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  progressBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressBarBackground: {
    flex: 1,
    height: 8,
    borderRadius: 4,
    marginRight: 12,
  },
  progressBarFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 12,
    fontWeight: '600',
    minWidth: 32,
  },
  estimatedDate: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  estimatedDateText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 6,
  },
  completedDate: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  completedDateText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 6,
  },
  footer: {
    marginTop: 20,
    padding: 16,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
    fontStyle: 'italic',
  },
});

export default ComingSoonScreen;
