/**
 * Gelişmiş Analitik Araçları
 * Trend analizi, tahminleme ve istatistiksel hesaplamalar
 */

/**
 * Trend analizi yapar
 */
export const analyzeTrend = (data) => {
  if (!data || data.length < 2) {
    return { trend: 'flat', slope: 0, strength: 0 };
  }

  // Linear regression ile trend hesapla
  const n = data.length;
  const x = data.map((_, index) => index);
  const y = data.map(item => item.value || item);
  
  const sumX = x.reduce((sum, val) => sum + val, 0);
  const sumY = y.reduce((sum, val) => sum + val, 0);
  const sumXY = x.reduce((sum, val, i) => sum + val * y[i], 0);
  const sumXX = x.reduce((sum, val) => sum + val * val, 0);
  
  const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
  const intercept = (sumY - slope * sumX) / n;
  
  // R-squared hesapla
  const yMean = sumY / n;
  const totalSumSquares = y.reduce((sum, val) => sum + Math.pow(val - yMean, 2), 0);
  const residualSumSquares = y.reduce((sum, val, i) => {
    const predicted = slope * x[i] + intercept;
    return sum + Math.pow(val - predicted, 2);
  }, 0);
  
  const rSquared = 1 - (residualSumSquares / totalSumSquares);
  
  // Trend yönü ve gücü
  const trend = slope > 0.1 ? 'increasing' : slope < -0.1 ? 'decreasing' : 'flat';
  const strength = Math.abs(rSquared);
  
  return {
    trend,
    slope,
    strength,
    rSquared,
    intercept,
    predicted: x.map(val => slope * val + intercept)
  };
};

/**
 * Basit tahminleme yapar
 */
export const forecast = (data, periods = 3) => {
  if (!data || data.length < 3) {
    return [];
  }

  const trend = analyzeTrend(data);
  const lastIndex = data.length - 1;
  const forecasts = [];
  
  for (let i = 1; i <= periods; i++) {
    const nextValue = trend.slope * (lastIndex + i) + trend.intercept;
    forecasts.push({
      period: i,
      value: Math.max(0, nextValue), // Negatif değerleri sıfırla
      confidence: Math.max(0, trend.strength * 100)
    });
  }
  
  return forecasts;
};

/**
 * Hareketli ortalama hesaplar
 */
export const calculateMovingAverage = (data, window = 3) => {
  if (!data || data.length < window) {
    return [];
  }

  const movingAverages = [];
  
  for (let i = window - 1; i < data.length; i++) {
    const slice = data.slice(i - window + 1, i + 1);
    const average = slice.reduce((sum, val) => sum + (val.value || val), 0) / window;
    movingAverages.push({
      index: i,
      value: average,
      period: data[i].period || data[i].month || i
    });
  }
  
  return movingAverages;
};

/**
 * Yüzde değişim hesaplar
 */
export const calculatePercentChange = (data) => {
  if (!data || data.length < 2) {
    return [];
  }

  const changes = [];
  
  for (let i = 1; i < data.length; i++) {
    const previous = data[i - 1].value || data[i - 1];
    const current = data[i].value || data[i];
    
    const change = previous === 0 ? 0 : ((current - previous) / previous) * 100;
    
    changes.push({
      index: i,
      value: change,
      period: data[i].period || data[i].month || i
    });
  }
  
  return changes;
};

/**
 * Temel istatistikler hesaplar
 */
export const calculateStatistics = (data) => {
  if (!data || data.length === 0) {
    return {};
  }

  const values = data.map(item => item.value || item);
  const sortedValues = [...values].sort((a, b) => a - b);
  
  const sum = values.reduce((sum, val) => sum + val, 0);
  const mean = sum / values.length;
  const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
  const standardDeviation = Math.sqrt(variance);
  
  const min = sortedValues[0];
  const max = sortedValues[sortedValues.length - 1];
  const median = values.length % 2 === 0 
    ? (sortedValues[values.length / 2 - 1] + sortedValues[values.length / 2]) / 2
    : sortedValues[Math.floor(values.length / 2)];
  
  const q1 = sortedValues[Math.floor(values.length * 0.25)];
  const q3 = sortedValues[Math.floor(values.length * 0.75)];
  
  return {
    count: values.length,
    sum,
    mean,
    median,
    min,
    max,
    variance,
    standardDeviation,
    q1,
    q3,
    range: max - min
  };
};

/**
 * Anomali tespiti yapar
 */
export const detectAnomalies = (data, threshold = 2) => {
  if (!data || data.length < 3) {
    return [];
  }

  const stats = calculateStatistics(data);
  const anomalies = [];
  
  data.forEach((item, index) => {
    const value = item.value || item;
    const zScore = Math.abs((value - stats.mean) / stats.standardDeviation);
    
    if (zScore > threshold) {
      anomalies.push({
        index,
        value,
        zScore,
        severity: zScore > 3 ? 'high' : 'medium'
      });
    }
  });
  
  return anomalies;
};

/**
 * Sezonalite analizi yapar
 */
export const analyzeSeasonality = (data) => {
  if (!data || data.length < 12) {
    return { hasSeasonality: false };
  }

  // Aylık veriler için basit sezonalite analizi
  const monthlyAverages = {};
  const monthlyData = {};
  
  data.forEach(item => {
    const month = item.month ? item.month.split('-')[1] : String((item.index || 0) % 12 + 1).padStart(2, '0');
    const value = item.value || item;
    
    if (!monthlyData[month]) {
      monthlyData[month] = [];
    }
    monthlyData[month].push(value);
  });
  
  Object.keys(monthlyData).forEach(month => {
    const values = monthlyData[month];
    monthlyAverages[month] = values.reduce((sum, val) => sum + val, 0) / values.length;
  });
  
  const averageValues = Object.values(monthlyAverages);
  const overallAverage = averageValues.reduce((sum, val) => sum + val, 0) / averageValues.length;
  
  // Sezonalite katsayıları
  const seasonalityCoefficients = {};
  Object.keys(monthlyAverages).forEach(month => {
    seasonalityCoefficients[month] = monthlyAverages[month] / overallAverage;
  });
  
  // Sezonalite varyansı
  const seasonalityVariance = averageValues.reduce((sum, val) => sum + Math.pow(val - overallAverage, 2), 0) / averageValues.length;
  const hasSeasonality = seasonalityVariance > (overallAverage * 0.1);
  
  return {
    hasSeasonality,
    monthlyAverages,
    seasonalityCoefficients,
    seasonalityVariance,
    strongestMonth: Object.keys(monthlyAverages).reduce((a, b) => 
      monthlyAverages[a] > monthlyAverages[b] ? a : b
    ),
    weakestMonth: Object.keys(monthlyAverages).reduce((a, b) => 
      monthlyAverages[a] < monthlyAverages[b] ? a : b
    )
  };
};
