import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

/**
 * AI Analysis Engine Component
 * Provides intelligent financial analysis and insights
 * Follows REPORTS_SCREEN_REDESIGN_PLAN.md specifications
 */
const AIAnalysisEngine = ({ 
  data = [],
  analysisType = 'comprehensive',
  onAnalysisComplete,
  theme 
}) => {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysis, setAnalysis] = useState(null);
  const [insights, setInsights] = useState([]);
  const [recommendations, setRecommendations] = useState([]);
  const [trends, setTrends] = useState([]);

  // Analysis types
  const analysisTypes = [
    {
      id: 'spending_patterns',
      name: '<PERSON><PERSON><PERSON>',
      icon: 'analytics',
      description: '<PERSON><PERSON><PERSON> alışkanlıkların<PERSON><PERSON><PERSON> analiz eder',
    },
    {
      id: 'income_trends',
      name: '<PERSON><PERSON><PERSON>',
      icon: 'trending-up',
      description: '<PERSON><PERSON><PERSON>zi takip eder',
    },
    {
      id: 'budget_optimization',
      name: 'Bütçe Optimizasyonu',
      icon: 'pie-chart',
      description: 'Bütçenizi optimize etme önerileri',
    },
    {
      id: 'savings_potential',
      name: 'Tasarruf Potansiyeli',
      icon: 'wallet',
      description: 'Tasarruf fırsatlarını keşfeder',
    },
    {
      id: 'risk_assessment',
      name: 'Risk Değerlendirmesi',
      icon: 'shield-checkmark',
      description: 'Finansal riskleri değerlendirir',
    },
    {
      id: 'goal_tracking',
      name: 'Hedef Takibi',
      icon: 'flag',
      description: 'Finansal hedeflerinizi takip eder',
    },
  ];

  // Perform AI analysis
  const performAnalysis = async (type = analysisType) => {
    setIsAnalyzing(true);
    
    try {
      // Simulate AI analysis with realistic processing time
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const analysisResult = await generateAnalysis(type, data);
      setAnalysis(analysisResult);
      
      const generatedInsights = await generateInsights(analysisResult);
      setInsights(generatedInsights);
      
      const generatedRecommendations = await generateRecommendations(analysisResult);
      setRecommendations(generatedRecommendations);
      
      const generatedTrends = await generateTrends(analysisResult);
      setTrends(generatedTrends);
      
      if (onAnalysisComplete) {
        onAnalysisComplete({
          analysis: analysisResult,
          insights: generatedInsights,
          recommendations: generatedRecommendations,
          trends: generatedTrends,
        });
      }
      
    } catch (error) {
      Alert.alert('Hata', 'Analiz sırasında bir hata oluştu.');
      console.error('Analysis error:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Generate analysis based on data
  const generateAnalysis = async (type, data) => {
    // Simulate AI analysis logic
    const totalTransactions = data.length;
    const totalAmount = data.reduce((sum, item) => sum + (item.amount || 0), 0);
    const avgAmount = totalAmount / totalTransactions || 0;
    
    const categories = [...new Set(data.map(item => item.category))];
    const monthlyData = groupByMonth(data);
    
    return {
      type,
      summary: {
        totalTransactions,
        totalAmount,
        avgAmount,
        categoriesCount: categories.length,
        timeRange: getTimeRange(data),
      },
      categoryBreakdown: getCategoryBreakdown(data),
      monthlyTrends: monthlyData,
      patterns: detectPatterns(data),
      anomalies: detectAnomalies(data),
      score: calculateFinancialScore(data),
    };
  };

  // Generate insights
  const generateInsights = async (analysis) => {
    const insights = [];
    
    // Spending pattern insights
    if (analysis.categoryBreakdown) {
      const topCategory = analysis.categoryBreakdown[0];
      insights.push({
        id: 'top_category',
        type: 'info',
        title: 'En Çok Harcama Yapılan Kategori',
        description: `${topCategory?.category} kategorisinde toplam ₺${topCategory?.amount?.toLocaleString()} harcama yaptınız.`,
        icon: 'pie-chart',
        priority: 'high',
      });
    }
    
    // Trend insights
    if (analysis.monthlyTrends?.length > 1) {
      const lastMonth = analysis.monthlyTrends[analysis.monthlyTrends.length - 1];
      const prevMonth = analysis.monthlyTrends[analysis.monthlyTrends.length - 2];
      const change = ((lastMonth.amount - prevMonth.amount) / prevMonth.amount) * 100;
      
      insights.push({
        id: 'monthly_trend',
        type: change > 0 ? 'warning' : 'success',
        title: 'Aylık Harcama Trendi',
        description: `Geçen aya göre harcamalarınız %${Math.abs(change).toFixed(1)} ${change > 0 ? 'arttı' : 'azaldı'}.`,
        icon: change > 0 ? 'trending-up' : 'trending-down',
        priority: 'medium',
      });
    }
    
    // Financial score insight
    insights.push({
      id: 'financial_score',
      type: analysis.score >= 70 ? 'success' : analysis.score >= 40 ? 'warning' : 'error',
      title: 'Finansal Sağlık Skoru',
      description: `Finansal sağlık skorunuz: ${analysis.score}/100. ${getScoreMessage(analysis.score)}`,
      icon: 'speedometer',
      priority: 'high',
    });
    
    return insights;
  };

  // Generate recommendations
  const generateRecommendations = async (analysis) => {
    const recommendations = [];
    
    // Budget recommendations
    if (analysis.score < 70) {
      recommendations.push({
        id: 'budget_plan',
        title: 'Bütçe Planı Oluşturun',
        description: 'Aylık gelir ve giderlerinizi planlamak için detaylı bir bütçe oluşturmanızı öneriyoruz.',
        action: 'create_budget',
        priority: 'high',
        impact: 'Aylık %15-20 tasarruf sağlayabilir',
        icon: 'calculator',
      });
    }
    
    // Category optimization
    if (analysis.categoryBreakdown?.length > 0) {
      const topCategory = analysis.categoryBreakdown[0];
      if (topCategory.percentage > 40) {
        recommendations.push({
          id: 'category_optimization',
          title: `${topCategory.category} Harcamalarını Optimize Edin`,
          description: `Bu kategorideki harcamalarınız toplam bütçenizin %${topCategory.percentage.toFixed(1)}'ini oluşturuyor.`,
          action: 'optimize_category',
          priority: 'medium',
          impact: 'Aylık %5-10 tasarruf sağlayabilir',
          icon: 'trending-down',
        });
      }
    }
    
    // Savings recommendations
    recommendations.push({
      id: 'savings_goal',
      title: 'Tasarruf Hedefi Belirleyin',
      description: 'Gelecek planlarınız için aylık tasarruf hedefi belirlemenizi öneriyoruz.',
      action: 'set_savings_goal',
      priority: 'medium',
      impact: 'Uzun vadeli finansal güvenlik',
      icon: 'wallet',
    });
    
    return recommendations;
  };

  // Generate trends
  const generateTrends = async (analysis) => {
    const trends = [];
    
    if (analysis.monthlyTrends?.length > 0) {
      trends.push({
        id: 'monthly_spending',
        title: 'Aylık Harcama Trendi',
        data: analysis.monthlyTrends,
        type: 'line',
        color: theme.PRIMARY,
      });
    }
    
    if (analysis.categoryBreakdown?.length > 0) {
      trends.push({
        id: 'category_distribution',
        title: 'Kategori Dağılımı',
        data: analysis.categoryBreakdown.slice(0, 5),
        type: 'pie',
        colors: [theme.PRIMARY, theme.SUCCESS, theme.WARNING, theme.ERROR, theme.INFO],
      });
    }
    
    return trends;
  };

  // Helper functions
  const groupByMonth = (data) => {
    const grouped = {};
    data.forEach(item => {
      const month = new Date(item.date).toISOString().slice(0, 7);
      if (!grouped[month]) {
        grouped[month] = { month, amount: 0, count: 0 };
      }
      grouped[month].amount += item.amount || 0;
      grouped[month].count += 1;
    });
    return Object.values(grouped).sort((a, b) => a.month.localeCompare(b.month));
  };

  const getCategoryBreakdown = (data) => {
    const categories = {};
    const total = data.reduce((sum, item) => sum + (item.amount || 0), 0);
    
    data.forEach(item => {
      if (!categories[item.category]) {
        categories[item.category] = { category: item.category, amount: 0, count: 0 };
      }
      categories[item.category].amount += item.amount || 0;
      categories[item.category].count += 1;
    });
    
    return Object.values(categories)
      .map(cat => ({
        ...cat,
        percentage: (cat.amount / total) * 100,
      }))
      .sort((a, b) => b.amount - a.amount);
  };

  const detectPatterns = (data) => {
    // Simple pattern detection
    return [
      'Hafta sonları daha fazla harcama yapma eğilimi',
      'Ayın ilk haftasında yüksek harcamalar',
      'Belirli kategorilerde düzenli harcamalar',
    ];
  };

  const detectAnomalies = (data) => {
    // Simple anomaly detection
    return [
      { date: '2024-01-15', amount: 2500, reason: 'Ortalamadan %300 yüksek harcama' },
    ];
  };

  const calculateFinancialScore = (data) => {
    // Simple scoring algorithm
    const totalAmount = data.reduce((sum, item) => sum + (item.amount || 0), 0);
    const avgAmount = totalAmount / data.length || 0;
    const categoryCount = [...new Set(data.map(item => item.category))].length;
    
    let score = 50; // Base score
    
    // Adjust based on spending patterns
    if (avgAmount < 100) score += 20;
    else if (avgAmount < 500) score += 10;
    else if (avgAmount > 1000) score -= 10;
    
    // Adjust based on category diversity
    if (categoryCount > 5) score += 10;
    else if (categoryCount < 3) score -= 10;
    
    return Math.max(0, Math.min(100, score));
  };

  const getTimeRange = (data) => {
    if (data.length === 0) return null;
    const dates = data.map(item => new Date(item.date)).sort();
    return {
      start: dates[0].toISOString().slice(0, 10),
      end: dates[dates.length - 1].toISOString().slice(0, 10),
    };
  };

  const getScoreMessage = (score) => {
    if (score >= 80) return 'Mükemmel finansal sağlık!';
    if (score >= 60) return 'İyi durumdasınız, küçük iyileştirmeler yapabilirsiniz.';
    if (score >= 40) return 'Orta seviye, bazı alanları geliştirmelisiniz.';
    return 'Finansal planlamanızı gözden geçirmeniz önerilir.';
  };

  useEffect(() => {
    if (data.length > 0) {
      performAnalysis();
    }
  }, [data, analysisType]);

  return (
    <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.SURFACE }]}>
        <View style={styles.headerContent}>
          <Ionicons name="brain" size={24} color={theme.PRIMARY} />
          <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
            AI Finansal Analiz
          </Text>
        </View>
        
        <TouchableOpacity
          style={[styles.refreshButton, { backgroundColor: theme.PRIMARY }]}
          onPress={() => performAnalysis()}
          disabled={isAnalyzing}
        >
          {isAnalyzing ? (
            <ActivityIndicator size="small" color={theme.SURFACE} />
          ) : (
            <Ionicons name="refresh" size={16} color={theme.SURFACE} />
          )}
        </TouchableOpacity>
      </View>

      {/* Analysis Types */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.typesContainer}
      >
        {analysisTypes.map((type) => (
          <TouchableOpacity
            key={type.id}
            style={[
              styles.typeCard,
              {
                backgroundColor: analysisType === type.id ? theme.PRIMARY : theme.SURFACE,
                borderColor: theme.BORDER,
              }
            ]}
            onPress={() => performAnalysis(type.id)}
            disabled={isAnalyzing}
          >
            <Ionicons 
              name={type.icon} 
              size={20} 
              color={analysisType === type.id ? theme.SURFACE : theme.PRIMARY} 
            />
            <Text style={[
              styles.typeTitle,
              { 
                color: analysisType === type.id ? theme.SURFACE : theme.TEXT_PRIMARY 
              }
            ]}>
              {type.name}
            </Text>
            <Text style={[
              styles.typeDescription,
              { 
                color: analysisType === type.id ? theme.SURFACE : theme.TEXT_SECONDARY 
              }
            ]}>
              {type.description}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Analysis Results */}
      <ScrollView style={styles.resultsContainer}>
        {isAnalyzing ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.PRIMARY} />
            <Text style={[styles.loadingText, { color: theme.TEXT_SECONDARY }]}>
              AI analiz yapıyor...
            </Text>
          </View>
        ) : analysis ? (
          <>
            {/* Summary */}
            <View style={[styles.summaryCard, { backgroundColor: theme.SURFACE }]}>
              <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
                📊 Analiz Özeti
              </Text>
              <View style={styles.summaryGrid}>
                <View style={styles.summaryItem}>
                  <Text style={[styles.summaryValue, { color: theme.PRIMARY }]}>
                    {analysis.summary.totalTransactions}
                  </Text>
                  <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>
                    İşlem
                  </Text>
                </View>
                <View style={styles.summaryItem}>
                  <Text style={[styles.summaryValue, { color: theme.SUCCESS }]}>
                    ₺{analysis.summary.totalAmount.toLocaleString()}
                  </Text>
                  <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>
                    Toplam
                  </Text>
                </View>
                <View style={styles.summaryItem}>
                  <Text style={[styles.summaryValue, { color: theme.INFO }]}>
                    {analysis.score}/100
                  </Text>
                  <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>
                    Skor
                  </Text>
                </View>
              </View>
            </View>

            {/* Insights */}
            {insights.length > 0 && (
              <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
                <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
                  💡 Önemli Bulgular
                </Text>
                {insights.map((insight) => (
                  <View 
                    key={insight.id}
                    style={[
                      styles.insightCard,
                      { backgroundColor: theme.BACKGROUND, borderLeftColor: getInsightColor(insight.type, theme) }
                    ]}
                  >
                    <View style={styles.insightHeader}>
                      <Ionicons 
                        name={insight.icon} 
                        size={20} 
                        color={getInsightColor(insight.type, theme)} 
                      />
                      <Text style={[styles.insightTitle, { color: theme.TEXT_PRIMARY }]}>
                        {insight.title}
                      </Text>
                    </View>
                    <Text style={[styles.insightDescription, { color: theme.TEXT_SECONDARY }]}>
                      {insight.description}
                    </Text>
                  </View>
                ))}
              </View>
            )}

            {/* Recommendations */}
            {recommendations.length > 0 && (
              <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
                <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
                  🎯 Öneriler
                </Text>
                {recommendations.map((rec) => (
                  <View 
                    key={rec.id}
                    style={[styles.recommendationCard, { backgroundColor: theme.BACKGROUND }]}
                  >
                    <View style={styles.recommendationHeader}>
                      <Ionicons name={rec.icon} size={20} color={theme.PRIMARY} />
                      <Text style={[styles.recommendationTitle, { color: theme.TEXT_PRIMARY }]}>
                        {rec.title}
                      </Text>
                    </View>
                    <Text style={[styles.recommendationDescription, { color: theme.TEXT_SECONDARY }]}>
                      {rec.description}
                    </Text>
                    <Text style={[styles.recommendationImpact, { color: theme.SUCCESS }]}>
                      💰 {rec.impact}
                    </Text>
                  </View>
                ))}
              </View>
            )}
          </>
        ) : (
          <View style={styles.emptyState}>
            <Ionicons name="analytics" size={48} color={theme.TEXT_SECONDARY} />
            <Text style={[styles.emptyStateText, { color: theme.TEXT_SECONDARY }]}>
              Analiz yapmak için yukarıdan bir analiz türü seçin
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const getInsightColor = (type, theme) => {
  switch (type) {
    case 'success': return theme.SUCCESS;
    case 'warning': return theme.WARNING;
    case 'error': return theme.ERROR;
    case 'info': return theme.INFO;
    default: return theme.PRIMARY;
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  refreshButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  typesContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  typeCard: {
    width: 160,
    padding: 12,
    borderRadius: 12,
    borderWidth: 1,
    marginRight: 12,
  },
  typeTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginTop: 8,
    marginBottom: 4,
  },
  typeDescription: {
    fontSize: 12,
    lineHeight: 16,
  },
  resultsContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  loadingText: {
    fontSize: 16,
    marginTop: 16,
  },
  summaryCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  summaryGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryValue: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  summaryLabel: {
    fontSize: 12,
    marginTop: 4,
  },
  section: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  insightCard: {
    padding: 12,
    borderRadius: 8,
    borderLeftWidth: 4,
    marginBottom: 8,
  },
  insightHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 4,
  },
  insightTitle: {
    fontSize: 14,
    fontWeight: '600',
  },
  insightDescription: {
    fontSize: 12,
    lineHeight: 16,
  },
  recommendationCard: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  recommendationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 4,
  },
  recommendationTitle: {
    fontSize: 14,
    fontWeight: '600',
  },
  recommendationDescription: {
    fontSize: 12,
    lineHeight: 16,
    marginBottom: 4,
  },
  recommendationImpact: {
    fontSize: 12,
    fontWeight: '600',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyStateText: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: 12,
  },
});

export default AIAnalysisEngine;
