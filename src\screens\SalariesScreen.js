import React, { useState, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  RefreshControl,
  Switch
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import { useAppContext } from '../context/AppContext';
import * as salaryService from '../services/salaryService';
import { getAllRegularIncomes, deleteRegularIncome, updateRegularIncome } from '../services/regularIncomeService';
import { formatCurrency } from '../utils/formatters';
import CurrencyEquivalent from '../components/CurrencyEquivalent';

/**
 * Maaş ve Gelir Yönetimi Ekranı
 * Tab-tabanlı interface ile maaşlar, dü<PERSON><PERSON> gelirler ve yaklaşan ödemeler gösterilir
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @returns {JSX.Element} Maaş ve Gelir Yönetimi Ekranı
 */
export default function SalariesScreen({ navigation }) {
  const db = useSQLiteContext();
  const { theme } = useAppContext();

  // Durum değişkenleri
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [salaries, setSalaries] = useState([]);
  const [upcomingPayments, setUpcomingPayments] = useState([]);
  const [regularIncomes, setRegularIncomes] = useState([]);
  const [filter, setFilter] = useState('all'); // all, active, inactive
  const [incomeFilter, setIncomeFilter] = useState('all'); // all, active, paused, ended
  const [sortBy, setSortBy] = useState('date'); // date, amount, name
  const [showAllUpcomingPayments, setShowAllUpcomingPayments] = useState(false);
  const [activeTab, setActiveTab] = useState('salaries'); // salaries, incomes, upcoming

  /**
   * Verileri yükle
   */
  const loadData = useCallback(async () => {
    try {
      setLoading(true);

      // Maaşları getir
      const salariesData = await salaryService.getSalaries(db);
      setSalaries(salariesData);

      // Yaklaşan ödemeleri getir
      const upcomingPaymentsData = await salaryService.getUpcomingSalaryPayments(db, 30);
      setUpcomingPayments(upcomingPaymentsData);
      
      // Düzenli gelirleri getir
      const regularIncomesData = await getAllRegularIncomes();
      setRegularIncomes(regularIncomesData);

      setLoading(false);
    } catch (error) {
      console.error('Veri yükleme hatası:', error);
      Alert.alert('Hata', 'Veriler yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  }, [db]);

  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadData();

      // Set activeTab if it's in the route params
      const routeParams = navigation.getState()?.routes?.find(route => route.name === 'Salaries')?.params;
      if (routeParams?.activeTab) {
        setActiveTab(routeParams.activeTab);
      }
    }, [loadData, navigation])
  );

  /**
   * Yenileme işlemi
   */
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  /**
   * Yeni düzenli gelir ekle
   */
  const addNewRegularIncome = () => {
    navigation.navigate('RegularIncomeForm', { onReturn: 'incomes' });
  };
  
  /**
   * Yeni maaş ekle (düzenli gelir olarak)
   */
  const addNewSalary = () => {
    navigation.navigate('RegularIncomeForm', { onReturn: 'salaries' });
  };
  
  /**
   * Düzenli geliri düzenle
   */
  const editRegularIncome = (incomeId) => {
    navigation.navigate('RegularIncomeForm', { incomeId });
  };
  
  // Düzenli geliri sil onayı
  const confirmDeleteRegularIncome = (income) => {
    Alert.alert(
      'Düzenli Geliri Sil',
      `"${income.title}" düzenli gelirini silmek istediğinize emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteRegularIncome({ id: income.id });
              Alert.alert('Başarılı', 'Düzenli gelir başarıyla silindi.');
              loadData();
            } catch (error) {
              console.error('Düzenli gelir silme hatası:', error);
              Alert.alert('Hata', 'Düzenli gelir silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };

  // Maaş detaylarını görüntüle
  const viewSalaryDetails = (salaryId) => {
    navigation.navigate('SalaryDetail', { salaryId });
  };

  // Maaşı düzenle (düzenli gelir olarak)
  const editSalary = (salary) => {
    // Düzenli gelir formuna salary verisini uyumlu bir şekilde aktararak yönlendir
    const regularIncomeData = {
      id: salary?.id,
      title: salary?.name,
      amount: salary?.amount,
      currency_code: salary?.currency,
      recurrence_type: 'monthly',
      payment_day: salary?.payment_day,
      notes: salary?.notes,
      is_salary: true
    };
    navigation.navigate('RegularIncomeForm', { regularIncomeData });
  };

  // Maaşı sil
  const deleteSalary = async (salaryId) => {
    try {
      await salaryService.deleteSalary(db, salaryId);
      Alert.alert('Başarılı', 'Maaş başarıyla silindi.');
      loadData();
    } catch (error) {
      console.error('Maaş silme hatası:', error);
      Alert.alert('Hata', 'Maaş silinirken bir hata oluştu.');
    }
  };

  // Maaş silme onayı
  const confirmDeleteSalary = (salary) => {
    Alert.alert(
      'Maaşı Sil',
      `"${salary.name}" maaşını silmek istediğinize emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: () => deleteSalary(salary.id)
        }
      ]
    );
  };

  // Gelecek ödemeleri oluştur
  const generateFuturePayments = async () => {
    try {
      // Kullanıcıya kaç ay için ödeme oluşturmak istediğini sor
      Alert.alert(
        'Gelecek Ödemeler Oluştur',
        'Seçilen süre için gelecek tüm ödemeleri otomatik olarak oluşturur. Bu işlem mevcut ödemeleri etkilemez.',
        [
          { text: 'İptal', style: 'cancel' },
          {
            text: '1 Ay',
            onPress: async () => await createFuturePayments(1)
          },
          {
            text: '3 Ay',
            onPress: async () => await createFuturePayments(3)
          },
          {
            text: '6 Ay',
            onPress: async () => await createFuturePayments(6)
          },
          {
            text: '12 Ay',
            onPress: async () => await createFuturePayments(12)
          }
        ]
      );
    } catch (error) {
      console.error('Gelecek ödemeler oluşturma hatası:', error);
      Alert.alert('Hata', 'Gelecek ödemeler oluşturulurken bir hata oluştu. Lütfen tekrar deneyin.');
    }
  };

  // Gelecek ödemeleri oluştur (asıl işlem)
  const createFuturePayments = async (months) => {
    try {
      // İşlem başladığını kullanıcıya bildir
      Alert.alert('İşlem Başladı', `${months} ay için gelecek ödemeler oluşturuluyor. Bu işlem biraz zaman alabilir.`);
      
      const count = await salaryService.generateFutureSalaryPayments(db, months);

      if (count > 0) {
        // Başarılı sonuç
        Alert.alert(
          'İşlem Başarılı', 
          `${count} adet gelecek ödeme başarıyla oluşturuldu.\n\nBu ödemeleri "Yaklaşan Ödemeler" sekmesinden görüntüleyebilirsiniz.`,
          [{ text: 'Tamam', onPress: () => setActiveTab('upcoming') }]
        );
      } else {
        Alert.alert('Bilgi', 'Oluşturulacak yeni ödeme bulunamadı. Tüm ödemeler zaten oluşturulmuş olabilir.');
      }

      // Verileri yenile
      await loadData();
      
    } catch (error) {
      console.error('Gelecek ödemeler oluşturma hatası:', error);
      Alert.alert('Hata', 'Gelecek ödemeler oluşturulurken bir hata oluştu. Lütfen tekrar deneyin.');
    }
  };

  // Ödemeyi işleme al
  const processPayment = async (paymentId) => {
    try {
      await salaryService.processSalaryPayment(db, paymentId);
      Alert.alert('Başarılı', 'Maaş ödemesi başarıyla işlendi.');
      loadData();
    } catch (error) {
      console.error('Ödeme işleme hatası:', error);
      Alert.alert('Hata', 'Ödeme işlenirken bir hata oluştu.');
    }
  };

  // Ödeme işleme onayı
  const confirmProcessPayment = (payment) => {
    Alert.alert(
      'Ödemeyi İşle',
      `${payment.salary_name} için ${formatCurrency(payment.amount, payment.currency)} tutarındaki ödemeyi işlemek istediğinize emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'İşle',
          style: 'default',
          onPress: () => processPayment(payment.id)
        }
      ]
    );
  };

  // Düzenli gelirlerin toplam tutarını hesapla
  const calculateTotalRegularIncome = useCallback(() => {
    if (!regularIncomes.length) return { total: 0, currency: 'TRY', breakdown: [] };
    
    // Sadece aktif olanları topla
    const activeIncomes = regularIncomes.filter(income => income.status === 'active');
    
    if (activeIncomes.length === 0) {
      return { total: 0, currency: 'TRY', breakdown: [] };
    }
    
    // Para birimi bazında gruplayarak topla
    const byCurrency = activeIncomes.reduce((acc, income) => {
      const currency = income.currency_code || 'TRY';
      if (!acc[currency]) {
        acc[currency] = 0;
      }
      acc[currency] += Number(income.amount);
      return acc;
    }, {});
    
    // Dökümü dizi formatında oluştur
    const breakdown = Object.entries(byCurrency).map(([currency, amount]) => ({
      currency,
      amount
    }));
    
    // En çok kullanılan para birimini ana para birimi olarak belirle
    const mainCurrency = Object.entries(byCurrency)
      .sort((a, b) => b[1] - a[1])[0][0];
    
    // Toplam tutarı ana para biriminde göster (gelecekte dönüşüm eklenebilir)
    const total = byCurrency[mainCurrency];
    
    return {
      total,
      currency: mainCurrency,
      breakdown
    };
  }, [regularIncomes]);
  
  // Filtrelenmiş düzenli gelirler
  const filteredRegularIncomes = useMemo(() => {
    let filtered = [...regularIncomes];
    
    // Status filtreleme
    if (incomeFilter !== 'all') {
      filtered = filtered.filter(income => income.status === incomeFilter);
    }
    
    // Sıralama
    switch (sortBy) {
      case 'date':
        filtered.sort((a, b) => new Date(a.next_payment_date) - new Date(b.next_payment_date));
        break;
      case 'amount':
        filtered.sort((a, b) => Number(b.amount) - Number(a.amount));
        break;
      case 'name':
        filtered.sort((a, b) => a.title.localeCompare(b.title));
        break;
      default:
        break;
    }
    
    return filtered;
  }, [regularIncomes, incomeFilter, sortBy]);

  // Filtrelenmiş maaşlar
  const filteredSalaries = salaries.filter(salary => {
    if (filter === 'active') return salary.is_active === 1;
    if (filter === 'inactive') return salary.is_active === 0;
    return true;
  });

  // Maaş öğesi render fonksiyonu - daha modern ve açıklayıcı
  const renderSalaryItem = React.useCallback(({ item }) => (
    <TouchableOpacity
      style={[
        styles.salaryItem, 
        item.is_active === 0 && styles.inactiveSalaryItem
      ]}
      onPress={() => viewSalaryDetails(item.id)}
      activeOpacity={0.7}
    >
      {/* Sol bölüm - ikon ve durum */}
      <View style={styles.salaryItemLeftSection}>
        <View style={[
          styles.salaryItemIconContainer,
          {backgroundColor: item.is_active === 1 ? Colors.PRIMARY + '20' : Colors.GRAY_300 + '50'}
        ]}>
          <MaterialIcons
            name={item.category_icon || "account-balance-wallet"}
            size={24}
            color={item.is_active === 1 ? item.category_color || Colors.PRIMARY : Colors.GRAY_500}
          />
        </View>
        
        <View style={[
          styles.statusBadge,
          item.is_active === 1 ? styles.activeBadge : styles.inactiveBadge
        ]}>
          <MaterialIcons 
            name={item.is_active === 1 ? "check-circle" : "not-interested"} 
            size={12} 
            color="#fff" 
            style={styles.statusIcon}
          />
          <Text style={styles.statusText}>
            {item.is_active === 1 ? 'Aktif' : 'Pasif'}
          </Text>
        </View>
      </View>
      
      {/* Orta bölüm - başlık ve detaylar */}
      <View style={styles.salaryItemMiddleSection}>
        <Text style={styles.salaryItemName}>{item.name}</Text>
        <View style={styles.salaryItemPaymentInfo}>
          <MaterialIcons name="date-range" size={14} color={Colors.GRAY_600} style={{ marginRight: 4 }} />
          <Text style={styles.salaryItemPaymentDate}>
            Her ayın <Text style={styles.highlightText}>{item.payment_day}.</Text> günü
          </Text>
        </View>
        {item.category_name && (
          <View style={styles.salaryItemCategory}>
            <Text style={styles.salaryItemCategoryText}>
              {item.category_name}
            </Text>
          </View>
        )}
        
        {item.notes && (
          <View style={styles.salaryItemNotes}>
            <MaterialIcons name="description" size={12} color={Colors.GRAY_500} style={{ marginRight: 4 }} />
            <Text style={styles.salaryItemNotesText} numberOfLines={1} ellipsizeMode="tail">
              {item.notes}
            </Text>
          </View>
        )}
      </View>
      
      {/* Sağ bölüm - tutar ve işlemler */}
      <View style={styles.salaryItemRightSection}>
        <CurrencyEquivalent
          amount={item.amount}
          currency={item.currency}
          usdEquivalent={item.usd_equivalent}
          eurEquivalent={item.eur_equivalent}
          customCurrency={item.custom_currency}
          customEquivalent={item.custom_equivalent}
          size="small"
        />
        
        <View style={styles.salaryItemActions}>
          <TouchableOpacity
            style={styles.salaryItemAction}
            onPress={() => editSalary(item)}
            accessibilityLabel="Maaşı düzenle"
          >
            <MaterialIcons name="edit" size={18} color={Colors.PRIMARY} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.salaryItemAction}
            onPress={() => confirmDeleteSalary(item)}
            accessibilityLabel="Maaşı sil"
          >
            <MaterialIcons name="delete" size={18} color={Colors.DANGER} />
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  ), []);

  // Yaklaşan ödeme öğesi render fonksiyonu - modernize edildi
  const renderUpcomingPaymentItem = ({ item }) => {
    // Ödeme tarihini JavaScript Date nesnesine çevir
    const paymentDate = new Date(item.payment_date);
    
    // Bugüne kalan gün sayısını hesapla
    const today = new Date();
    const diffTime = paymentDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    // Gün bilgisi metni oluştur
    let daysText = '';
    let statusColor = Colors.PRIMARY;
    
    if (diffDays < 0) {
      daysText = `${Math.abs(diffDays)} gün geçti`;
      statusColor = Colors.DANGER;
    } else if (diffDays === 0) {
      daysText = 'Bugün';
      statusColor = Colors.WARNING;
    } else if (diffDays === 1) {
      daysText = 'Yarın';
      statusColor = Colors.WARNING;
    } else if (diffDays <= 3) {
      daysText = `${diffDays} gün sonra`;
      statusColor = Colors.WARNING;
    } else {
      daysText = `${diffDays} gün sonra`;
      statusColor = Colors.PRIMARY;
    }
    
    // Tarih rengi belirle
    const dateStyle = {
      color: statusColor,
      fontWeight: diffDays <= 3 ? 'bold' : 'normal'
    };
    
    // Icon adını belirle
    const iconName = diffDays < 0 ? "error-outline" : 
                      diffDays <= 1 ? "alarm-on" : 
                      diffDays <= 3 ? "timelapse" : "event-available";
    
    return (
      <View style={[
        styles.upcomingPaymentItem,
        { borderLeftColor: statusColor }
      ]}>
        {/* Sol taraf - tarih bilgisi */}
        <View style={[
          styles.upcomingPaymentDateContainer,
          { backgroundColor: statusColor + '15' }
        ]}>
          <Text style={[styles.upcomingPaymentDayNumber, { color: statusColor }]}>
            {paymentDate.getDate()}
          </Text>
          <Text style={styles.upcomingPaymentMonth}>
            {paymentDate.toLocaleString('tr-TR', { month: 'short' })}
          </Text>
          <Text style={styles.upcomingPaymentYear}>
            {paymentDate.getFullYear()}
          </Text>
        </View>
        
        {/* Orta kısım - maaş bilgisi */}
        <View style={styles.upcomingPaymentInfo}>
          <Text style={styles.upcomingPaymentName}>{item.salary_name}</Text>
          <View style={styles.upcomingPaymentTimeContainer}>
            <MaterialIcons 
              name={iconName}
              size={14} 
              color={statusColor} 
              style={{ marginRight: 4 }} 
            />
            <Text style={[styles.upcomingPaymentTimeLeft, dateStyle]}>
              {daysText}
            </Text>
          </View>
        </View>

        {/* Sağ taraf - tutar ve onay butonu */}
        <View style={styles.upcomingPaymentAmount}>
          <CurrencyEquivalent
            amount={item.amount}
            currency={item.currency}
            usdEquivalent={item.usd_equivalent}
            eurEquivalent={item.eur_equivalent}
            customCurrency={item.custom_currency}
            customEquivalent={item.custom_equivalent}
            size="small"
            style={styles.upcomingPaymentCurrency}
          />
          <TouchableOpacity
            style={[styles.upcomingPaymentActionButton, { backgroundColor: statusColor }]}
            onPress={() => confirmProcessPayment(item)}
            accessibilityLabel="Ödemeyi işle"
          >
            <MaterialIcons name="check-circle" size={20} color="#fff" />
            <Text style={styles.upcomingPaymentActionText}>İşle</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };
  
  // Düzenli gelir öğesi render fonksiyonu
  const renderRegularIncomeItem = ({ item }) => {
    // Duruma göre renk ve ikon belirle
    const { color: statusColor, icon: statusIcon, text: statusText } = getStatusInfo(item.status);
    
    // Tekrarlama tipine göre metin belirle
    let recurrenceText = '';
    switch (item.recurrence_type) {
      case 'daily':
        recurrenceText = item.recurrence_interval > 1 
          ? `Her ${item.recurrence_interval} günde bir` 
          : 'Her gün';
        break;
      case 'weekly':
        recurrenceText = item.recurrence_interval > 1 
          ? `Her ${item.recurrence_interval} haftada bir` 
          : 'Her hafta';
        break;
      case 'monthly':
        recurrenceText = item.recurrence_interval > 1 
          ? `Her ${item.recurrence_interval} ayda bir` 
          : `Her ayın ${item.payment_day || '?'}. günü`;
        break;
      case 'yearly':
        recurrenceText = item.recurrence_interval > 1 
          ? `Her ${item.recurrence_interval} yılda bir` 
          : 'Her yıl';
        break;
      case 'custom':
        recurrenceText = `Her ${item.recurrence_interval || '?'} günde bir`;
        break;
      default:
        recurrenceText = 'Düzenli';
    }
    
    // Sonraki ödeme tarihi için kalan gün hesabı
    const nextPaymentDate = new Date(item.next_payment_date);
    const today = new Date();
    const diffTime = nextPaymentDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    let paymentDateText = '';
    if (diffDays < 0) {
      paymentDateText = `${Math.abs(diffDays)} gün gecikti`;
    } else if (diffDays === 0) {
      paymentDateText = 'Bugün';
    } else if (diffDays === 1) {
      paymentDateText = 'Yarın';
    } else {
      paymentDateText = `${diffDays} gün sonra`;
    }
    
    return (
      <View style={styles.regularIncomeItem}>
        <TouchableOpacity 
          onPress={() => toggleIncomeStatus(item)}
          style={[styles.regularIncomeStatus, { backgroundColor: statusColor }]}
        >
          <MaterialIcons name={statusIcon} size={16} color="#fff" />
          <Text style={styles.regularIncomeStatusText}>{statusText}</Text>
        </TouchableOpacity>

        <View style={styles.regularIncomeHeader}>
          <View style={styles.regularIncomeInfo}>
            <Text style={styles.regularIncomeName}>{item.title}</Text>
            <View style={styles.regularIncomeRecurrence}>
              <MaterialIcons name="repeat" size={14} color={Colors.GRAY_600} />
              <Text style={styles.regularIncomeRecurrenceText}>
                {recurrenceText}
              </Text>
            </View>
          </View>
          
          <View style={styles.regularIncomeActions}>
            <TouchableOpacity
              style={styles.regularIncomeAction}
              onPress={() => editRegularIncome(item.id)}
            >
              <MaterialIcons name="edit" size={20} color={Colors.PRIMARY} />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.regularIncomeAction}
              onPress={() => confirmDeleteRegularIncome(item)}
            >
              <MaterialIcons name="delete" size={20} color={Colors.DANGER} />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.regularIncomeDetails}>
          <View style={styles.regularIncomeAmount}>
            <Text style={styles.regularIncomeAmountLabel}>Tutar:</Text>
            <Text style={styles.regularIncomeAmountValue}>
              {formatCurrency(item.amount, item.currency_code)}
            </Text>
          </View>

          <View style={styles.regularIncomeNextPayment}>
            <Text style={styles.regularIncomeNextPaymentLabel}>Sonraki Ödeme:</Text>
            <View style={styles.regularIncomeNextPaymentInfo}>
              <Text style={styles.regularIncomeNextPaymentValue}>
                {nextPaymentDate.toLocaleDateString('tr-TR')}
              </Text>
              <Text style={[
                styles.regularIncomeNextPaymentDate, 
                diffDays < 0 ? styles.regularIncomeOverdue : 
                diffDays <= 3 ? styles.regularIncomeUpcoming : {}
              ]}>
                ({paymentDateText})
              </Text>
            </View>
          </View>
          
          {item.notes && (
            <View style={styles.regularIncomeNotes}>
              <Text style={styles.regularIncomeNotesLabel}>Notlar:</Text>
              <Text style={styles.regularIncomeNotesValue}>{item.notes}</Text>
            </View>
          )}
          
          {item.notification_settings?.enabled && (
            <View style={styles.regularIncomeNotification}>
              <MaterialIcons name="notifications-active" size={14} color={Colors.WARNING} />
              <Text style={styles.regularIncomeNotificationText}>
                {item.notification_settings.days_before} gün önce bildirim
              </Text>
            </View>
          )}
        </View>
      </View>
    );
  };

  // Düzenli gelir durumunu değiştir (aktif -> duraklatıldı -> sonlandırıldı -> aktif)
  const toggleIncomeStatus = async (income) => {
    try {
      // Durumu döngüsel olarak değiştir
      let newStatus;
      switch (income.status) {
        case 'active':
          newStatus = 'paused';
          break;
        case 'paused':
          newStatus = 'ended';
          break;
        case 'ended':
          newStatus = 'active';
          break;
        default:
          newStatus = 'active';
      }

      // Geliri güncelle
      await updateRegularIncome({ id: income.id, status: newStatus });
      
      // Kullanıcıya bilgi ver
      const statusMessages = {
        active: 'Düzenli gelir aktif duruma getirildi.',
        paused: 'Düzenli gelir duraklatıldı.',
        ended: 'Düzenli gelir sonlandırıldı.',
      };
      
      Alert.alert('Durum Güncellendi', statusMessages[newStatus]);
      
      // Verileri yenile
      loadData();
    } catch (error) {
      console.error('Düzenli gelir durumu güncelleme hatası:', error);
      Alert.alert('Hata', 'Durum güncellenirken bir hata oluştu.');
    }
  };
  
  // Durum bilgilerini getir (renk, ikon ve metin)
  const getStatusInfo = (status) => {
    switch (status) {
      case 'active':
        return {
          color: Colors.SUCCESS,
          icon: 'check-circle',
          text: 'Aktif'
        };
      case 'paused':
        return {
          color: Colors.WARNING,
          icon: 'pause-circle-filled',
          text: 'Duraklatıldı'
        };
      case 'ended':
        return {
          color: Colors.GRAY_500,
          icon: 'cancel',
          text: 'Sonlandırıldı'
        };
      default:
        return {
          color: Colors.PRIMARY,
          icon: 'help',
          text: 'Bilinmiyor'
        };
    }
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={styles.loadingText}>Maaş verileri yükleniyor...</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      {/* Modern başlık */}
      <View style={[styles.header, { backgroundColor: theme.PRIMARY }]}>
        <Text style={[styles.headerTitle, { color: theme.WHITE }]}>Maaşlarım</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={[styles.headerAction, { backgroundColor: 'rgba(255, 255, 255, 0.3)' }]}
            onPress={generateFuturePayments}
            accessibilityLabel="Ödemeleri güncelle"
          >
            <MaterialIcons name="event-note" size={22} color={theme.WHITE} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerAction}
            onPress={() => navigation.navigate('stats')}
            accessibilityLabel="Gelir istatistikleri"
          >
            <MaterialIcons name="bar-chart" size={22} color={theme.WHITE} />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView
          style={styles.content}
          contentContainerStyle={styles.contentContainer}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[Colors.PRIMARY]}
            />
          }
        >
          {/* Özet kartı - modern tasarım */}
          <View style={[styles.summaryCard, { backgroundColor: theme.SURFACE }]}>
            <View style={[styles.summaryHeader, { borderBottomColor: theme.BORDER }]}>
              <View style={styles.summaryHeaderContent}>
                <MaterialIcons name="dashboard" size={20} color={Colors.PRIMARY} />
                <Text style={[styles.summaryTitle, { color: theme.TEXT_PRIMARY }]}>Gelir Özeti</Text>
              </View>
              <View style={[styles.summaryBadge, { backgroundColor: Colors.PRIMARY + '15' }]}>
                <Text style={styles.summaryBadgeText}>
                  Toplam: {salaries.filter(s => s.is_active === 1).length + regularIncomes.filter(i => i.status === 'active').length}
                </Text>
              </View>
            </View>
            <View style={styles.summaryContent}>
              <TouchableOpacity
                style={[styles.summaryItem, { backgroundColor: theme.SURFACE, borderColor: theme.BORDER }, activeTab === 'salaries' && styles.activeTabItem]}
                onPress={() => setActiveTab('salaries')}
              >
                <View style={[styles.summaryIconContainer, { backgroundColor: theme.BACKGROUND }]}>
                  <MaterialIcons name="account-balance-wallet" size={22} color={Colors.SUCCESS} />
                </View>
                <Text style={[styles.summaryValue, { color: theme.TEXT_PRIMARY }]}>{salaries.filter(s => s.is_active === 1).length}</Text>
                <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>Aktif Maaşlar</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.summaryItem, { backgroundColor: theme.SURFACE, borderColor: theme.BORDER }, activeTab === 'incomes' && styles.activeTabItem]}
                onPress={() => setActiveTab('incomes')}
              >
                <View style={[styles.summaryIconContainer, { backgroundColor: theme.BACKGROUND }]}>
                  <MaterialIcons name="repeat" size={22} color={Colors.PRIMARY} />
                </View>
                <Text style={[styles.summaryValue, { color: theme.TEXT_PRIMARY }]}>{regularIncomes.filter(i => i.status === 'active').length}</Text>
                <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>Düzenli Gelirler</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.summaryItem, { backgroundColor: theme.SURFACE, borderColor: theme.BORDER }, activeTab === 'upcoming' && styles.activeTabItem]}
                onPress={() => setActiveTab('upcoming')}
              >
                <View style={[styles.summaryIconContainer, { backgroundColor: theme.BACKGROUND }]}>
                  <MaterialIcons name="event" size={22} color={Colors.WARNING} />
                </View>
                <Text style={[styles.summaryValue, { color: theme.TEXT_PRIMARY }]}>{upcomingPayments.length}</Text>
                <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>Yaklaşan Ödemeler</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Toplam gelir kartı */}
          <View style={[styles.totalIncomeCard, { backgroundColor: Colors.PRIMARY }]}>
            <View style={styles.totalIncomeHeader}>
              <MaterialIcons name="monetization-on" size={22} color="rgba(255, 255, 255, 0.9)" />
              <Text style={[styles.totalIncomeTitle, { color: theme.WHITE }]}>Toplam Aylık Gelir</Text>
            </View>
            {calculateTotalRegularIncome().breakdown.length > 0 ? (
              <View style={styles.currencyBreakdown}>
                {calculateTotalRegularIncome().breakdown.map((item, index) => (
                  <View key={index} style={styles.currencyItem}>
                    <Text style={[styles.totalIncomeValue, { color: theme.WHITE }]}>
                      {formatCurrency(item.amount, item.currency)}
                    </Text>
                    <Text style={[styles.currencyLabel, { color: 'rgba(255, 255, 255, 0.8)' }]}>
                      {item.currency === 'TRY' ? 'Türk Lirası' :
                        item.currency === 'USD' ? 'ABD Doları' :
                        item.currency === 'EUR' ? 'Euro' : item.currency}
                    </Text>
                  </View>
                ))}
              </View>
            ) : (
              <View style={styles.noIncomePlaceholder}>
                <Text style={[styles.totalIncomeValue, { color: theme.WHITE }]}>
                  {formatCurrency(0, 'TRY')}
                </Text>
                <Text style={[styles.noIncomeText, { color: 'rgba(255, 255, 255, 0.7)' }]}>
                  Henüz bir gelir kaydınız bulunmuyor
                </Text>
              </View>
            )}
          </View>

          {/* Yaklaşan Ödemeler - geliştirilmiş ve tab-tabanlı gösterim */}
          {activeTab === 'upcoming' && (
            <View style={[styles.upcomingPaymentsCard, { backgroundColor: theme.SURFACE }]}>
              <View style={styles.cardHeader}>
                <View style={styles.headerTitleContainer}>
                  <MaterialIcons name="date-range" size={22} color={Colors.PRIMARY} />
                  <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>Yaklaşan Ödemeler</Text>
                </View>
                <View style={[styles.statsChip, { backgroundColor: Colors.PRIMARY + '15' }]}>
                  <Text style={[styles.statsChipText, { color: Colors.PRIMARY }]}>
                    {upcomingPayments.length} ödeme
                  </Text>
                </View>
              </View>

              {upcomingPayments.length === 0 ? (
                <View style={styles.emptyState}>
                  <View style={styles.emptyStateIconContainer}>
                    <MaterialIcons name="event-busy" size={64} color={Colors.PRIMARY + '80'} />
                  </View>
                  <Text style={[styles.emptyStateTitle, { color: theme.TEXT_PRIMARY }]}>Yaklaşan Ödeme Bulunamadı</Text>
                  <Text style={[styles.emptyStateText, { color: theme.TEXT_SECONDARY }]}>
                    Henüz yaklaşan bir ödemeniz bulunmamaktadır. Yeni bir düzenli gelir ekleyerek ödeme planını oluşturabilirsiniz.
                  </Text>

                  <View style={styles.emptyStateActions}>
                    <TouchableOpacity
                      style={[styles.emptyStateButton, { backgroundColor: Colors.PRIMARY }]}
                      onPress={generateFuturePayments}
                      accessibilityLabel="Ödemeleri oluştur"
                    >
                      <MaterialIcons name="update" size={20} color="#fff" />
                      <Text style={[styles.emptyStateButtonText, { color: theme.WHITE }]}>Gelecek Ödemeleri Oluştur</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[styles.emptyStateButton, { backgroundColor: Colors.SUCCESS, marginTop: 10 }]}
                      onPress={() => navigation.navigate('AddRegularIncome')}
                      accessibilityLabel="Yeni düzenli gelir ekle"
                    >
                      <MaterialIcons name="add" size={20} color="#fff" />
                      <Text style={[styles.emptyStateButtonText, { color: theme.WHITE }]}>Yeni Düzenli Gelir Ekle</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              ) : (
                <View style={styles.upcomingPaymentsList}>
                  {upcomingPayments.map((item) => (
                    <View key={item.id} style={styles.upcomingPaymentItem}>
                      {renderUpcomingPaymentItem({ item })}
                    </View>
                  ))}
                </View>
              )}
            </View>
          )}

          {/* Filtreler - geliştirilmiş segmented kontrol */}
          <View style={[styles.filterCard, { backgroundColor: theme.SURFACE }]}>
            <View style={styles.filterHeader}>
              <View style={styles.filterHeadingContainer}>
                <MaterialIcons name="filter-list" size={18} color={Colors.PRIMARY} />
                <Text style={[styles.filterHeading, { color: theme.TEXT_PRIMARY }]}>Filtreler</Text>
              </View>

              <TouchableOpacity
                style={styles.sortButtonContainer}
                onPress={() => {
                  const nextSort = sortBy === 'date' ? 'amount' :
                                  sortBy === 'amount' ? 'name' : 'date';
                  setSortBy(nextSort);
                }}
              >
                <MaterialIcons
                  name={
                    sortBy === 'date' ? 'date-range' :
                    sortBy === 'amount' ? 'attach-money' : 'sort-by-alpha'
                  }
                  size={18}
                  color={Colors.PRIMARY}
                />
                <Text style={[styles.sortButtonText, { color: theme.TEXT_SECONDARY }]}>
                  {sortBy === 'date' ? 'Tarihe Göre' :
                   sortBy === 'amount' ? 'Tutara Göre' : 'İsme Göre'}
                </Text>
              </TouchableOpacity>
            </View>

                    {/* Tab'a göre filtreler gösterilir */}
            
            {/* Tab'a göre filtreler gösterilir */}
            {activeTab === 'salaries' && (
                        <View style={styles.segmentedControlContainer}>
                            <TouchableOpacity
                                style={[styles.segmentButton, { backgroundColor: theme.BACKGROUND, borderColor: theme.BORDER }, filter === 'all' && styles.activeSegment]}
                                onPress={() => setFilter('all')}
                            >
                                <Text style={[styles.segmentText, { color: theme.TEXT_SECONDARY }, filter === 'all' && styles.activeSegmentText]}>
                                    Tümü
                                </Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={[styles.segmentButton, { backgroundColor: theme.BACKGROUND, borderColor: theme.BORDER }, filter === 'active' && styles.activeSegment]}
                                onPress={() => setFilter('active')}
                            >
                                <Text style={[styles.segmentText, { color: theme.TEXT_SECONDARY }, filter === 'active' && styles.activeSegmentText]}>
                                    Aktif
                                </Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={[styles.segmentButton, { backgroundColor: theme.BACKGROUND, borderColor: theme.BORDER }, filter === 'inactive' && styles.activeSegment]}
                                onPress={() => setFilter('inactive')}
                            >
                                <Text style={[styles.segmentText, { color: theme.TEXT_SECONDARY }, filter === 'inactive' && styles.activeSegmentText]}>
                                    Pasif
                                </Text>
                            </TouchableOpacity>
                        </View>
                    )}

                    {/* Düzenli Gelirler filtreleme */}
                    {activeTab === 'incomes' && (
                        <View style={styles.segmentedControlContainer}>
                            <TouchableOpacity
                                style={[styles.segmentButton, incomeFilter === 'all' && styles.activeSegment]}
                                onPress={() => setIncomeFilter('all')}
                            >
                                <Text style={[styles.segmentText, incomeFilter === 'all' && styles.activeSegmentText]}>
                                    Tümü
                                </Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={[styles.segmentButton, incomeFilter === 'active' && styles.activeSegment]}
                                onPress={() => setIncomeFilter('active')}
                            >
                                <Text style={[styles.segmentText, incomeFilter === 'active' && styles.activeSegmentText]}>
                                    Aktif
                                </Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={[styles.segmentButton, incomeFilter === 'paused' && styles.activeSegment]}
                                onPress={() => setIncomeFilter('paused')}
                            >
                                <Text style={[styles.segmentText, incomeFilter === 'paused' && styles.activeSegmentText]}>
                                    Duraklatılmış
                                </Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={[styles.segmentButton, incomeFilter === 'ended' && styles.activeSegment]}
                                onPress={() => setIncomeFilter('ended')}
                            >
                                <Text style={[styles.segmentText, incomeFilter === 'ended' && styles.activeSegmentText]}>
                                    Sonlanmış
                                </Text>
                            </TouchableOpacity>
                        </View>
                    )}

                    {/* Yaklaşan Ödemeler filtreleme */}
                    {activeTab === 'upcoming' && (
                        <View style={styles.upcomingFilterContainer}>
                            <Text style={styles.upcomingFilterLabel}>Tüm ödemeleri göster</Text>
                            <Switch
                                value={showAllUpcomingPayments}
                                onValueChange={setShowAllUpcomingPayments}
                                trackColor={{ false: Colors.GRAY_300, true: Colors.PRIMARY + '80' }}
                                thumbColor={showAllUpcomingPayments ? Colors.PRIMARY : Colors.GRAY_400}
                            />
                        </View>
                    )}
                </View>

                {/* Tab içeriği burada koşullu olarak görüntüleyeceğiz */}
                {activeTab === 'salaries' && (
                    <View style={styles.salaryListContainer}>
                        {filteredSalaries.length === 0 ? (
                            <View style={styles.emptyState}>
                                <MaterialIcons name="work-off" size={64} color={Colors.GRAY_400} />
                                <Text style={styles.emptyStateTitle}>Maaş Kaydı Bulunamadı</Text>
                                <Text style={styles.emptyStateText}>
                                    Henüz maaş kaydı bulunmuyor. Yeni maaş eklemek için sağ üstteki butonu kullanabilirsiniz.
                                </Text>
                                <TouchableOpacity style={styles.addButton} onPress={addNewSalary}>
                                    <MaterialIcons name="add" size={20} color="#fff" />
                                    <Text style={styles.addButtonText}>Yeni Maaş Ekle</Text>
                                </TouchableOpacity>
                            </View>
                        ) : (
                            <View>
                                {filteredSalaries.map(salary => (
                                    <View key={salary.id} style={styles.salaryItemContainer}>
                                        {renderSalaryItem({ item: salary })}
                                    </View>
                                ))}
                            </View>
                        )}
                    </View>
                )}

                {/* Düzenli Gelirler Tab İçeriği */}
                {activeTab === 'incomes' && (
                    <View style={styles.regularIncomesContainer}>
                        {filteredRegularIncomes.length === 0 ? (
                            <View style={styles.emptyState}>
                                <MaterialIcons name="payments" size={64} color={Colors.GRAY_400} />
                                <Text style={styles.emptyStateTitle}>Düzenli Gelir Bulunamadı</Text>
                                <Text style={styles.emptyStateText}>
                                    Henüz düzenli gelir kaydı bulunmuyor. Yeni düzenli gelir eklemek için sağ üstteki butonu kullanabilirsiniz.
                                </Text>
                                <TouchableOpacity style={styles.addButton} onPress={addNewRegularIncome}>
                                    <MaterialIcons name="add" size={20} color="#fff" />
                                    <Text style={styles.addButtonText}>Yeni Düzenli Gelir Ekle</Text>
                                </TouchableOpacity>
                            </View>
                        ) : (
                            <View>
                                {filteredRegularIncomes.map(income => (
                                    <View key={income.id} style={styles.incomeItemContainer}>
                                        {renderRegularIncomeItem({ item: income })}
                                    </View>
                                ))}
                            </View>
                        )}
                    </View>
                )}
            </ScrollView>

            {/* Hızlı Ekleme Butonu */}
            <TouchableOpacity
                style={styles.fab}
                onPress={addNewSalary}
                accessibilityLabel="Yeni maaş ekle"
            >
                <View style={styles.fabInner}>
                    <MaterialIcons name="add" size={28} color="#fff" />
                </View>
                <Text style={styles.fabLabel}>Yeni Düzenli Gelir</Text>
            </TouchableOpacity>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f8f9fa',
    },
    centerContent: {
        justifyContent: 'center',
        alignItems: 'center',
    },
    loadingText: {
        marginTop: 10,
        fontSize: 16,
        color: Colors.GRAY_600,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 20,
        paddingVertical: 18,
        backgroundColor: Colors.PRIMARY,
        elevation: 4,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 2,
    },
    headerTitle: {
        flex: 1,
        fontSize: 22,
        fontWeight: '700',
        color: '#fff',
        letterSpacing: 0.5,
    },
    headerActions: {
        flexDirection: 'row',
    },
    headerAction: {
        width: 40,
        height: 40,
        borderRadius: 20,
        backgroundColor: 'rgba(255, 255, 255, 0.2)',
        justifyContent: 'center',
        alignItems: 'center',
        marginLeft: 12,
        shadowColor: 'rgba(0, 0, 0, 0.2)',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.3,
        shadowRadius: 2,
        elevation: 2,
    },
    content: {
        flex: 1,
    },
    contentContainer: {
        paddingHorizontal: 16,
        paddingVertical: 16,
        paddingBottom: 60, // FAB için ekstra padding
    },
    summaryCard: {
        backgroundColor: Colors.WHITE,
        borderRadius: 16,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.08,
        shadowRadius: 8,
        elevation: 4,
        margin: 16,
        marginTop: 8,
        overflow: 'hidden',
    },
    summaryHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: Colors.GRAY_100,
    },
    summaryHeaderContent: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    summaryTitle: {
        fontSize: 16,
        fontWeight: '600',
        marginLeft: 8,
        color: Colors.GRAY_800,
    },
    summaryBadge: {
        backgroundColor: Colors.PRIMARY + '15',
        paddingHorizontal: 10,
        paddingVertical: 4,
        borderRadius: 20,
    },
    summaryBadgeText: {
        fontSize: 12,
        fontWeight: '500',
        color: Colors.PRIMARY,
    },
    summaryContent: {
        flexDirection: 'row',
        paddingVertical: 4,
        paddingHorizontal: 8,
    },
    summaryItem: {
        flex: 1,
        padding: 16,
        alignItems: 'center',
        justifyContent: 'center',
        marginHorizontal: 4,
        borderRadius: 12,
        backgroundColor: Colors.WHITE,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.05,
        shadowRadius: 3,
        elevation: 1,
        borderWidth: 1,
        borderColor: Colors.GRAY_100,
    },
    summaryIconContainer: {
        width: 40,
        height: 40,
        borderRadius: 20,
        backgroundColor: Colors.GRAY_50,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 8,
    },
    summaryValue: {
        fontSize: 22,
        fontWeight: '700',
        color: Colors.GRAY_800,
        marginVertical: 4,
    },
    summaryLabel: {
        fontSize: 12,
        color: Colors.GRAY_600,
        textAlign: 'center',
    },
    // Aktif tab stili
    activeTabItem: {
        borderWidth: 2,
        borderColor: Colors.PRIMARY,
        transform: [{ scale: 1.05 }],
        shadowColor: Colors.PRIMARY,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 3,
        elevation: 3,
    },
    totalIncomeCard: {
        marginHorizontal: 16,
        marginBottom: 16,
        backgroundColor: Colors.PRIMARY,
        borderRadius: 16,
        padding: 20,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 8,
        elevation: 6,
    },
    totalIncomeHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 16,
    },
    totalIncomeTitle: {
        fontSize: 16,
        fontWeight: '600',
        color: 'rgba(255, 255, 255, 0.9)',
        marginLeft: 8,
    },
    currencyBreakdown: {
        gap: 12,
    },
    currencyItem: {
        alignItems: 'flex-start',
    },
    totalIncomeValue: {
        fontSize: 28,
        fontWeight: '700',
        color: '#fff',
        marginBottom: 4,
    },
    currencyLabel: {
        fontSize: 12,
        color: 'rgba(255, 255, 255, 0.8)',
        fontWeight: '500',
    },
    noIncomePlaceholder: {
        alignItems: 'center',
    },
    noIncomeText: {
        fontSize: 14,
        color: 'rgba(255, 255, 255, 0.7)',
        textAlign: 'center',
    },
    upcomingPaymentsCard: {
        backgroundColor: Colors.WHITE,
        borderRadius: 16,
        marginHorizontal: 16,
        marginBottom: 16,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.08,
        shadowRadius: 8,
        elevation: 4,
    },
    filterCard: {
        backgroundColor: Colors.WHITE,
        borderRadius: 12,
        marginHorizontal: 16,
        marginBottom: 16,
        paddingVertical: 16,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.05,
        shadowRadius: 4,
        elevation: 2,
    },
    filterHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 16,
        marginBottom: 12,
    },
    filterHeadingContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    filterHeading: {
        fontSize: 16,
        fontWeight: '600',
        color: Colors.GRAY_800,
        marginLeft: 8,
    },
    sortButtonContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: Colors.GRAY_100,
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderRadius: 8,
    },
    sortButtonText: {
        fontSize: 13,
        fontWeight: '500',
        color: Colors.GRAY_700,
        marginLeft: 6,
    },
    segmentedControlContainer: {
        flexDirection: 'row',
        marginBottom: 15,
        borderRadius: 10,
        backgroundColor: Colors.GRAY_100,
        padding: 4,
        marginHorizontal: 16,
    },
    segmentButton: {
        flex: 1,
        paddingVertical: 8,
        paddingHorizontal: 12,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 8,
    },
    activeSegment: {
        backgroundColor: Colors.WHITE,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
    },
    segmentText: {
        fontSize: 13,
        fontWeight: '500',
        color: Colors.GRAY_700,
    },
    activeSegmentText: {
        color: Colors.PRIMARY,
        fontWeight: '600',
    },
    upcomingFilterContainer: {
        marginHorizontal: 16,
        marginVertical: 10,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    upcomingFilterLabel: {
        fontSize: 14,
        color: Colors.GRAY_700,
    },
    // Yeni eklenen stiller - modern UI için
    salaryListContainer: {
        marginHorizontal: 16,
        marginBottom: 20,
    },
    regularIncomesContainer: {
        marginHorizontal: 16,
        marginBottom: 20,
    },
    salaryItemContainer: {
        marginBottom: 12,
    },
    incomeItemContainer: {
        marginBottom: 12,
        backgroundColor: Colors.WHITE,
        borderRadius: 12,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.05,
        shadowRadius: 4,
        elevation: 2,
    },
    cardHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: Colors.GRAY_200,
    },
    headerTitleContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    sectionTitle: {
        fontSize: 16,
        fontWeight: '600',
        color: Colors.GRAY_800,
        marginLeft: 8,
    },
    statsChip: {
        backgroundColor: Colors.PRIMARY + '15',
        paddingHorizontal: 10,
        paddingVertical: 4,
        borderRadius: 20,
    },
    statsChipText: {
        fontSize: 12,
        fontWeight: '500',
        color: Colors.PRIMARY,
    },
    emptyState: {
        backgroundColor: Colors.WHITE,
        borderRadius: 12,
        padding: 24,
        alignItems: 'center',
        marginVertical: 20,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.05,
        shadowRadius: 8,
        elevation: 2,
    },
    emptyStateIconContainer: {
        width: 100,
        height: 100,
        borderRadius: 50,
        backgroundColor: Colors.GRAY_50,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 16,
    },
    emptyStateTitle: {
        fontSize: 18,
        fontWeight: '600',
        color: Colors.GRAY_800,
        marginVertical: 12,
    },
    emptyStateText: {
        fontSize: 14,
        color: Colors.GRAY_600,
        textAlign: 'center',
        lineHeight: 20,
        marginBottom: 20,
        paddingHorizontal: 10,
    },
    emptyStateButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: Colors.PRIMARY,
        paddingVertical: 10,
        paddingHorizontal: 16,
        borderRadius: 8,
        marginTop: 8,
    },
    emptyStateButtonText: {
        color: Colors.WHITE,
        fontWeight: '500',
        fontSize: 14,
        marginLeft: 8,
    },
    addButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: Colors.SUCCESS,
        paddingVertical: 12,
        paddingHorizontal: 16,
        borderRadius: 8,
        marginTop: 10,
    },
    addButtonText: {
        color: Colors.WHITE,
        fontWeight: '500',
        fontSize: 14,
        marginLeft: 8,
    },
    emptyStateActions: {
        width: '100%',
        alignItems: 'center',
    },
    // FAB styles
    fab: {
        position: 'absolute',
        bottom: 20,
        right: 20,
        backgroundColor: Colors.PRIMARY,
        borderRadius: 30,
        elevation: 8,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        alignItems: 'center',
        minWidth: 60,
        paddingVertical: 8,
    },
    fabInner: {
        width: 56,
        height: 56,
        borderRadius: 28,
        backgroundColor: Colors.PRIMARY,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 4,
    },
    fabLabel: {
        fontSize: 10,
        color: '#fff',
        fontWeight: '600',
        textAlign: 'center',
        paddingHorizontal: 8,
    },
    // Salary item styles
    salaryItem: {
        backgroundColor: Colors.WHITE,
        borderRadius: 12,
        padding: 16,
        flexDirection: 'row',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.05,
        shadowRadius: 4,
        elevation: 2,
        marginBottom: 8,
    },
    inactiveSalaryItem: {
        opacity: 0.6,
        backgroundColor: Colors.GRAY_50,
    },
    salaryItemLeftSection: {
        alignItems: 'center',
        marginRight: 12,
    },
    salaryItemIconContainer: {
        width: 44,
        height: 44,
        borderRadius: 22,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 6,
    },
    statusBadge: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 6,
        paddingVertical: 2,
        borderRadius: 10,
        minWidth: 50,
    },
    activeBadge: {
        backgroundColor: Colors.SUCCESS,
    },
    inactiveBadge: {
        backgroundColor: Colors.GRAY_500,
    },
    statusIcon: {
        marginRight: 2,
    },
    statusText: {
        fontSize: 10,
        color: '#fff',
        fontWeight: '600',
    },
    salaryItemMiddleSection: {
        flex: 1,
        marginRight: 12,
    },
    salaryItemName: {
        fontSize: 16,
        fontWeight: '600',
        color: Colors.GRAY_800,
        marginBottom: 4,
    },
    salaryItemPaymentInfo: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 4,
    },
    salaryItemPaymentDate: {
        fontSize: 13,
        color: Colors.GRAY_600,
    },
    highlightText: {
        fontWeight: '700',
        color: Colors.PRIMARY,
    },
    salaryItemCategory: {
        marginBottom: 4,
    },
    salaryItemCategoryText: {
        fontSize: 12,
        color: Colors.GRAY_500,
        fontStyle: 'italic',
    },
    salaryItemNotes: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    salaryItemNotesText: {
        fontSize: 12,
        color: Colors.GRAY_500,
        flex: 1,
    },
    salaryItemRightSection: {
        alignItems: 'flex-end',
    },
    salaryItemActions: {
        flexDirection: 'row',
        marginTop: 8,
    },
    salaryItemAction: {
        padding: 6,
        marginLeft: 4,
    },
    // Regular income styles
    regularIncomeItem: {
        backgroundColor: Colors.WHITE,
        borderRadius: 12,
        padding: 16,
        marginBottom: 12,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.05,
        shadowRadius: 4,
        elevation: 2,
    },
    regularIncomeStatus: {
        flexDirection: 'row',
        alignItems: 'center',
        alignSelf: 'flex-start',
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
        marginBottom: 12,
    },
    regularIncomeStatusText: {
        fontSize: 12,
        color: '#fff',
        fontWeight: '600',
        marginLeft: 4,
    },
    regularIncomeHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: 12,
    },
    regularIncomeInfo: {
        flex: 1,
    },
    regularIncomeName: {
        fontSize: 16,
        fontWeight: '600',
        color: Colors.GRAY_800,
        marginBottom: 4,
    },
    regularIncomeRecurrence: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    regularIncomeRecurrenceText: {
        fontSize: 13,
        color: Colors.GRAY_600,
        marginLeft: 4,
    },
    regularIncomeActions: {
        flexDirection: 'row',
    },
    regularIncomeAction: {
        padding: 6,
        marginLeft: 4,
    },
    regularIncomeDetails: {
        gap: 8,
    },
    regularIncomeAmount: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    regularIncomeAmountLabel: {
        fontSize: 14,
        color: Colors.GRAY_600,
    },
    regularIncomeAmountValue: {
        fontSize: 16,
        fontWeight: '600',
        color: Colors.SUCCESS,
    },
    regularIncomeNextPayment: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    regularIncomeNextPaymentLabel: {
        fontSize: 14,
        color: Colors.GRAY_600,
    },
    regularIncomeNextPaymentInfo: {
        alignItems: 'flex-end',
    },
    regularIncomeNextPaymentValue: {
        fontSize: 14,
        fontWeight: '600',
        color: Colors.GRAY_800,
    },
    regularIncomeNextPaymentDate: {
        fontSize: 12,
        color: Colors.GRAY_500,
    },
    regularIncomeOverdue: {
        color: Colors.DANGER,
        fontWeight: '600',
    },
    regularIncomeUpcoming: {
        color: Colors.WARNING,
        fontWeight: '600',
    },
    regularIncomeNotes: {
        flexDirection: 'row',
        alignItems: 'flex-start',
    },
    regularIncomeNotesLabel: {
        fontSize: 14,
        color: Colors.GRAY_600,
        marginRight: 8,
        minWidth: 50,
    },
    regularIncomeNotesValue: {
        fontSize: 14,
        color: Colors.GRAY_700,
        flex: 1,
    },
    regularIncomeNotification: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    regularIncomeNotificationText: {
        fontSize: 12,
        color: Colors.WARNING,
        marginLeft: 4,
    },
    // Upcoming payment styles
    upcomingPaymentsList: {
        padding: 16,
    },
    upcomingPaymentItem: {
        backgroundColor: Colors.WHITE,
        borderRadius: 12,
        padding: 16,
        marginBottom: 12,
        flexDirection: 'row',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.05,
        shadowRadius: 4,
        elevation: 2,
        borderLeftWidth: 4,
    },
    upcomingPaymentDateContainer: {
        width: 60,
        height: 60,
        borderRadius: 30,
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 16,
    },
    upcomingPaymentDayNumber: {
        fontSize: 20,
        fontWeight: '700',
    },
    upcomingPaymentMonth: {
        fontSize: 10,
        fontWeight: '600',
        color: Colors.GRAY_600,
        textTransform: 'uppercase',
    },
    upcomingPaymentYear: {
        fontSize: 8,
        color: Colors.GRAY_500,
    },
    upcomingPaymentInfo: {
        flex: 1,
        marginRight: 12,
    },
    upcomingPaymentName: {
        fontSize: 16,
        fontWeight: '600',
        color: Colors.GRAY_800,
        marginBottom: 4,
    },
    upcomingPaymentTimeContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    upcomingPaymentTimeLeft: {
        fontSize: 13,
        fontWeight: '500',
    },
    upcomingPaymentAmount: {
        alignItems: 'flex-end',
    },
    upcomingPaymentCurrency: {
        marginBottom: 8,
    },
    upcomingPaymentActionButton: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 6,
    },
    upcomingPaymentActionText: {
        color: '#fff',
        fontWeight: '500',
        fontSize: 12,
        marginLeft: 4,
    }
});
