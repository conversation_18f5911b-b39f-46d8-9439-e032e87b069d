import React, { useState, useRef, useCallback } from 'react';
import { 
  TextInput, 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity,
  Animated 
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import tokens from '../tokens';

/**
 * Modern Input Component
 * Design system'e uygun, tutarlı input bileşeni
 */
const Input = ({
  label,
  placeholder,
  value,
  onChangeText,
  onFocus,
  onBlur,
  error,
  helperText,
  disabled = false,
  required = false,
  variant = 'outlined', // 'outlined', 'filled', 'underlined'
  size = 'md', // 'sm', 'md', 'lg'
  leftIcon,
  rightIcon,
  onRightIconPress,
  secureTextEntry = false,
  keyboardType = 'default',
  autoCapitalize = 'sentences',
  autoCorrect = true,
  multiline = false,
  numberOfLines = 1,
  maxLength,
  style,
  inputStyle,
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [isPasswordVisible, setIsPasswordVisible] = useState(!secureTextEntry);
  const animatedValue = useRef(new Animated.Value(value ? 1 : 0)).current;

  // Handle focus
  const handleFocus = useCallback((event) => {
    setIsFocused(true);
    Animated.timing(animatedValue, {
      toValue: 1,
      duration: 200,
      useNativeDriver: false,
    }).start();
    if (onFocus) onFocus(event);
  }, [animatedValue, onFocus]);

  // Handle blur
  const handleBlur = useCallback((event) => {
    setIsFocused(false);
    if (!value) {
      Animated.timing(animatedValue, {
        toValue: 0,
        duration: 200,
        useNativeDriver: false,
      }).start();
    }
    if (onBlur) onBlur(event);
  }, [animatedValue, value, onBlur]);

  // Toggle password visibility
  const togglePasswordVisibility = useCallback(() => {
    setIsPasswordVisible(!isPasswordVisible);
  }, [isPasswordVisible]);

  // Get variant styles
  const getVariantStyles = () => {
    const baseStyle = {
      borderRadius: tokens.borderRadius.lg,
      paddingHorizontal: tokens.spacing[4],
    };

    switch (variant) {
      case 'outlined':
        return {
          ...baseStyle,
          borderWidth: 1,
          borderColor: error 
            ? tokens.colors.danger[500] 
            : isFocused 
            ? tokens.colors.primary[500] 
            : tokens.colors.gray[300],
          backgroundColor: disabled ? tokens.colors.gray[50] : '#ffffff',
        };
      case 'filled':
        return {
          ...baseStyle,
          borderWidth: 0,
          backgroundColor: disabled 
            ? tokens.colors.gray[100] 
            : tokens.colors.gray[50],
          borderBottomWidth: 2,
          borderBottomColor: error 
            ? tokens.colors.danger[500] 
            : isFocused 
            ? tokens.colors.primary[500] 
            : tokens.colors.gray[300],
        };
      case 'underlined':
        return {
          paddingHorizontal: 0,
          borderWidth: 0,
          backgroundColor: 'transparent',
          borderBottomWidth: 1,
          borderBottomColor: error 
            ? tokens.colors.danger[500] 
            : isFocused 
            ? tokens.colors.primary[500] 
            : tokens.colors.gray[300],
        };
      default:
        return baseStyle;
    }
  };

  // Get size styles
  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return {
          paddingVertical: tokens.spacing[2],
          fontSize: tokens.typography.fontSize.sm,
          minHeight: 40,
        };
      case 'md':
        return {
          paddingVertical: tokens.spacing[3],
          fontSize: tokens.typography.fontSize.base,
          minHeight: 48,
        };
      case 'lg':
        return {
          paddingVertical: tokens.spacing[4],
          fontSize: tokens.typography.fontSize.lg,
          minHeight: 56,
        };
      default:
        return {
          paddingVertical: tokens.spacing[3],
          fontSize: tokens.typography.fontSize.base,
          minHeight: 48,
        };
    }
  };

  const variantStyles = getVariantStyles();
  const sizeStyles = getSizeStyles();

  // Animated label position
  const labelStyle = {
    position: 'absolute',
    left: leftIcon ? 40 : tokens.spacing[4],
    top: animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [sizeStyles.minHeight / 2 - 8, -8],
    }),
    fontSize: animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [sizeStyles.fontSize, tokens.typography.fontSize.xs],
    }),
    color: error 
      ? tokens.colors.danger[500] 
      : isFocused 
      ? tokens.colors.primary[500] 
      : tokens.colors.gray[500],
    backgroundColor: variant === 'outlined' ? '#ffffff' : 'transparent',
    paddingHorizontal: variant === 'outlined' ? 4 : 0,
    zIndex: 1,
  };

  return (
    <View style={[styles.container, style]}>
      {/* Label */}
      {label && (
        <Animated.Text style={labelStyle}>
          {label}{required && ' *'}
        </Animated.Text>
      )}

      {/* Input Container */}
      <View style={[styles.inputContainer, variantStyles]}>
        {/* Left Icon */}
        {leftIcon && (
          <MaterialIcons
            name={leftIcon}
            size={20}
            color={isFocused ? tokens.colors.primary[500] : tokens.colors.gray[500]}
            style={styles.leftIcon}
          />
        )}

        {/* Text Input */}
        <TextInput
          {...props}
          style={[
            styles.input,
            sizeStyles,
            {
              color: disabled ? tokens.colors.gray[500] : tokens.colors.gray[900],
              fontFamily: tokens.typography.fontFamily.regular,
            },
            leftIcon && styles.inputWithLeftIcon,
            (rightIcon || secureTextEntry) && styles.inputWithRightIcon,
            inputStyle,
          ]}
          value={value}
          onChangeText={onChangeText}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={!label ? placeholder : ''}
          placeholderTextColor={tokens.colors.gray[400]}
          secureTextEntry={secureTextEntry && !isPasswordVisible}
          keyboardType={keyboardType}
          autoCapitalize={autoCapitalize}
          autoCorrect={autoCorrect}
          multiline={multiline}
          numberOfLines={numberOfLines}
          maxLength={maxLength}
          editable={!disabled}
          selectTextOnFocus={!disabled}
        />

        {/* Right Icon / Password Toggle */}
        {(rightIcon || secureTextEntry) && (
          <TouchableOpacity
            onPress={secureTextEntry ? togglePasswordVisibility : onRightIconPress}
            style={styles.rightIcon}
            disabled={disabled}
          >
            <MaterialIcons
              name={
                secureTextEntry 
                  ? (isPasswordVisible ? 'visibility-off' : 'visibility')
                  : rightIcon
              }
              size={20}
              color={isFocused ? tokens.colors.primary[500] : tokens.colors.gray[500]}
            />
          </TouchableOpacity>
        )}
      </View>

      {/* Helper Text / Error */}
      {(helperText || error) && (
        <Text style={[
          styles.helperText,
          error && styles.errorText
        ]}>
          {error || helperText}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: tokens.spacing[4],
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  input: {
    flex: 1,
    fontFamily: tokens.typography.fontFamily.regular,
    includeFontPadding: false,
    textAlignVertical: 'center',
  },
  inputWithLeftIcon: {
    paddingLeft: 40,
  },
  inputWithRightIcon: {
    paddingRight: 40,
  },
  leftIcon: {
    position: 'absolute',
    left: tokens.spacing[3],
    zIndex: 1,
  },
  rightIcon: {
    position: 'absolute',
    right: tokens.spacing[3],
    padding: tokens.spacing[1],
    zIndex: 1,
  },
  helperText: {
    marginTop: tokens.spacing[1],
    fontSize: tokens.typography.fontSize.xs,
    color: tokens.colors.gray[500],
    fontFamily: tokens.typography.fontFamily.regular,
  },
  errorText: {
    color: tokens.colors.danger[500],
  },
});

export default Input;
