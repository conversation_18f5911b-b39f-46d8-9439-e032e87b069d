import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useSQLiteContext } from 'expo-sqlite';
import { Colors } from '../constants/colors';

/**
 * Yatırım işlemi detay ekranı
 * @param {Object} props Component props
 * @param {Object} props.navigation Navigation objesi
 * @param {Object} props.route Route objesi
 */
const InvestmentTransactionDetailScreen = ({ navigation, route }) => {
  const { transaction } = route.params;
  const db = useSQLiteContext();
  
  const [transactionData, setTransactionData] = useState(null);
  const [asset, setAsset] = useState(null);
  const [loading, setLoading] = useState(true);

  // İşlem ve varlık detaylarını yükle
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        
        // İşlem detaylarını getir
        const transactionResult = await db.getAllAsync(`
          SELECT * FROM investment_transactions
          WHERE id = ?
        `, [transaction.id]);
        
        if (transactionResult.length > 0) {
          setTransactionData(transactionResult[0]);
          
          // Varlık detaylarını getir
          const assetResult = await db.getAllAsync(`
            SELECT * FROM investment_assets
            WHERE id = ?
          `, [transactionResult[0].asset_id]);
          
          if (assetResult.length > 0) {
            setAsset(assetResult[0]);
          }
        }
      } catch (error) {
        console.error('Veri yükleme hatası:', error);
        Alert.alert('Hata', 'İşlem detayları yüklenirken bir hata oluştu.');
      } finally {
        setLoading(false);
      }
    };
    
    loadData();
  }, [transaction.id]);

  // Para formatı
  const formatCurrency = (value) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };

  // Tarih formatı
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  // İşlem tipini formatla
  const formatTransactionType = (type) => {
    switch (type) {
      case 'buy': return 'Alım';
      case 'sell': return 'Satım';
      case 'dividend': return 'Temettü';
      case 'interest': return 'Faiz';
      case 'transfer': return 'Transfer';
      default: return type;
    }
  };

  // İşlemi sil
  const handleDeleteTransaction = () => {
    Alert.alert(
      'İşlemi Sil',
      'Bu işlemi silmek istediğinize emin misiniz? Bu işlem geri alınamaz.',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await db.runAsync('DELETE FROM investment_transactions WHERE id = ?', [transaction.id]);
              
              Alert.alert('Başarılı', 'İşlem başarıyla silindi.');
              navigation.goBack();
            } catch (error) {
              console.error('İşlem silme hatası:', error);
              Alert.alert('Hata', 'İşlem silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
      </View>
    );
  }

  if (!transactionData || !asset) {
    return (
      <View style={styles.errorContainer}>
        <MaterialIcons name="error" size={48} color="#e74c3c" />
        <Text style={styles.errorText}>İşlem bulunamadı</Text>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>Geri Dön</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <MaterialIcons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.title}>İşlem Detayı</Text>
        <View style={styles.headerButtons}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => navigation.navigate('InvestmentTransactionForm', { transaction: transactionData })}
          >
            <MaterialIcons name="edit" size={24} color="#fff" />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={handleDeleteTransaction}
          >
            <MaterialIcons name="delete" size={24} color="#fff" />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.card}>
          <View style={styles.cardHeader}>
            <View style={styles.transactionType}>
              <MaterialIcons
                name={transactionData.type === 'buy' ? 'arrow-downward' : 'arrow-upward'}
                size={20}
                color="#fff"
              />
              <Text style={styles.transactionTypeText}>
                {formatTransactionType(transactionData.type)}
              </Text>
            </View>
            <Text style={styles.transactionDate}>{formatDate(transactionData.date)}</Text>
          </View>
          
          <View style={styles.assetInfo}>
            <Text style={styles.assetName}>{asset.name}</Text>
            <Text style={styles.assetSymbol}>{asset.symbol}</Text>
          </View>
          
          <View style={styles.detailsContainer}>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Miktar</Text>
              <Text style={styles.detailValue}>{transactionData.quantity} {asset.symbol}</Text>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Fiyat</Text>
              <Text style={styles.detailValue}>{formatCurrency(transactionData.price)}</Text>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Toplam Değer</Text>
              <Text style={styles.detailValue}>
                {formatCurrency(transactionData.price * transactionData.quantity)}
              </Text>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>İşlem Tarihi</Text>
              <Text style={styles.detailValue}>{formatDate(transactionData.date)}</Text>
            </View>
          </View>
          
          {transactionData.notes && (
            <View style={styles.notesContainer}>
              <Text style={styles.notesLabel}>Notlar</Text>
              <Text style={styles.notesText}>{transactionData.notes}</Text>
            </View>
          )}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  errorText: {
    fontSize: 18,
    color: '#333',
    marginTop: 16,
    marginBottom: 24,
  },
  backButton: {
    backgroundColor: Colors.PRIMARY,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  backButtonText: {
    color: '#fff',
    fontWeight: '500',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.PRIMARY,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  headerButtons: {
    flexDirection: 'row',
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  transactionType: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  transactionTypeText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
  transactionDate: {
    fontSize: 14,
    color: '#666',
  },
  assetInfo: {
    marginBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    paddingBottom: 16,
  },
  assetName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  assetSymbol: {
    fontSize: 16,
    color: '#666',
  },
  detailsContainer: {
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  detailLabel: {
    fontSize: 14,
    color: '#666',
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  notesContainer: {
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    paddingTop: 16,
  },
  notesLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  notesText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
});

export default InvestmentTransactionDetailScreen;
