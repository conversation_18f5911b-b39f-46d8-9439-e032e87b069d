/**
 * React Native Reanimated ba<PERSON><PERSON><PERSON> yardımcı işlevi
 *
 * <PERSON><PERSON> dosya, React Native Reanimated kü<PERSON><PERSON>pha<PERSON><PERSON>n doğru şekilde
 * başlatılmasını sağlar.
 */

/**
 * Reanimated hazır o<PERSON><PERSON> olmadığını kontrol eder
 * @returns {boolean} Reanimated hazır durumda mı?
 */
export const isReanimatedReady = () => {
  // Reanimated 3.x için her zaman hazır kabul edilebilir
  return true;
};

export default {
  isReanimatedReady
};
