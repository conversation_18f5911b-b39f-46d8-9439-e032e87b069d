/**
 * Hatırlatıcı özel tekrarlama desenleri için servis fonksiyonları
 */
import { addDays, addMonths, addWeeks, format, getDay, getDaysInMonth, isWeekend, lastDayOfMonth, setDate } from 'date-fns';

/**
 * Tüm tekrarlama desenlerini getirir
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {boolean} includeSystem - Sistem desenlerini dahil et
 * @returns {Promise<Array>} Tekrarlama desenleri listesi
 */
export const getAllPatterns = async (db, includeSystem = true) => {
  try {
    let query = `
      SELECT * FROM reminder_repeat_patterns
      ORDER BY name ASC
    `;

    if (!includeSystem) {
      query = `
        SELECT * FROM reminder_repeat_patterns
        WHERE is_system = 0
        ORDER BY name ASC
      `;
    }

    const patterns = await db.getAllAsync(query);
    
    // JSON alanlarını parse et
    return patterns.map(pattern => ({
      ...pattern,
      pattern_value: JSON.parse(pattern.pattern_value || '{}')
    }));
  } catch (error) {
    console.error('Tekrarlama desenlerini getirme hatası:', error);
    throw error;
  }
};

/**
 * Belirli bir tekrarlama desenini getirir
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} patternId - Desen ID
 * @returns {Promise<Object|null>} Tekrarlama deseni
 */
export const getPatternById = async (db, patternId) => {
  try {
    const pattern = await db.getFirstAsync(`
      SELECT * FROM reminder_repeat_patterns
      WHERE id = ?
    `, [patternId]);
    
    if (!pattern) return null;
    
    // JSON alanlarını parse et
    return {
      ...pattern,
      pattern_value: JSON.parse(pattern.pattern_value || '{}')
    };
  } catch (error) {
    console.error('Tekrarlama deseni getirme hatası:', error);
    throw error;
  }
};

/**
 * Yeni bir tekrarlama deseni ekler
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} pattern - Desen verileri
 * @param {string} pattern.name - Desen adı
 * @param {string} pattern.description - Desen açıklaması
 * @param {string} pattern.pattern_type - Desen tipi
 * @param {Object} pattern.pattern_value - Desen değeri
 * @returns {Promise<number>} Eklenen desenin ID'si
 */
export const addPattern = async (db, pattern) => {
  try {
    const result = await db.runAsync(`
      INSERT INTO reminder_repeat_patterns (
        name, description, pattern_type, pattern_value, is_system
      )
      VALUES (?, ?, ?, ?, ?)
    `, [
      pattern.name,
      pattern.description || '',
      pattern.pattern_type,
      JSON.stringify(pattern.pattern_value || {}),
      0 // Kullanıcı tarafından oluşturulan desenler
    ]);
    
    return result.lastInsertRowId;
  } catch (error) {
    console.error('Tekrarlama deseni ekleme hatası:', error);
    throw error;
  }
};

/**
 * Bir tekrarlama desenini günceller
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} patternId - Desen ID
 * @param {Object} pattern - Güncellenecek desen verileri
 * @returns {Promise<boolean>} Başarılı ise true
 */
export const updatePattern = async (db, patternId, pattern) => {
  try {
    // Sistem desenlerini güncellemeye izin verme
    const existingPattern = await getPatternById(db, patternId);
    if (existingPattern && existingPattern.is_system === 1) {
      throw new Error('Sistem desenleri güncellenemez');
    }
    
    await db.runAsync(`
      UPDATE reminder_repeat_patterns
      SET name = ?, description = ?, pattern_type = ?, pattern_value = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [
      pattern.name,
      pattern.description || '',
      pattern.pattern_type,
      JSON.stringify(pattern.pattern_value || {}),
      patternId
    ]);
    
    return true;
  } catch (error) {
    console.error('Tekrarlama deseni güncelleme hatası:', error);
    throw error;
  }
};

/**
 * Bir tekrarlama desenini siler
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} patternId - Desen ID
 * @returns {Promise<boolean>} Başarılı ise true
 */
export const deletePattern = async (db, patternId) => {
  try {
    // Sistem desenlerini silmeye izin verme
    const existingPattern = await getPatternById(db, patternId);
    if (existingPattern && existingPattern.is_system === 1) {
      throw new Error('Sistem desenleri silinemez');
    }
    
    await db.runAsync(`
      DELETE FROM reminder_repeat_patterns
      WHERE id = ?
    `, [patternId]);
    
    return true;
  } catch (error) {
    console.error('Tekrarlama deseni silme hatası:', error);
    throw error;
  }
};

/**
 * Bir sonraki tekrarlanma tarihini hesaplar
 * 
 * @param {Date} currentDate - Mevcut tarih
 * @param {string} patternType - Desen tipi
 * @param {Object} patternValue - Desen değeri
 * @returns {Date} Bir sonraki tekrarlanma tarihi
 */
export const calculateNextOccurrence = (currentDate, patternType, patternValue) => {
  const date = new Date(currentDate);
  
  switch (patternType) {
    case 'monthly_day': {
      // Her ayın belirli günü (örn: her ayın 15'i)
      const day = patternValue.day || 1;
      const nextDate = addMonths(date, 1);
      return setDate(nextDate, Math.min(day, getDaysInMonth(nextDate)));
    }
    
    case 'monthly_last_day': {
      // Her ayın son günü
      return lastDayOfMonth(addMonths(date, 1));
    }
    
    case 'monthly_last_business_day': {
      // Her ayın son iş günü
      let lastDay = lastDayOfMonth(addMonths(date, 1));
      
      // Eğer hafta sonu ise, önceki Cuma gününe ayarla
      while (isWeekend(lastDay)) {
        lastDay = addDays(lastDay, -1);
      }
      
      return lastDay;
    }
    
    case 'monthly_weekday': {
      // Her ayın belirli haftasının belirli günü (örn: her ayın ilk pazartesi)
      const weekday = patternValue.weekday || 1; // 0: Pazar, 1: Pazartesi, ...
      const occurrence = patternValue.occurrence || 1; // 1: ilk, 2: ikinci, ... -1: son
      
      let nextMonth = addMonths(date, 1);
      nextMonth = setDate(nextMonth, 1); // Ayın ilk gününe ayarla
      
      if (occurrence > 0) {
        // Pozitif oluşum (ilk, ikinci, ...)
        let count = 0;
        let currentDay = nextMonth;
        
        // Belirtilen gün sayısını bul
        while (count < occurrence) {
          if (getDay(currentDay) === weekday) {
            count++;
            if (count === occurrence) {
              return currentDay;
            }
          }
          currentDay = addDays(currentDay, 1);
          
          // Ay sınırını aşarsa, bir sonraki aya geç
          if (currentDay.getMonth() !== nextMonth.getMonth()) {
            nextMonth = addMonths(nextMonth, 1);
            currentDay = setDate(nextMonth, 1);
            count = 0;
          }
        }
      } else {
        // Negatif oluşum (son, sondan bir önceki, ...)
        const lastDay = lastDayOfMonth(nextMonth);
        let count = 0;
        let currentDay = lastDay;
        
        // Sondan belirtilen gün sayısını bul
        while (count > occurrence) {
          if (getDay(currentDay) === weekday) {
            count--;
            if (count === occurrence) {
              return currentDay;
            }
          }
          currentDay = addDays(currentDay, -1);
          
          // Ay sınırını aşarsa, bir önceki aya geç
          if (currentDay.getMonth() !== nextMonth.getMonth()) {
            nextMonth = addMonths(nextMonth, -1);
            currentDay = lastDayOfMonth(nextMonth);
            count = 0;
          }
        }
      }
      
      return nextMonth; // Varsayılan olarak bir sonraki ayın ilk günü
    }
    
    case 'interval_months': {
      // Belirli ay aralıklarıyla (örn: her 3 ayda bir)
      const months = patternValue.months || 1;
      return addMonths(date, months);
    }
    
    case 'interval_weeks': {
      // Belirli hafta aralıklarıyla (örn: her 2 haftada bir)
      const weeks = patternValue.weeks || 1;
      return addWeeks(date, weeks);
    }
    
    case 'weekdays': {
      // Belirli haftanın günlerinde (örn: Pazartesi, Çarşamba, Cuma)
      const days = patternValue.days || [1]; // 0: Pazar, 1: Pazartesi, ...
      let nextDate = addDays(date, 1);
      
      // Bir sonraki uygun günü bul
      while (!days.includes(getDay(nextDate))) {
        nextDate = addDays(nextDate, 1);
      }
      
      return nextDate;
    }
    
    default:
      // Bilinmeyen desen tipi, varsayılan olarak bir sonraki gün
      return addDays(date, 1);
  }
};

/**
 * Desen tipine göre insan tarafından okunabilir açıklama oluşturur
 * 
 * @param {string} patternType - Desen tipi
 * @param {Object} patternValue - Desen değeri
 * @returns {string} İnsan tarafından okunabilir açıklama
 */
export const getPatternDescription = (patternType, patternValue) => {
  switch (patternType) {
    case 'monthly_day':
      return `Her ayın ${patternValue.day || 1}. günü`;
    
    case 'monthly_last_day':
      return 'Her ayın son günü';
    
    case 'monthly_last_business_day':
      return 'Her ayın son iş günü';
    
    case 'monthly_weekday': {
      const weekday = patternValue.weekday || 1;
      const occurrence = patternValue.occurrence || 1;
      
      const weekdayNames = ['Pazar', 'Pazartesi', 'Salı', 'Çarşamba', 'Perşembe', 'Cuma', 'Cumartesi'];
      const occurrenceNames = {
        1: 'ilk',
        2: 'ikinci',
        3: 'üçüncü',
        4: 'dördüncü',
        5: 'beşinci',
        '-1': 'son',
        '-2': 'sondan bir önceki',
        '-3': 'sondan ikinci'
      };
      
      return `Her ayın ${occurrenceNames[occurrence] || occurrence}. ${weekdayNames[weekday]} günü`;
    }
    
    case 'interval_months':
      return `Her ${patternValue.months || 1} ayda bir`;
    
    case 'interval_weeks':
      return `Her ${patternValue.weeks || 1} haftada bir`;
    
    case 'weekdays': {
      const days = patternValue.days || [1];
      const weekdayNames = ['Pazar', 'Pazartesi', 'Salı', 'Çarşamba', 'Perşembe', 'Cuma', 'Cumartesi'];
      
      if (days.length === 5 && days.every(day => day >= 1 && day <= 5)) {
        return 'Hafta içi her gün (Pazartesi-Cuma)';
      } else if (days.length === 2 && days.includes(0) && days.includes(6)) {
        return 'Hafta sonu her gün (Cumartesi-Pazar)';
      } else {
        const dayNames = days.map(day => weekdayNames[day]).join(', ');
        return `Her hafta ${dayNames} günleri`;
      }
    }
    
    default:
      return 'Özel tekrarlama deseni';
  }
};
