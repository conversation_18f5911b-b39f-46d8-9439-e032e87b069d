import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import * as DocumentPicker from 'expo-document-picker';
import * as Print from 'expo-print';
import { Alert } from 'react-native';

/**
 * Veritabanı yedekleme ve geri yükleme servisi
 * 
 * @param {SQLiteDatabase} db - SQLite veritabanı nesnesi
 * @returns {Object} Yedekleme ve geri yükleme işlevlerini içeren nesne
 */
export const useBackupService = (db) => {
  // Yedekleme dizini
  const backupDir = `${FileSystem.documentDirectory}backups/`;
  
  /**
   * Yedekleme dizinini oluşturur
   * 
   * @returns {Promise<void>}
   */
  const ensureBackupDir = async () => {
    const dirInfo = await FileSystem.getInfoAsync(backupDir);
    if (!dirInfo.exists) {
      await FileSystem.makeDirectoryAsync(backupDir, { intermediates: true });
    }
  };

  /**
   * <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
   * 
   * @returns {Promise<void>}
   */
  const backupDatabase = async () => {
    try {
      await ensureBackupDir();
      
      // Veritabanı dosyasının yolunu al
      const dbPath = `${FileSystem.documentDirectory}SQLite/maas.db`;
      
      // Yedekleme dosyasının adını oluştur (tarih ve saat ile)
      const date = new Date();
      const dateStr = date.toISOString().replace(/[:.]/g, '-');
      const backupPath = `${backupDir}maas_backup_${dateStr}.db`;
      
      // Veritabanı dosyasını kopyala
      await FileSystem.copyAsync({
        from: dbPath,
        to: backupPath
      });
      
      // Yedekleme dosyasını paylaş
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(backupPath, {
          mimeType: 'application/octet-stream',
          dialogTitle: 'Veritabanı Yedeği Paylaş',
          UTI: 'public.database'
        });
      } else {
        Alert.alert('Hata', 'Paylaşım özelliği bu cihazda kullanılamıyor.');
      }
      
      return true;
    } catch (error) {
      console.error('Veritabanı yedekleme hatası:', error);
      Alert.alert('Hata', 'Veritabanı yedeklenirken bir hata oluştu: ' + error.message);
      return false;
    }
  };

  /**
   * Veritabanını geri yükler
   * 
   * @returns {Promise<void>}
   */
  const restoreDatabase = async () => {
    try {
      // Dosya seçiciyi aç
      const result = await DocumentPicker.getDocumentAsync({
        type: '*/*',
        copyToCacheDirectory: true
      });
      
      if (result.canceled) {
        return false;
      }
      
      // Seçilen dosyanın URI'sini al
      const fileUri = result.assets[0].uri;
      
      // Kullanıcıya onay sor
      return new Promise((resolve) => {
        Alert.alert(
          'Veritabanını Geri Yükle',
          'Bu işlem mevcut veritabanını seçilen yedekle değiştirecek. Tüm mevcut veriler kaybolacak. Devam etmek istiyor musunuz?',
          [
            {
              text: 'İptal',
              style: 'cancel',
              onPress: () => resolve(false)
            },
            {
              text: 'Geri Yükle',
              style: 'destructive',
              onPress: async () => {
                try {
                  // Veritabanını kapat
                  await db.closeAsync();
                  
                  // Veritabanı dosyasının yolunu al
                  const dbPath = `${FileSystem.documentDirectory}SQLite/maas.db`;
                  
                  // Yedekleme dosyasını kopyala
                  await FileSystem.copyAsync({
                    from: fileUri,
                    to: dbPath
                  });
                  
                  // Veritabanını yeniden aç
                  await db.openAsync();
                  
                  Alert.alert('Başarılı', 'Veritabanı başarıyla geri yüklendi. Uygulamayı yeniden başlatmanız gerekebilir.');
                  resolve(true);
                } catch (error) {
                  console.error('Veritabanı geri yükleme hatası:', error);
                  Alert.alert('Hata', 'Veritabanı geri yüklenirken bir hata oluştu: ' + error.message);
                  resolve(false);
                }
              }
            }
          ]
        );
      });
    } catch (error) {
      console.error('Veritabanı geri yükleme hatası:', error);
      Alert.alert('Hata', 'Veritabanı geri yüklenirken bir hata oluştu: ' + error.message);
      return false;
    }
  };

  /**
   * Verileri JSON formatında dışa aktarır
   * 
   * @returns {Promise<void>}
   */
  const exportDataAsJson = async () => {
    try {
      await ensureBackupDir();
      
      // Verileri al
      const categories = await db.getAllAsync('SELECT * FROM categories');
      const transactions = await db.getAllAsync('SELECT * FROM transactions');
      
      // JSON verisi oluştur
      const jsonData = {
        categories,
        transactions,
        exportDate: new Date().toISOString(),
        appVersion: '1.0.0'
      };
      
      // JSON dosyasını oluştur
      const date = new Date();
      const dateStr = date.toISOString().replace(/[:.]/g, '-');
      const jsonPath = `${backupDir}maas_export_${dateStr}.json`;
      
      await FileSystem.writeAsStringAsync(jsonPath, JSON.stringify(jsonData, null, 2));
      
      // JSON dosyasını paylaş
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(jsonPath, {
          mimeType: 'application/json',
          dialogTitle: 'JSON Verilerini Paylaş',
          UTI: 'public.json'
        });
      } else {
        Alert.alert('Hata', 'Paylaşım özelliği bu cihazda kullanılamıyor.');
      }
      
      return true;
    } catch (error) {
      console.error('JSON dışa aktarma hatası:', error);
      Alert.alert('Hata', 'Veriler JSON formatında dışa aktarılırken bir hata oluştu: ' + error.message);
      return false;
    }
  };

  /**
   * PDF rapor oluşturur
   * 
   * @returns {Promise<void>}
   */
  const generatePdfReport = async () => {
    try {
      // Verileri al
      const categories = await db.getAllAsync('SELECT * FROM categories');
      const transactions = await db.getAllAsync(`
        SELECT t.*, c.name as category_name, c.color as category_color, c.icon as category_icon
        FROM transactions t
        LEFT JOIN categories c ON t.category_id = c.id
        ORDER BY t.date DESC
      `);
      
      // Toplam gelir ve giderleri hesapla
      const totalIncome = transactions
        .filter(t => t.type === 'income')
        .reduce((sum, t) => sum + t.amount, 0);
      
      const totalExpense = transactions
        .filter(t => t.type === 'expense')
        .reduce((sum, t) => sum + t.amount, 0);
      
      const balance = totalIncome - totalExpense;
      
      // Son 30 günlük işlemleri al
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      const recentTransactions = transactions.filter(t => new Date(t.date) >= thirtyDaysAgo);
      
      // Kategori bazında harcamaları hesapla
      const categoryExpenses = {};
      transactions.forEach(t => {
        if (t.type === 'expense' && t.category_id) {
          if (!categoryExpenses[t.category_id]) {
            categoryExpenses[t.category_id] = {
              name: t.category_name || 'Bilinmeyen',
              color: t.category_color || '#cccccc',
              total: 0
            };
          }
          categoryExpenses[t.category_id].total += t.amount;
        }
      });
      
      // HTML içeriği oluştur
      const html = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>Finansal Rapor</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              margin: 0;
              padding: 20px;
              color: #333;
            }
            h1, h2, h3 {
              color: #2ecc71;
            }
            .header {
              text-align: center;
              margin-bottom: 30px;
              border-bottom: 1px solid #eee;
              padding-bottom: 20px;
            }
            .summary {
              display: flex;
              justify-content: space-between;
              margin-bottom: 30px;
              flex-wrap: wrap;
            }
            .summary-box {
              background-color: #f9f9f9;
              border-radius: 5px;
              padding: 15px;
              width: 30%;
              box-sizing: border-box;
              text-align: center;
              margin-bottom: 10px;
            }
            .income { color: #2ecc71; }
            .expense { color: #e74c3c; }
            .balance { color: #3498db; }
            table {
              width: 100%;
              border-collapse: collapse;
              margin-bottom: 30px;
            }
            th, td {
              padding: 10px;
              text-align: left;
              border-bottom: 1px solid #eee;
            }
            th {
              background-color: #f5f5f5;
            }
            .footer {
              text-align: center;
              margin-top: 50px;
              font-size: 12px;
              color: #999;
            }
            .category-chart {
              margin-bottom: 30px;
            }
            .chart-bar {
              height: 20px;
              background-color: #3498db;
              margin-bottom: 5px;
              border-radius: 3px;
            }
            .chart-label {
              display: flex;
              justify-content: space-between;
              font-size: 14px;
              margin-bottom: 15px;
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>Finansal Rapor</h1>
            <p>Oluşturulma Tarihi: ${new Date().toLocaleDateString('tr-TR')}</p>
          </div>
          
          <h2>Özet</h2>
          <div class="summary">
            <div class="summary-box">
              <h3>Toplam Gelir</h3>
              <p class="income">₺${totalIncome.toLocaleString('tr-TR')}</p>
            </div>
            <div class="summary-box">
              <h3>Toplam Gider</h3>
              <p class="expense">₺${totalExpense.toLocaleString('tr-TR')}</p>
            </div>
            <div class="summary-box">
              <h3>Bakiye</h3>
              <p class="balance">₺${balance.toLocaleString('tr-TR')}</p>
            </div>
          </div>
          
          <h2>Kategori Bazında Harcamalar</h2>
          <div class="category-chart">
            ${Object.values(categoryExpenses).map(cat => {
              const percentage = (cat.total / totalExpense) * 100;
              return `
                <div class="chart-label">
                  <span>${cat.name}</span>
                  <span>₺${cat.total.toLocaleString('tr-TR')} (${percentage.toFixed(1)}%)</span>
                </div>
                <div class="chart-bar" style="width: ${percentage}%; background-color: ${cat.color}"></div>
              `;
            }).join('')}
          </div>
          
          <h2>Son İşlemler</h2>
          <table>
            <tr>
              <th>Tarih</th>
              <th>Açıklama</th>
              <th>Kategori</th>
              <th>Tür</th>
              <th>Tutar</th>
            </tr>
            ${recentTransactions.map(t => `
              <tr>
                <td>${new Date(t.date).toLocaleDateString('tr-TR')}</td>
                <td>${t.description || '-'}</td>
                <td>${t.category_name || '-'}</td>
                <td>${t.type === 'income' ? 'Gelir' : 'Gider'}</td>
                <td style="color: ${t.type === 'income' ? '#2ecc71' : '#e74c3c'}">
                  ₺${t.amount.toLocaleString('tr-TR')}
                </td>
              </tr>
            `).join('')}
          </table>
          
          <div class="footer">
            <p>Bu rapor Finansal Takip uygulaması tarafından oluşturulmuştur.</p>
            <p>© ${new Date().getFullYear()} Finansal Takip</p>
          </div>
        </body>
        </html>
      `;
      
      // PDF oluştur
      const { uri } = await Print.printToFileAsync({ html });
      
      // PDF dosyasını paylaş
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(uri, {
          mimeType: 'application/pdf',
          dialogTitle: 'PDF Raporu Paylaş',
          UTI: 'com.adobe.pdf'
        });
      } else {
        Alert.alert('Hata', 'Paylaşım özelliği bu cihazda kullanılamıyor.');
      }
      
      return true;
    } catch (error) {
      console.error('PDF rapor oluşturma hatası:', error);
      Alert.alert('Hata', 'PDF rapor oluşturulurken bir hata oluştu: ' + error.message);
      return false;
    }
  };

  return {
    backupDatabase,
    restoreDatabase,
    exportDataAsJson,
    generatePdfReport
  };
};
