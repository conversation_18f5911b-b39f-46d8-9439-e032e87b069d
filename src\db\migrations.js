import { executeSql } from '../utils/dbUtils';
import { migrateSavingsGoals } from './migrations/savings_goals';
import { migrateSavingsTransactions } from './migrations/savings_transactions';
import { addCurrencyToTransactions } from './migrations/add_currency_to_transactions';
import { migrateBudgets } from './migrations/budgets';
import { migrateSalaries } from './migrations/salaries';
import { migrateAccounts } from './migrations/accounts';
import { migrateExchangeRates } from './migrations/exchange_rates';
import { migrateOvertime } from './migrations/overtime';
import { recreateExchangeRatesTable } from './migrations/recreate_exchange_rates';
import { migrateNotifications } from './migrations/notifications';
import { migrateReminderGroups } from './migrations/reminder_groups';
import { migrateReminderTags } from './migrations/reminder_tags';
import { migrateReminderTemplates } from './migrations/reminder_templates';
import { migrateCustomRepeatPatterns } from './migrations/custom_repeat_patterns';
import { migrateBudgetsEnhanced } from './migrations/budgets_enhanced';
import { migrateBudgetAlerts } from './migrations/budgetAlerts';
import { migrateWorkShiftsDate } from './migrations/work_shifts_date';
import { migrateWorkSettings } from './migrations/work_settings_update';
import { migrateWorkPayments } from './migrations/work_payments_update';
import { migrateWorkShiftTypes } from './migrations/work_shift_types';
import { migrateWorkShiftSchedules } from './migrations/work_shift_schedules';
import { migrateWorkShiftsUpdate } from './migrations/work_shifts_update';
import { migrateWorkShiftsEnhanced } from './migrations/work_shifts_enhanced';
import {
  migrateWorkShiftsOvertime,
  migrateWorkShiftsOvertimeNoTransaction
} from './migrations/work_shifts_overtime';
import {
  migrateWorkShiftsHourlyRate,
  migrateWorkShiftsHourlyRateNoTransaction
} from './migrations/work_shifts_hourly_rate';
import {
  migrateWorkShiftsTypeId,
  migrateWorkShiftsTypeIdNoTransaction
} from './migrations/work_shifts_type_id';
import { migrateWorkShiftNotifications } from './migrations/work_shift_notifications';
import { migrateCurrencyEquivalents } from './migrations/currency_equivalents';
import { migrateRegularIncomes } from './migrations/regular_incomes'; // Yeni migrasyonu import et

/**
 * Veritabanı migrasyonlarını yönetir
 * Eski veritabanı yapısı ile yeni yapı arasındaki geçişleri sağlar
 */

/**
 * Veritabanı migrasyonlarını çalıştırır
 *
 * @param {object} db - SQLite veritabanı nesnesi
 * @returns {Promise<void>}
 */
export const runMigrations = async (db) => {
  try {
    console.log('Veritabanı migrasyonları başlatılıyor...');

    // Migration'ları transaction dışında çalıştır
    // Her migration kendi transaction yönetimini yapar

    // categories tablosu için type sütunu var mı kontrol et
    const hasCategoriesTable = await db.getFirstAsync(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='categories'
    `);

    if (hasCategoriesTable) {
      // Güvenli bir şekilde sütunu ekle - hata alırsa tabloyu yeniden oluştur
      try {
        // categories tablosunun sütunlarını kontrol et
        const columns = await db.getAllAsync(`PRAGMA table_info(categories)`);
        const hasTypeColumn = columns.some(col => col.name === 'type');

        // type sütunu yoksa ekle
        if (!hasTypeColumn) {
          console.log('Categories tablosuna "type" sütunu ekleniyor...');
          await db.execAsync(`ALTER TABLE categories ADD COLUMN type TEXT DEFAULT 'expense'`);

          // Default kategorilerin type değerlerini güncelle
          await db.execAsync(`UPDATE categories SET type = 'income' WHERE id = 'salary'`);
        }
      } catch (alterError) {
        console.warn('Kategori tablosu güncellenirken hata:', alterError);
        console.log('Kategori tablosunu yeniden oluşturuyorum...');

        // Yedek tablosu oluştur ve mevcut verileri aktar (transaction olmadan)
        try {
          // Geçici tablo oluştur
          await db.execAsync(`
            CREATE TABLE categories_backup (
              id TEXT PRIMARY KEY,
              name TEXT NOT NULL,
              type TEXT DEFAULT 'expense',
              icon TEXT,
              color TEXT,
              created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
          `);

          // Mevcut verileri kopyala (type sütunu için 'expense' varsayılanını kullan)
          await db.execAsync(`
            INSERT INTO categories_backup (id, name, icon, color, created_at)
            SELECT id, name, icon, color, created_at FROM categories
          `);

          // 'salary' kategorisini income olarak işaretle
          await db.execAsync(`UPDATE categories_backup SET type = 'income' WHERE id = 'salary'`);

          // Eski tabloyu sil ve yenisini yeniden adlandır
          await db.execAsync(`DROP TABLE categories`);
          await db.execAsync(`ALTER TABLE categories_backup RENAME TO categories`);
        } catch (recreateError) {
          console.error('Kategori tablosu yeniden oluşturma hatası:', recreateError);
          // Yedek tabloyu temizle
          try {
            await db.execAsync(`DROP TABLE IF EXISTS categories_backup`);
          } catch (cleanupError) {
            console.warn('Yedek tablo temizleme hatası:', cleanupError);
          }
          throw recreateError;
        }
      }
    }

    // Eski salaries tablosunu kontrol et ve kaldır
    const hasSalariesTable = await db.getFirstAsync(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='salaries'
    `);

    if (hasSalariesTable) {
      console.log('Eski salaries tablosu kaldırılıyor...');
      try {
        await db.execAsync(`DROP TABLE IF EXISTS salaries`);
      } catch (error) {
        console.log('Salaries tablosu kaldırma hatası (normal olabilir):', error.message);
      }
    }

    const hasExpensesTable = await db.getFirstAsync(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='expenses'
    `);

    if (!hasExpensesTable) {
      console.log('Gider tablosu oluşturuluyor...');
      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS expenses (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          amount DECIMAL(10,2) NOT NULL,
          date DATE NOT NULL,
          category TEXT,
          description TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);
    }

    // Birikim hedefleri tablosunu oluştur
    await migrateSavingsGoals(db);

    // Birikim işlemleri tablosunu oluştur
    await migrateSavingsTransactions(db);

    // Transactions tablosuna currency sütununu ekle
    await addCurrencyToTransactions(db);

    // Bütçe tablolarını oluştur
    await migrateBudgets(db);

    // Hesap tablolarını oluştur
    await migrateAccounts(db);

    // Maaş tablolarını oluştur
    await migrateSalaries(db);

    // Döviz kurları tablosunu oluştur
    // await migrateExchangeRates(db);

    // Döviz kurları tablosunu yeniden oluştur
    await recreateExchangeRatesTable(db);

    // Mesai tablosunu oluştur
    await migrateOvertime(db);

    // Bildirim tablolarını oluştur
    await migrateNotifications(db);

    // Hatırlatıcı grupları tablosunu oluştur
    await migrateReminderGroups(db);

    // Hatırlatıcı etiketleri tablosunu oluştur
    await migrateReminderTags(db);

    // Hatırlatıcı şablonları tablosunu oluştur
    await migrateReminderTemplates(db);

    // Özel tekrarlama desenleri tablosunu oluştur
    await migrateCustomRepeatPatterns(db);

    // Gelişmiş bütçe yönetimi tablolarını oluştur
    await migrateBudgetsEnhanced(db);

    // Bütçe uyarıları tablosunu oluştur
    await migrateBudgetAlerts(db);

    // work_shifts tablosuna date sütunu ekle
    await migrateWorkShiftsDate(db);

    // work_settings tablosunu güncelle
    await migrateWorkSettings(db);

    // work_payments tablosunu güncelle
    await migrateWorkPayments(db);

    // Vardiya türleri tablosunu oluştur
    await migrateWorkShiftTypes(db);

    // Vardiya planlaması tablosunu oluştur
    await migrateWorkShiftSchedules(db);

    // Work shifts tablosunu güncelle
    await migrateWorkShiftsUpdate(db);

    try {
      // Gelişmiş vardiya takibi için migrasyon (transaction olmadan)
      await migrateWorkShiftsEnhanced(db);
    } catch (error) {
      console.warn('Gelişmiş vardiya takibi migrasyonu atlandı:', error.message);
    }

    try {
      // Vardiya overtime için migrasyon (transaction olmadan)
      await migrateWorkShiftsOvertimeNoTransaction(db);
    } catch (error) {
      console.warn('Vardiya overtime migrasyonu atlandı:', error.message);
    }

    try {
      // Vardiya hourly_rate için migrasyon (transaction olmadan)
      await migrateWorkShiftsHourlyRateNoTransaction(db);
    } catch (error) {
      console.warn('Vardiya hourly_rate migrasyonu atlandı:', error.message);
    }

    try {
      // Vardiya shift_type_id için migrasyon (transaction olmadan)
      await migrateWorkShiftsTypeIdNoTransaction(db);
    } catch (error) {
      console.warn('Vardiya shift_type_id migrasyonu atlandı:', error.message);
    }

    try {
      // Vardiya bildirimleri için migrasyon
      await migrateWorkShiftNotifications(db);
    } catch (error) {
      console.warn('Vardiya bildirimleri migrasyonu atlandı:', error.message);
    }

    try {
      // Döviz karşılıkları için migrasyon
      await migrateCurrencyEquivalents(db);
    } catch (error) {
      console.warn('Döviz karşılıkları migrasyonu atlandı:', error.message);
    }

    try {
      // Düzenli gelirler için migrasyon
      await migrateRegularIncomes(db);
    } catch (error) {
      console.warn('Düzenli gelirler migrasyonu atlandı:', error.message);
    }

    console.log('Tüm migrasyonlar başarıyla tamamlandı.');
  } catch (error) {
    console.error('Veritabanı migrasyon hatası:', error);
    throw error;
  }
};
