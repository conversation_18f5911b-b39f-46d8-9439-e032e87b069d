/**
 * Para birimi karşılıklarını gösteren bileşen
 *
 * Bu bileşen, bir tutarın döviz karşılıklarını gösterir.
 * Euro ve Dolar karşılıkları 1 saniyede bir değişir.
 * Kullanıcının seçtiği 3. para birimi sabit kalır.
 */
import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { formatCurrency } from '../utils/formatters';
import { Colors } from '../constants/colors';

/**
 * Para birimi karşılıklarını gösteren bileşen
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {number} props.amount - Miktar
 * @param {string} props.currency - Para birimi
 * @param {number} props.usdEquivalent - USD karşılığı
 * @param {number} props.eurEquivalent - EUR karşılığı
 * @param {string} props.customCurrency - Özel para birimi
 * @param {number} props.customEquivalent - Özel para birimi karşılığı
 * @param {string} props.size - Boyut (small, medium, large)
 * @param {Object} props.style - Ek stil
 * @returns {React.ReactElement} Para birimi karşılıkları bileşeni
 */
const CurrencyEquivalent = ({
  amount,
  currency,
  usdEquivalent,
  eurEquivalent,
  customCurrency,
  customEquivalent,
  size = 'medium',
  style
}) => {
  // Gösterilecek para birimi (USD veya EUR)
  const [showUsd, setShowUsd] = useState(true);

  // 1 saniyede bir değiştir
  useEffect(() => {
    const interval = setInterval(() => {
      setShowUsd(prev => !prev);
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Boyuta göre stil belirle
  const getStylesBySize = () => {
    switch (size) {
      case 'small':
        return {
          container: styles.containerSmall,
          mainAmount: styles.mainAmountSmall,
          equivalentContainer: styles.equivalentContainerSmall,
          equivalentAmount: styles.equivalentAmountSmall,
          equivalentCurrency: styles.equivalentCurrencySmall
        };
      case 'large':
        return {
          container: styles.containerLarge,
          mainAmount: styles.mainAmountLarge,
          equivalentContainer: styles.equivalentContainerLarge,
          equivalentAmount: styles.equivalentAmountLarge,
          equivalentCurrency: styles.equivalentCurrencyLarge
        };
      default: // medium
        return {
          container: styles.container,
          mainAmount: styles.mainAmount,
          equivalentContainer: styles.equivalentContainer,
          equivalentAmount: styles.equivalentAmount,
          equivalentCurrency: styles.equivalentCurrency
        };
    }
  };

  const sizeStyles = getStylesBySize();

  return (
    <View style={[sizeStyles.container, style]}>
      {/* Ana tutar */}
      <Text style={sizeStyles.mainAmount}>
        {formatCurrency(amount, currency)}
      </Text>

      {/* Döviz karşılıkları */}
      <View style={sizeStyles.equivalentContainer}>
        {/* USD/EUR karşılığı (değişen) */}
        <View style={styles.equivalentItem}>
          <Text style={sizeStyles.equivalentAmount}>
            {showUsd
              ? formatCurrency(usdEquivalent, 'USD')
              : formatCurrency(eurEquivalent, 'EUR')
            }
          </Text>
          <Text style={sizeStyles.equivalentCurrency}>
            {showUsd ? 'USD' : 'EUR'}
          </Text>
        </View>

        {/* Özel para birimi karşılığı (sabit) */}
        {customCurrency && customCurrency !== currency &&
         customCurrency !== 'USD' && customCurrency !== 'EUR' && (
          <View style={styles.equivalentItem}>
            <Text style={sizeStyles.equivalentAmount}>
              {formatCurrency(customEquivalent, customCurrency)}
            </Text>
            <Text style={sizeStyles.equivalentCurrency}>
              {customCurrency}
            </Text>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  // Orta boy (varsayılan)
  container: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    marginVertical: 4
  },
  mainAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.TEXT_PRIMARY
  },
  equivalentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 2
  },
  equivalentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 8
  },
  equivalentAmount: {
    fontSize: 14,
    color: Colors.TEXT_SECONDARY
  },
  equivalentCurrency: {
    fontSize: 12,
    color: Colors.TEXT_TERTIARY,
    marginLeft: 2
  },

  // Küçük boy
  containerSmall: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 2
  },
  mainAmountSmall: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.TEXT_PRIMARY,
    marginRight: 8
  },
  equivalentContainerSmall: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  equivalentAmountSmall: {
    fontSize: 12,
    color: Colors.TEXT_SECONDARY
  },
  equivalentCurrencySmall: {
    fontSize: 10,
    color: Colors.TEXT_TERTIARY,
    marginLeft: 2
  },

  // Büyük boy
  containerLarge: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    marginVertical: 6
  },
  mainAmountLarge: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.TEXT_PRIMARY
  },
  equivalentContainerLarge: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4
  },
  equivalentAmountLarge: {
    fontSize: 16,
    color: Colors.TEXT_SECONDARY
  },
  equivalentCurrencyLarge: {
    fontSize: 14,
    color: Colors.TEXT_TERTIARY,
    marginLeft: 2
  }
});

export default CurrencyEquivalent;
