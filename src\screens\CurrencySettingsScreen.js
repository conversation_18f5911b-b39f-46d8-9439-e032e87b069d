import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Alert,
  ActivityIndicator,
  SafeAreaView
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import * as settingsService from '../services/settingsService';
import * as exchangeRateService from '../services/exchangeRateService';

/**
 * Para Birimi Ayarları Ekranı
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @returns {JSX.Element} Para Birimi Ayarları Ekranı
 */
export default function CurrencySettingsScreen({ navigation }) {
  const db = useSQLiteContext();
  
  // Durum değişkenleri
  const [loading, setLoading] = useState(true);
  const [currencies, setCurrencies] = useState([]);
  const [selectedCurrency, setSelectedCurrency] = useState('');
  const [saving, setSaving] = useState(false);
  
  // Verileri yükle
  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      
      // Desteklenen para birimlerini al
      const supportedCurrencies = exchangeRateService.getSupportedCurrencies();
      setCurrencies(supportedCurrencies);
      
      // Seçili para birimini al
      const customCurrency = await settingsService.getCustomCurrency(db);
      setSelectedCurrency(customCurrency);
      
      setLoading(false);
    } catch (error) {
      console.error('Para birimi ayarları yükleme hatası:', error);
      Alert.alert('Hata', 'Para birimi ayarları yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  }, [db]);
  
  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [loadData])
  );
  
  // Para birimini seç
  const selectCurrency = async (currency) => {
    try {
      setSaving(true);
      
      // Para birimini kaydet
      await settingsService.saveCustomCurrency(db, currency);
      setSelectedCurrency(currency);
      
      setSaving(false);
      
      Alert.alert(
        'Başarılı',
        `Özel para birimi ${currency} olarak ayarlandı.`,
        [{ text: 'Tamam' }]
      );
    } catch (error) {
      console.error('Para birimi kaydetme hatası:', error);
      Alert.alert('Hata', 'Para birimi kaydedilirken bir hata oluştu.');
      setSaving(false);
    }
  };
  
  // Para birimi öğesi render fonksiyonu
  const renderCurrencyItem = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.currencyItem,
        selectedCurrency === item.code && styles.selectedCurrencyItem
      ]}
      onPress={() => selectCurrency(item.code)}
      disabled={saving}
    >
      <View style={styles.currencyInfo}>
        <Text style={styles.currencyCode}>{item.code}</Text>
        <Text style={styles.currencyName}>{item.name}</Text>
      </View>
      
      <View style={styles.currencySymbol}>
        <Text style={styles.symbolText}>{item.symbol}</Text>
        
        {selectedCurrency === item.code && (
          <MaterialIcons name="check-circle" size={24} color={Colors.SUCCESS} />
        )}
      </View>
    </TouchableOpacity>
  );
  
  // Bilgi mesajı
  const renderInfoMessage = () => (
    <View style={styles.infoContainer}>
      <MaterialIcons name="info-outline" size={24} color={Colors.PRIMARY} />
      <Text style={styles.infoText}>
        Varsayılan olarak USD ve EUR para birimleri her zaman kaydedilir. 
        Buradan seçeceğiniz üçüncü para birimi de tüm finansal işlemlerinizde kaydedilecektir.
      </Text>
    </View>
  );
  
  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={styles.loadingText}>Para birimi ayarları yükleniyor...</Text>
      </View>
    );
  }
  
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Para Birimi Ayarları</Text>
        <View style={{ width: 40 }} />
      </View>
      
      {renderInfoMessage()}
      
      <FlatList
        data={currencies}
        keyExtractor={(item) => item.code}
        renderItem={renderCurrencyItem}
        contentContainerStyle={styles.listContent}
        ListHeaderComponent={
          <Text style={styles.listHeader}>
            Özel Para Birimini Seçin
          </Text>
        }
      />
      
      {saving && (
        <View style={styles.savingOverlay}>
          <ActivityIndicator size="large" color={Colors.PRIMARY} />
          <Text style={styles.savingText}>Kaydediliyor...</Text>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.GRAY_600,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 16,
    paddingHorizontal: 16,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY_LIGHT,
    padding: 16,
    margin: 16,
    borderRadius: 8,
  },
  infoText: {
    flex: 1,
    marginLeft: 12,
    fontSize: 14,
    color: Colors.PRIMARY,
    lineHeight: 20,
  },
  listContent: {
    padding: 16,
  },
  listHeader: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.GRAY_800,
    marginBottom: 16,
  },
  currencyItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 16,
    marginBottom: 8,
    borderRadius: 8,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  selectedCurrencyItem: {
    borderWidth: 2,
    borderColor: Colors.PRIMARY,
  },
  currencyInfo: {
    flex: 1,
  },
  currencyCode: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.GRAY_800,
  },
  currencyName: {
    fontSize: 14,
    color: Colors.GRAY_600,
    marginTop: 4,
  },
  currencySymbol: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  symbolText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.GRAY_700,
    marginRight: 8,
  },
  savingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  savingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.PRIMARY,
  },
});
