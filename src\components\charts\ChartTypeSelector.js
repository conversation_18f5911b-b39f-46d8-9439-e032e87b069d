import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';

/**
 * Basit chart type selector component'i
 */
const ChartTypeSelector = ({ 
  selectedType = 'donut',
  onTypeChange,
  availableTypes = ['donut', 'bar']
}) => {
  const { theme } = useTheme();

  const chartTypes = [
    {
      id: 'donut',
      label: 'Liste',
      icon: 'list-outline',
      description: 'Kategori listesi'
    },
    {
      id: 'bar',
      label: '<PERSON><PERSON><PERSON>',
      icon: 'bar-chart-outline',
      description: 'Çubuk grafik'
    },
    {
      id: 'pie',
      label: 'Pasta',
      icon: 'pie-chart-outline',
      description: 'Pasta grafik',
      disabled: true // Henüz geliştirilecek
    },
    {
      id: 'line',
      label: 'Çizgi',
      icon: 'trending-up-outline',
      description: 'Çizgi grafik',
      disabled: true // Henüz geliştirilecek
    }
  ];

  const filteredTypes = chartTypes.filter(type => 
    availableTypes.includes(type.id) || !type.disabled
  );

  return (
    <View style={styles.container}>
      <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
        Grafik Türü
      </Text>
      
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.typesScrollView}
        contentContainerStyle={styles.typesContainer}
      >
        {filteredTypes.map((type) => (
          <TouchableOpacity
            key={type.id}
            style={[
              styles.typeButton,
              { 
                backgroundColor: theme.BACKGROUND, 
                borderColor: theme.BORDER,
                opacity: type.disabled ? 0.5 : 1 
              },
              selectedType === type.id && {
                backgroundColor: theme.PRIMARY,
                borderColor: theme.PRIMARY
              }
            ]}
            onPress={() => !type.disabled && onTypeChange(type.id)}
            disabled={type.disabled}
          >
            <View style={styles.typeContent}>
              <Ionicons 
                name={type.icon} 
                size={18} 
                color={selectedType === type.id ? '#fff' : theme.TEXT_PRIMARY} 
              />
              <Text style={[
                styles.typeLabel,
                { color: theme.TEXT_PRIMARY },
                selectedType === type.id && { color: '#fff' }
              ]}>
                {type.label}
              </Text>
              {type.disabled && (
                <View style={styles.disabledIndicator}>
                  <Ionicons name="lock-closed" size={10} color={theme.TEXT_SECONDARY} />
                </View>
              )}
            </View>
            
            {selectedType === type.id && (
              <View style={[styles.selectedIndicator, { backgroundColor: '#fff' }]} />
            )}
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Seçili type'ın açıklaması */}
      <Text style={[styles.description, { color: theme.TEXT_SECONDARY }]}>
        {chartTypes.find(t => t.id === selectedType)?.description || ''}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  title: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 12,
  },
  typesScrollView: {
    marginBottom: 8,
  },
  typesContainer: {
    gap: 8,
    paddingHorizontal: 4,
  },
  typeButton: {
    borderWidth: 1,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    position: 'relative',
    minWidth: 80,
    alignItems: 'center',
  },
  typeContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  typeLabel: {
    fontSize: 12,
    fontWeight: '500',
  },
  selectedIndicator: {
    position: 'absolute',
    bottom: -3,
    left: '50%',
    marginLeft: -6,
    width: 12,
    height: 2,
    borderRadius: 1,
  },
  disabledIndicator: {
    position: 'absolute',
    top: -4,
    right: -4,
    backgroundColor: 'rgba(255,255,255,0.9)',
    borderRadius: 6,
    padding: 1,
  },
  description: {
    fontSize: 11,
    textAlign: 'center',
    marginTop: 4,
    fontStyle: 'italic',
  },
});

export default ChartTypeSelector;
