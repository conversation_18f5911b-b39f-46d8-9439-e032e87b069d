import AsyncStorage from '@react-native-async-storage/async-storage';
import { Appearance } from 'react-native';

/**
 * Tema debug ve test yardımcı fonksiyonları
 */

/**
 * Tema ayarlarını sıfırla (test için)
 */
export const resetThemeSettings = async () => {
  try {
    await AsyncStorage.removeItem('themePreference');
    console.log('✅ Tema ayarları sıfırlandı');
  } catch (error) {
    console.error('❌ Tema ayarları sıfırlanırken hata:', error);
  }
};

/**
 * Mevcut tema durumunu kontrol et
 */
export const checkThemeStatus = async () => {
  try {
    const savedThemePreference = await AsyncStorage.getItem('themePreference');
    const systemColorScheme = Appearance.getColorScheme();
    
    console.log('📱 Tema Durumu:', {
      savedThemePreference,
      systemColorScheme,
      shouldUseDarkMode: savedThemePreference === 'dark' || 
        (savedThemePreference === 'system' && systemColorScheme === 'dark')
    });
    
    return {
      savedThemePreference,
      systemColorScheme,
      shouldUseDarkMode: savedThemePreference === 'dark' || 
        (savedThemePreference === 'system' && systemColorScheme === 'dark')
    };
  } catch (error) {
    console.error('❌ Tema durumu kontrol hatası:', error);
    return null;
  }
};

/**
 * Sistem tema değişikliğini simüle et (test için)
 */
export const simulateSystemThemeChange = () => {
  const currentScheme = Appearance.getColorScheme();
  const newScheme = currentScheme === 'dark' ? 'light' : 'dark';
  
  console.log(`🔄 Sistem tema simülasyonu: ${currentScheme} → ${newScheme}`);
  
  // Bu gerçek tema değişikliği yapmaz, sadece log'lar
  // Gerçek teste cihaz ayarlarından tema değişikliği yapılmalı
};

/**
 * Tema ayarlarını system'e zorla
 */
export const forceSystemTheme = async () => {
  try {
    await AsyncStorage.setItem('themePreference', 'system');
    console.log('✅ Tema ayarı system olarak zorlandı');
  } catch (error) {
    console.error('❌ Tema ayarı değiştirme hatası:', error);
  }
};
