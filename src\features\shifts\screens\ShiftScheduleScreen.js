import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Alert,
  Switch,
  Modal,
  TextInput,
  ScrollView
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Colors } from '../../../constants/colors';
import { shiftStyles } from '../styles/shiftStyles';
import { formatDate } from '../utils/shiftUtils';
import * as shiftService from '../services/shiftService';
import ShiftScheduleItem from '../components/ShiftScheduleItem';

/**
 * Vardiya Planlaması Ekranı
 * 
 * Bu ekran, vardiya planlamalarını listeler ve yönetmeyi sağlar.
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @returns {JSX.Element} Vardiya planlaması ekranı
 */
const ShiftScheduleScreen = ({ navigation }) => {
  const db = useSQLiteContext();
  const insets = useSafeAreaInsets();
  
  // Durum değişkenleri
  const [loading, setLoading] = useState(true);
  const [schedules, setSchedules] = useState([]);
  const [shiftTypes, setShiftTypes] = useState([]);
  const [showOnlyActive, setShowOnlyActive] = useState(true);
  
  // Form durumları
  const [showModal, setShowModal] = useState(false);
  const [editingSchedule, setEditingSchedule] = useState(null);
  const [name, setName] = useState('');
  const [selectedShiftType, setSelectedShiftType] = useState(null);
  const [selectedDays, setSelectedDays] = useState([]);
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState(null);
  const [isActive, setIsActive] = useState(true);
  
  // DateTimePicker durumları
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  
  // Verileri yükle
  useEffect(() => {
    loadData();
  }, [showOnlyActive]);
  
  // Verileri yükle
  const loadData = async () => {
    try {
      setLoading(true);
      
      // Vardiya türlerini yükle
      const types = await shiftService.getShiftTypes(db);
      setShiftTypes(types);
      
      // Vardiya planlamalarını yükle
      const schedulesData = await shiftService.getShiftSchedules(db, showOnlyActive);
      setSchedules(schedulesData);
      
      setLoading(false);
    } catch (error) {
      console.error('Veri yükleme hatası:', error);
      Alert.alert('Hata', 'Veriler yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  };
  
  // Modal aç
  const handleOpenModal = (schedule = null) => {
    if (schedule) {
      setEditingSchedule(schedule);
      setName(schedule.name);
      setSelectedShiftType(schedule.shift_type_id);
      setSelectedDays(schedule.days_of_week ? schedule.days_of_week.split(',').map(day => parseInt(day)) : []);
      setStartDate(new Date(schedule.start_date));
      setEndDate(schedule.end_date ? new Date(schedule.end_date) : null);
      setIsActive(schedule.is_active === 1);
    } else {
      setEditingSchedule(null);
      setName('');
      setSelectedShiftType(shiftTypes.length > 0 ? shiftTypes[0].id : null);
      setSelectedDays([1, 2, 3, 4, 5]); // Pazartesi-Cuma
      setStartDate(new Date());
      setEndDate(null);
      setIsActive(true);
    }
    
    setShowModal(true);
  };
  
  // Modal kapat
  const handleCloseModal = () => {
    setShowModal(false);
    setEditingSchedule(null);
  };
  
  // Gün seçimi değiştir
  const toggleDay = (day) => {
    if (selectedDays.includes(day)) {
      setSelectedDays(selectedDays.filter(d => d !== day));
    } else {
      setSelectedDays([...selectedDays, day].sort());
    }
  };
  
  // Tarih değişikliği
  const handleStartDateChange = (event, selectedDate) => {
    setShowStartDatePicker(false);
    if (selectedDate) {
      setStartDate(selectedDate);
    }
  };
  
  const handleEndDateChange = (event, selectedDate) => {
    setShowEndDatePicker(false);
    if (selectedDate) {
      setEndDate(selectedDate);
    }
  };
  
  // Vardiya planlaması kaydet
  const handleSaveSchedule = async () => {
    // Validasyon
    if (!name.trim()) {
      Alert.alert('Hata', 'Planlama adı boş olamaz.');
      return;
    }
    
    if (!selectedShiftType) {
      Alert.alert('Hata', 'Vardiya türü seçmelisiniz.');
      return;
    }
    
    if (selectedDays.length === 0) {
      Alert.alert('Hata', 'En az bir gün seçmelisiniz.');
      return;
    }
    
    try {
      const scheduleData = {
        name: name.trim(),
        shift_type_id: selectedShiftType,
        days_of_week: selectedDays.join(','),
        start_date: startDate.toISOString().split('T')[0],
        end_date: endDate ? endDate.toISOString().split('T')[0] : null,
        is_active: isActive
      };
      
      if (editingSchedule) {
        // Planlamayı güncelle
        await shiftService.updateShiftSchedule(db, editingSchedule.id, scheduleData);
        Alert.alert('Başarılı', 'Vardiya planlaması başarıyla güncellendi.');
      } else {
        // Yeni planlama ekle
        await shiftService.addShiftSchedule(db, scheduleData);
        Alert.alert('Başarılı', 'Yeni vardiya planlaması başarıyla eklendi.');
      }
      
      handleCloseModal();
      loadData();
    } catch (error) {
      console.error('Vardiya planlaması kaydetme hatası:', error);
      Alert.alert('Hata', 'Vardiya planlaması kaydedilirken bir hata oluştu.');
    }
  };
  
  // Vardiya planlaması sil
  const handleDeleteSchedule = (id) => {
    Alert.alert(
      'Vardiya Planlaması Sil',
      'Bu vardiya planlamasını silmek istediğinize emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await shiftService.deleteShiftSchedule(db, id);
              Alert.alert('Başarılı', 'Vardiya planlaması başarıyla silindi.');
              loadData();
            } catch (error) {
              console.error('Vardiya planlaması silme hatası:', error);
              Alert.alert('Hata', 'Vardiya planlaması silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };
  
  // Liste öğesi render fonksiyonu
  const renderScheduleItem = ({ item }) => (
    <ShiftScheduleItem
      schedule={item}
      onPress={() => handleOpenModal(item)}
      onDelete={handleDeleteSchedule}
    />
  );
  
  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      {/* Başlık */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Vardiya Planlaması</Text>
        
        <View style={{ width: 40 }} />
      </View>
      
      {/* Filtre */}
      <View style={styles.filterContainer}>
        <View style={styles.filterLabelContainer}>
          <MaterialIcons name="filter-list" size={20} color="#666" />
          <Text style={styles.filterLabel}>Sadece aktif planlamaları göster</Text>
        </View>
        <Switch
          value={showOnlyActive}
          onValueChange={setShowOnlyActive}
          trackColor={{ false: '#ddd', true: Colors.PRIMARY }}
          thumbColor={showOnlyActive ? Colors.PRIMARY : '#f4f3f4'}
          ios_backgroundColor="#eee"
        />
      </View>
      
      {/* Vardiya Planlamaları Listesi */}
      {loading ? (
        <View style={shiftStyles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.PRIMARY} />
          <Text style={shiftStyles.loadingText}>Planlamalar yükleniyor...</Text>
        </View>
      ) : (
        <FlatList
          data={schedules}
          renderItem={renderScheduleItem}
          keyExtractor={item => item.id.toString()}
          contentContainerStyle={styles.listContent}
          ListEmptyComponent={
            <View style={shiftStyles.emptyState}>
              <MaterialIcons name="schedule" size={48} color={Colors.GRAY_400} />
              <Text style={shiftStyles.emptyStateText}>Henüz vardiya planlaması yok</Text>
              <Text style={shiftStyles.emptyStateSubText}>
                Yeni bir planlama eklemek için aşağıdaki butona tıklayın
              </Text>
            </View>
          }
        />
      )}
      
      {/* Yeni Planlama Ekle Butonu */}
      <TouchableOpacity
        style={styles.addButton}
        onPress={() => handleOpenModal()}
      >
        <MaterialIcons name="add" size={24} color="#fff" />
      </TouchableOpacity>
      
      {/* Planlama Ekleme/Düzenleme Modalı */}
      <Modal
        visible={showModal}
        transparent={true}
        animationType="slide"
        onRequestClose={handleCloseModal}
      >
        <View style={shiftStyles.modalOverlay}>
          <View style={shiftStyles.modalContent}>
            <Text style={shiftStyles.modalTitle}>
              {editingSchedule ? 'Vardiya Planlaması Düzenle' : 'Yeni Vardiya Planlaması Ekle'}
            </Text>
            
            <ScrollView>
              {/* Planlama Adı */}
              <View style={styles.formGroup}>
                <Text style={styles.label}>Planlama Adı</Text>
                <TextInput
                  style={styles.input}
                  value={name}
                  onChangeText={setName}
                  placeholder="Örn: Hafta İçi Gündüz Vardiyası"
                />
              </View>
              
              {/* Vardiya Türü */}
              <View style={styles.formGroup}>
                <Text style={styles.label}>Vardiya Türü</Text>
                <View style={styles.shiftTypesContainer}>
                  {shiftTypes.map(type => (
                    <TouchableOpacity
                      key={type.id}
                      style={[
                        styles.shiftTypeOption,
                        selectedShiftType === type.id && styles.selectedShiftType,
                        { borderColor: type.color }
                      ]}
                      onPress={() => setSelectedShiftType(type.id)}
                    >
                      <View style={[styles.colorDot, { backgroundColor: type.color }]} />
                      <Text style={styles.shiftTypeText}>{type.name}</Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
              
              {/* Çalışma Günleri */}
              <View style={styles.formGroup}>
                <Text style={styles.label}>Çalışma Günleri</Text>
                <View style={styles.daysContainer}>
                  {[
                    { id: 1, name: 'Pazartesi' },
                    { id: 2, name: 'Salı' },
                    { id: 3, name: 'Çarşamba' },
                    { id: 4, name: 'Perşembe' },
                    { id: 5, name: 'Cuma' },
                    { id: 6, name: 'Cumartesi' },
                    { id: 7, name: 'Pazar' }
                  ].map(day => (
                    <TouchableOpacity
                      key={day.id}
                      style={[
                        styles.dayOption,
                        selectedDays.includes(day.id) && styles.selectedDay
                      ]}
                      onPress={() => toggleDay(day.id)}
                    >
                      <Text style={[
                        styles.dayText,
                        selectedDays.includes(day.id) && styles.selectedDayText
                      ]}>
                        {day.name.substring(0, 3)}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
              
              {/* Başlangıç Tarihi */}
              <View style={styles.formGroup}>
                <Text style={styles.label}>Başlangıç Tarihi</Text>
                <TouchableOpacity
                  style={styles.dateSelector}
                  onPress={() => setShowStartDatePicker(true)}
                >
                  <MaterialIcons name="event" size={20} color={Colors.PRIMARY} />
                  <Text style={styles.dateText}>{formatDate(startDate)}</Text>
                </TouchableOpacity>
                
                {showStartDatePicker && (
                  <DateTimePicker
                    value={startDate}
                    mode="date"
                    display="default"
                    onChange={handleStartDateChange}
                  />
                )}
              </View>
              
              {/* Bitiş Tarihi */}
              <View style={styles.formGroup}>
                <View style={styles.endDateHeader}>
                  <Text style={styles.label}>Bitiş Tarihi</Text>
                  <TouchableOpacity
                    style={styles.clearButton}
                    onPress={() => setEndDate(null)}
                  >
                    <Text style={styles.clearButtonText}>Temizle</Text>
                  </TouchableOpacity>
                </View>
                
                <TouchableOpacity
                  style={styles.dateSelector}
                  onPress={() => setShowEndDatePicker(true)}
                >
                  <MaterialIcons name="event" size={20} color={Colors.PRIMARY} />
                  <Text style={styles.dateText}>
                    {endDate ? formatDate(endDate) : 'Süresiz'}
                  </Text>
                </TouchableOpacity>
                
                {showEndDatePicker && (
                  <DateTimePicker
                    value={endDate || new Date()}
                    mode="date"
                    display="default"
                    onChange={handleEndDateChange}
                  />
                )}
              </View>
              
              {/* Aktif/Pasif */}
              <View style={styles.switchContainer}>
                <View style={styles.switchInfo}>
                  <MaterialIcons 
                    name="toggle-on" 
                    size={24} 
                    color={isActive ? Colors.PRIMARY : '#666'} 
                  />
                  <Text style={styles.switchLabel}>Aktif</Text>
                </View>
                <Switch
                  value={isActive}
                  onValueChange={setIsActive}
                  trackColor={{ false: '#ddd', true: Colors.PRIMARY }}
                  thumbColor="#fff"
                />
              </View>
            </ScrollView>
            
            {/* Butonlar */}
            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={[styles.button, styles.cancelButton]}
                onPress={handleCloseModal}
              >
                <Text style={styles.cancelButtonText}>İptal</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.button, styles.saveButton]}
                onPress={handleSaveSchedule}
              >
                <Text style={styles.saveButtonText}>Kaydet</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...shiftStyles.container,
  },
  header: {
    ...shiftStyles.header,
  },
  headerTitle: {
    ...shiftStyles.headerTitle,
  },
  backButton: {
    ...shiftStyles.backButton,
  },
  filterContainer: {
    ...shiftStyles.filterContainer,
  },
  filterLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  filterLabel: {
    fontSize: 16,
    color: '#333',
    marginLeft: 8,
  },
  listContent: {
    padding: 16,
  },
  addButton: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  shiftTypesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  shiftTypeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
    marginBottom: 8,
  },
  selectedShiftType: {
    backgroundColor: 'rgba(0, 120, 255, 0.1)',
    borderWidth: 2,
  },
  colorDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 8,
  },
  shiftTypeText: {
    fontSize: 14,
    color: '#333',
  },
  daysContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  dayOption: {
    width: '13%',
    aspectRatio: 1,
    borderRadius: 100,
    borderWidth: 1,
    borderColor: '#ddd',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  selectedDay: {
    backgroundColor: Colors.PRIMARY,
    borderColor: Colors.PRIMARY,
  },
  dayText: {
    fontSize: 12,
    color: '#333',
  },
  selectedDayText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  dateSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    backgroundColor: '#fff',
  },
  dateText: {
    fontSize: 16,
    color: '#333',
    marginLeft: 8,
    fontWeight: '500',
  },
  endDateHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  clearButton: {
    padding: 4,
  },
  clearButtonText: {
    fontSize: 14,
    color: Colors.PRIMARY,
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    backgroundColor: '#f9f9f9',
    borderRadius: 12,
    padding: 12,
  },
  switchInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  switchLabel: {
    fontSize: 16,
    color: '#333',
    marginLeft: 8,
    fontWeight: '500',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
    paddingTop: 16,
  },
  button: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButton: {
    backgroundColor: '#f0f0f0',
    marginRight: 8,
  },
  saveButton: {
    backgroundColor: Colors.PRIMARY,
    marginLeft: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#666',
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
  },
});

export default ShiftScheduleScreen;
