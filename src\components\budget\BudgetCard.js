/**
 * <PERSON><PERSON>tçe Kartı Bileşeni
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md Stage 1 implementasyonu
 * 
 * Tek bir bütçeyi görüntülemek için kullanılan kart bileşeni
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useAppContext } from '../../context/AppContext';
import { formatCurrency } from '../../utils/formatters';

const { width } = Dimensions.get('window');

/**
 * Bütçe kartı bileşeni
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.budget - Bütçe verisi
 * @param {Function} props.onPress - Kart tıklama fonksiyonu
 * @param {Function} props.onEdit - Düzenleme fonksiyonu
 * @param {Function} props.onDelete - Silme fonksiyonu
 * @returns {JSX.Element} Bütçe kartı
 */
const BudgetCard = ({ 
  budget, 
  onPress, 
  onEdit, 
  onDelete,
  style 
}) => {
  const { theme, isDarkMode } = useAppContext();

  // Bütçe durumuna göre renk belirleme
  const getStatusColor = () => {
    if (budget.is_over_budget) return theme.DANGER;
    if (budget.progress >= 90) return theme.WARNING;
    if (budget.progress >= 75) return theme.INFO;
    return theme.SUCCESS;
  };

  // Bütçe durumu metni
  const getStatusText = () => {
    if (budget.is_over_budget) return 'Limit Aşıldı';
    if (budget.progress >= 90) return 'Dikkat';
    if (budget.progress >= 75) return 'Uyarı';
    return 'İyi';
  };

  // Dönem formatı
  const formatPeriod = (periodType) => {
    switch (periodType) {
      case 'weekly': return 'Haftalık';
      case 'monthly': return 'Aylık';
      case 'custom': return 'Özel';
      default: return periodType;
    }
  };

  // Bütçe tipi formatı
  const formatType = (type) => {
    switch (type) {
      case 'total': return 'Toplam';
      case 'category_based': return 'Kategori Bazlı';
      case 'flexible': return 'Esnek';
      default: return type;
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        {
          backgroundColor: theme.SURFACE,
          borderColor: theme.BORDER,
          shadowColor: isDarkMode ? theme.BLACK : theme.SHADOW
        },
        style
      ]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
            {budget.name}
          </Text>
          <View style={styles.badgeContainer}>
            <View style={[
              styles.statusBadge,
              { backgroundColor: getStatusColor() + '20' }
            ]}>
              <Text style={[
                styles.statusText,
                { color: getStatusColor() }
              ]}>
                {getStatusText()}
              </Text>
            </View>
            <View style={[
              styles.typeBadge,
              { backgroundColor: theme.PRIMARY + '20' }
            ]}>
              <Text style={[
                styles.typeText,
                { color: theme.PRIMARY }
              ]}>
                {formatPeriod(budget.period_type)}
              </Text>
            </View>
          </View>
        </View>

        {/* Actions */}
        <View style={styles.actions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => onEdit?.(budget)}
          >
            <MaterialIcons 
              name="edit" 
              size={20} 
              color={theme.TEXT_SECONDARY} 
            />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => onDelete?.(budget)}
          >
            <MaterialIcons 
              name="delete" 
              size={20} 
              color={theme.DANGER} 
            />
          </TouchableOpacity>
        </View>
      </View>

      {/* Description */}
      {budget.description && (
        <Text style={[styles.description, { color: theme.TEXT_SECONDARY }]}>
          {budget.description}
        </Text>
      )}

      {/* Progress Section */}
      <View style={styles.progressSection}>
        <View style={styles.amountRow}>
          <Text style={[styles.amountLabel, { color: theme.TEXT_SECONDARY }]}>
            Harcanan
          </Text>
          <Text style={[styles.amountValue, { color: theme.TEXT_PRIMARY }]}>
            {formatCurrency(budget.total_spent || 0, budget.currency)}
          </Text>
        </View>

        <View style={styles.amountRow}>
          <Text style={[styles.amountLabel, { color: theme.TEXT_SECONDARY }]}>
            Toplam Limit
          </Text>
          <Text style={[styles.amountValue, { color: theme.TEXT_PRIMARY }]}>
            {formatCurrency(budget.total_allocated || 0, budget.currency)}
          </Text>
        </View>

        {/* Progress Bar */}
        <View style={styles.progressContainer}>
          <View style={[
            styles.progressBar,
            { backgroundColor: theme.BORDER }
          ]}>
            <View style={[
              styles.progressFill,
              {
                width: `${Math.min(budget.progress || 0, 100)}%`,
                backgroundColor: getStatusColor()
              }
            ]} />
          </View>
          <Text style={[styles.progressText, { color: theme.TEXT_SECONDARY }]}>
            %{Math.round(budget.progress || 0)}
          </Text>
        </View>

        {/* Remaining Amount */}
        <View style={styles.remainingContainer}>
          <Text style={[styles.remainingLabel, { color: theme.TEXT_SECONDARY }]}>
            Kalan:
          </Text>
          <Text style={[
            styles.remainingValue,
            { 
              color: budget.remaining >= 0 ? theme.SUCCESS : theme.DANGER 
            }
          ]}>
            {formatCurrency(budget.remaining || 0, budget.currency)}
          </Text>
        </View>
      </View>

      {/* Footer Info */}
      <View style={styles.footer}>
        <View style={styles.footerItem}>
          <MaterialIcons 
            name="category" 
            size={16} 
            color={theme.TEXT_SECONDARY} 
          />
          <Text style={[styles.footerText, { color: theme.TEXT_SECONDARY }]}>
            {budget.category_count || 0} kategori
          </Text>
        </View>

        <View style={styles.footerItem}>
          <MaterialIcons 
            name="schedule" 
            size={16} 
            color={theme.TEXT_SECONDARY} 
          />
          <Text style={[styles.footerText, { color: theme.TEXT_SECONDARY }]}>
            {formatType(budget.type)}
          </Text>
        </View>

        <View style={styles.footerItem}>
          <MaterialIcons 
            name="date-range" 
            size={16} 
            color={theme.TEXT_SECONDARY} 
          />
          <Text style={[styles.footerText, { color: theme.TEXT_SECONDARY }]}>
            {new Date(budget.start_date).toLocaleDateString('tr-TR')}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginVertical: 6,
    marginHorizontal: 16,
    borderWidth: 1,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  titleContainer: {
    flex: 1,
    marginRight: 12,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  badgeContainer: {
    flexDirection: 'row',
    gap: 6,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  typeBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  typeText: {
    fontSize: 12,
    fontWeight: '500',
  },
  actions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    padding: 4,
  },
  description: {
    fontSize: 14,
    marginBottom: 12,
    lineHeight: 20,
  },
  progressSection: {
    marginBottom: 12,
  },
  amountRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  amountLabel: {
    fontSize: 14,
  },
  amountValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    marginBottom: 8,
  },
  progressBar: {
    flex: 1,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 12,
    fontWeight: '500',
    minWidth: 35,
    textAlign: 'right',
  },
  remainingContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  remainingLabel: {
    fontSize: 14,
  },
  remainingValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  footerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  footerText: {
    fontSize: 12,
  },
});

export default BudgetCard;
