/**
 * work_settings tablosunu güncelleyen migrasyon
 *
 * @param {object} db - SQLite veritabanı nesnesi
 * @returns {Promise<void>}
 */
export const migrateWorkSettings = async (db) => {
  try {
    console.log('work_settings tablosu güncelleme migrasyonu başlatılıyor...');

    // work_settings tablosunu kontrol et
    const hasWorkSettingsTable = await db.getFirstAsync(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='work_settings'
    `);

    if (hasWorkSettingsTable) {
      console.log('work_settings tablosu mevcut, sütunları kontrol ediliyor...');

      // Tabloda eksik sütunlar var mı kontrol et
      const columns = await db.getAllAsync(`PRAGMA table_info(work_settings)`);

      // Sütun adlarını bir diziye dönüştür
      const columnNames = columns.map(col => col.name);

      // Gerekli sütunları kontrol et ve ekle
      const requiredColumns = [
        { name: 'hourly_rate', type: 'REAL NOT NULL DEFAULT 100' },
        { name: 'overtime_rate', type: 'REAL NOT NULL DEFAULT 150' },
        { name: 'weekly_work_hours', type: 'INTEGER NOT NULL DEFAULT 45' },
        { name: 'daily_work_hours', type: 'INTEGER NOT NULL DEFAULT 9' },
        { name: 'currency', type: "TEXT NOT NULL DEFAULT 'TRY'" },
        { name: 'work_days', type: "TEXT NOT NULL DEFAULT '1,2,3,4,5'" },
        { name: 'auto_create_shifts', type: 'INTEGER NOT NULL DEFAULT 1' },
        { name: 'auto_create_days_ahead', type: 'INTEGER NOT NULL DEFAULT 7' },
        { name: 'shift_notification_enabled', type: 'INTEGER NOT NULL DEFAULT 1' },
        { name: 'shift_notification_minutes', type: 'INTEGER NOT NULL DEFAULT 60' },
        { name: 'created_at', type: "TEXT DEFAULT '2023-01-01 00:00:00'" },
        { name: 'updated_at', type: "TEXT DEFAULT '2023-01-01 00:00:00'" }
      ];

      for (const column of requiredColumns) {
        if (!columnNames.includes(column.name)) {
          console.log(`${column.name} sütunu ekleniyor...`);
          await db.execAsync(`ALTER TABLE work_settings ADD COLUMN ${column.name} ${column.type}`);
        }
      }

      // Eğer eski sütunlar varsa, yeni sütunlara değerleri kopyala
      if (columnNames.includes('default_hourly_rate') && !columnNames.includes('hourly_rate')) {
        await db.execAsync(`UPDATE work_settings SET hourly_rate = default_hourly_rate`);
      }

      if (columnNames.includes('overtime_multiplier') && !columnNames.includes('overtime_rate')) {
        await db.execAsync(`UPDATE work_settings SET overtime_rate = default_hourly_rate * overtime_multiplier`);
      }

      console.log('work_settings tablosu başarıyla güncellendi.');
    } else {
      console.log('work_settings tablosu bulunamadı, yeni tablo oluşturuluyor...');

      // Yeni work_settings tablosunu oluştur
      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS work_settings (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          hourly_rate REAL NOT NULL DEFAULT 100,
          overtime_rate REAL NOT NULL DEFAULT 150,
          weekly_work_hours INTEGER NOT NULL DEFAULT 45,
          daily_work_hours INTEGER NOT NULL DEFAULT 9,
          currency TEXT NOT NULL DEFAULT 'TRY',
          work_days TEXT NOT NULL DEFAULT '1,2,3,4,5',
          auto_create_shifts INTEGER NOT NULL DEFAULT 1,
          auto_create_days_ahead INTEGER NOT NULL DEFAULT 7,
          shift_notification_enabled INTEGER NOT NULL DEFAULT 1,
          shift_notification_minutes INTEGER NOT NULL DEFAULT 60,
          created_at TEXT DEFAULT '2023-01-01 00:00:00',
          updated_at TEXT DEFAULT '2023-01-01 00:00:00'
        )
      `);

      // Varsayılan ayarları ekle
      await db.runAsync(`
        INSERT INTO work_settings (
          hourly_rate, overtime_rate, weekly_work_hours,
          daily_work_hours, currency, work_days,
          auto_create_shifts, auto_create_days_ahead,
          shift_notification_enabled, shift_notification_minutes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [100, 150, 45, 9, 'TRY', '1,2,3,4,5', 1, 7, 1, 60]);

      console.log('work_settings tablosu başarıyla oluşturuldu ve varsayılan değerler eklendi.');
    }

    console.log('work_settings tablosu güncelleme migrasyonu tamamlandı.');
  } catch (error) {
    console.error('work_settings tablosu güncelleme migrasyon hatası:', error);
    throw error;
  }
};
