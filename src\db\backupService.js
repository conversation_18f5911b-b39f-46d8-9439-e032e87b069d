import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import * as DocumentPicker from 'expo-document-picker';
import * as Print from 'expo-print';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Alert } from 'react-native';

/**
 * Veritabanı yedekleme ve geri yükleme servisi
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Object} Yedekleme ve geri yükleme fonksiyonları
 */
export const useBackupService = (db) => {
  // Veritabanı dosya yolu
  const dbFilePath = FileSystem.documentDirectory + 'SQLite/maas.db';
  
  /**
   * Veritabanını yedekler ve paylaşır
   * @returns {Promise<void>}
   */
  const backupDatabase = async () => {
    try {
      // Yedek dosya adı
      const date = format(new Date(), 'yyyy-MM-dd_HH-mm', { locale: tr });
      const backupFileName = `maas_yedek_${date}.db`;
      const backupFilePath = FileSystem.documentDirectory + backupFileName;
      
      // Veritabanı dosyasını kopyala
      await FileSystem.copyAsync({
        from: dbFilePath,
        to: backupFilePath
      });
      
      // Dosyayı paylaş
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(backupFilePath, {
          mimeType: 'application/octet-stream',
          dialogTitle: 'Veritabanı Yedeği',
          UTI: 'public.database'
        });
        
        // Paylaşımdan sonra geçici dosyayı sil
        await FileSystem.deleteAsync(backupFilePath, { idempotent: true });
      } else {
        Alert.alert('Hata', 'Paylaşım özelliği bu cihazda kullanılamıyor.');
      }
    } catch (error) {
      console.error('Veritabanı yedekleme hatası:', error);
      Alert.alert('Hata', 'Veritabanı yedeklenirken bir hata oluştu.');
    }
  };
  
  /**
   * Yedekten veritabanını geri yükler
   * @returns {Promise<boolean>} Geri yükleme başarılı mı?
   */
  const restoreDatabase = async () => {
    try {
      // Kullanıcıdan dosya seç
      const result = await DocumentPicker.getDocumentAsync({
        type: '*/*',
        copyToCacheDirectory: true
      });
      
      if (result.canceled) {
        return false;
      }
      
      const fileUri = result.assets[0].uri;
      const fileName = result.assets[0].name;
      
      // Dosya adı kontrolü
      if (!fileName.includes('maas_yedek') || !fileName.endsWith('.db')) {
        Alert.alert('Hata', 'Geçersiz yedek dosyası. Lütfen geçerli bir yedek dosyası seçin.');
        return false;
      }
      
      // Onay al
      return new Promise((resolve) => {
        Alert.alert(
          'Veritabanını Geri Yükle',
          'Bu işlem mevcut verilerin üzerine yazacak ve geri alınamaz. Devam etmek istiyor musunuz?',
          [
            { text: 'İptal', style: 'cancel', onPress: () => resolve(false) },
            {
              text: 'Geri Yükle',
              style: 'destructive',
              onPress: async () => {
                try {
                  // Veritabanını kapat
                  await db.closeAsync();
                  
                  // Yedek dosyasını veritabanı dosyasının üzerine kopyala
                  await FileSystem.copyAsync({
                    from: fileUri,
                    to: dbFilePath
                  });
                  
                  // Veritabanını yeniden aç
                  await db.openAsync();
                  
                  Alert.alert('Başarılı', 'Veritabanı başarıyla geri yüklendi. Uygulamayı yeniden başlatmanız gerekebilir.');
                  resolve(true);
                } catch (error) {
                  console.error('Veritabanı geri yükleme hatası:', error);
                  Alert.alert('Hata', 'Veritabanı geri yüklenirken bir hata oluştu.');
                  resolve(false);
                }
              }
            }
          ]
        );
      });
    } catch (error) {
      console.error('Dosya seçme hatası:', error);
      Alert.alert('Hata', 'Dosya seçilirken bir hata oluştu.');
      return false;
    }
  };
  
  /**
   * Verileri JSON formatında dışa aktarır
   * @returns {Promise<void>}
   */
  const exportDataAsJson = async () => {
    try {
      // Tüm verileri al
      const categories = await db.getAllAsync('SELECT * FROM categories');
      const transactions = await db.getAllAsync('SELECT * FROM transactions');
      
      // JSON verisi oluştur
      const exportData = {
        categories,
        transactions,
        exportDate: new Date().toISOString(),
        appVersion: '1.0.0'
      };
      
      // JSON dosyasını oluştur
      const date = format(new Date(), 'yyyy-MM-dd_HH-mm', { locale: tr });
      const jsonFileName = `maas_veriler_${date}.json`;
      const jsonFilePath = FileSystem.documentDirectory + jsonFileName;
      
      await FileSystem.writeAsStringAsync(
        jsonFilePath,
        JSON.stringify(exportData, null, 2),
        { encoding: FileSystem.EncodingType.UTF8 }
      );
      
      // Dosyayı paylaş
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(jsonFilePath, {
          mimeType: 'application/json',
          dialogTitle: 'Veri Dışa Aktarımı (JSON)',
          UTI: 'public.json'
        });
        
        // Paylaşımdan sonra geçici dosyayı sil
        await FileSystem.deleteAsync(jsonFilePath, { idempotent: true });
      } else {
        Alert.alert('Hata', 'Paylaşım özelliği bu cihazda kullanılamıyor.');
      }
    } catch (error) {
      console.error('JSON dışa aktarma hatası:', error);
      Alert.alert('Hata', 'Veriler dışa aktarılırken bir hata oluştu.');
    }
  };
  
  /**
   * Verileri PDF formatında raporlar
   * @returns {Promise<void>}
   */
  const generatePdfReport = async () => {
    try {
      // Verileri al
      const categories = await db.getAllAsync('SELECT * FROM categories');
      const transactions = await db.getAllAsync(`
        SELECT 
          t.*,
          c.name as category_name,
          c.icon as category_icon,
          c.color as category_color
        FROM transactions t
        LEFT JOIN categories c ON t.category_id = c.id
        ORDER BY t.transaction_date DESC
      `);
      
      // İstatistikleri al
      const stats = await db.getFirstAsync(`
        SELECT 
          SUM(CASE WHEN is_income = 1 THEN amount ELSE 0 END) as total_income,
          SUM(CASE WHEN is_income = 0 THEN amount ELSE 0 END) as total_expense,
          (SUM(CASE WHEN is_income = 1 THEN amount ELSE 0 END) - SUM(CASE WHEN is_income = 0 THEN amount ELSE 0 END)) as balance,
          COUNT(CASE WHEN is_income = 1 THEN 1 END) as income_count,
          COUNT(CASE WHEN is_income = 0 THEN 1 END) as expense_count
        FROM transactions
      `);
      
      // Para birimini formatla
      const formatCurrency = (amount) => {
        return new Intl.NumberFormat('tr-TR', {
          style: 'currency',
          currency: 'TRY',
          minimumFractionDigits: 2
        }).format(amount);
      };
      
      // Tarihi formatla
      const formatDate = (dateString) => {
        const date = new Date(dateString);
        return format(date, 'd MMMM yyyy', { locale: tr });
      };
      
      // İşlemleri gelir ve gider olarak ayır
      const incomeTransactions = transactions.filter(t => t.is_income === 1);
      const expenseTransactions = transactions.filter(t => t.is_income === 0);
      
      // İşlem tablosu HTML'i oluştur
      const createTransactionTable = (transactions, title) => {
        if (transactions.length === 0) {
          return `<p>Henüz ${title.toLowerCase()} kaydı bulunmuyor.</p>`;
        }
        
        return `
          <h3>${title}</h3>
          <table class="transactions">
            <thead>
              <tr>
                <th>Tarih</th>
                <th>Kategori</th>
                <th>Açıklama</th>
                <th>Tutar</th>
              </tr>
            </thead>
            <tbody>
              ${transactions.map(t => `
                <tr>
                  <td>${formatDate(t.transaction_date)}</td>
                  <td>${t.category_name || 'Genel'}</td>
                  <td>${t.description || '-'}</td>
                  <td class="${t.is_income ? 'income' : 'expense'}">${formatCurrency(t.amount)}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        `;
      };
      
      // HTML içeriği oluştur
      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>Finansal Rapor</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              margin: 20px;
              color: #333;
            }
            h1 {
              color: #2c3e50;
              text-align: center;
              margin-bottom: 30px;
            }
            h2 {
              color: #3498db;
              margin-top: 30px;
              border-bottom: 1px solid #eee;
              padding-bottom: 10px;
            }
            h3 {
              color: #2c3e50;
              margin-top: 25px;
            }
            .summary {
              background-color: #f8f9fa;
              border-radius: 5px;
              padding: 15px;
              margin-bottom: 30px;
            }
            .summary-item {
              margin-bottom: 10px;
            }
            .summary-label {
              font-weight: bold;
              display: inline-block;
              width: 150px;
            }
            .income {
              color: #2ecc71;
            }
            .expense {
              color: #e74c3c;
            }
            table {
              width: 100%;
              border-collapse: collapse;
              margin-bottom: 30px;
            }
            th, td {
              border: 1px solid #ddd;
              padding: 8px;
              text-align: left;
            }
            th {
              background-color: #f2f2f2;
              font-weight: bold;
            }
            tr:nth-child(even) {
              background-color: #f9f9f9;
            }
            .footer {
              text-align: center;
              margin-top: 50px;
              font-size: 12px;
              color: #7f8c8d;
            }
          </style>
        </head>
        <body>
          <h1>Finansal Rapor</h1>
          
          <div class="summary">
            <div class="summary-item">
              <span class="summary-label">Rapor Tarihi:</span>
              <span>${formatDate(new Date().toISOString())}</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">Toplam Gelir:</span>
              <span class="income">${formatCurrency(stats.total_income || 0)}</span>
              <span>(${stats.income_count || 0} işlem)</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">Toplam Gider:</span>
              <span class="expense">${formatCurrency(stats.total_expense || 0)}</span>
              <span>(${stats.expense_count || 0} işlem)</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">Net Bakiye:</span>
              <span class="${stats.balance >= 0 ? 'income' : 'expense'}">${formatCurrency(stats.balance || 0)}</span>
            </div>
          </div>
          
          <h2>İşlemler</h2>
          ${createTransactionTable(incomeTransactions, 'Gelirler')}
          ${createTransactionTable(expenseTransactions, 'Giderler')}
          
          <div class="footer">
            <p>Bu rapor Maaş Takip uygulaması tarafından oluşturulmuştur.</p>
            <p>${new Date().toISOString().split('T')[0]}</p>
          </div>
        </body>
        </html>
      `;
      
      // PDF oluştur
      const { uri } = await Print.printToFileAsync({
        html: htmlContent,
        base64: false
      });
      
      // Dosya adını ayarla
      const date = format(new Date(), 'yyyy-MM-dd_HH-mm', { locale: tr });
      const pdfFileName = `maas_rapor_${date}.pdf`;
      const pdfFilePath = FileSystem.documentDirectory + pdfFileName;
      
      // Dosyayı taşı
      await FileSystem.moveAsync({
        from: uri,
        to: pdfFilePath
      });
      
      // Dosyayı paylaş
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(pdfFilePath, {
          mimeType: 'application/pdf',
          dialogTitle: 'Finansal Rapor (PDF)',
          UTI: 'com.adobe.pdf'
        });
        
        // Paylaşımdan sonra geçici dosyayı sil
        await FileSystem.deleteAsync(pdfFilePath, { idempotent: true });
      } else {
        Alert.alert('Hata', 'Paylaşım özelliği bu cihazda kullanılamıyor.');
      }
    } catch (error) {
      console.error('PDF rapor oluşturma hatası:', error);
      Alert.alert('Hata', 'PDF rapor oluşturulurken bir hata oluştu.');
    }
  };
  
  return {
    backupDatabase,
    restoreDatabase,
    exportDataAsJson,
    generatePdfReport
  };
};
