import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Alert
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Colors } from '../../../constants/colors';
import { shiftStyles } from '../styles/shiftStyles';
import { formatDate, getPreviousDay, getNextDay } from '../utils/shiftUtils';
import * as shiftService from '../services/shiftService';
import ShiftItem from '../components/ShiftItem';
import ShiftFilterModal from '../components/ShiftFilterModal';
import ShiftFormModal from '../components/ShiftFormModal';

/**
 * Vardiya Listesi Ekranı
 * 
 * Bu ekran, tüm vardiyaları listeler ve filtreleme imkanı sunar.
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @returns {JSX.Element} Vardiya listesi ekranı
 */
const ShiftListScreen = ({ navigation }) => {
  const db = useSQLiteContext();
  const insets = useSafeAreaInsets();
  
  // Durum değişkenleri
  const [loading, setLoading] = useState(true);
  const [shifts, setShifts] = useState([]);
  const [shiftTypes, setShiftTypes] = useState([]);
  const [settings, setSettings] = useState(null);
  const [filterDate, setFilterDate] = useState(new Date());
  const [showFilterDatePicker, setShowFilterDatePicker] = useState(false);
  
  // Filtre durumları
  const [filters, setFilters] = useState({
    shiftTypeId: null,
    status: null,
    isOvertime: false,
    isHoliday: false
  });
  
  // Modal durumları
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [showAddShiftModal, setShowAddShiftModal] = useState(false);
  
  // Verileri yükle
  useEffect(() => {
    loadData();
  }, [filterDate, filters]);
  
  // Verileri yükle
  const loadData = async () => {
    try {
      setLoading(true);
      
      // Vardiya türlerini yükle
      const types = await shiftService.getShiftTypes(db);
      setShiftTypes(types);
      
      // Ayarları yükle
      const workSettings = await shiftService.getWorkSettings(db);
      setSettings(workSettings);
      
      // Vardiyaları yükle
      const dateStr = filterDate.toISOString().split('T')[0];
      let data = await shiftService.getShiftsByDate(db, dateStr);
      
      // Filtreleri uygula
      if (filters.shiftTypeId) {
        data = data.filter(shift => shift.shift_type_id === filters.shiftTypeId);
      }
      
      if (filters.status) {
        data = data.filter(shift => shift.status === filters.status);
      }
      
      if (filters.isOvertime) {
        data = data.filter(shift => shift.is_overtime === 1);
      }
      
      if (filters.isHoliday) {
        data = data.filter(shift => shift.is_holiday === 1);
      }
      
      setShifts(data);
      setLoading(false);
    } catch (error) {
      console.error('Veri yükleme hatası:', error);
      Alert.alert('Hata', 'Veriler yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  };
  
  // Tarih değişikliği
  const handleDateChange = (event, selectedDate) => {
    setShowFilterDatePicker(false);
    if (selectedDate) {
      setFilterDate(selectedDate);
    }
  };
  
  // Önceki güne git
  const handlePreviousDay = () => {
    setFilterDate(getPreviousDay(filterDate));
  };
  
  // Sonraki güne git
  const handleNextDay = () => {
    setFilterDate(getNextDay(filterDate));
  };
  
  // Filtreleri uygula
  const handleApplyFilters = (newFilters) => {
    setFilters(newFilters);
  };
  
  // Vardiya ekle/güncelle
  const handleSaveShift = async (id, shiftData) => {
    try {
      if (id) {
        // Vardiyayı güncelle
        await shiftService.updateShift(db, id, shiftData);
        Alert.alert('Başarılı', 'Vardiya başarıyla güncellendi.');
      } else {
        // Yeni vardiya ekle
        await shiftService.addShift(db, shiftData);
        Alert.alert('Başarılı', 'Yeni vardiya başarıyla eklendi.');
      }
      
      // Verileri yeniden yükle
      loadData();
    } catch (error) {
      console.error('Vardiya kaydetme hatası:', error);
      Alert.alert('Hata', 'Vardiya kaydedilirken bir hata oluştu.');
    }
  };
  
  // Vardiya sil
  const handleDeleteShift = (id) => {
    Alert.alert(
      'Vardiya Sil',
      'Bu vardiyayı silmek istediğinize emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await shiftService.deleteShift(db, id);
              Alert.alert('Başarılı', 'Vardiya başarıyla silindi.');
              loadData();
            } catch (error) {
              console.error('Vardiya silme hatası:', error);
              Alert.alert('Hata', 'Vardiya silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };
  
  // Vardiya detayına git
  const handleShiftPress = (shift) => {
    navigation.navigate('ShiftDetail', { shiftId: shift.id });
  };
  
  // Vardiya türünü bul
  const getShiftType = (shiftTypeId) => {
    return shiftTypes.find(type => type.id === shiftTypeId) || null;
  };
  
  // Liste öğesi render fonksiyonu
  const renderShiftItem = ({ item }) => (
    <ShiftItem
      shift={item}
      shiftType={getShiftType(item.shift_type_id)}
      onPress={() => handleShiftPress(item)}
      onDelete={handleDeleteShift}
    />
  );
  
  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      {/* Başlık */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Vardiya Listesi</Text>
        
        <TouchableOpacity
          style={styles.filterButton}
          onPress={() => setShowFilterModal(true)}
        >
          <MaterialIcons name="filter-list" size={24} color="#fff" />
        </TouchableOpacity>
      </View>
      
      {/* Tarih Seçici */}
      <View style={styles.dateSelector}>
        <TouchableOpacity
          style={styles.dateArrow}
          onPress={handlePreviousDay}
        >
          <MaterialIcons name="chevron-left" size={24} color={Colors.PRIMARY} />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.dateButton}
          onPress={() => setShowFilterDatePicker(true)}
        >
          <MaterialIcons name="event" size={20} color={Colors.PRIMARY} style={styles.dateIcon} />
          <Text style={styles.dateText}>
            {formatDate(filterDate)}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.dateArrow}
          onPress={handleNextDay}
        >
          <MaterialIcons name="chevron-right" size={24} color={Colors.PRIMARY} />
        </TouchableOpacity>
      </View>
      
      {/* Vardiya Listesi */}
      {loading ? (
        <View style={shiftStyles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.PRIMARY} />
          <Text style={shiftStyles.loadingText}>Vardiyalar yükleniyor...</Text>
        </View>
      ) : (
        <FlatList
          data={shifts}
          renderItem={renderShiftItem}
          keyExtractor={item => item.id.toString()}
          contentContainerStyle={styles.listContent}
          ListEmptyComponent={
            <View style={shiftStyles.emptyState}>
              <MaterialIcons name="event-busy" size={48} color={Colors.GRAY_400} />
              <Text style={shiftStyles.emptyStateText}>Bu tarih için vardiya bulunamadı</Text>
              <Text style={shiftStyles.emptyStateSubText}>
                Yeni bir vardiya eklemek için aşağıdaki butona tıklayın
              </Text>
            </View>
          }
        />
      )}
      
      {/* Yeni Vardiya Ekle Butonu */}
      <TouchableOpacity
        style={styles.addButton}
        onPress={() => setShowAddShiftModal(true)}
      >
        <MaterialIcons name="add" size={24} color="#fff" />
      </TouchableOpacity>
      
      {/* Tarih Seçici */}
      {showFilterDatePicker && (
        <DateTimePicker
          value={filterDate}
          mode="date"
          display="default"
          onChange={handleDateChange}
        />
      )}
      
      {/* Filtre Modalı */}
      <ShiftFilterModal
        visible={showFilterModal}
        onClose={() => setShowFilterModal(false)}
        onApply={handleApplyFilters}
        filters={filters}
        shiftTypes={shiftTypes}
      />
      
      {/* Vardiya Ekleme Modalı */}
      <ShiftFormModal
        visible={showAddShiftModal}
        onClose={() => setShowAddShiftModal(false)}
        onSave={handleSaveShift}
        shiftTypes={shiftTypes}
        settings={settings}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...shiftStyles.container,
  },
  header: {
    ...shiftStyles.header,
  },
  headerTitle: {
    ...shiftStyles.headerTitle,
  },
  backButton: {
    ...shiftStyles.backButton,
  },
  filterButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dateSelector: {
    ...shiftStyles.dateSelector,
  },
  dateButton: {
    ...shiftStyles.dateButton,
  },
  dateIcon: {
    ...shiftStyles.dateIcon,
  },
  dateText: {
    ...shiftStyles.dateText,
  },
  dateArrow: {
    ...shiftStyles.dateArrow,
  },
  listContent: {
    padding: 16,
  },
  addButton: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
});

export default ShiftListScreen;
