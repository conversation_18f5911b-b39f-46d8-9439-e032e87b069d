import { format, subDays, subMonths, startOfMonth, endOfMonth, startOfWeek, endOfWeek } from 'date-fns';
import { tr } from 'date-fns/locale';

/**
 * Gelişmiş Rapor Analiz Servisi
 * Detaylı finansal analiz ve raporlama işlevleri
 */

/**
 * <PERSON><PERSON> aralığı hesaplama
 * @param {string} period - 'week', 'month', '3months', '6months', 'year', 'custom'
 * @param {Date} customStart - Özel başlangıç tarihi
 * @param {Date} customEnd - Özel bitiş tarihi
 * @returns {Object} Başlangıç ve bitiş tarihleri
 */
export const getDateRange = (period, customStart = null, customEnd = null) => {
  const now = new Date();
  let startDate, endDate;

  switch (period) {
    case 'week':
      startDate = startOfWeek(now, { locale: tr });
      endDate = endOfWeek(now, { locale: tr });
      break;
    case 'month':
      startDate = startOfMonth(now);
      endDate = endOfMonth(now);
      break;
    case '3months':
      startDate = subMonths(startOfMonth(now), 2);
      endDate = endOfMonth(now);
      break;
    case '6months':
      startDate = subMonths(startOfMonth(now), 5);
      endDate = endOfMonth(now);
      break;
    case 'year':
      startDate = subMonths(startOfMonth(now), 11);
      endDate = endOfMonth(now);
      break;
    case 'custom':
      startDate = customStart || subDays(now, 30);
      endDate = customEnd || now;
      break;
    default:
      startDate = startOfMonth(now);
      endDate = endOfMonth(now);
  }

  return {
    startDate: format(startDate, 'yyyy-MM-dd'),
    endDate: format(endDate, 'yyyy-MM-dd'),
    startDateObj: startDate,
    endDateObj: endDate
  };
};

/**
 * Temel finansal özet
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {string} startDate - Başlangıç tarihi
 * @param {string} endDate - Bitiş tarihi
 * @returns {Object} Finansal özet
 */
export const getFinancialSummary = async (db, startDate, endDate) => {
  try {
    const summaryResult = await db.getAllAsync(`
      SELECT 
        type,
        SUM(amount) as total,
        COUNT(*) as count,
        AVG(amount) as average,
        MIN(amount) as minimum,
        MAX(amount) as maximum
      FROM transactions 
      WHERE date BETWEEN ? AND ?
      GROUP BY type
    `, [startDate, endDate]);

    const income = summaryResult.find(r => r.type === 'income') || { total: 0, count: 0, average: 0, minimum: 0, maximum: 0 };
    const expense = summaryResult.find(r => r.type === 'expense') || { total: 0, count: 0, average: 0, minimum: 0, maximum: 0 };

    const balance = income.total - expense.total;
    const savingsRate = income.total > 0 ? (balance / income.total) * 100 : 0;
    const totalTransactions = income.count + expense.count;

    return {
      income: {
        total: income.total,
        count: income.count,
        average: income.average,
        minimum: income.minimum,
        maximum: income.maximum
      },
      expense: {
        total: expense.total,
        count: expense.count,
        average: expense.average,
        minimum: expense.minimum,
        maximum: expense.maximum
      },
      balance,
      savingsRate,
      totalTransactions,
      averageTransactionSize: totalTransactions > 0 ? (income.total + expense.total) / totalTransactions : 0
    };
  } catch (error) {
    console.error('Finansal özet hatası:', error);
    throw error;
  }
};

/**
 * Kategori bazlı analiz
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {string} startDate - Başlangıç tarihi
 * @param {string} endDate - Bitiş tarihi
 * @param {string} type - 'income', 'expense' veya 'all'
 * @returns {Array} Kategori analizi
 */
export const getCategoryAnalysis = async (db, startDate, endDate, type = 'all') => {
  try {
    let whereClause = 'WHERE t.date BETWEEN ? AND ?';
    let params = [startDate, endDate];

    if (type !== 'all') {
      whereClause += ' AND t.type = ?';
      params.push(type);
    }

    const categoryResult = await db.getAllAsync(`
      SELECT
        c.id,
        c.name as category,
        c.color,
        t.type,
        SUM(t.amount) as total,
        COUNT(*) as transaction_count,
        AVG(t.amount) as average_amount,
        MIN(t.amount) as min_amount,
        MAX(t.amount) as max_amount,
        (SUM(t.amount) * 100.0 / (
          SELECT SUM(amount) FROM transactions 
          WHERE date BETWEEN ? AND ? ${type !== 'all' ? 'AND type = ?' : ''}
        )) as percentage
      FROM transactions t
      JOIN categories c ON t.category_id = c.id
      ${whereClause}
      GROUP BY c.id, c.name, c.color, t.type
      ORDER BY total DESC
    `, type !== 'all' ? [...params, ...params] : [...params, startDate, endDate]);

    return categoryResult.map(category => ({
      ...category,
      percentage: parseFloat(category.percentage) || 0
    }));
  } catch (error) {
    console.error('Kategori analizi hatası:', error);
    throw error;
  }
};

/**
 * Zaman bazlı trend analizi
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {string} startDate - Başlangıç tarihi
 * @param {string} endDate - Bitiş tarihi
 * @param {string} groupBy - 'day', 'week', 'month'
 * @returns {Array} Trend verileri
 */
export const getTrendAnalysis = async (db, startDate, endDate, groupBy = 'day') => {
  try {
    let dateFormat, dateGroup;
    
    switch (groupBy) {
      case 'week':
        dateFormat = '%Y-%W';
        dateGroup = "strftime('%Y-%W', date)";
        break;
      case 'month':
        dateFormat = '%Y-%m';
        dateGroup = "strftime('%Y-%m', date)";
        break;
      default: // day
        dateFormat = '%Y-%m-%d';
        dateGroup = "date";
    }

    const trendResult = await db.getAllAsync(`
      SELECT
        ${dateGroup} as period,
        type,
        SUM(amount) as total,
        COUNT(*) as count
      FROM transactions
      WHERE date BETWEEN ? AND ?
      GROUP BY ${dateGroup}, type
      ORDER BY period ASC
    `, [startDate, endDate]);

    // Verileri organize et
    const periods = [...new Set(trendResult.map(r => r.period))];
    const trendData = periods.map(period => {
      const incomeData = trendResult.find(r => r.period === period && r.type === 'income');
      const expenseData = trendResult.find(r => r.period === period && r.type === 'expense');
      
      return {
        period,
        income: incomeData ? incomeData.total : 0,
        expense: expenseData ? expenseData.total : 0,
        incomeCount: incomeData ? incomeData.count : 0,
        expenseCount: expenseData ? expenseData.count : 0,
        balance: (incomeData ? incomeData.total : 0) - (expenseData ? expenseData.total : 0)
      };
    });

    return trendData;
  } catch (error) {
    console.error('Trend analizi hatası:', error);
    throw error;
  }
};

/**
 * Karşılaştırmalı analiz (önceki dönemle karşılaştırma)
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {string} currentStart - Mevcut dönem başlangıcı
 * @param {string} currentEnd - Mevcut dönem bitişi
 * @param {string} previousStart - Önceki dönem başlangıcı
 * @param {string} previousEnd - Önceki dönem bitişi
 * @returns {Object} Karşılaştırmalı analiz
 */
export const getComparativeAnalysis = async (db, currentStart, currentEnd, previousStart, previousEnd) => {
  try {
    const [currentSummary, previousSummary] = await Promise.all([
      getFinancialSummary(db, currentStart, currentEnd),
      getFinancialSummary(db, previousStart, previousEnd)
    ]);

    const calculateChange = (current, previous) => {
      if (previous === 0) return current > 0 ? 100 : 0;
      return ((current - previous) / previous) * 100;
    };

    return {
      current: currentSummary,
      previous: previousSummary,
      changes: {
        income: calculateChange(currentSummary.income.total, previousSummary.income.total),
        expense: calculateChange(currentSummary.expense.total, previousSummary.expense.total),
        balance: calculateChange(currentSummary.balance, previousSummary.balance),
        savingsRate: calculateChange(currentSummary.savingsRate, previousSummary.savingsRate),
        transactionCount: calculateChange(currentSummary.totalTransactions, previousSummary.totalTransactions)
      }
    };
  } catch (error) {
    console.error('Karşılaştırmalı analiz hatası:', error);
    throw error;
  }
};

/**
 * En çok işlem yapılan günler analizi
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {string} startDate - Başlangıç tarihi
 * @param {string} endDate - Bitiş tarihi
 * @returns {Array} Günlük analiz
 */
export const getDayOfWeekAnalysis = async (db, startDate, endDate) => {
  try {
    const dayResult = await db.getAllAsync(`
      SELECT
        CASE strftime('%w', date)
          WHEN '0' THEN 'Pazar'
          WHEN '1' THEN 'Pazartesi'
          WHEN '2' THEN 'Salı'
          WHEN '3' THEN 'Çarşamba'
          WHEN '4' THEN 'Perşembe'
          WHEN '5' THEN 'Cuma'
          WHEN '6' THEN 'Cumartesi'
        END as day_name,
        strftime('%w', date) as day_number,
        type,
        SUM(amount) as total,
        COUNT(*) as count
      FROM transactions
      WHERE date BETWEEN ? AND ?
      GROUP BY strftime('%w', date), type
      ORDER BY day_number
    `, [startDate, endDate]);

    const days = ['Pazar', 'Pazartesi', 'Salı', 'Çarşamba', 'Perşembe', 'Cuma', 'Cumartesi'];
    
    return days.map((dayName, index) => {
      const incomeData = dayResult.find(r => r.day_number == index && r.type === 'income');
      const expenseData = dayResult.find(r => r.day_number == index && r.type === 'expense');
      
      return {
        day: dayName,
        dayNumber: index,
        income: incomeData ? incomeData.total : 0,
        expense: expenseData ? expenseData.total : 0,
        incomeCount: incomeData ? incomeData.count : 0,
        expenseCount: expenseData ? expenseData.count : 0,
        totalTransactions: (incomeData ? incomeData.count : 0) + (expenseData ? expenseData.count : 0)
      };
    });
  } catch (error) {
    console.error('Günlük analiz hatası:', error);
    throw error;
  }
};

/**
 * Büyük harcamalar analizi
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {string} startDate - Başlangıç tarihi
 * @param {string} endDate - Bitiş tarihi
 * @param {number} threshold - Büyük harcama eşiği (varsayılan: ortalama harcamanın 2 katı)
 * @returns {Array} Büyük harcamalar
 */
export const getLargeTransactionsAnalysis = async (db, startDate, endDate, threshold = null) => {
  try {
    // Eğer threshold belirtilmemişse, ortalama harcamanın 2 katını kullan
    if (!threshold) {
      const avgResult = await db.getFirstAsync(`
        SELECT AVG(amount) as avg_amount
        FROM transactions
        WHERE date BETWEEN ? AND ? AND type = 'expense'
      `, [startDate, endDate]);
      
      threshold = (avgResult?.avg_amount || 0) * 2;
    }

    const largeTransactions = await db.getAllAsync(`
      SELECT
        t.id,
        t.amount,
        t.description,
        t.date,
        t.type,
        c.name as category,
        c.color as category_color
      FROM transactions t
      JOIN categories c ON t.category_id = c.id
      WHERE t.date BETWEEN ? AND ? 
        AND t.type = 'expense' 
        AND t.amount >= ?
      ORDER BY t.amount DESC
      LIMIT 20
    `, [startDate, endDate, threshold]);

    return {
      threshold,
      transactions: largeTransactions,
      totalAmount: largeTransactions.reduce((sum, t) => sum + t.amount, 0),
      count: largeTransactions.length
    };
  } catch (error) {
    console.error('Büyük harcamalar analizi hatası:', error);
    throw error;
  }
};

/**
 * Tam rapor verisi oluştur
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} options - Rapor seçenekleri
 * @returns {Object} Tam rapor verisi
 */
export const generateFullReport = async (db, options = {}) => {
  try {
    const {
      period = 'month',
      customStart = null,
      customEnd = null,
      includeComparison = true,
      includeTrends = true,
      includeCategories = true,
      includeDayAnalysis = true,
      includeLargeTransactions = true
    } = options;

    const { startDate, endDate } = getDateRange(period, customStart, customEnd);
    
    // Temel özet
    const summary = await getFinancialSummary(db, startDate, endDate);
    
    const reportData = {
      period: {
        type: period,
        startDate,
        endDate,
        label: getPeriodLabel(period, startDate, endDate)
      },
      summary
    };

    // Kategori analizi
    if (includeCategories) {
      reportData.categories = {
        all: await getCategoryAnalysis(db, startDate, endDate, 'all'),
        income: await getCategoryAnalysis(db, startDate, endDate, 'income'),
        expense: await getCategoryAnalysis(db, startDate, endDate, 'expense')
      };
    }

    // Trend analizi
    if (includeTrends) {
      const groupBy = period === 'week' ? 'day' : period === 'month' ? 'day' : 'month';
      reportData.trends = await getTrendAnalysis(db, startDate, endDate, groupBy);
    }

    // Karşılaştırmalı analiz
    if (includeComparison) {
      const { startDate: prevStart, endDate: prevEnd } = getPreviousPeriod(period, startDate, endDate);
      reportData.comparison = await getComparativeAnalysis(db, startDate, endDate, prevStart, prevEnd);
    }

    // Günlük analiz
    if (includeDayAnalysis) {
      reportData.dayAnalysis = await getDayOfWeekAnalysis(db, startDate, endDate);
    }

    // Büyük harcamalar
    if (includeLargeTransactions) {
      reportData.largeTransactions = await getLargeTransactionsAnalysis(db, startDate, endDate);
    }

    return reportData;
  } catch (error) {
    console.error('Tam rapor oluşturma hatası:', error);
    throw error;
  }
};

/**
 * Önceki dönem tarihlerini hesapla
 */
const getPreviousPeriod = (period, currentStart, currentEnd) => {
  const start = new Date(currentStart);
  const end = new Date(currentEnd);
  const diffDays = Math.ceil((end - start) / (1000 * 60 * 60 * 24));
  
  const prevEnd = new Date(start);
  prevEnd.setDate(prevEnd.getDate() - 1);
  
  const prevStart = new Date(prevEnd);
  prevStart.setDate(prevStart.getDate() - diffDays);
  
  return {
    startDate: format(prevStart, 'yyyy-MM-dd'),
    endDate: format(prevEnd, 'yyyy-MM-dd')
  };
};

/**
 * Dönem etiketi oluştur
 */
const getPeriodLabel = (period, startDate, endDate) => {
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  switch (period) {
    case 'week':
      return `${format(start, 'd MMM', { locale: tr })} - ${format(end, 'd MMM yyyy', { locale: tr })}`;
    case 'month':
      return format(start, 'MMMM yyyy', { locale: tr });
    case '3months':
      return `${format(start, 'MMM', { locale: tr })} - ${format(end, 'MMM yyyy', { locale: tr })}`;
    case '6months':
      return `${format(start, 'MMM', { locale: tr })} - ${format(end, 'MMM yyyy', { locale: tr })}`;
    case 'year':
      return format(start, 'yyyy', { locale: tr });
    default:
      return `${format(start, 'd MMM', { locale: tr })} - ${format(end, 'd MMM yyyy', { locale: tr })}`;
  }
};
