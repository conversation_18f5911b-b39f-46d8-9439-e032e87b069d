import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Switch,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Colors } from '../constants/colors';
import * as salaryReminderService from '../services/salaryReminderService';
import * as categoryService from '../services/categoryService';
import * as reminderTagService from '../services/reminderTagService';
import { formatCurrency, getCurrencySymbol } from '../utils/formatters';

/**
 * Maaş Hatırlatıcısı Form Ekranı
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @param {Object} props.route - Route nesnesi
 * @returns {JSX.Element} Maaş Hatırlatıcısı Form Ekranı
 */
export default function SalaryReminderFormScreen({ navigation, route }) {
  const db = useSQLiteContext();
  const { reminderId } = route?.params || {};
  const isEditing = !!reminderId;

  // Durum değişkenleri
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [categories, setCategories] = useState([]);
  const [tags, setTags] = useState([]);
  const [selectedTags, setSelectedTags] = useState([]);
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [showTagsModal, setShowTagsModal] = useState(false);
  const [showCurrencyModal, setShowCurrencyModal] = useState(false);
  const [showSalaryTypeModal, setShowSalaryTypeModal] = useState(false);
  const [showPaymentDayModal, setShowPaymentDayModal] = useState(false);
  const [showRemindDaysModal, setShowRemindDaysModal] = useState(false);
  const [supportedCurrencies, setSupportedCurrencies] = useState(['TRY', 'USD', 'EUR', 'GBP']);
  const [paymentDayOptions, setPaymentDayOptions] = useState([]);
  const [remindDaysOptions, setRemindDaysOptions] = useState([]);
  const [salaryTypes, setSalaryTypes] = useState([]);

  // Form verileri
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    amount: '',
    currency: 'TRY',
    salary_type: 'regular',
    payment_day: 1,
    category_id: null,
    remind_days_before: 3,
    priority: 'normal',
    is_enabled: true
  });

  // Verileri yükle
  const loadData = useCallback(async () => {
    try {
      setLoading(true);

      // Kategorileri getir (sadece gelir kategorileri)
      const incomeCategories = await categoryService.getCategories(db, 'income');
      setCategories(incomeCategories);

      // Etiketleri getir
      const allTags = await reminderTagService.getAllTags(db);
      setTags(allTags);

      // Maaş türlerini getir
      const types = salaryReminderService.getSalaryTypes();
      setSalaryTypes(types);

      // Ödeme günü seçeneklerini getir
      const dayOptions = salaryReminderService.getPaymentDayOptions();
      setPaymentDayOptions(dayOptions);

      // Hatırlatma gün seçeneklerini getir
      const daysOptions = salaryReminderService.getRemindDaysOptions();
      setRemindDaysOptions(daysOptions);

      // Düzenleme modunda ise mevcut hatırlatıcı verilerini getir
      if (isEditing) {
        const reminderDetails = await salaryReminderService.getSalaryReminderById(db, reminderId);

        if (reminderDetails) {
          // Form verilerini doldur
          setFormData({
            title: reminderDetails.title,
            description: reminderDetails.message,
            amount: reminderDetails.amount ? reminderDetails.amount.toString() : '',
            currency: reminderDetails.currency || 'TRY',
            salary_type: reminderDetails.salary_type || 'regular',
            payment_day: reminderDetails.payment_day || 1,
            category_id: reminderDetails.category_id,
            remind_days_before: reminderDetails.remind_days_before || 3,
            priority: reminderDetails.priority || 'normal',
            is_enabled: reminderDetails.is_enabled === 1
          });

          // Seçili etiketleri getir
          const reminderTags = await reminderTagService.getTagsByReminderId(db, reminderId);
          setSelectedTags(reminderTags.map(tag => tag.id));
        }
      }

      setLoading(false);
    } catch (error) {
      console.error('Veri yükleme hatası:', error);
      Alert.alert('Hata', 'Veriler yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  }, [isEditing, reminderId, db]);

  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [loadData])
  );

  // Form alanını güncelle
  const updateFormField = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Kategori seç
  const selectCategory = (category) => {
    updateFormField('category_id', category.id);
    setShowCategoryModal(false);
  };

  // Para birimi değiştir
  const changeCurrency = (currency) => {
    updateFormField('currency', currency);
    setShowCurrencyModal(false);
  };

  // Maaş türü değiştir
  const changeSalaryType = (type) => {
    updateFormField('salary_type', type.id);
    setShowSalaryTypeModal(false);
  };

  // Ödeme günü değiştir
  const changePaymentDay = (option) => {
    updateFormField('payment_day', option.value);
    setShowPaymentDayModal(false);
  };

  // Hatırlatma günü değiştir
  const changeRemindDays = (option) => {
    updateFormField('remind_days_before', option.value);
    setShowRemindDaysModal(false);
  };

  // Etiket seç/kaldır
  const toggleTag = (tagId) => {
    setSelectedTags(prev => {
      if (prev.includes(tagId)) {
        return prev.filter(id => id !== tagId);
      } else {
        return [...prev, tagId];
      }
    });
  };

  // Formu doğrula
  const validateForm = () => {
    if (!formData.title.trim()) {
      Alert.alert('Hata', 'Lütfen bir başlık girin.');
      return false;
    }

    if (!formData.amount.trim() || isNaN(parseFloat(formData.amount))) {
      Alert.alert('Hata', 'Lütfen geçerli bir miktar girin.');
      return false;
    }

    return true;
  };

  // Hatırlatıcıyı kaydet
  const saveSalaryReminder = async () => {
    if (!validateForm()) return;

    try {
      setSaving(true);

      const reminderData = {
        ...formData,
        amount: parseFloat(formData.amount)
      };

      if (isEditing) {
        // Mevcut hatırlatıcıyı güncelle
        await salaryReminderService.updateSalaryReminder(
          db,
          reminderId,
          reminderData,
          selectedTags
        );
        Alert.alert('Başarılı', 'Maaş hatırlatıcısı güncellendi.');
      } else {
        // Yeni hatırlatıcı oluştur
        await salaryReminderService.addSalaryReminder(
          db,
          reminderData,
          selectedTags
        );
        Alert.alert('Başarılı', 'Maaş hatırlatıcısı oluşturuldu.');
      }

      setSaving(false);
      navigation.goBack();
    } catch (error) {
      console.error('Maaş hatırlatıcısı kaydetme hatası:', error);
      Alert.alert('Hata', 'Maaş hatırlatıcısı kaydedilirken bir hata oluştu.');
      setSaving(false);
    }
  };

  // Öncelik rengini getir
  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return Colors.DANGER;
      case 'normal': return Colors.WARNING;
      case 'low': return Colors.SUCCESS;
      default: return Colors.WARNING;
    }
  };

  // Öncelik metnini getir
  const getPriorityText = (priority) => {
    switch (priority) {
      case 'high': return 'Yüksek';
      case 'normal': return 'Normal';
      case 'low': return 'Düşük';
      default: return 'Normal';
    }
  };

  // Maaş türü bilgilerini getir
  const getSalaryTypeInfo = (typeId) => {
    const type = salaryTypes.find(t => t.id === typeId);
    return type || { name: 'Normal Maaş', icon: 'account-balance-wallet', color: '#2ecc71' };
  };

  // Ödeme günü metnini getir
  const getPaymentDayText = (day) => {
    const option = paymentDayOptions.find(o => o.value === day);
    return option ? option.label : 'Ayın 1. günü';
  };

  // Hatırlatma gün seçeneğini getir
  const getRemindDaysText = (days) => {
    const option = remindDaysOptions.find(o => o.value === days);
    return option ? option.label : '3 gün önce';
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={styles.loadingText}>Yükleniyor...</Text>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {isEditing ? 'Maaş Hatırlatıcısını Düzenle' : 'Yeni Maaş Hatırlatıcısı'}
        </Text>
        <TouchableOpacity
          style={styles.saveButton}
          onPress={saveSalaryReminder}
          disabled={saving}
        >
          {saving ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <MaterialIcons name="check" size={24} color="#fff" />
          )}
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>Maaş Bilgileri</Text>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Başlık</Text>
            <TextInput
              style={styles.input}
              value={formData.title}
              onChangeText={(text) => updateFormField('title', text)}
              placeholder="Örn: Aylık Maaş"
              placeholderTextColor="#999"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Açıklama (Opsiyonel)</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={formData.description}
              onChangeText={(text) => updateFormField('description', text)}
              placeholder="Maaş ile ilgili notlar..."
              placeholderTextColor="#999"
              multiline
              numberOfLines={3}
              textAlignVertical="top"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Maaş Türü</Text>
            <TouchableOpacity
              style={styles.selector}
              onPress={() => setShowSalaryTypeModal(true)}
            >
              <View style={styles.selectorContent}>
                <View style={[styles.salaryTypeIcon, { backgroundColor: getSalaryTypeInfo(formData.salary_type).color }]}>
                  <MaterialIcons
                    name={getSalaryTypeInfo(formData.salary_type).icon}
                    size={20}
                    color="#fff"
                  />
                </View>
                <Text style={styles.selectorText}>
                  {getSalaryTypeInfo(formData.salary_type).name}
                </Text>
              </View>
              <MaterialIcons name="arrow-drop-down" size={24} color={Colors.GRAY_600} />
            </TouchableOpacity>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Miktar</Text>
            <View style={styles.amountContainer}>
              <TextInput
                style={styles.amountInput}
                value={formData.amount}
                onChangeText={(text) => updateFormField('amount', text.replace(',', '.'))}
                placeholder="0.00"
                placeholderTextColor="#999"
                keyboardType="numeric"
              />
              <TouchableOpacity
                style={styles.currencySelector}
                onPress={() => setShowCurrencyModal(true)}
              >
                <Text style={styles.currencyText}>
                  {getCurrencySymbol(formData.currency)} {formData.currency}
                </Text>
                <MaterialIcons name="arrow-drop-down" size={20} color={Colors.GRAY_600} />
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Ödeme Günü</Text>
            <TouchableOpacity
              style={styles.selector}
              onPress={() => setShowPaymentDayModal(true)}
            >
              <Text style={styles.selectorText}>
                {getPaymentDayText(formData.payment_day)}
              </Text>
              <MaterialIcons name="arrow-drop-down" size={24} color={Colors.GRAY_600} />
            </TouchableOpacity>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Kategori (Opsiyonel)</Text>
            <TouchableOpacity
              style={styles.selector}
              onPress={() => setShowCategoryModal(true)}
            >
              {formData.category_id ? (
                <View style={styles.selectorContent}>
                  {(() => {
                    const category = categories.find(c => c.id === formData.category_id);
                    return category ? (
                      <>
                        <MaterialIcons
                          name={category.icon || "category"}
                          size={20}
                          color={category.color || Colors.PRIMARY}
                        />
                        <Text style={styles.selectorText}>{category.name}</Text>
                      </>
                    ) : (
                      <Text style={styles.selectorText}>Kategori seçin</Text>
                    );
                  })()}
                </View>
              ) : (
                <Text style={styles.selectorText}>Kategori seçin</Text>
              )}
              <MaterialIcons name="arrow-drop-down" size={24} color={Colors.GRAY_600} />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>Hatırlatıcı Ayarları</Text>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Ne Zaman Hatırlatılsın?</Text>
            <TouchableOpacity
              style={styles.selector}
              onPress={() => setShowRemindDaysModal(true)}
            >
              <Text style={styles.selectorText}>
                {getRemindDaysText(formData.remind_days_before)}
              </Text>
              <MaterialIcons name="arrow-drop-down" size={24} color={Colors.GRAY_600} />
            </TouchableOpacity>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Öncelik</Text>
            <View style={styles.prioritySelector}>
              {['low', 'normal', 'high'].map((priority) => (
                <TouchableOpacity
                  key={priority}
                  style={[
                    styles.priorityOption,
                    formData.priority === priority && styles.priorityOptionSelected,
                    { borderColor: getPriorityColor(priority) }
                  ]}
                  onPress={() => updateFormField('priority', priority)}
                >
                  <MaterialIcons
                    name={
                      priority === 'low' ? 'arrow-downward' :
                      priority === 'high' ? 'arrow-upward' : 'remove'
                    }
                    size={20}
                    color={getPriorityColor(priority)}
                  />
                  <Text style={[
                    styles.priorityOptionText,
                    { color: getPriorityColor(priority) }
                  ]}>
                    {getPriorityText(priority)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.formGroup}>
            <View style={styles.switchContainer}>
              <Text style={styles.switchLabel}>Etkinleştir</Text>
              <Switch
                value={formData.is_enabled}
                onValueChange={(value) => updateFormField('is_enabled', value)}
                trackColor={{ false: Colors.GRAY_300, true: Colors.PRIMARY_LIGHT }}
                thumbColor={formData.is_enabled ? Colors.PRIMARY : Colors.GRAY_500}
              />
            </View>
            <Text style={styles.switchDescription}>
              {formData.is_enabled ? 'Hatırlatıcı etkin' : 'Hatırlatıcı devre dışı'}
            </Text>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Etiketler (Opsiyonel)</Text>
            <TouchableOpacity
              style={styles.tagsSelector}
              onPress={() => setShowTagsModal(true)}
            >
              {selectedTags.length > 0 ? (
                <View style={styles.selectedTagsContainer}>
                  {selectedTags.map(tagId => {
                    const tag = tags.find(t => t.id === tagId);
                    return tag ? (
                      <View
                        key={tag.id}
                        style={[styles.tagBadge, { backgroundColor: tag.color || Colors.PRIMARY }]}
                      >
                        <Text style={styles.tagBadgeText}>{tag.name}</Text>
                      </View>
                    ) : null;
                  })}
                </View>
              ) : (
                <Text style={styles.selectorText}>Etiket seçin</Text>
              )}
              <MaterialIcons name="arrow-drop-down" size={24} color={Colors.GRAY_600} />
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>

      {/* Kategori Seçim Modalı */}
      {showCategoryModal && (
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Kategori Seçin</Text>
              <TouchableOpacity
                style={styles.modalCloseButton}
                onPress={() => setShowCategoryModal(false)}
              >
                <MaterialIcons name="close" size={24} color={Colors.GRAY_600} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody}>
              {categories.map((category) => (
                <TouchableOpacity
                  key={category.id}
                  style={[
                    styles.modalItem,
                    formData.category_id === category.id && styles.modalItemSelected
                  ]}
                  onPress={() => selectCategory(category)}
                >
                  <View style={styles.modalItemContent}>
                    <MaterialIcons
                      name={category.icon || "category"}
                      size={20}
                      color={category.color || Colors.PRIMARY}
                    />
                    <Text style={styles.modalItemText}>{category.name}</Text>
                  </View>
                  {formData.category_id === category.id && (
                    <MaterialIcons name="check" size={20} color={Colors.SUCCESS} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      )}

      {/* Para Birimi Seçim Modalı */}
      {showCurrencyModal && (
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Para Birimi Seçin</Text>
              <TouchableOpacity
                style={styles.modalCloseButton}
                onPress={() => setShowCurrencyModal(false)}
              >
                <MaterialIcons name="close" size={24} color={Colors.GRAY_600} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody}>
              {supportedCurrencies.map((currency) => (
                <TouchableOpacity
                  key={currency}
                  style={[
                    styles.modalItem,
                    formData.currency === currency && styles.modalItemSelected
                  ]}
                  onPress={() => changeCurrency(currency)}
                >
                  <View style={styles.modalItemContent}>
                    <Text style={styles.currencySymbol}>{getCurrencySymbol(currency)}</Text>
                    <Text style={styles.modalItemText}>{currency}</Text>
                  </View>
                  {formData.currency === currency && (
                    <MaterialIcons name="check" size={20} color={Colors.SUCCESS} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      )}

      {/* Maaş Türü Seçim Modalı */}
      {showSalaryTypeModal && (
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Maaş Türü Seçin</Text>
              <TouchableOpacity
                style={styles.modalCloseButton}
                onPress={() => setShowSalaryTypeModal(false)}
              >
                <MaterialIcons name="close" size={24} color={Colors.GRAY_600} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody}>
              {salaryTypes.map((type) => (
                <TouchableOpacity
                  key={type.id}
                  style={[
                    styles.modalItem,
                    formData.salary_type === type.id && styles.modalItemSelected
                  ]}
                  onPress={() => changeSalaryType(type)}
                >
                  <View style={styles.modalItemContent}>
                    <View style={[styles.salaryTypeIcon, { backgroundColor: type.color }]}>
                      <MaterialIcons name={type.icon} size={20} color="#fff" />
                    </View>
                    <Text style={styles.modalItemText}>{type.name}</Text>
                  </View>
                  {formData.salary_type === type.id && (
                    <MaterialIcons name="check" size={20} color={Colors.SUCCESS} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      )}

      {/* Ödeme Günü Seçim Modalı */}
      {showPaymentDayModal && (
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Ödeme Günü Seçin</Text>
              <TouchableOpacity
                style={styles.modalCloseButton}
                onPress={() => setShowPaymentDayModal(false)}
              >
                <MaterialIcons name="close" size={24} color={Colors.GRAY_600} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody}>
              {paymentDayOptions.map((option) => (
                <TouchableOpacity
                  key={option.value}
                  style={[
                    styles.modalItem,
                    formData.payment_day === option.value && styles.modalItemSelected
                  ]}
                  onPress={() => changePaymentDay(option)}
                >
                  <Text style={styles.modalItemText}>{option.label}</Text>
                  {formData.payment_day === option.value && (
                    <MaterialIcons name="check" size={20} color={Colors.SUCCESS} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      )}

      {/* Hatırlatma Günü Seçim Modalı */}
      {showRemindDaysModal && (
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Hatırlatma Zamanı Seçin</Text>
              <TouchableOpacity
                style={styles.modalCloseButton}
                onPress={() => setShowRemindDaysModal(false)}
              >
                <MaterialIcons name="close" size={24} color={Colors.GRAY_600} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody}>
              {remindDaysOptions.map((option) => (
                <TouchableOpacity
                  key={option.value}
                  style={[
                    styles.modalItem,
                    formData.remind_days_before === option.value && styles.modalItemSelected
                  ]}
                  onPress={() => changeRemindDays(option)}
                >
                  <Text style={styles.modalItemText}>{option.label}</Text>
                  {formData.remind_days_before === option.value && (
                    <MaterialIcons name="check" size={20} color={Colors.SUCCESS} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      )}

      {/* Etiket Seçim Modalı */}
      {showTagsModal && (
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Etiketleri Seçin</Text>
              <TouchableOpacity
                style={styles.modalCloseButton}
                onPress={() => setShowTagsModal(false)}
              >
                <MaterialIcons name="close" size={24} color={Colors.GRAY_600} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody}>
              {tags.map((tag) => (
                <TouchableOpacity
                  key={tag.id}
                  style={[
                    styles.modalItem,
                    selectedTags.includes(tag.id) && styles.modalItemSelected
                  ]}
                  onPress={() => toggleTag(tag.id)}
                >
                  <View style={styles.modalItemContent}>
                    <View style={[styles.tagDot, { backgroundColor: tag.color || Colors.PRIMARY }]} />
                    <Text style={styles.modalItemText}>{tag.name}</Text>
                  </View>
                  {selectedTags.includes(tag.id) && (
                    <MaterialIcons name="check" size={20} color={Colors.SUCCESS} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      )}
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa'
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center'
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.GRAY_600
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 16,
    paddingHorizontal: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2
  },
  backButton: {
    padding: 8
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff'
  },
  saveButton: {
    padding: 8
  },
  content: {
    flex: 1,
    padding: 16
  },
  formSection: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.GRAY_800,
    marginBottom: 16
  },
  formGroup: {
    marginBottom: 16
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.GRAY_700,
    marginBottom: 8
  },
  input: {
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: Colors.GRAY_800
  },
  textArea: {
    minHeight: 80
  },
  selector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10
  },
  selectorContent: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  selectorText: {
    fontSize: 16,
    color: Colors.GRAY_800,
    marginLeft: 8
  },
  amountContainer: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  amountInput: {
    flex: 1,
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: Colors.GRAY_800
  },
  currencySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.GRAY_200,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    marginLeft: 8
  },
  currencyText: {
    fontSize: 16,
    color: Colors.GRAY_800
  },
  prioritySelector: {
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  priorityOption: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    borderWidth: 1,
    paddingVertical: 8,
    marginHorizontal: 4
  },
  priorityOptionSelected: {
    backgroundColor: Colors.GRAY_50
  },
  priorityOptionText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4
  },
  switchLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.GRAY_800
  },
  switchDescription: {
    fontSize: 14,
    color: Colors.GRAY_600
  },
  tagsSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10
  },
  selectedTagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap'
  },
  tagBadge: {
    borderRadius: 16,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginRight: 4,
    marginBottom: 4
  },
  tagBadgeText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '500'
  },
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden'
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.GRAY_800
  },
  modalCloseButton: {
    padding: 4
  },
  modalBody: {
    padding: 16,
    maxHeight: 400
  },
  modalItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_100
  },
  modalItemSelected: {
    backgroundColor: Colors.GRAY_50
  },
  modalItemContent: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  modalItemText: {
    fontSize: 16,
    color: Colors.GRAY_800,
    marginLeft: 8
  },
  salaryTypeIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center'
  },
  currencySymbol: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.PRIMARY,
    width: 30,
    textAlign: 'center'
  },
  tagDot: {
    width: 12,
    height: 12,
    borderRadius: 6
  }
});
