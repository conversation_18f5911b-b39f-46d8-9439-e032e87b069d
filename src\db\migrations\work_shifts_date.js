/**
 * work_shifts tablosuna date sütunu ekleyen migrasyon
 *
 * @param {object} db - SQLite veritabanı nesnesi
 * @returns {Promise<void>}
 */
export const migrateWorkShiftsDate = async (db) => {
  try {
    console.log('work_shifts tablosuna date sütunu ekleme migrasyonu başlatılıyor...');

    // work_shifts tablosunu kontrol et
    const hasWorkShiftsTable = await db.getFirstAsync(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='work_shifts'
    `);

    if (hasWorkShiftsTable) {
      console.log('work_shifts tablosu mevcut, sütunları kontrol ediliyor...');

      // Tabloda eksik sütunlar var mı kontrol et
      const columns = await db.getAllAsync(`PRAGMA table_info(work_shifts)`);

      // Sütun adlarını bir diziye dönüştür
      const columnNames = columns.map(col => col.name);

      // date sütunu yoksa ekle
      if (!columnNames.includes('date')) {
        console.log('date sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE work_shifts ADD COLUMN date TEXT NOT NULL DEFAULT '2023-01-01'`);

        // Mevcut kayıtlar için date değerini start_time'dan çıkar
        try {
          const shifts = await db.getAllAsync(`SELECT id, start_time FROM work_shifts`);

          for (const shift of shifts) {
            try {
              if (shift.start_time) {
                // Eğer start_time bir ISO tarih formatındaysa (T içeriyorsa)
                if (shift.start_time.includes('T')) {
                  const datePart = shift.start_time.split('T')[0];
                  await db.runAsync(`UPDATE work_shifts SET date = ? WHERE id = ?`, [datePart, shift.id]);
                }
                // Eğer start_time sadece saat formatındaysa (HH:MM:SS)
                else {
                  // Varsayılan bir tarih ata
                  await db.runAsync(`UPDATE work_shifts SET date = ? WHERE id = ?`, ['2023-01-01', shift.id]);
                }
              }
            } catch (shiftError) {
              console.error(`Vardiya güncelleme hatası (ID: ${shift.id}):`, shiftError);
            }
          }
        } catch (shiftsError) {
          console.error('Vardiyaları getirme hatası:', shiftsError);
        }

        console.log('date sütunu başarıyla eklendi ve mevcut kayıtlar güncellendi.');
      } else {
        console.log('date sütunu zaten mevcut.');
      }
    } else {
      console.log('work_shifts tablosu bulunamadı, migrasyon atlanıyor.');
    }

    console.log('work_shifts tablosuna date sütunu ekleme migrasyonu tamamlandı.');
  } catch (error) {
    console.error('work_shifts tablosuna date sütunu ekleme migrasyon hatası:', error);
    throw error;
  }
};
