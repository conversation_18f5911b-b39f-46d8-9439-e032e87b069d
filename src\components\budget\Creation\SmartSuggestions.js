/**
 * <PERSON>k<PERSON>ll<PERSON> Bütçe Önerileri Komponenti
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md - Stage 2, Phase 3
 * 
 * Geçmiş verilere dayalı bütçe önerileri
 * Maksimum 250 satır - Tek sorumluluk prensibi
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../../../context/ThemeContext';
import { calculateBudgetSuggestion } from '../../../services/budget';
import { formatTRY, formatUSD, formatEUR } from '../../../utils/formatters';

/**
 * Akıllı bütçe önerileri komponenti
 * @param {Object} props - Component props
 * @param {string} props.budgetType - Bütçe türü ('total', 'category_based', 'flexible')
 * @param {string} props.period - Dönem türü ('monthly', 'weekly', 'custom')
 * @param {Array} props.selectedCategories - Seçili kategoriler
 * @param {string} props.currency - Para birimi ('TRY', 'USD', 'EUR')
 * @param {Function} props.onSuggestionApply - Öneri uygulama callback
 * @param {Object} props.theme - Tema objesi (opsiyonel, context'ten alınır)
 */
const SmartSuggestions = ({ 
  budgetType, 
  period, 
  selectedCategories = [], 
  currency = 'TRY',
  onSuggestionApply,
  theme: propTheme 
}) => {
  const { theme } = useTheme();
  const currentTheme = propTheme || theme;

  // State yönetimi
  const [suggestions, setSuggestions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedSuggestion, setSelectedSuggestion] = useState(null);

  /**
   * Önerileri yükle
   */
  const loadSuggestions = async () => {
    try {
      setLoading(true);
      
      // Geçmiş harcama verilerini analiz et (şimdilik mock data)
      const historicalData = {
        averageTotal: 5000,
        categories: {}
      };

      // Önerileri oluştur
      const generatedSuggestions = generateSuggestions(historicalData);
      setSuggestions(generatedSuggestions);
    } catch (error) {
      console.error('Öneriler yüklenirken hata:', error);
      setSuggestions([]);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Önerileri oluştur
   * @param {Object} historicalData - Geçmiş harcama verileri
   * @returns {Array} Öneri listesi
   */
  const generateSuggestions = (historicalData) => {
    const suggestions = [];

    if (budgetType === 'total') {
      // Toplam bütçe önerileri
      const avgSpending = historicalData.averageTotal || 0;
      
      suggestions.push({
        id: 'conservative',
        title: 'Muhafazakar Bütçe',
        description: 'Geçmiş ortalamadan %10 daha az',
        amount: Math.round(avgSpending * 0.9),
        icon: 'trending-down',
        color: '#4CAF50',
        confidence: 'Yüksek',
        reasoning: 'Tasarruf odaklı yaklaşım'
      });

      suggestions.push({
        id: 'balanced',
        title: 'Dengeli Bütçe',
        description: 'Geçmiş ortalama harcama',
        amount: Math.round(avgSpending),
        icon: 'balance',
        color: '#2196F3',
        confidence: 'Yüksek',
        reasoning: 'Mevcut harcama alışkanlıklarınıza uygun'
      });

      suggestions.push({
        id: 'flexible',
        title: 'Esnek Bütçe',
        description: 'Geçmiş ortalamadan %15 daha fazla',
        amount: Math.round(avgSpending * 1.15),
        icon: 'trending-up',
        color: '#FF9800',
        confidence: 'Orta',
        reasoning: 'Beklenmedik harcamalar için ek alan'
      });
    } else if (budgetType === 'category_based') {
      // Kategori bazlı öneriler
      selectedCategories.forEach(category => {
        const categoryData = historicalData.categories?.[category.id];
        if (categoryData) {
          const avgAmount = categoryData.average || 0;
          
          suggestions.push({
            id: `category_${category.id}`,
            title: `${category.name} Önerisi`,
            description: 'Son 3 aylık ortalama',
            amount: Math.round(avgAmount),
            categoryId: category.id,
            icon: category.icon || 'category',
            color: '#2196F3',
            confidence: categoryData.consistency > 0.7 ? 'Yüksek' : 'Orta',
            reasoning: `${categoryData.transactionCount} işlem analizi`
          });
        }
      });
    }

    return suggestions;
  };

  // Component mount edildiğinde önerileri yükle
  useEffect(() => {
    if (selectedCategories.length > 0 || budgetType === 'total') {
      loadSuggestions();
    }
  }, [budgetType, period, selectedCategories]);

  /**
   * Öneri seçim işleyicisi
   * @param {Object} suggestion - Seçilen öneri
   */
  const handleSuggestionSelect = (suggestion) => {
    setSelectedSuggestion(suggestion.id);
  };

  /**
   * Öneri uygulama işleyicisi
   */
  const handleApplySuggestion = () => {
    const suggestion = suggestions.find(s => s.id === selectedSuggestion);
    if (suggestion && onSuggestionApply) {
      onSuggestionApply(suggestion);
    }
  };

  /**
   * Para birimi formatı
   * @param {number} value - Formatlanacak değer
   * @returns {string} Formatlanmış değer
   */
  const formatCurrency = (value) => {
    switch (currency) {
      case 'USD':
        return formatUSD(value);
      case 'EUR':
        return formatEUR(value);
      default:
        return formatTRY(value);
    }
  };

  /**
   * Para birimi sembolü
   * @returns {string} Para birimi sembolü
   */
  const getCurrencySymbol = () => {
    switch (currency) {
      case 'USD':
        return '$';
      case 'EUR':
        return '€';
      default:
        return '₺';
    }
  };

  /**
   * Güven seviyesi rengi
   * @param {string} confidence - Güven seviyesi
   * @returns {string} Renk kodu
   */
  const getConfidenceColor = (confidence) => {
    switch (confidence) {
      case 'Yüksek':
        return currentTheme.SUCCESS;
      case 'Orta':
        return currentTheme.WARNING;
      default:
        return currentTheme.TEXT_SECONDARY;
    }
  };

  if (loading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: currentTheme.BACKGROUND }]}>
        <ActivityIndicator size="large" color={currentTheme.PRIMARY} />
        <Text style={[styles.loadingText, { color: currentTheme.TEXT_SECONDARY }]}>
          Akıllı öneriler hazırlanıyor...
        </Text>
      </View>
    );
  }

  if (suggestions.length === 0) {
    return (
      <View style={[styles.emptyContainer, { backgroundColor: currentTheme.BACKGROUND }]}>
        <MaterialIcons name="lightbulb-outline" size={64} color={currentTheme.TEXT_SECONDARY} />
        <Text style={[styles.emptyTitle, { color: currentTheme.TEXT_PRIMARY }]}>
          Henüz öneri yok
        </Text>
        <Text style={[styles.emptySubtitle, { color: currentTheme.TEXT_SECONDARY }]}>
          Daha fazla harcama verisi toplandıkça akıllı öneriler görünecek
        </Text>
      </View>
    );
  }

  const currencySymbol = getCurrencySymbol();

  return (
    <View style={[styles.container, { backgroundColor: currentTheme.BACKGROUND }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: currentTheme.TEXT_PRIMARY }]}>
          Akıllı Bütçe Önerileri
        </Text>
        <Text style={[styles.subtitle, { color: currentTheme.TEXT_SECONDARY }]}>
          Geçmiş harcamalarınıza dayalı öneriler
        </Text>
      </View>

      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {suggestions.map((suggestion) => {
          const isSelected = selectedSuggestion === suggestion.id;
          
          return (
            <TouchableOpacity
              key={suggestion.id}
              style={[
                styles.suggestionCard,
                {
                  backgroundColor: currentTheme.SURFACE,
                  borderColor: isSelected 
                    ? currentTheme.PRIMARY 
                    : currentTheme.BORDER,
                  borderWidth: isSelected ? 2 : 1,
                }
              ]}
              onPress={() => handleSuggestionSelect(suggestion)}
              activeOpacity={0.7}
            >
              <View style={styles.suggestionHeader}>
                <View style={[styles.suggestionIcon, { backgroundColor: suggestion.color + '20' }]}>
                  <MaterialIcons 
                    name={suggestion.icon} 
                    size={24} 
                    color={suggestion.color} 
                  />
                </View>
                
                <View style={styles.suggestionInfo}>
                  <Text style={[styles.suggestionTitle, { color: currentTheme.TEXT_PRIMARY }]}>
                    {suggestion.title}
                  </Text>
                  <Text style={[styles.suggestionDescription, { color: currentTheme.TEXT_SECONDARY }]}>
                    {suggestion.description}
                  </Text>
                </View>

                <MaterialIcons
                  name={isSelected ? 'radio-button-checked' : 'radio-button-unchecked'}
                  size={24}
                  color={isSelected ? currentTheme.PRIMARY : currentTheme.TEXT_SECONDARY}
                />
              </View>

              <View style={styles.suggestionDetails}>
                <View style={styles.amountSection}>
                  <Text style={[styles.amount, { color: currentTheme.TEXT_PRIMARY }]}>
                    {currencySymbol}{suggestion.amount.toLocaleString('tr-TR')}
                  </Text>
                  <Text style={[styles.amountFormatted, { color: currentTheme.TEXT_SECONDARY }]}>
                    {formatCurrency(suggestion.amount)}
                  </Text>
                </View>

                <View style={styles.metaInfo}>
                  <View style={[styles.confidenceBadge, { backgroundColor: getConfidenceColor(suggestion.confidence) + '20' }]}>
                    <Text style={[styles.confidenceText, { color: getConfidenceColor(suggestion.confidence) }]}>
                      {suggestion.confidence} Güven
                    </Text>
                  </View>
                  
                  <Text style={[styles.reasoning, { color: currentTheme.TEXT_SECONDARY }]}>
                    {suggestion.reasoning}
                  </Text>
                </View>
              </View>
            </TouchableOpacity>
          );
        })}
      </ScrollView>

      {/* Uygula butonu */}
      {selectedSuggestion && (
        <View style={styles.actionSection}>
          <TouchableOpacity
            style={[styles.applyButton, { backgroundColor: currentTheme.PRIMARY }]}
            onPress={handleApplySuggestion}
          >
            <MaterialIcons name="check" size={24} color={currentTheme.WHITE} />
            <Text style={[styles.applyButtonText, { color: currentTheme.WHITE }]}>
              Öneriyi Uygula
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  loadingText: {
    fontSize: 16,
    marginTop: 16,
    textAlign: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  header: {
    paddingVertical: 16,
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 80,
  },
  suggestionCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  suggestionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 12,
  },
  suggestionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  suggestionInfo: {
    flex: 1,
  },
  suggestionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  suggestionDescription: {
    fontSize: 13,
  },
  suggestionDetails: {
    gap: 8,
  },
  amountSection: {
    alignItems: 'center',
  },
  amount: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  amountFormatted: {
    fontSize: 12,
  },
  metaInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  confidenceBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  confidenceText: {
    fontSize: 11,
    fontWeight: 'bold',
  },
  reasoning: {
    fontSize: 11,
    flex: 1,
    textAlign: 'right',
  },
  actionSection: {
    position: 'absolute',
    bottom: 16,
    left: 16,
    right: 16,
  },
  applyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
  },
  applyButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default SmartSuggestions;
