import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Modal,
  Platform,
  TextInput
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Colors } from '../constants/colors';
import * as workService from '../services/workService';
import { formatCurrency } from '../utils/formatters';

/**
 * Mesai ödemesi detay ekranı
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @param {Object} props.route - Route nesnesi
 * @param {Object} props.route.params - Route parametreleri
 * @param {number} props.route.params.paymentId - Ödeme ID'si
 * @returns {JSX.Element} Mesai ödemesi detay ekranı
 */
export default function WorkPaymentDetail({ navigation, route }) {
  const { paymentId } = route.params;
  const db = useSQLiteContext();
  const insets = useSafeAreaInsets();
  
  // Durum değişkenleri
  const [loading, setLoading] = useState(true);
  const [payment, setPayment] = useState(null);
  const [shifts, setShifts] = useState([]);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  
  // Form değerleri
  const [formData, setFormData] = useState({
    payment_date: new Date(),
    is_paid: false,
    notes: ''
  });
  
  // Verileri yükle
  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      
      // Ödeme bilgilerini getir
      const paymentData = await db.getFirstAsync(`
        SELECT * FROM work_payments
        WHERE id = ?
      `, [paymentId]);
      
      if (!paymentData) {
        Alert.alert('Hata', 'Ödeme bulunamadı.');
        navigation.goBack();
        return;
      }
      
      setPayment(paymentData);
      
      // Form verilerini ayarla
      setFormData({
        payment_date: paymentData.payment_date ? new Date(paymentData.payment_date) : new Date(),
        is_paid: paymentData.is_paid === 1,
        notes: paymentData.notes || ''
      });
      
      // İlgili vardiyaları getir
      const shiftsData = await workService.getWorkShiftsByDateRange(
        paymentData.period_start_date,
        paymentData.period_end_date
      );
      
      setShifts(shiftsData);
      
      setLoading(false);
    } catch (error) {
      console.error('Ödeme detayları yükleme hatası:', error);
      Alert.alert('Hata', 'Ödeme detayları yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  }, [db, paymentId]);
  
  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [loadData])
  );
  
  // Ödemeyi düzenle
  const editPayment = () => {
    setShowEditModal(true);
  };
  
  // Ödemeyi sil
  const deletePayment = async () => {
    Alert.alert(
      'Ödemeyi Sil',
      'Bu ödemeyi silmek istediğinize emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await workService.deleteWorkPayment(paymentId);
              navigation.goBack();
            } catch (error) {
              console.error('Ödeme silme hatası:', error);
              Alert.alert('Hata', 'Ödeme silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };
  
  // Form değişikliklerini işle
  const handleChange = (name, value) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  // Tarih değişikliğini işle
  const handleDateChange = (event, selectedDate) => {
    setShowDatePicker(Platform.OS === 'ios');
    if (selectedDate) {
      setFormData(prev => ({ ...prev, payment_date: selectedDate }));
    }
  };
  
  // Formu kaydet
  const handleSave = async () => {
    try {
      // Ödeme verilerini hazırla
      const updatedPayment = {
        ...payment,
        payment_date: formData.is_paid ? formData.payment_date.toISOString().split('T')[0] : null,
        is_paid: formData.is_paid ? 1 : 0,
        notes: formData.notes.trim()
      };
      
      await workService.updateWorkPayment(paymentId, updatedPayment);
      
      setShowEditModal(false);
      await loadData();
      
      Alert.alert('Başarılı', 'Ödeme başarıyla güncellendi.');
    } catch (error) {
      console.error('Ödeme güncelleme hatası:', error);
      Alert.alert('Hata', 'Ödeme güncellenirken bir hata oluştu.');
    }
  };
  
  // Tarih aralığını formatla
  const formatDateRange = (startDate, endDate) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    return `${start.toLocaleDateString('tr-TR')} - ${end.toLocaleDateString('tr-TR')}`;
  };
  
  // Vardiya durumunu formatla
  const formatShiftStatus = (status) => {
    switch (status) {
      case 'planned': return 'Planlandı';
      case 'in_progress': return 'Devam Ediyor';
      case 'completed': return 'Tamamlandı';
      case 'cancelled': return 'İptal Edildi';
      default: return status;
    }
  };
  
  // Vardiya durumuna göre renk
  const getShiftStatusColor = (status) => {
    switch (status) {
      case 'planned': return Colors.WARNING;
      case 'in_progress': return Colors.INFO;
      case 'completed': return Colors.SUCCESS;
      case 'cancelled': return Colors.DANGER;
      default: return Colors.GRAY_600;
    }
  };
  
  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent, { paddingTop: insets.top }]}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={styles.loadingText}>Ödeme detayları yükleniyor...</Text>
      </View>
    );
  }
  
  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Ödeme Detayı</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={editPayment}
          >
            <MaterialIcons name="edit" size={24} color={Colors.PRIMARY} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={deletePayment}
          >
            <MaterialIcons name="delete" size={24} color={Colors.DANGER} />
          </TouchableOpacity>
        </View>
      </View>
      
      <ScrollView style={styles.scrollView}>
        {/* Ödeme Özeti */}
        <View style={styles.summaryCard}>
          <View style={styles.periodContainer}>
            <MaterialIcons name="date-range" size={24} color={Colors.PRIMARY} />
            <Text style={styles.periodText}>
              {formatDateRange(payment.period_start_date, payment.period_end_date)}
            </Text>
          </View>
          
          <View style={[
            styles.paymentStatus,
            { backgroundColor: payment.is_paid === 1 ? Colors.SUCCESS : Colors.WARNING }
          ]}>
            <Text style={styles.paymentStatusText}>
              {payment.is_paid === 1 ? 'Ödendi' : 'Ödenmedi'}
            </Text>
          </View>
          
          {payment.payment_date && (
            <Text style={styles.paymentDate}>
              Ödeme Tarihi: {new Date(payment.payment_date).toLocaleDateString('tr-TR')}
            </Text>
          )}
        </View>
        
        {/* Ödeme Detayları */}
        <View style={styles.detailCard}>
          <Text style={styles.cardTitle}>Ödeme Detayları</Text>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Normal Mesai</Text>
            <Text style={styles.detailValue}>
              {payment.regular_hours.toFixed(2)} saat
            </Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Normal Mesai Ücreti</Text>
            <Text style={styles.detailValue}>
              {formatCurrency(payment.regular_amount, 'TRY')}
            </Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Fazla Mesai</Text>
            <Text style={styles.detailValue}>
              {payment.overtime_hours.toFixed(2)} saat
            </Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Fazla Mesai Ücreti</Text>
            <Text style={styles.detailValue}>
              {formatCurrency(payment.overtime_amount, 'TRY')}
            </Text>
          </View>
          
          <View style={[styles.detailRow, styles.totalRow]}>
            <Text style={styles.totalLabel}>Toplam</Text>
            <Text style={styles.totalValue}>
              {formatCurrency(payment.total_amount, 'TRY')}
            </Text>
          </View>
        </View>
        
        {/* Notlar */}
        {payment.notes && (
          <View style={styles.notesCard}>
            <Text style={styles.cardTitle}>Notlar</Text>
            <Text style={styles.notesText}>{payment.notes}</Text>
          </View>
        )}
        
        {/* İlgili Vardiyalar */}
        <View style={styles.shiftsCard}>
          <Text style={styles.cardTitle}>İlgili Vardiyalar</Text>
          
          {shifts.length === 0 ? (
            <Text style={styles.emptyText}>Bu dönem için vardiya bulunmuyor.</Text>
          ) : (
            shifts.map((shift) => (
              <View key={shift.id} style={styles.shiftItem}>
                <View style={styles.shiftHeader}>
                  <Text style={styles.shiftDate}>
                    {new Date(shift.date).toLocaleDateString('tr-TR')}
                  </Text>
                  <View style={[
                    styles.shiftStatus,
                    { backgroundColor: getShiftStatusColor(shift.status) }
                  ]}>
                    <Text style={styles.shiftStatusText}>
                      {formatShiftStatus(shift.status)}
                    </Text>
                  </View>
                </View>
                
                <View style={styles.shiftTimes}>
                  <Text style={styles.shiftTimeText}>
                    {shift.start_time} - {shift.end_time || 'Devam Ediyor'}
                  </Text>
                  
                  {shift.break_duration > 0 && (
                    <Text style={styles.shiftBreakText}>
                      {shift.break_duration} dk mola
                    </Text>
                  )}
                </View>
                
                {shift.is_overtime === 1 && (
                  <View style={styles.overtimeTag}>
                    <MaterialIcons name="access-alarm" size={14} color="#fff" />
                    <Text style={styles.overtimeTagText}>Fazla Mesai</Text>
                  </View>
                )}
              </View>
            ))
          )}
        </View>
      </ScrollView>
      
      {/* Düzenleme Modalı */}
      <Modal
        visible={showEditModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowEditModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Ödemeyi Düzenle</Text>
              <TouchableOpacity onPress={() => setShowEditModal(false)}>
                <MaterialIcons name="close" size={24} color="#999" />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.modalForm}>
              {/* Ödeme Durumu */}
              <View style={styles.formGroup}>
                <View style={styles.switchContainer}>
                  <Text style={styles.formLabel}>Ödendi</Text>
                  <Switch
                    value={formData.is_paid}
                    onValueChange={(value) => handleChange('is_paid', value)}
                    trackColor={{ false: '#d1d1d1', true: Colors.PRIMARY }}
                    thumbColor="#fff"
                  />
                </View>
              </View>
              
              {/* Ödeme Tarihi */}
              {formData.is_paid && (
                <View style={styles.formGroup}>
                  <Text style={styles.formLabel}>Ödeme Tarihi</Text>
                  <TouchableOpacity
                    style={styles.datePickerButton}
                    onPress={() => setShowDatePicker(true)}
                  >
                    <Text style={styles.datePickerButtonText}>
                      {formData.payment_date.toLocaleDateString('tr-TR')}
                    </Text>
                    <MaterialIcons name="calendar-today" size={20} color="#666" />
                  </TouchableOpacity>
                  {showDatePicker && (
                    <DateTimePicker
                      value={formData.payment_date}
                      mode="date"
                      display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                      onChange={handleDateChange}
                    />
                  )}
                </View>
              )}
              
              {/* Notlar */}
              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Notlar</Text>
                <TextInput
                  style={[styles.formInput, styles.textArea]}
                  value={formData.notes}
                  onChangeText={(value) => handleChange('notes', value)}
                  placeholder="Ödeme hakkında notlar"
                  multiline
                  numberOfLines={4}
                />
              </View>
              
              {/* Butonlar */}
              <View style={styles.formButtons}>
                <TouchableOpacity
                  style={[styles.formButton, styles.cancelButton]}
                  onPress={() => setShowEditModal(false)}
                >
                  <Text style={styles.cancelButtonText}>İptal</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.formButton, styles.saveButton]}
                  onPress={handleSave}
                >
                  <Text style={styles.saveButtonText}>Kaydet</Text>
                </TouchableOpacity>
              </View>
            </ScrollView>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  headerActions: {
    flexDirection: 'row',
  },
  headerButton: {
    padding: 4,
    marginLeft: 16,
  },
  scrollView: {
    flex: 1,
  },
  summaryCard: {
    backgroundColor: '#fff',
    padding: 16,
    margin: 16,
    marginBottom: 8,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  periodContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  periodText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 8,
  },
  paymentStatus: {
    alignSelf: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
    marginBottom: 8,
  },
  paymentStatusText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#fff',
  },
  paymentDate: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
  },
  detailCard: {
    backgroundColor: '#fff',
    padding: 16,
    margin: 16,
    marginTop: 8,
    marginBottom: 8,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  detailLabel: {
    fontSize: 16,
    color: '#666',
  },
  detailValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  totalRow: {
    marginTop: 8,
    borderBottomWidth: 0,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  totalValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.PRIMARY,
  },
  notesCard: {
    backgroundColor: '#fff',
    padding: 16,
    margin: 16,
    marginTop: 8,
    marginBottom: 8,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  notesText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
  },
  shiftsCard: {
    backgroundColor: '#fff',
    padding: 16,
    margin: 16,
    marginTop: 8,
    marginBottom: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    padding: 16,
  },
  shiftItem: {
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    paddingVertical: 12,
  },
  shiftHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  shiftDate: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  shiftStatus: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  shiftStatusText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#fff',
  },
  shiftTimes: {
    marginBottom: 8,
  },
  shiftTimeText: {
    fontSize: 14,
    color: '#666',
  },
  shiftBreakText: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
  },
  overtimeTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.WARNING,
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  overtimeTagText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#fff',
    marginLeft: 4,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 12,
    width: '90%',
    maxHeight: '80%',
    padding: 16,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  modalForm: {
    flex: 1,
  },
  formGroup: {
    marginBottom: 16,
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  formLabel: {
    fontSize: 16,
    marginBottom: 8,
    color: '#333',
  },
  formInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: '#333',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  datePickerButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  datePickerButtonText: {
    fontSize: 16,
    color: '#333',
  },
  formButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
  },
  formButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#f5f5f5',
    marginRight: 8,
  },
  saveButton: {
    backgroundColor: Colors.PRIMARY,
    marginLeft: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
  },
});
