import React from 'react';
import { Text, StyleSheet } from 'react-native';
import tokens from '../tokens';

/**
 * Modern Typography Component
 * Design system'e uygun, tutarlı metin bileşeni
 */
const Typography = ({
  children,
  variant = 'body',
  size = null,
  weight = null,
  color = null,
  align = 'left',
  numberOfLines = null,
  style,
  ...props
}) => {
  // Variant stilleri
  const getVariantStyles = () => {
    switch (variant) {
      case 'h1':
        return {
          fontSize: tokens.typography.fontSize['4xl'],
          fontWeight: tokens.typography.fontWeight.bold,
          lineHeight: tokens.typography.lineHeight.tight * tokens.typography.fontSize['4xl'],
          color: tokens.colors.gray[900],
          fontFamily: tokens.typography.fontFamily.bold,
        };
      case 'h2':
        return {
          fontSize: tokens.typography.fontSize['3xl'],
          fontWeight: tokens.typography.fontWeight.bold,
          lineHeight: tokens.typography.lineHeight.tight * tokens.typography.fontSize['3xl'],
          color: tokens.colors.gray[900],
          fontFamily: tokens.typography.fontFamily.bold,
        };
      case 'h3':
        return {
          fontSize: tokens.typography.fontSize['2xl'],
          fontWeight: tokens.typography.fontWeight.semibold,
          lineHeight: tokens.typography.lineHeight.snug * tokens.typography.fontSize['2xl'],
          color: tokens.colors.gray[900],
          fontFamily: tokens.typography.fontFamily.bold,
        };
      case 'h4':
        return {
          fontSize: tokens.typography.fontSize.xl,
          fontWeight: tokens.typography.fontWeight.semibold,
          lineHeight: tokens.typography.lineHeight.snug * tokens.typography.fontSize.xl,
          color: tokens.colors.gray[900],
          fontFamily: tokens.typography.fontFamily.medium,
        };
      case 'h5':
        return {
          fontSize: tokens.typography.fontSize.lg,
          fontWeight: tokens.typography.fontWeight.medium,
          lineHeight: tokens.typography.lineHeight.normal * tokens.typography.fontSize.lg,
          color: tokens.colors.gray[900],
          fontFamily: tokens.typography.fontFamily.medium,
        };
      case 'h6':
        return {
          fontSize: tokens.typography.fontSize.base,
          fontWeight: tokens.typography.fontWeight.medium,
          lineHeight: tokens.typography.lineHeight.normal * tokens.typography.fontSize.base,
          color: tokens.colors.gray[900],
          fontFamily: tokens.typography.fontFamily.medium,
        };
      case 'subtitle1':
        return {
          fontSize: tokens.typography.fontSize.lg,
          fontWeight: tokens.typography.fontWeight.normal,
          lineHeight: tokens.typography.lineHeight.relaxed * tokens.typography.fontSize.lg,
          color: tokens.colors.gray[700],
          fontFamily: tokens.typography.fontFamily.regular,
        };
      case 'subtitle2':
        return {
          fontSize: tokens.typography.fontSize.base,
          fontWeight: tokens.typography.fontWeight.medium,
          lineHeight: tokens.typography.lineHeight.normal * tokens.typography.fontSize.base,
          color: tokens.colors.gray[600],
          fontFamily: tokens.typography.fontFamily.medium,
        };
      case 'body':
        return {
          fontSize: tokens.typography.fontSize.base,
          fontWeight: tokens.typography.fontWeight.normal,
          lineHeight: tokens.typography.lineHeight.relaxed * tokens.typography.fontSize.base,
          color: tokens.colors.gray[700],
          fontFamily: tokens.typography.fontFamily.regular,
        };
      case 'body2':
        return {
          fontSize: tokens.typography.fontSize.sm,
          fontWeight: tokens.typography.fontWeight.normal,
          lineHeight: tokens.typography.lineHeight.normal * tokens.typography.fontSize.sm,
          color: tokens.colors.gray[600],
          fontFamily: tokens.typography.fontFamily.regular,
        };
      case 'caption':
        return {
          fontSize: tokens.typography.fontSize.xs,
          fontWeight: tokens.typography.fontWeight.normal,
          lineHeight: tokens.typography.lineHeight.normal * tokens.typography.fontSize.xs,
          color: tokens.colors.gray[500],
          fontFamily: tokens.typography.fontFamily.regular,
        };
      case 'overline':
        return {
          fontSize: tokens.typography.fontSize.xs,
          fontWeight: tokens.typography.fontWeight.medium,
          lineHeight: tokens.typography.lineHeight.normal * tokens.typography.fontSize.xs,
          color: tokens.colors.gray[500],
          letterSpacing: tokens.typography.letterSpacing.wide,
          textTransform: 'uppercase',
          fontFamily: tokens.typography.fontFamily.medium,
        };
      case 'button':
        return {
          fontSize: tokens.typography.fontSize.sm,
          fontWeight: tokens.typography.fontWeight.medium,
          lineHeight: tokens.typography.lineHeight.normal * tokens.typography.fontSize.sm,
          color: tokens.colors.gray[900],
          letterSpacing: tokens.typography.letterSpacing.wide,
          fontFamily: tokens.typography.fontFamily.medium,
        };
      default:
        return {
          fontSize: tokens.typography.fontSize.base,
          fontWeight: tokens.typography.fontWeight.normal,
          lineHeight: tokens.typography.lineHeight.normal * tokens.typography.fontSize.base,
          color: tokens.colors.gray[700],
          fontFamily: tokens.typography.fontFamily.regular,
        };
    }
  };

  // Size override
  const getSizeOverride = () => {
    if (!size) return {};
    
    const fontSize = tokens.typography.fontSize[size] || size;
    return {
      fontSize,
      lineHeight: tokens.typography.lineHeight.normal * fontSize,
    };
  };

  // Weight override
  const getWeightOverride = () => {
    if (!weight) return {};
    
    const fontWeight = tokens.typography.fontWeight[weight] || weight;
    const fontFamily = weight === 'bold' || weight === 'extrabold' 
      ? tokens.typography.fontFamily.bold
      : weight === 'medium' || weight === 'semibold'
      ? tokens.typography.fontFamily.medium
      : tokens.typography.fontFamily.regular;
    
    return {
      fontWeight,
      fontFamily,
    };
  };

  // Color override
  const getColorOverride = () => {
    if (!color) return {};
    
    // Token color mu kontrol et
    if (color.includes('.')) {
      const [colorName, shade] = color.split('.');
      return {
        color: tokens.colors[colorName]?.[shade] || color,
      };
    }
    
    return {
      color: tokens.colors[color] || color,
    };
  };

  // Text align
  const getAlignStyles = () => {
    return {
      textAlign: align,
    };
  };

  const variantStyles = getVariantStyles();
  const sizeOverride = getSizeOverride();
  const weightOverride = getWeightOverride();
  const colorOverride = getColorOverride();
  const alignStyles = getAlignStyles();

  const textStyles = [
    styles.text,
    variantStyles,
    sizeOverride,
    weightOverride,
    colorOverride,
    alignStyles,
    style,
  ];

  return (
    <Text
      style={textStyles}
      numberOfLines={numberOfLines}
      {...props}
    >
      {children}
    </Text>
  );
};

// Convenience components
export const Heading = (props) => <Typography variant="h3" {...props} />;
export const Title = (props) => <Typography variant="h4" {...props} />;
export const Subtitle = (props) => <Typography variant="subtitle1" {...props} />;
export const Body = (props) => <Typography variant="body" {...props} />;
export const Caption = (props) => <Typography variant="caption" {...props} />;
export const Overline = (props) => <Typography variant="overline" {...props} />;

const styles = StyleSheet.create({
  text: {
    includeFontPadding: false, // Android font padding fix
    textAlignVertical: 'center', // Android alignment fix
  },
});

// Typography presets for quick access
export const TypographyPresets = {
  // Headings
  largeTitle: { variant: 'h1', weight: 'bold' },
  title1: { variant: 'h2', weight: 'bold' },
  title2: { variant: 'h3', weight: 'semibold' },
  title3: { variant: 'h4', weight: 'semibold' },

  // Body text
  headline: { variant: 'subtitle1', weight: 'semibold' },
  body: { variant: 'body', weight: 'normal' },
  callout: { variant: 'body', weight: 'medium' },
  subhead: { variant: 'body2', weight: 'normal' },
  footnote: { variant: 'caption', weight: 'normal' },

  // Special
  caption1: { variant: 'caption', weight: 'normal' },
  caption2: { variant: 'caption', weight: 'medium', size: 'xs' },
};

// Quick typography components
export const LargeTitle = (props) => <Typography {...TypographyPresets.largeTitle} {...props} />;
export const Title1 = (props) => <Typography {...TypographyPresets.title1} {...props} />;
export const Title2 = (props) => <Typography {...TypographyPresets.title2} {...props} />;
export const Title3 = (props) => <Typography {...TypographyPresets.title3} {...props} />;
export const Headline = (props) => <Typography {...TypographyPresets.headline} {...props} />;
export const Callout = (props) => <Typography {...TypographyPresets.callout} {...props} />;
export const Subhead = (props) => <Typography {...TypographyPresets.subhead} {...props} />;
export const Footnote = (props) => <Typography {...TypographyPresets.footnote} {...props} />;
export const Caption1 = (props) => <Typography {...TypographyPresets.caption1} {...props} />;
export const Caption2 = (props) => <Typography {...TypographyPresets.caption2} {...props} />;

export default Typography;
