/**
 * Budget System Migration
 * Creates complete budget management database schema
 * Following BUDGET_MANAGEMENT_REDESIGN_PLAN.md specifications
 */

/**
 * Creates all budget-related tables
 * @param {SQLiteDatabase} db - Database instance
 */
export const createBudgetTables = async (db) => {
  try {
    console.log('🔧 Creating budget management tables...');
    
    await db.execAsync(`
      BEGIN TRANSACTION;
      
      -- Main budgets table (enhanced version)
      CREATE TABLE IF NOT EXISTS budgets (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        type TEXT NOT NULL DEFAULT 'category_based' CHECK(type IN ('total', 'category_based', 'flexible')),
        period_type TEXT NOT NULL DEFAULT 'monthly' CHECK(period_type IN ('weekly', 'monthly', 'custom')),
        start_date DATE NOT NULL,
        end_date DATE,
        total_limit DECIMAL(10,2) NOT NULL DEFAULT 0,
        currency TEXT NOT NULL DEFAULT 'TRY' CHECK(currency IN ('TRY', 'USD', 'EUR')),
        status TEXT NOT NULL DEFAULT 'active' CHECK(status IN ('active', 'paused', 'completed', 'cancelled')),
        auto_renew INTEGER DEFAULT 0,
        template_id INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
      
      -- Budget category limits table
      CREATE TABLE IF NOT EXISTS budget_categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        budget_id INTEGER NOT NULL,
        category_id INTEGER NOT NULL,
        limit_amount DECIMAL(10,2) NOT NULL,
        spent_amount DECIMAL(10,2) DEFAULT 0,
        currency TEXT NOT NULL DEFAULT 'TRY',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (budget_id) REFERENCES budgets (id) ON DELETE CASCADE,
        FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE CASCADE,
        UNIQUE(budget_id, category_id)
      );
      
      -- Budget alert/notification settings
      CREATE TABLE IF NOT EXISTS budget_alert_settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        budget_id INTEGER NOT NULL,
        threshold_75 INTEGER DEFAULT 1,
        threshold_90 INTEGER DEFAULT 1,
        threshold_100 INTEGER DEFAULT 1,
        daily_limit_exceeded INTEGER DEFAULT 1,
        category_limit_exceeded INTEGER DEFAULT 1,
        weekly_summary INTEGER DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (budget_id) REFERENCES budgets (id) ON DELETE CASCADE
      );
      
      -- Budget performance history
      CREATE TABLE IF NOT EXISTS budget_history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        budget_id INTEGER NOT NULL,
        period_start DATE NOT NULL,
        period_end DATE NOT NULL,
        total_spent DECIMAL(10,2) DEFAULT 0,
        total_limit DECIMAL(10,2) NOT NULL,
        success_rate DECIMAL(5,2) DEFAULT 0,
        categories_over_limit INTEGER DEFAULT 0,
        performance_score DECIMAL(5,2) DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (budget_id) REFERENCES budgets (id) ON DELETE CASCADE
      );
      
      -- Budget templates for quick setup
      CREATE TABLE IF NOT EXISTS budget_templates (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        type TEXT NOT NULL DEFAULT 'category_based',
        template_data TEXT NOT NULL, -- JSON string with category limits
        user_created INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
      
      -- Budget notifications log
      CREATE TABLE IF NOT EXISTS budget_notifications (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        budget_id INTEGER NOT NULL,
        category_id INTEGER,
        notification_type TEXT NOT NULL,
        message TEXT NOT NULL,
        threshold_value DECIMAL(5,2),
        amount DECIMAL(10,2),
        sent_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        read_at DATETIME,
        FOREIGN KEY (budget_id) REFERENCES budgets (id) ON DELETE CASCADE,
        FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE SET NULL
      );
      
      COMMIT;
    `);
    
    console.log('✅ Budget tables created successfully');
    
    // Create indexes for performance
    await createBudgetIndexes(db);
    
    // Insert default budget templates
    await insertDefaultBudgetTemplates(db);
    
    console.log('✅ Budget system migration completed');
    
  } catch (error) {
    console.error('❌ Budget tables creation failed:', error);
    await db.execAsync('ROLLBACK');
    throw error;
  }
};

/**
 * Creates performance indexes for budget tables
 * @param {SQLiteDatabase} db - Database instance
 */
const createBudgetIndexes = async (db) => {
  try {
    await db.execAsync(`
      -- Performance indexes
      CREATE INDEX IF NOT EXISTS idx_budgets_status_period ON budgets(status, period_type, start_date, end_date);
      CREATE INDEX IF NOT EXISTS idx_budget_categories_budget_id ON budget_categories(budget_id);
      CREATE INDEX IF NOT EXISTS idx_budget_categories_category_id ON budget_categories(category_id);
      CREATE INDEX IF NOT EXISTS idx_budget_history_budget_period ON budget_history(budget_id, period_start, period_end);
      CREATE INDEX IF NOT EXISTS idx_budget_notifications_budget_sent ON budget_notifications(budget_id, sent_at);
      CREATE INDEX IF NOT EXISTS idx_budget_notifications_unread ON budget_notifications(read_at) WHERE read_at IS NULL;
    `);
    
    console.log('✅ Budget indexes created');
  } catch (error) {
    console.error('❌ Budget indexes creation failed:', error);
    throw error;
  }
};

/**
 * Inserts default budget templates
 * @param {SQLiteDatabase} db - Database instance
 */
const insertDefaultBudgetTemplates = async (db) => {
  try {
    // Check if templates already exist
    const existingTemplates = await db.getFirstAsync('SELECT COUNT(*) as count FROM budget_templates');
    if (existingTemplates.count > 0) {
      console.log('📋 Budget templates already exist, skipping...');
      return;
    }
    
    const templates = [
      {
        name: 'Temel İhtiyaçlar Bütçesi',
        description: 'Günlük yaşam giderleri için temel bütçe planı',
        type: 'category_based',
        template_data: JSON.stringify({
          categories: [
            { name: 'Gıda', percentage: 35, color: '#4CAF50' },
            { name: 'Ulaşım', percentage: 15, color: '#2196F3' },
            { name: 'Kira/Barınma', percentage: 30, color: '#FF9800' },
            { name: 'Faturalar', percentage: 10, color: '#9C27B0' },
            { name: 'Diğer', percentage: 10, color: '#F44336' }
          ],
          period_type: 'monthly'
        })
      },
      {
        name: 'Öğrenci Bütçesi',
        description: 'Öğrenciler için optimize edilmiş bütçe planı',
        type: 'category_based',
        template_data: JSON.stringify({
          categories: [
            { name: 'Gıda', percentage: 40, color: '#4CAF50' },
            { name: 'Ulaşım', percentage: 20, color: '#2196F3' },
            { name: 'Eğitim', percentage: 15, color: '#FF9800' },
            { name: 'Sosyal', percentage: 15, color: '#9C27B0' },
            { name: 'Diğer', percentage: 10, color: '#F44336' }
          ],
          period_type: 'monthly'
        })
      },
      {
        name: 'Aile Bütçesi',
        description: 'Aile için kapsamlı bütçe planı',
        type: 'category_based',
        template_data: JSON.stringify({
          categories: [
            { name: 'Gıda', percentage: 25, color: '#4CAF50' },
            { name: 'Barınma', percentage: 35, color: '#2196F3' },
            { name: 'Ulaşım', percentage: 10, color: '#FF9800' },
            { name: 'Çocuk', percentage: 15, color: '#9C27B0' },
            { name: 'Sağlık', percentage: 5, color: '#F44336' },
            { name: 'Tasarruf', percentage: 10, color: '#00BCD4' }
          ],
          period_type: 'monthly'
        })
      }
    ];
    
    for (const template of templates) {
      await db.runAsync(`
        INSERT INTO budget_templates (name, description, type, template_data, user_created)
        VALUES (?, ?, ?, ?, 0)
      `, [template.name, template.description, template.type, template.template_data]);
    }
    
    console.log('✅ Default budget templates inserted');
  } catch (error) {
    console.error('❌ Budget templates insertion failed:', error);
    throw error;
  }
};

/**
 * Drops budget tables (for development/testing)
 * @param {SQLiteDatabase} db - Database instance
 */
export const dropBudgetTables = async (db) => {
  try {
    await db.execAsync(`
      DROP TABLE IF EXISTS budget_notifications;
      DROP TABLE IF EXISTS budget_templates;
      DROP TABLE IF EXISTS budget_history;
      DROP TABLE IF EXISTS budget_alert_settings;
      DROP TABLE IF EXISTS budget_categories;
      DROP TABLE IF EXISTS budgets;
    `);
    console.log('✅ Budget tables dropped');
  } catch (error) {
    console.error('❌ Budget tables drop failed:', error);
    throw error;
  }
};

/**
 * Updates budget spent amounts from transactions
 * @param {SQLiteDatabase} db - Database instance
 */
export const updateBudgetSpentAmounts = async (db) => {
  try {
    console.log('📊 Updating budget spent amounts...');
    
    await db.execAsync(`
      UPDATE budget_categories 
      SET spent_amount = (
        SELECT COALESCE(SUM(ABS(t.amount)), 0)
        FROM transactions t
        INNER JOIN budgets b ON budget_categories.budget_id = b.id
        WHERE t.category_id = budget_categories.category_id
          AND t.type = 'expense'
          AND DATE(t.date) >= DATE(b.start_date)
          AND (b.end_date IS NULL OR DATE(t.date) <= DATE(b.end_date))
          AND t.currency = budget_categories.currency
      ),
      updated_at = CURRENT_TIMESTAMP
      WHERE EXISTS (
        SELECT 1 FROM budgets WHERE id = budget_categories.budget_id AND status = 'active'
      );
    `);
    
    console.log('✅ Budget spent amounts updated');
  } catch (error) {
    console.error('❌ Budget spent amounts update failed:', error);
    throw error;
  }
};
