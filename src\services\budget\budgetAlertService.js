/**
 * Bütçe Uyarı Servisi
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md Stage 1 implementasyonu
 * 
 * Bu servis bütçe uyarıları ve bildirimler için gerekli fonksiyonları sağlar
 */

/**
 * Bütçe uyarı ayarlarını getirir
 * @param {Object} db - SQLite database instance
 * @param {number} budgetId - Bütçe ID'si
 * @returns {Promise<Object>} Uyarı ayarları
 */
export const getBudgetAlertSettings = async (db, budgetId) => {
  try {
    const settings = await db.getFirstAsync(`
      SELECT * FROM budget_alert_settings WHERE budget_id = ?
    `, [budgetId]);

    return settings || {
      budget_id: budgetId,
      threshold_75: 1,
      threshold_90: 1,
      threshold_100: 1,
      daily_limit_exceeded: 1,
      category_limit_exceeded: 1
    };

  } catch (error) {
    console.error('❌ Bütçe uyarı ayarları getirme hatası:', error);
    throw error;
  }
};

/**
 * Bütçe uyarı ayarlarını günceller
 * @param {Object} db - SQLite database instance
 * @param {number} budgetId - Bütçe ID'si
 * @param {Object} settings - Uyarı ayarları
 * @returns {Promise<boolean>} Başarı durumu
 */
export const updateBudgetAlertSettings = async (db, budgetId, settings) => {
  try {
    const result = await db.runAsync(`
      INSERT OR REPLACE INTO budget_alert_settings 
      (budget_id, threshold_75, threshold_90, threshold_100, daily_limit_exceeded, category_limit_exceeded, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
    `, [
      budgetId,
      settings.threshold_75 ? 1 : 0,
      settings.threshold_90 ? 1 : 0,
      settings.threshold_100 ? 1 : 0,
      settings.daily_limit_exceeded ? 1 : 0,
      settings.category_limit_exceeded ? 1 : 0
    ]);

    console.log(`✅ Bütçe uyarı ayarları güncellendi (Budget ID: ${budgetId})`);
    return result.changes > 0;

  } catch (error) {
    console.error('❌ Bütçe uyarı ayarları güncelleme hatası:', error);
    throw error;
  }
};

/**
 * Aktif bütçeler için uyarıları kontrol eder
 * @param {Object} db - SQLite database instance
 * @returns {Promise<Array>} Uyarı listesi
 */
export const checkBudgetAlerts = async (db) => {
  try {
    const alerts = [];
    
    // Aktif bütçeleri getir
    const activeBudgets = await db.getAllAsync(`
      SELECT * FROM budgets_enhanced 
      WHERE status = 'active'
        AND start_date <= date('now')
        AND (end_date IS NULL OR end_date >= date('now'))
    `);

    for (const budget of activeBudgets) {
      const budgetAlerts = await checkSingleBudgetAlerts(db, budget.id);
      alerts.push(...budgetAlerts);
    }

    return alerts;

  } catch (error) {
    console.error('❌ Bütçe uyarıları kontrol hatası:', error);
    throw error;
  }
};

/**
 * Tek bir bütçe için uyarıları kontrol eder
 * @param {Object} db - SQLite database instance
 * @param {number} budgetId - Bütçe ID'si
 * @returns {Promise<Array>} Uyarı listesi
 */
export const checkSingleBudgetAlerts = async (db, budgetId) => {
  try {
    const alerts = [];

    // Bütçe ve uyarı ayarlarını getir
    const budget = await db.getFirstAsync(`
      SELECT b.*, bas.*
      FROM budgets_enhanced b
      LEFT JOIN budget_alert_settings bas ON b.id = bas.budget_id
      WHERE b.id = ?
    `, [budgetId]);

    if (!budget) return alerts;

    // Kategori bazlı harcamaları hesapla
    const categories = await db.getAllAsync(`
      SELECT 
        bc.*,
        c.name as category_name
      FROM budget_categories_enhanced bc
      JOIN categories c ON bc.category_id = c.id
      WHERE bc.budget_id = ?
    `, [budgetId]);

    let totalAllocated = 0;
    let totalSpent = 0;

    for (const category of categories) {
      const spent = await calculateCategorySpending(db, budgetId, category.category_id);
      totalAllocated += category.limit_amount;
      totalSpent += spent;

      // Kategori limiti aşıldı mı kontrol et
      if (budget.category_limit_exceeded && spent > category.limit_amount) {
        alerts.push({
          type: 'category_exceeded',
          severity: 'high',
          budget_id: budgetId,
          budget_name: budget.name,
          category_id: category.category_id,
          category_name: category.category_name,
          allocated: category.limit_amount,
          spent: spent,
          percentage: (spent / category.limit_amount) * 100,
          message: `${category.category_name} kategorisi limitini aştı`,
          created_at: new Date().toISOString()
        });
      }
    }

    // Genel bütçe uyarıları
    if (totalAllocated > 0) {
      const overallPercentage = (totalSpent / totalAllocated) * 100;

      // %100 uyarısı
      if (budget.threshold_100 && overallPercentage >= 100) {
        alerts.push({
          type: 'budget_exceeded',
          severity: 'high',
          budget_id: budgetId,
          budget_name: budget.name,
          allocated: totalAllocated,
          spent: totalSpent,
          percentage: overallPercentage,
          message: 'Bütçe limitini aştınız!',
          created_at: new Date().toISOString()
        });
      }
      // %90 uyarısı
      else if (budget.threshold_90 && overallPercentage >= 90) {
        alerts.push({
          type: 'budget_warning_90',
          severity: 'medium',
          budget_id: budgetId,
          budget_name: budget.name,
          allocated: totalAllocated,
          spent: totalSpent,
          percentage: overallPercentage,
          message: 'Bütçenizin %90\'ını kullandınız',
          created_at: new Date().toISOString()
        });
      }
      // %75 uyarısı
      else if (budget.threshold_75 && overallPercentage >= 75) {
        alerts.push({
          type: 'budget_warning_75',
          severity: 'low',
          budget_id: budgetId,
          budget_name: budget.name,
          allocated: totalAllocated,
          spent: totalSpent,
          percentage: overallPercentage,
          message: 'Bütçenizin %75\'ini kullandınız',
          created_at: new Date().toISOString()
        });
      }
    }

    return alerts;

  } catch (error) {
    console.error('❌ Tek bütçe uyarı kontrol hatası:', error);
    return [];
  }
};

/**
 * Kategori harcama miktarını hesaplar
 * @param {Object} db - SQLite database instance
 * @param {number} budgetId - Bütçe ID'si
 * @param {string} categoryId - Kategori ID'si
 * @returns {Promise<number>} Harcama miktarı
 */
const calculateCategorySpending = async (db, budgetId, categoryId) => {
  try {
    const budget = await db.getFirstAsync(`
      SELECT start_date, end_date FROM budgets_enhanced WHERE id = ?
    `, [budgetId]);

    if (!budget) return 0;

    const result = await db.getFirstAsync(`
      SELECT SUM(amount) as total
      FROM transactions
      WHERE category_id = ? 
        AND type = 'expense'
        AND date >= ?
        AND (? IS NULL OR date <= ?)
    `, [
      categoryId,
      budget.start_date,
      budget.end_date,
      budget.end_date
    ]);

    return result?.total || 0;
  } catch (error) {
    console.error('❌ Kategori harcama hesaplama hatası:', error);
    return 0;
  }
};

/**
 * Günlük limit kontrolü yapar
 * @param {Object} db - SQLite database instance
 * @param {number} budgetId - Bütçe ID'si
 * @returns {Promise<Array>} Günlük limit uyarıları
 */
export const checkDailyLimitAlerts = async (db, budgetId) => {
  try {
    const alerts = [];
    const today = new Date().toISOString().split('T')[0];

    // Bütçe ve uyarı ayarlarını getir
    const budget = await db.getFirstAsync(`
      SELECT b.*, bas.*
      FROM budgets_enhanced b
      LEFT JOIN budget_alert_settings bas ON b.id = bas.budget_id
      WHERE b.id = ? AND bas.daily_limit_exceeded = 1
    `, [budgetId]);

    if (!budget) return alerts;

    // Günlük harcama limitini hesapla
    const daysInPeriod = calculateDaysInPeriod(budget.start_date, budget.end_date);
    const totalAllocated = await getTotalAllocatedAmount(db, budgetId);
    const dailyLimit = daysInPeriod > 0 ? totalAllocated / daysInPeriod : 0;

    // Bugünkü harcamaları getir
    const todaySpending = await db.getFirstAsync(`
      SELECT SUM(t.amount) as total
      FROM transactions t
      JOIN budget_categories_enhanced bc ON t.category_id = bc.category_id
      WHERE bc.budget_id = ? 
        AND t.type = 'expense'
        AND t.date = ?
    `, [budgetId, today]);

    const todayTotal = todaySpending?.total || 0;

    if (todayTotal > dailyLimit) {
      alerts.push({
        type: 'daily_limit_exceeded',
        severity: 'medium',
        budget_id: budgetId,
        budget_name: budget.name,
        daily_limit: dailyLimit,
        daily_spent: todayTotal,
        date: today,
        message: 'Günlük harcama limitini aştınız',
        created_at: new Date().toISOString()
      });
    }

    return alerts;

  } catch (error) {
    console.error('❌ Günlük limit kontrol hatası:', error);
    return [];
  }
};

/**
 * Dönem içindeki toplam gün sayısını hesaplar
 * @param {string} startDate - Başlangıç tarihi
 * @param {string} endDate - Bitiş tarihi
 * @returns {number} Gün sayısı
 */
const calculateDaysInPeriod = (startDate, endDate) => {
  if (!endDate) {
    const start = new Date(startDate);
    const today = new Date();
    return Math.ceil((today - start) / (1000 * 60 * 60 * 24)) + 1;
  }
  
  const start = new Date(startDate);
  const end = new Date(endDate);
  return Math.ceil((end - start) / (1000 * 60 * 60 * 24)) + 1;
};

/**
 * Bütçenin toplam ayrılan miktarını getirir
 * @param {Object} db - SQLite database instance
 * @param {number} budgetId - Bütçe ID'si
 * @returns {Promise<number>} Toplam ayrılan miktar
 */
const getTotalAllocatedAmount = async (db, budgetId) => {
  try {
    const result = await db.getFirstAsync(`
      SELECT SUM(limit_amount) as total
      FROM budget_categories_enhanced
      WHERE budget_id = ?
    `, [budgetId]);

    return result?.total || 0;
  } catch (error) {
    console.error('❌ Toplam ayrılan miktar hesaplama hatası:', error);
    return 0;
  }
};
