import { validateAmount, sanitizeText, validateDate, validateCategory } from '../utils/validators';
import * as Crypto from 'expo-crypto';

const RATE_LIMIT_WINDOW = 60000; // 1 dakika
const MAX_REQUESTS = 100; // 1 dakikada maksimum istek sayısı
const ENCRYPTION_KEY = 'your-secret-key'; // Güvenli bir şekilde saklanmalı

export const SecurityService = {
  requestCounts: new Map(),
  
  /**
   * Rate limiting kontrolü
   */
  checkRateLimit: (userId) => {
    const now = Date.now();
    const userRequests = SecurityService.requestCounts.get(userId) || [];
    
    // Zaman aşımına uğramış istekleri temizle
    const validRequests = userRequests.filter(time => now - time < RATE_LIMIT_WINDOW);
    
    if (validRequests.length >= MAX_REQUESTS) {
      throw new Error('Çok fazla istek gönderildi. Lütfen biraz bekleyin.');
    }
    
    validRequests.push(now);
    SecurityService.requestCounts.set(userId, validRequests);
  },

  /**
   * Expense verilerini validate et
   */
  validateExpense: ({ amount, category, date, description }) => {
    const errors = [];

    if (!validateAmount(amount)) {
      errors.push('Geçersiz tutar');
    }

    if (!validateCategory(category)) {
      errors.push('Geçersiz kategori');
    }

    if (!validateDate(date)) {
      errors.push('Geçersiz tarih');
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedData: {
        amount,
        category: sanitizeText(category),
        date,
        description: sanitizeText(description)
      }
    };
  },

  /**
   * SQL injection önleme
   */
  sanitizeSqlParam: (param) => {
    if (typeof param === 'string') {
      return param.replace(/['";]/g, '');
    }
    return param;
  },

  /**
   * Veri şifreleme
   */
  encrypt: async (data) => {
    const jsonData = JSON.stringify(data);
    const encoder = new TextEncoder();
    const dataBuffer = encoder.encode(jsonData);
    
    const key = await Crypto.digestStringAsync(
      Crypto.CryptoDigestAlgorithm.SHA256,
      ENCRYPTION_KEY
    );

    const encrypted = await Crypto.encryptAsync(
      dataBuffer,
      key,
      { algorithm: 'AES' }
    );

    return encrypted;
  },

  /**
   * Veri şifre çözme
   */
  decrypt: async (encryptedData) => {
    const key = await Crypto.digestStringAsync(
      Crypto.CryptoDigestAlgorithm.SHA256,
      ENCRYPTION_KEY
    );

    const decrypted = await Crypto.decryptAsync(
      encryptedData,
      key,
      { algorithm: 'AES' }
    );

    const decoder = new TextDecoder();
    return JSON.parse(decoder.decode(decrypted));
  },

  /**
   * Audit log oluşturma
   */
  createAuditLog: async ({ action, userId, details }) => {
    const timestamp = new Date().toISOString();
    const logEntry = {
      action,
      userId,
      timestamp,
      details: await SecurityService.encrypt(details)
    };

    return logEntry;
  }
};
