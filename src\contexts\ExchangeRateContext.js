import React, { createContext, useState, useContext, useEffect } from 'react';
import { useExchangeRateService } from '../db/dbService';

// Döviz kuru context'i
const ExchangeRateContext = createContext();

/**
 * Döviz kuru sağlayıcısı
 * @param {Object} props - Bileşen props'ları
 * @returns {JSX.Element} Context Provider
 */
export const ExchangeRateProvider = ({ children }) => {
  const [rates, setRates] = useState({ USD: 0, EUR: 0 });
  const [lastUpdated, setLastUpdated] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  const exchangeRateService = useExchangeRateService();

  // Döviz kurlarını API'den çek
  const fetchRatesFromAPI = async () => {
    try {
      const response = await fetch('https://api.exchangerate-api.com/v4/latest/TRY');
      const data = await response.json();

      if (data && data.rates) {
        // Desteklenen para birimleri
        const supportedCurrencies = [
          'USD', 'EUR', 'GBP', 'JPY', 'CHF', 'CAD', 'AUD', 'CNY',
          'RUB', 'KRW', 'INR', 'BRL', 'ZAR', 'AED', 'SAR'
        ];

        // API'den gelen kurları TRY bazlı olarak dönüştür
        const newRates = {};

        supportedCurrencies.forEach(currency => {
          if (data.rates[currency]) {
            // 1 TRY = kaç birim yabancı para
            newRates[currency] = 1 / data.rates[currency];
          }
        });

        // Kurları veritabanına kaydet
        await exchangeRateService.addRates(newRates);

        // Altın ve kripto para kurlarını da ekle (örnek değerler)
        // Gerçek uygulamada ayrı bir API kullanılmalı
        newRates.XAU = 0.00042; // 1 TRY = 0.00042 gram altın
        newRates.BTC = 0.0000000333; // 1 TRY = 0.0000000333 BTC
        newRates.ETH = 0.0000005; // 1 TRY = 0.0000005 ETH

        // State'i güncelle
        setRates(newRates);
        setLastUpdated(new Date());
        setError(null);
      } else {
        throw new Error('API yanıtı geçersiz');
      }
    } catch (err) {
      console.error('Döviz kuru çekme hatası:', err);
      setError('Döviz kurları güncellenemedi');

      // Hata durumunda veritabanındaki en son kurları kullan
      const dbRates = await exchangeRateService.getLatestRates();
      if (dbRates && Object.keys(dbRates).length > 0) {
        setRates(dbRates);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Veritabanından kurları yükle
  const loadRatesFromDB = async () => {
    try {
      const dbRates = await exchangeRateService.getLatestRates();

      if (dbRates && Object.keys(dbRates).length > 0) {
        setRates(dbRates);
        setError(null);
      } else {
        // Veritabanında kur yoksa API'den çek
        await fetchRatesFromAPI();
      }
    } catch (err) {
      console.error('Veritabanından döviz kuru yükleme hatası:', err);
      setError('Döviz kurları yüklenemedi');
      // API'den çekmeyi dene
      await fetchRatesFromAPI();
    } finally {
      setIsLoading(false);
    }
  };

  // Kurları güncelle
  const updateRates = async () => {
    setIsLoading(true);
    await fetchRatesFromAPI();
  };

  // Belirli bir miktarı dövize çevir
  const convertCurrency = (amount, targetCurrency, date = null) => {
    if (!amount || !targetCurrency || !rates[targetCurrency]) {
      return 0;
    }

    // Eğer tarih belirtilmişse, o tarihteki kuru kullanmaya çalış
    // Not: Bu örnekte tarih parametresi kullanılmıyor, gerçek uygulamada
    // geçmiş tarihler için ayrı bir API çağrısı yapılmalı
    return amount * rates[targetCurrency];
  };

  // Para birimini formatla
  const formatCurrency = (amount, currency = 'TRY') => {
    if (amount === null || amount === undefined) {
      return '';
    }

    const currencySymbols = {
      TRY: '₺',
      USD: '$',
      EUR: '€',
      GBP: '£',
      JPY: '¥',
      CHF: 'CHF',
      CAD: 'C$',
      AUD: 'A$',
      CNY: '¥',
      RUB: '₽',
      KRW: '₩',
      INR: '₹',
      BRL: 'R$',
      ZAR: 'R',
      SAR: '﷼',
      AED: 'د.إ',
      XAU: 'XAU',
      BTC: '₿',
      ETH: 'Ξ',
    };

    const symbol = currencySymbols[currency] || currency;

    // Sayıyı formatla
    const formattedAmount = new Intl.NumberFormat('tr-TR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);

    return `${symbol}${formattedAmount}`;
  };

  // Desteklenen para birimlerini getir
  const getSupportedCurrencies = () => {
    return [
      { code: 'TRY', name: 'Türk Lirası', symbol: '₺' },
      { code: 'USD', name: 'Amerikan Doları', symbol: '$' },
      { code: 'EUR', name: 'Euro', symbol: '€' },
      { code: 'GBP', name: 'İngiliz Sterlini', symbol: '£' },
      { code: 'JPY', name: 'Japon Yeni', symbol: '¥' },
      { code: 'CHF', name: 'İsviçre Frangı', symbol: 'CHF' },
      { code: 'CAD', name: 'Kanada Doları', symbol: 'C$' },
      { code: 'AUD', name: 'Avustralya Doları', symbol: 'A$' },
      { code: 'CNY', name: 'Çin Yuanı', symbol: '¥' },
      { code: 'RUB', name: 'Rus Rublesi', symbol: '₽' },
      { code: 'KRW', name: 'Güney Kore Wonu', symbol: '₩' },
      { code: 'INR', name: 'Hindistan Rupisi', symbol: '₹' },
      { code: 'BRL', name: 'Brezilya Reali', symbol: 'R$' },
      { code: 'ZAR', name: 'Güney Afrika Randı', symbol: 'R' },
      { code: 'XAU', name: 'Altın (Gram)', symbol: 'XAU' },
      { code: 'BTC', name: 'Bitcoin', symbol: '₿' },
      { code: 'ETH', name: 'Ethereum', symbol: 'Ξ' },
    ];
  };

  // Bileşen yüklendiğinde kurları yükle
  useEffect(() => {
    loadRatesFromDB();

    // Her 6 saatte bir kurları güncelle
    const interval = setInterval(() => {
      fetchRatesFromAPI();
    }, 6 * 60 * 60 * 1000);

    return () => clearInterval(interval);
  }, []);

  const value = {
    rates,
    lastUpdated,
    isLoading,
    error,
    updateRates,
    convertCurrency,
    formatCurrency,
    getSupportedCurrencies
  };

  return (
    <ExchangeRateContext.Provider value={value}>
      {children}
    </ExchangeRateContext.Provider>
  );
};

/**
 * Döviz kuru context hook'u
 * @returns {Object} Döviz kuru context değerleri
 */
export const useExchangeRate = () => {
  const context = useContext(ExchangeRateContext);

  if (!context) {
    throw new Error('useExchangeRate hook must be used within an ExchangeRateProvider');
  }

  return context;
};

// Geriye dönük uyumluluk için eski adı da export et
export const useExchangeRates = useExchangeRate;
