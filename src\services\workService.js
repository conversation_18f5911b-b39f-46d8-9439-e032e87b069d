/**
 * <PERSON><PERSON> a<PERSON>ını getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<Object>} Mesai a<PERSON>ları
 */
export const getWorkSettings = async (db) => {
  try {
    const settings = await db.getFirstAsync(`
      SELECT * FROM work_settings
      ORDER BY id DESC
      LIMIT 1
    `);

    return settings || null;
  } catch (error) {
    console.error('Mesai ayarları getirme hatası:', error);
    throw error;
  }
};

/**
 * Mesai ayarlarını kaydeder (ekler veya günceller)
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} settings - Kaydedilecek ayarlar
 * @returns {Promise<number>} Etkilenen satır sayısı veya eklenen kaydın ID'si
 */
export const saveWorkSettings = async (db, settings) => {
  return updateWorkSettings(db, settings);
};

/**
 * <PERSON>i a<PERSON>ı<PERSON> günceller
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} settings - Güncellenecek ayarlar
 * @returns {Promise<number>} Etkilenen satır sayısı
 */
export const updateWorkSettings = async (db, settings) => {
  try {
    // Önce ayarların var olup olmadığını kontrol et
    const existingSettings = await db.getFirstAsync(`
      SELECT id FROM work_settings
      LIMIT 1
    `);

    if (existingSettings) {
      // Mevcut ayarları güncelle
      const result = await db.runAsync(`
        UPDATE work_settings
        SET hourly_rate = ?,
            overtime_rate = ?,
            weekly_work_hours = ?,
            daily_work_hours = ?,
            currency = ?,
            work_days = ?,
            auto_create_shifts = ?,
            auto_create_days_ahead = ?,
            shift_notification_enabled = ?,
            shift_notification_minutes = ?
        WHERE id = ?
      `, [
        settings.hourly_rate || 100,
        settings.overtime_rate || 150,
        settings.weekly_work_hours || 45,
        settings.daily_work_hours || 9,
        settings.currency || 'TRY',
        settings.work_days || '1,2,3,4,5',
        settings.auto_create_shifts !== undefined ? settings.auto_create_shifts : 1,
        settings.auto_create_days_ahead || 7,
        settings.shift_notification_enabled !== undefined ? settings.shift_notification_enabled : 1,
        settings.shift_notification_minutes || 60,
        existingSettings.id
      ]);

      return result.changes;
    } else {
      // Yeni ayarlar ekle
      const result = await db.runAsync(`
        INSERT INTO work_settings (
          hourly_rate, overtime_rate, weekly_work_hours,
          daily_work_hours, currency, work_days,
          auto_create_shifts, auto_create_days_ahead,
          shift_notification_enabled, shift_notification_minutes
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        settings.hourly_rate || 100,
        settings.overtime_rate || 150,
        settings.weekly_work_hours || 45,
        settings.daily_work_hours || 9,
        settings.currency || 'TRY',
        settings.work_days || '1,2,3,4,5',
        settings.auto_create_shifts !== undefined ? settings.auto_create_shifts : 1,
        settings.auto_create_days_ahead || 7,
        settings.shift_notification_enabled !== undefined ? settings.shift_notification_enabled : 1,
        settings.shift_notification_minutes || 60
      ]);

      return result.lastInsertRowId;
    }
  } catch (error) {
    console.error('Mesai ayarları güncelleme hatası:', error);
    throw error;
  }
};

/**
 * Mesai vardiyası ekler
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} shift - Eklenecek vardiya
 * @returns {Promise<number>} Eklenen vardiyanın ID'si
 */
export const addWorkShift = async (db, shift) => {
  try {
    const result = await db.runAsync(`
      INSERT INTO work_shifts (
        date, start_time, end_time, break_duration,
        is_overtime, overtime_multiplier, notes, status,
        shift_type_id, schedule_id
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      shift.date,
      shift.start_time,
      shift.end_time || null,
      shift.break_duration || 0,
      shift.is_overtime || 0,
      shift.overtime_multiplier || 1.5,
      shift.notes || null,
      shift.status || 'planned',
      shift.shift_type_id || null,
      shift.schedule_id || null
    ]);

    return result.lastInsertRowId;
  } catch (error) {
    console.error('Vardiya ekleme hatası:', error);
    throw error;
  }
};

/**
 * Mesai vardiyasını günceller
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} id - Vardiya ID'si
 * @param {Object} shift - Güncellenecek vardiya verileri
 * @returns {Promise<number>} Etkilenen satır sayısı
 */
export const updateWorkShift = async (db, id, shift) => {
  try {
    // Önce mevcut vardiyayı getir
    const existingShift = await getWorkShift(db, id);
    if (!existingShift) {
      throw new Error(`Vardiya bulunamadı: ${id}`);
    }

    // Eksik alanları mevcut vardiyadan doldur
    const updatedShift = {
      date: shift.date || existingShift.date,
      start_time: shift.start_time || existingShift.start_time,
      end_time: shift.end_time || existingShift.end_time,
      break_duration: shift.break_duration !== undefined ? shift.break_duration : existingShift.break_duration,
      is_overtime: shift.is_overtime !== undefined ? shift.is_overtime : existingShift.is_overtime,
      overtime_multiplier: shift.overtime_multiplier || existingShift.overtime_multiplier || 1.5,
      notes: shift.notes !== undefined ? shift.notes : existingShift.notes,
      status: shift.status || existingShift.status || 'planned',
      shift_type_id: shift.shift_type_id !== undefined ? shift.shift_type_id : existingShift.shift_type_id,
      schedule_id: shift.schedule_id !== undefined ? shift.schedule_id : existingShift.schedule_id,
      hourly_rate: shift.hourly_rate !== undefined ? shift.hourly_rate : existingShift.hourly_rate,
      is_holiday: shift.is_holiday !== undefined ? shift.is_holiday : existingShift.is_holiday,
      holiday_multiplier: shift.holiday_multiplier || existingShift.holiday_multiplier || 2.0,
      earnings: shift.earnings !== undefined ? shift.earnings : existingShift.earnings
    };

    const result = await db.runAsync(`
      UPDATE work_shifts
      SET date = ?,
          start_time = ?,
          end_time = ?,
          break_duration = ?,
          is_overtime = ?,
          overtime_multiplier = ?,
          notes = ?,
          status = ?,
          shift_type_id = ?,
          schedule_id = ?,
          hourly_rate = ?,
          is_holiday = ?,
          holiday_multiplier = ?,
          earnings = ?
      WHERE id = ?
    `, [
      updatedShift.date,
      updatedShift.start_time,
      updatedShift.end_time || null,
      updatedShift.break_duration || 0,
      updatedShift.is_overtime || 0,
      updatedShift.overtime_multiplier || 1.5,
      updatedShift.notes || null,
      updatedShift.status || 'planned',
      updatedShift.shift_type_id || null,
      updatedShift.schedule_id || null,
      updatedShift.hourly_rate || 100,
      updatedShift.is_holiday || 0,
      updatedShift.holiday_multiplier || 2.0,
      updatedShift.earnings || null,
      id
    ]);

    return result.changes;
  } catch (error) {
    console.error('Vardiya güncelleme hatası:', error);
    throw error;
  }
};

/**
 * Mesai vardiyasının durumunu günceller
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} id - Vardiya ID'si
 * @param {string} status - Yeni durum ('planned', 'active', 'completed', 'cancelled')
 * @returns {Promise<number>} Etkilenen satır sayısı
 */
export const updateWorkShiftStatus = async (db, id, status) => {
  try {
    // Önce mevcut vardiyayı getir
    const existingShift = await getWorkShift(db, id);
    if (!existingShift) {
      throw new Error(`Vardiya bulunamadı: ${id}`);
    }

    // Sadece durumu güncelle
    const result = await db.runAsync(`
      UPDATE work_shifts
      SET status = ?
      WHERE id = ?
    `, [status, id]);

    return result.changes;
  } catch (error) {
    console.error('Vardiya durumu güncelleme hatası:', error);
    throw error;
  }
};

/**
 * Belirli planlamalardan vardiyalar oluşturur
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Array<number>} scheduleIds - Planlama ID'leri
 * @param {number} daysAhead - Kaç gün ilerisine kadar vardiya oluşturulacağı
 * @returns {Promise<number>} Oluşturulan vardiya sayısı
 */
export const createShiftsFromSpecificSchedules = async (db, scheduleIds, daysAhead = 7) => {
  try {
    // Transaction başlat
    await db.execAsync('BEGIN TRANSACTION;');

    let createdShifts = 0;

    // Her planlama için
    for (const scheduleId of scheduleIds) {
      // Planlamayı getir
      const schedule = await db.getFirstAsync(`
        SELECT * FROM work_shift_schedules
        WHERE id = ?
      `, [scheduleId]);

      if (!schedule || schedule.is_active !== 1) {
        continue;
      }

      // Vardiya türünü getir
      const shiftType = await db.getFirstAsync(`
        SELECT * FROM work_shift_types
        WHERE id = ?
      `, [schedule.shift_type_id]);

      if (!shiftType) {
        continue;
      }

      // Tekrar günlerini parse et
      const daysOfWeek = schedule.days_of_week ? schedule.days_of_week.split(',').map(Number) : [];

      // Bugünden başlayarak daysAhead kadar gün için vardiya oluştur
      const today = new Date();

      for (let i = 0; i < daysAhead; i++) {
        const currentDate = new Date(today);
        currentDate.setDate(currentDate.getDate() + i);

        // Eğer bitiş tarihi varsa ve bu tarihten sonraysa, vardiya oluşturma
        if (schedule.end_date) {
          const endDate = new Date(schedule.end_date);
          if (currentDate > endDate) {
            continue;
          }
        }

        // Eğer başlangıç tarihinden önceyse, vardiya oluşturma
        const startDate = new Date(schedule.start_date);
        if (currentDate < startDate) {
          continue;
        }

        // Günün haftanın hangi günü olduğunu kontrol et (0: Pazar, 1: Pazartesi, ..., 6: Cumartesi)
        const dayOfWeek = currentDate.getDay();

        // Eğer bu gün planlama günlerinden biri değilse, vardiya oluşturma
        if (daysOfWeek.length > 0 && !daysOfWeek.includes(dayOfWeek)) {
          continue;
        }

        // Tarih formatını ayarla (YYYY-MM-DD)
        const dateStr = currentDate.toISOString().split('T')[0];

        // Bu tarihte zaten vardiya var mı kontrol et
        const existingShift = await db.getFirstAsync(`
          SELECT * FROM work_shifts
          WHERE date = ? AND shift_type_id = ?
        `, [dateStr, schedule.shift_type_id]);

        if (existingShift) {
          continue;
        }

        // Vardiya ekle
        const result = await db.runAsync(`
          INSERT INTO work_shifts (
            date, start_time, end_time, break_duration,
            is_overtime, overtime_multiplier, notes, status,
            shift_type_id, schedule_id, hourly_rate, is_holiday, holiday_multiplier
          )
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          dateStr,
          shiftType.start_time,
          shiftType.end_time,
          shiftType.break_duration || 0,
          0, // is_overtime
          1.5, // overtime_multiplier
          `${schedule.name || 'Vardiya Planı'} tarafından oluşturuldu.`,
          'planned', // status
          shiftType.id,
          schedule.id,
          shiftType.hourly_rate || 100,
          0, // is_holiday
          2.0 // holiday_multiplier
        ]);

        if (result.changes > 0) {
          createdShifts++;

          // Vardiya bildirimi oluştur
          await addWorkShiftNotification(db, {
            shift_id: result.lastInsertRowId,
            schedule_id: schedule.id,
            notification_time: new Date().toISOString(), // Şimdilik şu anki zaman
            is_sent: 0,
            title: `Vardiya Planlandı: ${shiftType.name}`,
            body: `${dateStr} tarihinde saat ${shiftType.start_time.substring(0, 5)}'de vardiya planlandı.`
          });
        }
      }
    }

    // Transaction'ı tamamla
    await db.execAsync('COMMIT;');

    return createdShifts;
  } catch (error) {
    // Hata durumunda transaction'ı geri al
    await db.execAsync('ROLLBACK;');
    console.error('Vardiya oluşturma hatası:', error);
    throw error;
  }
};



/**
 * Mesai vardiyasını siler
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} id - Vardiya ID'si
 * @returns {Promise<number>} Etkilenen satır sayısı
 */
export const deleteWorkShift = async (db, id) => {
  try {
    const result = await db.runAsync(`
      DELETE FROM work_shifts
      WHERE id = ?
    `, [id]);

    return result.changes;
  } catch (error) {
    console.error('Mesai vardiyası silme hatası:', error);
    throw error;
  }
};

/**
 * Mesai vardiyasını getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} id - Vardiya ID'si
 * @returns {Promise<Object>} Vardiya
 */
export const getWorkShift = async (db, id) => {
  try {
    const shift = await db.getFirstAsync(`
      SELECT * FROM work_shifts
      WHERE id = ?
    `, [id]);

    return shift || null;
  } catch (error) {
    console.error('Mesai vardiyası getirme hatası:', error);
    throw error;
  }
};

/**
 * Mesai vardiyalarını getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} options - Sorgu seçenekleri
 * @param {string} options.startDate - Başlangıç tarihi
 * @param {string} options.endDate - Bitiş tarihi
 * @param {string} options.status - Durum filtresi
 * @returns {Promise<Array>} Vardiyalar
 */
export const getWorkShifts = async (db, options = {}) => {
  try {
    let query = `
      SELECT * FROM work_shifts
      WHERE 1=1
    `;

    const params = [];

    if (options.startDate) {
      query += ' AND date >= ?';
      params.push(options.startDate);
    }

    if (options.endDate) {
      query += ' AND date <= ?';
      params.push(options.endDate);
    }

    if (options.status) {
      query += ' AND status = ?';
      params.push(options.status);
    }

    query += ' ORDER BY date DESC, start_time DESC';

    return await db.getAllAsync(query, params);
  } catch (error) {
    console.error('Mesai vardiyaları getirme hatası:', error);
    throw error;
  }
};

/**
 * Belirli bir tarih aralığındaki mesai vardiyalarını getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {string} startDate - Başlangıç tarihi (YYYY-MM-DD)
 * @param {string} endDate - Bitiş tarihi (YYYY-MM-DD)
 * @returns {Promise<Array>} Vardiyalar
 */
export const getWorkShiftsByDateRange = async (db, startDate, endDate) => {
  return getWorkShifts(db, { startDate, endDate });
};

/**
 * Belirli bir tarihteki mesai vardiyalarını getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {string} date - Tarih (YYYY-MM-DD)
 * @returns {Promise<Array>} Vardiyalar
 */
export const getWorkShiftsByDate = async (db, date) => {
  try {
    return await db.getAllAsync(`
      SELECT * FROM work_shifts
      WHERE date = ?
      ORDER BY start_time ASC
    `, [date]);
  } catch (error) {
    console.error('Tarihe göre vardiya getirme hatası:', error);
    throw error;
  }
};

/**
 * Belirli bir durumdaki vardiyaları getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {string} status - Vardiya durumu ('active', 'completed', 'planned', 'cancelled')
 * @returns {Promise<Array>} Belirtilen durumdaki vardiyalar
 */
export const getWorkShiftsByStatus = async (db, status) => {
  try {
    return await db.getAllAsync(`
      SELECT * FROM work_shifts
      WHERE status = ?
      ORDER BY date DESC, start_time DESC
    `, [status]);
  } catch (error) {
    console.error(`${status} durumundaki vardiyaları getirme hatası:`, error);
    throw error;
  }
};

/**
 * Aktif vardiyaları getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<Array>} Aktif vardiyalar
 */
export const getActiveWorkShifts = async (db) => {
  return getWorkShiftsByStatus(db, 'active');
};

/**
 * Belirli bir aydaki mesai vardiyalarını getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} year - Yıl
 * @param {number} month - Ay (1-12)
 * @returns {Promise<Array>} Vardiyalar
 */
export const getWorkShiftsByMonth = async (db, year, month) => {
  // Ay başlangıç ve bitiş tarihlerini hesapla
  const startDate = `${year}-${month.toString().padStart(2, '0')}-01`;

  // Ay sonu tarihini hesapla
  const lastDay = new Date(year, month, 0).getDate();
  const endDate = `${year}-${month.toString().padStart(2, '0')}-${lastDay}`;

  return getWorkShifts(db, { startDate, endDate });
};

/**
 * Mesai ödemesi ekler
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} payment - Eklenecek ödeme
 * @returns {Promise<number>} Eklenen ödemenin ID'si
 */
export const addWorkPayment = async (db, payment) => {
  try {
    const result = await db.runAsync(`
      INSERT INTO work_payments (
        period_start_date, period_end_date, regular_hours,
        overtime_hours, regular_amount, overtime_amount,
        total_amount, payment_date, is_paid, notes
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      payment.period_start_date,
      payment.period_end_date,
      payment.regular_hours,
      payment.overtime_hours,
      payment.regular_amount,
      payment.overtime_amount,
      payment.total_amount,
      payment.payment_date || null,
      payment.is_paid || 0,
      payment.notes || null
    ]);

    return result.lastInsertRowId;
  } catch (error) {
    console.error('Mesai ödemesi ekleme hatası:', error);
    throw error;
  }
};

/**
 * Mesai ödemesini günceller
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} id - Ödeme ID'si
 * @param {Object} payment - Güncellenecek ödeme verileri
 * @returns {Promise<number>} Etkilenen satır sayısı
 */
export const updateWorkPayment = async (db, id, payment) => {
  try {
    const result = await db.runAsync(`
      UPDATE work_payments
      SET period_start_date = ?,
          period_end_date = ?,
          regular_hours = ?,
          overtime_hours = ?,
          regular_amount = ?,
          overtime_amount = ?,
          total_amount = ?,
          payment_date = ?,
          is_paid = ?,
          notes = ?
      WHERE id = ?
    `, [
      payment.period_start_date,
      payment.period_end_date,
      payment.regular_hours,
      payment.overtime_hours,
      payment.regular_amount,
      payment.overtime_amount,
      payment.total_amount,
      payment.payment_date || null,
      payment.is_paid || 0,
      payment.notes || null,
      id
    ]);

    return result.changes;
  } catch (error) {
    console.error('Mesai ödemesi güncelleme hatası:', error);
    throw error;
  }
};

/**
 * Mesai ödemesini siler
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} id - Ödeme ID'si
 * @returns {Promise<number>} Etkilenen satır sayısı
 */
export const deleteWorkPayment = async (db, id) => {
  try {
    const result = await db.runAsync(`
      DELETE FROM work_payments
      WHERE id = ?
    `, [id]);

    return result.changes;
  } catch (error) {
    console.error('Mesai ödemesi silme hatası:', error);
    throw error;
  }
};

/**
 * Mesai ödemelerini getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} options - Sorgu seçenekleri
 * @param {string} options.startDate - Başlangıç tarihi
 * @param {string} options.endDate - Bitiş tarihi
 * @param {boolean} options.isPaid - Ödeme durumu
 * @returns {Promise<Array>} Ödemeler
 */
export const getWorkPayments = async (db, options = {}) => {
  try {
    let query = `
      SELECT * FROM work_payments
      WHERE 1=1
    `;

    const params = [];

    if (options.startDate) {
      query += ' AND period_start_date >= ?';
      params.push(options.startDate);
    }

    if (options.endDate) {
      query += ' AND period_end_date <= ?';
      params.push(options.endDate);
    }

    if (options.isPaid !== undefined) {
      query += ' AND is_paid = ?';
      params.push(options.isPaid ? 1 : 0);
    }

    query += ' ORDER BY period_start_date DESC';

    return await db.getAllAsync(query, params);
  } catch (error) {
    console.error('Mesai ödemeleri getirme hatası:', error);
    throw error;
  }
};

/**
 * Belirli bir tarih aralığındaki mesai saatlerini hesaplar
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {string} startDate - Başlangıç tarihi (YYYY-MM-DD)
 * @param {string} endDate - Bitiş tarihi (YYYY-MM-DD)
 * @returns {Promise<Object>} Mesai saatleri ve tutarları
 */
export const calculateWorkHours = async (db, startDate, endDate) => {
  try {
    // Mesai ayarlarını getir
    const settings = await getWorkSettings(db);

    if (!settings) {
      throw new Error('Mesai ayarları bulunamadı');
    }

    // Vardiyaları getir
    const shifts = await getWorkShiftsByDateRange(db, startDate, endDate);

    // Toplam saatleri hesapla
    let regularHours = 0;
    let overtimeHours = 0;

    for (const shift of shifts) {
      if (shift.status !== 'completed') continue;

      // Başlangıç ve bitiş zamanlarını parse et
      const startTime = new Date(`${shift.date}T${shift.start_time}`);
      const endTime = new Date(`${shift.date}T${shift.end_time}`);

      // Çalışma süresini hesapla (saat cinsinden)
      let hours = (endTime - startTime) / (1000 * 60 * 60);

      // Mola süresini çıkar
      hours -= (shift.break_duration || 0) / 60;

      // Fazla mesai mi?
      if (shift.is_overtime) {
        overtimeHours += hours;
      } else {
        regularHours += hours;
      }
    }

    // Tutarları hesapla
    const regularAmount = regularHours * settings.hourly_rate;
    const overtimeAmount = overtimeHours * settings.overtime_rate;
    const totalAmount = regularAmount + overtimeAmount;

    return {
      regularHours,
      overtimeHours,
      regularAmount,
      overtimeAmount,
      totalAmount,
      currency: settings.currency
    };
  } catch (error) {
    console.error('Mesai saatleri hesaplama hatası:', error);
    throw error;
  }
};

/**
 * Mesai vardiyasının süresini hesaplar
 *
 * @param {Object} shift - Vardiya
 * @returns {number} Saat cinsinden süre
 */
export const calculateShiftDuration = (shift) => {
  if (!shift.start_time || !shift.end_time) return 0;

  // Başlangıç ve bitiş zamanlarını parse et
  const startTime = new Date(`${shift.date}T${shift.start_time}`);
  const endTime = new Date(`${shift.date}T${shift.end_time}`);

  // Çalışma süresini hesapla (saat cinsinden)
  let hours = (endTime - startTime) / (1000 * 60 * 60);

  // Eğer bitiş saati başlangıç saatinden küçükse, ertesi güne geçmiş demektir
  if (hours < 0) {
    hours += 24;
  }

  // Mola süresini çıkar
  hours -= (shift.break_duration || 0) / 60;

  return Math.max(0, hours);
};

/**
 * Mesai vardiyasının ücretini hesaplar
 *
 * @param {Object} shift - Vardiya
 * @param {Object} settings - Mesai ayarları
 * @returns {number} Vardiya ücreti
 */
export const calculateShiftAmount = (shift, settings) => {
  const duration = calculateShiftDuration(shift);
  return shift.is_overtime ? duration * settings.overtime_rate : duration * settings.hourly_rate;
};

/**
 * Mesai ödemesini getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} id - Ödeme ID'si
 * @returns {Promise<Object>} Ödeme
 */
export const getWorkPayment = async (db, id) => {
  try {
    const payment = await db.getFirstAsync(`
      SELECT * FROM work_payments
      WHERE id = ?
    `, [id]);

    return payment || null;
  } catch (error) {
    console.error('Mesai ödemesi getirme hatası:', error);
    throw error;
  }
};

/**
 * Vardiya türlerini getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<Array>} Vardiya türleri
 */
export const getWorkShiftTypes = async (db) => {
  try {
    return await db.getAllAsync(`
      SELECT * FROM work_shift_types
      ORDER BY name
    `);
  } catch (error) {
    console.error('Vardiya türleri getirme hatası:', error);
    throw error;
  }
};

/**
 * Vardiya türü ekler
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} shiftType - Eklenecek vardiya türü
 * @returns {Promise<number>} Eklenen vardiya türünün ID'si
 */
export const addWorkShiftType = async (db, shiftType) => {
  try {
    const result = await db.runAsync(`
      INSERT INTO work_shift_types (
        name, start_time, end_time, color, is_night_shift, break_duration, notes
      )
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [
      shiftType.name,
      shiftType.start_time,
      shiftType.end_time,
      shiftType.color,
      shiftType.is_night_shift || 0,
      shiftType.break_duration || 0,
      shiftType.notes || null
    ]);

    return result.lastInsertRowId;
  } catch (error) {
    console.error('Vardiya türü ekleme hatası:', error);
    throw error;
  }
};

/**
 * Vardiya türünü günceller
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} id - Vardiya türü ID'si
 * @param {Object} shiftType - Güncellenecek vardiya türü verileri
 * @returns {Promise<number>} Etkilenen satır sayısı
 */
export const updateWorkShiftType = async (db, id, shiftType) => {
  try {
    const result = await db.runAsync(`
      UPDATE work_shift_types
      SET name = ?,
          start_time = ?,
          end_time = ?,
          color = ?,
          is_night_shift = ?,
          break_duration = ?,
          notes = ?
      WHERE id = ?
    `, [
      shiftType.name,
      shiftType.start_time,
      shiftType.end_time,
      shiftType.color,
      shiftType.is_night_shift || 0,
      shiftType.break_duration || 0,
      shiftType.notes || null,
      id
    ]);

    return result.changes;
  } catch (error) {
    console.error('Vardiya türü güncelleme hatası:', error);
    throw error;
  }
};

/**
 * Vardiya türünü siler
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} id - Vardiya türü ID'si
 * @returns {Promise<number>} Etkilenen satır sayısı
 */
export const deleteWorkShiftType = async (db, id) => {
  try {
    const result = await db.runAsync(`
      DELETE FROM work_shift_types
      WHERE id = ?
    `, [id]);

    return result.changes;
  } catch (error) {
    console.error('Vardiya türü silme hatası:', error);
    throw error;
  }
};

/**
 * Vardiya planlaması ekler
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} schedule - Eklenecek vardiya planlaması
 * @returns {Promise<number>} Eklenen planlamanın ID'si
 */
export const addWorkShiftSchedule = async (db, schedule) => {
  try {
    const result = await db.runAsync(`
      INSERT INTO work_shift_schedules (
        shift_type_id, start_date, end_date, repeat_type, repeat_interval,
        repeat_days, repeat_months, repeat_week_of_month, repeat_day_of_week,
        repeat_day_of_month, is_active, notes
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      schedule.shift_type_id,
      schedule.start_date,
      schedule.end_date || null,
      schedule.repeat_type,
      schedule.repeat_interval || 1,
      schedule.repeat_days || null,
      schedule.repeat_months || null,
      schedule.repeat_week_of_month || null,
      schedule.repeat_day_of_week || null,
      schedule.repeat_day_of_month || null,
      schedule.is_active || 1,
      schedule.notes || null
    ]);

    return result.lastInsertRowId;
  } catch (error) {
    console.error('Vardiya planlaması ekleme hatası:', error);
    throw error;
  }
};

/**
 * Vardiya planlamasını günceller
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} id - Vardiya planlaması ID'si
 * @param {Object} schedule - Güncellenecek vardiya planlaması verileri
 * @returns {Promise<number>} Etkilenen satır sayısı
 */
export const updateWorkShiftSchedule = async (db, id, schedule) => {
  try {
    const result = await db.runAsync(`
      UPDATE work_shift_schedules
      SET shift_type_id = ?,
          start_date = ?,
          end_date = ?,
          repeat_type = ?,
          repeat_interval = ?,
          repeat_days = ?,
          repeat_months = ?,
          repeat_week_of_month = ?,
          repeat_day_of_week = ?,
          repeat_day_of_month = ?,
          is_active = ?,
          notes = ?
      WHERE id = ?
    `, [
      schedule.shift_type_id,
      schedule.start_date,
      schedule.end_date || null,
      schedule.repeat_type,
      schedule.repeat_interval || 1,
      schedule.repeat_days || null,
      schedule.repeat_months || null,
      schedule.repeat_week_of_month || null,
      schedule.repeat_day_of_week || null,
      schedule.repeat_day_of_month || null,
      schedule.is_active || 1,
      schedule.notes || null,
      id
    ]);

    return result.changes;
  } catch (error) {
    console.error('Vardiya planlaması güncelleme hatası:', error);
    throw error;
  }
};

/**
 * Vardiya planlamasını siler
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} id - Vardiya planlaması ID'si
 * @returns {Promise<number>} Etkilenen satır sayısı
 */
export const deleteWorkShiftSchedule = async (db, id) => {
  try {
    const result = await db.runAsync(`
      DELETE FROM work_shift_schedules
      WHERE id = ?
    `, [id]);

    return result.changes;
  } catch (error) {
    console.error('Vardiya planlaması silme hatası:', error);
    throw error;
  }
};

/**
 * Vardiya planlamalarını getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} options - Sorgu seçenekleri
 * @param {boolean} options.onlyActive - Sadece aktif planlamaları getir
 * @returns {Promise<Array>} Vardiya planlamaları
 */
export const getWorkShiftSchedules = async (db, options = {}) => {
  try {
    let query = `
      SELECT s.*, t.name as shift_type_name, t.color as shift_type_color
      FROM work_shift_schedules s
      LEFT JOIN work_shift_types t ON s.shift_type_id = t.id
      WHERE 1=1
    `;

    const params = [];

    if (options.onlyActive) {
      query += ' AND s.is_active = 1';
    }

    query += ' ORDER BY s.start_date DESC';

    return await db.getAllAsync(query, params);
  } catch (error) {
    console.error('Vardiya planlamaları getirme hatası:', error);
    throw error;
  }
};

/**
 * Vardiya bildirimi ekler
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} notification - Eklenecek bildirim
 * @returns {Promise<number>} Eklenen bildirimin ID'si
 */
export const addWorkShiftNotification = async (db, notification) => {
  try {
    const result = await db.runAsync(`
      INSERT INTO work_shift_notifications (
        shift_id, schedule_id, notification_time, is_sent, title, body
      )
      VALUES (?, ?, ?, ?, ?, ?)
    `, [
      notification.shift_id || null,
      notification.schedule_id || null,
      notification.notification_time,
      notification.is_sent || 0,
      notification.title,
      notification.body
    ]);

    return result.lastInsertRowId;
  } catch (error) {
    console.warn('Vardiya bildirimi ekleme hatası:', error);
    // Hata durumunda sessizce devam et
    return 0;
  }
};

/**
 * Vardiya planlamasına göre otomatik vardiya oluşturur
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} options - Seçenekler
 * @param {string} options.startDate - Başlangıç tarihi (YYYY-MM-DD)
 * @param {string} options.endDate - Bitiş tarihi (YYYY-MM-DD)
 * @returns {Promise<Array>} Oluşturulan vardiyalar
 */
export const createShiftsFromSchedules = async (db, options = {}) => {
  try {
    // Ayarları getir
    const settings = await getWorkSettings(db);

    if (!settings || !settings.auto_create_shifts) {
      return [];
    }

    // Başlangıç ve bitiş tarihlerini belirle
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const startDate = options.startDate
      ? new Date(options.startDate)
      : today;

    const endDate = options.endDate
      ? new Date(options.endDate)
      : new Date(today);

    if (!options.endDate) {
      endDate.setDate(endDate.getDate() + (settings.auto_create_days_ahead || 7));
    }

    // Aktif planlamaları getir
    const schedules = await getWorkShiftSchedules(db, { onlyActive: true });

    if (!schedules || schedules.length === 0) {
      return [];
    }

    // Mevcut vardiyaları getir
    const existingShifts = await getWorkShiftsByDateRange(
      db,
      startDate.toISOString().split('T')[0],
      endDate.toISOString().split('T')[0]
    );

    // Oluşturulan vardiyaları sakla
    const createdShifts = [];

    // Her planlama için vardiya oluştur
    for (const schedule of schedules) {
      // Planlama başlangıç ve bitiş tarihlerini kontrol et
      const scheduleStartDate = new Date(schedule.start_date);
      const scheduleEndDate = schedule.end_date ? new Date(schedule.end_date) : null;

      // Planlama henüz başlamadıysa veya bittiyse atla
      if (scheduleStartDate > endDate || (scheduleEndDate && scheduleEndDate < startDate)) {
        continue;
      }

      // Vardiya türünü getir
      const shiftType = await db.getFirstAsync(`
        SELECT * FROM work_shift_types WHERE id = ?
      `, [schedule.shift_type_id]);

      if (!shiftType) {
        continue;
      }

      // Başlangıç tarihini belirle (planlama başlangıcı veya bugün, hangisi daha sonraysa)
      const effectiveStartDate = scheduleStartDate > startDate ? scheduleStartDate : startDate;

      // Bitiş tarihini belirle (planlama bitişi veya belirtilen bitiş, hangisi daha önceyse)
      const effectiveEndDate = scheduleEndDate && scheduleEndDate < endDate ? scheduleEndDate : endDate;

      // Tarih aralığındaki her gün için kontrol et
      const currentDate = new Date(effectiveStartDate);

      while (currentDate <= effectiveEndDate) {
        // Tekrarlama tipine göre vardiya oluştur
        let shouldCreateShift = false;

        switch (schedule.repeat_type) {
          case 'daily':
            // Her X günde bir
            const daysSinceStart = Math.floor((currentDate - scheduleStartDate) / (1000 * 60 * 60 * 24));
            shouldCreateShift = daysSinceStart % (schedule.repeat_interval || 1) === 0;
            break;

          case 'weekly':
            // Haftanın belirli günlerinde
            if (schedule.repeat_days) {
              const dayOfWeek = currentDate.getDay() || 7; // 0 (Pazar) -> 7 olarak değiştir
              const repeatDays = schedule.repeat_days.split(',').map(d => parseInt(d));

              // Haftanın bu günü seçilmiş mi?
              if (repeatDays.includes(dayOfWeek)) {
                // Tekrarlama aralığını kontrol et
                const weeksSinceStart = Math.floor((currentDate - scheduleStartDate) / (1000 * 60 * 60 * 24 * 7));
                shouldCreateShift = weeksSinceStart % (schedule.repeat_interval || 1) === 0;
              }
            }
            break;

          case 'monthly':
            // Ayın belirli günlerinde
            const dayOfMonth = currentDate.getDate();

            if (schedule.repeat_day_of_month) {
              // Ayın belirli bir günü
              shouldCreateShift = dayOfMonth === schedule.repeat_day_of_month;
            } else if (schedule.repeat_week_of_month && schedule.repeat_day_of_week) {
              // Ayın belirli haftasının belirli günü (örn. her ayın 2. pazartesi)
              const dayOfWeek = currentDate.getDay() || 7;

              if (dayOfWeek === schedule.repeat_day_of_week) {
                // Ayın kaçıncı haftası olduğunu hesapla
                const weekOfMonth = Math.ceil(dayOfMonth / 7);
                shouldCreateShift = weekOfMonth === schedule.repeat_week_of_month;
              }
            }

            // Tekrarlama aralığını kontrol et
            if (shouldCreateShift && schedule.repeat_interval > 1) {
              const monthsSinceStart =
                (currentDate.getFullYear() - scheduleStartDate.getFullYear()) * 12 +
                (currentDate.getMonth() - scheduleStartDate.getMonth());

              shouldCreateShift = monthsSinceStart % (schedule.repeat_interval || 1) === 0;
            }
            break;

          default:
            shouldCreateShift = false;
        }

        // Vardiya oluşturulacaksa
        if (shouldCreateShift) {
          // Tarih formatını ayarla (YYYY-MM-DD)
          const dateStr = currentDate.toISOString().split('T')[0];

          // Bu tarihte ve bu planlama için zaten vardiya var mı kontrol et
          const existingShift = existingShifts.find(shift =>
            shift.date === dateStr && shift.schedule_id === schedule.id
          );

          if (!existingShift) {
            // Yeni vardiya oluştur
            const newShift = {
              date: dateStr,
              start_time: shiftType.start_time,
              end_time: shiftType.end_time,
              break_duration: shiftType.break_duration || 0,
              is_overtime: 0,
              overtime_multiplier: 1.5,
              notes: `${shiftType.name} - Otomatik oluşturuldu`,
              status: 'planned',
              shift_type_id: shiftType.id,
              schedule_id: schedule.id
            };

            // Vardiyayı ekle
            const shiftId = await addWorkShift(db, newShift);

            if (shiftId) {
              newShift.id = shiftId;
              createdShifts.push(newShift);

              // Bildirim oluştur
              if (settings.shift_notification_enabled) {
                // Vardiya başlangıç zamanını hesapla
                const shiftDateTime = new Date(`${dateStr}T${shiftType.start_time}`);

                // Bildirim zamanını hesapla (varsayılan olarak 1 saat önce)
                const notificationMinutes = settings.shift_notification_minutes || 60;
                const notificationTime = new Date(shiftDateTime);
                notificationTime.setMinutes(notificationTime.getMinutes() - notificationMinutes);

                // Bildirim oluştur
                await addWorkShiftNotification(db, {
                  shift_id: shiftId,
                  schedule_id: schedule.id,
                  notification_time: notificationTime.toISOString(),
                  is_sent: 0,
                  title: `Vardiya Hatırlatması: ${shiftType.name}`,
                  body: `${dateStr} tarihinde saat ${shiftType.start_time.substring(0, 5)}'de vardiya başlayacak.`
                });
              }
            }
          }
        }

        // Sonraki güne geç
        currentDate.setDate(currentDate.getDate() + 1);
      }
    }

    return createdShifts;
  } catch (error) {
    console.error('Otomatik vardiya oluşturma hatası:', error);
    throw error;
  }
};
