/**
 * <PERSON><PERSON><PERSON><PERSON> tabloları için migrasyon
 *
 * @param {object} db - SQLite veritabanı nesnesi
 * @returns {Promise<void>}
 */
export const migrateNotifications = async (db) => {
  try {
    console.log('Bildirim tabloları migrasyonu başlatılıyor...');

    // notifications tablosunu oluştur
    const hasNotificationsTable = await db.getFirstAsync(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='notifications'
    `);

    if (!hasNotificationsTable) {
      console.log('notifications tablosu oluşturuluyor...');

      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS notifications (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          title TEXT NOT NULL,
          message TEXT NOT NULL,
          type TEXT NOT NULL, -- 'transaction', 'budget', 'savings', 'reminder', 'system'
          status TEXT NOT NULL DEFAULT 'pending', -- 'pending', 'sent', 'read', 'cancelled'
          priority TEXT NOT NULL DEFAULT 'normal', -- 'low', 'normal', 'high'
          scheduled_at DATETIME,
          repeat_type TEXT, -- 'once', 'daily', 'weekly', 'monthly', 'yearly'
          repeat_interval INTEGER DEFAULT 1,
          repeat_days TEXT, -- JSON array of days (0-6, 0 is Sunday)
          repeat_months TEXT, -- JSON array of months (1-12)
          repeat_end_date DATETIME,
          category_id INTEGER,
          related_id INTEGER, -- ID of related entity (transaction, budget, etc.)
          related_type TEXT, -- Type of related entity
          data TEXT, -- JSON data
          is_enabled INTEGER NOT NULL DEFAULT 1,
          expo_notification_id TEXT, -- Expo notification ID
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE SET NULL
        )
      `);

      // İndeks oluştur
      await db.execAsync(`
        CREATE INDEX IF NOT EXISTS idx_notifications_status
        ON notifications (status, scheduled_at)
      `);

      await db.execAsync(`
        CREATE INDEX IF NOT EXISTS idx_notifications_type
        ON notifications (type, related_type, related_id)
      `);

      console.log('notifications tablosu başarıyla oluşturuldu.');
    } else {
      console.log('notifications tablosu zaten mevcut.');

      // Tabloda eksik sütunlar var mı kontrol et
      const columns = await db.getAllAsync(`PRAGMA table_info(notifications)`);

      // Sütun adlarını bir diziye dönüştür
      const columnNames = columns.map(col => col.name);

      // Eksik sütunları kontrol et ve ekle
      if (!columnNames.includes('repeat_type')) {
        console.log('repeat_type sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE notifications ADD COLUMN repeat_type TEXT`);
      }

      if (!columnNames.includes('repeat_interval')) {
        console.log('repeat_interval sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE notifications ADD COLUMN repeat_interval INTEGER DEFAULT 1`);
      }

      if (!columnNames.includes('repeat_days')) {
        console.log('repeat_days sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE notifications ADD COLUMN repeat_days TEXT`);
      }

      if (!columnNames.includes('repeat_months')) {
        console.log('repeat_months sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE notifications ADD COLUMN repeat_months TEXT`);
      }

      if (!columnNames.includes('repeat_end_date')) {
        console.log('repeat_end_date sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE notifications ADD COLUMN repeat_end_date DATETIME`);
      }

      if (!columnNames.includes('is_enabled')) {
        console.log('is_enabled sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE notifications ADD COLUMN is_enabled INTEGER NOT NULL DEFAULT 1`);
      }

      if (!columnNames.includes('priority')) {
        console.log('priority sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE notifications ADD COLUMN priority TEXT NOT NULL DEFAULT 'normal'`);
      }

      if (!columnNames.includes('expo_notification_id')) {
        console.log('expo_notification_id sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE notifications ADD COLUMN expo_notification_id TEXT`);
      }
    }

    // notification_history tablosunu oluştur
    const hasNotificationHistoryTable = await db.getFirstAsync(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='notification_history'
    `);

    if (!hasNotificationHistoryTable) {
      console.log('notification_history tablosu oluşturuluyor...');

      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS notification_history (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          notification_id INTEGER,
          title TEXT NOT NULL,
          message TEXT NOT NULL,
          status TEXT NOT NULL, -- 'sent', 'read', 'cancelled'
          sent_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          read_at DATETIME,
          data TEXT, -- JSON data
          FOREIGN KEY (notification_id) REFERENCES notifications (id) ON DELETE SET NULL
        )
      `);

      // İndeks oluştur
      await db.execAsync(`
        CREATE INDEX IF NOT EXISTS idx_notification_history_notification_id
        ON notification_history (notification_id)
      `);

      console.log('notification_history tablosu başarıyla oluşturuldu.');
    } else {
      console.log('notification_history tablosu zaten mevcut.');
    }

    // notification_settings tablosunu oluştur
    const hasNotificationSettingsTable = await db.getFirstAsync(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='notification_settings'
    `);

    if (!hasNotificationSettingsTable) {
      console.log('notification_settings tablosu oluşturuluyor...');

      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS notification_settings (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          type TEXT NOT NULL UNIQUE, -- 'transaction', 'budget', 'savings', 'reminder', 'system'
          is_enabled INTEGER NOT NULL DEFAULT 1,
          sound_enabled INTEGER NOT NULL DEFAULT 1,
          vibration_enabled INTEGER NOT NULL DEFAULT 1,
          quiet_hours_start TEXT, -- HH:MM format
          quiet_hours_end TEXT, -- HH:MM format
          quiet_hours_enabled INTEGER NOT NULL DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Varsayılan ayarları ekle
      await db.execAsync(`
        INSERT INTO notification_settings (type, is_enabled, sound_enabled, vibration_enabled)
        VALUES
          ('transaction', 1, 1, 1),
          ('budget', 1, 1, 1),
          ('savings', 1, 1, 1),
          ('reminder', 1, 1, 1),
          ('system', 1, 1, 1)
      `);

      console.log('notification_settings tablosu başarıyla oluşturuldu.');
    } else {
      console.log('notification_settings tablosu zaten mevcut.');
    }

    console.log('Bildirim tabloları migrasyonu tamamlandı.');
  } catch (error) {
    console.error('Bildirim tabloları migrasyon hatası:', error);
    throw error;
  }
};
