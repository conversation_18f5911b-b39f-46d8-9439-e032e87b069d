import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useTheme } from '../../../../context/ThemeContext';

/**
 * Önizleme Paneli - Gerçek zamanlı rapor önizleme
 * Interaktif önizleme ve test özellikleri
 */
const PreviewPanel = ({
  elements = [],
  reportData = [],
  isPreviewMode = false,
  onTogglePreview,
}) => {
  const { theme } = useTheme();

  return (
    <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      <View style={[styles.header, { backgroundColor: theme.SURFACE }]}>
        <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
          👁 Rapor Önizleme
        </Text>
        <TouchableOpacity
          style={[styles.toggleButton, { backgroundColor: theme.PRIMARY }]}
          onPress={onTogglePreview}
        >
          <Text style={[styles.toggleButtonText, { color: theme.SURFACE }]}>
            {isPreviewMode ? 'Editör Modu' : 'Önizleme Modu'}
          </Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.content}>
        <Text style={[styles.subtitle, { color: theme.TEXT_SECONDARY }]}>
          Gerçek zamanlı önizleme sistemi - Yakında aktif olacak
        </Text>
        <Text style={[styles.info, { color: theme.TEXT_SECONDARY }]}>
          {elements.length} widget, {reportData.length} veri kaydı
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
  },
  toggleButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  toggleButtonText: {
    fontSize: 12,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  subtitle: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 8,
  },
  info: {
    fontSize: 12,
    textAlign: 'center',
  },
});

export default PreviewPanel;
