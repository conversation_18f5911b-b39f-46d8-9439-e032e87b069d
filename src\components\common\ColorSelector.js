import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
  SafeAreaView,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../../constants/colors';

// Ren<PERSON> paleti
const COLORS = [
  '#3498db', // Mavi
  '#2ecc71', // Yeşil
  '#e74c3c', // Kırmızı
  '#f39c12', // Turuncu
  '#9b59b6', // Mor
  '#1abc9c', // Turkuaz
  '#34495e', // Lacivert
  '#7f8c8d', // <PERSON>ri
  '#d35400', // Turuncu koyu
  '#27ae60', // Ye<PERSON>il koyu
  '#2980b9', // Mavi koyu
  '#8e44ad', // Mor koyu
  '#c0392b', // Kırmızı koyu
  '#16a085', // Turkuaz koyu
  '#f1c40f', // Sarı
  '#e67e22', // <PERSON>runcu açık
  '#95a5a6', // <PERSON><PERSON> açık
  '#d35400', // Turuncu koyu
  '#2c3e50', // Lacivert koyu
];

/**
 * Renk seçici bileşeni
 * @param {Object} props Component props
 * @param {string} props.selectedColor Seçili renk
 * @param {Function} props.onSelectColor Renk seçildiğinde çağrılacak fonksiyon
 * @param {Function} props.onClose Modal kapatıldığında çağrılacak fonksiyon
 */
const ColorSelector = ({ selectedColor, onSelectColor, onClose }) => {
  // Renk öğesi
  const renderColorItem = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.colorItem,
        { backgroundColor: item },
        selectedColor === item && styles.selectedColorItem
      ]}
      onPress={() => onSelectColor(item)}
    >
      {selectedColor === item && (
        <MaterialIcons name="check" size={20} color="#fff" />
      )}
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={true}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Renk Seç</Text>
            <TouchableOpacity onPress={onClose}>
              <MaterialIcons name="close" size={24} color="#333" />
            </TouchableOpacity>
          </View>
          
          <FlatList
            data={COLORS}
            renderItem={renderColorItem}
            keyExtractor={(item) => item}
            numColumns={4}
            contentContainerStyle={styles.colorList}
          />
          
          <TouchableOpacity
            style={styles.doneButton}
            onPress={onClose}
          >
            <Text style={styles.doneButtonText}>Tamam</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  colorList: {
    padding: 16,
  },
  colorItem: {
    width: 60,
    height: 60,
    borderRadius: 30,
    margin: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedColorItem: {
    borderWidth: 3,
    borderColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  doneButton: {
    backgroundColor: Colors.PRIMARY,
    marginHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  doneButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default ColorSelector;
