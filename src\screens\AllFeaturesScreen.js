import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import { useAppContext } from '../context/AppContext';

const { width } = Dimensions.get('window');
const ITEM_WIDTH = (width - 48) / 2;

/**
 * Tüm özelliklere erişim sağlayan ekran
 *
 * @returns {JSX.Element} Tüm özellikler ekranı
 */
export default function AllFeaturesScreen({ navigation }) {
  const insets = useSafeAreaInsets();
  const router = useRouter();
  const { theme } = useAppContext();

  // Tüm özellikler
  const features = [
    { id: 'home', title: '<PERSON> Sayfa', icon: 'home', route: '/(tabs)/home' },
    { id: 'transactions', title: '<PERSON><PERSON>lem<PERSON>', icon: 'receipt-long', route: '/(tabs)/transactions' },
    { id: 'investment', title: 'Yatırımlar', icon: 'account-balance', route: '/investment' },
    { id: 'salary', title: 'Maaş Takibi', icon: 'payments', route: 'Salaries' },
    { id: 'work', title: 'Mesai Takibi', icon: 'access-time', route: 'Overtime' },
    { id: 'stats', title: 'İstatistikler', icon: 'bar-chart', route: '/(tabs)/stats' },
    { id: 'settings', title: 'Ayarlar', icon: 'settings', route: '/(tabs)/settings' },
    { id: 'budget', title: 'Bütçe Yönetimi', icon: 'account-balance-wallet', route: 'Budgets' },
    { id: 'goals', title: 'Birikim Hedefleri', icon: 'flag', route: '/goals' },
    { id: 'currency', title: 'Döviz Çevirici', icon: 'currency-exchange', route: 'Currency' },
    { id: 'reminders', title: 'Hatırlatıcılar', icon: 'alarm', route: '/reminders' },
    { id: 'notifications', title: 'Bildirimler', icon: 'notifications', route: '/notifications' },
    { id: 'reports', title: 'Raporlar', icon: 'assessment', route: '/reports' },
    { id: 'calendar', title: 'Finansal Takvim', icon: 'event', route: '/calendar' },
    { id: 'shopping', title: 'Alışveriş Listesi', icon: 'shopping-cart', route: '/shopping' },
    { id: 'debt', title: 'Borç Takibi', icon: 'credit-card', route: '/debt' },
    { id: 'education', title: 'Finansal Eğitim', icon: 'school', route: '/education' },
    { id: 'backup', title: 'Yedekleme', icon: 'backup', route: '/backup' }
  ];

  // Özelliğe git
  const navigateToFeature = (route) => {
    if (route.startsWith('/')) {
      router.push(route);
    } else {
      navigation.navigate(route);
    }
  };

  // Özellik kartı
  const renderFeatureItem = (feature) => {
    return (
      <TouchableOpacity
        key={feature.id}
        style={[styles.featureItem, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}
        onPress={() => navigateToFeature(feature.route)}
      >
        <View style={[styles.featureIcon, { backgroundColor: theme.colors.backgroundSecondary }]}>
          <MaterialIcons name={feature.icon} size={32} color={theme.colors.primary} />
        </View>
        <Text style={[styles.featureTitle, { color: theme.colors.text }]}>{feature.title}</Text>
      </TouchableOpacity>
    );
  };

  return (
    <View style={[styles.container, { paddingTop: insets.top, backgroundColor: theme.colors.background }]}>
      <View style={[styles.header, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>Tüm Özellikler</Text>
      </View>

      <ScrollView style={styles.scrollView} contentContainerStyle={styles.content}>
        <View style={styles.featuresGrid}>
          {features.map(renderFeatureItem)}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  featuresGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  featureItem: {
    width: ITEM_WIDTH,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  featureIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});
