import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  Modal, 
  TouchableOpacity, 
  StyleSheet,
  ScrollView,
  TouchableWithoutFeedback
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../../../constants/colors';
import { shiftStyles } from '../styles/shiftStyles';

/**
 * Vardiya Filtre Modalı Bileşeni
 * 
 * Bu bileşen, vardiya listesini filtrelemek için kullanılan bir modal sunar.
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {boolean} props.visible - Modalın görünür olup olmadığı
 * @param {Function} props.onClose - Modal kapatıldığında çalışacak fonksiyon
 * @param {Function} props.onApply - Filtreler uygulandığında çalışacak fonksiyon
 * @param {Object} props.filters - Mevcut filtreler
 * @param {Array} props.shiftTypes - Vardiya türleri listesi
 * @returns {JSX.Element} Vardiya filtre modalı
 */
const ShiftFilterModal = ({ visible, onClose, onApply, filters, shiftTypes }) => {
  // Filtre durumları
  const [selectedShiftType, setSelectedShiftType] = useState(null);
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [isOvertime, setIsOvertime] = useState(false);
  const [isHoliday, setIsHoliday] = useState(false);
  
  // Durum seçenekleri
  const statusOptions = [
    { id: 'all', label: 'Tümü' },
    { id: 'active', label: 'Aktif' },
    { id: 'completed', label: 'Tamamlandı' },
    { id: 'planned', label: 'Planlandı' },
    { id: 'cancelled', label: 'İptal' }
  ];
  
  // Mevcut filtreleri yükle
  useEffect(() => {
    if (filters) {
      setSelectedShiftType(filters.shiftTypeId);
      setSelectedStatus(filters.status || 'all');
      setIsOvertime(filters.isOvertime || false);
      setIsHoliday(filters.isHoliday || false);
    }
  }, [filters, visible]);
  
  // Filtreleri uygula
  const handleApply = () => {
    onApply({
      shiftTypeId: selectedShiftType,
      status: selectedStatus !== 'all' ? selectedStatus : null,
      isOvertime,
      isHoliday
    });
    onClose();
  };
  
  // Filtreleri sıfırla
  const handleReset = () => {
    setSelectedShiftType(null);
    setSelectedStatus('all');
    setIsOvertime(false);
    setIsHoliday(false);
  };
  
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={shiftStyles.modalOverlay}>
          <TouchableWithoutFeedback>
            <View style={shiftStyles.modalContent}>
              <Text style={shiftStyles.modalTitle}>Vardiyaları Filtrele</Text>
              
              <ScrollView>
                {/* Vardiya Türü Filtresi */}
                <View style={styles.filterSection}>
                  <Text style={styles.filterTitle}>Vardiya Türü</Text>
                  
                  <View style={styles.optionsContainer}>
                    <TouchableOpacity
                      style={[
                        styles.shiftTypeOption,
                        selectedShiftType === null && styles.selectedOption
                      ]}
                      onPress={() => setSelectedShiftType(null)}
                    >
                      <Text style={styles.optionText}>Tümü</Text>
                    </TouchableOpacity>
                    
                    {shiftTypes.map(type => (
                      <TouchableOpacity
                        key={type.id}
                        style={[
                          styles.shiftTypeOption,
                          selectedShiftType === type.id && styles.selectedOption,
                          { borderColor: type.color }
                        ]}
                        onPress={() => setSelectedShiftType(type.id)}
                      >
                        <View style={[styles.colorDot, { backgroundColor: type.color }]} />
                        <Text style={styles.optionText}>{type.name}</Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>
                
                {/* Durum Filtresi */}
                <View style={styles.filterSection}>
                  <Text style={styles.filterTitle}>Durum</Text>
                  
                  <View style={styles.optionsContainer}>
                    {statusOptions.map(option => (
                      <TouchableOpacity
                        key={option.id}
                        style={[
                          styles.statusOption,
                          selectedStatus === option.id && styles.selectedOption
                        ]}
                        onPress={() => setSelectedStatus(option.id)}
                      >
                        <Text style={styles.optionText}>{option.label}</Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>
                
                {/* Ek Filtreler */}
                <View style={styles.filterSection}>
                  <Text style={styles.filterTitle}>Ek Filtreler</Text>
                  
                  <View style={styles.optionsContainer}>
                    <TouchableOpacity
                      style={[
                        styles.extraOption,
                        isOvertime && styles.selectedExtraOption
                      ]}
                      onPress={() => setIsOvertime(!isOvertime)}
                    >
                      <MaterialIcons 
                        name={isOvertime ? "check-box" : "check-box-outline-blank"} 
                        size={20} 
                        color={isOvertime ? Colors.PRIMARY : "#666"} 
                      />
                      <Text style={styles.extraOptionText}>Mesai Vardiyaları</Text>
                    </TouchableOpacity>
                    
                    <TouchableOpacity
                      style={[
                        styles.extraOption,
                        isHoliday && styles.selectedExtraOption
                      ]}
                      onPress={() => setIsHoliday(!isHoliday)}
                    >
                      <MaterialIcons 
                        name={isHoliday ? "check-box" : "check-box-outline-blank"} 
                        size={20} 
                        color={isHoliday ? Colors.PRIMARY : "#666"} 
                      />
                      <Text style={styles.extraOptionText}>Tatil Günü Vardiyaları</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </ScrollView>
              
              {/* Butonlar */}
              <View style={styles.buttonContainer}>
                <TouchableOpacity
                  style={[styles.button, styles.resetButton]}
                  onPress={handleReset}
                >
                  <MaterialIcons name="refresh" size={20} color="#666" />
                  <Text style={styles.resetButtonText}>Sıfırla</Text>
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={[styles.button, styles.applyButton]}
                  onPress={handleApply}
                >
                  <MaterialIcons name="check" size={20} color="#fff" />
                  <Text style={styles.applyButtonText}>Uygula</Text>
                </TouchableOpacity>
              </View>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  filterSection: {
    marginBottom: 20,
  },
  filterTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  shiftTypeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
    marginBottom: 8,
  },
  statusOption: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
    marginBottom: 8,
  },
  selectedOption: {
    backgroundColor: 'rgba(0, 120, 255, 0.1)',
    borderWidth: 2,
    borderColor: Colors.PRIMARY,
  },
  colorDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 8,
  },
  optionText: {
    fontSize: 14,
    color: '#333',
  },
  extraOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    marginBottom: 8,
    width: '100%',
  },
  selectedExtraOption: {
    backgroundColor: 'rgba(0, 120, 255, 0.05)',
    borderRadius: 8,
    paddingHorizontal: 8,
  },
  extraOptionText: {
    fontSize: 14,
    color: '#333',
    marginLeft: 8,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
    paddingTop: 16,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 12,
    flex: 1,
  },
  resetButton: {
    backgroundColor: '#f0f0f0',
    marginRight: 8,
  },
  applyButton: {
    backgroundColor: Colors.PRIMARY,
    marginLeft: 8,
  },
  resetButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#666',
    marginLeft: 8,
  },
  applyButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
    marginLeft: 8,
  },
});

export default ShiftFilterModal;
