import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

/**
 * Image Widget - Resim gösterimi için widget
 */
const ImageWidget = ({ widget, isPreviewMode, onUpdate, theme }) => {
  /**
   * G<PERSON><PERSON>li tema değeri alma
   * @param {string} property - <PERSON><PERSON>
   * @param {string} fallback - <PERSON><PERSON><PERSON><PERSON><PERSON>
   * @returns {string} G<PERSON><PERSON>li tema değeri
   */
  const getSafeThemeValue = (property, fallback) => {
    return theme[property] || theme.colors?.[property.toLowerCase()] || fallback;
  };

  return (
    <View style={[styles.container, { backgroundColor: getSafeThemeValue('BACKGROUND', '#fff') }]}>
      <View style={[styles.imageArea, { borderColor: getSafeThemeValue('BORDER', '#e0e0e0') }]}>
        <Text style={[styles.imageIcon, { color: getSafeThemeValue('SECONDARY', '#6c757d') }]}>
          🖼️
        </Text>
        <Text style={[styles.imageLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666') }]}>
          Image Placeholder
        </Text>
        <Text style={[styles.imageSubLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666') }]}>
          {widget.config?.alt || 'Resim'}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 4,
  },
  imageArea: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderStyle: 'dashed',
    borderRadius: 4,
  },
  imageIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  imageLabel: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 4,
  },
  imageSubLabel: {
    fontSize: 10,
  },
});

export default ImageWidget;
