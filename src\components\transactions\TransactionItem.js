import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';

/**
 * İşlem öğesi bileşeni
 *
 * @param {Object} props - Bileşen props'ları
 * @param {Object} props.transaction - İşlem verisi
 * @param {Function} props.onPress - Öğeye tıklandığında çağrılacak fonksiyon
 * @param {Function} props.onDelete - Silme butonuna tıklandığında çağrılacak fonksiyon
 * @returns {JSX.Element} İşlem öğesi bileşeni
 */
const TransactionItem = ({ transaction, onPress, onDelete }) => {
  // Para birimini formatla
  const formatCurrency = (amount, currency = 'TRY') => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(amount);
  };

  // Tarihi formatla
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return format(date, 'd MMMM yyyy', { locale: tr });
  };

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={onPress}
      activeOpacity={0.7}
    >
      {/* Kategori İkonu */}
      <View
        style={[
          styles.iconContainer,
          { backgroundColor: transaction.category_color ? `${transaction.category_color}20` : '#f0f0f0' }
        ]}
      >
        <MaterialIcons
          name={transaction.category_icon || 'category'}
          size={24}
          color={transaction.category_color || '#757575'}
        />
      </View>

      {/* İşlem Bilgileri */}
      <View style={styles.details}>
        <View style={styles.titleRow}>
          <Text style={styles.description} numberOfLines={1}>
            {transaction.description || (transaction.category_name || 'İşlem')}
          </Text>
          <View>
            <Text style={[
              styles.amount,
              { color: transaction.is_income ? '#2ecc71' : '#e74c3c' }
            ]}>
              {transaction.is_income ? '+' : '-'}{formatCurrency(transaction.amount, transaction.currency || 'TRY')}
            </Text>


          </View>
        </View>

        <View style={styles.infoRow}>
          <View style={styles.categoryContainer}>
            {transaction.category_name && (
              <>
                <MaterialIcons name="label" size={12} color="#757575" />
                <Text style={styles.category}>{transaction.category_name}</Text>
              </>
            )}
          </View>
          <Text style={styles.date}>{formatDate(transaction.transaction_date)}</Text>
        </View>
      </View>

      {/* Silme Butonu */}
      {onDelete && (
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={(e) => {
            e.stopPropagation();
            onDelete();
          }}
          hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
        >
          <MaterialIcons name="delete-outline" size={20} color="#e74c3c" />
        </TouchableOpacity>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  details: {
    flex: 1,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  description: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    flex: 1,
    marginRight: 8,
  },
  amount: {
    fontSize: 16,
    fontWeight: '600',
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  categoryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  category: {
    fontSize: 12,
    color: '#757575',
    marginLeft: 4,
  },
  date: {
    fontSize: 12,
    color: '#757575',
  },
  deleteButton: {
    marginLeft: 8,
    padding: 4,
  },

});

export default TransactionItem;
