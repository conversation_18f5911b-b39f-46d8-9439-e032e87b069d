/**
 * <PERSON><PERSON>tçe Yönetimi Veritabanı Şeması
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md'ye göre ta<PERSON>ır
 */

/**
 * Ana bütçe tablosu - Gelişmiş bütçe yönetimi için
 * Mevcut basit budgets tablosunu genişletir
 */
export const BUDGETS_ENHANCED_TABLE = `
CREATE TABLE IF NOT EXISTS budgets_enhanced (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  description TEXT,
  type TEXT NOT NULL CHECK(type IN ('total', 'category_based', 'flexible')),
  period_type TEXT NOT NULL CHECK(period_type IN ('weekly', 'monthly', 'custom')),
  start_date DATE NOT NULL,
  end_date DATE,
  total_limit DECIMAL(10,2) NOT NULL DEFAULT 0,
  currency TEXT NOT NULL DEFAULT 'TRY',
  status TEXT NOT NULL DEFAULT 'active' CHECK(status IN ('active', 'paused', 'completed', 'cancelled')),
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
)
`;

/**
 * Bütçe kategori limitleri tablosu
 * Her bütçe için kategori bazlı limitler
 */
export const BUDGET_CATEGORIES_ENHANCED_TABLE = `
CREATE TABLE IF NOT EXISTS budget_categories_enhanced (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  budget_id INTEGER NOT NULL,
  category_id INTEGER NOT NULL,
  limit_amount DECIMAL(10,2) NOT NULL,
  spent_amount DECIMAL(10,2) DEFAULT 0,
  currency TEXT NOT NULL DEFAULT 'TRY',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (budget_id) REFERENCES budgets_enhanced (id) ON DELETE CASCADE,
  FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE CASCADE,
  UNIQUE(budget_id, category_id)
)
`;

/**
 * Bütçe geçmişi ve performans tablosu
 * Dönemsel bütçe performans analizi için
 */
export const BUDGET_HISTORY_TABLE = `
CREATE TABLE IF NOT EXISTS budget_history (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  budget_id INTEGER NOT NULL,
  period_start DATE NOT NULL,
  period_end DATE NOT NULL,
  total_spent DECIMAL(10,2) DEFAULT 0,
  total_limit DECIMAL(10,2) NOT NULL,
  success_rate DECIMAL(5,2) DEFAULT 0,
  categories_data TEXT, -- JSON format for category breakdown
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (budget_id) REFERENCES budgets_enhanced (id) ON DELETE CASCADE
)
`;

/**
 * Bütçe uyarı ayarları tablosu
 * Kullanıcı tanımlı uyarı eşikleri
 */
export const BUDGET_ALERT_SETTINGS_TABLE = `
CREATE TABLE IF NOT EXISTS budget_alert_settings (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  budget_id INTEGER NOT NULL,
  threshold_75 INTEGER DEFAULT 1,
  threshold_90 INTEGER DEFAULT 1,
  threshold_100 INTEGER DEFAULT 1,
  daily_limit_exceeded INTEGER DEFAULT 1,
  category_limit_exceeded INTEGER DEFAULT 1,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (budget_id) REFERENCES budgets_enhanced (id) ON DELETE CASCADE,
  UNIQUE(budget_id)
)
`;

/**
 * Bütçe şablonları tablosu
 * Hazır bütçe şablonları için
 */
export const BUDGET_TEMPLATES_TABLE = `
CREATE TABLE IF NOT EXISTS budget_templates (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  description TEXT,
  type TEXT NOT NULL CHECK(type IN ('total', 'category_based', 'flexible')),
  period_type TEXT NOT NULL CHECK(period_type IN ('weekly', 'monthly', 'custom')),
  template_data TEXT NOT NULL, -- JSON format for template structure
  is_system INTEGER DEFAULT 0, -- System templates vs user templates
  is_active INTEGER DEFAULT 1,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
)
`;

/**
 * Optimized indexes for budget queries
 * Performance için gerekli indexler
 */
export const BUDGET_INDEXES = [
  'CREATE INDEX IF NOT EXISTS idx_budgets_enhanced_active ON budgets_enhanced(status, period_type, start_date, end_date)',
  'CREATE INDEX IF NOT EXISTS idx_budget_categories_enhanced_budget ON budget_categories_enhanced(budget_id, category_id)',
  'CREATE INDEX IF NOT EXISTS idx_budget_history_period ON budget_history(budget_id, period_start, period_end)',
  'CREATE INDEX IF NOT EXISTS idx_budget_alert_settings_budget ON budget_alert_settings(budget_id)',
  'CREATE INDEX IF NOT EXISTS idx_budget_templates_active ON budget_templates(is_active, type, period_type)',
  'CREATE INDEX IF NOT EXISTS idx_transactions_budget_calc ON transactions(category_id, date, amount, type)'
];

/**
 * Tüm bütçe tablolarını içeren dizi
 */
export const ALL_BUDGET_TABLES = [
  BUDGETS_ENHANCED_TABLE,
  BUDGET_CATEGORIES_ENHANCED_TABLE,
  BUDGET_HISTORY_TABLE,
  BUDGET_ALERT_SETTINGS_TABLE,
  BUDGET_TEMPLATES_TABLE
];

/**
 * Sistem bütçe şablonları
 * Varsayılan şablonlar
 */
export const SYSTEM_BUDGET_TEMPLATES = [
  {
    name: 'Temel İhtiyaçlar',
    description: 'Temel yaşam giderleri için bütçe şablonu',
    type: 'category_based',
    period_type: 'monthly',
    template_data: JSON.stringify({
      categories: [
        { name: 'Market', percentage: 40 },
        { name: 'Ulaşım', percentage: 15 },
        { name: 'Faturalar', percentage: 25 },
        { name: 'Diğer', percentage: 20 }
      ]
    }),
    is_system: 1
  },
  {
    name: 'Öğrenci Bütçesi',
    description: 'Öğrenci yaşamı için optimize edilmiş bütçe',
    type: 'category_based',
    period_type: 'monthly',
    template_data: JSON.stringify({
      categories: [
        { name: 'Yemek', percentage: 35 },
        { name: 'Ulaşım', percentage: 20 },
        { name: 'Kitap/Kırtasiye', percentage: 15 },
        { name: 'Sosyal', percentage: 20 },
        { name: 'Diğer', percentage: 10 }
      ]
    }),
    is_system: 1
  },
  {
    name: 'Aile Bütçesi',
    description: 'Aile giderleri için kapsamlı bütçe şablonu',
    type: 'category_based',
    period_type: 'monthly',
    template_data: JSON.stringify({
      categories: [
        { name: 'Market', percentage: 30 },
        { name: 'Faturalar', percentage: 25 },
        { name: 'Ulaşım', percentage: 15 },
        { name: 'Çocuk', percentage: 15 },
        { name: 'Sağlık', percentage: 10 },
        { name: 'Diğer', percentage: 5 }
      ]
    }),
    is_system: 1
  }
];

/**
 * Migration helper functions
 */
export const createBudgetTables = async (db) => {
  try {
    console.log('Gelişmiş bütçe tabloları oluşturuluyor...');
    
    // Create all budget tables
    for (const tableSQL of ALL_BUDGET_TABLES) {
      await db.execAsync(tableSQL);
    }
    
    // Create indexes
    for (const indexSQL of BUDGET_INDEXES) {
      await db.execAsync(indexSQL);
    }
    
    console.log('Gelişmiş bütçe tabloları başarıyla oluşturuldu');
  } catch (error) {
    console.error('Bütçe tabloları oluşturma hatası:', error);
    throw error;
  }
};

/**
 * Insert system budget templates
 */
export const insertSystemBudgetTemplates = async (db) => {
  try {
    console.log('Sistem bütçe şablonları ekleniyor...');
    
    for (const template of SYSTEM_BUDGET_TEMPLATES) {
      await db.runAsync(`
        INSERT OR IGNORE INTO budget_templates 
        (name, description, type, period_type, template_data, is_system, is_active)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [
        template.name,
        template.description,
        template.type,
        template.period_type,
        template.template_data,
        template.is_system,
        1
      ]);
    }
    
    console.log('Sistem bütçe şablonları başarıyla eklendi');
  } catch (error) {
    console.error('Sistem şablonları ekleme hatası:', error);
    throw error;
  }
};
