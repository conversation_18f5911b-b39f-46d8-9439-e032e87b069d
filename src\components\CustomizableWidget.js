import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal, ScrollView } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import { useAppContext } from '../context/AppContext';

// Widget tipleri
const WIDGET_TYPES = [
  {
    id: 'monthly_summary',
    title: 'Aylık Özet',
    icon: 'pie-chart',
    description: 'Bu ay için gelir ve gider özetini gösterir.'
  },
  {
    id: 'savings_goal',
    title: '<PERSON><PERSON><PERSON>',
    icon: 'savings',
    description: '<PERSON><PERSON>kim hedeflerinizi ve ilerlemenizi gösterir.'
  },
  {
    id: 'budget_status',
    title: 'Bütçe Durumu',
    icon: 'account-balance-wallet',
    description: 'Ayl<PERSON>k bütçe durumunuzu gösterir.'
  },
  {
    id: 'upcoming_bills',
    title: '<PERSON><PERSON><PERSON><PERSON>',
    icon: 'event',
    description: '<PERSON>kla<PERSON><PERSON> ödemelerinizi gösterir.'
  },
  {
    id: 'expense_categories',
    title: 'Harcama Kategorileri',
    icon: 'category',
    description: 'En çok harcama yaptığınız kategorileri gösterir.'
  }
];

/**
 * Özelleştirilebilir widget bileşeni
 *
 * @param {Object} props - Bileşen özellikleri
 * @returns {JSX.Element} Özelleştirilebilir widget bileşeni
 */
export default function CustomizableWidget() {
  const { theme } = useAppContext();
  const [widgetType, setWidgetType] = useState('monthly_summary');
  const [isModalVisible, setIsModalVisible] = useState(false);

  /**
   * Widget içeriğini render eder
   *
   * @returns {JSX.Element} Widget içeriği
   */
  const renderWidgetContent = () => {
    switch (widgetType) {
      case 'monthly_summary':
        return (
          <View style={styles.widgetContent}>
            <Text style={[styles.widgetTitle, { color: theme.TEXT_PRIMARY }]}>Aylık Özet</Text>
            <View style={styles.summaryContainer}>
              <View style={styles.summaryItem}>
                <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>Gelir</Text>
                <Text style={[styles.summaryValue, { color: Colors.SUCCESS }]}>₺3,250.00</Text>
              </View>
              <View style={styles.summaryItem}>
                <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>Gider</Text>
                <Text style={[styles.summaryValue, { color: Colors.DANGER }]}>₺2,180.50</Text>
              </View>
              <View style={styles.summaryItem}>
                <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>Kalan</Text>
                <Text style={[styles.summaryValue, { color: theme.PRIMARY }]}>₺1,069.50</Text>
              </View>
            </View>
          </View>
        );

      case 'savings_goal':
        return (
          <View style={styles.widgetContent}>
            <Text style={[styles.widgetTitle, { color: theme.TEXT_PRIMARY }]}>Birikim Hedefi</Text>
            <Text style={[styles.goalTitle, { color: theme.TEXT_PRIMARY }]}>Tatil Fonu</Text>
            <View style={styles.progressContainer}>
              <View style={[styles.progressBar, { backgroundColor: theme.SURFACE }]}>
                <View style={[styles.progressFill, { width: '45%', backgroundColor: theme.PRIMARY }]} />
              </View>
              <Text style={[styles.progressText, { color: theme.TEXT_SECONDARY }]}>₺4,500 / ₺10,000 (45%)</Text>
            </View>
          </View>
        );

      case 'budget_status':
        return (
          <View style={styles.widgetContent}>
            <Text style={styles.widgetTitle}>Bütçe Durumu</Text>
            <View style={styles.budgetContainer}>
              <View style={styles.budgetItem}>
                <Text style={styles.budgetCategory}>Market</Text>
                <View style={styles.budgetBar}>
                  <View style={[styles.budgetFill, { width: '80%', backgroundColor: Colors.WARNING }]} />
                </View>
                <Text style={styles.budgetText}>₺800 / ₺1,000</Text>
              </View>
              <View style={styles.budgetItem}>
                <Text style={styles.budgetCategory}>Ulaşım</Text>
                <View style={styles.budgetBar}>
                  <View style={[styles.budgetFill, { width: '60%', backgroundColor: Colors.SUCCESS }]} />
                </View>
                <Text style={styles.budgetText}>₺300 / ₺500</Text>
              </View>
              <View style={styles.budgetItem}>
                <Text style={styles.budgetCategory}>Eğlence</Text>
                <View style={styles.budgetBar}>
                  <View style={[styles.budgetFill, { width: '95%', backgroundColor: Colors.DANGER }]} />
                </View>
                <Text style={styles.budgetText}>₺475 / ₺500</Text>
              </View>
            </View>
          </View>
        );

      case 'upcoming_bills':
        return (
          <View style={styles.widgetContent}>
            <Text style={styles.widgetTitle}>Yaklaşan Ödemeler</Text>
            <View style={styles.billsContainer}>
              <View style={styles.billItem}>
                <MaterialIcons name="home" size={20} color={Colors.PRIMARY} />
                <View style={styles.billInfo}>
                  <Text style={styles.billTitle}>Kira</Text>
                  <Text style={styles.billDate}>15 Kasım 2023</Text>
                </View>
                <Text style={styles.billAmount}>₺2,500</Text>
              </View>
              <View style={styles.billItem}>
                <MaterialIcons name="power" size={20} color={Colors.PRIMARY} />
                <View style={styles.billInfo}>
                  <Text style={styles.billTitle}>Elektrik</Text>
                  <Text style={styles.billDate}>20 Kasım 2023</Text>
                </View>
                <Text style={styles.billAmount}>₺350</Text>
              </View>
            </View>
          </View>
        );

      case 'expense_categories':
        return (
          <View style={styles.widgetContent}>
            <Text style={styles.widgetTitle}>Harcama Kategorileri</Text>
            <View style={styles.categoriesContainer}>
              <View style={styles.categoryItem}>
                <View style={[styles.categoryColor, { backgroundColor: '#FF6B6B' }]} />
                <Text style={styles.categoryName}>Market</Text>
                <Text style={styles.categoryPercentage}>35%</Text>
              </View>
              <View style={styles.categoryItem}>
                <View style={[styles.categoryColor, { backgroundColor: '#4ECDC4' }]} />
                <Text style={styles.categoryName}>Ulaşım</Text>
                <Text style={styles.categoryPercentage}>25%</Text>
              </View>
              <View style={styles.categoryItem}>
                <View style={[styles.categoryColor, { backgroundColor: '#FFD166' }]} />
                <Text style={styles.categoryName}>Eğlence</Text>
                <Text style={styles.categoryPercentage}>20%</Text>
              </View>
              <View style={styles.categoryItem}>
                <View style={[styles.categoryColor, { backgroundColor: '#6B5B95' }]} />
                <Text style={styles.categoryName}>Diğer</Text>
                <Text style={styles.categoryPercentage}>20%</Text>
              </View>
            </View>
          </View>
        );

      default:
        return (
          <View style={styles.widgetContent}>
            <Text style={[styles.widgetTitle, { color: theme.TEXT_PRIMARY }]}>Widget Seçin</Text>
            <Text style={[styles.widgetDescription, { color: theme.TEXT_SECONDARY }]}>
              Özelleştirmek için sağ üstteki düğmeye tıklayın.
            </Text>
          </View>
        );
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.CARD }]}>
      {renderWidgetContent()}

      <TouchableOpacity
        style={[styles.customizeButton, { backgroundColor: theme.SURFACE }]}
        onPress={() => setIsModalVisible(true)}
      >
        <MaterialIcons name="settings" size={20} color={theme.TEXT_SECONDARY} />
      </TouchableOpacity>

      <Modal
        visible={isModalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setIsModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={[styles.modalContent, { backgroundColor: theme.CARD }]}>
            <View style={[styles.modalHeader, { borderBottomColor: theme.BORDER }]}>
              <Text style={[styles.modalTitle, { color: theme.TEXT_PRIMARY }]}>Widget Seçin</Text>
              <TouchableOpacity onPress={() => setIsModalVisible(false)}>
                <MaterialIcons name="close" size={24} color={theme.TEXT_SECONDARY} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.widgetList}>
              {WIDGET_TYPES.map(widget => (
                <TouchableOpacity
                  key={widget.id}
                  style={[
                    styles.widgetOption,
                    { borderBottomColor: theme.BORDER },
                    widgetType === widget.id && { backgroundColor: `${theme.PRIMARY}20` }
                  ]}
                  onPress={() => {
                    setWidgetType(widget.id);
                    setIsModalVisible(false);
                  }}
                >
                  <MaterialIcons
                    name={widget.icon}
                    size={24}
                    color={widgetType === widget.id ? theme.PRIMARY : theme.TEXT_SECONDARY}
                  />
                  <View style={styles.widgetOptionInfo}>
                    <Text style={[
                      styles.widgetOptionTitle,
                      { color: widgetType === widget.id ? theme.PRIMARY : theme.TEXT_PRIMARY }
                    ]}>
                      {widget.title}
                    </Text>
                    <Text style={[styles.widgetOptionDescription, { color: theme.TEXT_SECONDARY }]}>
                      {widget.description}
                    </Text>
                  </View>
                  {widgetType === widget.id && (
                    <MaterialIcons name="check" size={24} color={theme.PRIMARY} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  widgetContent: {
    minHeight: 100,
  },
  widgetTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 12,
  },
  widgetDescription: {
    fontSize: 14,
    color: '#333333',
  },
  customizeButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
  },
  widgetList: {
    padding: 16,
  },
  widgetOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  selectedWidgetOption: {
    backgroundColor: 'rgba(46, 204, 113, 0.1)',
  },
  widgetOptionInfo: {
    flex: 1,
    marginLeft: 12,
  },
  widgetOptionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333333',
    marginBottom: 4,
  },
  widgetOptionDescription: {
    fontSize: 14,
    color:'#333333' ,
  },
  // Aylık Özet stilleri
  summaryContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  summaryItem: {
    flex: 1,
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 14,
    color: '#333333',
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  // Birikim Hedefi stilleri
  goalTitle: {
    fontSize: 16,
    color: '#333333',
    marginBottom: 8,
  },
  progressContainer: {
    marginTop: 8,
  },
  progressBar: {
    height: 12,
    backgroundColor: '#f5f5f5',
    borderRadius: 6,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: Colors.PRIMARY,
    borderRadius: 6,
  },
  progressText: {
    fontSize: 14,
    color: '#333333',
    textAlign: 'center',
  },
  // Bütçe Durumu stilleri
  budgetContainer: {
    marginTop: 8,
  },
  budgetItem: {
    marginBottom: 12,
  },
  budgetCategory: {
    fontSize: 14,
    color: '#333333',
    marginBottom: 4,
  },
  budgetBar: {
    height: 8,
    backgroundColor: '#f5f5f5',
    borderRadius: 4,
    marginBottom: 4,
  },
  budgetFill: {
    height: '100%',
    borderRadius: 4,
  },
  budgetText: {
    fontSize: 12,
    color: '#333333',
  },
  // Yaklaşan Ödemeler stilleri
  billsContainer: {
    marginTop: 8,
  },
  billItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  billInfo: {
    flex: 1,
    marginLeft: 12,
  },
  billTitle: {
    fontSize: 14,
    color: '#333333',
  },
  billDate: {
    fontSize: 12,
    color: '#333333',
  },
  billAmount: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333333',
  },
  // Harcama Kategorileri stilleri
  categoriesContainer: {
    marginTop: 8,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryColor: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 8,
  },
  categoryName: {
    flex: 1,
    fontSize: 14,
    color: '#333333',
  },
  categoryPercentage: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333333',
  },
});
