// ...existing constants...

/**
 * Yat<PERSON><PERSON><PERSON>m tipine göre ikon döndürür
 * @param {string} type - Yatırım tipi
 * @returns {string} MaterialIcons ikon adı
 */
export const getInvestmentTypeIcon = (type) => {
  const icons = {
    'stock': 'trending-up',
    'gold': 'monetization-on',
    'forex': 'attach-money',
    'crypto': 'currency-bitcoin',
    'bond': 'description',
    'realestate': 'home',
    'fund': 'account-balance',
  };
  return icons[type] || 'help-outline';
};
