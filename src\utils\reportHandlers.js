import { Alert } from 'react-native';
import { ExportManager, PDFGenerator, ExcelExporter, EmailExporter } from '../components/reports/Export';

/**
 * Rapor Event Handler'ları
 * Rapor ekranı için olay işleyicileri
 */

/**
 * Rapor dışa aktarma işleyicisi
 * @param {string} format - Dışa aktarma formatı (pdf, excel, csv, email)
 * @param {Object} data - Rapor verisi
 * @param {string} title - <PERSON>or başlığı
 * @param {string} type - Rapor türü
 */
export const handleExportReport = async (format, data, title = 'Finansal Rapor', type = 'summary') => {
  try {
    let result;
    
    switch (format.toLowerCase()) {
      case 'pdf':
        result = await PDFGenerator.generatePDF({
          data,
          title,
          type,
          config: { format: 'A4', orientation: 'portrait' }
        });
        break;
        
      case 'excel':
      case 'csv':
        result = await ExcelExporter.exportToExcel({
          data,
          title,
          type,
          config: { format: 'csv', includeHeaders: true }
        });
        break;
        
      case 'email':
        result = await EmailExporter.sendReport({
          data,
          title,
          type,
          config: { 
            subject: `${title} - Finansal Rapor`,
            includeAttachments: true,
            attachmentFormats: ['pdf', 'excel']
          }
        });
        break;
        
      default:
        throw new Error(`Desteklenmeyen format: ${format}`);
    }
    
    if (result.success) {
      Alert.alert(
        'Başarılı', 
        `Rapor ${format.toUpperCase()} formatında başarıyla ${format === 'email' ? 'gönderildi' : 'dışa aktarıldı'}.`
      );
    } else {
      throw new Error(result.error || 'Bilinmeyen hata');
    }
    
  } catch (error) {
    console.error('Export Error:', error);
    Alert.alert(
      'Hata',
      `Rapor dışa aktarılırken hata oluştu: ${error.message}`
    );
  }
};

/**
 * Rapor kaydetme işleyicisi
 * @param {Object} data - Rapor verisi
 */
export const handleSaveReport = (data) => {
  Alert.alert(
    'Rapor Kaydetme',
    'Rapor kütüphanese kaydedilsin mi?',
    [
      { text: 'İptal', style: 'cancel' },
      { 
        text: 'Kaydet', 
        onPress: () => {
          // TODO: Gerçek kaydetme implementasyonu
          console.log('Rapor kaydediliyor:', data);
          Alert.alert('Başarılı', 'Rapor başarıyla kaydedildi.');
        }
      },
    ]
  );
};

/**
 * Rapor kütüphanesini açma işleyicisi
 */
export const handleOpenLibrary = () => {
  Alert.alert(
    'Rapor Kütüphanesi',
    'Kaydedilen raporlar görüntülensin mi?',
    [
      { text: 'İptal', style: 'cancel' },
      { 
        text: 'Aç', 
        onPress: () => {
          // TODO: Rapor kütüphanesi sayfasına yönlendir
          console.log('Rapor kütüphanesi açılıyor');
        }
      },
    ]
  );
};

/**
 * Rapor detayını açma işleyicisi
 * @param {Object} report - Rapor objesi
 * @param {Function} setActiveReport - Aktif rapor setter fonksiyonu
 */
export const handleOpenReport = (report, setActiveReport) => {
  // Report tipini template tipine map et
  const reportTypeMapping = {
    'financial': 'monthly_income_expense',
    'category': 'category_distribution',
    'income': 'shift_income',
    'expense': 'summary_overview'
  };

  const templateType = reportTypeMapping[report.type] || 'summary_overview';
  
  // Mock template config oluştur
  const templateConfig = {
    id: templateType,
    name: report.name,
    description: `${report.name} raporu görüntüleniyor`,
    defaultParams: {}
  };

  // Aktif rapor olarak ayarla
  setActiveReport({
    template: templateConfig,
    config: { id: templateType, reportId: report.id }
  });
};

/**
 * Hızlı şablon seçimi işleyicisi
 * @param {Object} template - Seçilen şablon
 * @param {Object} navigation - Navigation objesi
 * @param {Function} setActiveReport - Aktif rapor setter
 */
export const handleTemplateSelect = (template, navigation, setActiveReport) => {
  // Özel rapor editörü ve interaktif tablo için navigation
  if (template.id === 'custom-report-builder') {
    navigation.navigate('ReportBuilder');
    return;
  }
  
  if (template.id === 'interactive-table') {
    navigation.navigate('InteractiveTableBuilder');
    return;
  }
  
  // Diğer şablonlar için mevcut logic
  const templateConfig = {
    id: template.id,
    name: template.name,
    description: template.description,
    defaultParams: {}
  };

  setActiveReport({
    template: templateConfig,
    config: { id: template.id }
  });
};
