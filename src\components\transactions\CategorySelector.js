import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  FlatList,
  Modal,
  TextInput
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../../constants/colors';

/**
 * Kategori seçici bileşeni
 * 
 * @param {Object} props - Bileşen props'ları
 * @param {Array} props.categories - Kategoriler listesi
 * @param {number} props.selectedCategoryId - Seçili kategori ID'si
 * @param {Function} props.onSelect - Kategori seçildiğinde çağrılacak fonksiyon
 * @param {Function} props.onClose - Modal kapatıldığında çağrılacak fonksiyon
 * @param {Function} props.onAddCategory - Yeni kategori ekle butonuna tıklandığında çağrılacak fonksiyon
 * @returns {JSX.Element} Kategori seçici bileşeni
 */
const CategorySelector = ({ 
  categories = [], 
  selectedCategoryId, 
  onSelect, 
  onClose,
  onAddCategory
}) => {
  const [searchText, setSearchText] = useState('');
  
  // Arama filtresine göre kategorileri filtrele
  const filteredCategories = searchText
    ? categories.filter(category => 
        category.name.toLowerCase().includes(searchText.toLowerCase())
      )
    : categories;
  
  // Kategori öğesi
  const renderCategoryItem = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.categoryItem,
        selectedCategoryId === item.id && styles.selectedCategoryItem
      ]}
      onPress={() => onSelect(item)}
    >
      <View 
        style={[
          styles.categoryIcon, 
          { backgroundColor: item.color ? `${item.color}20` : '#f0f0f0' }
        ]}
      >
        <MaterialIcons 
          name={item.icon || 'category'} 
          size={24} 
          color={item.color || '#757575'} 
        />
      </View>
      <View style={styles.categoryInfo}>
        <Text style={styles.categoryName}>{item.name}</Text>
      </View>
      {selectedCategoryId === item.id && (
        <MaterialIcons name="check" size={24} color={Colors.PRIMARY} />
      )}
    </TouchableOpacity>
  );
  
  return (
    <Modal
      visible={true}
      animationType="slide"
      transparent={false}
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Başlık */}
        <View style={styles.header}>
          <TouchableOpacity 
            style={styles.closeButton}
            onPress={onClose}
          >
            <MaterialIcons name="close" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.title}>Kategori Seç</Text>
          <TouchableOpacity 
            style={styles.addButton}
            onPress={onAddCategory}
          >
            <MaterialIcons name="add" size={24} color={Colors.PRIMARY} />
          </TouchableOpacity>
        </View>
        
        {/* Arama */}
        <View style={styles.searchContainer}>
          <MaterialIcons name="search" size={20} color="#757575" />
          <TextInput
            style={styles.searchInput}
            value={searchText}
            onChangeText={setSearchText}
            placeholder="Kategori ara..."
            placeholderTextColor="#999"
          />
          {searchText ? (
            <TouchableOpacity 
              style={styles.clearButton}
              onPress={() => setSearchText('')}
            >
              <MaterialIcons name="clear" size={20} color="#757575" />
            </TouchableOpacity>
          ) : null}
        </View>
        
        {/* Kategori Listesi */}
        <FlatList
          data={filteredCategories}
          renderItem={renderCategoryItem}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.categoryList}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <MaterialIcons name="category" size={48} color="#ddd" />
              <Text style={styles.emptyText}>
                {searchText 
                  ? 'Arama sonucu bulunamadı' 
                  : 'Henüz kategori bulunmuyor'}
              </Text>
              <TouchableOpacity 
                style={styles.emptyButton}
                onPress={onAddCategory}
              >
                <Text style={styles.emptyButtonText}>Kategori Ekle</Text>
              </TouchableOpacity>
            </View>
          }
        />
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  closeButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  addButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    marginLeft: 8,
    color: '#333',
  },
  clearButton: {
    padding: 4,
  },
  categoryList: {
    padding: 16,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  selectedCategoryItem: {
    borderWidth: 1,
    borderColor: Colors.PRIMARY,
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
    marginTop: 32,
  },
  emptyText: {
    fontSize: 16,
    color: '#757575',
    marginTop: 16,
    textAlign: 'center',
  },
  emptyButton: {
    marginTop: 16,
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  emptyButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
});

export default CategorySelector;
