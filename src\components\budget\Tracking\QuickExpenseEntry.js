/**
 * Quick Expense Entry Modal
 * Allows quick expense entry directly from budget screen
 * Integrates with existing transaction system
 * 
 * Features:
 * - Quick expense addition
 * - Category selection
 * - Multi-currency support
 * - Real-time budget updates
 * - Integration with existing transaction system
 * - Turkish localization
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Modal,
  StyleSheet,
  Alert,
  ScrollView,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSQLiteContext } from 'expo-sqlite';

// Import existing services
import * as TransactionService from '../../services/TransactionService';
import * as CategoryService from '../../services/categoryService';
import * as RealBudgetService from '../../services/budget/realBudgetService';

/**
 * Quick Expense Entry Modal Component
 * @param {boolean} visible - Modal visibility
 * @param {Object} budget - Selected budget
 * @param {Function} onClose - Close modal callback
 * @param {Function} onExpenseAdded - Expense added callback
 * @param {Object} theme - Theme colors
 */
export default function QuickExpenseEntry({ 
  visible, 
  budget, 
  onClose, 
  onExpenseAdded, 
  theme 
}) {
  const db = useSQLiteContext();
  
  // State management
  const [amount, setAmount] = useState('');
  const [description, setDescription] = useState('');
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [categoriesLoading, setCategoriesLoading] = useState(true);

  /**
   * Loads available categories
   */
  const loadCategories = useCallback(async () => {
    try {
      setCategoriesLoading(true);
      
      // Get budget categories first (if budget has specific categories)
      let budgetCategories = [];
      if (budget && budget.categories && budget.categories.length > 0) {
        budgetCategories = budget.categories.map(bc => ({
          id: bc.category_id,
          name: bc.category_name,
          icon: bc.category_icon,
          color: bc.category_color,
          budget_limit: bc.limit_amount,
          budget_spent: bc.spent_amount,
          is_over_budget: bc.is_over_budget
        }));
      }
      
      // Get all expense categories as fallback
      const allCategories = await CategoryService.getCategories(db);
      const expenseCategories = allCategories.filter(cat => 
        cat.type === 'expense' || cat.type === 'both'
      );
      
      // Combine and prioritize budget categories
      const combinedCategories = budgetCategories.length > 0 
        ? budgetCategories 
        : expenseCategories;
      
      setCategories(combinedCategories);
      
      // Auto-select first category if available
      if (combinedCategories.length > 0 && !selectedCategory) {
        setSelectedCategory(combinedCategories[0]);
      }
      
    } catch (error) {
      console.error('❌ Categories loading failed:', error);
      Alert.alert('Hata', 'Kategoriler yüklenirken bir hata oluştu.');
    } finally {
      setCategoriesLoading(false);
    }
  }, [db, budget, selectedCategory]);

  /**
   * Handles expense submission
   */
  const handleSubmit = useCallback(async () => {
    try {
      // Validation
      if (!amount || parseFloat(amount) <= 0) {
        Alert.alert('Hata', 'Lütfen geçerli bir miktar girin.');
        return;
      }
      
      if (!selectedCategory) {
        Alert.alert('Hata', 'Lütfen bir kategori seçin.');
        return;
      }
      
      setLoading(true);
      
      // Prepare transaction data
      const transactionData = {
        type: 'expense',
        amount: parseFloat(amount),
        description: description || `Hızlı gider - ${selectedCategory.name}`,
        category_id: selectedCategory.id,
        currency: budget.currency || 'TRY',
        date: new Date().toISOString(),
        account_id: null, // Use default account
      };
      
      // Add transaction using existing service
      await TransactionService.addTransaction(db, transactionData);
      
      // Update budget spending amounts
      await RealBudgetService.updateBudgetSpending(db, budget.id);
      
      console.log('✅ Quick expense added successfully');
      
      // Reset form
      setAmount('');
      setDescription('');
      
      // Close modal and refresh data
      onExpenseAdded();
      
      Alert.alert(
        'Başarılı',
        `${parseFloat(amount).toLocaleString('tr-TR')} ₺ gider eklendi.`,
        [{ text: 'Tamam' }]
      );
      
    } catch (error) {
      console.error('❌ Quick expense submission failed:', error);
      Alert.alert(
        'Hata',
        'Gider eklenirken bir hata oluştu. Lütfen tekrar deneyin.'
      );
    } finally {
      setLoading(false);
    }
  }, [amount, description, selectedCategory, budget, db, onExpenseAdded]);

  /**
   * Load categories when modal opens
   */
  useEffect(() => {
    if (visible) {
      loadCategories();
    }
  }, [visible, loadCategories]);

  /**
   * Formats currency display
   */
  const formatCurrency = (value, currency = 'TRY') => {
    const symbols = { TRY: '₺', USD: '$', EUR: '€' };
    const symbol = symbols[currency] || currency;
    return `${symbol}`;
  };

  /**
   * Gets category usage status
   */
  const getCategoryStatus = (category) => {
    if (!category.budget_limit) return null;
    
    const usagePercentage = (category.budget_spent / category.budget_limit) * 100;
    
    if (usagePercentage >= 100) return { color: theme.DANGER, text: 'Limit aşıldı' };
    if (usagePercentage >= 90) return { color: theme.WARNING, text: 'Limit yaklaşıyor' };
    if (usagePercentage >= 75) return { color: theme.WARNING_LIGHT, text: 'Dikkat' };
    return { color: theme.SUCCESS, text: 'Güvenli' };
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
      >
        {/* Header */}
        <View style={[styles.header, { backgroundColor: theme.PRIMARY }]}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={theme.ON_PRIMARY} />
          </TouchableOpacity>
          
          <Text style={[styles.headerTitle, { color: theme.ON_PRIMARY }]}>
            Hızlı Gider Ekle
          </Text>
          
          <TouchableOpacity
            onPress={handleSubmit}
            disabled={loading || !amount || !selectedCategory}
            style={[
              styles.submitButton,
              { 
                backgroundColor: loading || !amount || !selectedCategory 
                  ? 'rgba(255,255,255,0.3)' 
                  : 'rgba(255,255,255,0.9)'
              }
            ]}
          >
            {loading ? (
              <ActivityIndicator size="small" color={theme.PRIMARY} />
            ) : (
              <Text style={[styles.submitButtonText, { color: theme.PRIMARY }]}>
                Ekle
              </Text>
            )}
          </TouchableOpacity>
        </View>

        {/* Budget Info */}
        <View style={[styles.budgetInfo, { backgroundColor: theme.SURFACE }]}>
          <Text style={[styles.budgetName, { color: theme.TEXT_PRIMARY }]}>
            {budget?.name}
          </Text>
          <Text style={[styles.budgetDetails, { color: theme.TEXT_SECONDARY }]}>
            Kalan: {((budget?.total_limit || 0) - (budget?.total_spent || 0)).toLocaleString('tr-TR')} {formatCurrency(0, budget?.currency)}
          </Text>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Amount Input */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
              Miktar
            </Text>
            <View style={[styles.amountContainer, { backgroundColor: theme.SURFACE, borderColor: theme.BORDER }]}>
              <TextInput
                style={[styles.amountInput, { color: theme.TEXT_PRIMARY }]}
                value={amount}
                onChangeText={setAmount}
                placeholder="0.00"
                placeholderTextColor={theme.TEXT_DISABLED}
                keyboardType="numeric"
                autoFocus
              />
              <Text style={[styles.currencySymbol, { color: theme.TEXT_SECONDARY }]}>
                {formatCurrency(0, budget?.currency)}
              </Text>
            </View>
          </View>

          {/* Description Input */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
              Açıklama (İsteğe bağlı)
            </Text>
            <TextInput
              style={[
                styles.descriptionInput,
                { 
                  backgroundColor: theme.SURFACE,
                  borderColor: theme.BORDER,
                  color: theme.TEXT_PRIMARY
                }
              ]}
              value={description}
              onChangeText={setDescription}
              placeholder="Gider açıklaması..."
              placeholderTextColor={theme.TEXT_DISABLED}
              multiline
              numberOfLines={2}
            />
          </View>

          {/* Category Selection */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
              Kategori
            </Text>
            
            {categoriesLoading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="small" color={theme.PRIMARY} />
                <Text style={[styles.loadingText, { color: theme.TEXT_SECONDARY }]}>
                  Kategoriler yükleniyor...
                </Text>
              </View>
            ) : (
              <View style={styles.categoriesGrid}>
                {categories.map((category) => {
                  const isSelected = selectedCategory?.id === category.id;
                  const status = getCategoryStatus(category);
                  
                  return (
                    <TouchableOpacity
                      key={category.id}
                      style={[
                        styles.categoryCard,
                        {
                          backgroundColor: isSelected ? theme.PRIMARY_LIGHT : theme.SURFACE,
                          borderColor: isSelected ? theme.PRIMARY : theme.BORDER,
                        }
                      ]}
                      onPress={() => setSelectedCategory(category)}
                    >
                      <View style={styles.categoryHeader}>
                        <Text style={[
                          styles.categoryName,
                          { color: isSelected ? theme.ON_PRIMARY : theme.TEXT_PRIMARY }
                        ]}>
                          {category.name}
                        </Text>
                        
                        {isSelected && (
                          <Ionicons 
                            name="checkmark-circle" 
                            size={20} 
                            color={theme.SUCCESS} 
                          />
                        )}
                      </View>
                      
                      {category.budget_limit && (
                        <View style={styles.categoryBudgetInfo}>
                          <Text style={[
                            styles.categoryBudgetText,
                            { color: isSelected ? theme.ON_PRIMARY : theme.TEXT_SECONDARY }
                          ]}>
                            {category.budget_spent?.toLocaleString('tr-TR') || 0} / {category.budget_limit?.toLocaleString('tr-TR')} ₺
                          </Text>
                          
                          {status && (
                            <Text style={[styles.categoryStatus, { color: status.color }]}>
                              {status.text}
                            </Text>
                          )}
                        </View>
                      )}
                    </TouchableOpacity>
                  );
                })}
              </View>
            )}
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: 48,
  },
  closeButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
    textAlign: 'center',
  },
  submitButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    minWidth: 60,
    alignItems: 'center',
  },
  submitButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  budgetInfo: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  budgetName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  budgetDetails: {
    fontSize: 14,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  amountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  amountInput: {
    flex: 1,
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'right',
  },
  currencySymbol: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  descriptionInput: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    minHeight: 80,
    textAlignVertical: 'top',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    padding: 16,
  },
  loadingText: {
    fontSize: 14,
  },
  categoriesGrid: {
    gap: 8,
  },
  categoryCard: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  categoryName: {
    fontSize: 14,
    fontWeight: '600',
  },
  categoryBudgetInfo: {
    gap: 2,
  },
  categoryBudgetText: {
    fontSize: 12,
  },
  categoryStatus: {
    fontSize: 11,
    fontWeight: '600',
  },
});
