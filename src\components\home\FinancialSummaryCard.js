import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { formatCurrency } from '../../utils/formatters';
import { Colors } from '../../constants/colors';

/**
 * Finansal özet bilgilerini gösteren kart bileşeni
 *
 * @param {Object} props Bileşen özellikleri
 * @param {number} props.currentBalance Mevcut bakiye
 * @param {number} props.income Toplam gelir
 * @param {number} props.expense Toplam gider
 * @param {Function} props.onIncomePress Gelir sekmesine tıklama işlevi
 * @param {Function} props.onExpensePress Gider sekmesine tıklama işlevi
 * @param {Object} props.exchangeRates Döviz karşılıkları
 * @returns {JSX.Element} FinancialSummaryCard bileşeni
 */
const FinancialSummaryCard = ({
  currentBalance = 0,
  income = 0,
  expense = 0,
  onIncomePress,
  onExpensePress,
  exchangeRates = null
}) => {
  // Bakiyenin pozitif mi negatif mi olduğunu belirle
  const isNegativeBalance = currentBalance < 0;

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.balanceTitle}>Mevcut Bakiye</Text>
        <MaterialIcons
          name="account-balance-wallet"
          size={24}
          color={Colors.PRIMARY}
        />
      </View>

      <Text style={[
        styles.balanceAmount,
        isNegativeBalance && styles.negativeAmount
      ]}>
        {formatCurrency(currentBalance)}
      </Text>

      {/* Döviz karşılıkları */}
      {exchangeRates && (
        <View style={styles.exchangeRatesContainer}>
          <View style={styles.exchangeRateItem}>
            <MaterialIcons name="attach-money" size={16} color={'#6B7280'} />
            <Text style={styles.exchangeText}>{exchangeRates.USD}</Text>
          </View>
          <View style={styles.exchangeRateItem}>
            <MaterialIcons name="euro" size={16} color={'#6B7280'} />
            <Text style={styles.exchangeText}>{exchangeRates.EUR}</Text>
          </View>
        </View>
      )}

      <View style={styles.detailsContainer}>
        <TouchableOpacity
          style={[styles.detailItem, styles.incomeItem]}
          onPress={onIncomePress}
          activeOpacity={0.7}
        >
          <MaterialIcons name="arrow-upward" size={20} color={'#2ecc71'} />
          <View style={styles.detailContent}>
            <Text style={styles.detailLabel}>Gelir</Text>
            <Text style={[styles.detailAmount, styles.incomeText]}>
              {formatCurrency(income)}
            </Text>
          </View>
          <MaterialIcons name="chevron-right" size={20} color={'#9CA3AF'} />
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.detailItem, styles.expenseItem]}
          onPress={onExpensePress}
          activeOpacity={0.7}
        >
          <MaterialIcons name="arrow-downward" size={20} color={'#e74c3c'} />
          <View style={styles.detailContent}>
            <Text style={styles.detailLabel}>Gider</Text>
            <Text style={[styles.detailAmount, styles.expenseText]}>
              {formatCurrency(expense)}
            </Text>
          </View>
          <MaterialIcons name="chevron-right" size={20} color={'#9CA3AF'} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    margin: 16,
    borderRadius: 12,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8
  },
  balanceTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151'
  },
  balanceAmount: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 8
  },
  negativeAmount: {
    color: '#e74c3c'
  },
  exchangeRatesContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    gap: 16
  },
  exchangeRateItem: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  exchangeText: {
    fontSize: 14,
    color: '#6B7280',
    marginLeft: 4
  },
  detailsContainer: {
    gap: 12
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    backgroundColor: '#F9FAFB'
  },
  incomeItem: {
    borderLeftWidth: 4,
    borderLeftColor: '#2ecc71'
  },
  expenseItem: {
    borderLeftWidth: 4,
    borderLeftColor: '#e74c3c'
  },
  detailContent: {
    flex: 1,
    marginLeft: 12
  },
  detailLabel: {
    fontSize: 14,
    color: '#4B5563',
    marginBottom: 4
  },
  detailAmount: {
    fontSize: 16,
    fontWeight: '600'
  },
  incomeText: {
    color: '#2ecc71'
  },
  expenseText: {
    color: '#e74c3c'
  }
});

export default FinancialSummaryCard;
