import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../../../constants/colors';
import { formatTime, calculateDuration, formatCurrency, getShiftStatusColor } from '../utils/shiftUtils';
import { shiftStyles } from '../styles/shiftStyles';

/**
 * Vardiya Öğesi Bileşeni
 * 
 * Bu bileşen, vardiya listesinde her bir vardiyayı temsil eder.
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.shift - Vardiya verisi
 * @param {Object} props.shiftType - Vardiya türü verisi
 * @param {Function} props.onPress - Vardiyaya tıklandığında çalışacak fonksiyon
 * @param {Function} props.onDelete - Silme butonuna tıklandığında çalışacak fonksiyon
 * @returns {JSX.Element} Vardiya öğesi
 */
const ShiftItem = ({ shift, shiftType, onPress, onDelete }) => {
  // Vardiya süresi hesaplama
  const duration = calculateDuration(
    new Date(`${shift.date}T${shift.start_time}`),
    new Date(`${shift.date}T${shift.end_time}`),
    shift.break_duration
  );
  
  // Vardiya kazancı
  const earnings = shift.earnings || 0;
  
  return (
    <TouchableOpacity 
      style={[styles.container, { borderLeftColor: shiftType?.color || Colors.PRIMARY }]}
      onPress={onPress}
    >
      {/* Vardiya Başlığı */}
      <View style={styles.header}>
        <View style={styles.timeContainer}>
          <MaterialIcons name="access-time" size={16} color="#666" />
          <Text style={styles.timeText}>
            {formatTime(new Date(`${shift.date}T${shift.start_time}`))} - 
            {formatTime(new Date(`${shift.date}T${shift.end_time}`))}
          </Text>
        </View>
        
        <View style={[styles.statusBadge, { backgroundColor: getShiftStatusColor(shift.status) }]}>
          <Text style={styles.statusText}>
            {shift.status === 'active' ? 'Aktif' : 
             shift.status === 'completed' ? 'Tamamlandı' : 
             shift.status === 'planned' ? 'Planlandı' : 'İptal'}
          </Text>
        </View>
      </View>
      
      {/* Vardiya Detayları */}
      <View style={styles.details}>
        {/* Vardiya Türü */}
        {shiftType && (
          <View style={[styles.tag, { backgroundColor: shiftType.color }]}>
            <MaterialIcons name="category" size={12} color="#fff" />
            <Text style={styles.tagText}>{shiftType.name}</Text>
          </View>
        )}
        
        {/* Mesai Durumu */}
        {shift.is_overtime === 1 && (
          <View style={[styles.tag, { backgroundColor: Colors.WARNING }]}>
            <MaterialIcons name="alarm-on" size={12} color="#fff" />
            <Text style={styles.tagText}>Mesai</Text>
          </View>
        )}
        
        {/* Tatil Durumu */}
        {shift.is_holiday === 1 && (
          <View style={[styles.tag, { backgroundColor: '#e74c3c' }]}>
            <MaterialIcons name="event" size={12} color="#fff" />
            <Text style={styles.tagText}>Tatil</Text>
          </View>
        )}
      </View>
      
      {/* Alt Bilgiler */}
      <View style={styles.footer}>
        <View style={styles.infoItem}>
          <MaterialIcons name="timelapse" size={16} color="#666" />
          <Text style={styles.infoText}>{duration} saat</Text>
        </View>
        
        <View style={styles.infoItem}>
          <MaterialIcons name="account-balance-wallet" size={16} color="#666" />
          <Text style={styles.earningsText}>{formatCurrency(earnings)}</Text>
        </View>
      </View>
      
      {/* Silme Butonu */}
      {onDelete && (
        <TouchableOpacity 
          style={styles.deleteButton}
          onPress={() => onDelete(shift.id)}
          hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
        >
          <MaterialIcons name="delete-outline" size={20} color="#ff5252" />
        </TouchableOpacity>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    ...shiftStyles.listItem,
    borderLeftWidth: 4,
    paddingLeft: 12,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 6,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#fff',
  },
  details: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 12,
  },
  tag: {
    ...shiftStyles.tag,
  },
  tagText: {
    ...shiftStyles.tagText,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    paddingTop: 12,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 6,
  },
  earningsText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.INCOME,
    marginLeft: 6,
  },
  deleteButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    padding: 4,
  },
});

export default ShiftItem;
