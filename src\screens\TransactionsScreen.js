import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList, RefreshControl } from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import { useAppContext } from '../context/AppContext';
import TransactionItem from '../components/TransactionItem';
import TransactionForm from '../components/TransactionForm';
import FilterBar from '../components/FilterBar';

/**
 * İşlemler ekranı
 *
 * @returns {JSX.Element} İşlemler ekranı
 */
export default function TransactionsScreen() {
  const insets = useSafeAreaInsets();
  const db = useSQLiteContext();
  const { theme } = useAppContext();

  const [transactions, setTransactions] = useState([]);
  const [isAddingTransaction, setIsAddingTransaction] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [filter, setFilter] = useState({
    type: 'all', // 'all', 'income', 'expense'
    period: 'all', // 'all', 'today', 'week', 'month', 'year'
    category: null,
  });

  // Verileri yükle
  const loadData = async () => {
    try {
      let query = `
        SELECT t.*, c.name as category_name, c.color as category_color, c.icon as category_icon
        FROM transactions t
        LEFT JOIN categories c ON t.category_id = c.id
        WHERE 1=1
      `;

      const params = [];

      // Tür filtresi
      if (filter.type !== 'all') {
        query += ` AND t.type = ?`;
        params.push(filter.type);
      }

      // Dönem filtresi
      if (filter.period !== 'all') {
        const now = new Date();
        let startDate;

        if (filter.period === 'today') {
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        } else if (filter.period === 'week') {
          startDate = new Date(now);
          startDate.setDate(now.getDate() - 7);
        } else if (filter.period === 'month') {
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        } else if (filter.period === 'year') {
          startDate = new Date(now.getFullYear(), 0, 1);
        }

        if (startDate) {
          query += ` AND t.date >= ?`;
          params.push(startDate.toISOString().split('T')[0]);
        }
      }

      // Kategori filtresi
      if (filter.category) {
        query += ` AND t.category_id = ?`;
        params.push(filter.category);
      }

      query += ` ORDER BY t.date DESC, t.id DESC`;

      const result = await db.getAllAsync(query, params);
      setTransactions(result);
    } catch (error) {
      console.error('Veri yükleme hatası:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // İlk yükleme
  useEffect(() => {
    loadData();
  }, [filter]);

  // Yenileme işlemi
  const onRefresh = () => {
    setRefreshing(true);
    loadData();
  };

  return (
    <View style={[styles.container, { paddingTop: insets.top, backgroundColor: theme.BACKGROUND }]}>
      <View style={[styles.header, { backgroundColor: theme.PRIMARY }]}>
        <Text style={[styles.title, { color: theme.WHITE }]}>İşlemler</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => setIsAddingTransaction(true)}
        >
          <MaterialIcons name="add" size={24} color={theme.WHITE} />
        </TouchableOpacity>
      </View>

      <FilterBar
        filter={filter}
        onFilterChange={setFilter}
      />

      <FlatList
        data={transactions}
        keyExtractor={(item) => item.id.toString()}
        renderItem={({ item }) => (
          <TransactionItem
            transaction={item}
            onRefresh={loadData}
          />
        )}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <MaterialIcons name="receipt-long" size={64} color={theme.TEXT_SECONDARY} />
            <Text style={[styles.emptyText, { color: theme.TEXT_SECONDARY }]}>Henüz işlem bulunmuyor</Text>
          </View>
        }
        contentContainerStyle={styles.listContent}
      />

      {isAddingTransaction && (
        <TransactionForm
          visible={isAddingTransaction}
          onClose={() => setIsAddingTransaction(false)}
          onSave={() => {
            setIsAddingTransaction(false);
            loadData();
          }}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContent: {
    padding: 16,
    flexGrow: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
  },
});
