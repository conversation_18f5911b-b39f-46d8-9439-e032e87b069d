import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, FlatList } from 'react-native';

/**
 * Son Raporlar Listesi
 * Son kull<PERSON><PERSON> rapor<PERSON>ın listesi
 */
const RecentReportsList = ({ recentReports, onReportPress, theme }) => {
  const getReportIcon = (type) => {
    switch (type) {
      case 'financial': return '💰';
      case 'category': return '🏷️';
      case 'income': return '💸';
      case 'expense': return '📊';
      default: return '📋';
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const renderReport = ({ item }) => (
    <TouchableOpacity
      style={[styles.reportCard, { backgroundColor: theme.SURFACE || theme.colors?.surface || '#f9f9f9' }]}
      onPress={() => onReportPress(item)}
    >
      <View style={styles.reportHeader}>
        <Text style={styles.reportIcon}>{getReportIcon(item.type)}</Text>
        <View style={styles.reportMeta}>
          <Text style={[styles.reportDate, { color: theme.TEXT_SECONDARY }]}>
            {formatDate(item.lastModified)}
          </Text>
          {item.isFavorite && (
            <Text style={styles.favoriteIcon}>⭐</Text>
          )}
        </View>
      </View>
      
      <Text style={[styles.reportName, { color: theme.TEXT_PRIMARY }]}>
        {item.name}
      </Text>
      
      <View style={styles.reportFooter}>
        <Text style={[styles.exportCount, { color: theme.TEXT_SECONDARY }]}>
          📤 {item.exportCount} dışa aktarma
        </Text>
        <TouchableOpacity style={styles.moreButton}>
          <Text style={[styles.moreButtonText, { color: theme.PRIMARY }]}>
            •••
          </Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.sectionHeader}>
        <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
          📋 Son Raporlar
        </Text>
        <TouchableOpacity>
          <Text style={[styles.seeAllText, { color: theme.PRIMARY }]}>
            Tümünü Gör
          </Text>
        </TouchableOpacity>
      </View>
      
      <FlatList
        data={recentReports}
        renderItem={renderReport}
        keyExtractor={(item) => item.id.toString()}
        scrollEnabled={false}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 16,
    paddingHorizontal: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  seeAllText: {
    fontSize: 14,
    fontWeight: '500',
  },
  reportCard: {
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  reportHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  reportIcon: {
    fontSize: 24,
  },
  reportMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  reportDate: {
    fontSize: 12,
    marginRight: 8,
  },
  favoriteIcon: {
    fontSize: 16,
  },
  reportName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  reportFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  exportCount: {
    fontSize: 12,
  },
  moreButton: {
    padding: 4,
  },
  moreButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  separator: {
    height: 12,
  },
});

export default RecentReportsList;
