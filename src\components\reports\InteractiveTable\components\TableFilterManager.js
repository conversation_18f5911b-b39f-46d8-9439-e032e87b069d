import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  TextInput,
  Modal,
  Alert,
} from 'react-native';

/**
 * Table Filter Manager Component
 * Advanced filtering system with multiple conditions and operators
 * Supports date ranges, number ranges, and text filters
 */
const TableFilterManager = ({ 
  visible,
  onClose,
  filters = [],
  onAddFilter,
  onUpdateFilter,
  onRemoveFilter,
  onClearFilters,
  availableColumns = [],
  theme 
}) => {
  const [editingFilter, setEditingFilter] = useState(null);
  const [newFilter, setNewFilter] = useState({
    column: '',
    operator: 'equals',
    value: '',
    value2: '', // For range filters
    type: 'text',
  });

  // Filter operators by data type
  const operators = {
    text: [
      { value: 'equals', label: 'Eşittir', symbol: '=' },
      { value: 'not_equals', label: 'Eşit değildir', symbol: '≠' },
      { value: 'contains', label: '<PERSON>ç<PERSON>r', symbol: '⊃' },
      { value: 'not_contains', label: '<PERSON><PERSON>er<PERSON>z', symbol: '⊅' },
      { value: 'starts_with', label: 'İle başlar', symbol: '⌐' },
      { value: 'ends_with', label: 'İle biter', symbol: '⌐' },
      { value: 'is_empty', label: 'Boş', symbol: '∅' },
      { value: 'is_not_empty', label: 'Boş değil', symbol: '≠∅' },
    ],
    number: [
      { value: 'equals', label: 'Eşittir', symbol: '=' },
      { value: 'not_equals', label: 'Eşit değildir', symbol: '≠' },
      { value: 'greater_than', label: 'Büyüktür', symbol: '>' },
      { value: 'greater_equal', label: 'Büyük eşittir', symbol: '≥' },
      { value: 'less_than', label: 'Küçüktür', symbol: '<' },
      { value: 'less_equal', label: 'Küçük eşittir', symbol: '≤' },
      { value: 'between', label: 'Arasında', symbol: '⟷' },
      { value: 'is_empty', label: 'Boş', symbol: '∅' },
    ],
    date: [
      { value: 'equals', label: 'Eşittir', symbol: '=' },
      { value: 'not_equals', label: 'Eşit değildir', symbol: '≠' },
      { value: 'after', label: 'Sonra', symbol: '>' },
      { value: 'before', label: 'Önce', symbol: '<' },
      { value: 'between', label: 'Arasında', symbol: '⟷' },
      { value: 'today', label: 'Bugün', symbol: '📅' },
      { value: 'yesterday', label: 'Dün', symbol: '📅' },
      { value: 'this_week', label: 'Bu hafta', symbol: '📅' },
      { value: 'this_month', label: 'Bu ay', symbol: '📅' },
      { value: 'this_year', label: 'Bu yıl', symbol: '📅' },
    ],
    boolean: [
      { value: 'equals', label: 'Eşittir', symbol: '=' },
      { value: 'not_equals', label: 'Eşit değildir', symbol: '≠' },
    ],
  };

  const handleAddFilter = () => {
    if (!newFilter.column || !newFilter.operator) {
      Alert.alert('Hata', 'Sütun ve operatör seçimi gereklidir.');
      return;
    }

    const filter = {
      ...newFilter,
      id: Date.now().toString(),
    };

    onAddFilter(filter);
    resetForm();
  };

  const handleEditFilter = (filter) => {
    setEditingFilter(filter);
    setNewFilter({ ...filter });
  };

  const handleUpdateFilter = () => {
    if (!newFilter.column || !newFilter.operator) {
      Alert.alert('Hata', 'Sütun ve operatör seçimi gereklidir.');
      return;
    }

    onUpdateFilter(editingFilter.id, newFilter);
    setEditingFilter(null);
    resetForm();
  };

  const handleRemoveFilter = (filterId) => {
    Alert.alert(
      'Filtreyi Sil',
      'Bu filtreyi silmek istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        { 
          text: 'Sil', 
          style: 'destructive',
          onPress: () => onRemoveFilter(filterId)
        },
      ]
    );
  };

  const resetForm = () => {
    setNewFilter({
      column: '',
      operator: 'equals',
      value: '',
      value2: '',
      type: 'text',
    });
  };

  const handleColumnSelect = (column) => {
    setNewFilter(prev => ({
      ...prev,
      column: column.name,
      type: column.type,
      operator: 'equals',
      value: '',
      value2: '',
    }));
  };

  const needsSecondValue = (operator) => {
    return operator === 'between';
  };

  const needsValue = (operator) => {
    return !['is_empty', 'is_not_empty', 'today', 'yesterday', 'this_week', 'this_month', 'this_year'].includes(operator);
  };

  const renderFilterForm = () => (
    <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
      <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
        {editingFilter ? 'Filtreyi Düzenle' : 'Yeni Filtre Ekle'}
      </Text>

      {/* Column Selection */}
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
          Sütun Seç *
        </Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {availableColumns.map((column, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.columnChip,
                {
                  backgroundColor: newFilter.column === column.name 
                    ? theme.PRIMARY 
                    : theme.BACKGROUND,
                  borderColor: theme.BORDER
                }
              ]}
              onPress={() => handleColumnSelect(column)}
            >
              <Text style={[
                styles.chipText,
                { 
                  color: newFilter.column === column.name 
                    ? theme.SURFACE 
                    : theme.TEXT_PRIMARY 
                }
              ]}>
                {column.name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Operator Selection */}
      {newFilter.column && (
        <View style={styles.formGroup}>
          <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
            Operatör *
          </Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {(operators[newFilter.type] || operators.text).map((op, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.operatorChip,
                  {
                    backgroundColor: newFilter.operator === op.value 
                      ? theme.INFO 
                      : theme.BACKGROUND,
                    borderColor: theme.BORDER
                  }
                ]}
                onPress={() => setNewFilter(prev => ({ ...prev, operator: op.value }))}
              >
                <Text style={styles.operatorSymbol}>{op.symbol}</Text>
                <Text style={[
                  styles.operatorLabel,
                  { 
                    color: newFilter.operator === op.value 
                      ? theme.SURFACE 
                      : theme.TEXT_PRIMARY 
                  }
                ]}>
                  {op.label}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}

      {/* Value Input */}
      {newFilter.operator && needsValue(newFilter.operator) && (
        <View style={styles.formGroup}>
          <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
            Değer {needsSecondValue(newFilter.operator) ? '1' : ''} *
          </Text>
          <TextInput
            style={[
              styles.input,
              { 
                backgroundColor: theme.BACKGROUND,
                color: theme.TEXT_PRIMARY,
                borderColor: theme.BORDER
              }
            ]}
            value={newFilter.value}
            onChangeText={(text) => setNewFilter(prev => ({ ...prev, value: text }))}
            placeholder={`${newFilter.type === 'date' ? 'YYYY-MM-DD' : 'Değer'} girin...`}
            placeholderTextColor={theme.TEXT_SECONDARY}
            keyboardType={newFilter.type === 'number' ? 'numeric' : 'default'}
          />
        </View>
      )}

      {/* Second Value Input for Range */}
      {newFilter.operator && needsSecondValue(newFilter.operator) && (
        <View style={styles.formGroup}>
          <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
            Değer 2 *
          </Text>
          <TextInput
            style={[
              styles.input,
              { 
                backgroundColor: theme.BACKGROUND,
                color: theme.TEXT_PRIMARY,
                borderColor: theme.BORDER
              }
            ]}
            value={newFilter.value2}
            onChangeText={(text) => setNewFilter(prev => ({ ...prev, value2: text }))}
            placeholder={`${newFilter.type === 'date' ? 'YYYY-MM-DD' : 'Değer'} girin...`}
            placeholderTextColor={theme.TEXT_SECONDARY}
            keyboardType={newFilter.type === 'number' ? 'numeric' : 'default'}
          />
        </View>
      )}

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <TouchableOpacity
          style={[styles.cancelButton, { backgroundColor: theme.ERROR }]}
          onPress={() => {
            setEditingFilter(null);
            resetForm();
          }}
        >
          <Text style={[styles.buttonText, { color: theme.SURFACE }]}>
            İptal
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.saveButton, { backgroundColor: theme.SUCCESS }]}
          onPress={editingFilter ? handleUpdateFilter : handleAddFilter}
        >
          <Text style={[styles.buttonText, { color: theme.SURFACE }]}>
            {editingFilter ? 'Güncelle' : 'Ekle'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
        {/* Header */}
        <View style={[styles.header, { backgroundColor: theme.SURFACE }]}>
          <TouchableOpacity onPress={onClose}>
            <Text style={[styles.closeButton, { color: theme.ERROR }]}>
              Kapat
            </Text>
          </TouchableOpacity>
          
          <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
            Filtre Yöneticisi
          </Text>
          
          <TouchableOpacity 
            onPress={onClearFilters}
            disabled={filters.length === 0}
          >
            <Text style={[
              styles.clearButton, 
              { 
                color: filters.length > 0 ? theme.WARNING : theme.DISABLED,
                opacity: filters.length > 0 ? 1 : 0.5
              }
            ]}>
              Temizle
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content}>
          {/* Filter Form */}
          {renderFilterForm()}

          {/* Active Filters */}
          <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
            <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
              Aktif Filtreler ({filters.length})
            </Text>

            {filters.map((filter, index) => (
              <View 
                key={filter.id} 
                style={[styles.filterItem, { backgroundColor: theme.BACKGROUND }]}
              >
                <View style={styles.filterInfo}>
                  <Text style={[styles.filterColumn, { color: theme.TEXT_PRIMARY }]}>
                    {filter.column}
                  </Text>
                  <Text style={[styles.filterCondition, { color: theme.TEXT_SECONDARY }]}>
                    {operators[filter.type]?.find(op => op.value === filter.operator)?.label || filter.operator}
                    {filter.value && ` "${filter.value}"`}
                    {filter.value2 && ` - "${filter.value2}"`}
                  </Text>
                </View>

                <View style={styles.filterActions}>
                  <TouchableOpacity
                    style={[styles.actionButton, { backgroundColor: theme.INFO }]}
                    onPress={() => handleEditFilter(filter)}
                  >
                    <Text style={[styles.actionButtonText, { color: theme.SURFACE }]}>
                      Düzenle
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[styles.actionButton, { backgroundColor: theme.ERROR }]}
                    onPress={() => handleRemoveFilter(filter.id)}
                  >
                    <Text style={[styles.actionButtonText, { color: theme.SURFACE }]}>
                      Sil
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            ))}

            {filters.length === 0 && (
              <Text style={[styles.emptyText, { color: theme.TEXT_SECONDARY }]}>
                Henüz filtre eklenmemiş. Yukarıdaki formu kullanarak filtre ekleyebilirsiniz.
              </Text>
            )}
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  closeButton: {
    fontSize: 16,
    fontWeight: '600',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  clearButton: {
    fontSize: 16,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 16,
    padding: 16,
    borderRadius: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  columnChip: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    borderWidth: 1,
    marginRight: 8,
  },
  operatorChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 8,
  },
  operatorSymbol: {
    fontSize: 16,
    marginRight: 6,
  },
  operatorLabel: {
    fontSize: 14,
    fontWeight: '600',
  },
  chipText: {
    fontSize: 14,
    fontWeight: '600',
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    marginRight: 8,
  },
  saveButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    marginLeft: 8,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  filterItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  filterInfo: {
    flex: 1,
  },
  filterColumn: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  filterCondition: {
    fontSize: 12,
  },
  filterActions: {
    flexDirection: 'row',
  },
  actionButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    marginLeft: 8,
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: '600',
  },
  emptyText: {
    textAlign: 'center',
    fontStyle: 'italic',
    padding: 20,
  },
});

export default TableFilterManager;
