import { Animated, Easing } from 'react-native';
import tokens from '../tokens';

/**
 * Animation System
 * Tutarlı animasyonlar için merkezi sistem
 */

/**
 * Animation presets
 */
export const AnimationPresets = {
  // Fade animations
  fadeIn: {
    duration: tokens.duration.normal,
    easing: Easing.out(Easing.quad),
    useNativeDriver: true,
  },
  fadeOut: {
    duration: tokens.duration.fast,
    easing: Easing.in(Easing.quad),
    useNativeDriver: true,
  },

  // Scale animations
  scaleIn: {
    duration: tokens.duration.normal,
    easing: Easing.out(Easing.back(1.2)),
    useNativeDriver: true,
  },
  scaleOut: {
    duration: tokens.duration.fast,
    easing: Easing.in(Easing.quad),
    useNativeDriver: true,
  },

  // Slide animations
  slideInUp: {
    duration: tokens.duration.normal,
    easing: Easing.out(Easing.quad),
    useNativeDriver: true,
  },
  slideInDown: {
    duration: tokens.duration.normal,
    easing: Easing.out(Easing.quad),
    useNativeDriver: true,
  },
  slideInLeft: {
    duration: tokens.duration.normal,
    easing: Easing.out(Easing.quad),
    useNativeDriver: true,
  },
  slideInRight: {
    duration: tokens.duration.normal,
    easing: Easing.out(Easing.quad),
    useNativeDriver: true,
  },

  // Spring animations
  spring: {
    tension: 100,
    friction: 8,
    useNativeDriver: true,
  },
  springBounce: {
    tension: 120,
    friction: 6,
    useNativeDriver: true,
  },

  // Button press
  buttonPress: {
    duration: tokens.duration.fastest,
    easing: Easing.inOut(Easing.quad),
    useNativeDriver: true,
  },

  // Modal animations
  modalSlideUp: {
    duration: tokens.duration.slow,
    easing: Easing.out(Easing.bezier(0.25, 0.46, 0.45, 0.94)),
    useNativeDriver: true,
  },
  modalFade: {
    duration: tokens.duration.normal,
    easing: Easing.inOut(Easing.quad),
    useNativeDriver: true,
  },
};

/**
 * Animation utilities
 */
export class AnimationUtils {
  /**
   * Fade in animation
   * @param {Animated.Value} animatedValue - Animated value
   * @param {Object} config - Animation config
   * @returns {Animated.CompositeAnimation} Animation
   */
  static fadeIn(animatedValue, config = {}) {
    return Animated.timing(animatedValue, {
      toValue: 1,
      ...AnimationPresets.fadeIn,
      ...config,
    });
  }

  /**
   * Fade out animation
   * @param {Animated.Value} animatedValue - Animated value
   * @param {Object} config - Animation config
   * @returns {Animated.CompositeAnimation} Animation
   */
  static fadeOut(animatedValue, config = {}) {
    return Animated.timing(animatedValue, {
      toValue: 0,
      ...AnimationPresets.fadeOut,
      ...config,
    });
  }

  /**
   * Scale in animation
   * @param {Animated.Value} animatedValue - Animated value
   * @param {Object} config - Animation config
   * @returns {Animated.CompositeAnimation} Animation
   */
  static scaleIn(animatedValue, config = {}) {
    return Animated.timing(animatedValue, {
      toValue: 1,
      ...AnimationPresets.scaleIn,
      ...config,
    });
  }

  /**
   * Scale out animation
   * @param {Animated.Value} animatedValue - Animated value
   * @param {Object} config - Animation config
   * @returns {Animated.CompositeAnimation} Animation
   */
  static scaleOut(animatedValue, config = {}) {
    return Animated.timing(animatedValue, {
      toValue: 0,
      ...AnimationPresets.scaleOut,
      ...config,
    });
  }

  /**
   * Slide in from bottom
   * @param {Animated.Value} animatedValue - Animated value
   * @param {number} fromValue - Start value
   * @param {Object} config - Animation config
   * @returns {Animated.CompositeAnimation} Animation
   */
  static slideInUp(animatedValue, fromValue = 100, config = {}) {
    animatedValue.setValue(fromValue);
    return Animated.timing(animatedValue, {
      toValue: 0,
      ...AnimationPresets.slideInUp,
      ...config,
    });
  }

  /**
   * Slide in from top
   * @param {Animated.Value} animatedValue - Animated value
   * @param {number} fromValue - Start value
   * @param {Object} config - Animation config
   * @returns {Animated.CompositeAnimation} Animation
   */
  static slideInDown(animatedValue, fromValue = -100, config = {}) {
    animatedValue.setValue(fromValue);
    return Animated.timing(animatedValue, {
      toValue: 0,
      ...AnimationPresets.slideInDown,
      ...config,
    });
  }

  /**
   * Spring animation
   * @param {Animated.Value} animatedValue - Animated value
   * @param {number} toValue - Target value
   * @param {Object} config - Animation config
   * @returns {Animated.CompositeAnimation} Animation
   */
  static spring(animatedValue, toValue, config = {}) {
    return Animated.spring(animatedValue, {
      toValue,
      ...AnimationPresets.spring,
      ...config,
    });
  }

  /**
   * Sequence animation
   * @param {Array} animations - Array of animations
   * @returns {Animated.CompositeAnimation} Sequence animation
   */
  static sequence(animations) {
    return Animated.sequence(animations);
  }

  /**
   * Parallel animation
   * @param {Array} animations - Array of animations
   * @returns {Animated.CompositeAnimation} Parallel animation
   */
  static parallel(animations) {
    return Animated.parallel(animations);
  }

  /**
   * Stagger animation
   * @param {number} time - Stagger time
   * @param {Array} animations - Array of animations
   * @returns {Animated.CompositeAnimation} Stagger animation
   */
  static stagger(time, animations) {
    return Animated.stagger(time, animations);
  }

  /**
   * Loop animation
   * @param {Animated.CompositeAnimation} animation - Animation to loop
   * @param {Object} config - Loop config
   * @returns {Animated.CompositeAnimation} Loop animation
   */
  static loop(animation, config = {}) {
    return Animated.loop(animation, {
      iterations: -1,
      ...config,
    });
  }

  /**
   * Button press animation
   * @param {Animated.Value} scaleValue - Scale animated value
   * @param {Function} onPress - Press callback
   * @returns {Function} Press handler
   */
  static createButtonPressAnimation(scaleValue, onPress) {
    return () => {
      Animated.sequence([
        Animated.timing(scaleValue, {
          toValue: 0.95,
          ...AnimationPresets.buttonPress,
        }),
        Animated.timing(scaleValue, {
          toValue: 1,
          ...AnimationPresets.buttonPress,
        }),
      ]).start();

      if (onPress) {
        setTimeout(onPress, tokens.duration.fastest);
      }
    };
  }

  /**
   * Modal slide up animation
   * @param {Animated.Value} translateY - TranslateY animated value
   * @param {Animated.Value} opacity - Opacity animated value
   * @returns {Animated.CompositeAnimation} Modal animation
   */
  static modalSlideUp(translateY, opacity) {
    return Animated.parallel([
      this.slideInUp(translateY, 300),
      this.fadeIn(opacity),
    ]);
  }

  /**
   * Modal slide down animation
   * @param {Animated.Value} translateY - TranslateY animated value
   * @param {Animated.Value} opacity - Opacity animated value
   * @returns {Animated.CompositeAnimation} Modal animation
   */
  static modalSlideDown(translateY, opacity) {
    return Animated.parallel([
      Animated.timing(translateY, {
        toValue: 300,
        ...AnimationPresets.slideInUp,
      }),
      this.fadeOut(opacity),
    ]);
  }
}

/**
 * Interpolation utilities
 */
export class InterpolationUtils {
  /**
   * Create fade interpolation
   * @param {Animated.Value} animatedValue - Animated value
   * @param {Array} inputRange - Input range
   * @param {Array} outputRange - Output range
   * @returns {Animated.AnimatedInterpolation} Interpolation
   */
  static fade(animatedValue, inputRange = [0, 1], outputRange = [0, 1]) {
    return animatedValue.interpolate({
      inputRange,
      outputRange,
      extrapolate: 'clamp',
    });
  }

  /**
   * Create scale interpolation
   * @param {Animated.Value} animatedValue - Animated value
   * @param {Array} inputRange - Input range
   * @param {Array} outputRange - Output range
   * @returns {Animated.AnimatedInterpolation} Interpolation
   */
  static scale(animatedValue, inputRange = [0, 1], outputRange = [0, 1]) {
    return animatedValue.interpolate({
      inputRange,
      outputRange,
      extrapolate: 'clamp',
    });
  }

  /**
   * Create rotate interpolation
   * @param {Animated.Value} animatedValue - Animated value
   * @param {Array} inputRange - Input range
   * @param {Array} outputRange - Output range (in degrees)
   * @returns {Animated.AnimatedInterpolation} Interpolation
   */
  static rotate(animatedValue, inputRange = [0, 1], outputRange = ['0deg', '360deg']) {
    return animatedValue.interpolate({
      inputRange,
      outputRange,
      extrapolate: 'clamp',
    });
  }

  /**
   * Create translate interpolation
   * @param {Animated.Value} animatedValue - Animated value
   * @param {Array} inputRange - Input range
   * @param {Array} outputRange - Output range
   * @returns {Animated.AnimatedInterpolation} Interpolation
   */
  static translate(animatedValue, inputRange = [0, 1], outputRange = [0, 100]) {
    return animatedValue.interpolate({
      inputRange,
      outputRange,
      extrapolate: 'clamp',
    });
  }
}

export default {
  AnimationPresets,
  AnimationUtils,
  InterpolationUtils,
};
