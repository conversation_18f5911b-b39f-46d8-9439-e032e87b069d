/**
 * Bütçe Performans Grafiği Komponenti
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md - Stage 4, Phase 1
 * 
 * Bütçe hedeflerine ulaşma başarı oranları ve performans skorları
 * Maksimum 300 satır - Tek sorumluluk prensibi
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Dimensions,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../../../context/ThemeContext';
import { formatTRY, formatUSD, formatEUR } from '../../../utils/formatters';

const { width } = Dimensions.get('window');

/**
 * Bütçe performans grafiği komponenti
 * @param {Object} props - Component props
 * @param {Array} props.performanceData - Performans verileri [{period, target, actual, score}]
 * @param {string} props.currency - Para birimi ('TRY', 'USD', 'EUR')
 * @param {string} props.period - Dönem türü ('monthly', 'weekly')
 * @param {number} props.chartHeight - Grafik yüksekliği
 * @param {Object} props.theme - Tema objesi (opsiyonel, context'ten alınır)
 */
const BudgetPerformanceChart = ({ 
  performanceData = [], 
  currency = 'TRY',
  period = 'monthly',
  chartHeight = 200,
  theme: propTheme 
}) => {
  const { theme, isDarkMode } = useTheme();
  const currentTheme = propTheme || theme;

  /**
   * Para birimi formatı
   * @param {number} value - Formatlanacak değer
   * @returns {string} Formatlanmış değer
   */
  const formatCurrency = (value) => {
    switch (currency) {
      case 'USD':
        return formatUSD(value);
      case 'EUR':
        return formatEUR(value);
      default:
        return formatTRY(value);
    }
  };

  /**
   * Para birimi sembolü
   * @returns {string} Para birimi sembolü
   */
  const getCurrencySymbol = () => {
    switch (currency) {
      case 'USD':
        return '$';
      case 'EUR':
        return '€';
      default:
        return '₺';
    }
  };

  /**
   * Performans skoru rengi belirleme
   * @param {number} score - Performans skoru (0-100)
   * @returns {string} Renk kodu
   */
  const getScoreColor = (score) => {
    if (score >= 90) return currentTheme.SUCCESS;
    if (score >= 75) return currentTheme.INFO;
    if (score >= 60) return currentTheme.WARNING;
    return currentTheme.ERROR;
  };

  /**
   * Performans skoru ikonu belirleme
   * @param {number} score - Performans skoru (0-100)
   * @returns {string} İkon adı
   */
  const getScoreIcon = (score) => {
    if (score >= 90) return 'star';
    if (score >= 75) return 'thumb-up';
    if (score >= 60) return 'info';
    return 'warning';
  };

  /**
   * Performans skoru mesajı belirleme
   * @param {number} score - Performans skoru (0-100)
   * @returns {string} Performans mesajı
   */
  const getScoreMessage = (score) => {
    if (score >= 90) return 'Mükemmel Performans';
    if (score >= 75) return 'İyi Performans';
    if (score >= 60) return 'Orta Performans';
    return 'Geliştirilmeli';
  };

  /**
   * Genel performans skoru hesaplama
   * @returns {number} Ortalama performans skoru
   */
  const calculateOverallScore = () => {
    if (performanceData.length === 0) return 0;
    const totalScore = performanceData.reduce((sum, data) => sum + data.score, 0);
    return Math.round(totalScore / performanceData.length);
  };

  /**
   * Grafik verilerini hazırla
   * @returns {Object} Hazırlanmış grafik verileri
   */
  const prepareChartData = () => {
    if (performanceData.length === 0) {
      return {
        maxValue: 100,
        bars: []
      };
    }

    const maxAmount = Math.max(
      ...performanceData.map(d => Math.max(d.target, d.actual))
    ) * 1.1;

    const chartWidth = width - 32;
    const barWidth = (chartWidth - (performanceData.length - 1) * 8) / performanceData.length;

    const bars = performanceData.map((data, index) => {
      const targetHeight = (data.target / maxAmount) * chartHeight;
      const actualHeight = (data.actual / maxAmount) * chartHeight;
      const x = index * (barWidth + 8);

      return {
        ...data,
        x,
        barWidth,
        targetHeight,
        actualHeight,
        scoreColor: getScoreColor(data.score)
      };
    });

    return {
      maxValue: maxAmount,
      bars
    };
  };

  /**
   * Dönem formatı
   * @param {string} periodString - Dönem string
   * @returns {string} Formatlanmış dönem
   */
  const formatPeriod = (periodString) => {
    if (period === 'monthly') {
      const [year, month] = periodString.split('-');
      const monthNames = [
        'Oca', 'Şub', 'Mar', 'Nis', 'May', 'Haz',
        'Tem', 'Ağu', 'Eyl', 'Eki', 'Kas', 'Ara'
      ];
      return `${monthNames[parseInt(month) - 1]} ${year}`;
    }
    return periodString;
  };

  if (performanceData.length === 0) {
    return (
      <View style={[styles.container, { backgroundColor: currentTheme.SURFACE }]}>
        <View style={styles.emptyContainer}>
          <MaterialIcons name="trending-up" size={48} color={currentTheme.TEXT_SECONDARY} />
          <Text style={[styles.emptyTitle, { color: currentTheme.TEXT_PRIMARY }]}>
            Performans Verisi Yok
          </Text>
          <Text style={[styles.emptySubtitle, { color: currentTheme.TEXT_SECONDARY }]}>
            Henüz performans analizi için yeterli veri bulunmuyor
          </Text>
        </View>
      </View>
    );
  }

  const chartData = prepareChartData();
  const overallScore = calculateOverallScore();
  const overallScoreColor = getScoreColor(overallScore);
  const overallScoreIcon = getScoreIcon(overallScore);
  const overallScoreMessage = getScoreMessage(overallScore);
  const currencySymbol = getCurrencySymbol();

  return (
    <View style={[styles.container, { backgroundColor: currentTheme.SURFACE }]}>
      {/* Header */}
      <View style={styles.header}>
        <MaterialIcons name="trending-up" size={20} color={currentTheme.PRIMARY} />
        <Text style={[styles.title, { color: currentTheme.TEXT_PRIMARY }]}>
          Bütçe Performansı
        </Text>
      </View>

      {/* Genel performans skoru */}
      <View style={[styles.overallScore, { backgroundColor: overallScoreColor + '20' }]}>
        <View style={styles.scoreHeader}>
          <MaterialIcons name={overallScoreIcon} size={24} color={overallScoreColor} />
          <Text style={[styles.scoreTitle, { color: currentTheme.TEXT_PRIMARY }]}>
            Genel Performans
          </Text>
        </View>
        
        <View style={styles.scoreContent}>
          <Text style={[styles.scoreValue, { color: overallScoreColor }]}>
            {overallScore}
          </Text>
          <Text style={[styles.scoreUnit, { color: overallScoreColor }]}>
            /100
          </Text>
        </View>
        
        <Text style={[styles.scoreMessage, { color: currentTheme.TEXT_SECONDARY }]}>
          {overallScoreMessage}
        </Text>
      </View>

      {/* Performans grafiği */}
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <View style={[styles.chartContainer, { height: chartHeight + 60 }]}>
          {/* Y ekseni etiketleri */}
          <View style={styles.yAxis}>
            {[0, 25, 50, 75, 100].map((percentage) => {
              const value = (chartData.maxValue * percentage) / 100;
              const y = chartHeight - (percentage / 100) * chartHeight;
              
              return (
                <View key={percentage} style={[styles.yAxisLabel, { top: y }]}>
                  <Text style={[styles.yAxisText, { color: currentTheme.TEXT_SECONDARY }]}>
                    {currencySymbol}{Math.round(value).toLocaleString('tr-TR')}
                  </Text>
                </View>
              );
            })}
          </View>

          {/* Çubuk grafik */}
          <View style={[styles.chartArea, { width: chartData.bars.length * 80, height: chartHeight }]}>
            {chartData.bars.map((bar, index) => (
              <View key={index} style={[styles.barGroup, { left: bar.x, width: bar.barWidth }]}>
                {/* Hedef çubuk */}
                <View
                  style={[
                    styles.targetBar,
                    {
                      height: bar.targetHeight,
                      bottom: 0,
                      backgroundColor: currentTheme.TEXT_SECONDARY + '40'
                    }
                  ]}
                />
                
                {/* Gerçek çubuk */}
                <View
                  style={[
                    styles.actualBar,
                    {
                      height: bar.actualHeight,
                      bottom: 0,
                      backgroundColor: bar.scoreColor
                    }
                  ]}
                />

                {/* Performans skoru */}
                <View style={[styles.scoreIndicator, { backgroundColor: bar.scoreColor }]}>
                  <Text style={[styles.scoreText, { color: currentTheme.WHITE }]}>
                    {bar.score}
                  </Text>
                </View>
              </View>
            ))}
          </View>

          {/* X ekseni etiketleri */}
          <View style={[styles.xAxis, { width: chartData.bars.length * 80 }]}>
            {chartData.bars.map((bar, index) => (
              <View
                key={index}
                style={[styles.xAxisLabel, { left: bar.x, width: bar.barWidth }]}
              >
                <Text style={[styles.xAxisText, { color: currentTheme.TEXT_SECONDARY }]}>
                  {formatPeriod(bar.period)}
                </Text>
              </View>
            ))}
          </View>
        </View>
      </ScrollView>

      {/* Legend */}
      <View style={styles.legend}>
        <View style={styles.legendItem}>
          <View style={[styles.legendColor, { backgroundColor: currentTheme.TEXT_SECONDARY + '40' }]} />
          <Text style={[styles.legendText, { color: currentTheme.TEXT_SECONDARY }]}>
            Hedef
          </Text>
        </View>
        
        <View style={styles.legendItem}>
          <View style={[styles.legendColor, { backgroundColor: currentTheme.PRIMARY }]} />
          <Text style={[styles.legendText, { color: currentTheme.TEXT_SECONDARY }]}>
            Gerçek
          </Text>
        </View>
      </View>

      {/* Performans özeti */}
      <View style={styles.summary}>
        <Text style={[styles.summaryTitle, { color: currentTheme.TEXT_PRIMARY }]}>
          Performans Özeti
        </Text>
        
        <View style={styles.summaryStats}>
          <View style={styles.summaryItem}>
            <Text style={[styles.summaryLabel, { color: currentTheme.TEXT_SECONDARY }]}>
              En İyi Dönem
            </Text>
            <Text style={[styles.summaryValue, { color: currentTheme.SUCCESS }]}>
              {performanceData.length > 0 
                ? formatPeriod(performanceData.reduce((best, current) => 
                    current.score > best.score ? current : best
                  ).period)
                : '-'
              }
            </Text>
          </View>

          <View style={styles.summaryItem}>
            <Text style={[styles.summaryLabel, { color: currentTheme.TEXT_SECONDARY }]}>
              Ortalama Skor
            </Text>
            <Text style={[styles.summaryValue, { color: overallScoreColor }]}>
              {overallScore}/100
            </Text>
          </View>

          <View style={styles.summaryItem}>
            <Text style={[styles.summaryLabel, { color: currentTheme.TEXT_SECONDARY }]}>
              Başarı Oranı
            </Text>
            <Text style={[styles.summaryValue, { color: currentTheme.PRIMARY }]}>
              %{Math.round((performanceData.filter(d => d.score >= 75).length / performanceData.length) * 100)}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '500',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: 'center',
  },
  overallScore: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    alignItems: 'center',
  },
  scoreHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  scoreTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  scoreContent: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 4,
  },
  scoreValue: {
    fontSize: 32,
    fontWeight: 'bold',
  },
  scoreUnit: {
    fontSize: 16,
    marginLeft: 4,
  },
  scoreMessage: {
    fontSize: 14,
  },
  chartContainer: {
    flexDirection: 'row',
    paddingLeft: 60,
  },
  yAxis: {
    position: 'absolute',
    left: 0,
    top: 0,
    width: 60,
  },
  yAxisLabel: {
    position: 'absolute',
    right: 8,
    transform: [{ translateY: -8 }],
  },
  yAxisText: {
    fontSize: 10,
    textAlign: 'right',
  },
  chartArea: {
    position: 'relative',
  },
  barGroup: {
    position: 'absolute',
    bottom: 0,
  },
  targetBar: {
    position: 'absolute',
    width: '40%',
    left: '10%',
    borderRadius: 2,
  },
  actualBar: {
    position: 'absolute',
    width: '40%',
    right: '10%',
    borderRadius: 2,
  },
  scoreIndicator: {
    position: 'absolute',
    top: -25,
    left: '50%',
    transform: [{ translateX: -12 }],
    width: 24,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scoreText: {
    fontSize: 10,
    fontWeight: 'bold',
  },
  xAxis: {
    position: 'relative',
    height: 30,
    marginTop: 10,
  },
  xAxisLabel: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
  },
  xAxisText: {
    fontSize: 10,
    textAlign: 'center',
  },
  legend: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 16,
    marginBottom: 16,
    gap: 20,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  legendText: {
    fontSize: 12,
  },
  summary: {
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
    paddingTop: 16,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 12,
    textAlign: 'center',
  },
  summaryStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: 'bold',
  },
});

export default BudgetPerformanceChart;
