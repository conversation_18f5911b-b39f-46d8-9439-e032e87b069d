import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { Colors } from '../constants/colors';

/**
 * Çubuk grafik bileşeni
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {Array} props.data - Grafik verileri
 * @returns {JSX.Element} Çubuk grafik bileşeni
 */
export default function BarChart({ data }) {
  if (!data || data.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>Veri bulunamadı</Text>
      </View>
    );
  }

  const maxValue = Math.max(...data.map(item => item.value));

  return (
    <ScrollView horizontal showsHorizontalScrollIndicator={false}>
      <View style={styles.container}>
        {data.map((item, index) => {
          const percentage = (item.value / maxValue) * 100;
          return (
            <View key={index} style={styles.barContainer}>
              <View style={styles.barLabelContainer}>
                <Text style={styles.barValue}>
                  ₺{item.value.toLocaleString('tr-TR')}
                </Text>
              </View>
              <View style={styles.barWrapper}>
                <View
                  style={[
                    styles.bar,
                    { height: `${percentage}%`, backgroundColor: Colors.PRIMARY }
                  ]}
                />
              </View>
              <Text style={styles.barLabel}>{item.label}</Text>
            </View>
          );
        })}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingVertical: 16,
    minWidth: '100%',
  },
  emptyContainer: {
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color:'#333333',
  },
  barContainer: {
    alignItems: 'center',
    marginHorizontal: 8,
    minWidth: 60,
  },
  barLabelContainer: {
    marginBottom: 8,
  },
  barValue: {
    fontSize: 12,
    color:'#333333',
  },
  barWrapper: {
    width: 40,
    height: 150,
    justifyContent: 'flex-end',
  },
  bar: {
    width: '100%',
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
  },
  barLabel: {
    marginTop: 8,
    fontSize: 12,
    color: '#333333',
    textAlign: 'center',
  },
});
