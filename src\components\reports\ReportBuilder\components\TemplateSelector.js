import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
} from 'react-native';
import { useTheme } from '../../../../context/ThemeContext';

/**
 * Şablon Seçici - Hazır rapor şablonları
 * Özelleştirilebilir ve kaydedilebilir şablonlar
 */
const TemplateSelector = ({
  selectedTemplate = null,
  onTemplateSelect,
  onLoadTemplate,
}) => {
  const { theme } = useTheme();
  const [selectedCategory, setSelectedCategory] = useState('all');

  /**
   * Şablon kategorileri
   */
  const getTemplateCategories = () => [
    { id: 'all', name: 'Tümü', icon: '📋' },
    { id: 'financial', name: 'Finansal', icon: '💰' },
    { id: 'dashboard', name: 'Dashboard', icon: '📊' },
    { id: 'comparison', name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>tırma', icon: '🔄' },
    { id: 'trend', name: 'Trend <PERSON>', icon: '📈' },
    { id: 'performance', name: 'Performans', icon: '🎯' },
    { id: 'custom', name: 'Özel', icon: '🎨' },
  ];

  /**
   * Hazır şablonlar
   */
  const getTemplates = () => [
    {
      id: 'monthly-report',
      name: 'Aylık Rapor',
      description: 'Ay sonu finansal özet raporu',
      category: 'financial',
      thumbnail: '📊',
      elements: [
        { type: 'kpi', name: 'Toplam Gelir', x: 20, y: 20 },
        { type: 'kpi', name: 'Toplam Gider', x: 240, y: 20 },
        { type: 'chart', name: 'Gelir-Gider Grafiği', x: 20, y: 120 },
        { type: 'table', name: 'Detay Listesi', x: 20, y: 300 },
      ],
      createdAt: '2024-01-15',
    },
    {
      id: 'expense-dashboard',
      name: 'Gider Dashboard',
      description: 'Giderlerin kategorik analizi',
      category: 'dashboard',
      thumbnail: '📈',
      elements: [
        { type: 'chart', name: 'Kategori Dağılımı', x: 20, y: 20 },
        { type: 'chart', name: 'Aylık Trend', x: 240, y: 20 },
        { type: 'kpi', name: 'En Yüksek Gider', x: 20, y: 200 },
        { type: 'table', name: 'Kategori Detayları', x: 20, y: 280 },
      ],
      createdAt: '2024-01-10',
    },
    {
      id: 'income-comparison',
      name: 'Gelir Karşılaştırması',
      description: 'Farklı dönemler arası gelir analizi',
      category: 'comparison',
      thumbnail: '🔄',
      elements: [
        { type: 'chart', name: 'Dönem Karşılaştırması', x: 20, y: 20 },
        { type: 'kpi', name: 'Büyüme Oranı', x: 240, y: 20 },
        { type: 'table', name: 'Detaylı Karşılaştırma', x: 20, y: 200 },
      ],
      createdAt: '2024-01-05',
    },
    {
      id: 'budget-performance',
      name: 'Bütçe Performansı',
      description: 'Bütçe hedefleri vs gerçekleşen',
      category: 'performance',
      thumbnail: '🎯',
      elements: [
        { type: 'chart', name: 'Bütçe vs Gerçekleşen', x: 20, y: 20 },
        { type: 'kpi', name: 'Hedef Başarı Oranı', x: 240, y: 20 },
        { type: 'table', name: 'Kategori Detayları', x: 20, y: 200 },
      ],
      createdAt: '2024-01-01',
    },
    {
      id: 'yearly-trend',
      name: 'Yıllık Trend',
      description: 'Yıl içi gelir-gider eğilimleri',
      category: 'trend',
      thumbnail: '📈',
      elements: [
        { type: 'chart', name: 'Yıllık Trend', x: 20, y: 20 },
        { type: 'chart', name: 'Mevsimsel Analiz', x: 240, y: 20 },
        { type: 'kpi', name: 'Yıllık Büyüme', x: 20, y: 200 },
      ],
      createdAt: '2023-12-30',
    },
    {
      id: 'custom-template',
      name: 'Özel Şablon',
      description: 'Kendi tasarımınızı oluşturun',
      category: 'custom',
      thumbnail: '🎨',
      elements: [],
      createdAt: '2024-01-20',
    },
  ];

  /**
   * Filtrelenmiş şablonlar
   */
  const getFilteredTemplates = () => {
    const templates = getTemplates();
    if (selectedCategory === 'all') {
      return templates;
    }
    return templates.filter(template => template.category === selectedCategory);
  };

  /**
   * Şablon seçimi
   */
  const handleTemplateSelect = (template) => {
    onTemplateSelect(template);
  };

  /**
   * Şablon yükleme
   */
  const handleLoadTemplate = (template) => {
    Alert.alert(
      'Şablon Yükle',
      `"${template.name}" şablonu yüklensin mi? Mevcut çalışmanız kaybolacak.`,
      [
        { text: 'İptal', style: 'cancel' },
        { text: 'Yükle', onPress: () => onLoadTemplate(template) },
      ]
    );
  };

  /**
   * Şablon önizleme
   */
  const handlePreviewTemplate = (template) => {
    Alert.alert(
      'Şablon Önizleme',
      `Şablon: ${template.name}\n\nAçıklama: ${template.description}\n\nWidget Sayısı: ${template.elements.length}`,
      [
        { text: 'Kapat', style: 'cancel' },
        { text: 'Kullan', onPress: () => handleLoadTemplate(template) },
      ]
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.SURFACE }]}>
        <Text style={[styles.headerTitle, { color: theme.TEXT_PRIMARY }]}>
          📋 Rapor Şablonları
        </Text>
        <Text style={[styles.headerSubtitle, { color: theme.TEXT_SECONDARY }]}>
          Hazır şablonlardan birini seçin veya özelleştirin
        </Text>
      </View>

      {/* Categories */}
      <View style={[styles.categoriesContainer, { backgroundColor: theme.SURFACE }]}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {getTemplateCategories().map((category) => (
            <TouchableOpacity
              key={category.id}
              style={[
                styles.categoryButton,
                {
                  backgroundColor: selectedCategory === category.id ? theme.PRIMARY : theme.BACKGROUND,
                },
              ]}
              onPress={() => setSelectedCategory(category.id)}
            >
              <Text style={styles.categoryIcon}>{category.icon}</Text>
              <Text
                style={[
                  styles.categoryName,
                  {
                    color: selectedCategory === category.id ? theme.SURFACE : theme.TEXT_PRIMARY,
                  },
                ]}
              >
                {category.name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Templates Grid */}
      <ScrollView style={styles.templatesContainer} showsVerticalScrollIndicator={false}>
        <View style={styles.templatesGrid}>
          {getFilteredTemplates().map((template) => (
            <View
              key={template.id}
              style={[
                styles.templateCard,
                {
                  backgroundColor: theme.SURFACE,
                  borderColor: selectedTemplate?.id === template.id ? theme.PRIMARY : theme.BORDER,
                  borderWidth: selectedTemplate?.id === template.id ? 2 : 1,
                },
              ]}
            >
              {/* Template Thumbnail */}
              <View style={[styles.templateThumbnail, { backgroundColor: theme.BACKGROUND }]}>
                <Text style={styles.templateThumbnailText}>{template.thumbnail}</Text>
              </View>

              {/* Template Info */}
              <View style={styles.templateInfo}>
                <Text style={[styles.templateName, { color: theme.TEXT_PRIMARY }]}>
                  {template.name}
                </Text>
                <Text style={[styles.templateDescription, { color: theme.TEXT_SECONDARY }]}>
                  {template.description}
                </Text>
                <Text style={[styles.templateMeta, { color: theme.TEXT_SECONDARY }]}>
                  {template.elements.length} widget • {template.createdAt}
                </Text>
              </View>

              {/* Template Actions */}
              <View style={styles.templateActions}>
                <TouchableOpacity
                  style={[styles.templateActionButton, { backgroundColor: theme.INFO }]}
                  onPress={() => handlePreviewTemplate(template)}
                >
                  <Text style={[styles.templateActionText, { color: theme.SURFACE }]}>
                    👁 Önizle
                  </Text>
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={[styles.templateActionButton, { backgroundColor: theme.PRIMARY }]}
                  onPress={() => handleLoadTemplate(template)}
                >
                  <Text style={[styles.templateActionText, { color: theme.SURFACE }]}>
                    🚀 Kullan
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
    opacity: 0.8,
  },
  categoriesContainer: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    gap: 4,
  },
  categoryIcon: {
    fontSize: 14,
  },
  categoryName: {
    fontSize: 12,
    fontWeight: '500',
  },
  templatesContainer: {
    flex: 1,
    padding: 16,
  },
  templatesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  templateCard: {
    width: '48%',
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  templateThumbnail: {
    height: 80,
    alignItems: 'center',
    justifyContent: 'center',
  },
  templateThumbnailText: {
    fontSize: 32,
  },
  templateInfo: {
    padding: 12,
  },
  templateName: {
    fontSize: 14,
    fontWeight: '700',
    marginBottom: 4,
  },
  templateDescription: {
    fontSize: 12,
    marginBottom: 6,
  },
  templateMeta: {
    fontSize: 10,
  },
  templateActions: {
    flexDirection: 'row',
    padding: 8,
    gap: 6,
  },
  templateActionButton: {
    flex: 1,
    paddingVertical: 6,
    borderRadius: 6,
    alignItems: 'center',
  },
  templateActionText: {
    fontSize: 10,
    fontWeight: '600',
  },
});

export default TemplateSelector;
