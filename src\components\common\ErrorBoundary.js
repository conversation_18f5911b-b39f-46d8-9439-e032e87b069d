import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';

/**
 * <PERSON>a sınırı bileşeni
 * @param {Object} props Component props
 * @param {React.ReactNode} props.children Alt bileşenler
 */
 const ErrorBoundary = ({ children }) => {
  const [hasError, setHasError] = useState(false);
  const [error, setError] = useState(null);

  if (hasError) {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>Bir Hata Oluştu</Text>
        <Text style={styles.message}>{error?.message || 'Beklenmeyen bir hata oluştu'}</Text>
        <TouchableOpacity 
          style={styles.button}
          onPress={() => setHasError(false)}
        >
          <Text style={styles.buttonText}>Tekrar Dene</Text>
        </TouchableOpacity>
      </View>
    );
  }

  try {
    return children;
  } catch (err) {
    setError(err);
    setHasError(true);
    return null;
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff'
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#e74c3c',
    marginBottom: 10
  },
  message: {
    fontSize: 16,
    color: '#34495e',
    textAlign: 'center',
    marginBottom: 20
  },
  button: {
    backgroundColor: '#3498db',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500'
  }
});

export default ErrorBoundary;