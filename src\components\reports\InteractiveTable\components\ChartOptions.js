import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Modal, ScrollView } from 'react-native';
import { ChartFactory, ChartTypeSelector } from '../../Charts/ChartComponents';

/**
 * Chart Options component for Interactive Table Builder
 * @param {Object} props - Component props
 * @param {boolean} props.isVisible - Visibility state
 * @param {Object} props.chartConfig - Chart configuration
 * @param {Function} props.onUpdateConfig - Chart config update handler
 * @param {Function} props.onClose - Close handler
 * @param {Object} props.theme - Theme object
 * @param {Array} props.tableData - Table data for chart
 * @param {Array} props.columns - Table columns
 * @returns {JSX.Element} Chart options component
 */
const ChartOptions = ({ 
  isVisible, 
  chartConfig, 
  onUpdateConfig, 
  onClose, 
  theme, 
  tableData = [], 
  columns = [] 
}) => {
  const [selectedChartType, setSelectedChartType] = useState(chartConfig?.type || 'line');
  const [showPreview, setShowPreview] = useState(false);

  /**
   * Get safe theme value
   * @param {string} property - Theme property
   * @param {string} fallback - Fallback value
   * @returns {string} Safe theme value
   */
  const getSafeThemeValue = (property, fallback) => {
    return theme?.[property] || theme?.colors?.[property.toLowerCase()] || fallback;
  };

  /**
   * Handle chart type selection
   * @param {string} type - Selected chart type
   */
  const handleChartTypeSelect = (type) => {
    setSelectedChartType(type);
    const newConfig = {
      ...chartConfig,
      type,
      enabled: true,
    };
    onUpdateConfig(newConfig);
  };

  /**
   * Generate chart data from table data
   * @param {string} type - Chart type
   * @returns {Object} Chart data
   */
  const generateChartData = (type) => {
    if (!tableData || tableData.length === 0) {
      return null;
    }

    const numericColumns = columns.filter(col => col.type === 'number');
    const dateColumns = columns.filter(col => col.type === 'date');
    const textColumns = columns.filter(col => col.type === 'text');

    switch (type) {
      case 'line':
      case 'bar':
        // Use first date column for labels and first numeric column for data
        const labelColumn = dateColumns[0] || textColumns[0];
        const dataColumn = numericColumns[0];
        
        if (!labelColumn || !dataColumn) {
          return {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{ data: [20, 45, 28, 80, 99, 43] }],
          };
        }

        const labels = tableData.map(row => {
          const value = row[labelColumn.key];
          if (labelColumn.type === 'date' && value) {
            return new Date(value).toLocaleDateString('tr-TR', { month: 'short' });
          }
          return String(value || '').substring(0, 10);
        }).slice(0, 10);

        const data = tableData.map(row => row[dataColumn.key] || 0).slice(0, 10);

        return {
          labels,
          datasets: [{ data }],
        };

      case 'pie':
        // Use first text column for names and first numeric column for values
        const nameColumn = textColumns[0];
        const valueColumn = numericColumns[0];
        
        if (!nameColumn || !valueColumn) {
          return [
            { name: 'Kategori A', population: 30, color: '#FF6B6B', legendFontColor: '#333', legendFontSize: 12 },
            { name: 'Kategori B', population: 25, color: '#4ECDC4', legendFontColor: '#333', legendFontSize: 12 },
            { name: 'Kategori C', population: 20, color: '#45B7D1', legendFontColor: '#333', legendFontSize: 12 },
          ];
        }

        const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'];
        return tableData.slice(0, 6).map((row, index) => ({
          name: String(row[nameColumn.key] || 'Kategori'),
          population: row[valueColumn.key] || 0,
          color: colors[index % colors.length],
          legendFontColor: getSafeThemeValue('TEXT_PRIMARY', '#333'),
          legendFontSize: 12,
        }));

      case 'progress':
        // Use first 4 numeric columns for progress data
        const progressColumns = numericColumns.slice(0, 4);
        if (progressColumns.length === 0) {
          return { labels: ['A', 'B', 'C', 'D'], data: [0.6, 0.8, 0.4, 0.9] };
        }

        return {
          labels: progressColumns.map(col => col.label || col.key),
          data: progressColumns.map(col => {
            const values = tableData.map(row => row[col.key] || 0);
            const avg = values.reduce((a, b) => a + b, 0) / values.length;
            return Math.min(avg / 100, 1); // Normalize to 0-1 range
          }),
        };

      default:
        return null;
    }
  };

  return (
    <Modal
      visible={isVisible}
      animationType="slide"
      transparent={false}
      onRequestClose={onClose}
    >
      <View style={[styles.container, { backgroundColor: getSafeThemeValue('BACKGROUND', '#ffffff') }]}>
        <View style={[styles.header, { backgroundColor: getSafeThemeValue('SURFACE', '#f9f9f9') }]}>
          <Text style={[styles.title, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
            📊 Grafik Seçenekleri
          </Text>
          <TouchableOpacity onPress={onClose}>
            <Text style={[styles.closeButton, { color: getSafeThemeValue('PRIMARY', '#007AFF') }]}>
              ✕
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <ChartTypeSelector
            selectedType={selectedChartType}
            onTypeSelect={handleChartTypeSelect}
            theme={theme}
          />

          <View style={styles.actions}>
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: getSafeThemeValue('PRIMARY', '#007AFF') }]}
              onPress={() => setShowPreview(!showPreview)}
            >
              <Text style={[styles.actionButtonText, { color: '#ffffff' }]}>
                {showPreview ? 'Önizlemeyi Gizle' : 'Grafik Önizleme'}
              </Text>
            </TouchableOpacity>
          </View>

          {showPreview && (
            <View style={styles.previewContainer}>
              <ChartFactory
                type={selectedChartType}
                data={generateChartData(selectedChartType)}
                theme={theme}
                title={`${selectedChartType} Grafik Önizleme`}
                config={{
                  decimalPlaces: 0,
                  propsForBackgroundLines: {
                    strokeWidth: 1,
                    stroke: getSafeThemeValue('BORDER', '#e0e0e0'),
                  },
                }}
              />
            </View>
          )}

          <View style={styles.infoContainer}>
            <Text style={[styles.infoTitle, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
              📈 Grafik Ayarları
            </Text>
            <Text style={[styles.infoText, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
              • Grafik türünü seçin
              • Tablo verileriniz otomatik olarak grafiğe dönüştürülecek
              • Sayısal sütunlar veri için, metin sütunlar etiket için kullanılacak
              • Grafik raporlarınızla birlikte kaydedilecek
            </Text>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  closeButton: {
    fontSize: 24,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  actions: {
    marginVertical: 16,
  },
  actionButton: {
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  previewContainer: {
    marginVertical: 16,
    borderRadius: 8,
    overflow: 'hidden',
  },
  infoContainer: {
    marginTop: 16,
    padding: 16,
    borderRadius: 8,
    backgroundColor: '#f0f8ff',
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    lineHeight: 20,
  },
});

export default ChartOptions;
