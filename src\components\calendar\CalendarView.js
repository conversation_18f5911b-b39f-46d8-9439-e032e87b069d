import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { format, addMonths, subMonths, startOfMonth, endOfMonth, eachDayOfInterval, getDay, isSameDay } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Colors } from '../../constants/colors';

/**
 * Takvim Görünümü Bileşeni
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {Date} props.selectedDate - Seçili tarih
 * @param {Function} props.onDateSelect - Tarih seçildiğinde çağrılacak fonksiyon
 * @param {Array} props.events - Etkinlikler
 * @param {Function} props.renderDay - Gün hücresini özelleştirmek için fonksiyon
 * @returns {JSX.Element} Takvim Görünümü Bileşeni
 */
const CalendarView = ({
  selectedDate = new Date(),
  onDateSelect,
  events = [],
  renderDay
}) => {
  const [currentMonth, setCurrentMonth] = useState(new Date(selectedDate));
  const [markedDates, setMarkedDates] = useState({});

  // Etkinlikleri işle
  useEffect(() => {
    const marked = {};

    events.forEach(event => {
      const eventDate = new Date(event.date);
      const dateString = format(eventDate, 'yyyy-MM-dd');

      if (!marked[dateString]) {
        marked[dateString] = [];
      }

      marked[dateString].push(event);
    });

    setMarkedDates(marked);
  }, [events]);

  // Önceki aya git
  const goToPreviousMonth = () => {
    setCurrentMonth(subMonths(currentMonth, 1));
  };

  // Sonraki aya git
  const goToNextMonth = () => {
    setCurrentMonth(addMonths(currentMonth, 1));
  };

  // Bugüne git
  const goToToday = () => {
    setCurrentMonth(new Date());
    onDateSelect && onDateSelect(new Date());
  };

  // Takvim günlerini oluştur
  const renderCalendar = () => {
    const monthStart = startOfMonth(currentMonth);
    const monthEnd = endOfMonth(currentMonth);
    const startDate = monthStart;
    const endDate = monthEnd;

    const dateFormat = 'd';
    const rows = [];

    const days = eachDayOfInterval({ start: startDate, end: endDate });

    // Haftanın günleri
    const dayNames = ['Pzt', 'Sal', 'Çar', 'Per', 'Cum', 'Cmt', 'Paz'];

    // Haftanın günleri başlığı
    const daysHeader = (
      <View style={styles.daysHeader}>
        {dayNames.map((day, index) => (
          <Text key={index} style={styles.dayName}>
            {day}
          </Text>
        ))}
      </View>
    );

    // Günleri haftalar halinde grupla
    let weeks = [];
    let week = Array(7).fill(null);

    // Ayın ilk gününün haftanın hangi günü olduğunu bul (0: Pazar, 1: Pazartesi, ...)
    // Pazartesi başlangıçlı takvim için düzenleme yap
    let dayOfWeek = getDay(startDate);
    dayOfWeek = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Pazartesi: 0, Salı: 1, ..., Pazar: 6

    // Önceki ayın günlerini boş bırak
    for (let i = 0; i < dayOfWeek; i++) {
      week[i] = null;
    }

    // Ayın günlerini ekle
    days.forEach((day, index) => {
      const dayOfWeek = getDay(day);
      const adjustedDayOfWeek = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Pazartesi: 0, Salı: 1, ..., Pazar: 6

      week[adjustedDayOfWeek] = day;

      // Haftanın son günü veya ayın son günü ise haftayı tamamla
      if (adjustedDayOfWeek === 6 || index === days.length - 1) {
        weeks.push([...week]);
        week = Array(7).fill(null);
      }
    });

    // Haftaları render et
    weeks.forEach((week, weekIndex) => {
      const days = week.map((day, dayIndex) => {
        if (!day) {
          return <View key={dayIndex} style={styles.emptyDay} />;
        }

        const dateString = format(day, 'yyyy-MM-dd');
        const isSelected = selectedDate && isSameDay(day, selectedDate);
        const isToday = isSameDay(day, new Date());
        const hasEvents = markedDates[dateString] && markedDates[dateString].length > 0;

        // Özel gün render fonksiyonu varsa kullan
        if (renderDay) {
          return React.cloneElement(
            renderDay({
              date: day,
              dateString,
              isSelected,
              isToday,
              events: markedDates[dateString] || [],
              onSelect: () => onDateSelect && onDateSelect(day)
            }),
            { key: dayIndex }
          );
        }

        // Varsayılan gün render
        return (
          <TouchableOpacity
            key={dayIndex}
            style={[
              styles.day,
              isSelected && styles.selectedDay,
              isToday && styles.today
            ]}
            onPress={() => onDateSelect && onDateSelect(day)}
          >
            <Text style={[
              styles.dayText,
              isSelected && styles.selectedDayText,
              isToday && styles.todayText
            ]}>
              {format(day, dateFormat)}
            </Text>

            {hasEvents && (
              <View style={styles.eventIndicator}>
                <Text style={styles.eventCount}>
                  {markedDates[dateString].length}
                </Text>
              </View>
            )}
          </TouchableOpacity>
        );
      });

      rows.push(
        <View key={weekIndex} style={styles.week}>
          {days}
        </View>
      );
    });

    return (
      <View style={styles.calendarContainer}>
        {daysHeader}
        {rows}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.headerButton}
          onPress={goToPreviousMonth}
        >
          <MaterialIcons name="chevron-left" size={24} color={Colors.GRAY_700} />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.monthYearContainer}
          onPress={goToToday}
        >
          <Text style={styles.monthYearText}>
            {format(currentMonth, 'MMMM yyyy', { locale: tr })}
          </Text>
          <MaterialIcons name="today" size={16} color={Colors.PRIMARY} style={styles.todayIcon} />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.headerButton}
          onPress={goToNextMonth}
        >
          <MaterialIcons name="chevron-right" size={24} color={Colors.GRAY_700} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.calendarScroll}>
        {renderCalendar()}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200,
  },
  headerButton: {
    padding: 4,
  },
  monthYearContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  monthYearText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.GRAY_800,
    textTransform: 'capitalize',
  },
  todayIcon: {
    marginLeft: 4,
  },
  calendarScroll: {
    maxHeight: 320,
  },
  calendarContainer: {
    padding: 8,
  },
  daysHeader: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 8,
    paddingVertical: 4,
  },
  dayName: {
    width: 40,
    textAlign: 'center',
    fontSize: 12,
    fontWeight: '500',
    color: Colors.GRAY_600,
  },
  week: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 8,
  },
  day: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 20,
  },
  emptyDay: {
    width: 40,
    height: 40,
  },
  selectedDay: {
    backgroundColor: Colors.PRIMARY,
  },
  today: {
    borderWidth: 1,
    borderColor: Colors.PRIMARY,
  },
  dayText: {
    fontSize: 14,
    color: Colors.GRAY_800,
  },
  selectedDayText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  todayText: {
    fontWeight: 'bold',
    color: Colors.PRIMARY,
  },
  eventIndicator: {
    position: 'absolute',
    bottom: 4,
    backgroundColor: Colors.SUCCESS,
    borderRadius: 10,
    paddingHorizontal: 4,
    paddingVertical: 1,
  },
  eventCount: {
    fontSize: 10,
    color: '#fff',
    fontWeight: 'bold',
  },
});

export default CalendarView;
