import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { useDataIntegration } from '../../../context/DataIntegrationContext';
import DataSourceSwitcher from './DataSourceSwitcher';

/**
 * Rapor Header Bileşeni
 * Rapor ekranı için başlık ve geri buton
 */
const ReportHeader = ({ 
  title, 
  subtitle, 
  onBackPress, 
  theme,
  showBackButton = true,
  showDataSourceSwitcher = true 
}) => {
  const [showDataSourceModal, setShowDataSourceModal] = useState(false);
  const { dataSource, hasRealData } = useDataIntegration();

  /**
   * Get safe theme value
   * @param {string} property - Theme property
   * @param {string} fallback - Fallback value
   * @returns {string} Safe theme value
   */
  const getSafeThemeValue = (property, fallback) => {
    return theme?.[property] || theme?.colors?.[property.toLowerCase()] || fallback;
  };

  /**
   * Get data source indicator
   * @returns {Object} Data source indicator
   */
  const getDataSourceIndicator = () => {
    switch (dataSource) {
      case 'real':
        return { icon: '💾', color: '#28a745', label: 'Gerçek' };
      case 'mock':
        return { icon: '🎭', color: '#ffc107', label: 'Örnek' };
      case 'auto':
        return { 
          icon: hasRealData ? '💾' : '🎭', 
          color: hasRealData ? '#28a745' : '#ffc107', 
          label: hasRealData ? 'Gerçek' : 'Örnek' 
        };
      default:
        return { icon: '❓', color: '#6c757d', label: 'Bilinmiyor' };
    }
  };

  const dataSourceIndicator = getDataSourceIndicator();

  return (
    <View style={[styles.header, { backgroundColor: getSafeThemeValue('SURFACE', '#f9f9f9') }]}>
      {showBackButton && (
        <TouchableOpacity 
          style={styles.backButton}
          onPress={onBackPress}
        >
          <Text style={[styles.backButtonText, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
            ← Geri
          </Text>
        </TouchableOpacity>
      )}
      
      <View style={styles.headerContent}>
        <Text style={[styles.headerTitle, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
          {title}
        </Text>
        {subtitle && (
          <Text style={[styles.headerSubtitle, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
            {subtitle}
          </Text>
        )}
      </View>

      {showDataSourceSwitcher && (
        <TouchableOpacity
          style={[styles.dataSourceButton, { backgroundColor: getSafeThemeValue('BACKGROUND', '#ffffff') }]}
          onPress={() => setShowDataSourceModal(true)}
        >
          <Text style={styles.dataSourceIcon}>{dataSourceIndicator.icon}</Text>
          <Text style={[styles.dataSourceLabel, { color: dataSourceIndicator.color }]}>
            {dataSourceIndicator.label}
          </Text>
        </TouchableOpacity>
      )}

      <DataSourceSwitcher
        isVisible={showDataSourceModal}
        onClose={() => setShowDataSourceModal(false)}
        theme={theme}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  backButton: {
    paddingRight: 16,
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  headerContent: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  headerSubtitle: {
    fontSize: 14,
    opacity: 0.8,
  },
  dataSourceButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  dataSourceIcon: {
    fontSize: 16,
    marginRight: 4,
  },
  dataSourceLabel: {
    fontSize: 12,
    fontWeight: '600',
  },
});

export default ReportHeader;
