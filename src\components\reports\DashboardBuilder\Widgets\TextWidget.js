import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

/**
 * Text Widget - Metin içeriği widget'ı
 */
const TextWidget = ({ widget, isPreviewMode, onUpdate, theme }) => {
  /**
   * <PERSON><PERSON><PERSON>li tema de<PERSON>
   * @param {string} property - <PERSON><PERSON>
   * @param {string} fallback - <PERSON><PERSON><PERSON><PERSON><PERSON>
   * @returns {string} <PERSON><PERSON><PERSON>li tema değeri
   */
  const getSafeThemeValue = (property, fallback) => {
    return theme[property] || theme.colors?.[property.toLowerCase()] || fallback;
  };

  return (
    <View style={[styles.container, { backgroundColor: getSafeThemeValue('BACKGROUND', '#fff') }]}>
      <Text style={[
        styles.textContent,
        {
          color: widget.config?.textColor || getSafeThemeValue('TEXT_PRIMARY', '#000'),
          fontSize: widget.config?.fontSize || 14,
          fontWeight: widget.config?.fontWeight || 'normal',
          textAlign: widget.config?.textAlign || 'left',
        }
      ]}>
        {widget.config?.content || 'Metin içeriği buraya gelecek...'}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 8,
  },
  textContent: {
    flex: 1,
    lineHeight: 20,
  },
});

export default TextWidget;
