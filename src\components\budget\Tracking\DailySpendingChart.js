/**
 * Günlük Harcama Grafiği Komponenti
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md - Stage 3, Phase 2
 * 
 * Günlük harcama trend grafiği (basit çizgi grafik)
 * Maksimum 250 satır - Tek sorumluluk prensibi
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Dimensions,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../../../context/ThemeContext';

const { width } = Dimensions.get('window');

/**
 * Günlük harcama grafiği komponenti
 * @param {Object} props - Component props
 * @param {Array} props.dailyData - Günlük harcama verileri [{date, amount, target}]
 * @param {string} props.currency - Para birimi ('TRY', 'USD', 'EUR')
 * @param {number} props.chartHeight - Grafik yüksekliği
 * @param {boolean} props.showTarget - Hede<PERSON> ç<PERSON> gö<PERSON>ilsin mi
 * @param {Object} props.theme - <PERSON><PERSON> ob<PERSON> (opsiyonel, context'ten alınır)
 */
const DailySpendingChart = ({ 
  dailyData = [], 
  currency = 'TRY',
  chartHeight = 200,
  showTarget = true,
  theme: propTheme 
}) => {
  const { theme, isDarkMode } = useTheme();
  const currentTheme = propTheme || theme;

  /**
   * Para birimi sembolü
   * @returns {string} Para birimi sembolü
   */
  const getCurrencySymbol = () => {
    switch (currency) {
      case 'USD':
        return '$';
      case 'EUR':
        return '€';
      default:
        return '₺';
    }
  };

  /**
   * Grafik verilerini hazırla
   * @returns {Object} Hazırlanmış grafik verileri
   */
  const prepareChartData = () => {
    if (dailyData.length === 0) {
      return {
        maxValue: 100,
        minValue: 0,
        points: [],
        targetPoints: []
      };
    }

    const amounts = dailyData.map(d => d.amount);
    const targets = dailyData.map(d => d.target || 0);
    const allValues = [...amounts, ...targets];
    
    const maxValue = Math.max(...allValues) * 1.1; // %10 padding
    const minValue = Math.min(...allValues, 0);
    
    const chartWidth = width - 32; // Padding
    const pointWidth = chartWidth / Math.max(dailyData.length - 1, 1);

    // Harcama noktaları
    const points = dailyData.map((data, index) => ({
      x: index * pointWidth,
      y: chartHeight - ((data.amount - minValue) / (maxValue - minValue)) * chartHeight,
      amount: data.amount,
      date: data.date,
      isOverTarget: data.target && data.amount > data.target
    }));

    // Hedef noktaları
    const targetPoints = showTarget ? dailyData.map((data, index) => ({
      x: index * pointWidth,
      y: chartHeight - ((data.target - minValue) / (maxValue - minValue)) * chartHeight,
      target: data.target
    })) : [];

    return {
      maxValue,
      minValue,
      points,
      targetPoints,
      chartWidth
    };
  };

  /**
   * SVG path oluştur
   * @param {Array} points - Nokta listesi
   * @returns {string} SVG path
   */
  const createPath = (points) => {
    if (points.length === 0) return '';
    
    let path = `M ${points[0].x} ${points[0].y}`;
    
    for (let i = 1; i < points.length; i++) {
      path += ` L ${points[i].x} ${points[i].y}`;
    }
    
    return path;
  };

  /**
   * Tarih formatı
   * @param {string} dateString - Tarih string
   * @returns {string} Formatlanmış tarih
   */
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return `${date.getDate()}/${date.getMonth() + 1}`;
  };

  /**
   * Y ekseni etiketleri oluştur
   * @param {number} maxValue - Maksimum değer
   * @param {number} minValue - Minimum değer
   * @returns {Array} Y ekseni etiketleri
   */
  const createYAxisLabels = (maxValue, minValue) => {
    const steps = 4;
    const stepValue = (maxValue - minValue) / steps;
    const labels = [];
    
    for (let i = 0; i <= steps; i++) {
      const value = minValue + (stepValue * i);
      const y = chartHeight - (i / steps) * chartHeight;
      labels.push({
        value: Math.round(value),
        y,
        label: Math.round(value).toLocaleString('tr-TR')
      });
    }
    
    return labels.reverse();
  };

  if (dailyData.length === 0) {
    return (
      <View style={[styles.container, { backgroundColor: currentTheme.SURFACE }]}>
        <View style={styles.emptyContainer}>
          <MaterialIcons name="show-chart" size={48} color={currentTheme.TEXT_SECONDARY} />
          <Text style={[styles.emptyTitle, { color: currentTheme.TEXT_PRIMARY }]}>
            Veri Bulunamadı
          </Text>
          <Text style={[styles.emptySubtitle, { color: currentTheme.TEXT_SECONDARY }]}>
            Henüz günlük harcama verisi bulunmuyor
          </Text>
        </View>
      </View>
    );
  }

  const chartData = prepareChartData();
  const yAxisLabels = createYAxisLabels(chartData.maxValue, chartData.minValue);
  const currencySymbol = getCurrencySymbol();

  return (
    <View style={[styles.container, { backgroundColor: currentTheme.SURFACE }]}>
      {/* Header */}
      <View style={styles.header}>
        <MaterialIcons name="show-chart" size={20} color={currentTheme.PRIMARY} />
        <Text style={[styles.title, { color: currentTheme.TEXT_PRIMARY }]}>
          Günlük Harcama Trendi
        </Text>
      </View>

      {/* Grafik */}
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <View style={[styles.chartContainer, { height: chartHeight + 40 }]}>
          {/* Y ekseni */}
          <View style={styles.yAxis}>
            {yAxisLabels.map((label, index) => (
              <View key={index} style={[styles.yAxisLabel, { top: label.y }]}>
                <Text style={[styles.yAxisText, { color: currentTheme.TEXT_SECONDARY }]}>
                  {currencySymbol}{label.label}
                </Text>
              </View>
            ))}
          </View>

          {/* Grafik alanı */}
          <View style={[styles.chartArea, { width: chartData.chartWidth, height: chartHeight }]}>
            {/* Grid çizgileri */}
            {yAxisLabels.map((label, index) => (
              <View
                key={index}
                style={[
                  styles.gridLine,
                  {
                    top: label.y,
                    width: chartData.chartWidth,
                    backgroundColor: currentTheme.BORDER
                  }
                ]}
              />
            ))}

            {/* Hedef çizgisi */}
            {showTarget && chartData.targetPoints.length > 0 && (
              <View style={styles.targetLine}>
                {chartData.targetPoints.map((point, index) => (
                  <View
                    key={index}
                    style={[
                      styles.targetPoint,
                      {
                        left: point.x - 2,
                        top: point.y - 2,
                        backgroundColor: currentTheme.WARNING
                      }
                    ]}
                  />
                ))}
              </View>
            )}

            {/* Harcama çizgisi */}
            <View style={styles.spendingLine}>
              {chartData.points.map((point, index) => (
                <View key={index}>
                  {/* Nokta */}
                  <View
                    style={[
                      styles.dataPoint,
                      {
                        left: point.x - 4,
                        top: point.y - 4,
                        backgroundColor: point.isOverTarget 
                          ? currentTheme.ERROR 
                          : currentTheme.PRIMARY
                      }
                    ]}
                  />
                  
                  {/* Çizgi (bir sonraki noktaya) */}
                  {index < chartData.points.length - 1 && (
                    <View
                      style={[
                        styles.connectionLine,
                        {
                          left: point.x,
                          top: point.y,
                          width: Math.sqrt(
                            Math.pow(chartData.points[index + 1].x - point.x, 2) +
                            Math.pow(chartData.points[index + 1].y - point.y, 2)
                          ),
                          transform: [{
                            rotate: `${Math.atan2(
                              chartData.points[index + 1].y - point.y,
                              chartData.points[index + 1].x - point.x
                            )}rad`
                          }],
                          backgroundColor: currentTheme.PRIMARY
                        }
                      ]}
                    />
                  )}
                </View>
              ))}
            </View>
          </View>

          {/* X ekseni etiketleri */}
          <View style={[styles.xAxis, { width: chartData.chartWidth }]}>
            {chartData.points.map((point, index) => (
              <View
                key={index}
                style={[styles.xAxisLabel, { left: point.x - 20 }]}
              >
                <Text style={[styles.xAxisText, { color: currentTheme.TEXT_SECONDARY }]}>
                  {formatDate(point.date)}
                </Text>
              </View>
            ))}
          </View>
        </View>
      </ScrollView>

      {/* Legend */}
      <View style={styles.legend}>
        <View style={styles.legendItem}>
          <View style={[styles.legendColor, { backgroundColor: currentTheme.PRIMARY }]} />
          <Text style={[styles.legendText, { color: currentTheme.TEXT_SECONDARY }]}>
            Harcama
          </Text>
        </View>
        
        {showTarget && (
          <View style={styles.legendItem}>
            <View style={[styles.legendColor, { backgroundColor: currentTheme.WARNING }]} />
            <Text style={[styles.legendText, { color: currentTheme.TEXT_SECONDARY }]}>
              Hedef
            </Text>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '500',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: 'center',
  },
  chartContainer: {
    flexDirection: 'row',
    paddingLeft: 60,
  },
  yAxis: {
    position: 'absolute',
    left: 0,
    top: 0,
    width: 60,
  },
  yAxisLabel: {
    position: 'absolute',
    right: 8,
    transform: [{ translateY: -8 }],
  },
  yAxisText: {
    fontSize: 10,
    textAlign: 'right',
  },
  chartArea: {
    position: 'relative',
  },
  gridLine: {
    position: 'absolute',
    height: 1,
    opacity: 0.3,
  },
  targetLine: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  targetPoint: {
    position: 'absolute',
    width: 4,
    height: 4,
    borderRadius: 2,
  },
  spendingLine: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  dataPoint: {
    position: 'absolute',
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  connectionLine: {
    position: 'absolute',
    height: 2,
    transformOrigin: '0 50%',
  },
  xAxis: {
    position: 'relative',
    height: 30,
    marginTop: 10,
  },
  xAxisLabel: {
    position: 'absolute',
    width: 40,
    alignItems: 'center',
  },
  xAxisText: {
    fontSize: 10,
    textAlign: 'center',
  },
  legend: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 16,
    gap: 20,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  legendText: {
    fontSize: 12,
  },
});

export default DailySpendingChart;
