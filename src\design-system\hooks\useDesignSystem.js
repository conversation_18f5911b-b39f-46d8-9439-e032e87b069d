import { useContext } from 'react';
import { useColorScheme } from 'react-native';
import { useAppContext } from '../../context/AppContext';
import tokens from '../tokens';

/**
 * Design System Hook
 * Tema ve design token'la<PERSON><PERSON><PERSON> er<PERSON>
 */
export const useDesignSystem = () => {
  const { isDarkMode, themePreference } = useAppContext();
  const systemColorScheme = useColorScheme();

  // Aktif tema belirleme
  const getActiveTheme = () => {
    if (themePreference === 'system') {
      return systemColorScheme === 'dark' ? tokens.darkTheme : tokens.lightTheme;
    }
    return isDarkMode ? tokens.darkTheme : tokens.lightTheme;
  };

  const activeTheme = getActiveTheme();

  // Responsive yardımcıları
  const responsive = tokens.responsive;

  // Spacing yardımcıları
  const spacing = (value) => {
    if (typeof value === 'number') {
      return tokens.spacing[value] || value;
    }
    return tokens.spacing[value] || 0;
  };

  // Color yardımcıları
  const getColor = (colorPath, fallback = '#000000') => {
    if (!colorPath) return fallback;

    // Theme color mu kontrol et
    if (colorPath.startsWith('theme.')) {
      const path = colorPath.replace('theme.', '').split('.');
      let color = activeTheme.colors;
      
      for (const key of path) {
        color = color?.[key];
        if (!color) break;
      }
      
      return color || fallback;
    }

    // Token color mu kontrol et
    if (colorPath.includes('.')) {
      const [colorName, shade] = colorPath.split('.');
      return tokens.colors[colorName]?.[shade] || fallback;
    }

    // Direct color
    return tokens.colors[colorPath] || colorPath || fallback;
  };

  // Typography yardımcıları
  const getTypography = (variant = 'body') => {
    return tokens.variants.text[variant] || tokens.variants.text.body;
  };

  // Shadow yardımcıları
  const getShadow = (level = 'sm') => {
    return tokens.shadows[level] || tokens.shadows.sm;
  };

  // Border radius yardımcıları
  const getBorderRadius = (size = 'md') => {
    return tokens.borderRadius[size] || tokens.borderRadius.md;
  };

  // Animation yardımcıları
  const getDuration = (speed = 'normal') => {
    return tokens.duration[speed] || tokens.duration.normal;
  };

  // Breakpoint yardımcıları
  const isBreakpoint = (breakpoint) => {
    return tokens.responsive.screenWidth >= tokens.breakpoints[breakpoint];
  };

  // Component variant yardımcıları
  const getVariant = (component, variant) => {
    return tokens.variants[component]?.[variant] || {};
  };

  return {
    // Theme
    theme: activeTheme,
    isDarkMode,
    themePreference,

    // Tokens
    tokens,
    colors: tokens.colors,
    typography: tokens.typography,
    spacing: tokens.spacing,
    borderRadius: tokens.borderRadius,
    shadows: tokens.shadows,
    breakpoints: tokens.breakpoints,
    zIndex: tokens.zIndex,
    duration: tokens.duration,
    easing: tokens.easing,
    variants: tokens.variants,

    // Responsive
    responsive,
    isBreakpoint,

    // Helper functions
    spacing,
    getColor,
    getTypography,
    getShadow,
    getBorderRadius,
    getDuration,
    getVariant,

    // Convenience getters
    primaryColor: getColor('theme.primary'),
    backgroundColor: getColor('theme.background'),
    textColor: getColor('theme.text.primary'),
    secondaryTextColor: getColor('theme.text.secondary'),
    borderColor: getColor('theme.border'),
    cardColor: getColor('theme.card'),
    surfaceColor: getColor('theme.surface'),
  };
};
