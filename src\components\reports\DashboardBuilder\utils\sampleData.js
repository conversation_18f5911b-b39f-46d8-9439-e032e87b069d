import { ChartFactory } from '../../Charts/ChartComponents';

/**
 * Sample chart data generator for dashboard testing
 */
export const generateSampleChartData = (type = 'line') => {
  const currentDate = new Date();
  const months = ['Oca', 'Şub', 'Mar', 'Nis', 'May', 'Haz'];
  
  switch (type) {
    case 'line':
      return {
        labels: months,
        datasets: [{
          data: [2400, 1398, 9800, 3908, 4800, 3800],
        }],
      };
      
    case 'bar':
      return {
        labels: ['Gıda', 'Ulaşım', '<PERSON><PERSON><PERSON>ce', 'Sağlık', 'Diğer'],
        datasets: [{
          data: [3500, 2800, 1200, 800, 1500],
        }],
      };
      
    case 'pie':
      return [
        { name: '<PERSON><PERSON><PERSON>', population: 35000, color: '#28a745', legendFontColor: '#333', legendFontSize: 12 },
        { name: 'Gider', population: 25000, color: '#dc3545', legendFontColor: '#333', legendFontSize: 12 },
        { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>m', population: 15000, color: '#ffc107', legendFontColor: '#333', legendFontSize: 12 },
        { name: '<PERSON><PERSON><PERSON><PERSON>', population: 10000, color: '#17a2b8', legendFontColor: '#333', legendFontSize: 12 },
      ];
      
    case 'progress':
      return {
        labels: ['Hedef', 'Gerçekleşen', 'Tasarruf', 'Yatırım'],
        data: [0.8, 0.6, 0.4, 0.9],
      };
      
    default:
      return null;
  }
};

/**
 * Sample widgets for dashboard testing
 */
export const getSampleWidgets = () => [
  {
    id: 'sample-line-chart',
    type: 'chart',
    title: 'Aylık Trend',
    position: { x: 10, y: 10 },
    size: { width: 300, height: 200 },
    config: {
      chartType: 'line',
      title: 'Aylık Gelir-Gider Trendi',
      dataSource: 'transactions',
    },
    data: generateSampleChartData('line'),
  },
  {
    id: 'sample-bar-chart',
    type: 'chart',
    title: 'Kategori Dağılımı',
    position: { x: 320, y: 10 },
    size: { width: 300, height: 200 },
    config: {
      chartType: 'bar',
      title: 'Kategori Bazlı Harcamalar',
      dataSource: 'transactions',
    },
    data: generateSampleChartData('bar'),
  },
  {
    id: 'sample-pie-chart',
    type: 'chart',
    title: 'Finansal Dağılım',
    position: { x: 10, y: 220 },
    size: { width: 300, height: 200 },
    config: {
      chartType: 'pie',
      title: 'Finansal Dağılım',
      dataSource: 'summary',
    },
    data: generateSampleChartData('pie'),
  },
  {
    id: 'sample-kpi-1',
    type: 'kpi',
    title: 'Toplam Gelir',
    position: { x: 320, y: 220 },
    size: { width: 140, height: 80 },
    config: {
      value: 45000,
      label: 'Toplam Gelir',
      format: 'currency',
      trend: 'up',
      trendValue: 12.5,
    },
  },
  {
    id: 'sample-kpi-2',
    type: 'kpi',
    title: 'Toplam Gider',
    position: { x: 470, y: 220 },
    size: { width: 140, height: 80 },
    config: {
      value: 32000,
      label: 'Toplam Gider',
      format: 'currency',
      trend: 'down',
      trendValue: 5.2,
    },
  },
  {
    id: 'sample-kpi-3',
    type: 'kpi',
    title: 'Net Tasarruf',
    position: { x: 320, y: 310 },
    size: { width: 140, height: 80 },
    config: {
      value: 13000,
      label: 'Net Tasarruf',
      format: 'currency',
      trend: 'up',
      trendValue: 8.7,
    },
  },
  {
    id: 'sample-text-1',
    type: 'text',
    title: 'Dashboard Başlığı',
    position: { x: 10, y: 430 },
    size: { width: 600, height: 60 },
    config: {
      text: '📊 Finansal Dashboard - Anlık Durum',
      fontSize: 18,
      fontWeight: 'bold',
      textAlign: 'center',
      backgroundColor: '#f8f9fa',
    },
  },
];
