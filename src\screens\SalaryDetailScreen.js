import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  FlatList,
  Share
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import * as salaryService from '../services/salaryService';
import { formatCurrency } from '../utils/formatters';
import CurrencyEquivalent from '../components/CurrencyEquivalent';

/**
 * Maaş Detay Ekranı
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @param {Object} props.route - Route nesnesi
 * @returns {JSX.Element} Maaş Detay Ekranı
 */
export default function SalaryDetailScreen({ navigation, route }) {
  const db = useSQLiteContext();
  const { salaryId } = route.params;

  // Durum değişkenleri
  const [loading, setLoading] = useState(true);
  const [salary, setSalary] = useState(null);

  // Verileri yükle
  const loadData = useCallback(async () => {
    try {
      setLoading(true);

      // Maaş detaylarını getir
      const salaryDetails = await salaryService.getSalaryDetails(db, salaryId);
      setSalary(salaryDetails);

      setLoading(false);
    } catch (error) {
      console.error('Maaş detayları yükleme hatası:', error);
      Alert.alert('Hata', 'Maaş detayları yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  }, [salaryId, db]);

  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [loadData])
  );

  // Maaşı düzenle (düzenli gelir olarak)
  const editSalary = () => {
    // Düzenli gelir formuna salary verisini uyumlu bir şekilde aktararak yönlendir
    const regularIncomeData = {
      id: salary?.id,
      title: salary?.name,
      amount: salary?.amount,
      currency_code: salary?.currency,
      recurrence_type: 'monthly',
      payment_day: salary?.payment_day,
      notes: salary?.notes,
      is_salary: true
    };
    navigation.navigate('RegularIncomeForm', { regularIncomeData });
  };

  // Maaşı sil
  const deleteSalary = () => {
    Alert.alert(
      'Maaşı Sil',
      'Bu maaşı silmek istediğinize emin misiniz? Bu işlem geri alınamaz.',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await salaryService.deleteSalary(db, salaryId);
              Alert.alert('Başarılı', 'Maaş başarıyla silindi.');
              navigation.goBack();
            } catch (error) {
              console.error('Maaş silme hatası:', error);
              Alert.alert('Hata', 'Maaş silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };

  // Maaşı paylaş
  const shareSalary = async () => {
    if (!salary) return;

    try {
      // Maaş özeti oluştur
      let message = `Maaş: ${salary.name}\n`;
      message += `Tutar: ${formatCurrency(salary.amount, salary.currency)}\n`;
      message += `Ödeme Günü: Her ayın ${salary.payment_day}. günü\n`;

      if (salary.category_name) {
        message += `Kategori: ${salary.category_name}\n`;
      }

      if (salary.tax_rate > 0) {
        message += `Vergi Oranı: %${salary.tax_rate}\n`;
      }

      if (salary.notes) {
        message += `\nNotlar: ${salary.notes}\n`;
      }

      if (salary.payments && salary.payments.length > 0) {
        message += '\nSon Ödemeler:\n';

        salary.payments.slice(0, 5).forEach(payment => {
          message += `- ${new Date(payment.payment_date).toLocaleDateString('tr-TR')}: ${formatCurrency(payment.amount, payment.currency)} (${payment.is_paid ? 'Ödendi' : 'Ödenmedi'})\n`;
        });
      }

      // Paylaşım dialogunu aç
      await Share.share({
        message,
        title: `${salary.name} Maaş Detayları`
      });
    } catch (error) {
      console.error('Maaş paylaşım hatası:', error);
      Alert.alert('Hata', 'Maaş paylaşılırken bir hata oluştu.');
    }
  };

  // Yeni ödeme ekle
  const addNewPayment = () => {
    if (!salary) return;

    // Bugünün tarihini YYYY-MM-DD formatında al
    const today = new Date().toISOString().split('T')[0];

    Alert.prompt(
      'Ödeme Tarihi',
      'Ödeme tarihini YYYY-MM-DD formatında girin:',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Devam',
          onPress: async (paymentDate) => {
            if (!paymentDate || !paymentDate.match(/^\d{4}-\d{2}-\d{2}$/)) {
              Alert.alert('Hata', 'Lütfen geçerli bir tarih girin (YYYY-MM-DD).');
              return;
            }

            try {
              await salaryService.createSalaryPayment(db, {
                salary_id: salary.id,
                amount: salary.amount,
                currency: salary.currency,
                payment_date: paymentDate,
                is_paid: 0
              });

              Alert.alert('Başarılı', 'Ödeme başarıyla eklendi.');
              loadData();
            } catch (error) {
              console.error('Ödeme ekleme hatası:', error);
              Alert.alert('Hata', 'Ödeme eklenirken bir hata oluştu.');
            }
          }
        }
      ],
      'plain-text',
      today
    );
  };

  // Ödemeyi işleme al
  const processPayment = async (paymentId) => {
    try {
      await salaryService.processSalaryPayment(db, paymentId);
      Alert.alert('Başarılı', 'Ödeme başarıyla işlendi.');
      loadData();
    } catch (error) {
      console.error('Ödeme işleme hatası:', error);
      Alert.alert('Hata', 'Ödeme işlenirken bir hata oluştu.');
    }
  };

  // Ödeme işleme onayı
  const confirmProcessPayment = (payment) => {
    Alert.alert(
      'Ödemeyi İşle',
      `${new Date(payment.payment_date).toLocaleDateString('tr-TR')} tarihli ${formatCurrency(payment.amount, payment.currency)} tutarındaki ödemeyi işlemek istediğinize emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'İşle',
          style: 'default',
          onPress: () => processPayment(payment.id)
        }
      ]
    );
  };

  // Ödemeyi sil
  const deletePayment = async (paymentId) => {
    try {
      await salaryService.deleteSalaryPayment(db, paymentId);
      Alert.alert('Başarılı', 'Ödeme başarıyla silindi.');
      loadData();
    } catch (error) {
      console.error('Ödeme silme hatası:', error);
      Alert.alert('Hata', 'Ödeme silinirken bir hata oluştu.');
    }
  };

  // Ödeme silme onayı
  const confirmDeletePayment = (payment) => {
    // Eğer ödeme işlendiyse silme
    if (payment.transaction_exists) {
      Alert.alert('Uyarı', 'Bu ödeme işlenmiş ve bir işlem kaydı oluşturulmuş. Önce ilgili işlemi silmeniz gerekiyor.');
      return;
    }

    Alert.alert(
      'Ödemeyi Sil',
      `${new Date(payment.payment_date).toLocaleDateString('tr-TR')} tarihli ${formatCurrency(payment.amount, payment.currency)} tutarındaki ödemeyi silmek istediğinize emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: () => deletePayment(payment.id)
        }
      ]
    );
  };

  // Ödeme öğesi render fonksiyonu
  const renderPaymentItem = ({ item }) => (
    <View style={styles.paymentItem}>
      <View style={styles.paymentInfo}>
        <Text style={styles.paymentDate}>
          {new Date(item.payment_date).toLocaleDateString('tr-TR')}
        </Text>
        <Text style={styles.paymentAmount}>
          {formatCurrency(item.amount, item.currency)}
        </Text>
        {/* Döviz Karşılıkları */}
        {(item.usd_equivalent > 0 || item.eur_equivalent > 0 || item.custom_equivalent > 0) && (
          <CurrencyEquivalent
            amount={item.amount}
            currency={item.currency}
            usdEquivalent={item.usd_equivalent}
            eurEquivalent={item.eur_equivalent}
            customCurrency={item.custom_currency}
            customEquivalent={item.custom_equivalent}
            size="small"
            style={styles.paymentEquivalents}
          />
        )}
      </View>

      <View style={styles.paymentActions}>
        {item.is_paid === 1 ? (
          <View style={styles.paidBadge}>
            <MaterialIcons name="check-circle" size={16} color={Colors.SUCCESS} />
            <Text style={styles.paidBadgeText}>Ödendi</Text>
          </View>
        ) : (
          <TouchableOpacity
            style={styles.paymentAction}
            onPress={() => confirmProcessPayment(item)}
          >
            <MaterialIcons name="check-circle-outline" size={20} color={Colors.PRIMARY} />
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={styles.paymentAction}
          onPress={() => confirmDeletePayment(item)}
        >
          <MaterialIcons name="delete-outline" size={20} color={Colors.DANGER} />
        </TouchableOpacity>
      </View>
    </View>
  );

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={styles.loadingText}>Maaş detayları yükleniyor...</Text>
      </View>
    );
  }

  if (!salary) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <MaterialIcons name="error-outline" size={48} color={Colors.DANGER} />
        <Text style={styles.errorText}>Maaş bulunamadı.</Text>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>Geri Dön</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{salary.name}</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerAction}
            onPress={shareSalary}
          >
            <MaterialIcons name="share" size={24} color="#fff" />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerAction}
            onPress={editSalary}
          >
            <MaterialIcons name="edit" size={24} color="#fff" />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerAction}
            onPress={deleteSalary}
          >
            <MaterialIcons name="delete" size={24} color="#fff" />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.salaryInfoCard}>
          <View style={styles.salaryAmount}>
            <Text style={styles.salaryAmountValue}>
              {formatCurrency(salary.amount, salary.currency)}
            </Text>
            <Text style={styles.salaryAmountLabel}>Aylık Maaş</Text>

            {/* Döviz Karşılıkları */}
            {(salary.usd_equivalent > 0 || salary.eur_equivalent > 0 || salary.custom_equivalent > 0) && (
              <CurrencyEquivalent
                amount={salary.amount}
                currency={salary.currency}
                usdEquivalent={salary.usd_equivalent}
                eurEquivalent={salary.eur_equivalent}
                customCurrency={salary.custom_currency}
                customEquivalent={salary.custom_equivalent}
                size="medium"
                style={styles.currencyEquivalents}
              />
            )}
          </View>

          <View style={styles.salaryDetails}>
            <View style={styles.salaryDetailItem}>
              <MaterialIcons name="event" size={20} color={Colors.GRAY_600} />
              <Text style={styles.salaryDetailText}>
                Her ayın {salary.payment_day}. günü
              </Text>
            </View>

            {salary.category_name && (
              <View style={styles.salaryDetailItem}>
                <MaterialIcons
                  name={salary.category_icon || "category"}
                  size={20}
                  color={salary.category_color || Colors.PRIMARY}
                />
                <Text style={styles.salaryDetailText}>
                  {salary.category_name}
                </Text>
              </View>
            )}

            {salary.tax_rate > 0 && (
              <TouchableOpacity
                style={styles.salaryDetailItem}
                onPress={() => Alert.alert(
                  "Vergi Oranı",
                  `Bu maaş için belirtilen gelir vergisi oranı %${salary.tax_rate}. Bu oran, brüt maaşınızdan kesilen vergi miktarını hesaplamak için kullanılır. Brüt maaşınız ${formatCurrency(salary.amount, salary.currency)} ise, yaklaşık ${formatCurrency(salary.amount * (salary.tax_rate / 100), salary.currency)} vergi kesintisi yapılır.`
                )}
              >
                <MaterialIcons name="receipt" size={20} color={Colors.GRAY_600} />
                <Text style={styles.salaryDetailText}>
                  Vergi Oranı: %{salary.tax_rate}
                </Text>
                <MaterialIcons name="info-outline" size={16} color={Colors.PRIMARY} style={{marginLeft: 4}} />
              </TouchableOpacity>
            )}

            <View style={styles.salaryDetailItem}>
              <MaterialIcons
                name={salary.is_active === 1 ? "check-circle" : "cancel"}
                size={20}
                color={salary.is_active === 1 ? Colors.SUCCESS : Colors.DANGER}
              />
              <Text style={styles.salaryDetailText}>
                {salary.is_active === 1 ? 'Aktif' : 'Pasif'}
              </Text>
            </View>
          </View>

          {salary.notes && (
            <View style={styles.salaryNotes}>
              <Text style={styles.salaryNotesLabel}>Notlar:</Text>
              <Text style={styles.salaryNotesText}>{salary.notes}</Text>
            </View>
          )}
        </View>

        <View style={styles.paymentsCard}>
          <View style={styles.paymentsHeader}>
            <Text style={styles.paymentsTitle}>Ödemeler</Text>
            <TouchableOpacity
              style={styles.addPaymentButton}
              onPress={addNewPayment}
            >
              <MaterialIcons name="add" size={20} color="#fff" />
              <Text style={styles.addPaymentButtonText}>Ödeme Ekle</Text>
            </TouchableOpacity>
          </View>

          {salary.payments && salary.payments.length > 0 ? (
            <FlatList
              data={salary.payments}
              keyExtractor={(item) => item.id.toString()}
              renderItem={renderPaymentItem}
              scrollEnabled={false}
            />
          ) : (
            <View style={styles.emptyPayments}>
              <MaterialIcons name="payment" size={48} color={Colors.GRAY_400} />
              <Text style={styles.emptyPaymentsText}>
                Henüz ödeme kaydı bulunmuyor.
              </Text>
              <TouchableOpacity
                style={styles.emptyPaymentsButton}
                onPress={addNewPayment}
              >
                <Text style={styles.emptyPaymentsButtonText}>Ödeme Ekle</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa'
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.GRAY_600
  },
  errorText: {
    marginTop: 16,
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.GRAY_800
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 16,
    paddingHorizontal: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2
  },
  backButton: {
    padding: 8,
    marginRight: 8
  },
  headerTitle: {
    flex: 1,
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    marginLeft: 8,
    marginRight: 8
  },
  headerActions: {
    flexDirection: 'row'
  },
  headerAction: {
    padding: 8,
    marginLeft: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
  },
  content: {
    flex: 1,
    padding: 16
  },
  salaryInfoCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1
  },
  salaryAmount: {
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200
  },
  salaryAmountValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.SUCCESS,
    marginBottom: 4
  },
  salaryAmountLabel: {
    fontSize: 14,
    color: Colors.GRAY_600
  },
  currencyEquivalents: {
    marginTop: 8
  },
  salaryDetails: {
    marginBottom: 16
  },
  salaryDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8
  },
  salaryDetailText: {
    fontSize: 16,
    color: Colors.GRAY_700,
    marginLeft: 8
  },
  salaryNotes: {
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.GRAY_200
  },
  salaryNotesLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.GRAY_700,
    marginBottom: 8
  },
  salaryNotesText: {
    fontSize: 14,
    color: Colors.GRAY_600,
    lineHeight: 20
  },
  paymentsCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1
  },
  paymentsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16
  },
  paymentsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.GRAY_800
  },
  addPaymentButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8
  },
  addPaymentButtonText: {
    color: '#fff',
    fontWeight: '500',
    marginLeft: 4
  },
  emptyPayments: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 32
  },
  emptyPaymentsText: {
    fontSize: 16,
    color: Colors.GRAY_600,
    marginTop: 16,
    marginBottom: 16
  },
  emptyPaymentsButton: {
    backgroundColor: Colors.PRIMARY,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8
  },
  emptyPaymentsButtonText: {
    color: '#fff',
    fontWeight: '500'
  },
  paymentItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200
  },
  paymentInfo: {
    flex: 1
  },
  paymentDate: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.GRAY_800,
    marginBottom: 4
  },
  paymentAmount: {
    fontSize: 14,
    color: Colors.GRAY_600
  },
  paymentEquivalents: {
    marginTop: 4
  },
  paymentActions: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  paymentAction: {
    padding: 8,
    marginLeft: 4
  },
  paidBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.SUCCESS_LIGHT,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginRight: 8
  },
  paidBadgeText: {
    fontSize: 12,
    color: Colors.SUCCESS,
    fontWeight: '500',
    marginLeft: 4
  },
  backButtonText: {
    fontSize: 16,
    color: Colors.PRIMARY,
    fontWeight: '500',
    marginTop: 16
  }
});
