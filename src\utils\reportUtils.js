/**
 * Rapor Utility Fonksiyonları
 * Rapor ekranı için yardımcı fonksiyonlar
 */

/**
 * <PERSON><PERSON><PERSON><PERSON>ı rapor şablonları listesi
 * @param {Object} theme - <PERSON><PERSON> renkleri
 * @returns {Array} Hızlı şablon listesi
 */
export const getQuickTemplates = (theme) => [
  {
    id: 'monthly-summary',
    name: '<PERSON><PERSON><PERSON><PERSON> Ö<PERSON>',
    description: '<PERSON><PERSON><PERSON>, gider ve kategori dağılımı',
    icon: '📊',
    color: theme.PRIMARY,
    estimatedTime: '2 dk',
  },
  {
    id: 'category-breakdown',
    name: '<PERSON><PERSON><PERSON>',
    description: '<PERSON><PERSON>lı kategori dağılım raporu',
    icon: '🏷️',
    color: theme.SUCCESS,
    estimatedTime: '3 dk',
  },
  {
    id: 'cash-flow',
    name: '<PERSON><PERSON><PERSON>',
    description: 'Aylık nakit akış analizi',
    icon: '💰',
    color: theme.INFO,
    estimatedTime: '4 dk',
  },
  {
    id: 'budget-analysis',
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>zi',
    description: '<PERSON><PERSON><PERSON><PERSON><PERSON> vs gerçekleş<PERSON> karşılaştırma',
    icon: '📈',
    color: theme.WARNING,
    estimatedTime: '3 dk',
  },
  {
    id: 'overtime-income',
    name: 'Mesai Analizi',
    description: 'Mesai gelir detay raporu',
    icon: '⏰',
    color: theme.ERROR,
    estimatedTime: '2 dk',
  },
  {
    id: 'custom-report-builder',
    name: 'Özel Rapor Editörü',
    description: 'Sürükle-bırak ile özel rapor tasarla',
    icon: '🎨',
    color: theme.ACCENT,
    estimatedTime: '10+ dk',
  },
  {
    id: 'interactive-table',
    name: 'İnteraktif Tablo',
    description: 'Dinamik tablo oluşturucu',
    icon: '📋',
    color: theme.SECONDARY,
    estimatedTime: '5 dk',
  },
  {
    id: 'tax-report',
    name: 'Vergi Raporu',
    description: 'Vergi hazırlığı için özet',
    icon: '📄',
    color: '#9C27B0',
    estimatedTime: '5 dk',
  },
];

/**
 * Rapor kategorileri listesi
 * @param {Object} reportStats - Rapor istatistikleri
 * @returns {Array} Kategori listesi
 */
export const getReportCategories = (reportStats = {}) => {
  // Güvenlik kontrolü - default değerler
  const safeStats = {
    totalReports: 0,
    favoritesCount: 0,
    scheduledCount: 0,
    ...reportStats
  };

  return [
    { id: 'all', name: 'Hepsi', count: safeStats.totalReports },
    { id: 'favorites', name: 'Favoriler', count: safeStats.favoritesCount },
    { id: 'recent', name: 'Son Kullanılanlar', count: 5 },
    { id: 'scheduled', name: 'Otomatik', count: safeStats.scheduledCount },
    { id: 'shared', name: 'Paylaşılanlar', count: 2 },
  ];
};

/**
 * Mock rapor verilerini getir
 * @returns {Object} Mock rapor verileri
 */
export const getMockReportData = () => ({
  recentReports: [
    {
      id: 1,
      name: 'Aylık Gelir-Gider Raporu',
      type: 'financial',
      lastModified: '2024-01-15',
      isFavorite: true,
      exportCount: 5,
    },
    {
      id: 2,
      name: 'Kategori Dağılım Analizi',
      type: 'category',
      lastModified: '2024-01-14',
      isFavorite: false,
      exportCount: 3,
    },
    {
      id: 3,
      name: 'Mesai Gelir Analizi',
      type: 'income',
      lastModified: '2024-01-13',
      isFavorite: true,
      exportCount: 8,
    },
  ],
  reportStats: {
    totalReports: 12,
    totalExports: 45,
    favoritesCount: 5,
    scheduledCount: 3,
  },
});
