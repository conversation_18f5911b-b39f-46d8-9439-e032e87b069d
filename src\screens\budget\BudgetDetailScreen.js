/**
 * <PERSON><PERSON>tçe Detay Ekranı
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md - Stage 3, Phase 4
 * 
 * Detaylı bütçe görüntüleme ve takip ekranı
 * Maksimum 300 satır - Ana ekran yönetimi
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  StatusBar,
  ScrollView,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useSQLiteContext } from 'expo-sqlite';
import { useTheme } from '../../context/ThemeContext';

// Import tracking components
import {
  BudgetStatusCard,
  RemainingAmountDisplay,
  SpendingVelocityIndicator,
  BudgetProgressBar,
  CategoryProgressList,
  DailySpendingChart,
  BudgetAlertCard,
  OverspendingWarning,
  LowBudgetNotification
} from '../../components/budget/Tracking';

/**
 * Bütçe detay ekranı
 * @param {Object} props - Component props
 * @param {Object} props.navigation - Navigation objesi
 * @param {Object} props.route - Route objesi
 */
const BudgetDetailScreen = ({ navigation, route }) => {
  const db = useSQLiteContext();
  const { theme, isDarkMode } = useTheme();
  const { budgetId } = route.params || {};

  // State yönetimi
  const [budget, setBudget] = useState(null);
  const [budgetData, setBudgetData] = useState({
    currentSpending: 0,
    remainingAmount: 0,
    categorySpending: {},
    categoryLimits: {},
    dailyData: [],
    alerts: []
  });
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  /**
   * Bütçe verilerini yükle
   */
  const loadBudgetData = async () => {
    try {
      setLoading(true);

      // Mock data - gerçek implementasyonda database'den gelecek
      const mockBudget = {
        id: budgetId,
        name: 'Aylık Bütçe',
        total_limit: 10000,
        currency: 'TRY',
        period_type: 'monthly',
        start_date: '2024-01-01',
        end_date: '2024-01-31'
      };

      const mockBudgetData = {
        currentSpending: 7500,
        remainingAmount: 2500,
        categorySpending: {
          '1': 2500, // Yemek
          '2': 1500, // Ulaşım
          '3': 2000, // Eğlence
          '4': 1500  // Alışveriş
        },
        categoryLimits: {
          '1': 3000,
          '2': 1500,
          '3': 2000,
          '4': 2000
        },
        dailyData: [
          { date: '2024-01-01', amount: 250, target: 323 },
          { date: '2024-01-02', amount: 180, target: 323 },
          { date: '2024-01-03', amount: 420, target: 323 },
          { date: '2024-01-04', amount: 310, target: 323 },
          { date: '2024-01-05', amount: 290, target: 323 },
          { date: '2024-01-06', amount: 380, target: 323 },
          { date: '2024-01-07', amount: 200, target: 323 }
        ],
        alerts: [
          {
            id: '1',
            type: 'warning',
            title: 'Bütçe Sınırına Yaklaşıldı',
            message: 'Toplam bütçenizin %75\'ini kullandınız. Dikkatli harcama yapın.',
            actionText: 'Harcamaları Görüntüle'
          }
        ]
      };

      setBudget(mockBudget);
      setBudgetData(mockBudgetData);

    } catch (error) {
      console.error('Bütçe verileri yüklenirken hata:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Component mount edildiğinde verileri yükle
  useEffect(() => {
    if (budgetId) {
      loadBudgetData();
    }
  }, [budgetId]);

  /**
   * Yenileme işleyicisi
   */
  const handleRefresh = () => {
    setRefreshing(true);
    loadBudgetData();
  };

  /**
   * Kategori tıklama işleyicisi
   * @param {Object} category - Kategori objesi
   */
  const handleCategoryPress = (category) => {
    // Kategori detay ekranına git
    console.log('Kategori detayı:', category);
  };

  /**
   * Uyarı aksiyon işleyicisi
   * @param {Object} alert - Uyarı objesi
   */
  const handleAlertAction = (alert) => {
    // Uyarı aksiyonunu işle
    console.log('Uyarı aksiyonu:', alert);
  };

  /**
   * Uyarı kapatma işleyicisi
   * @param {string} alertId - Uyarı ID'si
   */
  const handleDismissAlert = (alertId) => {
    setBudgetData(prev => ({
      ...prev,
      alerts: prev.alerts.filter(alert => alert.id !== alertId)
    }));
  };

  // Mock kategoriler
  const mockCategories = [
    { id: '1', name: 'Yemek', icon: 'restaurant' },
    { id: '2', name: 'Ulaşım', icon: 'directions-car' },
    { id: '3', name: 'Eğlence', icon: 'movie' },
    { id: '4', name: 'Alışveriş', icon: 'shopping-bag' }
  ];

  // Harcama hızı hesaplama
  const currentVelocity = budgetData.dailyData.length > 0 
    ? budgetData.dailyData.reduce((sum, d) => sum + d.amount, 0) / budgetData.dailyData.length
    : 0;
  const targetVelocity = budget ? budget.total_limit / 31 : 0; // Aylık ortalama

  // Aşırı harcama kontrolü
  const isOverspending = budgetData.currentSpending > (budget?.total_limit || 0);
  const overspentAmount = isOverspending 
    ? budgetData.currentSpending - (budget?.total_limit || 0)
    : 0;

  // Düşük bütçe kontrolü
  const isLowBudget = budgetData.remainingAmount > 0 && 
    (budgetData.remainingAmount / (budget?.total_limit || 1)) < 0.2;

  return (
    <SafeAreaView 
      style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
      edges={['top']}
    >
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor={theme.BACKGROUND}
      />

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.PRIMARY]}
            tintColor={theme.PRIMARY}
          />
        }
      >
        {/* Ana bütçe durumu */}
        {budget && (
          <BudgetStatusCard
            budget={budget}
            currentSpending={budgetData.currentSpending}
            remainingAmount={budgetData.remainingAmount}
            onPress={() => console.log('Bütçe kartı tıklandı')}
            theme={theme}
          />
        )}

        {/* Uyarılar */}
        {budgetData.alerts.map((alert) => (
          <BudgetAlertCard
            key={alert.id}
            type={alert.type}
            title={alert.title}
            message={alert.message}
            actionText={alert.actionText}
            onActionPress={() => handleAlertAction(alert)}
            onDismiss={() => handleDismissAlert(alert.id)}
            theme={theme}
          />
        ))}

        {/* Aşırı harcama uyarısı */}
        {isOverspending && (
          <OverspendingWarning
            overspentAmount={overspentAmount}
            currency={budget?.currency}
            onViewDetails={() => console.log('Detayları görüntüle')}
            onAdjustBudget={() => console.log('Bütçe ayarla')}
            theme={theme}
          />
        )}

        {/* Düşük bütçe bildirimi */}
        {isLowBudget && (
          <LowBudgetNotification
            remainingAmount={budgetData.remainingAmount}
            warningThreshold={20}
            currency={budget?.currency}
            daysLeft={15}
            onViewBudget={() => console.log('Bütçeyi görüntüle')}
            theme={theme}
          />
        )}

        {/* Kalan miktar göstergesi */}
        <RemainingAmountDisplay
          remaining={budgetData.remainingAmount}
          dailyAverage={currentVelocity}
          currency={budget?.currency}
          daysLeft={15}
          theme={theme}
        />

        {/* Harcama hızı göstergesi */}
        <SpendingVelocityIndicator
          currentVelocity={currentVelocity}
          targetVelocity={targetVelocity}
          trend="increasing"
          velocityChange={15}
          currency={budget?.currency}
          theme={theme}
        />

        {/* Toplam bütçe ilerleme çubuğu */}
        {budget && (
          <BudgetProgressBar
            spent={budgetData.currentSpending}
            limit={budget.total_limit}
            label="Toplam Bütçe"
            currency={budget.currency}
            theme={theme}
          />
        )}

        {/* Günlük harcama grafiği */}
        <DailySpendingChart
          dailyData={budgetData.dailyData}
          currency={budget?.currency}
          showTarget={true}
          theme={theme}
        />

        {/* Kategori ilerleme listesi */}
        <CategoryProgressList
          categories={mockCategories}
          categorySpending={budgetData.categorySpending}
          categoryLimits={budgetData.categoryLimits}
          currency={budget?.currency}
          onCategoryPress={handleCategoryPress}
          theme={theme}
        />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
});

export default BudgetDetailScreen;
