/**
 * Bütçe Detay Ekranı
 * Basit ve çalışır bir bütçe detay görünümü
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useSQLiteContext } from 'expo-sqlite';
import { useTheme } from '../../context/ThemeContext';
import { MaterialIcons } from '@expo/vector-icons';
import { getBudgetById, deleteBudget } from '../../services/budget';

/**
 * Bütçe detay ekranı
 */
const BudgetDetailScreen = ({ navigation, route }) => {
  const db = useSQLiteContext();
  const { theme } = useTheme();
  const { budgetId } = route.params || {};

  const [loading, setLoading] = useState(true);
  const [budget, setBudget] = useState(null);

  // Get theme-safe styles
  const getSafeThemeValue = (path, fallback) => {
    return theme?.colors?.[path] || theme?.[path] || fallback;
  };

  // Load budget data
  useEffect(() => {
    if (budgetId) {
      loadBudget();
    }
  }, [budgetId]);

  const loadBudget = async () => {
    try {
      setLoading(true);
      const budgetData = await getBudgetById(db, budgetId);
      setBudget(budgetData);
    } catch (error) {
      console.error('Bütçe yüklenemedi:', error);
      Alert.alert('Hata', 'Bütçe detayları yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    navigation.navigate('BudgetCreate', { budgetId, editMode: true });
  };

  const handleDelete = () => {
    Alert.alert(
      'Bütçeyi Sil',
      'Bu bütçeyi silmek istediğinize emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteBudget(db, budgetId);
              Alert.alert(
                'Başarılı',
                'Bütçe başarıyla silindi.',
                [{ text: 'Tamam', onPress: () => navigation.goBack() }]
              );
            } catch (error) {
              console.error('Bütçe silme hatası:', error);
              Alert.alert('Hata', 'Bütçe silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: getSafeThemeValue('background', '#ffffff'),
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 20,
      borderBottomWidth: 1,
      borderBottomColor: getSafeThemeValue('border', '#e0e0e0'),
    },
    backButton: {
      padding: 8,
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: getSafeThemeValue('text', '#333333'),
      flex: 1,
      textAlign: 'center',
    },
    actionButton: {
      padding: 8,
    },
    content: {
      flex: 1,
      padding: 20,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    budgetCard: {
      backgroundColor: getSafeThemeValue('surface', '#f9f9f9'),
      borderRadius: 12,
      padding: 20,
      marginBottom: 20,
      elevation: 2,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    budgetName: {
      fontSize: 24,
      fontWeight: 'bold',
      color: getSafeThemeValue('text', '#333333'),
      marginBottom: 10,
    },
    budgetDescription: {
      fontSize: 16,
      color: getSafeThemeValue('textSecondary', '#666666'),
      marginBottom: 15,
    },
    detailRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 8,
      borderBottomWidth: 1,
      borderBottomColor: getSafeThemeValue('border', '#e0e0e0'),
    },
    detailLabel: {
      fontSize: 16,
      color: getSafeThemeValue('textSecondary', '#666666'),
    },
    detailValue: {
      fontSize: 16,
      fontWeight: '600',
      color: getSafeThemeValue('text', '#333333'),
    },
    amountText: {
      fontSize: 20,
      fontWeight: 'bold',
      color: getSafeThemeValue('primary', '#007AFF'),
    },
    statusBadge: {
      paddingHorizontal: 12,
      paddingVertical: 4,
      borderRadius: 12,
      alignSelf: 'flex-start',
    },
    statusText: {
      fontSize: 12,
      fontWeight: '600',
      textTransform: 'uppercase',
    },
    actionButtons: {
      flexDirection: 'row',
      gap: 15,
      marginTop: 30,
    },
    editButton: {
      flex: 1,
      backgroundColor: getSafeThemeValue('primary', '#007AFF'),
      borderRadius: 8,
      padding: 15,
      alignItems: 'center',
    },
    deleteButton: {
      flex: 1,
      backgroundColor: getSafeThemeValue('error', '#FF3B30'),
      borderRadius: 8,
      padding: 15,
      alignItems: 'center',
    },
    buttonText: {
      color: '#ffffff',
      fontSize: 16,
      fontWeight: '600',
    },
  });

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <MaterialIcons 
              name="arrow-back" 
              size={24} 
              color={getSafeThemeValue('text', '#333333')} 
            />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Bütçe Detayı</Text>
          <View style={{ width: 40 }} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={getSafeThemeValue('primary', '#007AFF')} />
          <Text style={{ marginTop: 10, color: getSafeThemeValue('textSecondary', '#666666') }}>
            Yükleniyor...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!budget) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <MaterialIcons 
              name="arrow-back" 
              size={24} 
              color={getSafeThemeValue('text', '#333333')} 
            />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Bütçe Detayı</Text>
          <View style={{ width: 40 }} />
        </View>
        <View style={styles.loadingContainer}>
          <Text style={{ color: getSafeThemeValue('textSecondary', '#666666') }}>
            Bütçe bulunamadı
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return getSafeThemeValue('success', '#34C759');
      case 'exceeded':
        return getSafeThemeValue('error', '#FF3B30');
      case 'completed':
        return getSafeThemeValue('warning', '#FF9500');
      default:
        return getSafeThemeValue('textSecondary', '#666666');
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'active':
        return 'Aktif';
      case 'exceeded':
        return 'Aşıldı';
      case 'completed':
        return 'Tamamlandı';
      default:
        return 'Bilinmiyor';
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons 
            name="arrow-back" 
            size={24} 
            color={getSafeThemeValue('text', '#333333')} 
          />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Bütçe Detayı</Text>
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={handleEdit}
        >
          <MaterialIcons 
            name="edit" 
            size={24} 
            color={getSafeThemeValue('primary', '#007AFF')} 
          />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.budgetCard}>
          <Text style={styles.budgetName}>{budget.name}</Text>
          {budget.description && (
            <Text style={styles.budgetDescription}>{budget.description}</Text>
          )}

          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(budget.status) }]}>
            <Text style={[styles.statusText, { color: '#ffffff' }]}>
              {getStatusText(budget.status)}
            </Text>
          </View>

          <View style={{ marginTop: 20 }}>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Toplam Limit</Text>
              <Text style={styles.amountText}>
                {budget.total_limit?.toLocaleString('tr-TR')} {budget.currency}
              </Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Tür</Text>
              <Text style={styles.detailValue}>
                {budget.type === 'category_based' ? 'Kategori Bazlı' : 
                 budget.type === 'total' ? 'Toplam Bütçe' : 'Esnek'}
              </Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Dönem</Text>
              <Text style={styles.detailValue}>
                {budget.period_type === 'monthly' ? 'Aylık' : 
                 budget.period_type === 'weekly' ? 'Haftalık' : 'Özel'}
              </Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Başlangıç Tarihi</Text>
              <Text style={styles.detailValue}>
                {new Date(budget.start_date).toLocaleDateString('tr-TR')}
              </Text>
            </View>

            {budget.end_date && (
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Bitiş Tarihi</Text>
                <Text style={styles.detailValue}>
                  {new Date(budget.end_date).toLocaleDateString('tr-TR')}
                </Text>
              </View>
            )}
          </View>
        </View>

        <View style={styles.actionButtons}>
          <TouchableOpacity style={styles.editButton} onPress={handleEdit}>
            <Text style={styles.buttonText}>Düzenle</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.deleteButton} onPress={handleDelete}>
            <Text style={styles.buttonText}>Sil</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default BudgetDetailScreen;
