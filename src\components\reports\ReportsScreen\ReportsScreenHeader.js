import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  StatusBar,
} from 'react-native';

/**
 * Reports Screen Header Component
 * Displays main title, subtitle, and navigation controls
 * Follows REPORTS_SCREEN_REDESIGN_PLAN.md specifications
 */
const ReportsScreenHeader = ({ 
  title = "📊 İnteraktif Raporlar", 
  subtitle = "Finansal Analiz Platformu",
  onBackPress,
  onSearchPress,
  onSettingsPress,
  theme 
}) => {
  return (
    <>
      <StatusBar 
        barStyle={theme.STATUS_BAR_STYLE} 
        backgroundColor={theme.STATUS_BAR_COLOR} 
      />
      
      <View style={[styles.container, { backgroundColor: theme.SURFACE }]}>
        {/* Back Button */}
        {onBackPress && (
          <TouchableOpacity 
            style={[styles.backButton, { backgroundColor: theme.BACKGROUND }]}
            onPress={onBackPress}
          >
            <Text style={[styles.backButtonText, { color: theme.TEXT_PRIMARY }]}>
              ←
            </Text>
          </TouchableOpacity>
        )}

        {/* Title Section */}
        <View style={styles.titleSection}>
          <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
            {title}
          </Text>
          <Text style={[styles.subtitle, { color: theme.TEXT_SECONDARY }]}>
            {subtitle}
          </Text>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          {onSearchPress && (
            <TouchableOpacity 
              style={[styles.actionButton, { backgroundColor: theme.BACKGROUND }]}
              onPress={onSearchPress}
            >
              <Text style={[styles.actionButtonText, { color: theme.TEXT_PRIMARY }]}>
                🔍
              </Text>
            </TouchableOpacity>
          )}
          
          {onSettingsPress && (
            <TouchableOpacity 
              style={[styles.actionButton, { backgroundColor: theme.BACKGROUND }]}
              onPress={onSettingsPress}
            >
              <Text style={[styles.actionButtonText, { color: theme.TEXT_PRIMARY }]}>
                ⚙️
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  backButtonText: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  titleSection: {
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  subtitle: {
    fontSize: 14,
    opacity: 0.8,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionButtonText: {
    fontSize: 16,
  },
});

export default ReportsScreenHeader;
