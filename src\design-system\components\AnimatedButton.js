import React, { useRef } from 'react';
import { Animated, TouchableWithoutFeedback } from 'react-native';
import Button from './Button';
import { AnimationUtils } from '../animations';

/**
 * Animated Button Component
 * Press animasyonu ile geliştirilmiş buton
 */
const AnimatedButton = ({
  onPress,
  children,
  style,
  animationType = 'scale', // 'scale', 'opacity', 'both'
  ...props
}) => {
  const scaleValue = useRef(new Animated.Value(1)).current;
  const opacityValue = useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    const animations = [];

    if (animationType === 'scale' || animationType === 'both') {
      animations.push(
        Animated.timing(scaleValue, {
          toValue: 0.95,
          duration: 100,
          useNativeDriver: true,
        })
      );
    }

    if (animationType === 'opacity' || animationType === 'both') {
      animations.push(
        Animated.timing(opacityValue, {
          toValue: 0.7,
          duration: 100,
          useNativeDriver: true,
        })
      );
    }

    if (animations.length > 0) {
      Animated.parallel(animations).start();
    }
  };

  const handlePressOut = () => {
    const animations = [];

    if (animationType === 'scale' || animationType === 'both') {
      animations.push(
        Animated.timing(scaleValue, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        })
      );
    }

    if (animationType === 'opacity' || animationType === 'both') {
      animations.push(
        Animated.timing(opacityValue, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        })
      );
    }

    if (animations.length > 0) {
      Animated.parallel(animations).start();
    }
  };

  const animatedStyle = {
    transform: [{ scale: scaleValue }],
    opacity: opacityValue,
  };

  return (
    <TouchableWithoutFeedback
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      onPress={onPress}
    >
      <Animated.View style={[animatedStyle, style]}>
        <Button {...props} onPress={undefined}>
          {children}
        </Button>
      </Animated.View>
    </TouchableWithoutFeedback>
  );
};

export default AnimatedButton;
