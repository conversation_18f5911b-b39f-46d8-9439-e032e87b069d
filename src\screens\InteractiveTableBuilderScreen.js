import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  Dimensions,
  StatusBar,
} from 'react-native';
import { useTheme } from '../context/ThemeContext';
import { useNavigation } from '@react-navigation/native';

const { width: screenWidth } = Dimensions.get('window');

/**
 * İnteraktif Tablo Oluşturucu Screen - Kullanıcı kendi tablolarını oluşturur
 * Sürükle-bırak, filtreleme, formül desteği
 */
const InteractiveTableBuilderScreen = () => {
  const { theme } = useTheme();
  const navigation = useNavigation();
  
  const [selectedDataSources, setSelectedDataSources] = useState({
    transactions: true,
    salary: true,
    overtime: true,
    budgets: false,
    goals: false,
  });
  
  const [dateRange, setDateRange] = useState({
    start: '2024-01-01',
    end: '2024-12-31',
    period: 'thisMonth',
  });
  
  const [selectedColumns, setSelectedColumns] = useState([
    { id: 'date', name: '<PERSON><PERSON><PERSON>', type: 'date', width: 100, visible: true },
    { id: 'category', name: 'Kategori', type: 'text', width: 120, visible: true },
    { id: 'description', name: 'Açıklama', type: 'text', width: 150, visible: true },
    { id: 'amount', name: 'Tutar', type: 'currency', width: 100, visible: true },
    { id: 'type', name: 'Tip', type: 'text', width: 80, visible: true },
  ]);
  
  const [filters, setFilters] = useState([]);
  const [calculatedFields, setCalculatedFields] = useState([]);
  const [conditionalFormats, setConditionalFormats] = useState([]);
  
  const [previewData, setPreviewData] = useState([]);
  const [isPreviewVisible, setIsPreviewVisible] = useState(false);

  useEffect(() => {
    generatePreviewData();
  }, [selectedDataSources, dateRange, selectedColumns, filters]);

  /**
   * Veri kaynaklarını al
   */
  const getDataSources = () => [
    { id: 'transactions', name: 'İşlemler', icon: '💳', count: 1247 },
    { id: 'salary', name: 'Maaş', icon: '💰', count: 12 },
    { id: 'overtime', name: 'Mesai', icon: '⏰', count: 35 },
    { id: 'budgets', name: 'Bütçe', icon: '📊', count: 8 },
    { id: 'goals', name: 'Hedefler', icon: '🎯', count: 5 },
  ];

  /**
   * Tarih aralığı seçenekleri
   */
  const getDateRangeOptions = () => [
    { id: 'thisMonth', name: 'Bu Ay', icon: '📅' },
    { id: 'lastMonth', name: 'Geçen Ay', icon: '📅' },
    { id: 'thisYear', name: 'Bu Yıl', icon: '📅' },
    { id: 'lastYear', name: 'Geçen Yıl', icon: '📅' },
    { id: 'custom', name: 'Özel Tarih', icon: '🗓️' },
  ];

  /**
   * Kullanılabilir sütunlar
   */
  const getAvailableColumns = () => [
    { id: 'date', name: 'Tarih', type: 'date', source: 'all' },
    { id: 'category', name: 'Kategori', type: 'text', source: 'transactions' },
    { id: 'description', name: 'Açıklama', type: 'text', source: 'all' },
    { id: 'amount', name: 'Tutar', type: 'currency', source: 'all' },
    { id: 'type', name: 'Tip', type: 'text', source: 'all' },
    { id: 'source', name: 'Kaynak', type: 'text', source: 'all' },
    { id: 'balance', name: 'Bakiye', type: 'currency', source: 'all' },
    { id: 'month', name: 'Ay', type: 'text', source: 'all' },
    { id: 'year', name: 'Yıl', type: 'number', source: 'all' },
    { id: 'weekday', name: 'Haftanın Günü', type: 'text', source: 'all' },
  ];

  /**
   * Önizleme verisi oluştur
   */
  const generatePreviewData = () => {
    // TODO: Gerçek veri çekme implementasyonu
    // Şimdilik mock data
    const mockData = [
      {
        date: '2024-01-15',
        category: 'Gıda',
        description: 'Market alışverişi',
        amount: -250.50,
        type: 'Gider',
        source: 'İşlemler',
      },
      {
        date: '2024-01-14',
        category: 'Ulaşım',
        description: 'Benzin',
        amount: -180.00,
        type: 'Gider',
        source: 'İşlemler',
      },
      {
        date: '2024-01-13',
        category: 'Maaş',
        description: 'Aylık maaş',
        amount: 15000.00,
        type: 'Gelir',
        source: 'Maaş',
      },
      {
        date: '2024-01-12',
        category: 'Mesai',
        description: 'Hafta sonu mesai',
        amount: 800.00,
        type: 'Gelir',
        source: 'Mesai',
      },
      {
        date: '2024-01-11',
        category: 'Eğlence',
        description: 'Sinema',
        amount: -45.00,
        type: 'Gider',
        source: 'İşlemler',
      },
    ];
    
    setPreviewData(mockData);
  };

  /**
   * Veri kaynağı seçimini değiştir
   */
  const handleDataSourceToggle = (sourceId) => {
    setSelectedDataSources(prev => ({
      ...prev,
      [sourceId]: !prev[sourceId]
    }));
  };

  /**
   * Sütun ekle/çıkar
   */
  const handleColumnToggle = (columnId) => {
    setSelectedColumns(prev => 
      prev.map(col => 
        col.id === columnId 
          ? { ...col, visible: !col.visible }
          : col
      )
    );
  };

  /**
   * Hesaplanmış sütun ekle
   */
  const handleAddCalculatedField = () => {
    Alert.alert(
      'Hesaplanmış Sütun',
      'Yeni hesaplanmış sütun eklensin mi?',
      [
        { text: 'İptal', style: 'cancel' },
        { 
          text: 'Ekle', 
          onPress: () => {
            // TODO: Formül editörü açılacak
            console.log('Hesaplanmış sütun ekleniyor');
          }
        },
      ]
    );
  };

  /**
   * Filtre ekle
   */
  const handleAddFilter = () => {
    Alert.alert(
      'Filtre Ekle',
      'Yeni filtre eklensin mi?',
      [
        { text: 'İptal', style: 'cancel' },
        { 
          text: 'Ekle', 
          onPress: () => {
            // TODO: Filtre editörü açılacak
            console.log('Filtre ekleniyor');
          }
        },
      ]
    );
  };

  /**
   * Koşullu biçimlendirme ekle
   */
  const handleAddConditionalFormat = () => {
    Alert.alert(
      'Koşullu Biçimlendirme',
      'Yeni koşullu biçimlendirme eklensin mi?',
      [
        { text: 'İptal', style: 'cancel' },
        { 
          text: 'Ekle', 
          onPress: () => {
            // TODO: Koşullu biçimlendirme editörü açılacak
            console.log('Koşullu biçimlendirme ekleniyor');
          }
        },
      ]
    );
  };

  /**
   * Tabloyu kaydet
   */
  const handleSaveTable = () => {
    Alert.alert(
      'Tabloyu Kaydet',
      'Tablo kaydedilsin mi?',
      [
        { text: 'İptal', style: 'cancel' },
        { 
          text: 'Kaydet', 
          onPress: () => {
            // TODO: Tablo kaydetme implementasyonu
            console.log('Tablo kaydediliyor');
          }
        },
      ]
    );
  };

  /**
   * Tabloyu dışa aktar
   */
  const handleExportTable = () => {
    Alert.alert(
      'Dışa Aktar',
      'Tablo hangi formatta dışa aktarılsın?',
      [
        { text: 'İptal', style: 'cancel' },
        { text: 'CSV', onPress: () => console.log('CSV export') },
        { text: 'Excel', onPress: () => console.log('Excel export') },
        { text: 'PDF', onPress: () => console.log('PDF export') },
      ]
    );
  };

  /**
   * Para formatı
   */
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      <StatusBar barStyle={theme.STATUS_BAR_STYLE} backgroundColor={theme.STATUS_BAR_COLOR} />
      
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.SURFACE }]}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={[styles.backButtonText, { color: theme.TEXT_PRIMARY }]}>
            ← Geri
          </Text>
        </TouchableOpacity>
        
        <View style={styles.headerContent}>
          <Text style={[styles.headerTitle, { color: theme.TEXT_PRIMARY }]}>
            📊 Tablo Oluşturucu
          </Text>
          <Text style={[styles.headerSubtitle, { color: theme.TEXT_SECONDARY }]}>
            İnteraktif Tablo Tasarla
          </Text>
        </View>
        
        <TouchableOpacity 
          style={[styles.saveButton, { backgroundColor: theme.PRIMARY }]}
          onPress={handleSaveTable}
        >
          <Text style={[styles.saveButtonText, { color: theme.SURFACE }]}>
            Kaydet
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Veri Kaynağı Seçimi */}
        <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            🎯 Veri Kaynağı Seçimi
          </Text>
          
          <View style={styles.dataSourcesContainer}>
            {getDataSources().map((source) => (
              <TouchableOpacity
                key={source.id}
                style={[
                  styles.dataSourceCard,
                  { backgroundColor: theme.BACKGROUND },
                  selectedDataSources[source.id] && { borderColor: theme.PRIMARY, borderWidth: 2 }
                ]}
                onPress={() => handleDataSourceToggle(source.id)}
              >
                <View style={styles.dataSourceHeader}>
                  <Text style={styles.dataSourceIcon}>{source.icon}</Text>
                  <View style={[
                    styles.dataSourceCheckbox,
                    { backgroundColor: selectedDataSources[source.id] ? theme.PRIMARY : theme.BORDER }
                  ]}>
                    {selectedDataSources[source.id] && (
                      <Text style={[styles.checkmark, { color: theme.SURFACE }]}>✓</Text>
                    )}
                  </View>
                </View>
                
                <Text style={[styles.dataSourceName, { color: theme.TEXT_PRIMARY }]}>
                  {source.name}
                </Text>
                
                <Text style={[styles.dataSourceCount, { color: theme.TEXT_SECONDARY }]}>
                  {source.count} kayıt
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Tarih Aralığı */}
        <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            📅 Tarih Aralığı
          </Text>
          
          <View style={styles.dateRangeContainer}>
            {getDateRangeOptions().map((option) => (
              <TouchableOpacity
                key={option.id}
                style={[
                  styles.dateRangeOption,
                  { backgroundColor: theme.BACKGROUND },
                  dateRange.period === option.id && { borderColor: theme.PRIMARY, borderWidth: 2 }
                ]}
                onPress={() => setDateRange(prev => ({ ...prev, period: option.id }))}
              >
                <Text style={styles.dateRangeIcon}>{option.icon}</Text>
                <Text style={[styles.dateRangeName, { color: theme.TEXT_PRIMARY }]}>
                  {option.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Sütun Yapılandırması */}
        <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            ⚙️ Sütun Yapılandırması
          </Text>
          
          <View style={styles.columnsContainer}>
            {getAvailableColumns().map((column) => {
              const isSelected = selectedColumns.find(col => col.id === column.id)?.visible;
              
              return (
                <TouchableOpacity
                  key={column.id}
                  style={[
                    styles.columnCard,
                    { backgroundColor: theme.BACKGROUND },
                    isSelected && { borderColor: theme.PRIMARY, borderWidth: 2 }
                  ]}
                  onPress={() => handleColumnToggle(column.id)}
                >
                  <View style={styles.columnHeader}>
                    <Text style={[styles.columnName, { color: theme.TEXT_PRIMARY }]}>
                      {column.name}
                    </Text>
                    <View style={[
                      styles.columnCheckbox,
                      { backgroundColor: isSelected ? theme.PRIMARY : theme.BORDER }
                    ]}>
                      {isSelected && (
                        <Text style={[styles.checkmark, { color: theme.SURFACE }]}>✓</Text>
                      )}
                    </View>
                  </View>
                  
                  <Text style={[styles.columnType, { color: theme.TEXT_SECONDARY }]}>
                    {column.type}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </View>
        </View>

        {/* Hesaplanmış Sütunlar */}
        <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            📈 Hesaplanmış Sütunlar
          </Text>
          
          <View style={styles.calculatedFieldsContainer}>
            <TouchableOpacity
              style={[styles.addButton, { backgroundColor: theme.PRIMARY }]}
              onPress={handleAddCalculatedField}
            >
              <Text style={[styles.addButtonText, { color: theme.SURFACE }]}>
                + Hesaplanmış Sütun Ekle
              </Text>
            </TouchableOpacity>
            
            {calculatedFields.length > 0 && (
              <View style={styles.calculatedFieldsList}>
                {calculatedFields.map((field, index) => (
                  <View key={index} style={[styles.calculatedFieldCard, { backgroundColor: theme.BACKGROUND }]}>
                    <Text style={[styles.calculatedFieldName, { color: theme.TEXT_PRIMARY }]}>
                      {field.name}
                    </Text>
                    <Text style={[styles.calculatedFieldFormula, { color: theme.TEXT_SECONDARY }]}>
                      {field.formula}
                    </Text>
                  </View>
                ))}
              </View>
            )}
          </View>
        </View>

        {/* Koşullu Biçimlendirme */}
        <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            🎨 Koşullu Biçimlendirme
          </Text>
          
          <View style={styles.conditionalFormatsContainer}>
            <TouchableOpacity
              style={[styles.addButton, { backgroundColor: theme.SUCCESS }]}
              onPress={handleAddConditionalFormat}
            >
              <Text style={[styles.addButtonText, { color: theme.SURFACE }]}>
                + Koşullu Biçimlendirme Ekle
              </Text>
            </TouchableOpacity>
            
            {conditionalFormats.length > 0 && (
              <View style={styles.conditionalFormatsList}>
                {conditionalFormats.map((format, index) => (
                  <View key={index} style={[styles.conditionalFormatCard, { backgroundColor: theme.BACKGROUND }]}>
                    <Text style={[styles.conditionalFormatRule, { color: theme.TEXT_PRIMARY }]}>
                      {format.rule}
                    </Text>
                    <View style={[styles.conditionalFormatColor, { backgroundColor: format.color }]} />
                  </View>
                ))}
              </View>
            )}
          </View>
        </View>

        {/* Önizleme */}
        <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
          <TouchableOpacity
            style={styles.previewHeader}
            onPress={() => setIsPreviewVisible(!isPreviewVisible)}
          >
            <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
              👁 Önizleme
            </Text>
            <Text style={[styles.previewToggle, { color: theme.PRIMARY }]}>
              {isPreviewVisible ? '▲' : '▼'}
            </Text>
          </TouchableOpacity>
          
          {isPreviewVisible && (
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View style={styles.previewTable}>
                {/* Tablo başlığı */}
                <View style={styles.previewTableHeader}>
                  {selectedColumns.filter(col => col.visible).map((column) => (
                    <View key={column.id} style={[styles.previewHeaderCell, { backgroundColor: theme.PRIMARY }]}>
                      <Text style={[styles.previewHeaderText, { color: theme.SURFACE }]}>
                        {column.name}
                      </Text>
                    </View>
                  ))}
                </View>
                
                {/* Tablo verileri */}
                {previewData.map((row, index) => (
                  <View key={index} style={styles.previewTableRow}>
                    {selectedColumns.filter(col => col.visible).map((column) => (
                      <View key={column.id} style={[styles.previewCell, { backgroundColor: theme.BACKGROUND }]}>
                        <Text style={[styles.previewCellText, { color: theme.TEXT_PRIMARY }]}>
                          {column.type === 'currency' 
                            ? formatCurrency(row[column.id])
                            : row[column.id] || '-'
                          }
                        </Text>
                      </View>
                    ))}
                  </View>
                ))}
              </View>
            </ScrollView>
          )}
        </View>

        {/* Aksiyonlar */}
        <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            🚀 Aksiyonlar
          </Text>
          
          <View style={styles.actionsContainer}>
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: theme.PRIMARY }]}
              onPress={handleSaveTable}
            >
              <Text style={[styles.actionButtonText, { color: theme.SURFACE }]}>
                💾 Kaydet
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: theme.SUCCESS }]}
              onPress={handleExportTable}
            >
              <Text style={[styles.actionButtonText, { color: theme.SURFACE }]}>
                📤 Dışa Aktar
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: theme.WARNING }]}
              onPress={handleAddFilter}
            >
              <Text style={[styles.actionButtonText, { color: theme.SURFACE }]}>
                🔍 Filtre Ekle
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  backButton: {
    marginRight: 12,
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  headerContent: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 2,
  },
  headerSubtitle: {
    fontSize: 12,
    opacity: 0.8,
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  saveButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
  },
  section: {
    margin: 16,
    padding: 16,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  dataSourcesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  dataSourceCard: {
    width: (screenWidth - 64) / 2,
    padding: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  dataSourceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  dataSourceIcon: {
    fontSize: 20,
  },
  dataSourceCheckbox: {
    width: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkmark: {
    fontSize: 12,
    fontWeight: '600',
  },
  dataSourceName: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  dataSourceCount: {
    fontSize: 12,
  },
  dateRangeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  dateRangeOption: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'transparent',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  dateRangeIcon: {
    fontSize: 16,
  },
  dateRangeName: {
    fontSize: 14,
    fontWeight: '500',
  },
  columnsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  columnCard: {
    width: (screenWidth - 64) / 2,
    padding: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  columnHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  columnName: {
    fontSize: 14,
    fontWeight: '600',
    flex: 1,
  },
  columnCheckbox: {
    width: 16,
    height: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  columnType: {
    fontSize: 12,
  },
  calculatedFieldsContainer: {
    gap: 12,
  },
  conditionalFormatsContainer: {
    gap: 12,
  },
  addButton: {
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  addButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  calculatedFieldsList: {
    gap: 8,
  },
  calculatedFieldCard: {
    padding: 12,
    borderRadius: 8,
  },
  calculatedFieldName: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  calculatedFieldFormula: {
    fontSize: 12,
    fontFamily: 'monospace',
  },
  conditionalFormatsList: {
    gap: 8,
  },
  conditionalFormatCard: {
    padding: 12,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  conditionalFormatRule: {
    fontSize: 14,
    fontWeight: '600',
    flex: 1,
  },
  conditionalFormatColor: {
    width: 20,
    height: 20,
    borderRadius: 10,
  },
  previewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  previewToggle: {
    fontSize: 16,
    fontWeight: '600',
  },
  previewTable: {
    minWidth: screenWidth - 64,
  },
  previewTableHeader: {
    flexDirection: 'row',
    marginBottom: 1,
  },
  previewHeaderCell: {
    width: 120,
    padding: 12,
    marginRight: 1,
  },
  previewHeaderText: {
    fontSize: 12,
    fontWeight: '600',
  },
  previewTableRow: {
    flexDirection: 'row',
    marginBottom: 1,
  },
  previewCell: {
    width: 120,
    padding: 12,
    marginRight: 1,
  },
  previewCellText: {
    fontSize: 12,
  },
  actionsContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
});

export default InteractiveTableBuilderScreen;
