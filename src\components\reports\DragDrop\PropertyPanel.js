import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  TextInput,
  Switch,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

/**
 * Property Panel Component
 * Allows editing properties of selected components
 * Provides type-specific property editors
 */
const PropertyPanel = ({ 
  selectedComponent,
  onUpdateComponent,
  onDeleteComponent,
  theme 
}) => {
  const [properties, setProperties] = useState({});
  const [activeSection, setActiveSection] = useState('general');

  useEffect(() => {
    if (selectedComponent) {
      setProperties({
        ...selectedComponent.props,
        title: selectedComponent.title,
        type: selectedComponent.type,
      });
    }
  }, [selectedComponent]);

  const handlePropertyChange = (key, value) => {
    const updatedProperties = { ...properties, [key]: value };
    setProperties(updatedProperties);
    
    if (selectedComponent) {
      const updatedComponent = {
        ...selectedComponent,
        title: updatedProperties.title || selectedComponent.title,
        props: { ...updatedProperties },
      };
      onUpdateComponent(updatedComponent);
    }
  };

  const handleDelete = () => {
    Alert.alert(
      'Bileşeni Sil',
      'Bu bileşeni silmek istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        { 
          text: 'Sil', 
          style: 'destructive',
          onPress: () => onDeleteComponent(selectedComponent.id)
        },
      ]
    );
  };

  const renderGeneralProperties = () => (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
        Genel Özellikler
      </Text>

      {/* Title */}
      <View style={styles.property}>
        <Text style={[styles.propertyLabel, { color: theme.TEXT_PRIMARY }]}>
          Başlık
        </Text>
        <TextInput
          style={[
            styles.textInput,
            { 
              backgroundColor: theme.BACKGROUND,
              color: theme.TEXT_PRIMARY,
              borderColor: theme.BORDER
            }
          ]}
          value={properties.title || ''}
          onChangeText={(value) => handlePropertyChange('title', value)}
          placeholder="Bileşen başlığı"
          placeholderTextColor={theme.TEXT_SECONDARY}
        />
      </View>

      {/* Dimensions */}
      <View style={styles.property}>
        <Text style={[styles.propertyLabel, { color: theme.TEXT_PRIMARY }]}>
          Boyutlar
        </Text>
        <View style={styles.dimensionsContainer}>
          <View style={styles.dimensionInput}>
            <Text style={[styles.dimensionLabel, { color: theme.TEXT_SECONDARY }]}>
              Genişlik
            </Text>
            <TextInput
              style={[
                styles.numberInput,
                { 
                  backgroundColor: theme.BACKGROUND,
                  color: theme.TEXT_PRIMARY,
                  borderColor: theme.BORDER
                }
              ]}
              value={String(properties.width || 0)}
              onChangeText={(value) => handlePropertyChange('width', parseInt(value) || 0)}
              keyboardType="numeric"
              placeholder="0"
              placeholderTextColor={theme.TEXT_SECONDARY}
            />
          </View>
          <View style={styles.dimensionInput}>
            <Text style={[styles.dimensionLabel, { color: theme.TEXT_SECONDARY }]}>
              Yükseklik
            </Text>
            <TextInput
              style={[
                styles.numberInput,
                { 
                  backgroundColor: theme.BACKGROUND,
                  color: theme.TEXT_PRIMARY,
                  borderColor: theme.BORDER
                }
              ]}
              value={String(properties.height || 0)}
              onChangeText={(value) => handlePropertyChange('height', parseInt(value) || 0)}
              keyboardType="numeric"
              placeholder="0"
              placeholderTextColor={theme.TEXT_SECONDARY}
            />
          </View>
        </View>
      </View>
    </View>
  );

  const renderChartProperties = () => (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
        Grafik Özellikleri
      </Text>

      {/* Chart Type */}
      <View style={styles.property}>
        <Text style={[styles.propertyLabel, { color: theme.TEXT_PRIMARY }]}>
          Grafik Türü
        </Text>
        <View style={styles.optionsContainer}>
          {['bar', 'line', 'pie', 'area'].map((type) => (
            <TouchableOpacity
              key={type}
              style={[
                styles.optionButton,
                {
                  backgroundColor: properties.chartType === type 
                    ? theme.PRIMARY 
                    : theme.BACKGROUND,
                  borderColor: theme.BORDER,
                }
              ]}
              onPress={() => handlePropertyChange('chartType', type)}
            >
              <Text style={[
                styles.optionText,
                { 
                  color: properties.chartType === type 
                    ? theme.SURFACE 
                    : theme.TEXT_PRIMARY 
                }
              ]}>
                {type.toUpperCase()}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Data Source */}
      <View style={styles.property}>
        <Text style={[styles.propertyLabel, { color: theme.TEXT_PRIMARY }]}>
          Veri Kaynağı
        </Text>
        <View style={styles.optionsContainer}>
          {['transactions', 'categories', 'monthly_data'].map((source) => (
            <TouchableOpacity
              key={source}
              style={[
                styles.optionButton,
                {
                  backgroundColor: properties.dataSource === source 
                    ? theme.PRIMARY 
                    : theme.BACKGROUND,
                  borderColor: theme.BORDER,
                }
              ]}
              onPress={() => handlePropertyChange('dataSource', source)}
            >
              <Text style={[
                styles.optionText,
                { 
                  color: properties.dataSource === source 
                    ? theme.SURFACE 
                    : theme.TEXT_PRIMARY 
                }
              ]}>
                {source}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Show Legend */}
      <View style={styles.property}>
        <View style={styles.switchContainer}>
          <Text style={[styles.propertyLabel, { color: theme.TEXT_PRIMARY }]}>
            Açıklama Göster
          </Text>
          <Switch
            value={properties.showLegend || false}
            onValueChange={(value) => handlePropertyChange('showLegend', value)}
            trackColor={{ false: theme.DISABLED, true: theme.PRIMARY }}
            thumbColor={theme.SURFACE}
          />
        </View>
      </View>

      {/* Show Grid */}
      <View style={styles.property}>
        <View style={styles.switchContainer}>
          <Text style={[styles.propertyLabel, { color: theme.TEXT_PRIMARY }]}>
            Izgara Göster
          </Text>
          <Switch
            value={properties.showGrid || false}
            onValueChange={(value) => handlePropertyChange('showGrid', value)}
            trackColor={{ false: theme.DISABLED, true: theme.PRIMARY }}
            thumbColor={theme.SURFACE}
          />
        </View>
      </View>
    </View>
  );

  const renderTextProperties = () => (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
        Metin Özellikleri
      </Text>

      {/* Content */}
      <View style={styles.property}>
        <Text style={[styles.propertyLabel, { color: theme.TEXT_PRIMARY }]}>
          İçerik
        </Text>
        <TextInput
          style={[
            styles.textArea,
            { 
              backgroundColor: theme.BACKGROUND,
              color: theme.TEXT_PRIMARY,
              borderColor: theme.BORDER
            }
          ]}
          value={properties.content || ''}
          onChangeText={(value) => handlePropertyChange('content', value)}
          placeholder="Metin içeriği"
          placeholderTextColor={theme.TEXT_SECONDARY}
          multiline
          numberOfLines={3}
        />
      </View>

      {/* Font Size */}
      <View style={styles.property}>
        <Text style={[styles.propertyLabel, { color: theme.TEXT_PRIMARY }]}>
          Font Boyutu
        </Text>
        <TextInput
          style={[
            styles.numberInput,
            { 
              backgroundColor: theme.BACKGROUND,
              color: theme.TEXT_PRIMARY,
              borderColor: theme.BORDER
            }
          ]}
          value={String(properties.fontSize || 14)}
          onChangeText={(value) => handlePropertyChange('fontSize', parseInt(value) || 14)}
          keyboardType="numeric"
          placeholder="14"
          placeholderTextColor={theme.TEXT_SECONDARY}
        />
      </View>

      {/* Font Weight */}
      <View style={styles.property}>
        <Text style={[styles.propertyLabel, { color: theme.TEXT_PRIMARY }]}>
          Font Kalınlığı
        </Text>
        <View style={styles.optionsContainer}>
          {['normal', 'bold', '600'].map((weight) => (
            <TouchableOpacity
              key={weight}
              style={[
                styles.optionButton,
                {
                  backgroundColor: properties.fontWeight === weight 
                    ? theme.PRIMARY 
                    : theme.BACKGROUND,
                  borderColor: theme.BORDER,
                }
              ]}
              onPress={() => handlePropertyChange('fontWeight', weight)}
            >
              <Text style={[
                styles.optionText,
                { 
                  color: properties.fontWeight === weight 
                    ? theme.SURFACE 
                    : theme.TEXT_PRIMARY 
                }
              ]}>
                {weight}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Text Align */}
      <View style={styles.property}>
        <Text style={[styles.propertyLabel, { color: theme.TEXT_PRIMARY }]}>
          Hizalama
        </Text>
        <View style={styles.optionsContainer}>
          {['left', 'center', 'right'].map((align) => (
            <TouchableOpacity
              key={align}
              style={[
                styles.optionButton,
                {
                  backgroundColor: properties.textAlign === align 
                    ? theme.PRIMARY 
                    : theme.BACKGROUND,
                  borderColor: theme.BORDER,
                }
              ]}
              onPress={() => handlePropertyChange('textAlign', align)}
            >
              <Ionicons 
                name={
                  align === 'left' ? 'text-outline' :
                  align === 'center' ? 'text' :
                  'text-outline'
                } 
                size={16} 
                color={properties.textAlign === align ? theme.SURFACE : theme.TEXT_PRIMARY} 
              />
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </View>
  );

  const renderKpiProperties = () => (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
        KPI Özellikleri
      </Text>

      {/* Metric */}
      <View style={styles.property}>
        <Text style={[styles.propertyLabel, { color: theme.TEXT_PRIMARY }]}>
          Metrik
        </Text>
        <View style={styles.optionsContainer}>
          {['total_income', 'total_expense', 'net_income', 'savings'].map((metric) => (
            <TouchableOpacity
              key={metric}
              style={[
                styles.optionButton,
                {
                  backgroundColor: properties.metric === metric 
                    ? theme.PRIMARY 
                    : theme.BACKGROUND,
                  borderColor: theme.BORDER,
                }
              ]}
              onPress={() => handlePropertyChange('metric', metric)}
            >
              <Text style={[
                styles.optionText,
                { 
                  color: properties.metric === metric 
                    ? theme.SURFACE 
                    : theme.TEXT_PRIMARY 
                }
              ]}>
                {metric.replace('_', ' ')}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Format */}
      <View style={styles.property}>
        <Text style={[styles.propertyLabel, { color: theme.TEXT_PRIMARY }]}>
          Format
        </Text>
        <View style={styles.optionsContainer}>
          {['currency', 'number', 'percentage'].map((format) => (
            <TouchableOpacity
              key={format}
              style={[
                styles.optionButton,
                {
                  backgroundColor: properties.format === format 
                    ? theme.PRIMARY 
                    : theme.BACKGROUND,
                  borderColor: theme.BORDER,
                }
              ]}
              onPress={() => handlePropertyChange('format', format)}
            >
              <Text style={[
                styles.optionText,
                { 
                  color: properties.format === format 
                    ? theme.SURFACE 
                    : theme.TEXT_PRIMARY 
                }
              ]}>
                {format}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Show Trend */}
      <View style={styles.property}>
        <View style={styles.switchContainer}>
          <Text style={[styles.propertyLabel, { color: theme.TEXT_PRIMARY }]}>
            Trend Göster
          </Text>
          <Switch
            value={properties.showTrend || false}
            onValueChange={(value) => handlePropertyChange('showTrend', value)}
            trackColor={{ false: theme.DISABLED, true: theme.PRIMARY }}
            thumbColor={theme.SURFACE}
          />
        </View>
      </View>
    </View>
  );

  const sections = [
    { id: 'general', name: 'Genel', icon: 'settings' },
    { id: 'specific', name: 'Özel', icon: 'options' },
    { id: 'style', name: 'Stil', icon: 'color-palette' },
  ];

  if (!selectedComponent) {
    return (
      <View style={[styles.container, { backgroundColor: theme.SURFACE }]}>
        <View style={styles.emptyState}>
          <Ionicons name="hand-left" size={48} color={theme.TEXT_SECONDARY} />
          <Text style={[styles.emptyStateText, { color: theme.TEXT_SECONDARY }]}>
            Özelliklerini düzenlemek için bir bileşen seçin
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.SURFACE }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
          Özellikler
        </Text>
        <TouchableOpacity
          style={[styles.deleteButton, { backgroundColor: theme.ERROR }]}
          onPress={handleDelete}
        >
          <Ionicons name="trash" size={16} color={theme.SURFACE} />
        </TouchableOpacity>
      </View>

      {/* Component Info */}
      <View style={[styles.componentInfo, { backgroundColor: theme.BACKGROUND }]}>
        <Text style={[styles.componentType, { color: theme.PRIMARY }]}>
          {selectedComponent.type.toUpperCase()}
        </Text>
        <Text style={[styles.componentTitle, { color: theme.TEXT_PRIMARY }]}>
          {selectedComponent.title}
        </Text>
      </View>

      {/* Section Tabs */}
      <View style={styles.sectionTabs}>
        {sections.map((section) => (
          <TouchableOpacity
            key={section.id}
            style={[
              styles.sectionTab,
              {
                backgroundColor: activeSection === section.id 
                  ? theme.PRIMARY 
                  : theme.BACKGROUND,
                borderColor: theme.BORDER,
              }
            ]}
            onPress={() => setActiveSection(section.id)}
          >
            <Ionicons 
              name={section.icon} 
              size={16} 
              color={activeSection === section.id ? theme.SURFACE : theme.TEXT_PRIMARY} 
            />
            <Text style={[
              styles.sectionTabText,
              { 
                color: activeSection === section.id 
                  ? theme.SURFACE 
                  : theme.TEXT_PRIMARY 
              }
            ]}>
              {section.name}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Properties */}
      <ScrollView style={styles.propertiesContainer}>
        {activeSection === 'general' && renderGeneralProperties()}
        
        {activeSection === 'specific' && (
          <>
            {selectedComponent.type === 'chart' && renderChartProperties()}
            {selectedComponent.type === 'text' && renderTextProperties()}
            {selectedComponent.type === 'kpi' && renderKpiProperties()}
          </>
        )}
        
        {activeSection === 'style' && (
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
              Stil Özellikleri
            </Text>
            <Text style={[styles.comingSoon, { color: theme.TEXT_SECONDARY }]}>
              Yakında gelecek...
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  deleteButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  componentInfo: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  componentType: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 4,
  },
  componentTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  sectionTabs: {
    flexDirection: 'row',
    padding: 16,
    gap: 8,
  },
  sectionTab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    gap: 6,
  },
  sectionTabText: {
    fontSize: 12,
    fontWeight: '600',
  },
  propertiesContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  property: {
    marginBottom: 16,
  },
  propertyLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
  },
  textArea: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    minHeight: 80,
    textAlignVertical: 'top',
  },
  numberInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    width: 100,
  },
  dimensionsContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  dimensionInput: {
    flex: 1,
  },
  dimensionLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  optionButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
  },
  optionText: {
    fontSize: 12,
    fontWeight: '600',
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyStateText: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: 12,
  },
  comingSoon: {
    fontSize: 14,
    fontStyle: 'italic',
    textAlign: 'center',
    padding: 20,
  },
});

export default PropertyPanel;
