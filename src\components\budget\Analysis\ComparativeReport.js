/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON>ştırmalı Rapor Komponenti
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md - Stage 4, Phase 2
 * 
 * Dönemler arası karşılaştırmalı analiz raporu
 * Maksimum 200 satır - Tek sorumluluk prensibi
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../../../context/ThemeContext';
import { formatTRY, formatUSD, formatEUR } from '../../../utils/formatters';

/**
 * Kar<PERSON><PERSON>laştırmalı rapor komponenti
 * @param {Object} props - Component props
 * @param {Array} props.comparisonData - Karşılaştırma verileri [{period, data}]
 * @param {string} props.currency - Para birimi ('TRY', 'USD', 'EUR')
 * @param {string} props.comparisonType - Karşılaştırma türü ('monthly', 'yearly')
 * @param {Function} props.onPeriodSelect - Dönem seçim callback
 * @param {Object} props.theme - Tema objesi (opsiyonel, context'ten alınır)
 */
const ComparativeReport = ({ 
  comparisonData = [], 
  currency = 'TRY',
  comparisonType = 'monthly',
  onPeriodSelect,
  theme: propTheme 
}) => {
  const { theme, isDarkMode } = useTheme();
  const currentTheme = propTheme || theme;

  // State yönetimi
  const [selectedMetric, setSelectedMetric] = useState('spending');

  /**
   * Para birimi formatı
   * @param {number} value - Formatlanacak değer
   * @returns {string} Formatlanmış değer
   */
  const formatCurrency = (value) => {
    switch (currency) {
      case 'USD':
        return formatUSD(value);
      case 'EUR':
        return formatEUR(value);
      default:
        return formatTRY(value);
    }
  };

  /**
   * Para birimi sembolü
   * @returns {string} Para birimi sembolü
   */
  const getCurrencySymbol = () => {
    switch (currency) {
      case 'USD':
        return '$';
      case 'EUR':
        return '€';
      default:
        return '₺';
    }
  };

  /**
   * Dönem formatı
   * @param {string} period - Dönem string
   * @returns {string} Formatlanmış dönem
   */
  const formatPeriod = (period) => {
    if (comparisonType === 'monthly') {
      const [year, month] = period.split('-');
      const monthNames = [
        'Oca', 'Şub', 'Mar', 'Nis', 'May', 'Haz',
        'Tem', 'Ağu', 'Eyl', 'Eki', 'Kas', 'Ara'
      ];
      return `${monthNames[parseInt(month) - 1]} ${year}`;
    }
    return period;
  };

  /**
   * Değişim yüzdesi hesaplama
   * @param {number} current - Mevcut değer
   * @param {number} previous - Önceki değer
   * @returns {Object} Değişim bilgisi {percentage, isPositive, color}
   */
  const calculateChange = (current, previous) => {
    if (previous === 0) return { percentage: 0, isPositive: true, color: currentTheme.TEXT_SECONDARY };
    
    const percentage = ((current - previous) / previous) * 100;
    const isPositive = percentage > 0;
    
    let color;
    if (selectedMetric === 'spending') {
      color = isPositive ? currentTheme.ERROR : currentTheme.SUCCESS;
    } else if (selectedMetric === 'savings') {
      color = isPositive ? currentTheme.SUCCESS : currentTheme.ERROR;
    } else {
      color = isPositive ? currentTheme.SUCCESS : currentTheme.ERROR;
    }

    return { percentage: Math.abs(percentage), isPositive, color };
  };

  /**
   * Metrik seçenekleri
   */
  const metrics = [
    { key: 'spending', label: 'Harcama', icon: 'shopping-cart' },
    { key: 'budget', label: 'Bütçe', icon: 'account-balance-wallet' },
    { key: 'savings', label: 'Tasarruf', icon: 'savings' },
    { key: 'utilization', label: 'Kullanım', icon: 'pie-chart' }
  ];

  /**
   * Seçili metrik verisi alma
   * @param {Object} data - Dönem verisi
   * @returns {number} Metrik değeri
   */
  const getMetricValue = (data) => {
    switch (selectedMetric) {
      case 'spending':
        return data.totalSpent || 0;
      case 'budget':
        return data.totalBudget || 0;
      case 'savings':
        return data.totalSavings || 0;
      case 'utilization':
        return data.utilizationRate || 0;
      default:
        return 0;
    }
  };

  if (comparisonData.length === 0) {
    return (
      <View style={[styles.container, { backgroundColor: currentTheme.SURFACE }]}>
        <View style={styles.emptyContainer}>
          <MaterialIcons name="compare" size={48} color={currentTheme.TEXT_SECONDARY} />
          <Text style={[styles.emptyTitle, { color: currentTheme.TEXT_PRIMARY }]}>
            Karşılaştırma Verisi Yok
          </Text>
          <Text style={[styles.emptySubtitle, { color: currentTheme.TEXT_SECONDARY }]}>
            Karşılaştırma için yeterli veri bulunmuyor
          </Text>
        </View>
      </View>
    );
  }

  const currencySymbol = getCurrencySymbol();
  const selectedMetricData = metrics.find(m => m.key === selectedMetric);

  return (
    <View style={[styles.container, { backgroundColor: currentTheme.SURFACE }]}>
      {/* Header */}
      <View style={styles.header}>
        <MaterialIcons name="compare" size={20} color={currentTheme.PRIMARY} />
        <Text style={[styles.title, { color: currentTheme.TEXT_PRIMARY }]}>
          Karşılaştırmalı Analiz
        </Text>
      </View>

      {/* Metrik seçici */}
      <View style={styles.metricSelector}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles.metricButtons}>
            {metrics.map((metric) => (
              <TouchableOpacity
                key={metric.key}
                style={[
                  styles.metricButton,
                  {
                    backgroundColor: selectedMetric === metric.key 
                      ? currentTheme.PRIMARY 
                      : currentTheme.BACKGROUND,
                    borderColor: currentTheme.PRIMARY,
                  }
                ]}
                onPress={() => setSelectedMetric(metric.key)}
                activeOpacity={0.7}
              >
                <MaterialIcons 
                  name={metric.icon} 
                  size={16} 
                  color={selectedMetric === metric.key ? currentTheme.WHITE : currentTheme.PRIMARY} 
                />
                <Text style={[
                  styles.metricButtonText,
                  { 
                    color: selectedMetric === metric.key 
                      ? currentTheme.WHITE 
                      : currentTheme.PRIMARY 
                  }
                ]}>
                  {metric.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
      </View>

      {/* Karşılaştırma listesi */}
      <ScrollView style={styles.comparisonList} showsVerticalScrollIndicator={false}>
        {comparisonData.map((item, index) => {
          const currentValue = getMetricValue(item.data);
          const previousItem = comparisonData[index + 1];
          const previousValue = previousItem ? getMetricValue(previousItem.data) : 0;
          const change = calculateChange(currentValue, previousValue);

          return (
            <TouchableOpacity
              key={item.period}
              style={styles.comparisonItem}
              onPress={() => onPeriodSelect && onPeriodSelect(item)}
              activeOpacity={0.7}
            >
              <View style={styles.comparisonLeft}>
                <Text style={[styles.periodText, { color: currentTheme.TEXT_PRIMARY }]}>
                  {formatPeriod(item.period)}
                </Text>
                <Text style={[styles.valueText, { color: currentTheme.TEXT_PRIMARY }]}>
                  {selectedMetric === 'utilization' 
                    ? `%${currentValue.toFixed(1)}`
                    : `${currencySymbol}${currentValue.toLocaleString('tr-TR')}`
                  }
                </Text>
              </View>

              <View style={styles.comparisonRight}>
                {index < comparisonData.length - 1 && (
                  <View style={styles.changeIndicator}>
                    <MaterialIcons 
                      name={change.isPositive ? 'arrow-upward' : 'arrow-downward'} 
                      size={16} 
                      color={change.color} 
                    />
                    <Text style={[styles.changeText, { color: change.color }]}>
                      %{change.percentage.toFixed(1)}
                    </Text>
                  </View>
                )}
                
                <MaterialIcons name="chevron-right" size={20} color={currentTheme.TEXT_SECONDARY} />
              </View>
            </TouchableOpacity>
          );
        })}
      </ScrollView>

      {/* Özet istatistikler */}
      <View style={styles.summarySection}>
        <Text style={[styles.summaryTitle, { color: currentTheme.TEXT_PRIMARY }]}>
          {selectedMetricData?.label} Özeti
        </Text>
        
        <View style={styles.summaryStats}>
          <View style={styles.summaryItem}>
            <Text style={[styles.summaryLabel, { color: currentTheme.TEXT_SECONDARY }]}>
              En Yüksek
            </Text>
            <Text style={[styles.summaryValue, { color: currentTheme.SUCCESS }]}>
              {(() => {
                const maxValue = Math.max(...comparisonData.map(item => getMetricValue(item.data)));
                return selectedMetric === 'utilization' 
                  ? `%${maxValue.toFixed(1)}`
                  : `${currencySymbol}${maxValue.toLocaleString('tr-TR')}`;
              })()}
            </Text>
          </View>

          <View style={styles.summaryItem}>
            <Text style={[styles.summaryLabel, { color: currentTheme.TEXT_SECONDARY }]}>
              En Düşük
            </Text>
            <Text style={[styles.summaryValue, { color: currentTheme.ERROR }]}>
              {(() => {
                const minValue = Math.min(...comparisonData.map(item => getMetricValue(item.data)));
                return selectedMetric === 'utilization' 
                  ? `%${minValue.toFixed(1)}`
                  : `${currencySymbol}${minValue.toLocaleString('tr-TR')}`;
              })()}
            </Text>
          </View>

          <View style={styles.summaryItem}>
            <Text style={[styles.summaryLabel, { color: currentTheme.TEXT_SECONDARY }]}>
              Ortalama
            </Text>
            <Text style={[styles.summaryValue, { color: currentTheme.PRIMARY }]}>
              {(() => {
                const avgValue = comparisonData.reduce((sum, item) => sum + getMetricValue(item.data), 0) / comparisonData.length;
                return selectedMetric === 'utilization' 
                  ? `%${avgValue.toFixed(1)}`
                  : `${currencySymbol}${avgValue.toLocaleString('tr-TR')}`;
              })()}
            </Text>
          </View>
        </View>
      </View>

      {/* Trend analizi */}
      <View style={styles.trendSection}>
        <Text style={[styles.trendTitle, { color: currentTheme.TEXT_PRIMARY }]}>
          Genel Trend
        </Text>
        
        {(() => {
          const firstValue = getMetricValue(comparisonData[comparisonData.length - 1]?.data || {});
          const lastValue = getMetricValue(comparisonData[0]?.data || {});
          const overallChange = calculateChange(lastValue, firstValue);
          
          return (
            <View style={[styles.trendCard, { backgroundColor: overallChange.color + '20' }]}>
              <MaterialIcons 
                name={overallChange.isPositive ? 'trending-up' : 'trending-down'} 
                size={24} 
                color={overallChange.color} 
              />
              <View style={styles.trendContent}>
                <Text style={[styles.trendChange, { color: overallChange.color }]}>
                  {overallChange.isPositive ? '+' : '-'}%{overallChange.percentage.toFixed(1)}
                </Text>
                <Text style={[styles.trendDescription, { color: currentTheme.TEXT_SECONDARY }]}>
                  {comparisonData.length > 1 
                    ? `Son ${comparisonData.length} dönemde ${selectedMetricData?.label.toLowerCase()} ${overallChange.isPositive ? 'artış' : 'azalış'} gösterdi`
                    : 'Trend analizi için daha fazla veri gerekli'
                  }
                </Text>
              </View>
            </View>
          );
        })()}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '500',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: 'center',
  },
  metricSelector: {
    marginBottom: 16,
  },
  metricButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  metricButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    gap: 6,
  },
  metricButtonText: {
    fontSize: 12,
    fontWeight: '500',
  },
  comparisonList: {
    maxHeight: 300,
    marginBottom: 16,
  },
  comparisonItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  comparisonLeft: {
    flex: 1,
  },
  periodText: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 2,
  },
  valueText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  comparisonRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  changeIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  changeText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  summarySection: {
    marginBottom: 16,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 12,
  },
  summaryStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  trendSection: {
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
    paddingTop: 16,
  },
  trendTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 12,
  },
  trendCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 12,
  },
  trendContent: {
    flex: 1,
  },
  trendChange: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  trendDescription: {
    fontSize: 12,
    lineHeight: 16,
  },
});

export default ComparativeReport;
