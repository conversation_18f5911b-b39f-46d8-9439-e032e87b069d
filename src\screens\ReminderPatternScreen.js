import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  RefreshControl
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import * as reminderPatternService from '../services/reminderPatternService';
import { Colors } from '../constants/colors';

/**
 * Özel Tekrarlama Desenleri Ekranı
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @returns {JSX.Element} Özel Tekrarlama Desenleri Ekranı
 */
const ReminderPatternScreen = ({ navigation }) => {
  const db = useSQLiteContext();
  
  // Durum
  const [patterns, setPatterns] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  
  // Verileri yükle
  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      const patternData = await reminderPatternService.getAllPatterns(db);
      setPatterns(patternData);
      setLoading(false);
    } catch (error) {
      console.error('Tekrarlama desenlerini yükleme hatası:', error);
      Alert.alert('Hata', 'Tekrarlama desenleri yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  }, [db]);
  
  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [loadData])
  );
  
  // Yenileme işlemi
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };
  
  // Desen düzenleme ekranına git
  const editPattern = (pattern) => {
    navigation.navigate('ReminderPatternForm', { patternId: pattern.id });
  };
  
  // Desen silme
  const deletePattern = (pattern) => {
    // Sistem desenlerini silmeye izin verme
    if (pattern.is_system === 1) {
      Alert.alert('Uyarı', 'Sistem desenleri silinemez.');
      return;
    }
    
    Alert.alert(
      'Deseni Sil',
      `"${pattern.name}" desenini silmek istediğinize emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await reminderPatternService.deletePattern(db, pattern.id);
              Alert.alert('Başarılı', 'Desen silindi.');
              loadData();
            } catch (error) {
              console.error('Desen silme hatası:', error);
              Alert.alert('Hata', 'Desen silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };
  
  // Desen öğesi
  const renderPatternItem = ({ item }) => {
    // Desen açıklaması
    const description = reminderPatternService.getPatternDescription(item.pattern_type, item.pattern_value);
    
    return (
      <View style={styles.patternItem}>
        <View style={styles.patternHeader}>
          <Text style={styles.patternName}>{item.name}</Text>
          <View style={styles.patternActions}>
            {item.is_system !== 1 && (
              <>
                <TouchableOpacity
                  style={styles.actionButton}
                  onPress={() => editPattern(item)}
                >
                  <MaterialIcons name="edit" size={20} color={Colors.PRIMARY} />
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.actionButton}
                  onPress={() => deletePattern(item)}
                >
                  <MaterialIcons name="delete" size={20} color={Colors.DANGER} />
                </TouchableOpacity>
              </>
            )}
          </View>
        </View>
        
        <View style={styles.patternContent}>
          <Text style={styles.patternDescription}>{description}</Text>
          {item.description && (
            <Text style={styles.patternNote}>{item.description}</Text>
          )}
          
          {item.is_system === 1 && (
            <View style={styles.systemBadge}>
              <Text style={styles.systemBadgeText}>Sistem</Text>
            </View>
          )}
        </View>
      </View>
    );
  };
  
  // Boş durum
  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <MaterialIcons name="event-repeat" size={64} color={Colors.GRAY_300} />
      <Text style={styles.emptyTitle}>Henüz Özel Desen Yok</Text>
      <Text style={styles.emptyText}>
        Sık kullandığınız tekrarlama desenleri için özel desenler oluşturun
      </Text>
      <TouchableOpacity
        style={styles.emptyButton}
        onPress={() => navigation.navigate('ReminderPatternForm')}
      >
        <MaterialIcons name="add" size={20} color="#fff" />
        <Text style={styles.emptyButtonText}>Desen Ekle</Text>
      </TouchableOpacity>
    </View>
  );
  
  // Yükleniyor durumu
  if (loading && !refreshing) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={styles.loadingText}>Yükleniyor...</Text>
      </View>
    );
  }
  
  return (
    <View style={styles.container}>
      {/* Başlık */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Özel Tekrarlama Desenleri</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => navigation.navigate('ReminderPatternForm')}
        >
          <MaterialIcons name="add" size={24} color="#fff" />
        </TouchableOpacity>
      </View>
      
      {/* Desen Listesi */}
      <FlatList
        data={patterns}
        renderItem={renderPatternItem}
        keyExtractor={item => item.id.toString()}
        contentContainerStyle={styles.listContent}
        ListEmptyComponent={renderEmptyState}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[Colors.PRIMARY]}
          />
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
  },
  addButton: {
    backgroundColor: Colors.PRIMARY,
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: Colors.PRIMARY,
  },
  listContent: {
    padding: 16,
    flexGrow: 1,
  },
  patternItem: {
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
  },
  patternHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  patternName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    flex: 1,
  },
  patternActions: {
    flexDirection: 'row',
  },
  actionButton: {
    padding: 8,
    marginLeft: 4,
  },
  patternContent: {
    marginLeft: 0,
  },
  patternDescription: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.TEXT_DARK,
    marginBottom: 4,
  },
  patternNote: {
    fontSize: 14,
    color: Colors.TEXT_LIGHT,
    marginBottom: 8,
  },
  systemBadge: {
    backgroundColor: Colors.GRAY_300,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    alignSelf: 'flex-start',
    marginTop: 4,
  },
  systemBadgeText: {
    fontSize: 12,
    color: Colors.TEXT_DARK,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    marginTop: 16,
  },
  emptyText: {
    fontSize: 14,
    color: Colors.TEXT_LIGHT,
    textAlign: 'center',
    marginTop: 8,
    marginHorizontal: 32,
  },
  emptyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 24,
  },
  emptyButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 8,
  },
});

export default ReminderPatternScreen;
