import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  ActivityIndicator,
  Alert,
  RefreshControl
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import { useAppContext } from '../context/AppContext';
import { simpleExchangeRateService } from '../services/simpleExchangeRateService';

/**
 * Döviz Çevirici Ekranı
 * Gerçek zamanlı döviz kurları ile çeviri yapma
 */
export default function CurrencyConverterScreen({ navigation }) {
  const insets = useSafeAreaInsets();
  const { theme } = useAppContext();

  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [exchangeRates, setExchangeRates] = useState({});
  const [amount, setAmount] = useState('1');
  const [fromCurrency, setFromCurrency] = useState('TRY');
  const [toCurrency, setToCurrency] = useState('USD');
  const [result, setResult] = useState(0);
  const [lastUpdated, setLastUpdated] = useState(null);

  // Desteklenen para birimleri
  const currencies = [
    { code: 'TRY', name: 'Türk Lirası', symbol: '₺' },
    { code: 'USD', name: 'Amerikan Doları', symbol: '$' },
    { code: 'EUR', name: 'Euro', symbol: '€' },
    { code: 'GBP', name: 'İngiliz Sterlini', symbol: '£' },
    { code: 'JPY', name: 'Japon Yeni', symbol: '¥' },
    { code: 'CHF', name: 'İsviçre Frangı', symbol: 'CHF' },
    { code: 'CAD', name: 'Kanada Doları', symbol: 'C$' },
    { code: 'AUD', name: 'Avustralya Doları', symbol: 'A$' },
    { code: 'CNY', name: 'Çin Yuanı', symbol: '¥' },
    { code: 'RUB', name: 'Rus Rublesi', symbol: '₽' }
  ];

  // Döviz kurlarını yükle
  const loadExchangeRates = async () => {
    try {
      setLoading(true);

      // API'den döviz kurlarını çek
      const response = await fetch('https://api.exchangerate-api.com/v4/latest/TRY');
      const data = await response.json();

      if (data && data.rates) {
        setExchangeRates(data.rates);
        setLastUpdated(new Date(data.date));
      } else {
        throw new Error('API\'den geçersiz veri geldi');
      }

      setLoading(false);
    } catch (error) {
      console.error('Döviz kurları yükleme hatası:', error);
      Alert.alert('Hata', 'Döviz kurları yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  };

  // Yenileme işlemi
  const onRefresh = async () => {
    setRefreshing(true);
    await loadExchangeRates();
    setRefreshing(false);
  };

  // İlk yükleme
  useEffect(() => {
    loadExchangeRates();
  }, []);

  // Çeviri hesaplama
  useEffect(() => {
    if (exchangeRates && Object.keys(exchangeRates).length > 0) {
      calculateConversion();
    }
  }, [amount, fromCurrency, toCurrency, exchangeRates]);

  // Çeviri hesaplama fonksiyonu
  const calculateConversion = () => {
    try {
      const amountNum = parseFloat(amount) || 0;
      
      if (fromCurrency === toCurrency) {
        setResult(amountNum);
        return;
      }

      // TRY base olduğu için
      let rate = 1;
      
      if (fromCurrency === 'TRY') {
        // TRY'den diğer para birimine
        rate = exchangeRates[toCurrency] || 1;
        setResult(amountNum / rate);
      } else if (toCurrency === 'TRY') {
        // Diğer para biriminden TRY'ye
        rate = exchangeRates[fromCurrency] || 1;
        setResult(amountNum * rate);
      } else {
        // İki farklı para birimi arasında (TRY üzerinden)
        const fromRate = exchangeRates[fromCurrency] || 1;
        const toRate = exchangeRates[toCurrency] || 1;
        const tryAmount = amountNum * fromRate;
        setResult(tryAmount / toRate);
      }
    } catch (error) {
      console.error('Çeviri hesaplama hatası:', error);
      setResult(0);
    }
  };

  // Para birimlerini değiştir
  const swapCurrencies = () => {
    const temp = fromCurrency;
    setFromCurrency(toCurrency);
    setToCurrency(temp);
  };

  // Para birimi seçici
  const CurrencySelector = ({ value, onSelect, label }) => (
    <View style={[styles.currencySelector, { backgroundColor: theme.CARD, borderColor: theme.BORDER }]}>
      <Text style={[styles.currencySelectorLabel, { color: theme.TEXT_SECONDARY }]}>{label}</Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.currencyList}>
        {currencies.map((currency) => (
          <TouchableOpacity
            key={currency.code}
            style={[
              styles.currencyItem,
              value === currency.code && { backgroundColor: theme.PRIMARY },
              { borderColor: theme.BORDER }
            ]}
            onPress={() => onSelect(currency.code)}
          >
            <Text style={[
              styles.currencyCode,
              { color: value === currency.code ? theme.WHITE : theme.TEXT_PRIMARY }
            ]}>
              {currency.code}
            </Text>
            <Text style={[
              styles.currencySymbol,
              { color: value === currency.code ? theme.WHITE : theme.TEXT_SECONDARY }
            ]}>
              {currency.symbol}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  // Hızlı tutar seçimi
  const QuickAmounts = () => {
    const amounts = ['1', '10', '100', '1000', '10000'];
    
    return (
      <View style={styles.quickAmountsContainer}>
        <Text style={[styles.quickAmountsLabel, { color: theme.TEXT_SECONDARY }]}>Hızlı Tutarlar:</Text>
        <View style={styles.quickAmounts}>
          {amounts.map((quickAmount) => (
            <TouchableOpacity
              key={quickAmount}
              style={[
                styles.quickAmountButton,
                { backgroundColor: theme.SURFACE, borderColor: theme.BORDER }
              ]}
              onPress={() => setAmount(quickAmount)}
            >
              <Text style={[styles.quickAmountText, { color: theme.TEXT_PRIMARY }]}>
                {quickAmount}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent, { backgroundColor: theme.BACKGROUND }]}>
        <ActivityIndicator size="large" color={theme.PRIMARY} />
        <Text style={[styles.loadingText, { color: theme.TEXT_PRIMARY }]}>Döviz kurları yükleniyor...</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { paddingTop: insets.top, backgroundColor: theme.BACKGROUND }]}>
      <View style={[styles.header, { backgroundColor: theme.PRIMARY }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color={theme.WHITE} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.WHITE }]}>Döviz Çevirici</Text>
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={onRefresh}
          disabled={refreshing}
        >
          <MaterialIcons 
            name="refresh" 
            size={24} 
            color={theme.WHITE}
            style={refreshing && { opacity: 0.5 }}
          />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Tutar Girişi */}
        <View style={[styles.amountContainer, { backgroundColor: theme.CARD }]}>
          <Text style={[styles.amountLabel, { color: theme.TEXT_SECONDARY }]}>Çevrilecek Tutar</Text>
          <TextInput
            style={[styles.amountInput, { color: theme.TEXT_PRIMARY, borderColor: theme.BORDER }]}
            value={amount}
            onChangeText={setAmount}
            keyboardType="numeric"
            placeholder="0"
            placeholderTextColor={theme.TEXT_SECONDARY}
          />
        </View>

        {/* Hızlı Tutarlar */}
        <QuickAmounts />

        {/* Para Birimi Seçiciler */}
        <CurrencySelector
          value={fromCurrency}
          onSelect={setFromCurrency}
          label="Kaynak Para Birimi"
        />

        {/* Değiştirme Butonu */}
        <View style={styles.swapContainer}>
          <TouchableOpacity
            style={[styles.swapButton, { backgroundColor: theme.PRIMARY }]}
            onPress={swapCurrencies}
          >
            <MaterialIcons name="swap-vert" size={24} color={theme.WHITE} />
          </TouchableOpacity>
        </View>

        <CurrencySelector
          value={toCurrency}
          onSelect={setToCurrency}
          label="Hedef Para Birimi"
        />

        {/* Sonuç */}
        <View style={[styles.resultContainer, { backgroundColor: theme.CARD }]}>
          <Text style={[styles.resultLabel, { color: theme.TEXT_SECONDARY }]}>Sonuç</Text>
          <Text style={[styles.resultAmount, { color: theme.PRIMARY }]}>
            {result.toLocaleString('tr-TR', { 
              minimumFractionDigits: 2, 
              maximumFractionDigits: 6 
            })} {toCurrency}
          </Text>
          
          {/* Kur Bilgisi */}
          <Text style={[styles.rateInfo, { color: theme.TEXT_SECONDARY }]}>
            1 {fromCurrency} = {(result / (parseFloat(amount) || 1)).toLocaleString('tr-TR', { 
              minimumFractionDigits: 2, 
              maximumFractionDigits: 6 
            })} {toCurrency}
          </Text>
        </View>

        {/* Son Güncelleme */}
        {lastUpdated && (
          <View style={styles.lastUpdatedContainer}>
            <MaterialIcons name="schedule" size={16} color={theme.TEXT_SECONDARY} />
            <Text style={[styles.lastUpdatedText, { color: theme.TEXT_SECONDARY }]}>
              Son güncelleme: {lastUpdated.toLocaleString('tr-TR')}
            </Text>
          </View>
        )}

        {/* Popüler Kurlar */}
        <View style={[styles.popularRatesContainer, { backgroundColor: theme.CARD }]}>
          <Text style={[styles.popularRatesTitle, { color: theme.TEXT_PRIMARY }]}>Popüler Kurlar</Text>
          {['USD', 'EUR', 'GBP'].map((currency) => (
            <View key={currency} style={styles.popularRateItem}>
              <Text style={[styles.popularRateCurrency, { color: theme.TEXT_PRIMARY }]}>
                1 {currency}
              </Text>
              <Text style={[styles.popularRateValue, { color: theme.TEXT_SECONDARY }]}>
                {exchangeRates[currency]?.toLocaleString('tr-TR', { 
                  minimumFractionDigits: 2, 
                  maximumFractionDigits: 4 
                })} TRY
              </Text>
            </View>
          ))}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  headerTitle: {
    flex: 1,
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  refreshButton: {
    padding: 8,
    marginLeft: 8,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  amountContainer: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  amountLabel: {
    fontSize: 14,
    marginBottom: 8,
  },
  amountInput: {
    fontSize: 24,
    fontWeight: 'bold',
    borderBottomWidth: 2,
    paddingVertical: 8,
    textAlign: 'center',
  },
  quickAmountsContainer: {
    marginBottom: 16,
  },
  quickAmountsLabel: {
    fontSize: 14,
    marginBottom: 8,
  },
  quickAmounts: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  quickAmountButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginHorizontal: 2,
    alignItems: 'center',
  },
  quickAmountText: {
    fontSize: 14,
    fontWeight: '500',
  },
  currencySelector: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  currencySelectorLabel: {
    fontSize: 14,
    marginBottom: 12,
  },
  currencyList: {
    flexDirection: 'row',
  },
  currencyItem: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginRight: 8,
    alignItems: 'center',
    minWidth: 60,
  },
  currencyCode: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  currencySymbol: {
    fontSize: 12,
    marginTop: 2,
  },
  swapContainer: {
    alignItems: 'center',
    marginVertical: 8,
  },
  swapButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  resultContainer: {
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  resultLabel: {
    fontSize: 14,
    marginBottom: 8,
  },
  resultAmount: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  rateInfo: {
    fontSize: 14,
    textAlign: 'center',
  },
  lastUpdatedContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  lastUpdatedText: {
    fontSize: 12,
    marginLeft: 4,
  },
  popularRatesContainer: {
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  popularRatesTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  popularRateItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  popularRateCurrency: {
    fontSize: 14,
    fontWeight: '500',
  },
  popularRateValue: {
    fontSize: 14,
  },
});
