/**
 * <PERSON><PERSON> Miktar Göstergesi Komponenti
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md - Stage 3, Phase 1
 * 
 * Kalan miktar ve günlük ortalama göstergesi
 * Maksimum 150 satır - Tek sorumluluk prensibi
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../../../context/ThemeContext';
import { formatTRY, formatUSD, formatEUR } from '../../../utils/formatters';

/**
 * <PERSON>lan miktar göstergesi komponenti
 * @param {Object} props - Component props
 * @param {number} props.remaining - <PERSON>lan miktar
 * @param {number} props.dailyAverage - Günlük ortalama harcama
 * @param {string} props.currency - Para birimi ('TRY', 'USD', 'EUR')
 * @param {number} props.daysLeft - <PERSON><PERSON> gün say<PERSON>ı
 * @param {Object} props.theme - <PERSON><PERSON> objesi (opsiyonel, context'ten alınır)
 */
const RemainingAmountDisplay = ({ 
  remaining = 0, 
  dailyAverage = 0, 
  currency = 'TRY', 
  daysLeft = 0,
  theme: propTheme 
}) => {
  const { theme, isDarkMode } = useTheme();
  const currentTheme = propTheme || theme;

  /**
   * Para birimi formatı
   * @param {number} value - Formatlanacak değer
   * @returns {string} Formatlanmış değer
   */
  const formatCurrency = (value) => {
    switch (currency) {
      case 'USD':
        return formatUSD(value);
      case 'EUR':
        return formatEUR(value);
      default:
        return formatTRY(value);
    }
  };

  /**
   * Para birimi sembolü
   * @returns {string} Para birimi sembolü
   */
  const getCurrencySymbol = () => {
    switch (currency) {
      case 'USD':
        return '$';
      case 'EUR':
        return '€';
      default:
        return '₺';
    }
  };

  /**
   * Günlük önerilen harcama hesaplama
   * @returns {number} Günlük önerilen harcama
   */
  const getDailyRecommendation = () => {
    if (daysLeft <= 0) return 0;
    return remaining / daysLeft;
  };

  /**
   * Durum rengi belirleme
   * @returns {string} Durum rengi
   */
  const getStatusColor = () => {
    if (remaining < 0) return currentTheme.ERROR;
    if (remaining < dailyAverage * 3) return currentTheme.WARNING;
    return currentTheme.SUCCESS;
  };

  /**
   * Durum ikonu belirleme
   * @returns {string} İkon adı
   */
  const getStatusIcon = () => {
    if (remaining < 0) return 'error';
    if (remaining < dailyAverage * 3) return 'warning';
    return 'check-circle';
  };

  const statusColor = getStatusColor();
  const statusIcon = getStatusIcon();
  const dailyRecommendation = getDailyRecommendation();
  const currencySymbol = getCurrencySymbol();

  return (
    <View style={[styles.container, { backgroundColor: currentTheme.SURFACE }]}>
      {/* Header */}
      <View style={styles.header}>
        <MaterialIcons name={statusIcon} size={20} color={statusColor} />
        <Text style={[styles.title, { color: currentTheme.TEXT_PRIMARY }]}>
          Kalan Bütçe
        </Text>
      </View>

      {/* Ana miktar */}
      <View style={styles.mainAmount}>
        <Text style={[styles.currencySymbol, { color: statusColor }]}>
          {currencySymbol}
        </Text>
        <Text style={[styles.amount, { color: statusColor }]}>
          {Math.abs(remaining).toLocaleString('tr-TR')}
        </Text>
        {remaining < 0 && (
          <Text style={[styles.negativeIndicator, { color: currentTheme.ERROR }]}>
            (aşım)
          </Text>
        )}
      </View>

      {/* Formatlanmış miktar */}
      <Text style={[styles.formattedAmount, { color: currentTheme.TEXT_SECONDARY }]}>
        {formatCurrency(Math.abs(remaining))}
      </Text>

      {/* Günlük bilgiler */}
      {daysLeft > 0 && (
        <View style={styles.dailyInfo}>
          <View style={styles.dailyItem}>
            <Text style={[styles.dailyLabel, { color: currentTheme.TEXT_SECONDARY }]}>
              Günlük önerilen:
            </Text>
            <Text style={[styles.dailyValue, { color: currentTheme.PRIMARY }]}>
              {currencySymbol}{dailyRecommendation.toLocaleString('tr-TR', { maximumFractionDigits: 0 })}
            </Text>
          </View>

          <View style={styles.dailyItem}>
            <Text style={[styles.dailyLabel, { color: currentTheme.TEXT_SECONDARY }]}>
              Kalan gün:
            </Text>
            <Text style={[styles.dailyValue, { color: currentTheme.TEXT_PRIMARY }]}>
              {daysLeft} gün
            </Text>
          </View>
        </View>
      )}

      {/* Uyarı mesajı */}
      {remaining < 0 && (
        <View style={[styles.warningMessage, { backgroundColor: currentTheme.ERROR + '20' }]}>
          <MaterialIcons name="warning" size={16} color={currentTheme.ERROR} />
          <Text style={[styles.warningText, { color: currentTheme.ERROR }]}>
            Bütçe aşıldı!
          </Text>
        </View>
      )}

      {remaining >= 0 && remaining < dailyAverage * 3 && (
        <View style={[styles.warningMessage, { backgroundColor: currentTheme.WARNING + '20' }]}>
          <MaterialIcons name="info" size={16} color={currentTheme.WARNING} />
          <Text style={[styles.warningText, { color: currentTheme.WARNING }]}>
            Dikkatli harcama yapın
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '500',
  },
  mainAmount: {
    flexDirection: 'row',
    alignItems: 'baseline',
    justifyContent: 'center',
    marginBottom: 8,
    gap: 4,
  },
  currencySymbol: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  amount: {
    fontSize: 32,
    fontWeight: 'bold',
  },
  negativeIndicator: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  formattedAmount: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 16,
  },
  dailyInfo: {
    gap: 8,
    marginBottom: 12,
  },
  dailyItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dailyLabel: {
    fontSize: 14,
  },
  dailyValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  warningMessage: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    gap: 8,
  },
  warningText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default RemainingAmountDisplay;
