import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const { width: screenWidth } = Dimensions.get('window');

/**
 * Predictive Analytics Component
 * Provides future financial predictions and trend forecasting
 * Uses historical data to predict spending patterns and financial outcomes
 */
const PredictiveAnalytics = ({ 
  historicalData = [],
  predictionPeriod = 'monthly',
  onPredictionUpdate,
  theme 
}) => {
  const [predictions, setPredictions] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState(predictionPeriod);
  const [confidence, setConfidence] = useState(0);
  const [trends, setTrends] = useState([]);

  const predictionPeriods = [
    { id: 'weekly', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', icon: 'calendar', duration: 7 },
    { id: 'monthly', name: 'Aylı<PERSON>', icon: 'calendar-outline', duration: 30 },
    { id: 'quarterly', name: '3 Aylık', icon: 'calendar-clear', duration: 90 },
    { id: 'yearly', name: 'Yıllık', icon: 'calendar-number', duration: 365 },
  ];

  const predictionTypes = [
    {
      id: 'spending',
      name: 'Harcama Tahmini',
      icon: 'card',
      description: 'Gelecek dönem harcama miktarı',
    },
    {
      id: 'income',
      name: 'Gelir Tahmini',
      icon: 'trending-up',
      description: 'Beklenen gelir miktarı',
    },
    {
      id: 'savings',
      name: 'Tasarruf Potansiyeli',
      icon: 'wallet',
      description: 'Tasarruf edebileceğiniz miktar',
    },
    {
      id: 'budget_variance',
      name: 'Bütçe Sapması',
      icon: 'analytics',
      description: 'Bütçeden sapma oranı',
    },
  ];

  // Generate predictions based on historical data
  const generatePredictions = async (period = selectedPeriod) => {
    setIsLoading(true);
    
    try {
      // Simulate prediction calculation
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const periodData = predictionPeriods.find(p => p.id === period);
      const predictions = await calculatePredictions(historicalData, periodData);
      const confidenceScore = calculateConfidence(historicalData, predictions);
      const trendAnalysis = analyzeTrends(historicalData, predictions);
      
      setPredictions(predictions);
      setConfidence(confidenceScore);
      setTrends(trendAnalysis);
      
      if (onPredictionUpdate) {
        onPredictionUpdate({
          predictions,
          confidence: confidenceScore,
          trends: trendAnalysis,
          period: periodData,
        });
      }
      
    } catch (error) {
      console.error('Prediction error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Calculate predictions using simple linear regression and moving averages
  const calculatePredictions = async (data, period) => {
    if (data.length === 0) return [];
    
    const predictions = [];
    const periodDays = period.duration;
    
    // Group data by categories
    const categoryData = groupDataByCategory(data);
    
    // Calculate predictions for each category
    Object.keys(categoryData).forEach(category => {
      const categoryTransactions = categoryData[category];
      const monthlyAverage = calculateMonthlyAverage(categoryTransactions);
      const trend = calculateTrend(categoryTransactions);
      const seasonality = calculateSeasonality(categoryTransactions);
      
      // Predict future amount
      const basePrediction = monthlyAverage * (periodDays / 30);
      const trendAdjustment = trend * (periodDays / 30);
      const seasonalAdjustment = seasonality;
      
      const predictedAmount = basePrediction + trendAdjustment + seasonalAdjustment;
      const variance = calculateVariance(categoryTransactions);
      
      predictions.push({
        id: `${category}_${period.id}`,
        category,
        type: 'spending',
        period: period.name,
        predictedAmount: Math.max(0, predictedAmount),
        confidence: Math.max(0.3, Math.min(0.95, 1 - (variance / predictedAmount))),
        trend: trend > 0 ? 'increasing' : trend < 0 ? 'decreasing' : 'stable',
        trendPercentage: Math.abs((trend / monthlyAverage) * 100),
        historicalAverage: monthlyAverage,
        variance,
        factors: [
          'Geçmiş harcama kalıpları',
          'Mevsimsel etkiler',
          'Trend analizi',
        ],
      });
    });
    
    // Add overall predictions
    const totalHistoricalAverage = data.reduce((sum, item) => sum + item.amount, 0) / data.length;
    const overallTrend = calculateOverallTrend(data);
    
    predictions.push({
      id: `total_${period.id}`,
      category: 'Toplam',
      type: 'total',
      period: period.name,
      predictedAmount: totalHistoricalAverage * (periodDays / 30) + overallTrend,
      confidence: 0.75,
      trend: overallTrend > 0 ? 'increasing' : overallTrend < 0 ? 'decreasing' : 'stable',
      trendPercentage: Math.abs((overallTrend / totalHistoricalAverage) * 100),
      historicalAverage: totalHistoricalAverage,
      factors: [
        'Genel harcama trendi',
        'Ekonomik faktörler',
        'Kişisel finansal durum',
      ],
    });
    
    return predictions.sort((a, b) => b.predictedAmount - a.predictedAmount);
  };

  // Helper functions for calculations
  const groupDataByCategory = (data) => {
    return data.reduce((groups, item) => {
      const category = item.category || 'Diğer';
      if (!groups[category]) {
        groups[category] = [];
      }
      groups[category].push(item);
      return groups;
    }, {});
  };

  const calculateMonthlyAverage = (transactions) => {
    if (transactions.length === 0) return 0;
    
    const monthlyTotals = {};
    transactions.forEach(transaction => {
      const month = new Date(transaction.date).toISOString().slice(0, 7);
      if (!monthlyTotals[month]) {
        monthlyTotals[month] = 0;
      }
      monthlyTotals[month] += transaction.amount;
    });
    
    const months = Object.keys(monthlyTotals);
    const total = Object.values(monthlyTotals).reduce((sum, amount) => sum + amount, 0);
    
    return months.length > 0 ? total / months.length : 0;
  };

  const calculateTrend = (transactions) => {
    if (transactions.length < 2) return 0;
    
    const monthlyData = {};
    transactions.forEach(transaction => {
      const month = new Date(transaction.date).toISOString().slice(0, 7);
      if (!monthlyData[month]) {
        monthlyData[month] = 0;
      }
      monthlyData[month] += transaction.amount;
    });
    
    const months = Object.keys(monthlyData).sort();
    if (months.length < 2) return 0;
    
    const firstMonth = monthlyData[months[0]];
    const lastMonth = monthlyData[months[months.length - 1]];
    
    return (lastMonth - firstMonth) / months.length;
  };

  const calculateSeasonality = (transactions) => {
    // Simple seasonality calculation based on current month
    const currentMonth = new Date().getMonth();
    const seasonalFactors = [
      1.1, 0.9, 1.0, 1.0, 1.0, 1.0, // Jan-Jun
      1.2, 1.1, 1.0, 1.0, 1.1, 1.3  // Jul-Dec
    ];
    
    return seasonalFactors[currentMonth] || 1.0;
  };

  const calculateVariance = (transactions) => {
    if (transactions.length === 0) return 0;
    
    const amounts = transactions.map(t => t.amount);
    const mean = amounts.reduce((sum, amount) => sum + amount, 0) / amounts.length;
    const variance = amounts.reduce((sum, amount) => sum + Math.pow(amount - mean, 2), 0) / amounts.length;
    
    return Math.sqrt(variance);
  };

  const calculateOverallTrend = (data) => {
    if (data.length < 2) return 0;
    
    const monthlyTotals = {};
    data.forEach(item => {
      const month = new Date(item.date).toISOString().slice(0, 7);
      if (!monthlyTotals[month]) {
        monthlyTotals[month] = 0;
      }
      monthlyTotals[month] += item.amount;
    });
    
    const months = Object.keys(monthlyTotals).sort();
    if (months.length < 2) return 0;
    
    const firstMonth = monthlyTotals[months[0]];
    const lastMonth = monthlyTotals[months[months.length - 1]];
    
    return (lastMonth - firstMonth) / months.length;
  };

  const calculateConfidence = (data, predictions) => {
    if (data.length === 0 || predictions.length === 0) return 0;
    
    // Base confidence on data quantity and consistency
    let confidence = Math.min(0.9, data.length / 100); // More data = higher confidence
    
    // Adjust based on prediction variance
    const avgVariance = predictions.reduce((sum, p) => sum + (p.variance || 0), 0) / predictions.length;
    const avgAmount = predictions.reduce((sum, p) => sum + p.predictedAmount, 0) / predictions.length;
    
    if (avgAmount > 0) {
      const varianceRatio = avgVariance / avgAmount;
      confidence *= Math.max(0.3, 1 - varianceRatio);
    }
    
    return Math.max(0.3, Math.min(0.95, confidence));
  };

  const analyzeTrends = (data, predictions) => {
    const trends = [];
    
    // Overall spending trend
    const totalPrediction = predictions.find(p => p.type === 'total');
    if (totalPrediction) {
      trends.push({
        id: 'overall_spending',
        title: 'Genel Harcama Trendi',
        direction: totalPrediction.trend,
        percentage: totalPrediction.trendPercentage,
        description: `Harcamalarınız ${totalPrediction.trend === 'increasing' ? 'artış' : totalPrediction.trend === 'decreasing' ? 'azalış' : 'sabit'} eğiliminde`,
        impact: totalPrediction.trend === 'increasing' ? 'negative' : totalPrediction.trend === 'decreasing' ? 'positive' : 'neutral',
      });
    }
    
    // Category trends
    const categoryPredictions = predictions.filter(p => p.type === 'spending');
    categoryPredictions.slice(0, 3).forEach(prediction => {
      trends.push({
        id: `category_${prediction.category}`,
        title: `${prediction.category} Kategorisi`,
        direction: prediction.trend,
        percentage: prediction.trendPercentage,
        description: `${prediction.category} harcamalarınız ${prediction.trend === 'increasing' ? 'artıyor' : prediction.trend === 'decreasing' ? 'azalıyor' : 'sabit'}`,
        impact: prediction.trend === 'increasing' ? 'negative' : prediction.trend === 'decreasing' ? 'positive' : 'neutral',
      });
    });
    
    return trends;
  };

  const getTrendIcon = (direction) => {
    switch (direction) {
      case 'increasing': return 'trending-up';
      case 'decreasing': return 'trending-down';
      default: return 'remove';
    }
  };

  const getTrendColor = (impact) => {
    switch (impact) {
      case 'positive': return theme.SUCCESS;
      case 'negative': return theme.ERROR;
      default: return theme.INFO;
    }
  };

  const getConfidenceColor = (confidence) => {
    if (confidence >= 0.8) return theme.SUCCESS;
    if (confidence >= 0.6) return theme.WARNING;
    return theme.ERROR;
  };

  useEffect(() => {
    if (historicalData.length > 0) {
      generatePredictions();
    }
  }, [historicalData, selectedPeriod]);

  return (
    <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.SURFACE }]}>
        <View style={styles.headerContent}>
          <Ionicons name="analytics" size={24} color={theme.PRIMARY} />
          <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
            Tahmin Analizi
          </Text>
        </View>
        
        <TouchableOpacity
          style={[styles.refreshButton, { backgroundColor: theme.PRIMARY }]}
          onPress={() => generatePredictions()}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color={theme.SURFACE} />
          ) : (
            <Ionicons name="refresh" size={16} color={theme.SURFACE} />
          )}
        </TouchableOpacity>
      </View>

      {/* Period Selection */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.periodContainer}
      >
        {predictionPeriods.map((period) => (
          <TouchableOpacity
            key={period.id}
            style={[
              styles.periodButton,
              {
                backgroundColor: selectedPeriod === period.id ? theme.PRIMARY : theme.BACKGROUND,
                borderColor: theme.BORDER,
              }
            ]}
            onPress={() => {
              setSelectedPeriod(period.id);
              generatePredictions(period.id);
            }}
            disabled={isLoading}
          >
            <Ionicons 
              name={period.icon} 
              size={16} 
              color={selectedPeriod === period.id ? theme.SURFACE : theme.TEXT_PRIMARY} 
            />
            <Text style={[
              styles.periodText,
              { 
                color: selectedPeriod === period.id ? theme.SURFACE : theme.TEXT_PRIMARY 
              }
            ]}>
              {period.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Content */}
      <ScrollView style={styles.content}>
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.PRIMARY} />
            <Text style={[styles.loadingText, { color: theme.TEXT_SECONDARY }]}>
              Tahminler hesaplanıyor...
            </Text>
          </View>
        ) : predictions.length > 0 ? (
          <>
            {/* Confidence Score */}
            <View style={[styles.confidenceCard, { backgroundColor: theme.SURFACE }]}>
              <View style={styles.confidenceHeader}>
                <Ionicons name="shield-checkmark" size={20} color={getConfidenceColor(confidence)} />
                <Text style={[styles.confidenceTitle, { color: theme.TEXT_PRIMARY }]}>
                  Tahmin Güvenilirliği
                </Text>
              </View>
              <View style={styles.confidenceContent}>
                <Text style={[styles.confidenceScore, { color: getConfidenceColor(confidence) }]}>
                  %{(confidence * 100).toFixed(0)}
                </Text>
                <Text style={[styles.confidenceDescription, { color: theme.TEXT_SECONDARY }]}>
                  {confidence >= 0.8 ? 'Yüksek güvenilirlik' : 
                   confidence >= 0.6 ? 'Orta güvenilirlik' : 'Düşük güvenilirlik'}
                </Text>
              </View>
            </View>

            {/* Predictions */}
            <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
              <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
                📈 Tahminler
              </Text>
              
              {predictions.map((prediction) => (
                <View 
                  key={prediction.id}
                  style={[styles.predictionCard, { backgroundColor: theme.BACKGROUND }]}
                >
                  <View style={styles.predictionHeader}>
                    <Text style={[styles.predictionCategory, { color: theme.TEXT_PRIMARY }]}>
                      {prediction.category}
                    </Text>
                    <View style={styles.predictionTrend}>
                      <Ionicons 
                        name={getTrendIcon(prediction.trend)} 
                        size={16} 
                        color={getTrendColor(prediction.trend === 'increasing' ? 'negative' : 'positive')} 
                      />
                      <Text style={[
                        styles.predictionTrendText,
                        { color: getTrendColor(prediction.trend === 'increasing' ? 'negative' : 'positive') }
                      ]}>
                        %{prediction.trendPercentage.toFixed(1)}
                      </Text>
                    </View>
                  </View>
                  
                  <Text style={[styles.predictionAmount, { color: theme.PRIMARY }]}>
                    ₺{prediction.predictedAmount.toLocaleString()}
                  </Text>
                  
                  <Text style={[styles.predictionPeriod, { color: theme.TEXT_SECONDARY }]}>
                    {prediction.period} tahmini
                  </Text>
                  
                  <View style={styles.predictionMeta}>
                    <Text style={[styles.predictionConfidence, { color: getConfidenceColor(prediction.confidence) }]}>
                      Güvenilirlik: %{(prediction.confidence * 100).toFixed(0)}
                    </Text>
                    <Text style={[styles.predictionAverage, { color: theme.TEXT_SECONDARY }]}>
                      Ortalama: ₺{prediction.historicalAverage.toLocaleString()}
                    </Text>
                  </View>
                </View>
              ))}
            </View>

            {/* Trends */}
            {trends.length > 0 && (
              <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
                <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
                  📊 Trend Analizi
                </Text>
                
                {trends.map((trend) => (
                  <View 
                    key={trend.id}
                    style={[styles.trendCard, { backgroundColor: theme.BACKGROUND }]}
                  >
                    <View style={styles.trendHeader}>
                      <Ionicons 
                        name={getTrendIcon(trend.direction)} 
                        size={18} 
                        color={getTrendColor(trend.impact)} 
                      />
                      <Text style={[styles.trendTitle, { color: theme.TEXT_PRIMARY }]}>
                        {trend.title}
                      </Text>
                    </View>
                    
                    <Text style={[styles.trendDescription, { color: theme.TEXT_SECONDARY }]}>
                      {trend.description}
                    </Text>
                    
                    <Text style={[
                      styles.trendPercentage,
                      { color: getTrendColor(trend.impact) }
                    ]}>
                      %{trend.percentage.toFixed(1)} değişim
                    </Text>
                  </View>
                ))}
              </View>
            )}
          </>
        ) : (
          <View style={styles.emptyState}>
            <Ionicons name="analytics-outline" size={48} color={theme.TEXT_SECONDARY} />
            <Text style={[styles.emptyStateText, { color: theme.TEXT_SECONDARY }]}>
              Tahmin yapabilmek için yeterli geçmiş veri bulunmuyor
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  refreshButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  periodContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  periodButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 12,
    gap: 6,
  },
  periodText: {
    fontSize: 14,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  loadingText: {
    fontSize: 16,
    marginTop: 16,
  },
  confidenceCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  confidenceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 12,
  },
  confidenceTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  confidenceContent: {
    alignItems: 'center',
  },
  confidenceScore: {
    fontSize: 32,
    fontWeight: 'bold',
  },
  confidenceDescription: {
    fontSize: 14,
    marginTop: 4,
  },
  section: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  predictionCard: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  predictionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  predictionCategory: {
    fontSize: 14,
    fontWeight: '600',
  },
  predictionTrend: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  predictionTrendText: {
    fontSize: 12,
    fontWeight: '600',
  },
  predictionAmount: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  predictionPeriod: {
    fontSize: 12,
    marginBottom: 8,
  },
  predictionMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  predictionConfidence: {
    fontSize: 12,
    fontWeight: '600',
  },
  predictionAverage: {
    fontSize: 12,
  },
  trendCard: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  trendHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 4,
  },
  trendTitle: {
    fontSize: 14,
    fontWeight: '600',
  },
  trendDescription: {
    fontSize: 12,
    lineHeight: 16,
    marginBottom: 4,
  },
  trendPercentage: {
    fontSize: 12,
    fontWeight: '600',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyStateText: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: 12,
  },
});

export default PredictiveAnalytics;
