/**
 * <PERSON>rca<PERSON> hatırlatıcıları için servis fonksiyonları
 */
import { format, parseISO, addDays } from 'date-fns';
import * as reminderService from './reminderService';
import * as notificationDbService from './notificationDbService';
import * as exchangeRateService from './exchangeRateService';

/**
 * Harcama hatırlatıcısı ekler
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} expenseReminder - Harcama hatırlatıcı verileri
 * @param {string} expenseReminder.title - Başlık
 * @param {string} expenseReminder.description - Açıklama
 * @param {number} expenseReminder.amount - Miktar
 * @param {string} expenseReminder.currency - Para birimi
 * @param {string} expenseReminder.expense_type - Harcama türü (bill, subscription, regular)
 * @param {string} expenseReminder.due_date - Son ödeme tarihi
 * @param {number} expenseReminder.category_id - Kategori ID
 * @param {string} expenseReminder.repeat_type - Tekrarlama tipi (once, daily, weekly, monthly, yearly)
 * @param {number} expenseReminder.repeat_interval - Tekrarlama aralığı
 * @param {number} expenseReminder.remind_days_before - Kaç gün önce hatırlatılacak
 * @param {Array<number>} tagIds - Etiket ID'leri
 * @returns {Promise<number>} Eklenen hatırlatıcı ID'si
 */
export const addExpenseReminder = async (db, expenseReminder, tagIds = []) => {
  try {
    // Hatırlatma tarihini hesapla (son ödeme tarihinden X gün önce)
    const dueDate = typeof expenseReminder.due_date === 'string'
      ? parseISO(expenseReminder.due_date)
      : expenseReminder.due_date;

    const remindDaysBefore = expenseReminder.remind_days_before || 3;
    const reminderDate = addDays(dueDate, -remindDaysBefore);

    // Hatırlatıcı başlığını oluştur
    let title = expenseReminder.title;
    if (!title) {
      switch (expenseReminder.expense_type) {
        case 'bill':
          title = 'Fatura Ödemesi';
          break;
        case 'subscription':
          title = 'Abonelik Ödemesi';
          break;
        case 'regular':
          title = 'Düzenli Ödeme';
          break;
        default:
          title = 'Harcama Hatırlatıcısı';
      }
    }

    // Hatırlatıcı mesajını oluştur
    let message = expenseReminder.description;
    if (!message) {
      const formattedAmount = new Intl.NumberFormat('tr-TR', {
        style: 'currency',
        currency: expenseReminder.currency || 'TRY'
      }).format(expenseReminder.amount);

      message = `${formattedAmount} tutarındaki ödemeniz ${format(dueDate, 'dd.MM.yyyy')} tarihinde yapılmalıdır.`;
    }

    // Hatırlatıcı verilerini hazırla
    const reminderData = {
      title,
      message,
      date: reminderDate,
      time: '09:00', // Varsayılan saat
      priority: expenseReminder.priority || 'normal',
      category_id: expenseReminder.category_id,
      repeat_type: expenseReminder.repeat_type || 'once',
      repeat_interval: expenseReminder.repeat_interval || 1,
      data: {
        isExpenseReminder: true,
        expenseType: expenseReminder.expense_type,
        amount: expenseReminder.amount,
        currency: expenseReminder.currency || 'TRY',
        dueDate: dueDate.toISOString(),
        remindDaysBefore,
        originalDueDate: dueDate.toISOString()
      }
    };

    // Hatırlatıcıyı ekle
    return await reminderService.addReminder(db, reminderData, tagIds);
  } catch (error) {
    console.error('Harcama hatırlatıcısı ekleme hatası:', error);
    throw error;
  }
};

/**
 * Harcama hatırlatıcısını günceller
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} reminderId - Hatırlatıcı ID
 * @param {Object} expenseReminder - Harcama hatırlatıcı verileri
 * @param {Array<number>} tagIds - Etiket ID'leri
 * @returns {Promise<boolean>} Başarılı ise true
 */
export const updateExpenseReminder = async (db, reminderId, expenseReminder, tagIds = null) => {
  try {
    // Mevcut hatırlatıcıyı getir
    const existingReminder = await reminderService.getReminderById(db, reminderId);
    if (!existingReminder) {
      throw new Error('Hatırlatıcı bulunamadı');
    }

    // Mevcut verileri parse et
    const existingData = existingReminder.data ?
      (typeof existingReminder.data === 'string' ? JSON.parse(existingReminder.data) : existingReminder.data) :
      {};

    // Hatırlatma tarihini hesapla (son ödeme tarihinden X gün önce)
    const dueDate = typeof expenseReminder.due_date === 'string'
      ? parseISO(expenseReminder.due_date)
      : expenseReminder.due_date;

    const remindDaysBefore = expenseReminder.remind_days_before || existingData.remindDaysBefore || 3;
    const reminderDate = addDays(dueDate, -remindDaysBefore);

    // Hatırlatıcı başlığını oluştur
    let title = expenseReminder.title || existingReminder.title;

    // Hatırlatıcı mesajını oluştur
    let message = expenseReminder.description || existingReminder.message;
    if (expenseReminder.amount && expenseReminder.amount !== existingData.amount) {
      const formattedAmount = new Intl.NumberFormat('tr-TR', {
        style: 'currency',
        currency: expenseReminder.currency || existingData.currency || 'TRY'
      }).format(expenseReminder.amount);

      message = `${formattedAmount} tutarındaki ödemeniz ${format(dueDate, 'dd.MM.yyyy')} tarihinde yapılmalıdır.`;
    }

    // Hatırlatıcı verilerini hazırla
    const reminderData = {
      title,
      message,
      date: reminderDate,
      time: existingReminder.time || '09:00',
      priority: expenseReminder.priority || existingReminder.priority,
      category_id: expenseReminder.category_id || existingReminder.category_id,
      repeat_type: expenseReminder.repeat_type || existingReminder.repeat_type,
      repeat_interval: expenseReminder.repeat_interval || existingReminder.repeat_interval,
      data: {
        ...existingData,
        isExpenseReminder: true,
        expenseType: expenseReminder.expense_type || existingData.expenseType,
        amount: expenseReminder.amount || existingData.amount,
        currency: expenseReminder.currency || existingData.currency || 'TRY',
        dueDate: dueDate.toISOString(),
        remindDaysBefore,
        originalDueDate: dueDate.toISOString()
      }
    };

    // Hatırlatıcıyı güncelle
    return await reminderService.updateReminder(db, reminderId, reminderData, tagIds);
  } catch (error) {
    console.error('Harcama hatırlatıcısı güncelleme hatası:', error);
    throw error;
  }
};

/**
 * Harcama hatırlatıcılarını getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} options - Sorgu seçenekleri
 * @returns {Promise<Array>} Harcama hatırlatıcıları listesi
 */
export const getExpenseReminders = async (db, options = {}) => {
  try {
    console.log('getExpenseReminders çağrıldı');

    // Önce tüm hatırlatıcıları kontrol et
    const allNotifications = await db.getAllAsync(`
      SELECT id, title, message, data FROM notifications
      WHERE related_type = 'user_reminder'
      LIMIT 20
    `);

    console.log(`Toplam ${allNotifications.length} hatırlatıcı bulundu`);

    // Her bir hatırlatıcının data alanını kontrol et
    const expenseReminders = [];

    for (const reminder of allNotifications) {
      try {
        let data = null;

        if (reminder.data) {
          if (typeof reminder.data === 'string') {
            data = JSON.parse(reminder.data);
          } else {
            data = reminder.data;
          }
        }

        // Harcama hatırlatıcısı mı kontrol et
        if (data && data.isExpenseReminder === true) {
          console.log(`Harcama hatırlatıcısı bulundu: ID=${reminder.id}, Başlık=${reminder.title}`);

          // Tam hatırlatıcı bilgilerini getir
          const fullReminder = await getReminderById(db, reminder.id);
          if (fullReminder) {
            expenseReminders.push(fullReminder);
          }
        }
      } catch (parseError) {
        console.error(`ID=${reminder.id} için JSON parse hatası:`, parseError);
      }
    }

    console.log(`${expenseReminders.length} harcama hatırlatıcısı işlendi`);
    return expenseReminders;
  } catch (error) {
    console.error('Harcama hatırlatıcıları getirme hatası:', error);
    return []; // Hata durumunda boş dizi döndür
  }
};

/**
 * Belirli bir harcama hatırlatıcısını getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} reminderId - Hatırlatıcı ID
 * @returns {Promise<Object>} Harcama hatırlatıcısı
 */
export const getExpenseReminderById = async (db, reminderId) => {
  try {
    // Doğrudan veritabanından harcama hatırlatıcısını getir
    const query = `
      SELECT * FROM notifications
      WHERE id = ?
      AND related_type = 'user_reminder'
    `;

    const reminder = await db.getFirstAsync(query, [reminderId]);
    if (!reminder) return null;

    // Tarih ve saat bilgilerini ayır
    const scheduledDate = parseISO(reminder.scheduled_at);
    const date = format(scheduledDate, 'yyyy-MM-dd');
    const time = format(scheduledDate, 'HH:mm');

    // Tekrarlama bilgilerini parse et
    let repeatDays = null;
    let repeatMonths = null;
    let data = {};

    try {
      if (reminder.repeat_days) repeatDays = JSON.parse(reminder.repeat_days);
      if (reminder.repeat_months) repeatMonths = JSON.parse(reminder.repeat_months);
      if (reminder.data) data = JSON.parse(reminder.data);
    } catch (parseError) {
      console.error('JSON parse hatası:', parseError);
      return null;
    }

    // Harcama hatırlatıcısı değilse null döndür
    if (!data.isExpenseReminder) return null;

    return {
      ...reminder,
      date,
      time,
      repeat_days: repeatDays,
      repeat_months: repeatMonths,
      data,
      expense_type: data.expenseType,
      amount: data.amount,
      currency: data.currency,
      due_date: data.dueDate,
      remind_days_before: data.remindDaysBefore
    };
  } catch (error) {
    console.error('Harcama hatırlatıcısı getirme hatası:', error);
    return null; // Hata durumunda null döndür
  }
};

/**
 * Yaklaşan harcama hatırlatıcılarını getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} days - Kaç gün içindeki hatırlatıcılar
 * @param {number} limit - Limit
 * @returns {Promise<Array>} Yaklaşan harcama hatırlatıcıları listesi
 */
export const getUpcomingExpenseReminders = async (db, days = 7, limit = 10) => {
  try {
    const now = new Date();
    const endDate = addDays(now, days);

    // Doğrudan veritabanından yaklaşan harcama hatırlatıcılarını getir
    const query = `
      SELECT * FROM notifications
      WHERE related_type = 'user_reminder'
      AND status = 'pending'
      AND scheduled_at BETWEEN ? AND ?
      AND is_enabled = 1
      AND data LIKE '%isExpenseReminder":true%'
      ORDER BY scheduled_at ASC
      LIMIT ?
    `;

    const reminders = await db.getAllAsync(query, [now.toISOString(), endDate.toISOString(), limit]);

    // Sonuçları işle
    return reminders.map(reminder => {
      // Tarih ve saat bilgilerini ayır
      const scheduledDate = parseISO(reminder.scheduled_at);
      const date = format(scheduledDate, 'yyyy-MM-dd');
      const time = format(scheduledDate, 'HH:mm');

      // Tekrarlama bilgilerini parse et
      let repeatDays = null;
      let repeatMonths = null;
      let data = {};

      try {
        if (reminder.repeat_days) repeatDays = JSON.parse(reminder.repeat_days);
        if (reminder.repeat_months) repeatMonths = JSON.parse(reminder.repeat_months);
        if (reminder.data) data = JSON.parse(reminder.data);
      } catch (parseError) {
        console.error('JSON parse hatası:', parseError);
      }

      return {
        ...reminder,
        date,
        time,
        repeat_days: repeatDays,
        repeat_months: repeatMonths,
        data
      };
    });
  } catch (error) {
    console.error('Yaklaşan harcama hatırlatıcıları getirme hatası:', error);
    return []; // Hata durumunda boş dizi döndür
  }
};

/**
 * Harcama türlerini getirir
 *
 * @returns {Array} Harcama türleri
 */
export const getExpenseTypes = () => {
  return [
    { id: 'bill', name: 'Fatura', icon: 'receipt', color: '#e74c3c' },
    { id: 'subscription', name: 'Abonelik', icon: 'subscriptions', color: '#3498db' },
    { id: 'regular', name: 'Düzenli Ödeme', icon: 'repeat', color: '#2ecc71' },
    { id: 'other', name: 'Diğer', icon: 'attach-money', color: '#f39c12' }
  ];
};

/**
 * Hatırlatma gün seçeneklerini getirir
 *
 * @returns {Array} Hatırlatma gün seçenekleri
 */
export const getRemindDaysOptions = () => {
  return [
    { value: 0, label: 'Aynı gün' },
    { value: 1, label: '1 gün önce' },
    { value: 2, label: '2 gün önce' },
    { value: 3, label: '3 gün önce' },
    { value: 5, label: '5 gün önce' },
    { value: 7, label: '1 hafta önce' },
    { value: 14, label: '2 hafta önce' },
    { value: 30, label: '1 ay önce' }
  ];
};
