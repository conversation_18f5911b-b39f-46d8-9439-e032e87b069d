import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Share
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Colors } from '../constants/colors';
import * as overtimeService from '../services/overtimeService';

/**
 * Mesai Detay Ekranı
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @param {Object} props.route - Route nesnesi
 * @returns {JSX.Element} Mesai Detay Ekranı
 */
export default function OvertimeDetailScreen({ navigation, route }) {
  const db = useSQLiteContext();
  const { overtimeId } = route.params;
  
  // Durum değişkenleri
  const [loading, setLoading] = useState(true);
  const [overtime, setOvertime] = useState(null);
  
  // Verileri yükle
  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      
      // Mesai detaylarını getir
      const overtimeDetails = await overtimeService.getOvertimeDetails(db, overtimeId);
      setOvertime(overtimeDetails);
      
      setLoading(false);
    } catch (error) {
      console.error('Mesai detayları yükleme hatası:', error);
      Alert.alert('Hata', 'Mesai detayları yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  }, [overtimeId, db]);
  
  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [loadData])
  );
  
  // Mesai düzenle
  const editOvertime = () => {
    navigation.navigate('OvertimeForm', { overtimeId });
  };
  
  // Mesai sil
  const deleteOvertime = () => {
    Alert.alert(
      'Mesai Kaydını Sil',
      'Bu mesai kaydını silmek istediğinize emin misiniz? Bu işlem geri alınamaz.',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await overtimeService.deleteOvertime(db, overtimeId);
              Alert.alert('Başarılı', 'Mesai kaydı başarıyla silindi.');
              navigation.goBack();
            } catch (error) {
              console.error('Mesai silme hatası:', error);
              Alert.alert('Hata', 'Mesai silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };
  
  // Mesai paylaş
  const shareOvertime = async () => {
    if (!overtime) return;
    
    try {
      // Mesai özeti oluştur
      let message = `Mesai: ${overtime.title}\n`;
      message += `Tarih: ${format(new Date(overtime.date), 'dd MMMM yyyy', { locale: tr })}\n`;
      message += `Saat: ${overtime.start_time} - ${overtime.end_time}\n`;
      message += `Süre: ${overtimeService.formatDuration(overtime.duration)}\n`;
      message += `Kazanç: ${new Intl.NumberFormat('tr-TR', {
        style: 'currency',
        currency: overtime.currency
      }).format(overtime.duration * overtime.hourly_rate)}\n`;
      
      if (overtime.category_name) {
        message += `Kategori: ${overtime.category_name}\n`;
      }
      
      if (overtime.description) {
        message += `Açıklama: ${overtime.description}\n`;
      }
      
      if (overtime.notes) {
        message += `\nNotlar: ${overtime.notes}\n`;
      }
      
      // Paylaşım dialogunu aç
      await Share.share({
        message,
        title: `${overtime.title} Mesai Detayları`
      });
    } catch (error) {
      console.error('Mesai paylaşım hatası:', error);
      Alert.alert('Hata', 'Mesai paylaşılırken bir hata oluştu.');
    }
  };
  
  // Ödeme işle
  const processPayment = async () => {
    if (!overtime || overtime.is_paid) return;
    
    try {
      await overtimeService.processOvertimePayment(db, overtimeId);
      Alert.alert('Başarılı', 'Mesai ödemesi başarıyla işlendi.');
      loadData();
    } catch (error) {
      console.error('Ödeme işleme hatası:', error);
      Alert.alert('Hata', 'Ödeme işlenirken bir hata oluştu.');
    }
  };
  
  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={styles.loadingText}>Mesai detayları yükleniyor...</Text>
      </View>
    );
  }
  
  if (!overtime) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <MaterialIcons name="error-outline" size={48} color={Colors.DANGER} />
        <Text style={styles.errorText}>Mesai kaydı bulunamadı.</Text>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>Geri Dön</Text>
        </TouchableOpacity>
      </View>
    );
  }
  
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{overtime.title}</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerAction}
            onPress={shareOvertime}
          >
            <MaterialIcons name="share" size={24} color="#fff" />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerAction}
            onPress={editOvertime}
          >
            <MaterialIcons name="edit" size={24} color="#fff" />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerAction}
            onPress={deleteOvertime}
          >
            <MaterialIcons name="delete" size={24} color="#fff" />
          </TouchableOpacity>
        </View>
      </View>
      
      <ScrollView style={styles.content}>
        <View style={[styles.overtimeCard, { borderLeftColor: overtime.color || Colors.PRIMARY }]}>
          <View style={styles.overtimeHeader}>
            <View style={styles.overtimeDate}>
              <MaterialIcons name="event" size={20} color={Colors.GRAY_600} />
              <Text style={styles.overtimeDateText}>
                {format(new Date(overtime.date), 'dd MMMM yyyy, EEEE', { locale: tr })}
              </Text>
            </View>
            
            {overtime.is_paid ? (
              <View style={styles.paidBadge}>
                <MaterialIcons name="check-circle" size={16} color={Colors.SUCCESS} />
                <Text style={styles.paidBadgeText}>Ödendi</Text>
              </View>
            ) : (
              <View style={styles.unpaidBadge}>
                <MaterialIcons name="schedule" size={16} color={Colors.WARNING} />
                <Text style={styles.unpaidBadgeText}>Ödenmedi</Text>
              </View>
            )}
          </View>
          
          {overtime.description && (
            <Text style={styles.overtimeDescription}>{overtime.description}</Text>
          )}
          
          <View style={styles.overtimeDetails}>
            <View style={styles.detailItem}>
              <MaterialIcons name="access-time" size={20} color={Colors.GRAY_600} />
              <Text style={styles.detailText}>
                {overtime.start_time} - {overtime.end_time}
              </Text>
            </View>
            
            <View style={styles.detailItem}>
              <MaterialIcons name="timer" size={20} color={Colors.GRAY_600} />
              <Text style={styles.detailText}>
                {overtimeService.formatDuration(overtime.duration)}
              </Text>
            </View>
            
            {overtime.category_name && (
              <View style={styles.detailItem}>
                <MaterialIcons 
                  name={overtime.category_icon || "category"} 
                  size={20} 
                  color={overtime.category_color || Colors.GRAY_600} 
                />
                <Text style={styles.detailText}>{overtime.category_name}</Text>
              </View>
            )}
            
            {overtime.is_paid && overtime.payment_date && (
              <View style={styles.detailItem}>
                <MaterialIcons name="payment" size={20} color={Colors.GRAY_600} />
                <Text style={styles.detailText}>
                  Ödeme Tarihi: {format(new Date(overtime.payment_date), 'dd MMMM yyyy', { locale: tr })}
                </Text>
              </View>
            )}
          </View>
          
          <View style={styles.earningsCard}>
            <View style={styles.earningsHeader}>
              <Text style={styles.earningsTitle}>Kazanç Detayları</Text>
            </View>
            
            <View style={styles.earningsDetails}>
              <View style={styles.earningsItem}>
                <Text style={styles.earningsLabel}>Saatlik Ücret:</Text>
                <Text style={styles.earningsValue}>
                  {new Intl.NumberFormat('tr-TR', {
                    style: 'currency',
                    currency: overtime.currency
                  }).format(overtime.hourly_rate)}
                </Text>
              </View>
              
              <View style={styles.earningsItem}>
                <Text style={styles.earningsLabel}>Çalışma Süresi:</Text>
                <Text style={styles.earningsValue}>
                  {overtimeService.formatDuration(overtime.duration)}
                </Text>
              </View>
              
              <View style={styles.earningsTotal}>
                <Text style={styles.earningsTotalLabel}>Toplam Kazanç:</Text>
                <Text style={styles.earningsTotalValue}>
                  {new Intl.NumberFormat('tr-TR', {
                    style: 'currency',
                    currency: overtime.currency
                  }).format(overtime.duration * overtime.hourly_rate)}
                </Text>
              </View>
            </View>
          </View>
          
          {overtime.notes && (
            <View style={styles.notesSection}>
              <Text style={styles.notesTitle}>Notlar</Text>
              <Text style={styles.notesText}>{overtime.notes}</Text>
            </View>
          )}
          
          {!overtime.is_paid && (
            <TouchableOpacity
              style={styles.processPaymentButton}
              onPress={processPayment}
            >
              <MaterialIcons name="payment" size={20} color="#fff" />
              <Text style={styles.processPaymentButtonText}>Ödemeyi İşle</Text>
            </TouchableOpacity>
          )}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.GRAY_600,
  },
  errorText: {
    marginTop: 16,
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.GRAY_800,
  },
  backButtonText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.PRIMARY,
    fontWeight: '500',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 16,
    paddingHorizontal: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginLeft: 8,
  },
  headerActions: {
    flexDirection: 'row',
  },
  headerAction: {
    padding: 8,
    marginLeft: 4,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  overtimeCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    borderLeftWidth: 4,
  },
  overtimeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  overtimeDate: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  overtimeDateText: {
    fontSize: 16,
    color: Colors.GRAY_800,
    marginLeft: 8,
  },
  paidBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.SUCCESS_LIGHT,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 16,
  },
  paidBadgeText: {
    fontSize: 12,
    color: Colors.SUCCESS,
    fontWeight: '500',
    marginLeft: 4,
  },
  unpaidBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.WARNING_LIGHT,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 16,
  },
  unpaidBadgeText: {
    fontSize: 12,
    color: Colors.WARNING,
    fontWeight: '500',
    marginLeft: 4,
  },
  overtimeDescription: {
    fontSize: 16,
    color: Colors.GRAY_700,
    marginBottom: 16,
    lineHeight: 22,
  },
  overtimeDetails: {
    marginBottom: 16,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailText: {
    fontSize: 16,
    color: Colors.GRAY_800,
    marginLeft: 8,
  },
  earningsCard: {
    backgroundColor: Colors.GRAY_50,
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  earningsHeader: {
    marginBottom: 12,
  },
  earningsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.GRAY_800,
  },
  earningsDetails: {
    marginBottom: 8,
  },
  earningsItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  earningsLabel: {
    fontSize: 14,
    color: Colors.GRAY_600,
  },
  earningsValue: {
    fontSize: 14,
    color: Colors.GRAY_800,
    fontWeight: '500',
  },
  earningsTotal: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: Colors.GRAY_300,
  },
  earningsTotalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.GRAY_800,
  },
  earningsTotalValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.SUCCESS,
  },
  notesSection: {
    marginBottom: 16,
  },
  notesTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.GRAY_800,
    marginBottom: 8,
  },
  notesText: {
    fontSize: 14,
    color: Colors.GRAY_700,
    lineHeight: 20,
  },
  processPaymentButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.SUCCESS,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  processPaymentButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
    marginLeft: 8,
  },
});
