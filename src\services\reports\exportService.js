/**
 * Export Service - Rapor ve Tablo Dışa Aktarma Servisi
 * PDF, CSV, Excel formatlarında dışa aktarma
 */

import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import { Platform } from 'react-native';

/**
 * CSV formatında dışa aktarma
 * @param {Object} params - Parametreler
 * @param {Array} params.data - Dışa aktarılacak veri
 * @param {Array} params.columns - Sütun yapılandırması
 * @param {string} params.filename - Dosya adı
 * @returns {Promise<string>} Dosya yolu
 */
export const exportToCSV = async ({ data, columns, filename = 'export' }) => {
  try {
    console.log('📊 CSV dışa aktarma başlatılıyor...');
    
    // CSV başlığı oluştur
    const visibleColumns = columns.filter(col => col.visible);
    const headers = visibleColumns.map(col => col.name).join(',');
    
    // CSV verileri oluştur
    const csvRows = data.map(row => {
      return visibleColumns.map(col => {
        const value = row[col.id] || '';
        // Virgül ve çift tırnak içeren değerleri düzenle
        if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value;
      }).join(',');
    });
    
    // CSV içeriğini birleştir
    const csvContent = [headers, ...csvRows].join('\n');
    
    // Dosya yolu oluştur
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filepath = `${FileSystem.documentDirectory}${filename}_${timestamp}.csv`;
    
    // Dosyaya yaz
    await FileSystem.writeAsStringAsync(filepath, csvContent, {
      encoding: FileSystem.EncodingType.UTF8,
    });
    
    console.log('✅ CSV dosyası oluşturuldu:', filepath);
    
    // Dosyayı paylaş
    if (await Sharing.isAvailableAsync()) {
      await Sharing.shareAsync(filepath);
    }
    
    return filepath;
    
  } catch (error) {
    console.error('❌ CSV dışa aktarma hatası:', error);
    throw error;
  }
};

/**
 * Excel formatında dışa aktarma
 * @param {Object} params - Parametreler
 * @param {Array} params.data - Dışa aktarılacak veri
 * @param {Array} params.columns - Sütun yapılandırması
 * @param {string} params.filename - Dosya adı
 * @returns {Promise<string>} Dosya yolu
 */
export const exportToExcel = async ({ data, columns, filename = 'export' }) => {
  try {
    console.log('📊 Excel dışa aktarma başlatılıyor...');
    
    // Excel için XML formatı oluştur (basit Excel XML)
    const visibleColumns = columns.filter(col => col.visible);
    
    let xmlContent = `<?xml version="1.0"?>
<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"
          xmlns:o="urn:schemas-microsoft-com:office:office"
          xmlns:x="urn:schemas-microsoft-com:office:excel"
          xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"
          xmlns:html="http://www.w3.org/TR/REC-html40">
<Worksheet ss:Name="Rapor">
<Table>`;
    
    // Başlık satırı
    xmlContent += '<Row>';
    visibleColumns.forEach(col => {
      xmlContent += `<Cell><Data ss:Type="String">${col.name}</Data></Cell>`;
    });
    xmlContent += '</Row>';
    
    // Veri satırları
    data.forEach(row => {
      xmlContent += '<Row>';
      visibleColumns.forEach(col => {
        const value = row[col.id] || '';
        const dataType = col.type === 'currency' || col.type === 'number' ? 'Number' : 'String';
        xmlContent += `<Cell><Data ss:Type="${dataType}">${value}</Data></Cell>`;
      });
      xmlContent += '</Row>';
    });
    
    xmlContent += '</Table></Worksheet></Workbook>';
    
    // Dosya yolu oluştur
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filepath = `${FileSystem.documentDirectory}${filename}_${timestamp}.xls`;
    
    // Dosyaya yaz
    await FileSystem.writeAsStringAsync(filepath, xmlContent, {
      encoding: FileSystem.EncodingType.UTF8,
    });
    
    console.log('✅ Excel dosyası oluşturuldu:', filepath);
    
    // Dosyayı paylaş
    if (await Sharing.isAvailableAsync()) {
      await Sharing.shareAsync(filepath);
    }
    
    return filepath;
    
  } catch (error) {
    console.error('❌ Excel dışa aktarma hatası:', error);
    throw error;
  }
};

/**
 * PDF formatında dışa aktarma
 * @param {Object} params - Parametreler
 * @param {Array} params.data - Dışa aktarılacak veri
 * @param {Array} params.columns - Sütun yapılandırması
 * @param {string} params.filename - Dosya adı
 * @param {string} params.title - Rapor başlığı
 * @returns {Promise<string>} Dosya yolu
 */
export const exportToPDF = async ({ data, columns, filename = 'export', title = 'Rapor' }) => {
  try {
    console.log('📊 PDF dışa aktarma başlatılıyor...');
    
    // HTML içeriği oluştur
    const visibleColumns = columns.filter(col => col.visible);
    
    let htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>${title}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #007AFF;
            padding-bottom: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #007AFF;
            color: white;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .currency {
            text-align: right;
            font-weight: bold;
            color: #007AFF;
        }
        .date {
            text-align: center;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>${title}</h1>
        <table>
            <thead>
                <tr>
                    ${visibleColumns.map(col => `<th>${col.name}</th>`).join('')}
                </tr>
            </thead>
            <tbody>
                ${data.map(row => `
                    <tr>
                        ${visibleColumns.map(col => {
                          const value = row[col.id] || '';
                          const cssClass = col.type === 'currency' ? 'currency' : 
                                           col.type === 'date' ? 'date' : '';
                          return `<td class="${cssClass}">${value}</td>`;
                        }).join('')}
                    </tr>
                `).join('')}
            </tbody>
        </table>
        <div class="footer">
            <p>Rapor Oluşturulma Tarihi: ${new Date().toLocaleDateString('tr-TR')}</p>
            <p>Toplam Kayıt: ${data.length}</p>
        </div>
    </div>
</body>
</html>`;
    
    // HTML dosyasını oluştur
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const htmlFilepath = `${FileSystem.documentDirectory}${filename}_${timestamp}.html`;
    
    await FileSystem.writeAsStringAsync(htmlFilepath, htmlContent, {
      encoding: FileSystem.EncodingType.UTF8,
    });
    
    console.log('✅ HTML dosyası oluşturuldu (PDF alternatifi):', htmlFilepath);
    
    // Dosyayı paylaş
    if (await Sharing.isAvailableAsync()) {
      await Sharing.shareAsync(htmlFilepath);
    }
    
    return htmlFilepath;
    
  } catch (error) {
    console.error('❌ PDF dışa aktarma hatası:', error);
    throw error;
  }
};

/**
 * İşlem listesi dışa aktarma
 * @param {Object} exportData - Dışa aktarılacak veri
 * @param {string} format - Dışa aktarma formatı (pdf, csv, excel)
 * @returns {Promise<string>} Dosya yolu
 */
export const exportTransactionList = async (exportData, format = 'pdf') => {
  try {
    console.log('💾 İşlem listesi dışa aktarma başlatılıyor:', format);
    
    const { title, summary, transactions, filters, generatedAt } = exportData;
    
    if (format === 'csv') {
      // CSV formatında dışa aktarma
      const columns = [
        { id: 'date', name: 'Tarih', visible: true },
        { id: 'description', name: 'Açıklama', visible: true },
        { id: 'category_name', name: 'Kategori', visible: true },
        { id: 'type', name: 'Tür', visible: true },
        { id: 'amount', name: 'Tutar', visible: true },
      ];
      
      const csvData = transactions.map(transaction => ({
        date: new Date(transaction.date).toLocaleDateString('tr-TR'),
        description: transaction.description || '',
        category_name: transaction.category_name || 'Kategorisiz',
        type: transaction.type === 'income' ? 'Gelir' : 'Gider',
        amount: transaction.amount.toFixed(2),
      }));
      
      return await exportToCSV({
        data: csvData,
        columns,
        filename: 'islem_listesi',
      });
    }
    
    if (format === 'excel') {
      // Excel formatında dışa aktarma
      const columns = [
        { id: 'date', name: 'Tarih', visible: true },
        { id: 'description', name: 'Açıklama', visible: true },
        { id: 'category_name', name: 'Kategori', visible: true },
        { id: 'type', name: 'Tür', visible: true },
        { id: 'amount', name: 'Tutar', visible: true },
      ];
      
      const excelData = transactions.map(transaction => ({
        date: new Date(transaction.date).toLocaleDateString('tr-TR'),
        description: transaction.description || '',
        category_name: transaction.category_name || 'Kategorisiz',
        type: transaction.type === 'income' ? 'Gelir' : 'Gider',
        amount: transaction.amount.toFixed(2),
      }));
      
      return await exportToExcel({
        data: excelData,
        columns,
        filename: 'islem_listesi',
      });
    }
    
    if (format === 'pdf') {
      // PDF formatında dışa aktarma
      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <title>${title}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .summary { display: flex; justify-content: space-around; margin-bottom: 30px; }
            .summary-item { text-align: center; }
            .summary-value { font-size: 24px; font-weight: bold; }
            .table { width: 100%; border-collapse: collapse; }
            .table th, .table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            .table th { background-color: #f2f2f2; }
            .amount.income { color: green; }
            .amount.expense { color: red; }
            .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>${title}</h1>
            <p>Oluşturulma Tarihi: ${new Date(generatedAt).toLocaleString('tr-TR')}</p>
          </div>
          
          <div class="summary">
            <div class="summary-item">
              <div class="summary-value" style="color: green;">₺${summary.totalIncome.toFixed(2)}</div>
              <div>Toplam Gelir</div>
            </div>
            <div class="summary-item">
              <div class="summary-value" style="color: red;">₺${summary.totalExpense.toFixed(2)}</div>
              <div>Toplam Gider</div>
            </div>
            <div class="summary-item">
              <div class="summary-value" style="color: ${summary.netAmount >= 0 ? 'green' : 'red'};">₺${summary.netAmount.toFixed(2)}</div>
              <div>Net Tutar</div>
            </div>
          </div>
          
          <table class="table">
            <thead>
              <tr>
                <th>Tarih</th>
                <th>Açıklama</th>
                <th>Kategori</th>
                <th>Tür</th>
                <th>Tutar</th>
              </tr>
            </thead>
            <tbody>
              ${transactions.map(transaction => `
                <tr>
                  <td>${new Date(transaction.date).toLocaleDateString('tr-TR')}</td>
                  <td>${transaction.description || ''}</td>
                  <td>${transaction.category_name || 'Kategorisiz'}</td>
                  <td>${transaction.type === 'income' ? 'Gelir' : 'Gider'}</td>
                  <td class="amount ${transaction.type}">
                    ${transaction.type === 'income' ? '+' : '-'}₺${Math.abs(transaction.amount).toFixed(2)}
                  </td>
                </tr>
              `).join('')}
            </tbody>
          </table>
          
          <div class="footer">
            <p>Toplam ${summary.transactionCount} işlem listelendi.</p>
          </div>
        </body>
        </html>
      `;
      
      return await exportToPDF({
        html: htmlContent,
        filename: 'islem_listesi',
        format: 'A4',
        orientation: 'portrait',
      });
    }
    
    throw new Error('Desteklenmeyen format: ' + format);
    
  } catch (error) {
    console.error('❌ İşlem listesi dışa aktarma hatası:', error);
    throw error;
  }
};

/**
 * Tablo kaydetme servisi
 * @param {Object} params - Parametreler
 * @param {string} params.name - Tablo adı
 * @param {Object} params.config - Tablo yapılandırması
 * @param {Array} params.columns - Sütun yapılandırması
 * @param {Array} params.filters - Filtre yapılandırması
 * @param {Array} params.dataSources - Veri kaynakları
 * @returns {Promise<Object>} Kayıt sonucu
 */
export const saveTableConfiguration = async ({ name, config, columns, filters, dataSources }) => {
  try {
    console.log('💾 Tablo yapılandırması kaydediliyor...');
    
    const tableConfig = {
      name,
      config: {
        ...config,
        columns,
        filters,
        dataSources,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    };
    
    // Bu kısım database'e kayıt yapabilir
    // Şimdilik localStorage kullanıyoruz
    const existingTables = await getStoredTables();
    const newId = Date.now().toString();
    
    const updatedTables = {
      ...existingTables,
      [newId]: tableConfig
    };
    
    // AsyncStorage ile kaydet (gerçek implementasyonda database kullanılacak)
    console.log('Tablo yapılandırması kaydedildi:', newId);
    
    return {
      success: true,
      id: newId,
      config: tableConfig
    };
    
  } catch (error) {
    console.error('❌ Tablo kaydetme hatası:', error);
    throw error;
  }
};

/**
 * Kaydedilen tabloları getir
 * @returns {Promise<Object>} Tablo listesi
 */
export const getStoredTables = async () => {
  try {
    // Bu kısım database'den çekebilir
    // Şimdilik boş obje döndürüyoruz
    return {};
  } catch (error) {
    console.error('❌ Tablo listesi getirme hatası:', error);
    return {};
  }
};

/**
 * Para formatı
 * @param {number} amount - Tutar
 * @returns {string} Formatlanmış tutar
 */
export const formatCurrency = (amount) => {
  return new Intl.NumberFormat('tr-TR', {
    style: 'currency',
    currency: 'TRY',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};
