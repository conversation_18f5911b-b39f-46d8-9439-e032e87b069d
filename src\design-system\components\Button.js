import React from 'react';
import { TouchableOpacity, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import tokens from '../tokens';

/**
 * Modern Button Component
 * Design system'e uygun, tutarlı buton bileşeni
 */
const Button = ({
  children,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  icon = null,
  iconPosition = 'left',
  fullWidth = false,
  onPress,
  style,
  textStyle,
  ...props
}) => {
  // Variant stilleri
  const getVariantStyles = () => {
    switch (variant) {
      case 'primary':
        return {
          backgroundColor: disabled ? tokens.colors.gray[300] : tokens.colors.primary[500],
          borderColor: disabled ? tokens.colors.gray[300] : tokens.colors.primary[500],
          borderWidth: 1,
        };
      case 'secondary':
        return {
          backgroundColor: disabled ? tokens.colors.gray[100] : tokens.colors.gray[100],
          borderColor: disabled ? tokens.colors.gray[200] : tokens.colors.gray[300],
          borderWidth: 1,
        };
      case 'outline':
        return {
          backgroundColor: 'transparent',
          borderColor: disabled ? tokens.colors.gray[300] : tokens.colors.primary[500],
          borderWidth: 1,
        };
      case 'ghost':
        return {
          backgroundColor: 'transparent',
          borderColor: 'transparent',
          borderWidth: 0,
        };
      case 'danger':
        return {
          backgroundColor: disabled ? tokens.colors.gray[300] : tokens.colors.danger[500],
          borderColor: disabled ? tokens.colors.gray[300] : tokens.colors.danger[500],
          borderWidth: 1,
        };
      case 'success':
        return {
          backgroundColor: disabled ? tokens.colors.gray[300] : tokens.colors.success[500],
          borderColor: disabled ? tokens.colors.gray[300] : tokens.colors.success[500],
          borderWidth: 1,
        };
      default:
        return {
          backgroundColor: tokens.colors.primary[500],
          borderColor: tokens.colors.primary[500],
          borderWidth: 1,
        };
    }
  };

  // Size stilleri
  const getSizeStyles = () => {
    switch (size) {
      case 'xs':
        return {
          paddingHorizontal: tokens.spacing[2],
          paddingVertical: tokens.spacing[1],
          borderRadius: tokens.borderRadius.base,
        };
      case 'sm':
        return {
          paddingHorizontal: tokens.spacing[3],
          paddingVertical: tokens.spacing[2],
          borderRadius: tokens.borderRadius.md,
        };
      case 'md':
        return {
          paddingHorizontal: tokens.spacing[4],
          paddingVertical: tokens.spacing[3],
          borderRadius: tokens.borderRadius.lg,
        };
      case 'lg':
        return {
          paddingHorizontal: tokens.spacing[6],
          paddingVertical: tokens.spacing[4],
          borderRadius: tokens.borderRadius.xl,
        };
      case 'xl':
        return {
          paddingHorizontal: tokens.spacing[8],
          paddingVertical: tokens.spacing[5],
          borderRadius: tokens.borderRadius['2xl'],
        };
      default:
        return {
          paddingHorizontal: tokens.spacing[4],
          paddingVertical: tokens.spacing[3],
          borderRadius: tokens.borderRadius.lg,
        };
    }
  };

  // Text renkleri
  const getTextColor = () => {
    if (disabled) {
      return tokens.colors.gray[500];
    }

    switch (variant) {
      case 'primary':
      case 'danger':
      case 'success':
        return '#ffffff';
      case 'secondary':
        return tokens.colors.gray[700];
      case 'outline':
      case 'ghost':
        return tokens.colors.primary[500];
      default:
        return '#ffffff';
    }
  };

  // Text size
  const getTextSize = () => {
    switch (size) {
      case 'xs':
        return tokens.typography.fontSize.xs;
      case 'sm':
        return tokens.typography.fontSize.sm;
      case 'md':
        return tokens.typography.fontSize.base;
      case 'lg':
        return tokens.typography.fontSize.lg;
      case 'xl':
        return tokens.typography.fontSize.xl;
      default:
        return tokens.typography.fontSize.base;
    }
  };

  // Icon size
  const getIconSize = () => {
    switch (size) {
      case 'xs':
        return 14;
      case 'sm':
        return 16;
      case 'md':
        return 18;
      case 'lg':
        return 20;
      case 'xl':
        return 24;
      default:
        return 18;
    }
  };

  const variantStyles = getVariantStyles();
  const sizeStyles = getSizeStyles();
  const textColor = getTextColor();
  const textSize = getTextSize();
  const iconSize = getIconSize();

  const buttonStyles = [
    styles.button,
    variantStyles,
    sizeStyles,
    fullWidth && styles.fullWidth,
    disabled && styles.disabled,
    style,
  ];

  const textStyles = [
    styles.text,
    {
      color: textColor,
      fontSize: textSize,
      fontFamily: tokens.typography.fontFamily.medium,
      fontWeight: tokens.typography.fontWeight.medium,
    },
    textStyle,
  ];

  const renderContent = () => {
    if (loading) {
      return (
        <ActivityIndicator
          size="small"
          color={textColor}
          style={styles.loader}
        />
      );
    }

    const textElement = (
      <Text style={textStyles} numberOfLines={1}>
        {children}
      </Text>
    );

    if (!icon) {
      return textElement;
    }

    const iconElement = (
      <MaterialIcons
        name={icon}
        size={iconSize}
        color={textColor}
        style={iconPosition === 'left' ? styles.iconLeft : styles.iconRight}
      />
    );

    return (
      <>
        {iconPosition === 'left' && iconElement}
        {textElement}
        {iconPosition === 'right' && iconElement}
      </>
    );
  };

  return (
    <TouchableOpacity
      style={buttonStyles}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.7}
      {...props}
    >
      {renderContent()}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    ...tokens.shadows.sm,
  },
  fullWidth: {
    width: '100%',
  },
  disabled: {
    opacity: 0.6,
    ...tokens.shadows.xs,
  },
  text: {
    textAlign: 'center',
    lineHeight: tokens.typography.lineHeight.snug * tokens.typography.fontSize.base,
  },
  loader: {
    marginHorizontal: tokens.spacing[2],
  },
  iconLeft: {
    marginRight: tokens.spacing[2],
  },
  iconRight: {
    marginLeft: tokens.spacing[2],
  },
});

export default Button;
