import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Dimensions,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTheme } from '../context/ThemeContext';
import { useSQLiteContext } from 'expo-sqlite';
import SimpleCategoryChart from '../components/charts/SimpleCategoryChart';
import ChartTypeSelector from '../components/charts/ChartTypeSelector';
import { showExportOptions } from '../utils/exportHelpers';

const { width: screenWidth } = Dimensions.get('window');

/**
 * Modern Statistics Screen - Phase 3: Chart Integration
 * STATISTICS_SCREEN_REDESIGN_PLAN.md dosyasına uygun olarak tasarlanmıştır
 */
const ModernStatisticsScreen = ({ navigation }) => {
  const insets = useSafeAreaInsets();
  const { theme } = useTheme();
  const db = useSQLiteContext();
  
  const [refreshing, setRefreshing] = useState(false);
  const [selectedChartType, setSelectedChartType] = useState('donut');
  const [selectedPeriod, setSelectedPeriod] = useState('30days'); // 7days, 30days, 90days, year
  const [stats, setStats] = useState({
    totalIncome: 0,
    totalExpense: 0,
    balance: 0,
    transactionCount: 0,
  });
  const [categoryData, setCategoryData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [regularIncomeData, setRegularIncomeData] = useState({});
  const [overtimeData, setOvertimeData] = useState({});
  const [transactionData, setTransactionData] = useState({});
  const [showExportMenu, setShowExportMenu] = useState(false);

  /**
   * Period'a göre tarih aralığını belirle
   */
  const getPeriodDays = () => {
    switch (selectedPeriod) {
      case '7days': return 7;
      case '30days': return 30;
      case '90days': return 90;
      case 'year': return 365;
      default: return 30;
    }
  };

  /**
   * Kapsamlı istatistik verileri yükleme (düzenli gelir ve mesai dahil)
   */
  const loadBasicStats = async () => {
    try {
      setLoading(true);
      
      const days = getPeriodDays();
      
      // Seçilen dönem için kapsamlı istatistikler
      const transactionResult = await db.getAllAsync(`
        SELECT 
          SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END) as totalIncome,
          SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as totalExpense,
          COUNT(*) as transactionCount
        FROM transactions 
        WHERE date >= date('now', '-${days} days')
      `);

      // Düzenli gelirler (aktif olanları dönem boyunca tahmini gelir olarak hesapla)
      const regularIncomeResult = await db.getAllAsync(`
        SELECT 
          SUM(CASE WHEN status = 'active' THEN 
            CASE 
              WHEN recurrence_type = 'monthly' THEN amount * (${days} / 30.0)
              WHEN recurrence_type = 'weekly' THEN amount * (${days} / 7.0)
              WHEN recurrence_type = 'daily' THEN amount * ${days}
              ELSE amount
            END
          ELSE 0 END) as regularIncomeTotal,
          COUNT(CASE WHEN status = 'active' THEN 1 END) as regularIncomeCount
        FROM regular_incomes 
        WHERE status = 'active'
      `);

      // Mesai ücreti (seçilen dönem içinde ödenmiş olanlar)
      const overtimeResult = await db.getAllAsync(`
        SELECT 
          SUM(CASE WHEN is_paid = 1 THEN (duration * hourly_rate) ELSE 0 END) as overtimeTotal,
          COUNT(CASE WHEN is_paid = 1 THEN 1 END) as overtimeCount
        FROM overtime 
        WHERE date >= date('now', '-${days} days')
      `);

      const transactionResult_Data = transactionResult[0] || {};
      const regularIncomeResult_Data = regularIncomeResult[0] || {};
      const overtimeResult_Data = overtimeResult[0] || {};
      
      // State'e kaydet
      setTransactionData(transactionResult_Data);
      setRegularIncomeData(regularIncomeResult_Data);
      setOvertimeData(overtimeResult_Data);

      // Toplam geliri hesapla (transactions + düzenli gelir + mesai)
      const totalIncome = (transactionResult_Data.totalIncome || 0) + 
                         (regularIncomeResult_Data.regularIncomeTotal || 0) + 
                         (overtimeResult_Data.overtimeTotal || 0);
      
      const totalExpense = transactionResult_Data.totalExpense || 0;
      const totalTransactionCount = (transactionResult_Data.transactionCount || 0) + 
                                   (regularIncomeResult_Data.regularIncomeCount || 0) + 
                                   (overtimeResult_Data.overtimeCount || 0);

      setStats({
        totalIncome: totalIncome,
        totalExpense: totalExpense,
        balance: totalIncome - totalExpense,
        transactionCount: totalTransactionCount,
      });

      // Kategori verileri yükleme
      const categoryResult = await db.getAllAsync(`
        SELECT 
          c.name,
          c.color,
          SUM(t.amount) as total,
          COUNT(t.id) as count
        FROM transactions t
        JOIN categories c ON t.category_id = c.id
        WHERE t.date >= date('now', '-${days} days') AND t.type = 'expense'
        GROUP BY c.id, c.name, c.color
        ORDER BY total DESC
        LIMIT 6
      `);

      setCategoryData(categoryResult || []);
    } catch (error) {
      console.error('İstatistik yükleme hatası:', error);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Yenileme işlemi
   */
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadBasicStats();
    setRefreshing(false);
  };

  /**
   * Para formatı
   */
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  /**
   * Period Filter Component
   */
  const PeriodFilter = () => {
    const periods = [
      { id: '7days', label: 'Son 7 Gün', shortLabel: '7G' },
      { id: '30days', label: 'Son 30 Gün', shortLabel: '30G' },
      { id: '90days', label: 'Son 3 Ay', shortLabel: '3A' },
      { id: 'year', label: 'Bu Yıl', shortLabel: 'Yıl' },
    ];

    return (
      <View style={styles.periodFilter}>
        <Text style={[styles.periodFilterTitle, { color: theme.TEXT_PRIMARY }]}>
          📅 Dönem Seçimi
        </Text>
        <View style={styles.periodButtons}>
          {periods.map((period) => (
            <TouchableOpacity
              key={period.id}
              style={[
                styles.periodButton,
                {
                  backgroundColor: selectedPeriod === period.id ? theme.PRIMARY : theme.SURFACE,
                  borderColor: selectedPeriod === period.id ? theme.PRIMARY : theme.BORDER,
                },
              ]}            onPress={() => {
              setSelectedPeriod(period.id);
            }}
            >
              <Text
                style={[
                  styles.periodButtonText,
                  {
                    color: selectedPeriod === period.id ? '#fff' : theme.TEXT_PRIMARY,
                    fontWeight: selectedPeriod === period.id ? '600' : '500',
                  },
                ]}
              >
                {period.shortLabel}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
        <Text style={[styles.periodFilterSubtitle, { color: theme.TEXT_SECONDARY }]}>
          {periods.find(p => p.id === selectedPeriod)?.label || 'Son 30 Gün'}
        </Text>
      </View>
    );
  };



  /**
   * Simple Donut Chart Component
   */
  const SimpleDonutChart = () => {
    const total = categoryData.reduce((sum, item) => sum + item.total, 0);
    const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];

    if (categoryData.length === 0) {
      return (
        <View style={styles.emptyChart}>
          <Ionicons name="pie-chart-outline" size={48} color={theme.TEXT_SECONDARY} />
          <Text style={[styles.emptyChartText, { color: theme.TEXT_PRIMARY }]}>
            Henüz kategori verisi yok
          </Text>
        </View>
      );
    }

    return (
      <View style={styles.chartContainer}>
        {/* Basit donut chart center */}
        <View style={[styles.donutCenter, { borderColor: theme.PRIMARY }]}>
          <Text style={[styles.donutCenterAmount, { color: theme.TEXT_PRIMARY }]}>
            {formatCurrency(total)}
          </Text>
          <Text style={[styles.donutCenterLabel, { color: theme.TEXT_SECONDARY }]}>
            Toplam Gider
          </Text>
        </View>

        {/* Legend */}
        <View style={styles.legend}>
          {categoryData.map((item, index) => (
            <View key={index} style={styles.legendItem}>
              <View
                style={[
                  styles.legendColor,
                  { backgroundColor: colors[index % colors.length] },
                ]}
              />
              <Text
                style={[styles.legendText, { color: theme.TEXT_PRIMARY }]}
                numberOfLines={1}
              >
                {item.name}
              </Text>
              <Text style={[styles.legendAmount, { color: theme.TEXT_SECONDARY }]}>
                {formatCurrency(item.total)}
              </Text>
            </View>
          ))}
        </View>
      </View>
    );
  };

  /**
   * Simple Bar Chart Component
   */
  const SimpleBarChart = () => {
    if (categoryData.length === 0) {
      return (
        <View style={styles.emptyChart}>
          <Ionicons name="bar-chart-outline" size={48} color={theme.TEXT_SECONDARY} />
          <Text style={[styles.emptyChartText, { color: theme.TEXT_PRIMARY }]}>
            Henüz kategori verisi yok
          </Text>
        </View>
      );
    }

    const maxValue = Math.max(...categoryData.map(item => item.total));

    return (
      <View style={styles.chartContainer}>
        <View style={styles.barChartContainer}>
          {categoryData.map((item, index) => {
            const height = maxValue > 0 ? (item.total / maxValue) * 120 : 0;
            return (
              <View key={index} style={styles.barItem}>
                <View
                  style={[
                    styles.bar,
                    {
                      height: height || 10,
                      backgroundColor: theme.PRIMARY,
                    },
                  ]}
                />
                <Text
                  style={[styles.barLabel, { color: theme.TEXT_SECONDARY }]}
                  numberOfLines={1}
                >
                  {item.name}
                </Text>
                <Text style={[styles.barAmount, { color: theme.TEXT_PRIMARY }]}>
                  {formatCurrency(item.total)}
                </Text>
              </View>
            );
          })}
        </View>
      </View>
    );
  };

  /**
   * Chart Renderer
   */
  const renderChart = () => {
    switch (selectedChartType) {
      case 'donut':
        return <SimpleDonutChart />;
      case 'bar':
        return <SimpleBarChart />;
      default:
        return <SimpleDonutChart />;
    }
  };

  /**
   * Export istatistik verilerini - Gelişmiş versiyonu
   */
  const exportStatistics = async () => {
    try {
      const exportData = {
        period: selectedPeriod,
        date: new Date().toISOString().split('T')[0],
        stats: {
          totalIncome: stats.totalIncome,
          totalExpense: stats.totalExpense,
          balance: stats.balance,
          transactionCount: stats.transactionCount,
        },
        categoryBreakdown: categoryData.map(item => ({
          name: item.name,
          total: item.total,
          count: item.count,
          percentage: ((item.total / stats.totalExpense) * 100).toFixed(1)
        })),
        incomeBreakdown: {
          transactions: (stats.totalIncome - (regularIncomeData.regularIncomeTotal || 0) - (overtimeData.overtimeTotal || 0)),
          regularIncome: (regularIncomeData.regularIncomeTotal || 0),
          overtime: (overtimeData.overtimeTotal || 0),
        }
      };

      // Export seçeneklerini göster
      showExportOptions(exportData);
      
    } catch (error) {
      console.error('Export hatası:', error);
      Alert.alert('Hata', 'İstatistik verileri export edilirken bir hata oluştu.');
    }
  };

  /**
   * Export için veri hazırlama
   */
  const prepareExportData = () => {
    return {
      period: selectedPeriod,
      date: new Date().toISOString().split('T')[0],
      stats: {
        totalIncome: stats.totalIncome,
        totalExpense: stats.totalExpense,
        balance: stats.balance,
        transactionCount: stats.transactionCount,
      },
      categoryBreakdown: categoryData.map(item => ({
        name: item.name,
        total: item.total,
        count: item.count,
        percentage: stats.totalExpense > 0 ? ((item.total / stats.totalExpense) * 100).toFixed(1) : '0'
      })),
      incomeBreakdown: {
        transactions: (stats.totalIncome - (regularIncomeData.regularIncomeTotal || 0) - (overtimeData.overtimeTotal || 0)),
        regularIncome: (regularIncomeData.regularIncomeTotal || 0),
        overtime: (overtimeData.overtimeTotal || 0),
      }
    };
  };

  useEffect(() => {
    loadBasicStats();
  }, [selectedPeriod]); // Period değiştiğinde data'yı yeniden yükle

  return (
    <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      {/* Modern Header */}
      <View
        style={[
          styles.header,
          {
            paddingTop: insets.top + 10,
            backgroundColor: theme.PRIMARY,
          },
        ]}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity
            style={[styles.headerButton, { backgroundColor: 'rgba(255,255,255,0.2)' }]}
            onPress={() => {
              // Navigate önceki ekrana, eğer AllFeatures ise oraya, değilse Home'a
              if (navigation.canGoBack()) {
                navigation.goBack();
              } else {
                navigation.navigate('HomeStack', { screen: 'Home' });
              }
            }}
          >
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>

          <View style={styles.headerCenter}>
            <Text style={[styles.headerTitle, { color: '#fff' }]}>
              📊 İstatistikler
            </Text>
            <Text style={[styles.headerSubtitle, { color: 'rgba(255,255,255,0.8)' }]}>
              {selectedPeriod === '7days' ? 'Son 7 Gün' : 
               selectedPeriod === '30days' ? 'Son 30 Gün' : 
               selectedPeriod === '90days' ? 'Son 3 Ay' : 'Bu Yıl'} • Kategoriler ve Trendler
            </Text>
          </View>

          <TouchableOpacity
            style={[styles.headerButton, { backgroundColor: 'rgba(255,255,255,0.2)' }]}
            onPress={() => {
              handleRefresh();
            }}
          >
            <Ionicons name="refresh" size={24} color="#fff" />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.headerButton, { backgroundColor: 'rgba(255,255,255,0.2)' }]}
            onPress={() => {
              exportStatistics();
            }}
          >
            <Ionicons name="share-outline" size={24} color="#fff" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Ana İçerik */}
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.PRIMARY]}
            tintColor={theme.PRIMARY}
          />
        }
      >
        {/* Finansal Özet Kartları */}
        <View style={styles.summarySection}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            💡 Finansal Özet
          </Text>
          
          <View style={styles.summaryCards}>
            {/* Gelir Kartı */}
            <View style={[styles.summaryCard, { backgroundColor: theme.SURFACE }]}>
              <View style={styles.cardHeader}>
                <Text style={styles.cardIcon}>💰</Text>
                <Text style={[styles.cardTitle, { color: theme.TEXT_PRIMARY }]}>
                  Gelir
                </Text>
              </View>
              <Text style={[styles.cardAmount, { color: theme.SUCCESS }]}>
                {formatCurrency(stats.totalIncome)}
              </Text>
              <Text style={[styles.cardSubtitle, { color: theme.TEXT_SECONDARY }]}>
                {selectedPeriod === '7days' ? 'Son 7 gün' : 
                 selectedPeriod === '30days' ? 'Son 30 gün' : 
                 selectedPeriod === '90days' ? 'Son 3 ay' : 'Bu yıl'}
              </Text>
            </View>

            {/* Gider Kartı */}
            <View style={[styles.summaryCard, { backgroundColor: theme.SURFACE }]}>
              <View style={styles.cardHeader}>
                <Text style={styles.cardIcon}>📤</Text>
                <Text style={[styles.cardTitle, { color: theme.TEXT_PRIMARY }]}>
                  Gider
                </Text>
              </View>
              <Text style={[styles.cardAmount, { color: theme.ERROR }]}>
                {formatCurrency(stats.totalExpense)}
              </Text>
              <Text style={[styles.cardSubtitle, { color: theme.TEXT_SECONDARY }]}>
                {selectedPeriod === '7days' ? 'Son 7 gün' : 
                 selectedPeriod === '30days' ? 'Son 30 gün' : 
                 selectedPeriod === '90days' ? 'Son 3 ay' : 'Bu yıl'}
              </Text>
            </View>

            {/* Bakiye Kartı */}
            <View style={[styles.summaryCard, { backgroundColor: theme.SURFACE }]}>
              <View style={styles.cardHeader}>
                <Text style={styles.cardIcon}>⚖️</Text>
                <Text style={[styles.cardTitle, { color: theme.TEXT_PRIMARY }]}>
                  Bakiye
                </Text>
              </View>
              <Text
                style={[
                  styles.cardAmount,
                  { color: stats.balance >= 0 ? theme.SUCCESS : theme.ERROR },
                ]}
              >
                {formatCurrency(stats.balance)}
              </Text>
              <Text style={[styles.cardSubtitle, { color: theme.TEXT_SECONDARY }]}>
                Gelir - Gider
              </Text>
            </View>
          </View>
        </View>

        {/* Dönem Filtreleme */}
        <PeriodFilter />

        {/* Chart Section - Phase 3 */}

        {/* Kategori Detayları */}
        {categoryData.length > 0 && (
          <View style={styles.categoryDetailsSection}>
            <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
              📋 Kategori Detayları
            </Text>
            <View style={[styles.categoryDetailsList, { backgroundColor: theme.SURFACE }]}>
              {categoryData.map((item, index) => (
                <View key={index} style={styles.categoryDetailItem}>
                  <View style={styles.categoryDetailLeft}>
                    <Text style={[styles.categoryDetailRank, { color: theme.PRIMARY }]}>
                      #{index + 1}
                    </Text>
                    <Text style={[styles.categoryDetailName, { color: theme.TEXT_PRIMARY }]}>
                      {item.name}
                    </Text>
                  </View>
                  <View style={styles.categoryDetailRight}>
                    <Text style={[styles.categoryDetailAmount, { color: theme.TEXT_PRIMARY }]}>
                      {formatCurrency(item.total)}
                    </Text>
                    <Text style={[styles.categoryDetailCount, { color: theme.TEXT_SECONDARY }]}>
                      {item.count} işlem
                    </Text>
                  </View>
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Gelişmiş Öngörüler */}
        <View style={styles.insightsSection}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            💡 Akıllı Öngörüler
          </Text>
          
          <View style={[styles.insightCard, { backgroundColor: theme.SURFACE }]}>
            <View style={styles.insightHeader}>
              <Text style={styles.insightIcon}>📊</Text>
              <Text style={[styles.insightTitle, { color: theme.TEXT_PRIMARY }]}>
                Harcama Analizi
              </Text>
            </View>
            <Text style={[styles.insightText, { color: theme.TEXT_SECONDARY }]}>
              {categoryData.length > 0 
                ? `En çok harcama yaptığın kategori: ${categoryData[0].name} (${formatCurrency(categoryData[0].total)}). Bu, toplam harcamanın %${((categoryData[0].total / stats.totalExpense) * 100).toFixed(1)}'ini oluşturuyor.`
                : "Henüz kategori verisi bulunmuyor."
              }
            </Text>
          </View>

          <View style={[styles.insightCard, { backgroundColor: theme.SURFACE }]}>
            <View style={styles.insightHeader}>
              <Text style={styles.insightIcon}>⚖️</Text>
              <Text style={[styles.insightTitle, { color: theme.TEXT_PRIMARY }]}>
                Finansal Durum
              </Text>
            </View>
            <Text style={[styles.insightText, { color: theme.TEXT_SECONDARY }]}>
              {stats.balance >= 0 
                ? `Gelir-gider dengen pozitif! ${formatCurrency(stats.balance)} tasarruf ettin. Harika bir finansal durum.`
                : `Giderler gelirden ${formatCurrency(Math.abs(stats.balance))} fazla. Harcamalarını gözden geçirmeyi düşün.`
              }
            </Text>
          </View>

          <View style={[styles.insightCard, { backgroundColor: theme.SURFACE }]}>
            <View style={styles.insightHeader}>
              <Text style={styles.insightIcon}>📈</Text>
              <Text style={[styles.insightTitle, { color: theme.TEXT_PRIMARY }]}>
                Gelir Kaynakları
              </Text>
            </View>
            <Text style={[styles.insightText, { color: theme.TEXT_SECONDARY }]}>
              {(() => {
                const transactionIncome = (stats.totalIncome - (regularIncomeData.regularIncomeTotal || 0) - (overtimeData.overtimeTotal || 0));
                const regularIncome = regularIncomeData.regularIncomeTotal || 0;
                const overtimeIncome = overtimeData.overtimeTotal || 0;
                
                if (stats.totalIncome === 0) {
                  return "Henüz gelir kaydı bulunmuyor.";
                }
                
                let mainSource = "İşlemler";
                let maxAmount = transactionIncome;
                
                if (regularIncome > maxAmount) {
                  mainSource = "Düzenli gelir";
                  maxAmount = regularIncome;
                }
                
                if (overtimeIncome > maxAmount) {
                  mainSource = "Mesai";
                  maxAmount = overtimeIncome;
                }
                
                return `Ana gelir kaynağın: ${mainSource} (${formatCurrency(maxAmount)})`;
              })()}
            </Text>
          </View>

          <View style={[styles.insightCard, { backgroundColor: theme.SURFACE }]}>
            <View style={styles.insightHeader}>
              <Text style={styles.insightIcon}>📊</Text>
              <Text style={[styles.insightTitle, { color: theme.TEXT_PRIMARY }]}>
                Dönemsel Karşılaştırma
              </Text>
            </View>
            <Text style={[styles.insightText, { color: theme.TEXT_SECONDARY }]}>
              {(() => {
                const selectedDays = getPeriodDays();
                if (selectedDays >= 30) {
                  const dailyAverage = stats.totalExpense / selectedDays;
                  return `Günlük ortalama harcama: ${formatCurrency(dailyAverage)}`;
                } else if (selectedDays >= 7) {
                  const weeklyAverage = stats.totalExpense / Math.ceil(selectedDays / 7);
                  return `Haftalık ortalama harcama: ${formatCurrency(weeklyAverage)}`;
                } else {
                  return `Son ${selectedDays} günde toplam harcama: ${formatCurrency(stats.totalExpense)}`;
                }
              })()}
            </Text>
          </View>

          <View style={[styles.insightCard, { backgroundColor: theme.SURFACE }]}>
            <View style={styles.insightHeader}>
              <Text style={styles.insightIcon}>🎯</Text>
              <Text style={[styles.insightTitle, { color: theme.TEXT_PRIMARY }]}>
                Tasarruf Önerisi
              </Text>
            </View>
            <Text style={[styles.insightText, { color: theme.TEXT_SECONDARY }]}>
              {(() => {
                if (stats.balance >= 0) {
                  const savingsRate = (stats.balance / stats.totalIncome) * 100;
                  return `Tasarruf oranın: %${savingsRate.toFixed(1)}. ${savingsRate > 20 ? 'Harika!' : 'İyi bir seviye!'}`;
                } else {
                  return "Tasarruf edebilmek için en büyük harcama kategorini azaltmayı dene.";
                }
              })()}
            </Text>
          </View>
        </View>

        {/* Chart Section - Phase 3 */}
        <View style={styles.chartSection}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            📊 Kategori Analizi
          </Text>
          
          {/* Chart Type Selector */}
          <ChartTypeSelector
            selectedType={selectedChartType}
            onTypeChange={setSelectedChartType}
            availableTypes={['donut', 'bar']}
          />

          {/* Category Chart */}
          <SimpleCategoryChart
            data={categoryData}
            title="Gider Kategorileri"
            type={selectedChartType}
            onCategoryPress={(category) => {
              // Gelecekte kategori detay ekranına yönlendirme
              console.log('Kategori seçildi:', category);
            }}
          />
        </View>

        {/* Export Section */}
        <View style={styles.exportSection}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            📤 Veri Export
          </Text>
          
          <View style={styles.exportButtons}>
            <TouchableOpacity
              style={[styles.exportButton, { backgroundColor: theme.SUCCESS }]}
              onPress={() => {
                const exportData = prepareExportData();
                import('../utils/exportHelpers').then(module => 
                  module.exportToCSV(exportData, 'istatistikler')
                );
              }}
            >
              <Ionicons name="document-text-outline" size={24} color="#fff" />
              <Text style={[styles.exportButtonText, { color: '#fff' }]}>
                CSV/Excel
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.exportButton, { backgroundColor: theme.WARNING }]}
              onPress={() => {
                const exportData = prepareExportData();
                import('../utils/exportHelpers').then(module => 
                  module.exportToPDF(exportData, 'istatistikler')
                );
              }}
            >
              <Ionicons name="document-outline" size={24} color="#fff" />
              <Text style={[styles.exportButtonText, { color: '#fff' }]}>
                PDF Rapor
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.exportButton, { backgroundColor: theme.INFO }]}
              onPress={() => {
                const exportData = prepareExportData();
                import('../utils/exportHelpers').then(module => 
                  module.exportToJSON(exportData, 'istatistikler')
                );
              }}
            >
              <Ionicons name="code-outline" size={24} color="#fff" />
              <Text style={[styles.exportButtonText, { color: '#fff' }]}>
                JSON
              </Text>
            </TouchableOpacity>
          </View>
          
          <Text style={[styles.exportDescription, { color: theme.TEXT_SECONDARY }]}>
            Verilerinizi Excel, PDF raporu veya JSON formatında export edip paylaşabilirsiniz
          </Text>
        </View>

        {/* Detaylı Raporlar Yönlendirmesi */}
        <View style={styles.actionSection}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            📋 Daha Fazla Analiz
          </Text>
          
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.PRIMARY }]}
            onPress={() => navigation.navigate('Reports')}
          >
            <Text style={[styles.actionButtonText, { color: '#fff' }]}>
              📊 Detaylı Raporlar
            </Text>
            <Ionicons name="chevron-forward" size={20} color="#fff" />
          </TouchableOpacity>
          
          <Text style={[styles.actionDescription, { color: theme.TEXT_SECONDARY }]}>
            Gelişmiş grafikler ve karşılaştırmalı analizler için
          </Text>
        </View>

        {/* Alt Boşluk */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingBottom: 20,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 20,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 2,
  },
  headerSubtitle: {
    fontSize: 12,
    fontWeight: '500',
    opacity: 0.9,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  summarySection: {
    marginTop: 20,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  summaryCards: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  summaryCard: {
    width: (screenWidth - 48) / 3,
    padding: 12,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  cardIcon: {
    fontSize: 14,
    marginRight: 6,
  },
  cardTitle: {
    fontSize: 12,
    fontWeight: '500',
  },
  cardAmount: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 4,
  },
  cardSubtitle: {
    fontSize: 10,
    opacity: 0.7,
  },
  chartTypeSelector: {
    marginBottom: 24,
  },
  chartTypeSelectorTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  chartTypeButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  chartTypeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  chartTypeButtonText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
  chartSection: {
    marginBottom: 24,
  },
  chartWrapper: {
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  chartContainer: {
    alignItems: 'center',
    minHeight: 200,
  },
  emptyChart: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  emptyChartText: {
    fontSize: 16,
    marginTop: 12,
    textAlign: 'center',
  },
  // Donut Chart Styles
  donutCenter: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  donutCenterAmount: {
    fontSize: 14,
    fontWeight: '700',
    textAlign: 'center',
  },
  donutCenterLabel: {
    fontSize: 10,
    textAlign: 'center',
  },
  legend: {
    width: '100%',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  legendText: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
  legendAmount: {
    fontSize: 12,
    fontWeight: '400',
  },
  // Bar Chart Styles
  barChartContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-around',
    width: '100%',
    height: 160,
    paddingBottom: 20,
  },
  barItem: {
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 4,
  },
  bar: {
    width: 20,
    marginBottom: 8,
    borderRadius: 4,
    minHeight: 10,
  },
  barLabel: {
    fontSize: 10,
    textAlign: 'center',
    marginBottom: 4,
  },
  barAmount: {
    fontSize: 10,
    textAlign: 'center',
    fontWeight: '600',
  },
  categoryDetailsSection: {
    marginBottom: 24,
  },
  categoryDetailsList: {
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  categoryDetailItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  categoryDetailLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryDetailRank: {
    fontSize: 14,
    fontWeight: '700',
    width: 30,
  },
  categoryDetailName: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
  categoryDetailRight: {
    alignItems: 'flex-end',
  },
  categoryDetailAmount: {
    fontSize: 14,
    fontWeight: '700',
  },
  categoryDetailCount: {
    fontSize: 12,
    marginTop: 2,
  },
  insightsSection: {
    marginBottom: 24,
  },
  chartSection: {
    marginBottom: 24,
  },
  insightCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  insightHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  insightIcon: {
    fontSize: 16,
    marginRight: 8,
  },
  insightTitle: {
    fontSize: 14,
    fontWeight: '600',
  },
  insightText: {
    fontSize: 13,
    lineHeight: 18,
  },
  actionSection: {
    marginBottom: 24,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  actionDescription: {
    fontSize: 13,
    textAlign: 'center',
    lineHeight: 18,
  },
  bottomSpacing: {
    height: 20,
  },
  // Period Filter Styles
  periodFilter: {
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  periodFilterTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  periodButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  periodButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginRight: 8,
  },
  periodButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  periodFilterSubtitle: {
    fontSize: 12,
    textAlign: 'center',
  },
  // Export Section Styles
  exportSection: {
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  exportButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
    gap: 12,
  },
  exportButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  exportButtonText: {
    fontSize: 12,
    fontWeight: '600',
    marginTop: 4,
    textAlign: 'center',
  },
  exportDescription: {
    fontSize: 13,
    textAlign: 'center',
    lineHeight: 18,
  },
});

export default ModernStatisticsScreen;
