import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Alert,
  RefreshControl
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import { useExchangeRate } from '../context/ExchangeRateContext';
import { formatCurrency, getCurrencySymbol } from '../utils/formatters';
import ExchangeRateDisplay from '../components/ExchangeRateDisplay';

/**
 * Döviz Kuru Ekranı
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @returns {JSX.Element} Döviz Kuru Ekranı
 */
export default function CurrencyScreen({ navigation }) {
  const db = useSQLiteContext();

  // ExchangeRateContext'ten değerleri al
  const {
    rates,
    loading,
    lastUpdate,
    convertCurrency: convertCurrencyFromContext,
    updateExchangeRates,
    getSupportedCurrencies
  } = useExchangeRate();

  // Durum değişkenleri
  const [refreshing, setRefreshing] = useState(false);
  const [amount, setAmount] = useState('1');
  const [fromCurrency, setFromCurrency] = useState('TRY');
  const [toCurrency, setToCurrency] = useState('USD');
  const [convertedAmount, setConvertedAmount] = useState(null);
  const [showCurrencySelector, setShowCurrencySelector] = useState(false);
  const [selectingFor, setSelectingFor] = useState('from'); // 'from' veya 'to'

  // Desteklenen para birimleri
  const supportedCurrencies = getSupportedCurrencies();

  // Popüler para birimleri
  const popularCurrencies = ['TRY', 'USD', 'EUR', 'GBP'];

  // Dönüşüm yap
  const performConversion = useCallback(async () => {
    if (amount && fromCurrency && toCurrency) {
      try {
        const converted = await convertCurrencyFromContext(
          parseFloat(amount),
          fromCurrency,
          toCurrency
        );
        setConvertedAmount(converted);
      } catch (error) {
        console.error('Döviz dönüştürme hatası:', error);
        Alert.alert('Hata', 'Döviz dönüştürülürken bir hata oluştu.');
      }
    }
  }, [amount, fromCurrency, toCurrency, convertCurrencyFromContext]);

  // Ekran yüklendiğinde veya para birimleri değiştiğinde dönüşüm yap
  useEffect(() => {
    performConversion();
  }, [performConversion]);

  // Yenileme işlemi
  const handleRefresh = async () => {
    setRefreshing(true);

    try {
      // Döviz kurlarını güncelle
      await updateExchangeRates();
      await performConversion();
    } catch (error) {
      console.error('Döviz kurları güncelleme hatası:', error);
      Alert.alert('Hata', 'Döviz kurları güncellenirken bir hata oluştu.');
    }

    setRefreshing(false);
  };

  // Para birimi seçimi
  const handleCurrencySelect = (currency) => {
    if (selectingFor === 'from') {
      setFromCurrency(currency);
    } else {
      setToCurrency(currency);
    }

    setShowCurrencySelector(false);
  };

  // Para birimlerini değiştir
  const swapCurrencies = () => {
    setFromCurrency(toCurrency);
    setToCurrency(fromCurrency);
  };

  // Dönüşüm yap
  const convertCurrency = async () => {
    if (!amount || isNaN(parseFloat(amount))) {
      Alert.alert('Hata', 'Lütfen geçerli bir miktar girin.');
      return;
    }

    await performConversion();
  };

  // Para birimi seçim modalı
  const renderCurrencySelector = () => {
    if (!showCurrencySelector) return null;

    return (
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>
              {selectingFor === 'from' ? 'Kaynak Para Birimi' : 'Hedef Para Birimi'}
            </Text>
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setShowCurrencySelector(false)}
            >
              <MaterialIcons name="close" size={24} color={Colors.GRAY_600} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalBody}>
            <Text style={styles.sectionTitle}>Popüler Para Birimleri</Text>
            <View style={styles.currencyGrid}>
              {popularCurrencies.map(currency => (
                <TouchableOpacity
                  key={currency}
                  style={[
                    styles.currencyItem,
                    (selectingFor === 'from' && fromCurrency === currency) ||
                    (selectingFor === 'to' && toCurrency === currency)
                      ? styles.selectedCurrency
                      : null
                  ]}
                  onPress={() => handleCurrencySelect(currency)}
                >
                  <Text style={styles.currencySymbol}>
                    {getCurrencySymbol(currency)}
                  </Text>
                  <Text style={styles.currencyCode}>{currency}</Text>
                </TouchableOpacity>
              ))}
            </View>

            <Text style={styles.sectionTitle}>Tüm Para Birimleri</Text>
            {supportedCurrencies.map(currency => (
              <TouchableOpacity
                key={currency.code}
                style={styles.currencyListItem}
                onPress={() => handleCurrencySelect(currency.code)}
              >
                <View style={styles.currencyInfo}>
                  <Text style={styles.currencyListSymbol}>
                    {currency.symbol}
                  </Text>
                  <View>
                    <Text style={styles.currencyListCode}>{currency.code}</Text>
                    <Text style={styles.currencyListName}>{currency.name}</Text>
                  </View>
                </View>

                {(selectingFor === 'from' && fromCurrency === currency.code) ||
                 (selectingFor === 'to' && toCurrency === currency.code) ? (
                  <MaterialIcons name="check" size={20} color={Colors.SUCCESS} />
                ) : null}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Döviz Çevirici</Text>
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={handleRefresh}
          disabled={refreshing}
        >
          {refreshing ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <MaterialIcons name="refresh" size={24} color="#fff" />
          )}
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[Colors.PRIMARY]}
          />
        }
      >
        {/* Döviz Çevirici */}
        <View style={styles.converterCard}>
          <Text style={styles.cardTitle}>Döviz Çevirici</Text>

          <View style={styles.inputContainer}>
            <TextInput
              style={styles.amountInput}
              value={amount}
              onChangeText={setAmount}
              placeholder="Miktar"
              keyboardType="numeric"
              onEndEditing={convertCurrency}
            />

            <TouchableOpacity
              style={styles.currencyButton}
              onPress={() => {
                setSelectingFor('from');
                setShowCurrencySelector(true);
              }}
            >
              <Text style={styles.currencyButtonText}>{fromCurrency}</Text>
              <MaterialIcons name="arrow-drop-down" size={24} color={Colors.GRAY_600} />
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={styles.swapButton}
            onPress={swapCurrencies}
          >
            <MaterialIcons name="swap-vert" size={24} color={Colors.PRIMARY} />
          </TouchableOpacity>

          <View style={styles.inputContainer}>
            <Text style={styles.resultText}>
              {convertedAmount !== null
                ? formatCurrency(convertedAmount, toCurrency)
                : '-'}
            </Text>

            <TouchableOpacity
              style={styles.currencyButton}
              onPress={() => {
                setSelectingFor('to');
                setShowCurrencySelector(true);
              }}
            >
              <Text style={styles.currencyButtonText}>{toCurrency}</Text>
              <MaterialIcons name="arrow-drop-down" size={24} color={Colors.GRAY_600} />
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={styles.convertButton}
            onPress={convertCurrency}
          >
            <Text style={styles.convertButtonText}>Dönüştür</Text>
          </TouchableOpacity>

          {lastUpdate && (
            <Text style={styles.lastUpdate}>
              Son güncelleme: {new Date(lastUpdate).toLocaleString('tr-TR')}
            </Text>
          )}
        </View>

        {/* Döviz Kurları */}
        <ExchangeRateDisplay
          baseCurrency="TRY"
          targetCurrencies={['USD', 'EUR', 'GBP']}
        />

        <ExchangeRateDisplay
          baseCurrency="USD"
          targetCurrencies={['TRY', 'EUR', 'GBP']}
        />

        <ExchangeRateDisplay
          baseCurrency="EUR"
          targetCurrencies={['TRY', 'USD', 'GBP']}
        />
      </ScrollView>

      {renderCurrencySelector()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.PRIMARY,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
  refreshButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  converterCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.GRAY_800,
    marginBottom: 16,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  amountInput: {
    flex: 1,
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: Colors.GRAY_800,
    marginRight: 8,
  },
  currencyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    minWidth: 100,
  },
  currencyButtonText: {
    fontSize: 16,
    color: Colors.GRAY_800,
    marginRight: 4,
  },
  swapButton: {
    alignSelf: 'center',
    padding: 8,
    marginVertical: 8,
  },
  resultText: {
    flex: 1,
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.SUCCESS,
    marginRight: 8,
  },
  convertButton: {
    backgroundColor: Colors.PRIMARY,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    marginTop: 16,
  },
  convertButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
  },
  lastUpdate: {
    fontSize: 12,
    color: Colors.GRAY_500,
    marginTop: 16,
    textAlign: 'center',
  },
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.GRAY_800,
  },
  modalCloseButton: {
    padding: 4,
  },
  modalBody: {
    padding: 16,
    maxHeight: 400,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.GRAY_700,
    marginBottom: 12,
    marginTop: 8,
  },
  currencyGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  currencyItem: {
    width: '23%',
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    padding: 12,
    marginRight: '2%',
    marginBottom: 8,
    alignItems: 'center',
  },
  selectedCurrency: {
    backgroundColor: Colors.PRIMARY_LIGHT,
    borderWidth: 1,
    borderColor: Colors.PRIMARY,
  },
  currencySymbol: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.GRAY_800,
    marginBottom: 4,
  },
  currencyCode: {
    fontSize: 14,
    color: Colors.GRAY_700,
  },
  currencyListItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200,
  },
  currencyInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  currencyListSymbol: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.GRAY_800,
    marginRight: 12,
    width: 30,
    textAlign: 'center',
  },
  currencyListCode: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.GRAY_800,
  },
  currencyListName: {
    fontSize: 14,
    color: Colors.GRAY_600,
  },
});
