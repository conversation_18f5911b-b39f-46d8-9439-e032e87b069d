import { useState, useCallback } from 'react';

/**
 * <PERSON><PERSON><PERSON> sütunlarını yönetmek için custom hook
 * @param {Array} initialColumns - Başlangıç sütunları
 * @returns {Object} Sütun yönetimi fonksiyonları ve durumu
 */
const useTableColumns = (initialColumns = []) => {
  const [columns, setColumns] = useState(initialColumns);
  const [history, setHistory] = useState([initialColumns]);
  const [historyIndex, setHistoryIndex] = useState(0);

  /**
   * Sütun ekle
   * @param {Object} column - Eklenecek sütun
   */
  const addColumn = useCallback((column) => {
    const newColumn = {
      id: column.id || `col_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: column.name || 'Yeni <PERSON>ütun',
      type: column.type || 'text',
      visible: column.visible !== false,
      sortable: column.sortable !== false,
      filterable: column.filterable !== false,
      resizable: column.resizable !== false,
      width: column.width || 100,
      minWidth: column.minWidth || 50,
      maxWidth: column.maxWidth || 500,
      align: column.align || 'left',
      format: column.format || null,
      ...column
    };

    setColumns(prev => {
      const newColumns = [...prev, newColumn];
      addToHistory(newColumns);
      return newColumns;
    });
  }, []);

  /**
   * Sütun sil
   * @param {string} columnId - Silinecek sütun ID'si
   */
  const removeColumn = useCallback((columnId) => {
    setColumns(prev => {
      const newColumns = prev.filter(col => col.id !== columnId);
      addToHistory(newColumns);
      return newColumns;
    });
  }, []);

  /**
   * Sütun güncelle
   * @param {string} columnId - Güncellenecek sütun ID'si
   * @param {Object} updates - Güncellenecek alanlar
   */
  const updateColumn = useCallback((columnId, updates) => {
    setColumns(prev => {
      const newColumns = prev.map(col => 
        col.id === columnId ? { ...col, ...updates } : col
      );
      addToHistory(newColumns);
      return newColumns;
    });
  }, []);

  /**
   * Sütun sırası değiştir
   * @param {number} fromIndex - Kaynak index
   * @param {number} toIndex - Hedef index
   */
  const moveColumn = useCallback((fromIndex, toIndex) => {
    setColumns(prev => {
      const newColumns = [...prev];
      const [movedColumn] = newColumns.splice(fromIndex, 1);
      newColumns.splice(toIndex, 0, movedColumn);
      addToHistory(newColumns);
      return newColumns;
    });
  }, []);

  /**
   * Sütun görünürlüğünü değiştir
   * @param {string} columnId - Sütun ID'si
   * @param {boolean} visible - Görünürlük durumu
   */
  const toggleColumnVisibility = useCallback((columnId, visible) => {
    updateColumn(columnId, { visible });
  }, [updateColumn]);

  /**
   * Sütun genişliğini değiştir
   * @param {string} columnId - Sütun ID'si
   * @param {number} width - Yeni genişlik
   */
  const resizeColumn = useCallback((columnId, width) => {
    const column = columns.find(col => col.id === columnId);
    if (column) {
      const clampedWidth = Math.max(
        column.minWidth || 50,
        Math.min(width, column.maxWidth || 500)
      );
      updateColumn(columnId, { width: clampedWidth });
    }
  }, [columns, updateColumn]);

  /**
   * Tüm sütunları sıfırla
   */
  const resetColumns = useCallback(() => {
    setColumns([]);
    addToHistory([]);
  }, []);

  /**
   * Sütunları varsayılan ayarlara döndür
   */
  const resetToDefault = useCallback(() => {
    setColumns(initialColumns);
    addToHistory(initialColumns);
  }, [initialColumns]);

  /**
   * Geçmişe yeni durum ekle
   * @param {Array} newColumns - Yeni sütun dizisi
   */
  const addToHistory = useCallback((newColumns) => {
    setHistory(prev => {
      const newHistory = prev.slice(0, historyIndex + 1);
      newHistory.push(newColumns);
      // Geçmişi 50 adımla sınırla
      if (newHistory.length > 50) {
        newHistory.shift();
      }
      return newHistory;
    });
    setHistoryIndex(prev => Math.min(prev + 1, 49));
  }, [historyIndex]);

  /**
   * Geri al
   */
  const undo = useCallback(() => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1;
      setHistoryIndex(newIndex);
      setColumns(history[newIndex]);
    }
  }, [history, historyIndex]);

  /**
   * Yinele
   */
  const redo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      const newIndex = historyIndex + 1;
      setHistoryIndex(newIndex);
      setColumns(history[newIndex]);
    }
  }, [history, historyIndex]);

  /**
   * Sütun bulma
   * @param {string} columnId - Sütun ID'si
   * @returns {Object|null} Bulunan sütun
   */
  const findColumn = useCallback((columnId) => {
    return columns.find(col => col.id === columnId) || null;
  }, [columns]);

  /**
   * Görünür sütunları getir
   * @returns {Array} Görünür sütunlar
   */
  const getVisibleColumns = useCallback(() => {
    return columns.filter(col => col.visible !== false);
  }, [columns]);

  /**
   * Sıralanabilir sütunları getir
   * @returns {Array} Sıralanabilir sütunlar
   */
  const getSortableColumns = useCallback(() => {
    return columns.filter(col => col.sortable !== false);
  }, [columns]);

  /**
   * Filtrelenebilir sütunları getir
   * @returns {Array} Filtrelenebilir sütunlar
   */
  const getFilterableColumns = useCallback(() => {
    return columns.filter(col => col.filterable !== false);
  }, [columns]);

  /**
   * Sütun istatistikleri
   * @returns {Object} Sütun istatistikleri
   */
  const getColumnStats = useCallback(() => {
    return {
      total: columns.length,
      visible: columns.filter(col => col.visible !== false).length,
      hidden: columns.filter(col => col.visible === false).length,
      sortable: columns.filter(col => col.sortable !== false).length,
      filterable: columns.filter(col => col.filterable !== false).length,
      resizable: columns.filter(col => col.resizable !== false).length,
    };
  }, [columns]);

  /**
   * Sütunları JSON olarak dışa aktar
   * @returns {string} JSON string
   */
  const exportColumns = useCallback(() => {
    return JSON.stringify(columns, null, 2);
  }, [columns]);

  /**
   * JSON'dan sütunları içe aktar
   * @param {string} jsonString - JSON string
   * @returns {boolean} Başarı durumu
   */
  const importColumns = useCallback((jsonString) => {
    try {
      const importedColumns = JSON.parse(jsonString);
      if (Array.isArray(importedColumns)) {
        setColumns(importedColumns);
        addToHistory(importedColumns);
        return true;
      }
      return false;
    } catch (error) {
      return false;
    }
  }, []);

  return {
    // Durum
    columns,
    canUndo: historyIndex > 0,
    canRedo: historyIndex < history.length - 1,
    
    // Sütun yönetimi
    addColumn,
    removeColumn,
    updateColumn,
    moveColumn,
    toggleColumnVisibility,
    resizeColumn,
    resetColumns,
    resetToDefault,
    
    // Geçmiş yönetimi
    undo,
    redo,
    
    // Yardımcı fonksiyonlar
    findColumn,
    getVisibleColumns,
    getSortableColumns,
    getFilterableColumns,
    getColumnStats,
    exportColumns,
    importColumns,
  };
};

export { useTableColumns };
