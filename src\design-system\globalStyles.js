import { StyleSheet } from 'react-native';
import tokens from './tokens';

/**
 * Global Style System
 * Tüm uygulama genelinde kullanılacak ortak stiller
 */

/**
 * Modern Card Styles
 */
export const cardStyles = StyleSheet.create({
  // Temel card
  base: {
    backgroundColor: '#ffffff',
    borderRadius: tokens.borderRadius['2xl'],
    padding: tokens.spacing[4],
    marginBottom: tokens.spacing[4],
    ...tokens.shadows.sm,
  },
  
  // Elevated card
  elevated: {
    backgroundColor: '#ffffff',
    borderRadius: tokens.borderRadius['2xl'],
    padding: tokens.spacing[6],
    marginBottom: tokens.spacing[4],
    ...tokens.shadows.md,
  },
  
  // Interactive card
  interactive: {
    backgroundColor: '#ffffff',
    borderRadius: tokens.borderRadius['2xl'],
    padding: tokens.spacing[4],
    marginBottom: tokens.spacing[4],
    ...tokens.shadows.sm,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  
  // Header card
  header: {
    backgroundColor: tokens.colors.primary[500],
    borderRadius: 0,
    borderBottomLeftRadius: tokens.borderRadius['3xl'],
    borderBottomRightRadius: tokens.borderRadius['3xl'],
    padding: tokens.spacing[6],
    paddingTop: tokens.spacing[12],
    ...tokens.shadows.lg,
  },
});

/**
 * Modern Button Styles
 */
export const buttonStyles = StyleSheet.create({
  // Base button
  base: {
    borderRadius: tokens.borderRadius.xl,
    paddingVertical: tokens.spacing[3],
    paddingHorizontal: tokens.spacing[6],
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    minHeight: 48,
  },
  
  // Primary button
  primary: {
    backgroundColor: tokens.colors.primary[500],
    ...tokens.shadows.sm,
  },
  
  // Secondary button
  secondary: {
    backgroundColor: tokens.colors.gray[100],
    borderWidth: 1,
    borderColor: tokens.colors.gray[300],
  },
  
  // Outline button
  outline: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: tokens.colors.primary[500],
  },
  
  // Ghost button
  ghost: {
    backgroundColor: 'transparent',
  },
  
  // Danger button
  danger: {
    backgroundColor: tokens.colors.danger[500],
    ...tokens.shadows.sm,
  },
  
  // Success button
  success: {
    backgroundColor: tokens.colors.success[500],
    ...tokens.shadows.sm,
  },
  
  // Small size
  small: {
    paddingVertical: tokens.spacing[2],
    paddingHorizontal: tokens.spacing[4],
    minHeight: 36,
  },
  
  // Large size
  large: {
    paddingVertical: tokens.spacing[4],
    paddingHorizontal: tokens.spacing[8],
    minHeight: 56,
  },
  
  // Full width
  fullWidth: {
    width: '100%',
  },
  
  // Disabled state
  disabled: {
    opacity: 0.6,
  },
});

/**
 * Modern Input Styles
 */
export const inputStyles = StyleSheet.create({
  // Container
  container: {
    marginBottom: tokens.spacing[4],
  },
  
  // Label
  label: {
    fontSize: tokens.typography.fontSize.sm,
    fontWeight: tokens.typography.fontWeight.medium,
    color: tokens.colors.gray[700],
    marginBottom: tokens.spacing[2],
  },
  
  // Input base
  input: {
    borderWidth: 1,
    borderColor: tokens.colors.gray[300],
    borderRadius: tokens.borderRadius.lg,
    paddingVertical: tokens.spacing[3],
    paddingHorizontal: tokens.spacing[4],
    fontSize: tokens.typography.fontSize.base,
    backgroundColor: '#ffffff',
    minHeight: 48,
  },
  
  // Focused state
  focused: {
    borderColor: tokens.colors.primary[500],
    borderWidth: 2,
    ...tokens.shadows.sm,
  },
  
  // Error state
  error: {
    borderColor: tokens.colors.danger[500],
    borderWidth: 2,
  },
  
  // Disabled state
  disabled: {
    backgroundColor: tokens.colors.gray[50],
    color: tokens.colors.gray[400],
  },
  
  // Helper text
  helperText: {
    fontSize: tokens.typography.fontSize.xs,
    color: tokens.colors.gray[600],
    marginTop: tokens.spacing[1],
  },
  
  // Error text
  errorText: {
    fontSize: tokens.typography.fontSize.xs,
    color: tokens.colors.danger[500],
    marginTop: tokens.spacing[1],
  },
});

/**
 * Modern Typography Styles
 */
export const typographyStyles = StyleSheet.create({
  // Headings
  h1: {
    fontSize: tokens.typography.fontSize['4xl'],
    fontWeight: tokens.typography.fontWeight.bold,
    lineHeight: tokens.typography.lineHeight.tight * tokens.typography.fontSize['4xl'],
    color: tokens.colors.gray[900],
  },
  
  h2: {
    fontSize: tokens.typography.fontSize['3xl'],
    fontWeight: tokens.typography.fontWeight.bold,
    lineHeight: tokens.typography.lineHeight.tight * tokens.typography.fontSize['3xl'],
    color: tokens.colors.gray[900],
  },
  
  h3: {
    fontSize: tokens.typography.fontSize['2xl'],
    fontWeight: tokens.typography.fontWeight.semibold,
    lineHeight: tokens.typography.lineHeight.snug * tokens.typography.fontSize['2xl'],
    color: tokens.colors.gray[900],
  },
  
  h4: {
    fontSize: tokens.typography.fontSize.xl,
    fontWeight: tokens.typography.fontWeight.semibold,
    lineHeight: tokens.typography.lineHeight.snug * tokens.typography.fontSize.xl,
    color: tokens.colors.gray[900],
  },
  
  // Body text
  body: {
    fontSize: tokens.typography.fontSize.base,
    fontWeight: tokens.typography.fontWeight.normal,
    lineHeight: tokens.typography.lineHeight.normal * tokens.typography.fontSize.base,
    color: tokens.colors.gray[700],
  },
  
  bodyLarge: {
    fontSize: tokens.typography.fontSize.lg,
    fontWeight: tokens.typography.fontWeight.normal,
    lineHeight: tokens.typography.lineHeight.normal * tokens.typography.fontSize.lg,
    color: tokens.colors.gray[700],
  },
  
  bodySmall: {
    fontSize: tokens.typography.fontSize.sm,
    fontWeight: tokens.typography.fontWeight.normal,
    lineHeight: tokens.typography.lineHeight.normal * tokens.typography.fontSize.sm,
    color: tokens.colors.gray[600],
  },
  
  // Caption
  caption: {
    fontSize: tokens.typography.fontSize.xs,
    fontWeight: tokens.typography.fontWeight.normal,
    lineHeight: tokens.typography.lineHeight.normal * tokens.typography.fontSize.xs,
    color: tokens.colors.gray[500],
  },
  
  // Overline
  overline: {
    fontSize: tokens.typography.fontSize.xs,
    fontWeight: tokens.typography.fontWeight.medium,
    lineHeight: tokens.typography.lineHeight.normal * tokens.typography.fontSize.xs,
    color: tokens.colors.gray[600],
    textTransform: 'uppercase',
    letterSpacing: tokens.typography.letterSpacing.wide,
  },
});

/**
 * Layout Styles
 */
export const layoutStyles = StyleSheet.create({
  // Container
  container: {
    flex: 1,
    backgroundColor: tokens.colors.gray[50],
  },
  
  // Content
  content: {
    flex: 1,
    padding: tokens.spacing[4],
  },
  
  // Section
  section: {
    marginBottom: tokens.spacing[6],
  },
  
  // Row
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  // Column
  column: {
    flexDirection: 'column',
  },
  
  // Center
  center: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  // Space between
  spaceBetween: {
    justifyContent: 'space-between',
  },
  
  // Space around
  spaceAround: {
    justifyContent: 'space-around',
  },
  
  // Flex 1
  flex1: {
    flex: 1,
  },
  
  // Padding
  p1: { padding: tokens.spacing[1] },
  p2: { padding: tokens.spacing[2] },
  p3: { padding: tokens.spacing[3] },
  p4: { padding: tokens.spacing[4] },
  p5: { padding: tokens.spacing[5] },
  p6: { padding: tokens.spacing[6] },
  
  // Margin
  m1: { margin: tokens.spacing[1] },
  m2: { margin: tokens.spacing[2] },
  m3: { margin: tokens.spacing[3] },
  m4: { margin: tokens.spacing[4] },
  m5: { margin: tokens.spacing[5] },
  m6: { margin: tokens.spacing[6] },
  
  // Margin bottom
  mb1: { marginBottom: tokens.spacing[1] },
  mb2: { marginBottom: tokens.spacing[2] },
  mb3: { marginBottom: tokens.spacing[3] },
  mb4: { marginBottom: tokens.spacing[4] },
  mb5: { marginBottom: tokens.spacing[5] },
  mb6: { marginBottom: tokens.spacing[6] },
  
  // Margin top
  mt1: { marginTop: tokens.spacing[1] },
  mt2: { marginTop: tokens.spacing[2] },
  mt3: { marginTop: tokens.spacing[3] },
  mt4: { marginTop: tokens.spacing[4] },
  mt5: { marginTop: tokens.spacing[5] },
  mt6: { marginTop: tokens.spacing[6] },
});

/**
 * Animation Styles
 */
export const animationStyles = {
  // Fade in
  fadeIn: {
    opacity: 0,
    transform: [{ scale: 0.95 }],
  },
  
  fadeInVisible: {
    opacity: 1,
    transform: [{ scale: 1 }],
  },
  
  // Slide up
  slideUp: {
    transform: [{ translateY: 20 }],
    opacity: 0,
  },
  
  slideUpVisible: {
    transform: [{ translateY: 0 }],
    opacity: 1,
  },
  
  // Scale
  scale: {
    transform: [{ scale: 0.9 }],
  },
  
  scaleVisible: {
    transform: [{ scale: 1 }],
  },
};

export default {
  cardStyles,
  buttonStyles,
  inputStyles,
  typographyStyles,
  layoutStyles,
  animationStyles,
};
