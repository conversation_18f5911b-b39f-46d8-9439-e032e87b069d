import { Platform, Alert } from 'react-native';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import * as Print from 'expo-print';

/**
 * PDF Generator - Profesyonel PDF raporları oluşturur
 * React Native için expo-print kullanarak HTML→PDF dönüşümü yapar
 */
export class PDFGenerator {
  
  /**
   * PDF raporu oluşturur
   * @param {Object} params - PDF parametreleri
   * @param {Object} params.data - Rapor verisi
   * @param {string} params.title - <PERSON>or ba<PERSON>lığı
   * @param {string} params.type - <PERSON>or türü
   * @param {Object} params.config - PDF yapılandırması
   * @returns {Promise<Object>} PDF oluşturma sonucu
   */
  static async generatePDF({ data, title, type, config = {} }) {
    try {
      // PDF yapılandırması
      const pdfConfig = {
        format: config.format || 'A4',
        orientation: config.orientation || 'portrait',
        margins: config.margins || {
          top: 50,
          bottom: 50,
          left: 50,
          right: 50
        },
        includeCharts: config.includeCharts !== false,
        includeData: config.includeData !== false,
        ...config
      };

      // HTML içeriğini oluştur
      const htmlContent = await this.generateHTMLContent({
        data,
        title,
        type,
        config: pdfConfig
      });

      // PDF oluştur
      const { uri } = await Print.printToFileAsync({
        html: htmlContent,
        base64: false,
        ...pdfConfig
      });

      // Dosya ismini oluştur
      const fileName = this.generateFileName(title, type);
      const newUri = `${FileSystem.documentDirectory}${fileName}`;

      // Dosyayı doküman klasörüne kopyala
      await FileSystem.moveAsync({
        from: uri,
        to: newUri
      });

      // Dosyayı paylaş
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(newUri, {
          mimeType: 'application/pdf',
          dialogTitle: 'PDF Raporu Paylaş'
        });
      }

      return {
        success: true,
        uri: newUri,
        fileName
      };

    } catch (error) {
      console.error('PDF Generation Error:', error);
      return {
        success: false,
        error: error.message || 'PDF oluşturulurken bilinmeyen hata'
      };
    }
  }

  /**
   * HTML içeriğini oluşturur
   * @param {Object} params - HTML parametreleri
   * @returns {Promise<string>} HTML içeriği
   */
  static async generateHTMLContent({ data, title, type, config }) {
    const currentDate = new Date().toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    const styles = this.getHTMLStyles(config);
    const bodyContent = this.generateBodyContent({ data, type, config });

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="UTF-8">
          <title>${title}</title>
          <style>${styles}</style>
        </head>
        <body>
          <div class="container">
            <header class="header">
              <h1>${title}</h1>
              <p class="date">Oluşturulma Tarihi: ${currentDate}</p>
            </header>
            
            <main class="content">
              ${bodyContent}
            </main>
            
            <footer class="footer">
              <p>Maaş Takip Uygulaması - ${currentDate}</p>
            </footer>
          </div>
        </body>
      </html>
    `;
  }

  /**
   * HTML stilleri döndürür
   * @param {Object} config - PDF yapılandırması
   * @returns {string} CSS stilleri
   */
  static getHTMLStyles(config) {
    return `
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
        line-height: 1.6;
        color: #333;
        background-color: #fff;
      }
      
      .container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
      }
      
      .header {
        text-align: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid #e0e0e0;
      }
      
      .header h1 {
        font-size: 24px;
        color: #2c3e50;
        margin-bottom: 10px;
      }
      
      .date {
        font-size: 14px;
        color: #7f8c8d;
        font-style: italic;
      }
      
      .content {
        margin-bottom: 40px;
      }
      
      .section {
        margin-bottom: 25px;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e9ecef;
      }
      
      .section-title {
        font-size: 18px;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 15px;
        border-bottom: 1px solid #bdc3c7;
        padding-bottom: 5px;
      }
      
      .data-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 15px;
      }
      
      .data-table th,
      .data-table td {
        border: 1px solid #ddd;
        padding: 12px;
        text-align: left;
      }
      
      .data-table th {
        background-color: #3498db;
        color: white;
        font-weight: bold;
      }
      
      .data-table tr:nth-child(even) {
        background-color: #f2f2f2;
      }
      
      .data-table tr:hover {
        background-color: #e8f4f8;
      }
      
      .summary-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-top: 15px;
      }
      
      .summary-item {
        background-color: #fff;
        padding: 15px;
        border-radius: 6px;
        border: 1px solid #e0e0e0;
        text-align: center;
      }
      
      .summary-value {
        font-size: 18px;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 5px;
      }
      
      .summary-label {
        font-size: 14px;
        color: #7f8c8d;
      }
      
      .footer {
        text-align: center;
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #e0e0e0;
        font-size: 12px;
        color: #7f8c8d;
      }
      
      .chart-placeholder {
        width: 100%;
        height: 300px;
        background-color: #f8f9fa;
        border: 2px dashed #bdc3c7;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #7f8c8d;
        font-size: 14px;
        margin: 15px 0;
        border-radius: 6px;
      }
      
      .highlight-positive {
        color: #27ae60;
        font-weight: bold;
      }
      
      .highlight-negative {
        color: #e74c3c;
        font-weight: bold;
      }
      
      .highlight-neutral {
        color: #f39c12;
        font-weight: bold;
      }
    `;
  }

  /**
   * Rapor türüne göre HTML içeriğini oluşturur
   * @param {Object} params - İçerik parametreleri
   * @returns {string} HTML body içeriği
   */
  static generateBodyContent({ data, type, config }) {
    switch (type) {
      case 'monthly-income-expense':
        return this.generateMonthlyIncomeExpenseContent(data, config);
      case 'basic-summary':
        return this.generateBasicSummaryContent(data, config);
      case 'category-distribution':
        return this.generateCategoryDistributionContent(data, config);
      case 'cash-flow':
        return this.generateCashFlowContent(data, config);
      case 'budget-vs-actual':
        return this.generateBudgetVsActualContent(data, config);
      case 'transaction-list':
        return this.generateTransactionListContent(data, config);
      default:
        return this.generateGenericContent(data, config);
    }
  }

  /**
   * Aylık gelir-gider raporu içeriği
   */
  static generateMonthlyIncomeExpenseContent(data, config) {
    const { summary, monthlyData } = data;
    
    let content = `
      <div class="section">
        <h2 class="section-title">Aylık Gelir-Gider Özeti</h2>
        <div class="summary-grid">
          <div class="summary-item">
            <div class="summary-value highlight-positive">₺${summary.totalIncome?.toLocaleString('tr-TR') || '0'}</div>
            <div class="summary-label">Toplam Gelir</div>
          </div>
          <div class="summary-item">
            <div class="summary-value highlight-negative">₺${summary.totalExpense?.toLocaleString('tr-TR') || '0'}</div>
            <div class="summary-label">Toplam Gider</div>
          </div>
          <div class="summary-item">
            <div class="summary-value ${summary.netIncome >= 0 ? 'highlight-positive' : 'highlight-negative'}">
              ₺${summary.netIncome?.toLocaleString('tr-TR') || '0'}
            </div>
            <div class="summary-label">Net Gelir</div>
          </div>
        </div>
      </div>
    `;

    if (monthlyData && monthlyData.length > 0) {
      content += `
        <div class="section">
          <h2 class="section-title">Aylık Detaylar</h2>
          <table class="data-table">
            <thead>
              <tr>
                <th>Ay</th>
                <th>Gelir</th>
                <th>Gider</th>
                <th>Net</th>
              </tr>
            </thead>
            <tbody>
              ${monthlyData.map(item => `
                <tr>
                  <td>${item.month}</td>
                  <td class="highlight-positive">₺${item.income?.toLocaleString('tr-TR') || '0'}</td>
                  <td class="highlight-negative">₺${item.expense?.toLocaleString('tr-TR') || '0'}</td>
                  <td class="${item.net >= 0 ? 'highlight-positive' : 'highlight-negative'}">
                    ₺${item.net?.toLocaleString('tr-TR') || '0'}
                  </td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
      `;
    }

    return content;
  }

  /**
   * Temel özet raporu içeriği
   */
  static generateBasicSummaryContent(data, config) {
    const { summary } = data;
    
    return `
      <div class="section">
        <h2 class="section-title">Finansal Özet</h2>
        <div class="summary-grid">
          <div class="summary-item">
            <div class="summary-value highlight-positive">₺${summary.totalIncome?.toLocaleString('tr-TR') || '0'}</div>
            <div class="summary-label">Toplam Gelir</div>
          </div>
          <div class="summary-item">
            <div class="summary-value highlight-negative">₺${summary.totalExpense?.toLocaleString('tr-TR') || '0'}</div>
            <div class="summary-label">Toplam Gider</div>
          </div>
          <div class="summary-item">
            <div class="summary-value highlight-neutral">₺${summary.averageIncome?.toLocaleString('tr-TR') || '0'}</div>
            <div class="summary-label">Ortalama Gelir</div>
          </div>
          <div class="summary-item">
            <div class="summary-value highlight-neutral">₺${summary.averageExpense?.toLocaleString('tr-TR') || '0'}</div>
            <div class="summary-label">Ortalama Gider</div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Kategori dağılımı raporu içeriği
   */
  static generateCategoryDistributionContent(data, config) {
    const { categories } = data;
    
    if (!categories || categories.length === 0) {
      return '<div class="section"><p>Kategori verisi bulunamadı.</p></div>';
    }

    return `
      <div class="section">
        <h2 class="section-title">Kategori Dağılımı</h2>
        <table class="data-table">
          <thead>
            <tr>
              <th>Kategori</th>
              <th>Tutar</th>
              <th>Yüzde</th>
              <th>İşlem Sayısı</th>
            </tr>
          </thead>
          <tbody>
            ${categories.map(category => `
              <tr>
                <td>${category.name}</td>
                <td class="${category.type === 'income' ? 'highlight-positive' : 'highlight-negative'}">
                  ₺${category.amount?.toLocaleString('tr-TR') || '0'}
                </td>
                <td>${category.percentage?.toFixed(1) || '0'}%</td>
                <td>${category.count || 0}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>
    `;
  }

  /**
   * Nakit akış raporu içeriği
   */
  static generateCashFlowContent(data, config) {
    const { cashFlowData } = data;
    
    if (!cashFlowData || cashFlowData.length === 0) {
      return '<div class="section"><p>Nakit akış verisi bulunamadı.</p></div>';
    }

    return `
      <div class="section">
        <h2 class="section-title">Nakit Akış</h2>
        <table class="data-table">
          <thead>
            <tr>
              <th>Periode</th>
              <th>Başlangıç</th>
              <th>Gelir</th>
              <th>Gider</th>
              <th>Bitiş</th>
            </tr>
          </thead>
          <tbody>
            ${cashFlowData.map(item => `
              <tr>
                <td>${item.period}</td>
                <td>₺${item.startBalance?.toLocaleString('tr-TR') || '0'}</td>
                <td class="highlight-positive">₺${item.income?.toLocaleString('tr-TR') || '0'}</td>
                <td class="highlight-negative">₺${item.expense?.toLocaleString('tr-TR') || '0'}</td>
                <td class="${item.endBalance >= 0 ? 'highlight-positive' : 'highlight-negative'}">
                  ₺${item.endBalance?.toLocaleString('tr-TR') || '0'}
                </td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>
    `;
  }

  /**
   * Bütçe vs gerçekleşen raporu içeriği
   */
  static generateBudgetVsActualContent(data, config) {
    const { budgetData } = data;
    
    if (!budgetData || budgetData.length === 0) {
      return '<div class="section"><p>Bütçe verisi bulunamadı.</p></div>';
    }

    return `
      <div class="section">
        <h2 class="section-title">Bütçe vs Gerçekleşen</h2>
        <table class="data-table">
          <thead>
            <tr>
              <th>Kategori</th>
              <th>Bütçe</th>
              <th>Gerçekleşen</th>
              <th>Fark</th>
              <th>%</th>
            </tr>
          </thead>
          <tbody>
            ${budgetData.map(item => `
              <tr>
                <td>${item.category}</td>
                <td>₺${item.budget?.toLocaleString('tr-TR') || '0'}</td>
                <td>₺${item.actual?.toLocaleString('tr-TR') || '0'}</td>
                <td class="${item.difference >= 0 ? 'highlight-positive' : 'highlight-negative'}">
                  ₺${item.difference?.toLocaleString('tr-TR') || '0'}
                </td>
                <td>${item.percentage?.toFixed(1) || '0'}%</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>
    `;
  }

  /**
   * İşlem listesi raporu içeriği
   */
  static generateTransactionListContent(data, config) {
    const { transactions } = data;
    
    if (!transactions || transactions.length === 0) {
      return '<div class="section"><p>İşlem verisi bulunamadı.</p></div>';
    }

    return `
      <div class="section">
        <h2 class="section-title">İşlem Listesi</h2>
        <table class="data-table">
          <thead>
            <tr>
              <th>Tarih</th>
              <th>Açıklama</th>
              <th>Kategori</th>
              <th>Tutar</th>
              <th>Tip</th>
            </tr>
          </thead>
          <tbody>
            ${transactions.map(transaction => `
              <tr>
                <td>${transaction.date}</td>
                <td>${transaction.description}</td>
                <td>${transaction.category}</td>
                <td class="${transaction.type === 'income' ? 'highlight-positive' : 'highlight-negative'}">
                  ₺${transaction.amount?.toLocaleString('tr-TR') || '0'}
                </td>
                <td>${transaction.type === 'income' ? 'Gelir' : 'Gider'}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>
    `;
  }

  /**
   * Genel rapor içeriği
   */
  static generateGenericContent(data, config) {
    return `
      <div class="section">
        <h2 class="section-title">Rapor Verisi</h2>
        <pre style="background-color: #f8f9fa; padding: 15px; border-radius: 6px; font-size: 12px; overflow-x: auto;">
          ${JSON.stringify(data, null, 2)}
        </pre>
      </div>
    `;
  }

  /**
   * Dosya adı oluşturur
   * @param {string} title - Rapor başlığı
   * @param {string} type - Rapor türü
   * @returns {string} Dosya adı
   */
  static generateFileName(title, type) {
    const date = new Date();
    const dateStr = date.toISOString().split('T')[0];
    const timeStr = date.toTimeString().split(' ')[0].replace(/:/g, '-');
    
    const cleanTitle = title
      .replace(/[^a-zA-Z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .toLowerCase();
    
    return `${cleanTitle}-${dateStr}-${timeStr}.pdf`;
  }
}
