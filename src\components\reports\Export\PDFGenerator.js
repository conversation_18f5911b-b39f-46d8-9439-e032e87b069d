import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';
import { Platform, Alert } from 'react-native';

/**
 * PDF Generator - Profesyonel PDF raporları oluşturur
 */
export class PDFGenerator {

  /**
   * PDF raporu oluşturur
   * @param {Object} params - PDF parametreleri
   * @param {Object} params.data - Rapor verisi
   * @param {string} params.title - <PERSON><PERSON> ba<PERSON>ı
   * @param {string} params.type - <PERSON>or türü
   * @param {Object} params.config - PDF yapılandırması
   * @returns {Promise<Object>} PDF oluşturma sonucu
   */
  static async generatePDF({ data, title, type, config = {} }) {
    try {
      const htmlContent = this.generateHTMLContent(data, title, type, config);

      const { uri } = await Print.printToFileAsync({
        html: htmlContent,
        base64: false,
        width: 612,
        height: 792,
        margins: {
          left: 20,
          top: 20,
          right: 20,
          bottom: 20,
        },
      });

      // Share the PDF
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(uri, {
          mimeType: 'application/pdf',
          dialogTitle: 'PDF Raporu Paylaş',
        });
      }

      return {
        success: true,
        message: 'PDF başarıyla oluşturuldu',
        filePath: uri
      };
    } catch (error) {
      console.error('PDF generation error:', error);
      return {
        success: false,
        message: 'PDF oluşturulurken hata oluştu: ' + error.message,
        filePath: null
      };
    }
  }

  /**
   * HTML içeriği oluşturur
   */
  static generateHTMLContent(data, title, type, config) {
    const currentDate = new Date().toLocaleDateString('tr-TR');

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>${title}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .header { text-align: center; margin-bottom: 30px; }
          .title { font-size: 24px; font-weight: bold; color: #333; }
          .date { font-size: 14px; color: #666; margin-top: 10px; }
          .content { margin-top: 20px; }
          .section { margin-bottom: 20px; }
          .section-title { font-size: 18px; font-weight: bold; margin-bottom: 10px; }
          table { width: 100%; border-collapse: collapse; margin-top: 10px; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          th { background-color: #f2f2f2; }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="title">${title}</div>
          <div class="date">Rapor Tarihi: ${currentDate}</div>
        </div>
        <div class="content">
          ${this.generateContentHTML(data, type)}
        </div>
      </body>
      </html>
    `;
  }

  /**
   * İçerik HTML'i oluşturur
   */
  static generateContentHTML(data, type) {
    if (!data) return '<p>Veri bulunamadı</p>';

    let html = '';

    // Summary section
    if (data.summary) {
      html += '<div class="section">';
      html += '<div class="section-title">Özet</div>';
      html += `<p>Toplam Gelir: ${data.summary.totalIncome || 0} ₺</p>`;
      html += `<p>Toplam Gider: ${data.summary.totalExpense || 0} ₺</p>`;
      html += `<p>Net Gelir: ${data.summary.netIncome || 0} ₺</p>`;
      html += '</div>';
    }

    // Transactions table
    if (data.transactions && data.transactions.length > 0) {
      html += '<div class="section">';
      html += '<div class="section-title">İşlemler</div>';
      html += '<table>';
      html += '<tr><th>Tarih</th><th>Açıklama</th><th>Kategori</th><th>Tutar</th><th>Tür</th></tr>';

      data.transactions.slice(0, 50).forEach(transaction => {
        html += '<tr>';
        html += `<td>${new Date(transaction.date).toLocaleDateString('tr-TR')}</td>`;
        html += `<td>${transaction.description || '-'}</td>`;
        html += `<td>${transaction.category_name || '-'}</td>`;
        html += `<td>${transaction.amount} ₺</td>`;
        html += `<td>${transaction.type === 'income' ? 'Gelir' : 'Gider'}</td>`;
        html += '</tr>';
      });

      html += '</table>';

      if (data.transactions.length > 50) {
        html += `<p><em>Not: Yalnızca ilk 50 işlem gösterilmektedir. Toplam ${data.transactions.length} işlem bulunmaktadır.</em></p>`;
      }
      html += '</div>';
    }

    return html;
  }
}
