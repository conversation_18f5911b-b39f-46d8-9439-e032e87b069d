import React from 'react';
import { View, Text, TouchableOpacity, ScrollView, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../../../context/ThemeContext';

/**
 * <PERSON><PERSON>lo kontrolleri bileşeni - kaydetme, dışa aktarma ve diğer eylemler
 * @param {Object} props - Bileşen props'ları
 * @param {Function} props.onSave - Kaydetme fonksiyonu
 * @param {Function} props.onExport - Dışa aktarma fonksiyonu
 * @param {Function} props.onClear - Temizleme fonksiyonu
 * @param {Function} props.onPreview - Önizleme fonksiyonu
 * @param {Function} props.onUndo - Geri alma fonksiyonu
 * @param {Function} props.onRedo - Yineleme fonksiyonu
 * @param {Boolean} props.canUndo - Geri alma yapılabilir mi
 * @param {Boolean} props.canRedo - Yineleme yapılabilir mi
 * @param {Boolean} props.hasChanges - Değişiklik var mı
 * @param {Boolean} props.isLoading - Yükleme durumu
 * @param {Number} props.dataCount - Veri sayısı
 * @param {Array} props.exportFormats - Dışa aktarma formatları
 */
const TableControls = ({
  onSave,
  onExport,
  onClear,
  onPreview,
  onUndo,
  onRedo,
  canUndo = false,
  canRedo = false,
  hasChanges = false,
  isLoading = false,
  dataCount = 0,
  exportFormats = ['PDF', 'Excel', 'CSV']
}) => {
  const { theme } = useTheme();

  // Tema güvenlik kontrolü
  if (!theme) {
    console.error('TableControls: Theme objesi undefined!');
    return null;
  }

  const styles = {
    container: {
      backgroundColor: theme.SURFACE || theme.colors?.surface || '#f9f9f9',
      borderTopWidth: 1,
      borderTopColor: theme.BORDER || theme.colors?.border || '#e0e0e0',
      padding: 16,
    },
    topRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 12,
    },
    leftSection: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    rightSection: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    infoText: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginRight: 16,
    },
    statusIndicator: {
      flexDirection: 'row',
      alignItems: 'center',
      marginRight: 16,
    },
    statusDot: {
      width: 8,
      height: 8,
      borderRadius: 4,
      marginRight: 6,
    },
    statusDotSaved: {
      backgroundColor: theme.colors.success,
    },
    statusDotUnsaved: {
      backgroundColor: theme.colors.warning,
    },
    statusText: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    buttonsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    buttonGroup: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    button: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 12,
      paddingVertical: 8,
      borderRadius: 6,
      marginHorizontal: 4,
    },
    buttonPrimary: {
      backgroundColor: theme.colors.primary,
    },
    buttonSecondary: {
      backgroundColor: theme.colors.surfaceVariant,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    buttonDanger: {
      backgroundColor: theme.colors.error,
    },
    buttonDisabled: {
      opacity: 0.5,
    },
    buttonText: {
      fontSize: 12,
      fontWeight: '600',
      marginLeft: 4,
    },
    buttonTextPrimary: {
      color: theme.colors.onPrimary,
    },
    buttonTextSecondary: {
      color: theme.colors.text,
    },
    buttonTextDanger: {
      color: theme.colors.onError,
    },
    exportMenu: {
      position: 'absolute',
      bottom: 60,
      right: 16,
      backgroundColor: theme.colors.surface,
      borderRadius: 8,
      padding: 8,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 4,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    exportOption: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 12,
      paddingVertical: 8,
      borderRadius: 4,
      marginVertical: 2,
    },
    exportOptionText: {
      fontSize: 14,
      color: theme.colors.text,
      marginLeft: 8,
    },
    loadingContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 8,
    },
    loadingText: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginLeft: 8,
    },
  };

  const [showExportMenu, setShowExportMenu] = React.useState(false);

  /**
   * Kaydetme onayı
   */
  const handleSaveConfirm = () => {
    if (!hasChanges) {
      Alert.alert('Bilgi', 'Kaydedilecek değişiklik bulunmuyor');
      return;
    }

    Alert.alert(
      'Tabloyu Kaydet',
      'Mevcut tablo yapılandırmasını kaydetmek istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        { text: 'Kaydet', onPress: onSave }
      ]
    );
  };

  /**
   * Temizleme onayı
   */
  const handleClearConfirm = () => {
    Alert.alert(
      'Tabloyu Temizle',
      'Tüm yapılandırma ve verileri temizlemek istediğinizden emin misiniz? Bu işlem geri alınamaz.',
      [
        { text: 'İptal', style: 'cancel' },
        { text: 'Temizle', style: 'destructive', onPress: onClear }
      ]
    );
  };

  /**
   * Dışa aktarma format seçimi
   * @param {string} format - Seçilen format
   */
  const handleExportFormat = (format) => {
    setShowExportMenu(false);
    onExport(format);
  };

  /**
   * Format ikonu getir
   * @param {string} format - Format adı
   * @returns {string} İkon adı
   */
  const getFormatIcon = (format) => {
    switch (format.toLowerCase()) {
      case 'pdf':
        return 'document';
      case 'excel':
        return 'grid';
      case 'csv':
        return 'list';
      default:
        return 'download';
    }
  };

  if (isLoading) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <Ionicons name="hourglass" size={16} color={theme.colors.textSecondary} />
          <Text style={styles.loadingText}>İşleniyor...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.topRow}>
        <View style={styles.leftSection}>
          <Text style={styles.infoText}>
            {dataCount} kayıt
          </Text>
          <View style={styles.statusIndicator}>
            <View style={[
              styles.statusDot,
              hasChanges ? styles.statusDotUnsaved : styles.statusDotSaved
            ]} />
            <Text style={styles.statusText}>
              {hasChanges ? 'Kaydedilmemiş değişiklikler' : 'Kaydedildi'}
            </Text>
          </View>
        </View>
        <View style={styles.rightSection}>
          <TouchableOpacity
            style={[styles.button, styles.buttonSecondary, !canUndo && styles.buttonDisabled]}
            onPress={onUndo}
            disabled={!canUndo}
          >
            <Ionicons name="arrow-undo" size={16} color={theme.colors.text} />
            <Text style={[styles.buttonText, styles.buttonTextSecondary]}>Geri</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.button, styles.buttonSecondary, !canRedo && styles.buttonDisabled]}
            onPress={onRedo}
            disabled={!canRedo}
          >
            <Ionicons name="arrow-redo" size={16} color={theme.colors.text} />
            <Text style={[styles.buttonText, styles.buttonTextSecondary]}>İleri</Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.buttonsContainer}>
        <View style={styles.buttonGroup}>
          <TouchableOpacity
            style={[styles.button, styles.buttonSecondary]}
            onPress={onPreview}
          >
            <Ionicons name="eye" size={16} color={theme.colors.text} />
            <Text style={[styles.buttonText, styles.buttonTextSecondary]}>Önizleme</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.button, styles.buttonDanger]}
            onPress={handleClearConfirm}
          >
            <Ionicons name="trash" size={16} color={theme.colors.onError} />
            <Text style={[styles.buttonText, styles.buttonTextDanger]}>Temizle</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.buttonGroup}>
          <TouchableOpacity
            style={[styles.button, styles.buttonSecondary]}
            onPress={() => setShowExportMenu(!showExportMenu)}
          >
            <Ionicons name="download" size={16} color={theme.colors.text} />
            <Text style={[styles.buttonText, styles.buttonTextSecondary]}>Dışa Aktar</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.button, styles.buttonPrimary]}
            onPress={handleSaveConfirm}
          >
            <Ionicons name="save" size={16} color={theme.colors.onPrimary} />
            <Text style={[styles.buttonText, styles.buttonTextPrimary]}>Kaydet</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Dışa Aktarma Menüsü */}
      {showExportMenu && (
        <>
          <TouchableOpacity
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'transparent',
            }}
            onPress={() => setShowExportMenu(false)}
          />
          <View style={styles.exportMenu}>
            {exportFormats.map((format) => (
              <TouchableOpacity
                key={format}
                style={styles.exportOption}
                onPress={() => handleExportFormat(format)}
              >
                <Ionicons
                  name={getFormatIcon(format)}
                  size={16}
                  color={theme.colors.text}
                />
                <Text style={styles.exportOptionText}>{format}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </>
      )}
    </View>
  );
};

export default TableControls;
