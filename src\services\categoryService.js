/**
 * Kategori servisi
 */

/**
 * Tüm kategorileri getirir
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {string} type - Kategori tipi (income, expense, both, all)
 * @returns {Promise<Array>} Kategoriler listesi
 */
export const getCategories = async (db, type = 'all') => {
  try {
    let query = `SELECT * FROM categories`;
    
    if (type !== 'all') {
      if (type === 'both') {
        query += ` WHERE type = 'both'`;
      } else {
        query += ` WHERE type IN ('${type}', 'both')`;
      }
    }
    
    query += ` ORDER BY name ASC`;
    
    return await db.getAllAsync(query);
  } catch (error) {
    console.error('Kategorileri getirme hatası:', error);
    throw error;
  }
};

/**
 * Belirli bir kategoriyi getirir
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} id - Kategori ID
 * @returns {Promise<Object>} Kategori
 */
export const getCategory = async (db, id) => {
  try {
    return await db.getFirstAsync(`
      SELECT * FROM categories WHERE id = ?
    `, [id]);
  } catch (error) {
    console.error('Kategori getirme hatası:', error);
    throw error;
  }
};

/**
 * Yeni bir kategori ekler
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} category - Kategori verileri
 * @param {string} category.name - Kategori adı
 * @param {string} category.type - Kategori tipi (income, expense, both)
 * @param {string} category.color - Kategori rengi
 * @param {string} category.icon - Kategori ikonu
 * @returns {Promise<Object>} Ekleme sonucu
 */
export const addCategory = async (db, category) => {
  try {
    return await db.runAsync(`
      INSERT INTO categories (name, type, color, icon, is_default)
      VALUES (?, ?, ?, ?, ?)
    `, [
      category.name,
      category.type || 'expense',
      category.color || '#3498db',
      category.icon || 'category',
      category.is_default || 0
    ]);
  } catch (error) {
    console.error('Kategori ekleme hatası:', error);
    throw error;
  }
};

/**
 * Bir kategoriyi günceller
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} id - Kategori ID
 * @param {Object} category - Güncellenecek kategori verileri
 * @returns {Promise<Object>} Güncelleme sonucu
 */
export const updateCategory = async (db, id, category) => {
  try {
    return await db.runAsync(`
      UPDATE categories
      SET name = ?, type = ?, color = ?, icon = ?, is_default = ?
      WHERE id = ?
    `, [
      category.name,
      category.type || 'expense',
      category.color || '#3498db',
      category.icon || 'category',
      category.is_default || 0,
      id
    ]);
  } catch (error) {
    console.error('Kategori güncelleme hatası:', error);
    throw error;
  }
};

/**
 * Bir kategoriyi siler
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} id - Kategori ID
 * @returns {Promise<Object>} Silme sonucu
 */
export const deleteCategory = async (db, id) => {
  try {
    return await db.runAsync(`
      DELETE FROM categories WHERE id = ?
    `, [id]);
  } catch (error) {
    console.error('Kategori silme hatası:', error);
    throw error;
  }
};
