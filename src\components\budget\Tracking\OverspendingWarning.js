/**
 * Aşırı Harcama Uyarısı Komponenti
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md - Stage 3, Phase 3
 * 
 * Bütçe aşımı durumunda özel uyarı
 * Maksimum 150 satır - Tek sorumluluk prensibi
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../../../context/ThemeContext';
import { formatTRY, formatUSD, formatEUR } from '../../../utils/formatters';

/**
 * Aşırı harcama uyarısı komponenti
 * @param {Object} props - Component props
 * @param {number} props.overspentAmount - Aşılan miktar
 * @param {string} props.currency - Para birimi ('TRY', 'USD', 'EUR')
 * @param {string} props.categoryName - <PERSON><PERSON><PERSON> adı (opsiyonel)
 * @param {Function} props.onViewDetails - <PERSON><PERSON> g<PERSON>ü<PERSON>ü<PERSON> callback
 * @param {Function} props.onAdjustBudget - Bütçe ayarlama callback
 * @param {Object} props.theme - Tema objesi (opsiyonel, context'ten alınır)
 */
const OverspendingWarning = ({ 
  overspentAmount = 0,
  currency = 'TRY',
  categoryName,
  onViewDetails,
  onAdjustBudget,
  theme: propTheme 
}) => {
  const { theme, isDarkMode } = useTheme();
  const currentTheme = propTheme || theme;

  /**
   * Para birimi formatı
   * @param {number} value - Formatlanacak değer
   * @returns {string} Formatlanmış değer
   */
  const formatCurrency = (value) => {
    switch (currency) {
      case 'USD':
        return formatUSD(value);
      case 'EUR':
        return formatEUR(value);
      default:
        return formatTRY(value);
    }
  };

  /**
   * Para birimi sembolü
   * @returns {string} Para birimi sembolü
   */
  const getCurrencySymbol = () => {
    switch (currency) {
      case 'USD':
        return '$';
      case 'EUR':
        return '€';
      default:
        return '₺';
    }
  };

  if (overspentAmount <= 0) {
    return null;
  }

  const currencySymbol = getCurrencySymbol();

  return (
    <View style={[
      styles.container,
      {
        backgroundColor: currentTheme.ERROR + '10',
        borderColor: currentTheme.ERROR,
      }
    ]}>
      {/* Uyarı ikonu ve başlık */}
      <View style={styles.header}>
        <View style={[styles.warningIcon, { backgroundColor: currentTheme.ERROR }]}>
          <MaterialIcons name="warning" size={24} color={currentTheme.WHITE} />
        </View>
        
        <View style={styles.headerText}>
          <Text style={[styles.title, { color: currentTheme.ERROR }]}>
            Bütçe Aşıldı!
          </Text>
          <Text style={[styles.subtitle, { color: currentTheme.TEXT_SECONDARY }]}>
            {categoryName ? `${categoryName} kategorisinde` : 'Toplam bütçede'} aşım var
          </Text>
        </View>
      </View>

      {/* Aşım miktarı */}
      <View style={styles.amountSection}>
        <Text style={[styles.amountLabel, { color: currentTheme.TEXT_SECONDARY }]}>
          Aşılan Miktar:
        </Text>
        <View style={styles.amountDisplay}>
          <Text style={[styles.currencySymbol, { color: currentTheme.ERROR }]}>
            {currencySymbol}
          </Text>
          <Text style={[styles.amount, { color: currentTheme.ERROR }]}>
            {overspentAmount.toLocaleString('tr-TR')}
          </Text>
        </View>
        <Text style={[styles.formattedAmount, { color: currentTheme.TEXT_SECONDARY }]}>
          {formatCurrency(overspentAmount)}
        </Text>
      </View>

      {/* Aksiyon butonları */}
      <View style={styles.actions}>
        {onViewDetails && (
          <TouchableOpacity
            style={[
              styles.actionButton,
              styles.secondaryButton,
              { borderColor: currentTheme.ERROR }
            ]}
            onPress={onViewDetails}
          >
            <MaterialIcons name="visibility" size={16} color={currentTheme.ERROR} />
            <Text style={[styles.secondaryButtonText, { color: currentTheme.ERROR }]}>
              Detayları Gör
            </Text>
          </TouchableOpacity>
        )}

        {onAdjustBudget && (
          <TouchableOpacity
            style={[
              styles.actionButton,
              styles.primaryButton,
              { backgroundColor: currentTheme.ERROR }
            ]}
            onPress={onAdjustBudget}
          >
            <MaterialIcons name="tune" size={16} color={currentTheme.WHITE} />
            <Text style={[styles.primaryButtonText, { color: currentTheme.WHITE }]}>
              Bütçe Ayarla
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    borderWidth: 1,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 12,
  },
  warningIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerText: {
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  subtitle: {
    fontSize: 14,
  },
  amountSection: {
    alignItems: 'center',
    marginBottom: 16,
  },
  amountLabel: {
    fontSize: 14,
    marginBottom: 8,
  },
  amountDisplay: {
    flexDirection: 'row',
    alignItems: 'baseline',
    gap: 4,
    marginBottom: 4,
  },
  currencySymbol: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  amount: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  formattedAmount: {
    fontSize: 12,
  },
  actions: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 6,
  },
  primaryButton: {
    // backgroundColor set via style prop
  },
  secondaryButton: {
    borderWidth: 1,
  },
  primaryButtonText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  secondaryButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default OverspendingWarning;
