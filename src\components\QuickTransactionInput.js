import React, { useState, useEffect } from 'react';
import { View, TextInput, StyleSheet, TouchableOpacity, Text, Alert, Modal, FlatList, ScrollView } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useSQLiteContext } from 'expo-sqlite';
import { Colors } from '../constants/colors';
import { useExchangeRate } from '../context/ExchangeRateProvider';
import { useTheme } from '../context/ThemeContext';
import CategorySelectorSimple from './transaction/CategorySelectorSimple';
import CategoryForm from './category/CategoryForm';

/**
 * Hızlı işlem ekleme bileşeni
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {Function} props.onTransactionAdded - İşlem eklendiğinde çağrılacak fonksiyon
 * @returns {JSX.Element} Hızlı işlem ekleme bileşeni
 */
export default function QuickTransactionInput({ onTransactionAdded }) {
  const db = useSQLiteContext();
  const { theme, isDarkMode } = useTheme();

  // Döviz kuru context'ini kullanmaya çalış, hata durumunda null kullan
  let exchangeRateContext;
  try {
    exchangeRateContext = useExchangeRate();
  } catch (error) {
    console.warn('ExchangeRate context not available:', error);
    exchangeRateContext = null;
  }

  const [amount, setAmount] = useState('');
  const [description, setDescription] = useState('');
  const [transactionType, setTransactionType] = useState('expense'); // Varsayılan olarak gider
  const [showCurrencyModal, setShowCurrencyModal] = useState(false);
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showCategoryForm, setShowCategoryForm] = useState(false);
  const [selectedCurrency, setSelectedCurrency] = useState('TRY');
  const [preferredCurrency, setPreferredCurrency] = useState('USD'); // Varsayılan tercih edilen para birimi
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [categories, setCategories] = useState([]);
  const [transactionDate, setTransactionDate] = useState(new Date());
  const [categoryFormType, setCategoryFormType] = useState('expense');

  // Bileşen yüklendiğinde tercih edilen para birimini ayarla
  useEffect(() => {
    const loadPreferences = async () => {
      try {
        // Varsayılan para birimini ayarla
        if (exchangeRateContext) {
          // Eğer context'te preferredCurrency varsa onu kullan
          if (exchangeRateContext.preferredCurrency) {
            setPreferredCurrency(exchangeRateContext.preferredCurrency);
          }

          // Eğer context'te baseCurrency varsa, seçili para birimi olarak onu kullan
          if (exchangeRateContext.baseCurrency) {
            setSelectedCurrency(exchangeRateContext.baseCurrency);
          }
        }
      } catch (error) {
        console.error('Para birimi tercihleri yüklenirken hata:', error);
      }
    };

    loadPreferences();
  }, [exchangeRateContext]);

  // Kategorileri yükleme fonksiyonu
  const loadCategories = async () => {
    try {
      // İşlem tipine göre kategorileri getir
      const categoryType = transactionType === 'income' ? 'income' : 'expense';

      const result = await db.getAllAsync(`
        SELECT * FROM categories
        WHERE type = ? OR type = 'both'
        ORDER BY name ASC
      `, [categoryType]);

      setCategories(result);

      // Varsayılan kategoriyi seç
      const defaultCategory = result.find(cat => cat.is_default === 1);
      if (defaultCategory) {
        setSelectedCategory(defaultCategory.id);
      } else if (result.length > 0) {
        setSelectedCategory(result[0].id);
      }
    } catch (error) {
      console.error('Kategorileri yükleme hatası:', error);
    }
  };

  // Kategorileri yükle
  useEffect(() => {
    loadCategories();
  }, [transactionType, db]);

  /**
   * İşlemi ekler
   *
   * @returns {Promise<void>}
   */
  const addTransaction = async () => {
    try {
      // Boş değer kontrolü
      if (!amount || amount === '0') {
        Alert.alert('Hata', 'Lütfen geçerli bir tutar girin.');
        return;
      }

      // Tutarı sayıya çevir
      const numericAmount = parseFloat(amount.replace(/[^0-9.-]/g, ''));

      if (isNaN(numericAmount) || numericAmount === 0) {
        Alert.alert('Hata', 'Lütfen geçerli bir tutar girin.');
        return;
      }

      // Tutarın mutlak değerini al
      const absoluteAmount = Math.abs(numericAmount);

      // Kategori ID'si selectedCategory değişkeninde

      // İşlem verilerini hazırla
      const transactionData = {
        type: transactionType,
        amount: absoluteAmount,
        description: description || (transactionType === 'income' ? 'Gelir' : 'Gider'),
        date: transactionDate.toISOString().split('T')[0],
        category_id: selectedCategory,
        currency: selectedCurrency,
        preferred_currency: preferredCurrency
      };

      // Döviz kuru bilgilerini al ve dönüşüm yap
      if (exchangeRateContext && selectedCurrency !== preferredCurrency) {
        try {
          // Döviz kurunu al
          let exchangeRate;
          let convertedAmount;

          if (exchangeRateContext.rates && exchangeRateContext.rates[preferredCurrency]) {
            exchangeRate = exchangeRateContext.rates[preferredCurrency];
          }

          // Dönüşüm yap
          if (exchangeRate) {
            // exchangeRateContext'in yapısına göre uygun metodu kullan
            if (typeof exchangeRateContext.convertCurrency === 'function') {
              convertedAmount = exchangeRateContext.convertCurrency(
                absoluteAmount,
                selectedCurrency,
                preferredCurrency
              );
            } else if (typeof exchangeRateContext.convert === 'function') {
              convertedAmount = exchangeRateContext.convert(
                absoluteAmount,
                selectedCurrency,
                preferredCurrency
              );
            }

            if (convertedAmount) {
              transactionData.exchange_rate = exchangeRate;
              transactionData.converted_amount = convertedAmount;
            }
          }
        } catch (error) {
          console.error('Döviz kuru dönüşüm hatası:', error);
        }
      }

      // İşlemi ekle - sadece temel alanları kullan
      await db.runAsync(`
        INSERT INTO transactions (
          type, amount, description, date, category_id
        )
        VALUES (?, ?, ?, ?, ?)
      `, [
        transactionData.type,
        transactionData.amount,
        transactionData.description,
        transactionData.date,
        transactionData.category_id
      ]);

      // Alanları temizle
      setAmount('');
      setDescription('');

      // Başarı mesajı göster
      Alert.alert(
        'Başarılı',
        `${transactionType === 'income' ? 'Gelir' : 'Gider'} başarıyla eklendi.`,
        [{ text: 'Tamam', onPress: onTransactionAdded }]
      );
    } catch (error) {
      console.error('İşlem ekleme hatası:', error);
      Alert.alert('Hata', 'İşlem eklenirken bir hata oluştu.');
    }
  };

  // İşlem tipini değiştir
  const toggleTransactionType = () => {
    setTransactionType(prevType => prevType === 'income' ? 'expense' : 'income');
  };

  // Para birimi seçimi
  const toggleCurrencyModal = () => {
    setShowCurrencyModal(!showCurrencyModal);
  };

  // Para birimi seçme
  const selectCurrency = (currency) => {
    setSelectedCurrency(currency);
    setShowCurrencyModal(false);
  };

  // Tercih edilen para birimi seçme
  const selectPreferredCurrency = (currency) => {
    setPreferredCurrency(currency);
    setShowCurrencyModal(false);
  };

  // Kategori seçimi modalını aç/kapat
  const toggleCategoryModal = () => {
    setShowCategoryModal(!showCategoryModal);
  };

  // Kategori seç
  const selectCategory = (categoryId) => {
    setSelectedCategory(categoryId);
    setShowCategoryModal(false);
  };

  // Yeni kategori ekleme modalını aç
  const handleAddCategory = (type) => {
    setCategoryFormType(type || transactionType);
    setShowCategoryForm(true);
  };

  // Kategori kaydetme
  const handleSaveCategory = (newCategory) => {
    // Kategorileri yeniden yükle
    loadCategories();

    // Yeni kategoriyi seç
    if (newCategory && newCategory.id) {
      setSelectedCategory(newCategory.id);
    }
  };

  // Tarih seçimi modalını aç/kapat
  const toggleDatePicker = () => {
    setShowDatePicker(!showDatePicker);
  };

  // Tarih seç
  const onDateChange = (event, selectedDate) => {
    const currentDate = selectedDate || transactionDate;
    setShowDatePicker(false);
    setTransactionDate(currentDate);
  };

  // Tarih formatla
  const formatDate = (date) => {
    return date.toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Para birimi sembolü
  const getCurrencySymbol = (currencyCode) => {
    const symbols = {
      TRY: '₺',
      USD: '$',
      EUR: '€',
      GBP: '£',
      JPY: '¥'
    };

    return symbols[currencyCode] || currencyCode;
  };

  // Anlık döviz kuru dönüşümü
  const getConvertedAmount = () => {
    if (!amount || amount === '0' || !exchangeRateContext || selectedCurrency === preferredCurrency) {
      return null;
    }

    try {
      // Tutarı sayıya çevir
      const numericAmount = parseFloat(amount.replace(/[^0-9.-]/g, ''));

      if (isNaN(numericAmount) || numericAmount === 0) {
        return null;
      }

      // Basit döviz kuru hesaplama - rates'den direkt al
      if (exchangeRateContext.rates && exchangeRateContext.rates[preferredCurrency]) {
        const rate = exchangeRateContext.rates[preferredCurrency];

        if (selectedCurrency === 'TRY' && preferredCurrency !== 'TRY') {
          // TRY'den başka para birimine
          const convertedAmount = Math.abs(numericAmount) * rate;
          return exchangeRateContext.formatCurrency ?
            exchangeRateContext.formatCurrency(convertedAmount, preferredCurrency) :
            `${convertedAmount.toFixed(2)} ${preferredCurrency}`;
        } else if (selectedCurrency !== 'TRY' && preferredCurrency === 'TRY') {
          // Başka para biriminden TRY'ye
          const convertedAmount = Math.abs(numericAmount) / rate;
          return exchangeRateContext.formatCurrency ?
            exchangeRateContext.formatCurrency(convertedAmount, preferredCurrency) :
            `${convertedAmount.toFixed(2)} ${preferredCurrency}`;
        } else if (selectedCurrency !== 'TRY' && preferredCurrency !== 'TRY') {
          // İki farklı para birimi arasında (TRY üzerinden)
          const tryRates = exchangeRateContext.rates;
          if (tryRates[selectedCurrency] && tryRates[preferredCurrency]) {
            const amountInTRY = Math.abs(numericAmount) / tryRates[selectedCurrency];
            const convertedAmount = amountInTRY * tryRates[preferredCurrency];
            return exchangeRateContext.formatCurrency ?
              exchangeRateContext.formatCurrency(convertedAmount, preferredCurrency) :
              `${convertedAmount.toFixed(2)} ${preferredCurrency}`;
          }
        }
      }

      return null;
    } catch (error) {
      console.error('Anlık dönüşüm hatası:', error);
      return null;
    }
  };

  // Döviz kuru bilgisi
  const getExchangeRateInfo = () => {
    if (!exchangeRateContext || selectedCurrency === preferredCurrency) {
      return null;
    }

    try {
      let rate;

      // exchangeRateContext'in yapısına göre uygun şekilde kur bilgisini al
      if (exchangeRateContext.rates && exchangeRateContext.rates[preferredCurrency]) {
        rate = exchangeRateContext.rates[preferredCurrency];
      } else {
        return null;
      }

      if (rate) {
        return `1 ${selectedCurrency} = ${typeof rate === 'number' ? rate.toFixed(4) : rate} ${preferredCurrency}`;
      }
      return null;
    } catch (error) {
      console.error('Döviz kuru bilgisi hatası:', error);
      return null;
    }
  };

  return (
    <View style={[getStyles(theme).container, { backgroundColor: theme.SURFACE }]}>
      <View style={getStyles(theme).header}>
        <Text style={[getStyles(theme).title, { color: theme.TEXT_PRIMARY }]}>Hızlı İşlem</Text>
        <TouchableOpacity
          style={[
            getStyles(theme).typeToggle,
            { backgroundColor: transactionType === 'income' ? theme.SUCCESS : theme.DANGER }
          ]}
          onPress={toggleTransactionType}
        >
          <MaterialIcons
            name={transactionType === 'income' ? 'arrow-upward' : 'arrow-downward'}
            size={16}
            color="#fff"
          />
          <Text style={getStyles(theme).typeText}>
            {transactionType === 'income' ? 'Gelir' : 'Gider'}
          </Text>
        </TouchableOpacity>
      </View>

      <View style={getStyles(theme).inputContainer}>
        <View style={[getStyles(theme).amountContainer, { backgroundColor: theme.SURFACE_VARIANT, borderColor: theme.BORDER }]}>
          <TouchableOpacity
            style={[getStyles(theme).currencyButton, { backgroundColor: theme.BACKGROUND }]}
            onPress={toggleCurrencyModal}
          >
            <Text style={[getStyles(theme).currencySymbol, { color: theme.TEXT_PRIMARY }]}>{getCurrencySymbol(selectedCurrency)}</Text>
            <MaterialIcons name="arrow-drop-down" size={16} color={theme.TEXT_SECONDARY} />
          </TouchableOpacity>

          <TextInput
            style={[
              getStyles(theme).amountInput,
              { 
                color: transactionType === 'income' ? theme.SUCCESS : theme.DANGER,
                placeholderTextColor: theme.TEXT_SECONDARY
              }
            ]}
            value={amount}
            onChangeText={setAmount}
            placeholder="0.00"
            keyboardType="numeric"
            placeholderTextColor={theme.TEXT_SECONDARY}
          />

          <TouchableOpacity
            style={[getStyles(theme).preferredCurrencyButton, { backgroundColor: theme.BACKGROUND }]}
            onPress={toggleCurrencyModal}
          >
            <Text style={[getStyles(theme).preferredCurrencyText, { color: theme.TEXT_PRIMARY }]}>{preferredCurrency}</Text>
          </TouchableOpacity>
        </View>

        {/* Anlık döviz kuru dönüşümü */}
        {selectedCurrency !== preferredCurrency && amount && amount !== '0' && (
          <View style={[getStyles(theme).conversionContainer, { backgroundColor: theme.SURFACE_VARIANT }]}>
            <Text style={[getStyles(theme).conversionText, { color: theme.TEXT_PRIMARY }]}>
              {getConvertedAmount() || `${getCurrencySymbol(preferredCurrency)}0.00`}
            </Text>
            <Text style={[getStyles(theme).exchangeRateText, { color: theme.TEXT_SECONDARY }]}>
              {getExchangeRateInfo()}
            </Text>
          </View>
        )}

        <View style={[getStyles(theme).categoryContainer, { backgroundColor: theme.SURFACE_VARIANT }]}>
          <MaterialIcons name="category" size={20} color={theme.TEXT_SECONDARY} />
          <TouchableOpacity
            style={getStyles(theme).categorySelector}
            onPress={() => setShowCategoryModal(true)}
          >
            <Text style={[getStyles(theme).categorySelectorText, { color: theme.TEXT_PRIMARY }]}>
              {categories.find(c => c.id === selectedCategory)?.name || 'Kategori seçin'}
            </Text>
            <MaterialIcons name="arrow-drop-down" size={20} color={theme.TEXT_SECONDARY} />
          </TouchableOpacity>
          <TouchableOpacity
            style={[getStyles(theme).addCategoryButton, { backgroundColor: theme.BACKGROUND }]}
            onPress={() => handleAddCategory(transactionType)}
          >
            <MaterialIcons name="add" size={20} color={theme.PRIMARY} />
          </TouchableOpacity>
        </View>

        <View style={[getStyles(theme).descriptionContainer, { backgroundColor: theme.SURFACE_VARIANT }]}>
          <MaterialIcons name="description" size={20} color={theme.TEXT_SECONDARY} />
          <TextInput
            style={[getStyles(theme).descriptionInput, { color: theme.TEXT_PRIMARY }]}
            value={description}
            onChangeText={setDescription}
            placeholder="Açıklama (opsiyonel)"
            placeholderTextColor={theme.TEXT_SECONDARY}
          />
        </View>
      </View>

      <TouchableOpacity
        style={[
          getStyles(theme).addButton,
          { backgroundColor: transactionType === 'income' ? theme.SUCCESS : theme.DANGER }
        ]}
        onPress={addTransaction}
      >
        <MaterialIcons name="add" size={24} color="#fff" />
      </TouchableOpacity>

      {/* Para Birimi Seçme Modalı - Sade ve İşlevsel */}
      <Modal
        visible={showCurrencyModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowCurrencyModal(false)}
      >
        <TouchableOpacity 
          style={getStyles(theme).modalOverlay}
          activeOpacity={1}
          onPress={() => setShowCurrencyModal(false)}
        >
          <TouchableOpacity 
            style={[getStyles(theme).simpleCurrencyModal, { backgroundColor: theme.SURFACE }]}
            activeOpacity={1}
            onPress={() => {}}
          >
            {/* İşlem Para Birimi */}
            <View style={getStyles(theme).currencySection}>
              <Text style={[getStyles(theme).currencySectionTitle, { color: theme.TEXT_PRIMARY }]}>İşlem Para Birimi</Text>
              <View style={getStyles(theme).currencyRowOptions}>
                {[
                  { code: 'TRY', symbol: '₺' },
                  { code: 'USD', symbol: '$' },
                  { code: 'EUR', symbol: '€' },
                  { code: 'GBP', symbol: '£' }
                ].map((currency) => (
                  <TouchableOpacity
                    key={`tx_${currency.code}`}
                    style={[
                      getStyles(theme).currencyRowOption,
                      { 
                        backgroundColor: selectedCurrency === currency.code ? theme.PRIMARY : theme.SURFACE_VARIANT,
                        borderColor: selectedCurrency === currency.code ? theme.PRIMARY : theme.BORDER
                      }
                    ]}
                    onPress={() => selectCurrency(currency.code)}
                  >
                    <Text style={[
                      getStyles(theme).currencyRowSymbol,
                      { color: selectedCurrency === currency.code ? '#fff' : theme.TEXT_PRIMARY }
                    ]}>
                      {currency.symbol}
                    </Text>
                    <Text style={[
                      getStyles(theme).currencyRowCode,
                      { color: selectedCurrency === currency.code ? '#fff' : theme.TEXT_PRIMARY }
                    ]}>
                      {currency.code}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Tercih Edilen Para Birimi */}
            <View style={getStyles(theme).currencySection}>
              <Text style={[getStyles(theme).currencySectionTitle, { color: theme.TEXT_PRIMARY }]}>Tercih Edilen Para Birimi</Text>
              <View style={getStyles(theme).currencyRowOptions}>
                {[
                  { code: 'TRY', symbol: '₺' },
                  { code: 'USD', symbol: '$' },
                  { code: 'EUR', symbol: '€' },
                  { code: 'GBP', symbol: '£' }
                ].map((currency) => (
                  <TouchableOpacity
                    key={`pref_${currency.code}`}
                    style={[
                      getStyles(theme).currencyRowOption,
                      { 
                        backgroundColor: preferredCurrency === currency.code ? theme.SUCCESS : theme.SURFACE_VARIANT,
                        borderColor: preferredCurrency === currency.code ? theme.SUCCESS : theme.BORDER
                      }
                    ]}
                    onPress={() => selectPreferredCurrency(currency.code)}
                  >
                    <Text style={[
                      getStyles(theme).currencyRowSymbol,
                      { color: preferredCurrency === currency.code ? '#fff' : theme.TEXT_PRIMARY }
                    ]}>
                      {currency.symbol}
                    </Text>
                    <Text style={[
                      getStyles(theme).currencyRowCode,
                      { color: preferredCurrency === currency.code ? '#fff' : theme.TEXT_PRIMARY }
                    ]}>
                      {currency.code}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </TouchableOpacity>
        </TouchableOpacity>
      </Modal>

      {/* Kategori Seçim Modalı */}
      <Modal
        visible={showCategoryModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowCategoryModal(false)}
      >
        <View style={getStyles(theme).modalOverlay}>
          <View style={[getStyles(theme).modalContainer, { backgroundColor: theme.SURFACE }]}>
            <View style={[getStyles(theme).modalHeader, { borderBottomColor: theme.BORDER }]}>
              <Text style={[getStyles(theme).modalTitle, { color: theme.TEXT_PRIMARY }]}>Kategori Seçin</Text>
              <TouchableOpacity onPress={() => setShowCategoryModal(false)}>
                <MaterialIcons name="close" size={24} color={theme.TEXT_PRIMARY} />
              </TouchableOpacity>
            </View>

            <View style={getStyles(theme).modalSection}>
              <CategorySelectorSimple
                categories={categories}
                selectedCategoryId={selectedCategory}
                onSelectCategory={(categoryId) => {
                  setSelectedCategory(categoryId);
                  setShowCategoryModal(false);
                }}
                onAddCategory={handleAddCategory}
                type={transactionType}
              />
            </View>

            <TouchableOpacity
              style={[getStyles(theme).modalButton, { backgroundColor: theme.PRIMARY }]}
              onPress={() => setShowCategoryModal(false)}
            >
              <Text style={getStyles(theme).modalButtonText}>Tamam</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Kategori Ekleme/Düzenleme Modalı */}
      <CategoryForm
        visible={showCategoryForm}
        onClose={() => setShowCategoryForm(false)}
        category={null}
        type={categoryFormType}
        onSave={handleSaveCategory}
      />
    </View>
  );
}

const getStyles = (theme) => StyleSheet.create({
  container: {
    borderRadius: 20,
    padding: 20,
    marginBottom: 20,
    shadowColor: theme.SHADOW,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.12,
    shadowRadius: 12,
    elevation: 8,
    borderWidth: 1,
    borderColor: theme.BORDER,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    letterSpacing: 0.5,
  },
  typeToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 16,
    shadowColor: theme.SHADOW,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 4,
  },
  typeText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
    letterSpacing: 0.3,
  },
  inputContainer: {
    marginBottom: 16,
  },
  amountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 16,
    borderWidth: 1,
  },
  currencyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 12,
    marginRight: 12,
    shadowColor: theme.SHADOW,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  currencySymbol: {
    fontSize: 18,
    fontWeight: '700',
    marginRight: 4,
  },
  amountInput: {
    flex: 1,
    fontSize: 24,
    fontWeight: '700',
    letterSpacing: 0.5,
  },
  preferredCurrencyButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 12,
    shadowColor: theme.SHADOW,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  preferredCurrencyText: {
    fontSize: 14,
    fontWeight: '600',
    letterSpacing: 0.3,
  },
  descriptionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: theme.BORDER,
  },
  descriptionInput: {
    flex: 1,
    fontSize: 16,
    marginLeft: 12,
    fontWeight: '500',
  },
  addButton: {
    alignSelf: 'flex-end',
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 12,
    shadowColor: theme.SHADOW,
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.25,
    shadowRadius: 12,
    elevation: 8,
  },
  // Modal stilleri
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    padding: 24,
    maxHeight: '80%',
    shadowColor: theme.SHADOW,
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 10,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
    paddingBottom: 12,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 22,
    fontWeight: '700',
    letterSpacing: 0.5,
  },
  modalSection: {
    marginBottom: 24,
  },
  modalSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
    letterSpacing: 0.3,
  },
  currencyItem: {
    paddingHorizontal: 20,
    paddingVertical: 14,
    borderRadius: 16,
    marginRight: 12,
    marginBottom: 12,
    borderWidth: 1,
    shadowColor: theme.SHADOW,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  selectedCurrencyItem: {
    backgroundColor: theme.PRIMARY,
    borderColor: theme.PRIMARY,
    shadowColor: theme.PRIMARY,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  currencyItemText: {
    fontSize: 14,
    fontWeight: '500',
  },
  selectedCurrencyItemText: {
    fontWeight: '600',
  },
  modalButton: {
    borderRadius: 16,
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 12,
    shadowColor: theme.PRIMARY,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  modalButtonText: {
    color: theme.ON_PRIMARY,
    fontSize: 16,
    fontWeight: '700',
    letterSpacing: 0.5,
  },
  currencyItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  currencySymbolText: {
    fontSize: 16,
    fontWeight: '700',
    marginRight: 10,
  },
  conversionContainer: {
    marginBottom: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.BORDER,
    alignItems: 'flex-end',
  },
  conversionText: {
    fontSize: 16,
    fontWeight: '600',
    letterSpacing: 0.3,
  },
  exchangeRateText: {
    fontSize: 12,
    marginTop: 4,
    fontWeight: '500',
  },
  categoryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: theme.BORDER,
  },
  categorySelector: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginLeft: 12,
    paddingVertical: 4,
  },
  categorySelectorText: {
    fontSize: 16,
    fontWeight: '500',
  },
  addCategoryButton: {
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 18,
    marginLeft: 12,
    shadowColor: theme.SHADOW,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  descriptionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: theme.BORDER,
  },
  descriptionInput: {
    flex: 1,
    fontSize: 16,
    marginLeft: 12,
    fontWeight: '500',
  },
  // Sade Currency Modal Styles
  simpleCurrencyModal: {
    width: '90%',
    maxWidth: 400,
    backgroundColor: theme.SURFACE,
    borderRadius: 20,
    padding: 24,
    shadowColor: theme.SHADOW,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 8,
  },
  currencySection: {
    marginBottom: 24,
  },
  currencySectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    textAlign: 'center',
  },
  currencyRowOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 8,
  },
  currencyRowOption: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 12,
    borderWidth: 1,
    minHeight: 60,
    justifyContent: 'center',
  },
  currencyRowSymbol: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
  },
  currencyRowCode: {
    fontSize: 12,
    fontWeight: '600',
  },
  // Modern Currency Modal Styles
  modernModalContainer: {
    width: '95%',
    maxHeight: '85%',
    borderRadius: 24,
    overflow: 'hidden',
    shadowColor: theme.SHADOW,
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 15,
  },
  modernModalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  headerIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(108, 92, 231, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  headerTextContainer: {
    flex: 1,
  },
  modernModalTitle: {
    fontSize: 20,
    fontWeight: '700',
    letterSpacing: 0.3,
  },
  modernModalSubtitle: {
    fontSize: 14,
    marginTop: 2,
    fontWeight: '500',
  },
  modernCloseButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modernModalContent: {
    flex: 1,
    paddingHorizontal: 20,
  },
  modernSection: {
    marginVertical: 16,
  },
  sectionHeaderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  modernSectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
    letterSpacing: 0.3,
  },
  sectionDescription: {
    fontSize: 14,
    marginBottom: 16,
    lineHeight: 20,
    fontWeight: '500',
  },
  currencyGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  modernCurrencyCard: {
    width: '48%',
    aspectRatio: 1.2,
    borderRadius: 16,
    borderWidth: 2,
    padding: 16,
    marginBottom: 12,
    justifyContent: 'space-between',
    shadowColor: theme.SHADOW,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 4,
  },
  currencyCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  currencyFlag: {
    fontSize: 18,
    marginBottom: 4,
  },
  currencySymbolContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modernCurrencySymbol: {
    fontSize: 18,
    fontWeight: '700',
  },
  selectedBadge: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: 'rgba(255,255,255,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modernCurrencyCode: {
    fontSize: 16,
    fontWeight: '700',
    letterSpacing: 0.5,
    marginTop: 8,
  },
  modernCurrencyName: {
    fontSize: 12,
    fontWeight: '500',
    lineHeight: 16,
  },
  exchangeRateInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 12,
    borderWidth: 1,
    marginTop: 16,
  },
  exchangeRateText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
    flex: 1,
  },
  modernModalFooter: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
  },
  modernModalButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 16,
    shadowColor: theme.PRIMARY,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  modernModalButtonText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#fff',
    marginLeft: 8,
    letterSpacing: 0.5,
  },
});
