import React, { useState } from 'react';
import { 
  View, 
  Text, 
  TouchableOpacity, 
  StyleSheet, 
  Modal, 
  ScrollView, 
  Alert 
} from 'react-native';
import { useDataIntegration } from '../../../context/DataIntegrationContext';

/**
 * Data Source Switcher Component
 * Allows users to switch between real and mock data sources
 */
const DataSourceSwitcher = ({ isVisible, onClose, theme }) => {
  const {
    dataSource,
    setDataSource,
    hasRealData,
    dataFreshness,
    refreshDataFreshness,
    loading
  } = useDataIntegration();

  const [refreshing, setRefreshing] = useState(false);

  /**
   * Get safe theme value
   * @param {string} property - Theme property
   * @param {string} fallback - Fallback value
   * @returns {string} Safe theme value
   */
  const getSafeThemeValue = (property, fallback) => {
    return theme?.[property] || theme?.colors?.[property.toLowerCase()] || fallback;
  };

  /**
   * Handle data source change
   * @param {string} newSource - New data source
   */
  const handleDataSourceChange = (newSource) => {
    if (newSource === 'real' && !hasRealData) {
      Alert.alert(
        'Veri Bulunamadı',
        'Henüz gerçek finansal verileriniz bulunmuyor. Önce işlem ekleyerek başlayabilirsiniz.',
        [{ text: 'Tamam', style: 'default' }]
      );
      return;
    }
    
    setDataSource(newSource);
    Alert.alert(
      'Veri Kaynağı Değiştirildi',
      `Raporlar artık ${getDataSourceLabel(newSource)} kullanacak.`,
      [{ text: 'Tamam', style: 'default' }]
    );
  };

  /**
   * Get data source label
   * @param {string} source - Data source
   * @returns {string} Label
   */
  const getDataSourceLabel = (source) => {
    switch (source) {
      case 'real':
        return 'gerçek verilerinizi';
      case 'mock':
        return 'örnek verileri';
      case 'auto':
        return 'otomatik seçimi';
      default:
        return 'bilinmeyen veri kaynağı';
    }
  };

  /**
   * Get data freshness indicator
   * @returns {Object} Freshness indicator
   */
  const getFreshnessIndicator = () => {
    if (!dataFreshness) {
      return { color: '#6c757d', text: 'Bilinmiyor', icon: '❓' };
    }

    switch (dataFreshness.freshness) {
      case 'no_data':
        return { color: '#6c757d', text: 'Veri Yok', icon: '❌' };
      case 'very_fresh':
        return { color: '#28a745', text: 'Çok Taze', icon: '✅' };
      case 'fresh':
        return { color: '#28a745', text: 'Taze', icon: '🟢' };
      case 'moderate':
        return { color: '#ffc107', text: 'Orta', icon: '🟡' };
      case 'stale':
        return { color: '#dc3545', text: 'Eski', icon: '🔴' };
      default:
        return { color: '#6c757d', text: 'Bilinmiyor', icon: '❓' };
    }
  };

  /**
   * Handle refresh data freshness
   */
  const handleRefreshFreshness = async () => {
    setRefreshing(true);
    try {
      await refreshDataFreshness();
    } catch (error) {
      Alert.alert('Hata', 'Veri tazeliği güncellenemedi.');
    } finally {
      setRefreshing(false);
    }
  };

  const freshnessIndicator = getFreshnessIndicator();

  return (
    <Modal
      visible={isVisible}
      animationType="slide"
      transparent={false}
      onRequestClose={onClose}
    >
      <View style={[styles.container, { backgroundColor: getSafeThemeValue('BACKGROUND', '#ffffff') }]}>
        <View style={[styles.header, { backgroundColor: getSafeThemeValue('SURFACE', '#f9f9f9') }]}>
          <Text style={[styles.title, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
            🔄 Veri Kaynağı Ayarları
          </Text>
          <TouchableOpacity onPress={onClose}>
            <Text style={[styles.closeButton, { color: getSafeThemeValue('PRIMARY', '#007AFF') }]}>
              ✕
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Current Data Source */}
          <View style={[styles.section, { backgroundColor: getSafeThemeValue('SURFACE', '#f9f9f9') }]}>
            <Text style={[styles.sectionTitle, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
              📊 Mevcut Veri Kaynağı
            </Text>
            <View style={[styles.currentSource, { backgroundColor: getSafeThemeValue('BACKGROUND', '#ffffff') }]}>
              <Text style={[styles.currentSourceText, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
                {getDataSourceLabel(dataSource).charAt(0).toUpperCase() + getDataSourceLabel(dataSource).slice(1)}
              </Text>
              <View style={[styles.currentSourceBadge, { backgroundColor: getSafeThemeValue('PRIMARY', '#007AFF') }]}>
                <Text style={styles.currentSourceBadgeText}>Aktif</Text>
              </View>
            </View>
          </View>

          {/* Real Data Status */}
          <View style={[styles.section, { backgroundColor: getSafeThemeValue('SURFACE', '#f9f9f9') }]}>
            <Text style={[styles.sectionTitle, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
              💾 Gerçek Veri Durumu
            </Text>
            
            <View style={[styles.statusCard, { backgroundColor: getSafeThemeValue('BACKGROUND', '#ffffff') }]}>
              <View style={styles.statusRow}>
                <Text style={[styles.statusLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
                  Veri Varlığı:
                </Text>
                <Text style={[styles.statusValue, { color: hasRealData ? '#28a745' : '#dc3545' }]}>
                  {hasRealData ? '✅ Mevcut' : '❌ Yok'}
                </Text>
              </View>
              
              {hasRealData && dataFreshness && (
                <>
                  <View style={styles.statusRow}>
                    <Text style={[styles.statusLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
                      Toplam İşlem:
                    </Text>
                    <Text style={[styles.statusValue, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
                      {dataFreshness.totalTransactions}
                    </Text>
                  </View>
                  
                  <View style={styles.statusRow}>
                    <Text style={[styles.statusLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
                      Son İşlem:
                    </Text>
                    <Text style={[styles.statusValue, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
                      {dataFreshness.latestTransactionDate || 'Bilinmiyor'}
                    </Text>
                  </View>
                  
                  <View style={styles.statusRow}>
                    <Text style={[styles.statusLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
                      Veri Tazeliği:
                    </Text>
                    <View style={styles.freshnessContainer}>
                      <Text style={styles.freshnessIcon}>{freshnessIndicator.icon}</Text>
                      <Text style={[styles.freshnessText, { color: freshnessIndicator.color }]}>
                        {freshnessIndicator.text}
                      </Text>
                    </View>
                  </View>
                </>
              )}
              
              <TouchableOpacity
                style={[styles.refreshButton, { backgroundColor: getSafeThemeValue('SECONDARY', '#6c757d') }]}
                onPress={handleRefreshFreshness}
                disabled={refreshing}
              >
                <Text style={[styles.refreshButtonText, { color: '#ffffff' }]}>
                  {refreshing ? '🔄 Yenileniyor...' : '🔄 Durumu Yenile'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Data Source Options */}
          <View style={[styles.section, { backgroundColor: getSafeThemeValue('SURFACE', '#f9f9f9') }]}>
            <Text style={[styles.sectionTitle, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
              ⚙️ Veri Kaynağı Seçenekleri
            </Text>
            
            {/* Auto Mode */}
            <TouchableOpacity
              style={[
                styles.optionCard,
                { 
                  backgroundColor: getSafeThemeValue('BACKGROUND', '#ffffff'),
                  borderColor: dataSource === 'auto' ? getSafeThemeValue('PRIMARY', '#007AFF') : getSafeThemeValue('BORDER', '#e0e0e0')
                }
              ]}
              onPress={() => handleDataSourceChange('auto')}
            >
              <View style={styles.optionHeader}>
                <Text style={styles.optionIcon}>🤖</Text>
                <Text style={[styles.optionTitle, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
                  Otomatik Seçim
                </Text>
                {dataSource === 'auto' && (
                  <View style={[styles.selectedBadge, { backgroundColor: getSafeThemeValue('PRIMARY', '#007AFF') }]}>
                    <Text style={styles.selectedBadgeText}>✓</Text>
                  </View>
                )}
              </View>
              <Text style={[styles.optionDescription, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
                Gerçek veriler varsa onları kullan, yoksa örnek verileri göster.
              </Text>
            </TouchableOpacity>

            {/* Real Data Mode */}
            <TouchableOpacity
              style={[
                styles.optionCard,
                { 
                  backgroundColor: getSafeThemeValue('BACKGROUND', '#ffffff'),
                  borderColor: dataSource === 'real' ? getSafeThemeValue('PRIMARY', '#007AFF') : getSafeThemeValue('BORDER', '#e0e0e0'),
                  opacity: hasRealData ? 1 : 0.5
                }
              ]}
              onPress={() => handleDataSourceChange('real')}
              disabled={!hasRealData}
            >
              <View style={styles.optionHeader}>
                <Text style={styles.optionIcon}>💾</Text>
                <Text style={[styles.optionTitle, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
                  Gerçek Veriler
                </Text>
                {dataSource === 'real' && (
                  <View style={[styles.selectedBadge, { backgroundColor: getSafeThemeValue('PRIMARY', '#007AFF') }]}>
                    <Text style={styles.selectedBadgeText}>✓</Text>
                  </View>
                )}
              </View>
              <Text style={[styles.optionDescription, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
                Sadece gerçek finansal verilerinizi kullan.
                {!hasRealData && ' (Şu anda veri yok)'}
              </Text>
            </TouchableOpacity>

            {/* Mock Data Mode */}
            <TouchableOpacity
              style={[
                styles.optionCard,
                { 
                  backgroundColor: getSafeThemeValue('BACKGROUND', '#ffffff'),
                  borderColor: dataSource === 'mock' ? getSafeThemeValue('PRIMARY', '#007AFF') : getSafeThemeValue('BORDER', '#e0e0e0')
                }
              ]}
              onPress={() => handleDataSourceChange('mock')}
            >
              <View style={styles.optionHeader}>
                <Text style={styles.optionIcon}>🎭</Text>
                <Text style={[styles.optionTitle, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
                  Örnek Veriler
                </Text>
                {dataSource === 'mock' && (
                  <View style={[styles.selectedBadge, { backgroundColor: getSafeThemeValue('PRIMARY', '#007AFF') }]}>
                    <Text style={styles.selectedBadgeText}>✓</Text>
                  </View>
                )}
              </View>
              <Text style={[styles.optionDescription, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
                Örnek verilerle rapor özelliklerini test et.
              </Text>
            </TouchableOpacity>
          </View>

          {/* Info Section */}
          <View style={[styles.section, { backgroundColor: getSafeThemeValue('SURFACE', '#f9f9f9') }]}>
            <Text style={[styles.sectionTitle, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
              ℹ️ Bilgi
            </Text>
            <View style={[styles.infoCard, { backgroundColor: getSafeThemeValue('BACKGROUND', '#ffffff') }]}>
              <Text style={[styles.infoText, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
                • Otomatik mod, veri durumunuza göre en uygun seçeneği kullanır
                • Gerçek veri modu sadece kendi verilerinizi gösterir
                • Örnek veri modu özellik keşfi için idealdir
                • Veri kaynağını istediğiniz zaman değiştirebilirsiniz
              </Text>
            </View>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  closeButton: {
    fontSize: 24,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 16,
    borderRadius: 8,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  currentSource: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  currentSourceText: {
    fontSize: 16,
    fontWeight: '500',
  },
  currentSourceBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  currentSourceBadgeText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: '600',
  },
  statusCard: {
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  statusValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  freshnessContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  freshnessIcon: {
    fontSize: 16,
    marginRight: 4,
  },
  freshnessText: {
    fontSize: 14,
    fontWeight: '600',
  },
  refreshButton: {
    marginTop: 12,
    padding: 10,
    borderRadius: 6,
    alignItems: 'center',
  },
  refreshButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  optionCard: {
    marginBottom: 12,
    padding: 16,
    borderRadius: 8,
    borderWidth: 2,
  },
  optionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  optionIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  selectedBadge: {
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectedBadgeText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: '600',
  },
  optionDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  infoCard: {
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  infoText: {
    fontSize: 14,
    lineHeight: 20,
  },
});

export default DataSourceSwitcher;
