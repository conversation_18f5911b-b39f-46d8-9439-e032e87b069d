/**
 * Pivot Tablo Servisi
 * Veri gruplandırma, toplama ve pivot tablo hesaplamaları
 */

/**
 * Pivot tablo verisi oluştur
 * @param {Array} data - Ham veri
 * @param {Object} config - Pivot yapılandırması
 * @returns {Object} Pivot tablo verisi
 */
export const generatePivotTable = (data, config) => {
  if (!data || data.length === 0) {
    return {
      headers: [],
      rows: [],
      summary: {},
      metadata: {
        totalRows: 0,
        totalColumns: 0,
        aggregations: {}
      }
    };
  }

  const { rows, columns, values, aggregations, filters } = config;

  // Filtreleri uygula
  let filteredData = applyFilters(data, filters);

  // Pivot hesaplamaları
  const pivotData = calculatePivotData(filteredData, { rows, columns, values, aggregations });

  return {
    headers: pivotData.headers,
    rows: pivotData.rows,
    summary: pivotData.summary,
    metadata: {
      totalRows: pivotData.rows.length,
      totalColumns: pivotData.headers.length,
      aggregations: aggregations,
      originalDataCount: data.length,
      filteredDataCount: filteredData.length
    }
  };
};

/**
 * Filtreleri uygula
 * @param {Array} data - Ham veri
 * @param {Object} filters - Filtre yapılandırması
 * @returns {Array} Filtrelenmiş veri
 */
const applyFilters = (data, filters) => {
  if (!filters || Object.keys(filters).length === 0) {
    return data;
  }

  return data.filter(row => {
    return Object.entries(filters).every(([field, filterConfig]) => {
      const value = row[field];
      
      switch (filterConfig.type) {
        case 'equals':
          return value === filterConfig.value;
        case 'contains':
          return String(value).toLowerCase().includes(String(filterConfig.value).toLowerCase());
        case 'range':
          return value >= filterConfig.min && value <= filterConfig.max;
        case 'in':
          return filterConfig.values.includes(value);
        case 'not_null':
          return value != null && value !== '';
        default:
          return true;
      }
    });
  });
};

/**
 * Pivot veri hesaplamaları
 * @param {Array} data - Filtrelenmiş veri
 * @param {Object} config - Pivot yapılandırması
 * @returns {Object} Hesaplanmış pivot verisi
 */
const calculatePivotData = (data, config) => {
  const { rows, columns, values, aggregations } = config;

  // Satır grupları oluştur
  const rowGroups = groupByFields(data, rows);
  
  // Sütun grupları oluştur
  const columnGroups = groupByFields(data, columns);
  
  // Headers oluştur
  const headers = buildHeaders(rows, columns, columnGroups, values);
  
  // Satırları oluştur
  const pivotRows = buildRows(rowGroups, columnGroups, values, aggregations, data);
  
  // Özet hesaplamaları
  const summary = calculateSummary(data, values, aggregations);

  return {
    headers,
    rows: pivotRows,
    summary
  };
};

/**
 * Alanlara göre gruplandırma
 * @param {Array} data - Veri
 * @param {Array} fields - Gruplandırma alanları
 * @returns {Map} Gruplandırılmış veri
 */
const groupByFields = (data, fields) => {
  if (!fields || fields.length === 0) {
    return new Map([['', data]]);
  }

  const groups = new Map();

  data.forEach(row => {
    const key = fields.map(field => row[field.name] || '').join('|');
    
    if (!groups.has(key)) {
      groups.set(key, []);
    }
    groups.get(key).push(row);
  });

  return groups;
};

/**
 * Başlıkları oluştur
 * @param {Array} rowFields - Satır alanları
 * @param {Array} columnFields - Sütun alanları
 * @param {Map} columnGroups - Sütun grupları
 * @param {Array} valueFields - Değer alanları
 * @returns {Array} Başlık dizisi
 */
const buildHeaders = (rowFields, columnFields, columnGroups, valueFields) => {
  const headers = [];

  // Satır alanları başlıkları
  rowFields.forEach(field => {
    headers.push({
      key: field.name,
      title: field.displayName,
      type: 'row_header',
      field: field
    });
  });

  // Sütun grupları için başlıklar
  if (columnFields.length === 0) {
    // Sütun grubu yoksa, sadece değer alanları
    valueFields.forEach(field => {
      headers.push({
        key: field.name,
        title: field.displayName,
        type: 'value',
        field: field
      });
    });
  } else {
    // Sütun grupları varsa, her grup için değer alanları
    Array.from(columnGroups.keys()).forEach(columnKey => {
      valueFields.forEach(field => {
        headers.push({
          key: `${columnKey}|${field.name}`,
          title: `${columnKey} - ${field.displayName}`,
          type: 'value',
          field: field,
          columnGroup: columnKey
        });
      });
    });
  }

  return headers;
};

/**
 * Satırları oluştur
 * @param {Map} rowGroups - Satır grupları
 * @param {Map} columnGroups - Sütun grupları
 * @param {Array} valueFields - Değer alanları
 * @param {Object} aggregations - Toplama fonksiyonları
 * @param {Array} allData - Tüm veri
 * @returns {Array} Satır dizisi
 */
const buildRows = (rowGroups, columnGroups, valueFields, aggregations, allData) => {
  const rows = [];

  Array.from(rowGroups.entries()).forEach(([rowKey, rowData]) => {
    const row = {};

    // Satır alanları değerleri
    const rowKeyParts = rowKey.split('|');
    rowKeyParts.forEach((part, index) => {
      row[`row_${index}`] = part;
    });

    // Değer hesaplamaları
    if (columnGroups.size <= 1) {
      // Sütun grubu yoksa
      valueFields.forEach(field => {
        const aggregationType = aggregations[field.name] || 'SUM';
        row[field.name] = calculateAggregation(rowData, field.name, aggregationType);
      });
    } else {
      // Sütun grupları varsa
      Array.from(columnGroups.entries()).forEach(([columnKey, columnData]) => {
        // Bu satır ve sütun kesişimindeki veriyi bul
        const intersectionData = rowData.filter(item => 
          columnData.some(colItem => 
            Object.keys(colItem).every(key => colItem[key] === item[key])
          )
        );

        valueFields.forEach(field => {
          const aggregationType = aggregations[field.name] || 'SUM';
          const key = `${columnKey}|${field.name}`;
          row[key] = calculateAggregation(intersectionData, field.name, aggregationType);
        });
      });
    }

    rows.push(row);
  });

  return rows;
};

/**
 * Toplama hesaplaması
 * @param {Array} data - Veri
 * @param {string} fieldName - Alan adı
 * @param {string} aggregationType - Toplama tipi
 * @returns {number} Hesaplanan değer
 */
const calculateAggregation = (data, fieldName, aggregationType) => {
  if (!data || data.length === 0) {
    return 0;
  }

  const values = data
    .map(item => item[fieldName])
    .filter(value => value != null && !isNaN(value))
    .map(value => Number(value));

  if (values.length === 0) {
    return 0;
  }

  switch (aggregationType.toUpperCase()) {
    case 'SUM':
      return values.reduce((sum, value) => sum + value, 0);
    case 'AVG':
      return values.reduce((sum, value) => sum + value, 0) / values.length;
    case 'COUNT':
      return values.length;
    case 'MIN':
      return Math.min(...values);
    case 'MAX':
      return Math.max(...values);
    case 'MEDIAN':
      const sorted = values.sort((a, b) => a - b);
      const mid = Math.floor(sorted.length / 2);
      return sorted.length % 2 === 0 
        ? (sorted[mid - 1] + sorted[mid]) / 2 
        : sorted[mid];
    default:
      return values.reduce((sum, value) => sum + value, 0);
  }
};

/**
 * Özet hesaplamaları
 * @param {Array} data - Veri
 * @param {Array} valueFields - Değer alanları
 * @param {Object} aggregations - Toplama fonksiyonları
 * @returns {Object} Özet verisi
 */
const calculateSummary = (data, valueFields, aggregations) => {
  const summary = {};

  valueFields.forEach(field => {
    const aggregationType = aggregations[field.name] || 'SUM';
    summary[field.name] = {
      value: calculateAggregation(data, field.name, aggregationType),
      type: aggregationType,
      field: field.displayName
    };
  });

  return summary;
};

/**
 * Pivot tabloyu CSV formatına çevir
 * @param {Object} pivotData - Pivot tablo verisi
 * @returns {string} CSV string
 */
export const pivotTableToCSV = (pivotData) => {
  const { headers, rows } = pivotData;
  
  // CSV başlıkları
  const csvHeaders = headers.map(header => `"${header.title}"`).join(',');
  
  // CSV satırları
  const csvRows = rows.map(row => {
    return headers.map(header => {
      const value = row[header.key] || '';
      return `"${value}"`;
    }).join(',');
  });
  
  return [csvHeaders, ...csvRows].join('\n');
};

/**
 * Pivot tablo yapılandırmasını doğrula
 * @param {Object} config - Pivot yapılandırması
 * @returns {Object} Doğrulama sonucu
 */
export const validatePivotConfig = (config) => {
  const errors = [];
  const warnings = [];

  if (!config.rows || config.rows.length === 0) {
    warnings.push('En az bir satır alanı seçmeniz önerilir');
  }

  if (!config.values || config.values.length === 0) {
    errors.push('En az bir değer alanı seçmelisiniz');
  }

  if (config.rows.length + config.columns.length > 10) {
    warnings.push('Çok fazla alan seçimi performansı etkileyebilir');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Pivot tablo verilerini Excel formatına hazırla
 * @param {Object} pivotData - Pivot tablo verisi
 * @returns {Object} Excel formatı verisi
 */
export const preparePivotForExcel = (pivotData) => {
  const { headers, rows, summary } = pivotData;
  
  return {
    worksheets: [
      {
        name: 'Pivot Tablo',
        headers: headers.map(h => h.title),
        data: rows.map(row => 
          headers.map(header => row[header.key] || '')
        )
      },
      {
        name: 'Özet',
        headers: ['Alan', 'Değer', 'Toplama Tipi'],
        data: Object.entries(summary).map(([key, value]) => [
          value.field,
          value.value,
          value.type
        ])
      }
    ]
  };
};
