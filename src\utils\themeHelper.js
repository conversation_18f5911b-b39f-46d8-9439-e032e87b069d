/**
 * Theme yardımcı fonksiyonları
 * Theme property'lerini güvenli bir şekilde almak için
 */

/**
 * Theme objesinden güvenli renk alması
 * @param {Object} theme - Theme objesi
 * @param {string} colorKey - <PERSON><PERSON> anah<PERSON>ı
 * @returns {string} - <PERSON><PERSON> değeri
 */
export const getThemeColor = (theme, colorKey) => {
  // Theme objesi yoksa varsayılan renkleri dön
  if (!theme) {
    return getDefaultColor(colorKey);
  }

  // Önce theme objesinin ana property'lerini kontrol et
  if (theme[colorKey]) {
    return theme[colorKey];
  }

  // Sonra colors alt objesini kontrol et
  if (theme.colors && theme.colors[colorKey]) {
    return theme.colors[colorKey];
  }

  // Varsayılan renk mapping'ini kontrol et
  const colorMapping = {
    background: theme.BACKGROUND || theme.colors?.background || '#ffffff',
    surface: theme.SURFACE || theme.colors?.surface || '#f5f5f5',
    card: theme.CARD || theme.colors?.card || '#ffffff',
    text: theme.TEXT_PRIMARY || theme.colors?.text || '#000000',
    textSecondary: theme.TEXT_SECONDARY || theme.colors?.textSecondary || '#666666',
    primary: theme.PRIMARY || theme.colors?.primary || '#007bff',
    success: theme.SUCCESS || theme.colors?.success || '#28a745',
    danger: theme.DANGER || theme.colors?.danger || '#dc3545',
    warning: theme.WARNING || theme.colors?.warning || '#ffc107',
    info: theme.INFO || theme.colors?.info || '#17a2b8',
    border: theme.BORDER || theme.colors?.border || '#dee2e6',
    error: theme.DANGER || theme.colors?.error || '#dc3545',
    income: theme.INCOME || theme.colors?.income || '#28a745',
    expense: theme.EXPENSE || theme.colors?.expense || '#dc3545',
    white: theme.WHITE || theme.colors?.white || '#ffffff',
    black: theme.BLACK || theme.colors?.black || '#000000',
  };

  return colorMapping[colorKey] || getDefaultColor(colorKey);
};

/**
 * Varsayılan renk değerleri
 * @param {string} colorKey - Renk anahtarı
 * @returns {string} - Varsayılan renk
 */
const getDefaultColor = (colorKey) => {
  const defaultColors = {
    background: '#ffffff',
    surface: '#f5f5f5',
    card: '#ffffff',
    text: '#000000',
    textSecondary: '#666666',
    primary: '#007bff',
    success: '#28a745',
    danger: '#dc3545',
    warning: '#ffc107',
    info: '#17a2b8',
    border: '#dee2e6',
    error: '#dc3545',
    income: '#28a745',
    expense: '#dc3545',
    white: '#ffffff',
    black: '#000000',
  };

  return defaultColors[colorKey] || '#000000';
};

/**
 * Theme objesi doğrulama
 * @param {Object} theme - Theme objesi
 * @returns {boolean} - Geçerli theme objesi mi
 */
export const validateTheme = (theme) => {
  if (!theme) return false;
  
  // En az bir renk property'si olmalı
  const hasColors = theme.colors && Object.keys(theme.colors).length > 0;
  const hasDirectColors = theme.BACKGROUND || theme.PRIMARY || theme.TEXT_PRIMARY;
  
  return hasColors || hasDirectColors;
};

/**
 * Güvenli theme hook wrapper
 * @param {Object} theme - Theme objesi
 * @returns {Object} - Güvenli theme objesi
 */
export const useSafeTheme = (theme) => {
  if (!validateTheme(theme)) {
    console.warn('Invalid theme object, using default colors');
    return {
      BACKGROUND: '#ffffff',
      SURFACE: '#f5f5f5',
      CARD: '#ffffff',
      TEXT_PRIMARY: '#000000',
      TEXT_SECONDARY: '#666666',
      PRIMARY: '#007bff',
      SUCCESS: '#28a745',
      DANGER: '#dc3545',
      WARNING: '#ffc107',
      INFO: '#17a2b8',
      BORDER: '#dee2e6',
      WHITE: '#ffffff',
      BLACK: '#000000',
      INCOME: '#28a745',
      EXPENSE: '#dc3545',
      colors: {
        background: '#ffffff',
        surface: '#f5f5f5',
        card: '#ffffff',
        text: '#000000',
        textSecondary: '#666666',
        primary: '#007bff',
        success: '#28a745',
        danger: '#dc3545',
        warning: '#ffc107',
        info: '#17a2b8',
        border: '#dee2e6',
        error: '#dc3545',
        income: '#28a745',
        expense: '#dc3545',
        white: '#ffffff',
        black: '#000000',
      }
    };
  }

  return theme;
};
