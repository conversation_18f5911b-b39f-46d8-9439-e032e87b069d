import { format, parse, isValid } from 'date-fns';
import { tr } from 'date-fns/locale/tr';

/**
 * Güvenli tarih formatting fonksiyonu - geçersiz tarihleri ele alır
 * @param {string|Date} date - Format edilecek tarih
 * @param {string} formatStr - Format string'i
 * @param {object} options - Format seçenekleri
 * @returns {string} Formatlanmış tarih veya hata mesajı
 */
const safeFormatDate = (date, formatStr = 'dd.MM.yyyy', options = { locale: tr }) => {
  try {
    const dateObj = date instanceof Date ? date : new Date(date);
    if (!isValid(dateObj)) {
      return 'Geçersiz tarih';
    }
    return format(dateObj, formatStr, options);
  } catch (error) {
    console.warn('Date formatting error:', error);
    return 'Geçersiz tarih';
  }
};

/**
 * <PERSON><PERSON><PERSON> formatına çevirir
 * @param {string} dateString SQLite tarih string'i
 * @returns {string} Formatlanmış tarih
 */
export const formatDate = (dateString) => {
  try {
    const date = parseSQLiteDate(dateString);
    return date.toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  } catch {
    return 'Geçersiz tarih';
  }
};

/**
 * Formats a date into Month Year format in Turkish
 * @param {string} dateStr Date string in YYYY-MM format
 * @returns {string} Formatted month year string
 */
export const formatMonthYear = (dateStr) => {
  if (!dateStr) return '';
  
  const [year, month] = dateStr.split('-');
  const date = new Date(year, parseInt(month) - 1);
  
  return date.toLocaleDateString('tr-TR', {
    month: 'long',
    year: 'numeric'
  });
};

/**
 * Tarihi Türkçe formatlar
 * @param {string|Date} date Formatlanacak tarih
 * @returns {string} Formatlanmış tarih
 */
export const formatLocaleDate = (date) => {
  return safeFormatDate(date, 'd MMMM yyyy');
};

/**
 * Formats a date into short month format
 * @param {Date|string} date Date to format
 * @returns {string} Formatted month string
 */
export const formatShortMonth = (date) => {
  return new Date(date).toLocaleDateString('tr-TR', {
    month: 'short'
  });
};

/**
 * Formats a date into a short format suitable for charts
 * @param {Date|string} date Date to format
 * @returns {string} Formatted date string
 */
export const formatShortDate = (date) => {
  if (!date) return '';
  const d = new Date(date);
  const day = d.getDate();
  const month = d.getMonth() + 1;
  return `${day}/${month}`;
};

/**
 * SQLite formatındaki tarihi JavaScript Date nesnesine çevirir
 * @param {string} dateStr - SQLite formatında tarih string'i
 * @returns {Date} JavaScript Date nesnesi
 */
export const parseSQLiteDate = (dateStr) => {
  if (!dateStr) return new Date();
  return parse(dateStr, 'yyyy-MM-dd', new Date());
};

/**
 * Tarihi SQLite formatına çevirir (YYYY-MM-DD)
 * @param {Date|string} date Formatlanacak tarih
 * @returns {string} YYYY-MM-DD formatında tarih
 */
export const formatSQLiteDate = (date) => {
  if (!date) return null;
  return safeFormatDate(date, 'yyyy-MM-dd', {});
};
