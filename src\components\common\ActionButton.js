import React from 'react';
import { TouchableOpacity, Text, StyleSheet } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';

/**
 * Eylem butonu bileşeni
 * @param {Object} props Component props
 * @param {string} props.text But<PERSON> metni
 * @param {string} props.icon Buton ikonu
 * @param {Function} props.onPress Butona basıldığında çağrılacak fonksiyon
 * @param {string} props.backgroundColor Buton arka plan rengi
 * @param {Object} props.style Ek stil özellikleri
 * @returns {JSX.Element} ActionButton bileşeni
 */
const ActionButton = ({ text, icon, onPress, backgroundColor = '#3498db', style }) => {
  return (
    <TouchableOpacity 
      style={[
        styles.button,
        { backgroundColor },
        style
      ]}
      onPress={onPress}
    >
      {icon && (
        <MaterialIcons name={icon} size={20} color="#fff" style={styles.icon} />
      )}
      <Text style={styles.text}>{text}</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2
  },
  icon: {
    marginRight: 8
  },
  text: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 14
  }
});

export default ActionButton;
