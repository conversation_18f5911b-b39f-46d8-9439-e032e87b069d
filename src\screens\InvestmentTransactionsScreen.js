import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  RefreshControl,
  Alert,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useSQLiteContext } from 'expo-sqlite';
import { Colors } from '../constants/colors';
import EmptyState from '../components/common/EmptyState';

/**
 * Yatırım işlemleri ekranı
 * @param {Object} props Component props
 * @param {Object} props.navigation Navigation objesi
 */
const InvestmentTransactionsScreen = ({ navigation }) => {
  const db = useSQLiteContext();
  const [transactions, setTransactions] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);

  // İşlemleri yükle
  const loadTransactions = async () => {
    try {
      setRefreshing(true);
      
      const result = await db.getAllAsync(`
        SELECT t.*, a.name as asset_name, a.symbol as asset_symbol
        FROM investment_transactions t
        JOIN investment_assets a ON t.asset_id = a.id
        ORDER BY t.date DESC, t.id DESC
      `);
      
      setTransactions(result);
    } catch (error) {
      console.error('İşlem yükleme hatası:', error);
      Alert.alert('Hata', 'İşlemler yüklenirken bir hata oluştu.');
    } finally {
      setRefreshing(false);
      setLoading(false);
    }
  };

  // İlk yükleme
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      loadTransactions();
    });

    return unsubscribe;
  }, [navigation]);

  // Para formatı
  const formatCurrency = (value) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };

  // Tarih formatı
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  // İşlem tipini formatla
  const formatTransactionType = (type) => {
    switch (type) {
      case 'buy': return 'Alım';
      case 'sell': return 'Satım';
      case 'dividend': return 'Temettü';
      case 'interest': return 'Faiz';
      case 'transfer': return 'Transfer';
      default: return type;
    }
  };

  // İşlem öğesi
  const renderTransactionItem = ({ item }) => {
    return (
      <TouchableOpacity
        style={styles.transactionItem}
        onPress={() => navigation.navigate('InvestmentTransactionDetail', { transaction: item })}
      >
        <View style={styles.transactionHeader}>
          <View style={styles.assetInfo}>
            <Text style={styles.assetSymbol}>{item.asset_symbol}</Text>
            <Text style={styles.assetName}>{item.asset_name}</Text>
          </View>
          <Text style={styles.transactionDate}>{formatDate(item.date)}</Text>
        </View>
        
        <View style={styles.transactionDetails}>
          <View style={styles.transactionType}>
            <MaterialIcons
              name={item.type === 'buy' ? 'arrow-downward' : 'arrow-upward'}
              size={16}
              color="#fff"
            />
            <Text style={styles.transactionTypeText}>
              {formatTransactionType(item.type)}
            </Text>
          </View>
          
          <View style={styles.transactionValues}>
            <Text style={styles.transactionQuantity}>
              {item.quantity} {item.asset_symbol}
            </Text>
            <Text style={styles.transactionPrice}>
              {formatCurrency(item.price)}
            </Text>
            <Text style={styles.transactionTotal}>
              {formatCurrency(item.price * item.quantity)}
            </Text>
          </View>
        </View>
        
        {item.notes && (
          <Text style={styles.transactionNotes}>{item.notes}</Text>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <MaterialIcons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.title}>Yatırım İşlemleri</Text>
        <TouchableOpacity
          style={styles.headerButton}
          onPress={() => navigation.navigate('InvestmentTransactionForm')}
        >
          <MaterialIcons name="add" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      {transactions.length === 0 ? (
        <EmptyState
          icon="receipt-long"
          title="Henüz İşlem Yok"
          message="Yatırım işlemlerinizi ekleyerek takip etmeye başlayın."
          buttonText="İşlem Ekle"
          onButtonPress={() => navigation.navigate('InvestmentTransactionForm')}
        />
      ) : (
        <FlatList
          data={transactions}
          keyExtractor={(item) => item.id.toString()}
          renderItem={renderTransactionItem}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={loadTransactions} />
          }
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.PRIMARY,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContent: {
    padding: 16,
  },
  transactionItem: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  transactionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  assetInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  assetSymbol: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginRight: 8,
  },
  assetName: {
    fontSize: 14,
    color: '#666',
  },
  transactionDate: {
    fontSize: 14,
    color: '#666',
  },
  transactionDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  transactionType: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  transactionTypeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  transactionValues: {
    alignItems: 'flex-end',
  },
  transactionQuantity: {
    fontSize: 14,
    color: '#333',
    marginBottom: 2,
  },
  transactionPrice: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  transactionTotal: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  transactionNotes: {
    fontSize: 14,
    color: '#666',
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    paddingTop: 8,
  },
});

export default InvestmentTransactionsScreen;
