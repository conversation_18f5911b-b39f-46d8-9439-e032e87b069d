/**
 * Yatırım servisi
 * Yatırım işlemlerini yönetir
 */
import { useSQLiteContext } from 'expo-sqlite';

/**
 * Yatırım varlıklarını getirir
 * 
 * @param {Object} options - <PERSON><PERSON><PERSON>ç<PERSON>ri
 * @param {string} options.type - Varlık tipi (stock, crypto, gold, vb.)
 * @param {boolean} options.activeOnly - Sadece aktif varlıkları getir
 * @returns {Promise<Array>} Yatırım varlıkları
 */
export const getInvestmentAssets = async (options = {}) => {
  const db = useSQLiteContext();
  
  try {
    let query = `
      SELECT * FROM investment_assets
      WHERE 1=1
    `;
    
    const params = [];
    
    if (options.type) {
      query += ' AND type = ?';
      params.push(options.type);
    }
    
    if (options.activeOnly) {
      query += ' AND is_active = 1';
    }
    
    query += ' ORDER BY name';
    
    return await db.getAllAsync(query, params);
  } catch (error) {
    console.error('Yatırım varlıkları getirme hatası:', error);
    throw error;
  }
};

/**
 * Yatırım varlığı ekler
 * 
 * @param {Object} asset - Yatırım varlığı
 * @returns {Promise<number>} Eklenen varlığın ID'si
 */
export const addInvestmentAsset = async (asset) => {
  const db = useSQLiteContext();
  
  try {
    const result = await db.runAsync(`
      INSERT INTO investment_assets (name, symbol, type, description, icon, color)
      VALUES (?, ?, ?, ?, ?, ?)
    `, [
      asset.name,
      asset.symbol,
      asset.type,
      asset.description || null,
      asset.icon || null,
      asset.color || null
    ]);
    
    return result.lastInsertRowId;
  } catch (error) {
    console.error('Yatırım varlığı ekleme hatası:', error);
    throw error;
  }
};

/**
 * Yatırım varlığını günceller
 * 
 * @param {number} id - Varlık ID'si
 * @param {Object} asset - Güncellenecek varlık verileri
 * @returns {Promise<number>} Etkilenen satır sayısı
 */
export const updateInvestmentAsset = async (id, asset) => {
  const db = useSQLiteContext();
  
  try {
    const result = await db.runAsync(`
      UPDATE investment_assets
      SET name = ?, symbol = ?, type = ?, description = ?, icon = ?, color = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [
      asset.name,
      asset.symbol,
      asset.type,
      asset.description || null,
      asset.icon || null,
      asset.color || null,
      asset.is_active !== undefined ? asset.is_active : 1,
      id
    ]);
    
    return result.changes;
  } catch (error) {
    console.error('Yatırım varlığı güncelleme hatası:', error);
    throw error;
  }
};

/**
 * Yatırım varlığını siler
 * 
 * @param {number} id - Varlık ID'si
 * @returns {Promise<number>} Etkilenen satır sayısı
 */
export const deleteInvestmentAsset = async (id) => {
  const db = useSQLiteContext();
  
  try {
    // Önce bu varlığa ait işlemleri kontrol et
    const transactions = await db.getAllAsync(`
      SELECT COUNT(*) as count FROM investment_transactions
      WHERE asset_id = ?
    `, [id]);
    
    if (transactions[0].count > 0) {
      throw new Error('Bu varlığa ait işlemler var. Önce işlemleri silmelisiniz.');
    }
    
    // Varlığı sil
    const result = await db.runAsync(`
      DELETE FROM investment_assets
      WHERE id = ?
    `, [id]);
    
    return result.changes;
  } catch (error) {
    console.error('Yatırım varlığı silme hatası:', error);
    throw error;
  }
};

/**
 * Yatırım işlemi ekler
 * 
 * @param {Object} transaction - Yatırım işlemi
 * @returns {Promise<number>} Eklenen işlemin ID'si
 */
export const addInvestmentTransaction = async (transaction) => {
  const db = useSQLiteContext();
  
  try {
    // İşlemi ekle
    const result = await db.runAsync(`
      INSERT INTO investment_transactions (
        asset_id, type, quantity, price_per_unit, total_amount, fee,
        date, currency, exchange_rate, preferred_currency, preferred_currency_amount,
        notes, metadata
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      transaction.asset_id,
      transaction.type,
      transaction.quantity,
      transaction.price_per_unit,
      transaction.total_amount,
      transaction.fee || 0,
      transaction.date,
      transaction.currency || 'TRY',
      transaction.exchange_rate || null,
      transaction.preferred_currency || null,
      transaction.preferred_currency_amount || null,
      transaction.notes || null,
      transaction.metadata ? JSON.stringify(transaction.metadata) : null
    ]);
    
    // Portföyü güncelle
    await updatePortfolio(transaction.asset_id);
    
    return result.lastInsertRowId;
  } catch (error) {
    console.error('Yatırım işlemi ekleme hatası:', error);
    throw error;
  }
};

/**
 * Yatırım işlemini günceller
 * 
 * @param {number} id - İşlem ID'si
 * @param {Object} transaction - Güncellenecek işlem verileri
 * @returns {Promise<number>} Etkilenen satır sayısı
 */
export const updateInvestmentTransaction = async (id, transaction) => {
  const db = useSQLiteContext();
  
  try {
    // İşlemi güncelle
    const result = await db.runAsync(`
      UPDATE investment_transactions
      SET asset_id = ?, type = ?, quantity = ?, price_per_unit = ?, total_amount = ?, fee = ?,
          date = ?, currency = ?, exchange_rate = ?, preferred_currency = ?, preferred_currency_amount = ?,
          notes = ?, metadata = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [
      transaction.asset_id,
      transaction.type,
      transaction.quantity,
      transaction.price_per_unit,
      transaction.total_amount,
      transaction.fee || 0,
      transaction.date,
      transaction.currency || 'TRY',
      transaction.exchange_rate || null,
      transaction.preferred_currency || null,
      transaction.preferred_currency_amount || null,
      transaction.notes || null,
      transaction.metadata ? JSON.stringify(transaction.metadata) : null,
      id
    ]);
    
    // Portföyü güncelle
    await updatePortfolio(transaction.asset_id);
    
    return result.changes;
  } catch (error) {
    console.error('Yatırım işlemi güncelleme hatası:', error);
    throw error;
  }
};

/**
 * Yatırım işlemini siler
 * 
 * @param {number} id - İşlem ID'si
 * @returns {Promise<number>} Etkilenen satır sayısı
 */
export const deleteInvestmentTransaction = async (id) => {
  const db = useSQLiteContext();
  
  try {
    // İşlemin varlık ID'sini al
    const transaction = await db.getFirstAsync(`
      SELECT asset_id FROM investment_transactions
      WHERE id = ?
    `, [id]);
    
    if (!transaction) {
      throw new Error('İşlem bulunamadı');
    }
    
    // İşlemi sil
    const result = await db.runAsync(`
      DELETE FROM investment_transactions
      WHERE id = ?
    `, [id]);
    
    // Portföyü güncelle
    await updatePortfolio(transaction.asset_id);
    
    return result.changes;
  } catch (error) {
    console.error('Yatırım işlemi silme hatası:', error);
    throw error;
  }
};

/**
 * Yatırım işlemlerini getirir
 * 
 * @param {Object} options - Sorgu seçenekleri
 * @param {number} options.asset_id - Varlık ID'si
 * @param {string} options.type - İşlem tipi (buy, sell, vb.)
 * @param {string} options.startDate - Başlangıç tarihi
 * @param {string} options.endDate - Bitiş tarihi
 * @returns {Promise<Array>} Yatırım işlemleri
 */
export const getInvestmentTransactions = async (options = {}) => {
  const db = useSQLiteContext();
  
  try {
    let query = `
      SELECT t.*, a.name as asset_name, a.symbol as asset_symbol, a.type as asset_type
      FROM investment_transactions t
      JOIN investment_assets a ON t.asset_id = a.id
      WHERE 1=1
    `;
    
    const params = [];
    
    if (options.asset_id) {
      query += ' AND t.asset_id = ?';
      params.push(options.asset_id);
    }
    
    if (options.type) {
      query += ' AND t.type = ?';
      params.push(options.type);
    }
    
    if (options.startDate) {
      query += ' AND t.date >= ?';
      params.push(options.startDate);
    }
    
    if (options.endDate) {
      query += ' AND t.date <= ?';
      params.push(options.endDate);
    }
    
    query += ' ORDER BY t.date DESC, t.id DESC';
    
    return await db.getAllAsync(query, params);
  } catch (error) {
    console.error('Yatırım işlemleri getirme hatası:', error);
    throw error;
  }
};

/**
 * Portföyü günceller
 * 
 * @param {number} assetId - Varlık ID'si
 * @returns {Promise<void>}
 */
export const updatePortfolio = async (assetId) => {
  const db = useSQLiteContext();
  
  try {
    // Varlığın tüm işlemlerini getir
    const transactions = await db.getAllAsync(`
      SELECT * FROM investment_transactions
      WHERE asset_id = ?
      ORDER BY date, id
    `, [assetId]);
    
    // Portföy hesapla
    let quantity = 0;
    let totalCost = 0;
    
    for (const tx of transactions) {
      if (tx.type === 'buy') {
        const cost = tx.price_per_unit * tx.quantity;
        totalCost += cost + tx.fee;
        quantity += tx.quantity;
      } else if (tx.type === 'sell') {
        // Satış işleminde, ortalama maliyeti kullanarak toplam maliyeti güncelle
        const averageCost = quantity > 0 ? totalCost / quantity : 0;
        const soldCost = averageCost * tx.quantity;
        
        totalCost -= soldCost;
        quantity -= tx.quantity;
        
        // Negatif miktar olamaz
        if (quantity < 0) quantity = 0;
        if (totalCost < 0) totalCost = 0;
      }
    }
    
    // Ortalama alış fiyatı
    const averageBuyPrice = quantity > 0 ? totalCost / quantity : 0;
    
    // Güncel fiyatı al (örnek olarak son işlemin fiyatını kullanıyoruz)
    // Gerçek uygulamada bir API'den güncel fiyat alınmalı
    const latestTransaction = transactions.length > 0 ? transactions[transactions.length - 1] : null;
    const currentPrice = latestTransaction ? latestTransaction.price_per_unit : 0;
    
    // Güncel değer ve kar/zarar hesapla
    const currentValue = quantity * currentPrice;
    const profitLoss = currentValue - totalCost;
    const profitLossPercentage = totalCost > 0 ? (profitLoss / totalCost) * 100 : 0;
    
    // Portföyü güncelle veya oluştur
    const existingPortfolio = await db.getFirstAsync(`
      SELECT id FROM investment_portfolio
      WHERE asset_id = ?
    `, [assetId]);
    
    if (existingPortfolio) {
      await db.runAsync(`
        UPDATE investment_portfolio
        SET quantity = ?, average_buy_price = ?, current_price = ?,
            current_value = ?, profit_loss = ?, profit_loss_percentage = ?,
            last_updated = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
        WHERE asset_id = ?
      `, [
        quantity,
        averageBuyPrice,
        currentPrice,
        currentValue,
        profitLoss,
        profitLossPercentage,
        assetId
      ]);
    } else {
      await db.runAsync(`
        INSERT INTO investment_portfolio (
          asset_id, quantity, average_buy_price, current_price,
          current_value, profit_loss, profit_loss_percentage, last_updated
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
      `, [
        assetId,
        quantity,
        averageBuyPrice,
        currentPrice,
        currentValue,
        profitLoss,
        profitLossPercentage
      ]);
    }
  } catch (error) {
    console.error('Portföy güncelleme hatası:', error);
    throw error;
  }
};

/**
 * Portföyü getirir
 * 
 * @param {Object} options - Sorgu seçenekleri
 * @param {string} options.type - Varlık tipi (stock, crypto, gold, vb.)
 * @returns {Promise<Array>} Portföy
 */
export const getPortfolio = async (options = {}) => {
  const db = useSQLiteContext();
  
  try {
    let query = `
      SELECT p.*, a.name as asset_name, a.symbol as asset_symbol, a.type as asset_type, a.icon, a.color
      FROM investment_portfolio p
      JOIN investment_assets a ON p.asset_id = a.id
      WHERE p.quantity > 0
    `;
    
    const params = [];
    
    if (options.type) {
      query += ' AND a.type = ?';
      params.push(options.type);
    }
    
    query += ' ORDER BY p.current_value DESC';
    
    return await db.getAllAsync(query, params);
  } catch (error) {
    console.error('Portföy getirme hatası:', error);
    throw error;
  }
};

/**
 * Portföy özetini getirir
 * 
 * @returns {Promise<Object>} Portföy özeti
 */
export const getPortfolioSummary = async () => {
  const db = useSQLiteContext();
  
  try {
    // Toplam portföy değeri
    const totalValue = await db.getFirstAsync(`
      SELECT SUM(current_value) as total_value FROM investment_portfolio
    `);
    
    // Toplam kar/zarar
    const totalProfitLoss = await db.getFirstAsync(`
      SELECT SUM(profit_loss) as total_profit_loss FROM investment_portfolio
    `);
    
    // Varlık tiplerine göre dağılım
    const distribution = await db.getAllAsync(`
      SELECT a.type, SUM(p.current_value) as value
      FROM investment_portfolio p
      JOIN investment_assets a ON p.asset_id = a.id
      GROUP BY a.type
      ORDER BY value DESC
    `);
    
    // En karlı varlıklar
    const topPerformers = await db.getAllAsync(`
      SELECT p.*, a.name as asset_name, a.symbol as asset_symbol, a.type as asset_type
      FROM investment_portfolio p
      JOIN investment_assets a ON p.asset_id = a.id
      WHERE p.quantity > 0
      ORDER BY p.profit_loss_percentage DESC
      LIMIT 5
    `);
    
    // En zararlı varlıklar
    const worstPerformers = await db.getAllAsync(`
      SELECT p.*, a.name as asset_name, a.symbol as asset_symbol, a.type as asset_type
      FROM investment_portfolio p
      JOIN investment_assets a ON p.asset_id = a.id
      WHERE p.quantity > 0
      ORDER BY p.profit_loss_percentage ASC
      LIMIT 5
    `);
    
    return {
      totalValue: totalValue.total_value || 0,
      totalProfitLoss: totalProfitLoss.total_profit_loss || 0,
      profitLossPercentage: totalValue.total_value > 0 ? (totalProfitLoss.total_profit_loss / totalValue.total_value) * 100 : 0,
      distribution,
      topPerformers,
      worstPerformers
    };
  } catch (error) {
    console.error('Portföy özeti getirme hatası:', error);
    throw error;
  }
};

/**
 * Fiyat geçmişi ekler
 * 
 * @param {Object} priceData - Fiyat verisi
 * @returns {Promise<number>} Eklenen kaydın ID'si
 */
export const addPriceHistory = async (priceData) => {
  const db = useSQLiteContext();
  
  try {
    const result = await db.runAsync(`
      INSERT INTO investment_price_history (asset_id, price, date, currency)
      VALUES (?, ?, ?, ?)
    `, [
      priceData.asset_id,
      priceData.price,
      priceData.date,
      priceData.currency || 'TRY'
    ]);
    
    return result.lastInsertRowId;
  } catch (error) {
    console.error('Fiyat geçmişi ekleme hatası:', error);
    throw error;
  }
};

/**
 * Fiyat geçmişini getirir
 * 
 * @param {Object} options - Sorgu seçenekleri
 * @param {number} options.asset_id - Varlık ID'si
 * @param {string} options.startDate - Başlangıç tarihi
 * @param {string} options.endDate - Bitiş tarihi
 * @returns {Promise<Array>} Fiyat geçmişi
 */
export const getPriceHistory = async (options = {}) => {
  const db = useSQLiteContext();
  
  try {
    let query = `
      SELECT * FROM investment_price_history
      WHERE 1=1
    `;
    
    const params = [];
    
    if (options.asset_id) {
      query += ' AND asset_id = ?';
      params.push(options.asset_id);
    }
    
    if (options.startDate) {
      query += ' AND date >= ?';
      params.push(options.startDate);
    }
    
    if (options.endDate) {
      query += ' AND date <= ?';
      params.push(options.endDate);
    }
    
    query += ' ORDER BY date';
    
    return await db.getAllAsync(query, params);
  } catch (error) {
    console.error('Fiyat geçmişi getirme hatası:', error);
    throw error;
  }
};
