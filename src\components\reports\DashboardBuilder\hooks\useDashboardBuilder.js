import React, { useState, useEffect, useCallback } from 'react';

/**
 * Dashboard Builder Hook - Dashboard oluşturma ve yönetimi
 */
const useDashboardBuilder = (dashboardId = null) => {
  const [widgets, setWidgets] = useState([]);
  const [selectedWidget, setSelectedWidget] = useState(null);
  const [history, setHistory] = useState([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [dashboardConfig, setDashboardConfig] = useState({
    id: null,
    title: '',
    description: '',
    settings: {},
    layout: {},
    createdAt: null,
    updatedAt: null,
  });

  /**
   * History yönetimi
   */
  const addToHistory = useCallback((newState) => {
    const newHistory = history.slice(0, historyIndex + 1);
    newHistory.push(newState);
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  }, [history, historyIndex]);

  /**
   * Widget ekleme
   */
  const addWidget = useCallback((widget) => {
    const newWidgets = [...widgets, widget];
    setWidgets(newWidgets);
    addToHistory({ widgets: newWidgets, selectedWidget });
    setSelectedWidget(widget);
  }, [widgets, selectedWidget, addToHistory]);

  /**
   * Widget silme
   */
  const removeWidget = useCallback((widgetId) => {
    const newWidgets = widgets.filter(w => w.id !== widgetId);
    setWidgets(newWidgets);
    addToHistory({ widgets: newWidgets, selectedWidget: null });
    if (selectedWidget?.id === widgetId) {
      setSelectedWidget(null);
    }
  }, [widgets, selectedWidget, addToHistory]);

  /**
   * Widget güncelleme
   */
  const updateWidget = useCallback((widgetId, updates) => {
    const newWidgets = widgets.map(widget => 
      widget.id === widgetId ? { ...widget, ...updates } : widget
    );
    setWidgets(newWidgets);
    addToHistory({ widgets: newWidgets, selectedWidget });
    
    // Seçili widget'ı da güncelle
    if (selectedWidget?.id === widgetId) {
      setSelectedWidget(prev => ({ ...prev, ...updates }));
    }
  }, [widgets, selectedWidget, addToHistory]);

  /**
   * Widget seçimi
   */
  const selectWidget = useCallback((widget) => {
    setSelectedWidget(widget);
  }, []);

  /**
   * Widget konumu değiştirme
   */
  const moveWidget = useCallback((widgetId, newPosition) => {
    updateWidget(widgetId, { position: newPosition });
  }, [updateWidget]);

  /**
   * Widget boyutu değiştirme
   */
  const resizeWidget = useCallback((widgetId, newSize) => {
    updateWidget(widgetId, { size: newSize });
  }, [updateWidget]);

  /**
   * Geri alma
   */
  const undo = useCallback(() => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1;
      const previousState = history[newIndex];
      setWidgets(previousState.widgets);
      setSelectedWidget(previousState.selectedWidget);
      setHistoryIndex(newIndex);
    }
  }, [history, historyIndex]);

  /**
   * Yineleme
   */
  const redo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      const newIndex = historyIndex + 1;
      const nextState = history[newIndex];
      setWidgets(nextState.widgets);
      setSelectedWidget(nextState.selectedWidget);
      setHistoryIndex(newIndex);
    }
  }, [history, historyIndex]);

  /**
   * Temizleme
   */
  const clear = useCallback(() => {
    setWidgets([]);
    setSelectedWidget(null);
    addToHistory({ widgets: [], selectedWidget: null });
  }, [addToHistory]);

  /**
   * Dashboard kaydetme
   */
  const saveDashboard = useCallback(async (config) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // TODO: Implement actual save to database
      const savedDashboard = {
        ...config,
        id: dashboardId || Date.now().toString(),
        widgets,
        createdAt: dashboardConfig.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      setDashboardConfig(savedDashboard);
      return savedDashboard;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [dashboardId, widgets, dashboardConfig]);

  /**
   * Dashboard yükleme
   */
  const loadDashboard = useCallback(async (id) => {
    if (!id) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      // TODO: Implement actual load from database
      // Mock data for now
      const mockDashboard = {
        id,
        title: 'Sample Dashboard',
        description: 'Sample dashboard description',
        widgets: [],
        settings: {},
        layout: {},
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      setDashboardConfig(mockDashboard);
      setWidgets(mockDashboard.widgets);
      setSelectedWidget(null);
      setHistory([{ widgets: mockDashboard.widgets, selectedWidget: null }]);
      setHistoryIndex(0);
      
      return mockDashboard;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Dashboard dışa aktarma
   */
  const exportDashboard = useCallback(async (format, options = {}) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // TODO: Implement actual export functionality
      const exportData = {
        format,
        dashboard: {
          ...dashboardConfig,
          widgets,
        },
        options,
        exportedAt: new Date().toISOString(),
      };
      
      return exportData;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [dashboardConfig, widgets]);

  /**
   * Dashboard yükleme (component mount)
   */
  useEffect(() => {
    if (dashboardId) {
      loadDashboard(dashboardId);
    } else {
      // Yeni dashboard
      setHistory([{ widgets: [], selectedWidget: null }]);
      setHistoryIndex(0);
    }
  }, [dashboardId, loadDashboard]);

  /**
   * History durumu
   */
  const canUndo = historyIndex > 0;
  const canRedo = historyIndex < history.length - 1;

  return {
    // State
    widgets,
    selectedWidget,
    dashboardConfig,
    isLoading,
    error,
    canUndo,
    canRedo,
    
    // Actions
    addWidget,
    removeWidget,
    updateWidget,
    selectWidget,
    moveWidget,
    resizeWidget,
    undo,
    redo,
    clear,
    saveDashboard,
    loadDashboard,
    exportDashboard,
  };
};

export default useDashboardBuilder;
