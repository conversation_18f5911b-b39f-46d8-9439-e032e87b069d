import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  TextInput, 
  ScrollView, 
  Alert,
  Animated,
  Dimensions
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAppContext } from '../context/AppContext';
import { useTheme } from '../context/ThemeContext';

const { width } = Dimensions.get('window');

/**
 * Modern kullanıcı profili ekranı
 * Gelişmiş avatar seçimi ve kullanıcı deneyimi
 */
const UserProfileScreen = ({ navigation }) => {
  const insets = useSafeAreaInsets();
  const { theme } = useTheme();
  const { userProfile, updateUserProfile } = useAppContext();
  
  const [userName, setUserName] = useState(userProfile.name || 'Kullanıcı');
  const [selectedAvatar, setSelectedAvatar] = useState(userProfile.avatar || 'person');
  const [isEditing, setIsEditing] = useState(false);
  const [saveAnimation] = useState(new Animated.Value(0));

  // Kategorize edilmiş avatar seçenekleri
  const avatarCategories = [
    {
      title: 'Kişisel',
      icons: [
        { icon: 'person', label: 'Varsayılan', color: '#6c5ce7' },
        { icon: 'face', label: 'Yüz', color: '#fd79a8' },
        { icon: 'account-circle', label: 'Profil', color: '#00b894' },
        { icon: 'sentiment-satisfied', label: 'Mutlu', color: '#fdcb6e' },
        { icon: 'sentiment-very-satisfied', label: 'Çok Mutlu', color: '#e17055' },
        { icon: 'sentiment-satisfied-alt', label: 'Gülümse', color: '#74b9ff' },
      ]
    },
    {
      title: 'Semboller',
      icons: [
        { icon: 'star', label: 'Yıldız', color: '#f39c12' },
        { icon: 'favorite', label: 'Kalp', color: '#d63031' },
        { icon: 'lightbulb', label: 'Fikir', color: '#f39c12' },
        { icon: 'diamond', label: 'Elmas', color: '#a29bfe' },
        { icon: 'auto-awesome', label: 'Parlak', color: '#fdcb6e' },
        { icon: 'verified', label: 'Doğrulanmış', color: '#00b894' },
      ]
    },
    {
      title: 'Hobiler',
      icons: [
        { icon: 'music-note', label: 'Müzik', color: '#6c5ce7' },
        { icon: 'sports-soccer', label: 'Spor', color: '#fd79a8' },
        { icon: 'palette', label: 'Sanat', color: '#a29bfe' },
        { icon: 'local-florist', label: 'Çiçek', color: '#55a3ff' },
        { icon: 'eco', label: 'Doğa', color: '#00b894' },
        { icon: 'pets', label: 'Evcil Hayvan', color: '#74b9ff' },
      ]
    },
    {
      title: 'Özel',
      icons: [
        { icon: 'psychology', label: 'Zeka', color: '#fd79a8' },
        { icon: 'rocket-launch', label: 'Roket', color: '#e17055' },
        { icon: 'workspace-premium', label: 'Premium', color: '#f39c12' },
        { icon: 'emoji-events', label: 'Ödül', color: '#fdcb6e' },
        { icon: 'local-fire-department', label: 'Ateş', color: '#d63031' },
        { icon: 'whatshot', label: 'Trend', color: '#fd79a8' },
      ]
    }
  ];

  // Tüm avatar seçeneklerini düz bir diziye çevir
  const allAvatars = avatarCategories.flatMap(category => category.icons);

  /**
   * Profil bilgilerini kaydet
   */
  const handleSave = async () => {
    try {
      // Animasyonu başlat
      Animated.sequence([
        Animated.timing(saveAnimation, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(saveAnimation, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();

      const profileData = {
        name: userName.trim() || 'Kullanıcı',
        avatar: selectedAvatar,
        updatedAt: new Date().toISOString()
      };

      await updateUserProfile(profileData);
      await AsyncStorage.setItem('userProfile', JSON.stringify(profileData));
      
      setIsEditing(false);
      Alert.alert('✅ Başarılı', 'Profil bilgileriniz güncellendi!', [
        { text: 'Tamam', style: 'default' }
      ]);
    } catch (error) {
      console.error('Profil güncelleme hatası:', error);
      Alert.alert('❌ Hata', 'Profil bilgileri güncellenirken bir hata oluştu.');
    }
  };

  /**
   * Değişiklikleri iptal et
   */
  const handleCancel = () => {
    setUserName(userProfile.name || 'Kullanıcı');
    setSelectedAvatar(userProfile.avatar || 'person');
    setIsEditing(false);
  };

  /**
   * Avatar seçimi için özel bileşen
   */
  const AvatarSelector = ({ avatar, isSelected, onSelect }) => {
    const [scaleAnim] = useState(new Animated.Value(1));

    const handlePress = () => {
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 0.9,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }),
      ]).start();
      
      onSelect(avatar.icon);
    };

    return (
      <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
        <TouchableOpacity
          style={[
            styles.avatarOption,
            { backgroundColor: avatar.color + '15' },
            isSelected && [styles.avatarOptionSelected, { borderColor: avatar.color }]
          ]}
          onPress={handlePress}
          activeOpacity={0.7}
        >
          <View style={[
            styles.avatarIconContainer,
            { backgroundColor: avatar.color + '25' }
          ]}>
            <MaterialIcons 
              name={avatar.icon} 
              size={24} 
              color={avatar.color} 
            />
          </View>
          <Text style={[styles.avatarLabel, { color: isSelected ? avatar.color : theme.TEXT_SECONDARY }]}>
            {avatar.label}
          </Text>
          {isSelected && (
            <View style={[styles.selectedBadge, { backgroundColor: avatar.color }]}>
              <MaterialIcons name="check" size={12} color="#fff" />
            </View>
          )}
        </TouchableOpacity>
      </Animated.View>
    );
  };

  const getStyles = (theme) => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.BACKGROUND,
    },
    header: {
      backgroundColor: theme.PRIMARY,
      paddingTop: insets.top + 10,
      paddingBottom: 20,
      paddingHorizontal: 20,
      borderBottomLeftRadius: 24,
      borderBottomRightRadius: 24,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 12,
      elevation: 8,
    },
    headerRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    backButton: {
      width: 44,
      height: 44,
      borderRadius: 22,
      backgroundColor: 'rgba(255,255,255,0.25)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    headerTitle: {
      fontSize: 22,
      fontWeight: '700',
      color: '#fff',
      flex: 1,
      textAlign: 'center',
      marginHorizontal: 20,
    },
    editButton: {
      width: 44,
      height: 44,
      borderRadius: 22,
      backgroundColor: 'rgba(255,255,255,0.25)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    content: {
      flex: 1,
      padding: 20,
    },
    profileSection: {
      alignItems: 'center',
      marginBottom: 32,
      paddingVertical: 32,
      paddingHorizontal: 24,
      backgroundColor: theme.SURFACE,
      borderRadius: 24,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 12,
      elevation: 6,
    },
    currentAvatar: {
      width: 96,
      height: 96,
      borderRadius: 48,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 20,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 6 },
      shadowOpacity: 0.2,
      shadowRadius: 12,
      elevation: 8,
    },
    nameSection: {
      width: '100%',
      alignItems: 'center',
    },
    nameInput: {
      fontSize: 26,
      fontWeight: '700',
      color: theme.TEXT_PRIMARY,
      textAlign: 'center',
      paddingVertical: 12,
      paddingHorizontal: 20,
      borderRadius: 16,
      minWidth: 220,
      backgroundColor: isEditing ? theme.SURFACE_VARIANT : 'transparent',
      borderWidth: isEditing ? 2 : 0,
      borderColor: theme.PRIMARY + '40',
    },
    nameLabel: {
      fontSize: 14,
      color: theme.TEXT_SECONDARY,
      marginTop: 8,
      fontWeight: '500',
    },
    sectionTitle: {
      fontSize: 20,
      fontWeight: '700',
      color: theme.TEXT_PRIMARY,
      marginBottom: 20,
      marginLeft: 4,
    },
    categorySection: {
      marginBottom: 24,
    },
    categoryTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.TEXT_PRIMARY,
      marginBottom: 12,
      marginLeft: 4,
    },
    avatarGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
      marginBottom: 16,
    },
    avatarOption: {
      width: (width - 80) / 3,
      paddingVertical: 16,
      paddingHorizontal: 8,
      borderRadius: 16,
      alignItems: 'center',
      marginBottom: 16,
      backgroundColor: theme.SURFACE,
      borderWidth: 2,
      borderColor: 'transparent',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.08,
      shadowRadius: 6,
      elevation: 3,
      position: 'relative',
    },
    avatarOptionSelected: {
      borderColor: theme.PRIMARY,
      backgroundColor: theme.PRIMARY + '10',
      shadowColor: theme.PRIMARY,
      shadowOpacity: 0.2,
      shadowRadius: 8,
      elevation: 6,
    },
    avatarIconContainer: {
      width: 48,
      height: 48,
      borderRadius: 24,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 8,
    },
    avatarLabel: {
      fontSize: 11,
      fontWeight: '600',
      textAlign: 'center',
      lineHeight: 14,
    },
    selectedBadge: {
      position: 'absolute',
      top: 8,
      right: 8,
      width: 20,
      height: 20,
      borderRadius: 10,
      justifyContent: 'center',
      alignItems: 'center',
    },
    actionButtons: {
      flexDirection: 'row',
      gap: 16,
      marginTop: 20,
      paddingHorizontal: 4,
    },
    button: {
      flex: 1,
      paddingVertical: 18,
      borderRadius: 16,
      justifyContent: 'center',
      alignItems: 'center',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.2,
      shadowRadius: 8,
      elevation: 6,
    },
    saveButton: {
      backgroundColor: theme.PRIMARY,
    },
    cancelButton: {
      backgroundColor: theme.TEXT_SECONDARY + '80',
    },
    buttonText: {
      fontSize: 16,
      fontWeight: '700',
      color: '#fff',
    },
    statsSection: {
      marginTop: 32,
      padding: 20,
      backgroundColor: theme.SURFACE,
      borderRadius: 20,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 8,
      elevation: 4,
    },
    statRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 8,
    },
    statLabel: {
      fontSize: 14,
      color: theme.TEXT_SECONDARY,
      fontWeight: '500',
    },
    statValue: {
      fontSize: 14,
      color: theme.TEXT_PRIMARY,
      fontWeight: '600',
    },
  });

  const styles = getStyles(theme);

  // Seçilen avatar bilgisini al
  const currentAvatarInfo = allAvatars.find(a => a.icon === selectedAvatar) || allAvatars[0];

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerRow}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => navigation.goBack()}
            activeOpacity={0.7}
          >
            <MaterialIcons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          
          <Text style={styles.headerTitle}>Profil</Text>
          
          <TouchableOpacity 
            style={styles.editButton}
            onPress={() => setIsEditing(!isEditing)}
            activeOpacity={0.7}
          >
            <MaterialIcons 
              name={isEditing ? "close" : "edit"} 
              size={22} 
              color="#fff" 
            />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Profil Bölümü */}
        <View style={styles.profileSection}>
          <View style={[
            styles.currentAvatar, 
            { backgroundColor: currentAvatarInfo.color }
          ]}>
            <MaterialIcons 
              name={selectedAvatar} 
              size={48} 
              color="#fff" 
            />
          </View>
          
          <View style={styles.nameSection}>
            <TextInput
              style={styles.nameInput}
              value={userName}
              onChangeText={setUserName}
              editable={isEditing}
              placeholder="Kullanıcı Adı"
              placeholderTextColor={theme.TEXT_SECONDARY}
              maxLength={20}
            />
            <Text style={styles.nameLabel}>Kullanıcı Adı</Text>
          </View>
        </View>

        {/* Avatar Seçimi */}
        {isEditing && (
          <>
            <Text style={styles.sectionTitle}>✨ Avatar Seç</Text>
            {avatarCategories.map((category, index) => (
              <View key={index} style={styles.categorySection}>
                <Text style={styles.categoryTitle}>{category.title}</Text>
                <View style={styles.avatarGrid}>
                  {category.icons.map((avatar) => (
                    <AvatarSelector
                      key={avatar.icon}
                      avatar={avatar}
                      isSelected={selectedAvatar === avatar.icon}
                      onSelect={setSelectedAvatar}
                    />
                  ))}
                </View>
              </View>
            ))}
          </>
        )}

        {/* Profil İstatistikleri */}
        {!isEditing && (
          <View style={styles.statsSection}>
            <Text style={styles.sectionTitle}>📊 Profil Bilgileri</Text>
            <View style={styles.statRow}>
              <Text style={styles.statLabel}>Kayıt Tarihi</Text>
              <Text style={styles.statValue}>
                {userProfile.createdAt 
                  ? new Date(userProfile.createdAt).toLocaleDateString('tr-TR')
                  : 'Bilinmiyor'
                }
              </Text>
            </View>
            <View style={styles.statRow}>
              <Text style={styles.statLabel}>Son Güncelleme</Text>
              <Text style={styles.statValue}>
                {userProfile.updatedAt 
                  ? new Date(userProfile.updatedAt).toLocaleDateString('tr-TR')
                  : 'Hiç güncellenmemiş'
                }
              </Text>
            </View>
            <View style={styles.statRow}>
              <Text style={styles.statLabel}>Seçilen Avatar</Text>
              <Text style={styles.statValue}>{currentAvatarInfo.label}</Text>
            </View>
          </View>
        )}

        {/* Aksiyon Butonları */}
        {isEditing && (
          <Animated.View 
            style={[
              styles.actionButtons,
              {
                transform: [{
                  scale: saveAnimation.interpolate({
                    inputRange: [0, 1],
                    outputRange: [1, 0.95]
                  })
                }]
              }
            ]}
          >
            <TouchableOpacity 
              style={[styles.button, styles.cancelButton]}
              onPress={handleCancel}
              activeOpacity={0.8}
            >
              <Text style={styles.buttonText}>❌ İptal</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.button, styles.saveButton]}
              onPress={handleSave}
              activeOpacity={0.8}
            >
              <Text style={styles.buttonText}>✅ Kaydet</Text>
            </TouchableOpacity>
          </Animated.View>
        )}
      </ScrollView>
    </View>
  );
};

export default UserProfileScreen;
