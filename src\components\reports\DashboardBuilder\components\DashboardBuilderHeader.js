import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useTheme } from '../../../../context/ThemeContext';

/**
 * Dashboard Builder Header - Toolbar ve navigasyon
 */
const DashboardBuilderHeader = ({
  title = 'Dashboard Builder',
  canUndo = false,
  canRedo = false,
  onUndo,
  onRedo,
  onSave,
  onExport,
  onPreview,
  onWidgetLibrary,
  onLayoutManager,
  onClear,
  isPreviewMode = false,
}) => {
  const { theme } = useTheme();

  // Theme kontrolü
  if (!theme) {
    return null;
  }

  /**
   * Güvenli tema değeri alma
   * @param {string} property - Tema ö<PERSON>ği
   * @param {string} fallback - Varsay<PERSON><PERSON> değer
   * @returns {string} Gü<PERSON>li tema değeri
   */
  const getSafeThemeValue = (property, fallback) => {
    return theme[property] || theme.colors?.[property.toLowerCase()] || fallback;
  };

  /**
   * Dışa aktarma seçenekleri
   */
  const showExportOptions = () => {
    Alert.alert(
      'Dışa Aktar',
      'Hangi formatta dışa aktarmak istiyorsunuz?',
      [
        { text: 'İptal', style: 'cancel' },
        { text: 'PDF', onPress: () => onExport('pdf') },
        { text: 'PNG', onPress: () => onExport('png') },
        { text: 'Excel', onPress: () => onExport('excel') },
        { text: 'PowerPoint', onPress: () => onExport('powerpoint') },
      ]
    );
  };

  /**
   * Temizleme onayı
   */
  const confirmClear = () => {
    Alert.alert(
      'Dashboard Temizle',
      'Tüm widget\'ları silmek istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        { text: 'Temizle', onPress: onClear, style: 'destructive' },
      ]
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: getSafeThemeValue('SURFACE', '#f9f9f9') }]}>
      {/* Title */}
      <View style={styles.titleContainer}>
        <Text style={[styles.title, { color: getSafeThemeValue('TEXT_PRIMARY', '#000') }]}>
          {title}
        </Text>
        {isPreviewMode && (
          <View style={[styles.previewBadge, { backgroundColor: getSafeThemeValue('PRIMARY', '#007AFF') }]}>
            <Text style={[styles.previewBadgeText, { color: getSafeThemeValue('SURFACE', '#fff') }]}>
              Önizleme
            </Text>
          </View>
        )}
      </View>

      {/* Toolbar */}
      <View style={styles.toolbar}>
        {/* Left Section */}
        <View style={styles.toolbarSection}>
          <TouchableOpacity
            style={[
              styles.toolbarButton,
              { backgroundColor: getSafeThemeValue('PRIMARY', '#007AFF') },
            ]}
            onPress={onWidgetLibrary}
          >
            <Text style={[styles.toolbarButtonText, { color: getSafeThemeValue('SURFACE', '#fff') }]}>
              📦 Widget Ekle
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.toolbarButton,
              { backgroundColor: getSafeThemeValue('SECONDARY', '#6c757d') },
            ]}
            onPress={onLayoutManager}
          >
            <Text style={[styles.toolbarButtonText, { color: getSafeThemeValue('SURFACE', '#fff') }]}>
              📐 Layout
            </Text>
          </TouchableOpacity>
        </View>

        {/* Center Section */}
        <View style={styles.toolbarSection}>
          <TouchableOpacity
            style={[
              styles.toolbarButton,
              { 
                backgroundColor: canUndo ? getSafeThemeValue('SUCCESS', '#28a745') : getSafeThemeValue('DISABLED', '#ccc'),
              },
            ]}
            onPress={onUndo}
            disabled={!canUndo}
          >
            <Text style={[styles.toolbarButtonText, { color: getSafeThemeValue('SURFACE', '#fff') }]}>
              ↶
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.toolbarButton,
              { 
                backgroundColor: canRedo ? getSafeThemeValue('SUCCESS', '#28a745') : getSafeThemeValue('DISABLED', '#ccc'),
              },
            ]}
            onPress={onRedo}
            disabled={!canRedo}
          >
            <Text style={[styles.toolbarButtonText, { color: getSafeThemeValue('SURFACE', '#fff') }]}>
              ↷
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.toolbarButton,
              { backgroundColor: getSafeThemeValue('WARNING', '#ffc107') },
            ]}
            onPress={onPreview}
          >
            <Text style={[styles.toolbarButtonText, { color: getSafeThemeValue('TEXT_PRIMARY', '#000') }]}>
              👁️ {isPreviewMode ? 'Düzenle' : 'Önizle'}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Right Section */}
        <View style={styles.toolbarSection}>
          <TouchableOpacity
            style={[
              styles.toolbarButton,
              { backgroundColor: getSafeThemeValue('INFO', '#17a2b8') },
            ]}
            onPress={onSave}
          >
            <Text style={[styles.toolbarButtonText, { color: getSafeThemeValue('SURFACE', '#fff') }]}>
              💾 Kaydet
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.toolbarButton,
              { backgroundColor: getSafeThemeValue('ACCENT', '#6f42c1') },
            ]}
            onPress={showExportOptions}
          >
            <Text style={[styles.toolbarButtonText, { color: getSafeThemeValue('SURFACE', '#fff') }]}>
              📤 Dışa Aktar
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.toolbarButton,
              { backgroundColor: getSafeThemeValue('ERROR', '#dc3545') },
            ]}
            onPress={confirmClear}
          >
            <Text style={[styles.toolbarButtonText, { color: getSafeThemeValue('SURFACE', '#fff') }]}>
              🗑️
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    marginRight: 12,
  },
  previewBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  previewBadgeText: {
    fontSize: 10,
    fontWeight: '600',
  },
  toolbar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  toolbarSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  toolbarButton: {
    paddingHorizontal: 8,
    paddingVertical: 6,
    borderRadius: 6,
    minWidth: 36,
    alignItems: 'center',
  },
  toolbarButtonText: {
    fontSize: 11,
    fontWeight: '600',
  },
});

export default DashboardBuilderHeader;
