import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  PanGestureHandler,
  Animated,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const { width: screenWidth } = Dimensions.get('window');

/**
 * Drag & Drop Report Editor Component
 * Allows users to create reports by dragging and dropping components
 * Follows REPORTS_SCREEN_REDESIGN_PLAN.md specifications
 */
const DragDropEditor = ({ 
  onSave,
  onCancel,
  initialLayout = [],
  theme 
}) => {
  const [layout, setLayout] = useState(initialLayout);
  const [draggedItem, setDraggedItem] = useState(null);
  const [dropZones, setDropZones] = useState([]);
  const [isDragging, setIsDragging] = useState(false);

  // Available components for drag & drop
  const availableComponents = [
    {
      id: 'chart',
      type: 'chart',
      title: 'Grafik',
      icon: 'bar-chart',
      description: '<PERSON><PERSON><PERSON>, pasta veya çizgi grafik',
      defaultProps: {
        chartType: 'bar',
        dataSource: 'transactions',
        width: screenWidth - 32,
        height: 200,
      },
    },
    {
      id: 'table',
      type: 'table',
      title: 'Tablo',
      icon: 'grid',
      description: 'Veri tablosu',
      defaultProps: {
        dataSource: 'transactions',
        columns: ['date', 'amount', 'category'],
        width: screenWidth - 32,
        height: 300,
      },
    },
    {
      id: 'kpi',
      type: 'kpi',
      title: 'KPI Kartı',
      icon: 'speedometer',
      description: 'Anahtar performans göstergesi',
      defaultProps: {
        metric: 'total_income',
        format: 'currency',
        width: (screenWidth - 48) / 2,
        height: 120,
      },
    },
    {
      id: 'text',
      type: 'text',
      title: 'Metin',
      icon: 'text',
      description: 'Başlık veya açıklama metni',
      defaultProps: {
        content: 'Yeni metin',
        fontSize: 16,
        fontWeight: 'normal',
        width: screenWidth - 32,
        height: 50,
      },
    },
    {
      id: 'filter',
      type: 'filter',
      title: 'Filtre',
      icon: 'filter',
      description: 'Tarih veya kategori filtresi',
      defaultProps: {
        filterType: 'date',
        width: screenWidth - 32,
        height: 80,
      },
    },
    {
      id: 'summary',
      type: 'summary',
      title: 'Özet',
      icon: 'list',
      description: 'Finansal özet kartı',
      defaultProps: {
        summaryType: 'monthly',
        width: screenWidth - 32,
        height: 150,
      },
    },
  ];

  // Drag gesture handler
  const onGestureEvent = Animated.event(
    [{ nativeEvent: { translationX: 0, translationY: 0 } }],
    { useNativeDriver: false }
  );

  const onHandlerStateChange = (event, component) => {
    const { state, translationX, translationY } = event.nativeEvent;

    if (state === 5) { // ENDED
      setIsDragging(false);
      
      // Check if dropped in a valid zone
      const dropZone = findDropZone(translationX, translationY);
      if (dropZone) {
        addComponentToLayout(component, dropZone);
      }
      
      setDraggedItem(null);
    } else if (state === 2) { // BEGAN
      setIsDragging(true);
      setDraggedItem(component);
    }
  };

  const findDropZone = (x, y) => {
    // Simple drop zone detection - in a real implementation,
    // you would calculate actual positions
    return {
      x: Math.max(0, x),
      y: Math.max(0, y),
      index: layout.length,
    };
  };

  const addComponentToLayout = (component, dropZone) => {
    const newComponent = {
      id: `${component.type}_${Date.now()}`,
      type: component.type,
      title: component.title,
      props: { ...component.defaultProps },
      position: {
        x: dropZone.x,
        y: dropZone.y,
      },
    };

    setLayout(prev => [...prev, newComponent]);
  };

  const removeComponent = (componentId) => {
    setLayout(prev => prev.filter(comp => comp.id !== componentId));
  };

  const updateComponent = (componentId, updates) => {
    setLayout(prev => prev.map(comp => 
      comp.id === componentId 
        ? { ...comp, ...updates }
        : comp
    ));
  };

  const renderComponent = (component) => {
    const { type, title, props, position } = component;

    const componentStyle = {
      position: 'absolute',
      left: position?.x || 0,
      top: position?.y || 0,
      width: props.width,
      height: props.height,
      backgroundColor: theme.SURFACE,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: theme.BORDER,
      padding: 12,
    };

    return (
      <View key={component.id} style={componentStyle}>
        <View style={styles.componentHeader}>
          <Text style={[styles.componentTitle, { color: theme.TEXT_PRIMARY }]}>
            {title}
          </Text>
          <TouchableOpacity
            onPress={() => removeComponent(component.id)}
            style={styles.removeButton}
          >
            <Ionicons name="close" size={16} color={theme.ERROR} />
          </TouchableOpacity>
        </View>

        <View style={styles.componentContent}>
          {type === 'chart' && (
            <View style={styles.placeholder}>
              <Ionicons name="bar-chart" size={32} color={theme.TEXT_SECONDARY} />
              <Text style={[styles.placeholderText, { color: theme.TEXT_SECONDARY }]}>
                Grafik Alanı
              </Text>
            </View>
          )}

          {type === 'table' && (
            <View style={styles.placeholder}>
              <Ionicons name="grid" size={32} color={theme.TEXT_SECONDARY} />
              <Text style={[styles.placeholderText, { color: theme.TEXT_SECONDARY }]}>
                Tablo Alanı
              </Text>
            </View>
          )}

          {type === 'kpi' && (
            <View style={styles.placeholder}>
              <Ionicons name="speedometer" size={32} color={theme.TEXT_SECONDARY} />
              <Text style={[styles.placeholderText, { color: theme.TEXT_SECONDARY }]}>
                KPI Değeri
              </Text>
            </View>
          )}

          {type === 'text' && (
            <Text style={[styles.textContent, { color: theme.TEXT_PRIMARY }]}>
              {props.content}
            </Text>
          )}

          {type === 'filter' && (
            <View style={styles.placeholder}>
              <Ionicons name="filter" size={32} color={theme.TEXT_SECONDARY} />
              <Text style={[styles.placeholderText, { color: theme.TEXT_SECONDARY }]}>
                Filtre Alanı
              </Text>
            </View>
          )}

          {type === 'summary' && (
            <View style={styles.placeholder}>
              <Ionicons name="list" size={32} color={theme.TEXT_SECONDARY} />
              <Text style={[styles.placeholderText, { color: theme.TEXT_SECONDARY }]}>
                Özet Alanı
              </Text>
            </View>
          )}
        </View>
      </View>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.SURFACE }]}>
        <TouchableOpacity onPress={onCancel}>
          <Text style={[styles.cancelButton, { color: theme.ERROR }]}>
            İptal
          </Text>
        </TouchableOpacity>
        
        <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
          Rapor Editörü
        </Text>
        
        <TouchableOpacity 
          onPress={() => onSave(layout)}
          style={[styles.saveButton, { backgroundColor: theme.SUCCESS }]}
        >
          <Text style={[styles.saveButtonText, { color: theme.SURFACE }]}>
            Kaydet
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        {/* Component Palette */}
        <View style={[styles.palette, { backgroundColor: theme.SURFACE }]}>
          <Text style={[styles.paletteTitle, { color: theme.TEXT_PRIMARY }]}>
            Bileşenler
          </Text>
          
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {availableComponents.map((component) => (
              <PanGestureHandler
                key={component.id}
                onGestureEvent={onGestureEvent}
                onHandlerStateChange={(event) => onHandlerStateChange(event, component)}
              >
                <Animated.View>
                  <TouchableOpacity
                    style={[
                      styles.componentItem,
                      { 
                        backgroundColor: theme.BACKGROUND,
                        borderColor: theme.BORDER,
                        opacity: draggedItem?.id === component.id ? 0.5 : 1
                      }
                    ]}
                  >
                    <Ionicons 
                      name={component.icon} 
                      size={24} 
                      color={theme.PRIMARY} 
                    />
                    <Text style={[styles.componentItemTitle, { color: theme.TEXT_PRIMARY }]}>
                      {component.title}
                    </Text>
                    <Text style={[styles.componentItemDesc, { color: theme.TEXT_SECONDARY }]}>
                      {component.description}
                    </Text>
                  </TouchableOpacity>
                </Animated.View>
              </PanGestureHandler>
            ))}
          </ScrollView>
        </View>

        {/* Canvas */}
        <View style={[styles.canvas, { backgroundColor: theme.BACKGROUND }]}>
          {layout.length === 0 ? (
            <View style={styles.emptyCanvas}>
              <Ionicons name="add-circle-outline" size={48} color={theme.TEXT_SECONDARY} />
              <Text style={[styles.emptyCanvasText, { color: theme.TEXT_SECONDARY }]}>
                Rapor oluşturmak için yukarıdaki bileşenleri buraya sürükleyin
              </Text>
            </View>
          ) : (
            <ScrollView style={styles.canvasScroll}>
              <View style={styles.canvasContent}>
                {layout.map(renderComponent)}
              </View>
            </ScrollView>
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  cancelButton: {
    fontSize: 16,
    fontWeight: '600',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  palette: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  paletteTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  componentItem: {
    width: 120,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginRight: 12,
    alignItems: 'center',
  },
  componentItemTitle: {
    fontSize: 12,
    fontWeight: '600',
    marginTop: 8,
    textAlign: 'center',
  },
  componentItemDesc: {
    fontSize: 10,
    marginTop: 4,
    textAlign: 'center',
  },
  canvas: {
    flex: 1,
    position: 'relative',
  },
  emptyCanvas: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyCanvasText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 16,
  },
  canvasScroll: {
    flex: 1,
  },
  canvasContent: {
    minHeight: 600,
    position: 'relative',
  },
  componentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  componentTitle: {
    fontSize: 14,
    fontWeight: '600',
  },
  removeButton: {
    padding: 4,
  },
  componentContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholder: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  placeholderText: {
    fontSize: 12,
    marginTop: 8,
  },
  textContent: {
    fontSize: 14,
    textAlign: 'center',
  },
});

export default DragDropEditor;
