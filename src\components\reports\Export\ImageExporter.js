import { captureRef } from 'react-native-view-shot';
import * as Sharing from 'expo-sharing';
import { Alert } from 'react-native';

/**
 * Image Exporter - Görsel dosyaları oluşturur
 */
export class ImageExporter {

  /**
   * Görsel dosyası oluşturur
   * @param {Object} params - Görsel parametreleri
   * @returns {Promise<Object>} Görsel oluşturma sonucu
   */
  static async exportToImage({ data, title, type, config = {} }) {
    try {
      if (!config.viewRef) {
        return {
          success: false,
          message: 'Görsel oluşturmak için view referansı gerekli',
          filePath: null
        };
      }

      const uri = await captureRef(config.viewRef, {
        format: config.format || 'png',
        quality: config.quality || 0.8,
        result: 'tmpfile',
        height: config.height,
        width: config.width,
      });

      // Share the image
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(uri, {
          mimeType: `image/${config.format || 'png'}`,
          dialogTitle: 'Görsel Raporu Paylaş',
        });
      }

      return {
        success: true,
        message: 'Görsel başarıyla oluşturuldu',
        filePath: uri
      };
    } catch (error) {
      console.error('Image export error:', error);
      return {
        success: false,
        message: `Görsel oluşturulurken hata oluştu: ${error.message}`,
        filePath: null
      };
    }
  }

  /**
   * Chart görselini yakalar
   * @param {Object} chartRef - Chart referansı
   * @param {Object} config - Yapılandırma
   * @returns {Promise<Object>} Sonuç
   */
  static async captureChart(chartRef, config = {}) {
    try {
      if (!chartRef) {
        throw new Error('Chart referansı bulunamadı');
      }

      const uri = await captureRef(chartRef, {
        format: 'png',
        quality: 1.0,
        result: 'tmpfile',
        ...config
      });

      return {
        success: true,
        uri: uri
      };
    } catch (error) {
      console.error('Chart capture error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}
