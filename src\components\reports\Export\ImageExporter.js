import { Platform } from 'react-native';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import { captureRef } from 'react-native-view-shot';

/**
 * Image Exporter - Raporları resim formatında dışa aktarır
 * React Native'de view-shot kullanarak component'leri resme dönüştürür
 */
export class ImageExporter {
  
  /**
   * Resim formatında dışa aktarma
   * @param {Object} params - Resim parametreleri
   * @param {Object} params.data - Rapor verisi
   * @param {string} params.title - Rapor başlığı
   * @param {string} params.type - Rapor türü
   * @param {Object} params.config - Resim yapılandırması
   * @param {Object} params.viewRef - Component referansı (react-native-view-shot için)
   * @returns {Promise<Object>} Resim dışa aktarma sonucu
   */
  static async exportToImage({ data, title, type, config = {}, viewRef }) {
    try {
      // Resim yapılandırması
      const imageConfig = {
        format: config.format || 'png',
        quality: config.quality || 1.0,
        result: config.result || 'tmpfile',
        height: config.height || 1080,
        width: config.width || 1920,
        backgroundColor: config.backgroundColor || '#ffffff',
        ...config
      };

      let imageUri;

      if (viewRef) {
        // Component referansı varsa view-shot kullan
        imageUri = await this.captureView(viewRef, imageConfig);
      } else {
        // Component referansı yoksa Canvas API ile oluştur
        imageUri = await this.generateImageCanvas({
          data,
          title,
          type,
          config: imageConfig
        });
      }

      // Dosya adını oluştur
      const fileName = this.generateFileName(title, type, imageConfig.format);
      const finalUri = `${FileSystem.documentDirectory}${fileName}`;

      // Dosyayı kopyala
      await FileSystem.moveAsync({
        from: imageUri,
        to: finalUri
      });

      // Dosyayı paylaş
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(finalUri, {
          mimeType: `image/${imageConfig.format}`,
          dialogTitle: 'Rapor Resmi Paylaş'
        });
      }

      return {
        success: true,
        uri: finalUri,
        fileName
      };

    } catch (error) {
      console.error('Image Export Error:', error);
      return {
        success: false,
        error: error.message || 'Resim oluşturulurken bilinmeyen hata'
      };
    }
  }

  /**
   * View component'ini yakalayıp resme dönüştürür
   * @param {Object} viewRef - Component referansı
   * @param {Object} config - Resim yapılandırması
   * @returns {Promise<string>} Resim URI'si
   */
  static async captureView(viewRef, config) {
    try {
      const uri = await captureRef(viewRef, {
        format: config.format,
        quality: config.quality,
        result: config.result,
        height: config.height,
        width: config.width,
        backgroundColor: config.backgroundColor
      });
      
      return uri;
    } catch (error) {
      console.error('View Capture Error:', error);
      throw new Error('Component görüntüsü yakalanamadı');
    }
  }

  /**
   * Canvas API ile resim oluşturur (web için)
   * @param {Object} params - Canvas parametreleri
   * @returns {Promise<string>} Resim URI'si
   */
  static async generateImageCanvas({ data, title, type, config }) {
    try {
      // React Native'de canvas API yok, HTML5 Canvas simülasyonu
      if (Platform.OS === 'web') {
        return await this.generateWebCanvas({ data, title, type, config });
      } else {
        // Mobile için SVG tabanlı alternatif
        return await this.generateSVGImage({ data, title, type, config });
      }
    } catch (error) {
      console.error('Canvas Generation Error:', error);
      throw new Error('Canvas resmi oluşturulamadı');
    }
  }

  /**
   * Web için HTML5 Canvas kullanarak resim oluşturur
   * @param {Object} params - Canvas parametreleri
   * @returns {Promise<string>} Resim URI'si
   */
  static async generateWebCanvas({ data, title, type, config }) {
    return new Promise((resolve, reject) => {
      try {
        const canvas = document.createElement('canvas');
        canvas.width = config.width;
        canvas.height = config.height;
        const ctx = canvas.getContext('2d');

        // Arka plan
        ctx.fillStyle = config.backgroundColor;
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Başlık
        ctx.fillStyle = '#333';
        ctx.font = 'bold 48px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(title, canvas.width / 2, 80);

        // Tarih
        ctx.fillStyle = '#666';
        ctx.font = '24px Arial';
        ctx.fillText(
          `Oluşturulma: ${new Date().toLocaleDateString('tr-TR')}`,
          canvas.width / 2,
          120
        );

        // Rapor içeriği
        this.drawReportContent(ctx, data, type, config);

        // Canvas'ı blob'a dönüştür
        canvas.toBlob((blob) => {
          const reader = new FileReader();
          reader.onload = () => resolve(reader.result);
          reader.onerror = reject;
          reader.readAsDataURL(blob);
        }, `image/${config.format}`, config.quality);

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * SVG tabanlı resim oluşturur (mobile için)
   * @param {Object} params - SVG parametreleri
   * @returns {Promise<string>} Resim URI'si
   */
  static async generateSVGImage({ data, title, type, config }) {
    const svgContent = this.generateSVGContent({ data, title, type, config });
    
    // SVG'yi dosya olarak kaydet
    const fileName = `temp_${Date.now()}.svg`;
    const tempUri = `${FileSystem.cacheDirectory}${fileName}`;
    
    await FileSystem.writeAsStringAsync(tempUri, svgContent, {
      encoding: FileSystem.EncodingType.UTF8
    });

    // SVG'yi PNG'ye dönüştür (gelecekte implementasyon)
    // Şimdilik SVG döndür
    return tempUri;
  }

  /**
   * SVG içeriğini oluşturur
   * @param {Object} params - SVG parametreleri
   * @returns {string} SVG içeriği
   */
  static generateSVGContent({ data, title, type, config }) {
    const width = config.width;
    const height = config.height;
    const backgroundColor = config.backgroundColor;

    let svgContent = `
      <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="${backgroundColor}"/>
        
        <!-- Başlık -->
        <text x="${width / 2}" y="80" text-anchor="middle" 
              font-family="Arial, sans-serif" font-size="48" font-weight="bold" fill="#333">
          ${title}
        </text>
        
        <!-- Tarih -->
        <text x="${width / 2}" y="120" text-anchor="middle" 
              font-family="Arial, sans-serif" font-size="24" fill="#666">
          Oluşturulma: ${new Date().toLocaleDateString('tr-TR')}
        </text>
    `;

    // Rapor türüne göre içerik ekle
    svgContent += this.generateSVGReportContent({ data, type, config, width, height });

    svgContent += '</svg>';
    return svgContent;
  }

  /**
   * Rapor içeriğini SVG formatında oluşturur
   * @param {Object} params - SVG rapor parametreleri
   * @returns {string} SVG rapor içeriği
   */
  static generateSVGReportContent({ data, type, config, width, height }) {
    const startY = 200;
    let content = '';

    switch (type) {
      case 'monthly-income-expense':
        content = this.generateMonthlyIncomeExpenseSVG(data, width, height, startY);
        break;
      case 'basic-summary':
        content = this.generateBasicSummarySVG(data, width, height, startY);
        break;
      case 'category-distribution':
        content = this.generateCategoryDistributionSVG(data, width, height, startY);
        break;
      default:
        content = this.generateGenericSVG(data, width, height, startY);
    }

    return content;
  }

  /**
   * Aylık gelir-gider SVG içeriği
   */
  static generateMonthlyIncomeExpenseSVG(data, width, height, startY) {
    const { summary } = data;
    const cardWidth = 300;
    const cardHeight = 120;
    const cardSpacing = 50;
    const totalCardsWidth = cardWidth * 3 + cardSpacing * 2;
    const startX = (width - totalCardsWidth) / 2;

    let content = '';

    // Toplam Gelir kartı
    content += `
      <rect x="${startX}" y="${startY}" width="${cardWidth}" height="${cardHeight}" 
            fill="#27ae60" rx="10" ry="10"/>
      <text x="${startX + cardWidth / 2}" y="${startY + 40}" text-anchor="middle" 
            font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="white">
        ₺${summary.totalIncome?.toLocaleString('tr-TR') || '0'}
      </text>
      <text x="${startX + cardWidth / 2}" y="${startY + 70}" text-anchor="middle" 
            font-family="Arial, sans-serif" font-size="18" fill="white">
        Toplam Gelir
      </text>
    `;

    // Toplam Gider kartı
    content += `
      <rect x="${startX + cardWidth + cardSpacing}" y="${startY}" width="${cardWidth}" height="${cardHeight}" 
            fill="#e74c3c" rx="10" ry="10"/>
      <text x="${startX + cardWidth + cardSpacing + cardWidth / 2}" y="${startY + 40}" text-anchor="middle" 
            font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="white">
        ₺${summary.totalExpense?.toLocaleString('tr-TR') || '0'}
      </text>
      <text x="${startX + cardWidth + cardSpacing + cardWidth / 2}" y="${startY + 70}" text-anchor="middle" 
            font-family="Arial, sans-serif" font-size="18" fill="white">
        Toplam Gider
      </text>
    `;

    // Net Gelir kartı
    const netColor = (summary.netIncome || 0) >= 0 ? '#27ae60' : '#e74c3c';
    content += `
      <rect x="${startX + (cardWidth + cardSpacing) * 2}" y="${startY}" width="${cardWidth}" height="${cardHeight}" 
            fill="${netColor}" rx="10" ry="10"/>
      <text x="${startX + (cardWidth + cardSpacing) * 2 + cardWidth / 2}" y="${startY + 40}" text-anchor="middle" 
            font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="white">
        ₺${summary.netIncome?.toLocaleString('tr-TR') || '0'}
      </text>
      <text x="${startX + (cardWidth + cardSpacing) * 2 + cardWidth / 2}" y="${startY + 70}" text-anchor="middle" 
            font-family="Arial, sans-serif" font-size="18" fill="white">
        Net Gelir
      </text>
    `;

    return content;
  }

  /**
   * Temel özet SVG içeriği
   */
  static generateBasicSummarySVG(data, width, height, startY) {
    const { summary } = data;
    const cardWidth = 200;
    const cardHeight = 100;
    const cardSpacing = 30;
    const totalCardsWidth = cardWidth * 4 + cardSpacing * 3;
    const startX = (width - totalCardsWidth) / 2;

    let content = '';

    const metrics = [
      { label: 'Toplam Gelir', value: summary.totalIncome, color: '#27ae60' },
      { label: 'Toplam Gider', value: summary.totalExpense, color: '#e74c3c' },
      { label: 'Ort. Gelir', value: summary.averageIncome, color: '#3498db' },
      { label: 'Ort. Gider', value: summary.averageExpense, color: '#f39c12' }
    ];

    metrics.forEach((metric, index) => {
      const x = startX + (cardWidth + cardSpacing) * index;
      content += `
        <rect x="${x}" y="${startY}" width="${cardWidth}" height="${cardHeight}" 
              fill="${metric.color}" rx="8" ry="8"/>
        <text x="${x + cardWidth / 2}" y="${startY + 35}" text-anchor="middle" 
              font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="white">
          ₺${metric.value?.toLocaleString('tr-TR') || '0'}
        </text>
        <text x="${x + cardWidth / 2}" y="${startY + 60}" text-anchor="middle" 
              font-family="Arial, sans-serif" font-size="14" fill="white">
          ${metric.label}
        </text>
      `;
    });

    return content;
  }

  /**
   * Kategori dağılımı SVG içeriği
   */
  static generateCategoryDistributionSVG(data, width, height, startY) {
    const { categories } = data;
    
    if (!categories || categories.length === 0) {
      return `
        <text x="${width / 2}" y="${startY + 50}" text-anchor="middle" 
              font-family="Arial, sans-serif" font-size="24" fill="#666">
          Kategori verisi bulunamadı
        </text>
      `;
    }

    let content = '';
    const barWidth = Math.min(600, width - 200);
    const barHeight = 40;
    const barSpacing = 10;
    const startX = (width - barWidth) / 2;

    // Maksimum değeri bul
    const maxValue = Math.max(...categories.map(cat => cat.amount || 0));

    categories.slice(0, 10).forEach((category, index) => {
      const y = startY + (barHeight + barSpacing) * index;
      const currentBarWidth = maxValue > 0 ? (category.amount / maxValue) * barWidth : 0;
      const color = category.type === 'income' ? '#27ae60' : '#e74c3c';

      content += `
        <!-- Kategori çubuğu -->
        <rect x="${startX}" y="${y}" width="${currentBarWidth}" height="${barHeight}" 
              fill="${color}" rx="4" ry="4"/>
        
        <!-- Kategori adı -->
        <text x="${startX - 10}" y="${y + barHeight / 2 + 5}" text-anchor="end" 
              font-family="Arial, sans-serif" font-size="14" fill="#333">
          ${category.name}
        </text>
        
        <!-- Değer -->
        <text x="${startX + currentBarWidth + 10}" y="${y + barHeight / 2 + 5}" 
              font-family="Arial, sans-serif" font-size="14" fill="#333">
          ₺${category.amount?.toLocaleString('tr-TR') || '0'}
        </text>
      `;
    });

    return content;
  }

  /**
   * Genel SVG içeriği
   */
  static generateGenericSVG(data, width, height, startY) {
    return `
      <rect x="${width / 2 - 200}" y="${startY}" width="400" height="200" 
            fill="#f8f9fa" stroke="#ddd" stroke-width="2" rx="10" ry="10"/>
      <text x="${width / 2}" y="${startY + 100}" text-anchor="middle" 
            font-family="Arial, sans-serif" font-size="24" fill="#666">
        📊 Veri Görselleştirmesi
      </text>
      <text x="${width / 2}" y="${startY + 130}" text-anchor="middle" 
            font-family="Arial, sans-serif" font-size="16" fill="#999">
        Rapor verileri burada gösterilecek
      </text>
    `;
  }

  /**
   * Web Canvas'ta rapor içeriği çizer
   * @param {CanvasRenderingContext2D} ctx - Canvas context
   * @param {Object} data - Rapor verisi
   * @param {string} type - Rapor türü
   * @param {Object} config - Yapılandırma
   */
  static drawReportContent(ctx, data, type, config) {
    const startY = 200;
    
    switch (type) {
      case 'monthly-income-expense':
        this.drawMonthlyIncomeExpenseCanvas(ctx, data, startY, config);
        break;
      case 'basic-summary':
        this.drawBasicSummaryCanvas(ctx, data, startY, config);
        break;
      default:
        this.drawGenericCanvas(ctx, data, startY, config);
    }
  }

  /**
   * Aylık gelir-gider Canvas çizimi
   */
  static drawMonthlyIncomeExpenseCanvas(ctx, data, startY, config) {
    const { summary } = data;
    const cardWidth = 300;
    const cardHeight = 120;
    const cardSpacing = 50;
    const totalCardsWidth = cardWidth * 3 + cardSpacing * 2;
    const startX = (config.width - totalCardsWidth) / 2;

    // Toplam Gelir kartı
    ctx.fillStyle = '#27ae60';
    ctx.fillRect(startX, startY, cardWidth, cardHeight);
    ctx.fillStyle = 'white';
    ctx.font = 'bold 32px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(`₺${summary.totalIncome?.toLocaleString('tr-TR') || '0'}`, startX + cardWidth / 2, startY + 50);
    ctx.font = '18px Arial';
    ctx.fillText('Toplam Gelir', startX + cardWidth / 2, startY + 80);

    // Toplam Gider kartı
    ctx.fillStyle = '#e74c3c';
    ctx.fillRect(startX + cardWidth + cardSpacing, startY, cardWidth, cardHeight);
    ctx.fillStyle = 'white';
    ctx.font = 'bold 32px Arial';
    ctx.fillText(`₺${summary.totalExpense?.toLocaleString('tr-TR') || '0'}`, startX + cardWidth + cardSpacing + cardWidth / 2, startY + 50);
    ctx.font = '18px Arial';
    ctx.fillText('Toplam Gider', startX + cardWidth + cardSpacing + cardWidth / 2, startY + 80);

    // Net Gelir kartı
    const netColor = (summary.netIncome || 0) >= 0 ? '#27ae60' : '#e74c3c';
    ctx.fillStyle = netColor;
    ctx.fillRect(startX + (cardWidth + cardSpacing) * 2, startY, cardWidth, cardHeight);
    ctx.fillStyle = 'white';
    ctx.font = 'bold 32px Arial';
    ctx.fillText(`₺${summary.netIncome?.toLocaleString('tr-TR') || '0'}`, startX + (cardWidth + cardSpacing) * 2 + cardWidth / 2, startY + 50);
    ctx.font = '18px Arial';
    ctx.fillText('Net Gelir', startX + (cardWidth + cardSpacing) * 2 + cardWidth / 2, startY + 80);
  }

  /**
   * Temel özet Canvas çizimi
   */
  static drawBasicSummaryCanvas(ctx, data, startY, config) {
    const { summary } = data;
    const cardWidth = 200;
    const cardHeight = 100;
    const cardSpacing = 30;
    const totalCardsWidth = cardWidth * 4 + cardSpacing * 3;
    const startX = (config.width - totalCardsWidth) / 2;

    const metrics = [
      { label: 'Toplam Gelir', value: summary.totalIncome, color: '#27ae60' },
      { label: 'Toplam Gider', value: summary.totalExpense, color: '#e74c3c' },
      { label: 'Ort. Gelir', value: summary.averageIncome, color: '#3498db' },
      { label: 'Ort. Gider', value: summary.averageExpense, color: '#f39c12' }
    ];

    metrics.forEach((metric, index) => {
      const x = startX + (cardWidth + cardSpacing) * index;
      ctx.fillStyle = metric.color;
      ctx.fillRect(x, startY, cardWidth, cardHeight);
      ctx.fillStyle = 'white';
      ctx.font = 'bold 24px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(`₺${metric.value?.toLocaleString('tr-TR') || '0'}`, x + cardWidth / 2, startY + 40);
      ctx.font = '14px Arial';
      ctx.fillText(metric.label, x + cardWidth / 2, startY + 65);
    });
  }

  /**
   * Genel Canvas çizimi
   */
  static drawGenericCanvas(ctx, data, startY, config) {
    ctx.fillStyle = '#f8f9fa';
    ctx.fillRect(config.width / 2 - 200, startY, 400, 200);
    ctx.strokeStyle = '#ddd';
    ctx.lineWidth = 2;
    ctx.strokeRect(config.width / 2 - 200, startY, 400, 200);
    
    ctx.fillStyle = '#666';
    ctx.font = '24px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('📊 Veri Görselleştirmesi', config.width / 2, startY + 100);
    
    ctx.font = '16px Arial';
    ctx.fillStyle = '#999';
    ctx.fillText('Rapor verileri burada gösterilecek', config.width / 2, startY + 130);
  }

  /**
   * Dosya adı oluşturur
   * @param {string} title - Rapor başlığı
   * @param {string} type - Rapor türü
   * @param {string} format - Resim formatı
   * @returns {string} Dosya adı
   */
  static generateFileName(title, type, format = 'png') {
    const date = new Date();
    const dateStr = date.toISOString().split('T')[0];
    const timeStr = date.toTimeString().split(' ')[0].replace(/:/g, '-');
    
    const cleanTitle = title
      .replace(/[^a-zA-Z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .toLowerCase();
    
    return `${cleanTitle}-${dateStr}-${timeStr}.${format}`;
  }

  /**
   * Resim boyutlarını optimize eder
   * @param {Object} config - Yapılandırma
   * @returns {Object} Optimize edilmiş yapılandırma
   */
  static optimizeImageSize(config) {
    const { width, height } = config;
    const maxWidth = 1920;
    const maxHeight = 1080;
    
    if (width > maxWidth || height > maxHeight) {
      const aspectRatio = width / height;
      
      if (aspectRatio > maxWidth / maxHeight) {
        return {
          ...config,
          width: maxWidth,
          height: Math.round(maxWidth / aspectRatio)
        };
      } else {
        return {
          ...config,
          width: Math.round(maxHeight * aspectRatio),
          height: maxHeight
        };
      }
    }
    
    return config;
  }
}
