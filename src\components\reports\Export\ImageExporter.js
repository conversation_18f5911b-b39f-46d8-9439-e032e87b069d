/**
 * Image Exporter - Görsel dosyaları oluşturur
 * Temporary mock implementation for testing ExportManager
 */
export class ImageExporter {
  
  /**
   * Görsel dosyası oluşturur
   * @param {Object} params - Görsel parametreleri
   * @returns {Promise<Object>} Görsel oluşturma sonucu
   */
  static async exportToImage({ data, title, type, config = {} }) {
    console.log('🔍 ImageExporter.exportToImage called with:', { title, type });
    
    // Temporary mock implementation
    return {
      success: true,
      message: 'Image export mocked (packages removed for testing)',
      filePath: null
    };
  }
}
