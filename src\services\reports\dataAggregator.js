/**
 * Data Aggregator Service - Veri Toplama ve Analiz Servisi
 * Finansal verileri toplar, analiz eder ve raporlar için hazırlar
 */

let dbInstance = null;

/**
 * Database instance'ı set eder
 * @param {Object} db - Database instance
 */
export const setDatabaseInstance = (db) => {
  dbInstance = db;
};

/**
 * Database instance'ı getirir
 * @returns {Object} Database instance
 */
const getDb = () => {
  if (!dbInstance) {
    throw new Error('Database instance not set. Call setDatabaseInstance first.');
  }
  return dbInstance;
};

/**
 * <PERSON>irli tarih aralığında işlem verilerini getirir
 * @param {Object} params - Parametreler
 * @param {string} params.startDate - Başlangıç tarihi (YYYY-MM-DD)
 * @param {string} params.endDate - Bitiş tarihi (YYYY-MM-DD)
 * @param {Array} params.categories - Kategori filtreleri
 * @param {Array} params.types - İşlem türü filtreleri ('income', 'expense')
 * @param {number} params.minAmount - Minimum tutar
 * @param {number} params.maxAmount - Maksimum tutar
 * @returns {Array} İşlem verileri
 */
export const getTransactionData = async (params = {}) => {
  try {
    const db = getDb();
    let query = 'SELECT * FROM transactions WHERE 1=1';
    const sqlParams = [];

    // Tarih aralığı filtresi
    if (params.startDate) {
      query += ' AND date >= ?';
      sqlParams.push(params.startDate);
    }
    if (params.endDate) {
      query += ' AND date <= ?';
      sqlParams.push(params.endDate);
    }

    // Kategori filtresi
    if (params.categories && params.categories.length > 0) {
      const placeholders = params.categories.map(() => '?').join(',');
      query += ` AND category IN (${placeholders})`;
      sqlParams.push(...params.categories);
    }

    // İşlem türü filtresi
    if (params.types && params.types.length > 0) {
      const placeholders = params.types.map(() => '?').join(',');
      query += ` AND type IN (${placeholders})`;
      sqlParams.push(...params.types);
    }

    // Tutar aralığı filtresi
    if (params.minAmount !== undefined) {
      query += ' AND amount >= ?';
      sqlParams.push(params.minAmount);
    }
    if (params.maxAmount !== undefined) {
      query += ' AND amount <= ?';
      sqlParams.push(params.maxAmount);
    }

    query += ' ORDER BY date DESC';

    const result = await db.getAllAsync(query, sqlParams);
    return result;
  } catch (error) {
    console.error('❌ İşlem verileri getirilirken hata:', error);
    throw error;
  }
};

/**
 * Maaş verilerini getirir
 * @param {Object} params - Parametreler
 * @returns {Array} Maaş verileri
 */
export const getSalaryData = async (params = {}) => {
  try {
    const db = getDb();
    let query = 'SELECT * FROM salaries WHERE 1=1';
    const sqlParams = [];    // Tarih aralığı filtresi
    if (params.startDate) {
      query += ' AND date >= ?';
      sqlParams.push(params.startDate);
    }
    if (params.endDate) {
      query += ' AND date <= ?';
      sqlParams.push(params.endDate);
    }
    
    query += ' ORDER BY date DESC';

    const result = await db.getAllAsync(query, sqlParams);
    return result;
  } catch (error) {
    console.error('❌ Maaş verileri getirilirken hata:', error);
    throw error;
  }
};

/**
 * Mesai verilerini getirir
 * @param {Object} params - Parametreler
 * @returns {Array} Mesai verileri
 */
export const getOvertimeData = async (params = {}) => {
  try {
    const db = getDb();
    let query = 'SELECT * FROM overtime_shifts WHERE 1=1';
    const sqlParams = [];

    // Tarih aralığı filtresi
    if (params.startDate) {
      query += ' AND shift_date >= ?';
      sqlParams.push(params.startDate);
    }
    if (params.endDate) {
      query += ' AND shift_date <= ?';
      sqlParams.push(params.endDate);
    }

    query += ' ORDER BY shift_date DESC';

    const result = await db.getAllAsync(query, sqlParams);
    return result;
  } catch (error) {
    console.error('❌ Mesai verileri getirilirken hata:', error);
    throw error;
  }
};

/**
 * Bütçe verilerini getirir
 * @param {Object} params - Parametreler
 * @returns {Array} Bütçe verileri
 */
export const getBudgetData = async (params = {}) => {
  try {
    const db = getDb();
    let query = 'SELECT * FROM budgets WHERE 1=1';
    const sqlParams = [];

    // Tarih aralığı filtresi
    if (params.startDate) {
      query += ' AND start_date >= ?';
      sqlParams.push(params.startDate);
    }
    if (params.endDate) {
      query += ' AND end_date <= ?';
      sqlParams.push(params.endDate);
    }

    query += ' ORDER BY start_date DESC';

    const result = await db.getAllAsync(query, sqlParams);
    return result;
  } catch (error) {
    console.error('❌ Bütçe verileri getirilirken hata:', error);
    throw error;
  }
};

/**
 * Aylık gelir-gider analizini yapar
 * @param {Object} params - Parametreler
 * @returns {Object} Aylık analiz verisi
 */
export const getMonthlyIncomeExpenseAnalysis = async (params = {}) => {
  try {
    const db = getDb();
    
    // Varsayılan tarih aralığı - son 12 ay
    const endDate = params.endDate || new Date().toISOString().split('T')[0];
    const startDate = params.startDate || new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    // Aylık gelir-gider toplamları
    const monthlyData = await db.getAllAsync(`
      SELECT 
        strftime('%Y-%m', date) as month,
        SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END) as total_income,
        SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as total_expense,
        COUNT(*) as transaction_count
      FROM transactions 
      WHERE date >= ? AND date <= ?
      GROUP BY strftime('%Y-%m', date)
      ORDER BY month
    `, [startDate, endDate]);

    // Maaş verilerini ekle
    const salaryData = await db.getAllAsync(`
      SELECT 
        strftime('%Y-%m', date) as month,
        SUM(net_salary) as salary_total
      FROM salaries 
      WHERE date >= ? AND date <= ?
      GROUP BY strftime('%Y-%m', date)
      ORDER BY month
    `, [startDate, endDate]);

    // Mesai verilerini ekle
    const overtimeData = await db.getAllAsync(`
      SELECT 
        strftime('%Y-%m', shift_date) as month,
        SUM(total_payment) as overtime_total
      FROM overtime_shifts 
      WHERE shift_date >= ? AND shift_date <= ?
      GROUP BY strftime('%Y-%m', shift_date)
      ORDER BY month
    `, [startDate, endDate]);

    // Verileri birleştir
    const combinedData = monthlyData.map(month => {
      const salary = salaryData.find(s => s.month === month.month);
      const overtime = overtimeData.find(o => o.month === month.month);
      
      return {
        month: month.month,
        totalIncome: month.total_income + (salary?.salary_total || 0) + (overtime?.overtime_total || 0),
        totalExpense: month.total_expense,
        netAmount: (month.total_income + (salary?.salary_total || 0) + (overtime?.overtime_total || 0)) - month.total_expense,
        transactionCount: month.transaction_count,
        salaryIncome: salary?.salary_total || 0,
        overtimeIncome: overtime?.overtime_total || 0,
        regularIncome: month.total_income
      };
    });

    // Özet istatistikler
    const summary = {
      totalIncome: combinedData.reduce((sum, item) => sum + item.totalIncome, 0),
      totalExpense: combinedData.reduce((sum, item) => sum + item.totalExpense, 0),
      netAmount: combinedData.reduce((sum, item) => sum + item.netAmount, 0),
      avgMonthlyIncome: combinedData.reduce((sum, item) => sum + item.totalIncome, 0) / combinedData.length || 0,
      avgMonthlyExpense: combinedData.reduce((sum, item) => sum + item.totalExpense, 0) / combinedData.length || 0,
      monthCount: combinedData.length
    };

    return {
      monthlyData: combinedData,
      summary
    };
  } catch (error) {
    console.error('❌ Aylık gelir-gider analizi yapılırken hata:', error);
    throw error;
  }
};

/**
 * Kategori dağılım analizini yapar
 * @param {Object} params - Parametreler
 * @returns {Object} Kategori analiz verisi
 */
export const getCategoryDistributionAnalysis = async (params = {}) => {
  try {
    const db = getDb();
    
    // Varsayılan tarih aralığı - son 3 ay
    const endDate = params.endDate || new Date().toISOString().split('T')[0];
    const startDate = params.startDate || new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    // Kategori bazlı analiz
    const categoryData = await db.getAllAsync(`
      SELECT 
        category,
        type,
        COUNT(*) as transaction_count,
        SUM(amount) as total_amount,
        AVG(amount) as avg_amount,
        MIN(amount) as min_amount,
        MAX(amount) as max_amount
      FROM transactions 
      WHERE date >= ? AND date <= ?
      GROUP BY category, type
      ORDER BY total_amount DESC
    `, [startDate, endDate]);

    // Sadece gider kategorilerini al
    const expenseCategories = categoryData.filter(cat => cat.type === 'expense');
    const incomeCategories = categoryData.filter(cat => cat.type === 'income');

    // Toplam gider
    const totalExpense = expenseCategories.reduce((sum, cat) => sum + cat.total_amount, 0);
    const totalIncome = incomeCategories.reduce((sum, cat) => sum + cat.total_amount, 0);

    // Yüzde hesapla
    const expenseWithPercentage = expenseCategories.map(cat => ({
      ...cat,
      percentage: totalExpense > 0 ? (cat.total_amount / totalExpense) * 100 : 0
    }));

    const incomeWithPercentage = incomeCategories.map(cat => ({
      ...cat,
      percentage: totalIncome > 0 ? (cat.total_amount / totalIncome) * 100 : 0
    }));

    // En yüksek harcama kategorileri (top 5)
    const topExpenseCategories = expenseWithPercentage.slice(0, 5);

    return {
      expenseCategories: expenseWithPercentage,
      incomeCategories: incomeWithPercentage,
      topExpenseCategories,
      summary: {
        totalExpense,
        totalIncome,
        categoryCount: categoryData.length,
        topCategory: expenseWithPercentage[0]?.category || 'Veri yok'
      }
    };
  } catch (error) {
    console.error('❌ Kategori dağılım analizi yapılırken hata:', error);
    throw error;
  }
};

/**
 * Nakit akış analizini yapar
 * @param {Object} params - Parametreler
 * @returns {Object} Nakit akış analiz verisi
 */
export const getCashFlowAnalysis = async (params = {}) => {
  try {
    const db = getDb();
    
    // Varsayılan tarih aralığı - son 6 ay
    const endDate = params.endDate || new Date().toISOString().split('T')[0];
    const startDate = params.startDate || new Date(Date.now() - 180 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    // Günlük nakit akış
    const dailyFlow = await db.getAllAsync(`
      SELECT 
        date,
        SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END) as daily_income,
        SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as daily_expense,
        SUM(CASE WHEN type = 'income' THEN amount ELSE -amount END) as daily_net
      FROM transactions 
      WHERE date >= ? AND date <= ?
      GROUP BY date
      ORDER BY date
    `, [startDate, endDate]);

    // Kümülatif nakit akış hesapla
    let cumulativeFlow = 0;
    const cumulativeData = dailyFlow.map(day => {
      cumulativeFlow += day.daily_net;
      return {
        ...day,
        cumulative_flow: cumulativeFlow
      };
    });

    // Haftalık özet
    const weeklyFlow = await db.getAllAsync(`
      SELECT 
        strftime('%Y-%W', date) as week,
        SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END) as weekly_income,
        SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as weekly_expense,
        SUM(CASE WHEN type = 'income' THEN amount ELSE -amount END) as weekly_net
      FROM transactions 
      WHERE date >= ? AND date <= ?
      GROUP BY strftime('%Y-%W', date)
      ORDER BY week
    `, [startDate, endDate]);

    // Trend analizi
    const trendAnalysis = calculateTrend(cumulativeData.map(d => d.cumulative_flow));

    return {
      dailyFlow: cumulativeData,
      weeklyFlow,
      trend: trendAnalysis,
      summary: {
        totalInflow: dailyFlow.reduce((sum, day) => sum + day.daily_income, 0),
        totalOutflow: dailyFlow.reduce((sum, day) => sum + day.daily_expense, 0),
        netFlow: dailyFlow.reduce((sum, day) => sum + day.daily_net, 0),
        avgDailyIncome: dailyFlow.reduce((sum, day) => sum + day.daily_income, 0) / dailyFlow.length || 0,
        avgDailyExpense: dailyFlow.reduce((sum, day) => sum + day.daily_expense, 0) / dailyFlow.length || 0,
        currentBalance: cumulativeFlow
      }
    };
  } catch (error) {
    console.error('❌ Nakit akış analizi yapılırken hata:', error);
    throw error;
  }
};

/**
 * Mesai gelir analizini yapar
 * @param {Object} params - Parametreler
 * @returns {Object} Mesai gelir analiz verisi
 */
export const getOvertimeIncomeAnalysis = async (params = {}) => {
  try {
    const db = getDb();
    
    // Varsayılan tarih aralığı - son 6 ay
    const endDate = params.endDate || new Date().toISOString().split('T')[0];
    const startDate = params.startDate || new Date(Date.now() - 180 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    // Mesai detayları
    const overtimeDetails = await db.getAllAsync(`
      SELECT 
        shift_date,
        hours_worked,
        hourly_rate,
        total_payment,
        shift_type,
        location
      FROM overtime_shifts 
      WHERE shift_date >= ? AND shift_date <= ?
      ORDER BY shift_date DESC
    `, [startDate, endDate]);

    // Aylık mesai özeti
    const monthlyOvertimeSummary = await db.getAllAsync(`
      SELECT 
        strftime('%Y-%m', shift_date) as month,
        COUNT(*) as shift_count,
        SUM(hours_worked) as total_hours,
        SUM(total_payment) as total_payment,
        AVG(hourly_rate) as avg_hourly_rate
      FROM overtime_shifts 
      WHERE shift_date >= ? AND shift_date <= ?
      GROUP BY strftime('%Y-%m', shift_date)
      ORDER BY month
    `, [startDate, endDate]);

    // Shift türü bazlı analiz
    const shiftTypeAnalysis = await db.getAllAsync(`
      SELECT 
        shift_type,
        COUNT(*) as shift_count,
        SUM(hours_worked) as total_hours,
        SUM(total_payment) as total_payment,
        AVG(hourly_rate) as avg_hourly_rate
      FROM overtime_shifts 
      WHERE shift_date >= ? AND shift_date <= ?
      GROUP BY shift_type
      ORDER BY total_payment DESC
    `, [startDate, endDate]);

    // Lokasyon bazlı analiz
    const locationAnalysis = await db.getAllAsync(`
      SELECT 
        location,
        COUNT(*) as shift_count,
        SUM(hours_worked) as total_hours,
        SUM(total_payment) as total_payment,
        AVG(hourly_rate) as avg_hourly_rate
      FROM overtime_shifts 
      WHERE shift_date >= ? AND shift_date <= ?
      GROUP BY location
      ORDER BY total_payment DESC
    `, [startDate, endDate]);

    // Özet istatistikler
    const summary = {
      totalShifts: overtimeDetails.length,
      totalHours: overtimeDetails.reduce((sum, shift) => sum + shift.hours_worked, 0),
      totalPayment: overtimeDetails.reduce((sum, shift) => sum + shift.total_payment, 0),
      avgHourlyRate: overtimeDetails.reduce((sum, shift) => sum + shift.hourly_rate, 0) / overtimeDetails.length || 0,
      avgShiftHours: overtimeDetails.reduce((sum, shift) => sum + shift.hours_worked, 0) / overtimeDetails.length || 0,
      avgShiftPayment: overtimeDetails.reduce((sum, shift) => sum + shift.total_payment, 0) / overtimeDetails.length || 0
    };

    return {
      overtimeDetails,
      monthlyOvertimeSummary,
      shiftTypeAnalysis,
      locationAnalysis,
      summary
    };
  } catch (error) {
    console.error('❌ Mesai gelir analizi yapılırken hata:', error);
    throw error;
  }
};

/**
 * Trend analizi yapar
 * @param {Array} data - Veri dizisi
 * @returns {Object} Trend analiz sonucu
 */
const calculateTrend = (data) => {
  if (data.length < 2) {
    return { direction: 'stable', strength: 0, slope: 0 };
  }

  // Basit linear regression
  const n = data.length;
  const x = Array.from({ length: n }, (_, i) => i);
  const y = data;

  const sumX = x.reduce((sum, val) => sum + val, 0);
  const sumY = y.reduce((sum, val) => sum + val, 0);
  const sumXY = x.reduce((sum, val, i) => sum + val * y[i], 0);
  const sumXX = x.reduce((sum, val) => sum + val * val, 0);

  const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
  const intercept = (sumY - slope * sumX) / n;

  // Trend yönü ve gücü
  const direction = slope > 0 ? 'increasing' : slope < 0 ? 'decreasing' : 'stable';
  const strength = Math.abs(slope);

  return {
    direction,
    strength,
    slope,
    intercept
  };
};

/**
 * Birden fazla veri kaynağından veri toplar
 * @param {Array} dataSources - Veri kaynakları listesi
 * @param {Object} params - Parametreler
 * @returns {Object} Birleştirilmiş veri
 */
export const aggregateMultipleDataSources = async (dataSources, params = {}) => {
  try {
    const results = {};

    for (const source of dataSources) {
      switch (source) {
        case 'transactions':
          results.transactions = await getTransactionData(params);
          break;
        case 'salaries':
          results.salaries = await getSalaryData(params);
          break;
        case 'overtime':
          results.overtime = await getOvertimeData(params);
          break;
        case 'budgets':
          results.budgets = await getBudgetData(params);
          break;
        default:
          console.warn(`Bilinmeyen veri kaynağı: ${source}`);
      }
    }

    return results;
  } catch (error) {
    console.error('❌ Çoklu veri kaynağı toplandırılırken hata:', error);
    throw error;
  }
};
