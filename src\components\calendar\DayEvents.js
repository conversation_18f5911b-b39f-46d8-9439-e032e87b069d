import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Colors } from '../../constants/colors';
import { formatDuration } from '../../services/overtimeService';

/**
 * <PERSON>ün Etkinlikleri Bileşeni
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {Date} props.date - Tarih
 * @param {Array} props.events - Etkinlikler
 * @param {Function} props.onEventPress - Etkinliğe tıklandığında çağrılacak fonksiyon
 * @param {Function} props.onAddPress - Ekle butonuna tıklandığında çağrılacak fonksiyon
 * @returns {JSX.Element} Gün Et<PERSON>leri Bileşeni
 */
const DayEvents = ({ date, events = [], onEventPress, onAddPress }) => {
  // Toplam süre ve kazanç hesapla
  const totalDuration = events.reduce((total, event) => total + event.duration, 0);
  const totalEarnings = events.reduce((total, event) => total + (event.duration * event.hourly_rate), 0);
  
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.dateText}>
          {format(date, 'dd MMMM yyyy, EEEE', { locale: tr })}
        </Text>
        
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => onAddPress && onAddPress(date)}
        >
          <MaterialIcons name="add" size={20} color="#fff" />
          <Text style={styles.addButtonText}>Mesai Ekle</Text>
        </TouchableOpacity>
      </View>
      
      {events.length > 0 ? (
        <>
          <ScrollView style={styles.eventsList}>
            {events.map((event) => (
              <TouchableOpacity
                key={event.id}
                style={styles.eventItem}
                onPress={() => onEventPress && onEventPress(event)}
              >
                <View style={[styles.eventColor, { backgroundColor: event.color || Colors.PRIMARY }]} />
                
                <View style={styles.eventInfo}>
                  <Text style={styles.eventTitle}>{event.title}</Text>
                  
                  <View style={styles.eventDetails}>
                    <View style={styles.eventTime}>
                      <MaterialIcons name="access-time" size={14} color={Colors.GRAY_600} />
                      <Text style={styles.eventTimeText}>
                        {event.start_time} - {event.end_time}
                      </Text>
                    </View>
                    
                    <View style={styles.eventDuration}>
                      <MaterialIcons name="timer" size={14} color={Colors.GRAY_600} />
                      <Text style={styles.eventDurationText}>
                        {formatDuration(event.duration)}
                      </Text>
                    </View>
                  </View>
                  
                  {event.is_paid ? (
                    <View style={styles.paidBadge}>
                      <MaterialIcons name="check-circle" size={12} color={Colors.SUCCESS} />
                      <Text style={styles.paidBadgeText}>Ödendi</Text>
                    </View>
                  ) : (
                    <View style={styles.unpaidBadge}>
                      <MaterialIcons name="schedule" size={12} color={Colors.WARNING} />
                      <Text style={styles.unpaidBadgeText}>Ödenmedi</Text>
                    </View>
                  )}
                </View>
                
                <View style={styles.eventAmount}>
                  <Text style={styles.eventAmountText}>
                    {new Intl.NumberFormat('tr-TR', {
                      style: 'currency',
                      currency: event.currency || 'TRY'
                    }).format(event.duration * event.hourly_rate)}
                  </Text>
                  <MaterialIcons name="chevron-right" size={20} color={Colors.GRAY_500} />
                </View>
              </TouchableOpacity>
            ))}
          </ScrollView>
          
          <View style={styles.summary}>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>Toplam Süre:</Text>
              <Text style={styles.summaryValue}>{formatDuration(totalDuration)}</Text>
            </View>
            
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>Toplam Kazanç:</Text>
              <Text style={styles.summaryValue}>
                {new Intl.NumberFormat('tr-TR', {
                  style: 'currency',
                  currency: events[0]?.currency || 'TRY'
                }).format(totalEarnings)}
              </Text>
            </View>
          </View>
        </>
      ) : (
        <View style={styles.emptyContainer}>
          <MaterialIcons name="event-busy" size={48} color={Colors.GRAY_400} />
          <Text style={styles.emptyText}>Bu tarihte mesai kaydı bulunmuyor.</Text>
          <TouchableOpacity
            style={styles.emptyAddButton}
            onPress={() => onAddPress && onAddPress(date)}
          >
            <Text style={styles.emptyAddButtonText}>Mesai Ekle</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 8,
    overflow: 'hidden',
    marginTop: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200,
  },
  dateText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.GRAY_800,
    textTransform: 'capitalize',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  addButtonText: {
    color: '#fff',
    fontWeight: '500',
    marginLeft: 4,
  },
  eventsList: {
    maxHeight: 300,
  },
  eventItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200,
  },
  eventColor: {
    width: 4,
    height: '100%',
    borderRadius: 2,
    marginRight: 12,
  },
  eventInfo: {
    flex: 1,
  },
  eventTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.GRAY_800,
    marginBottom: 4,
  },
  eventDetails: {
    flexDirection: 'row',
    marginBottom: 4,
  },
  eventTime: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
  },
  eventTimeText: {
    fontSize: 12,
    color: Colors.GRAY_600,
    marginLeft: 4,
  },
  eventDuration: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  eventDurationText: {
    fontSize: 12,
    color: Colors.GRAY_600,
    marginLeft: 4,
  },
  paidBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.SUCCESS_LIGHT,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    alignSelf: 'flex-start',
  },
  paidBadgeText: {
    fontSize: 10,
    color: Colors.SUCCESS,
    marginLeft: 2,
  },
  unpaidBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.WARNING_LIGHT,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    alignSelf: 'flex-start',
  },
  unpaidBadgeText: {
    fontSize: 10,
    color: Colors.WARNING,
    marginLeft: 2,
  },
  eventAmount: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  eventAmountText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.GRAY_800,
    marginRight: 4,
  },
  summary: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.GRAY_200,
    backgroundColor: Colors.GRAY_50,
  },
  summaryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  summaryLabel: {
    fontSize: 14,
    color: Colors.GRAY_700,
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.GRAY_800,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 16,
    color: Colors.GRAY_600,
    marginTop: 16,
    marginBottom: 16,
    textAlign: 'center',
  },
  emptyAddButton: {
    backgroundColor: Colors.PRIMARY,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  emptyAddButtonText: {
    color: '#fff',
    fontWeight: '500',
  },
});

export default DayEvents;
