import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  RefreshControl
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import { format, parseISO } from 'date-fns';
import { tr } from 'date-fns/locale';
import * as budgetService from '../services/budgetService';
import * as budgetReportService from '../services/budgetReportService';
import { Colors } from '../constants/colors';

/**
 * Bütçe Detayları Ekranı
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @param {Object} props.route - Route nesnesi
 * @returns {JSX.Element} Bütçe Detayları Ekranı
 */
const BudgetDetailsScreen = ({ navigation, route }) => {
  const db = useSQLiteContext();
  const { budgetId } = route.params;
  
  // Durum
  const [budget, setBudget] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [transactions, setTransactions] = useState([]);
  const [showAllCategories, setShowAllCategories] = useState(false);
  
  // Verileri yükle
  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      
      // Bütçe detaylarını getir
      const budgetDetails = await budgetService.getBudgetDetails(db, budgetId);
      setBudget(budgetDetails);
      
      // Bütçe işlemlerini getir
      const budgetTransactions = await budgetService.getBudgetTransactions(db, budgetId);
      setTransactions(budgetTransactions);
      
      setLoading(false);
    } catch (error) {
      console.error('Bütçe detayları yükleme hatası:', error);
      Alert.alert('Hata', 'Bütçe detayları yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  }, [db, budgetId]);
  
  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [loadData])
  );
  
  // Yenileme işlemi
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };
  
  // Bütçe düzenleme ekranına git
  const navigateToBudgetForm = () => {
    navigation.navigate('BudgetForm', { budgetId });
  };
  
  // Bütçe raporu oluştur
  const createBudgetReport = async () => {
    try {
      await budgetReportService.createBudgetReport(db, budgetId);
      Alert.alert('Başarılı', 'Bütçe raporu oluşturuldu.');
      loadData();
    } catch (error) {
      console.error('Bütçe raporu oluşturma hatası:', error);
      Alert.alert('Hata', 'Bütçe raporu oluşturulurken bir hata oluştu.');
    }
  };
  
  // Bütçe silme
  const deleteBudget = () => {
    Alert.alert(
      'Bütçeyi Sil',
      `"${budget.name}" bütçesini silmek istediğinize emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await budgetService.deleteBudget(db, budgetId);
              Alert.alert('Başarılı', 'Bütçe silindi.');
              navigation.goBack();
            } catch (error) {
              console.error('Bütçe silme hatası:', error);
              Alert.alert('Hata', 'Bütçe silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };
  
  // Yükleniyor durumu
  if (loading && !refreshing) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={styles.loadingText}>Yükleniyor...</Text>
      </View>
    );
  }
  
  // Bütçe bulunamadı
  if (!budget) {
    return (
      <View style={styles.emptyContainer}>
        <MaterialIcons name="error-outline" size={64} color={Colors.GRAY_300} />
        <Text style={styles.emptyTitle}>Bütçe Bulunamadı</Text>
        <TouchableOpacity
          style={styles.emptyButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={20} color="#fff" />
          <Text style={styles.emptyButtonText}>Geri Dön</Text>
        </TouchableOpacity>
      </View>
    );
  }
  
  // Tarih aralığı
  const startDate = format(parseISO(budget.start_date), 'dd MMMM yyyy', { locale: tr });
  const endDate = budget.end_date ? format(parseISO(budget.end_date), 'dd MMMM yyyy', { locale: tr }) : 'Süresiz';
  
  // Kategorileri filtrele
  const displayCategories = showAllCategories 
    ? budget.categories 
    : budget.categories.slice(0, 5);
  
  return (
    <ScrollView 
      style={styles.container}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={handleRefresh}
          colors={[Colors.PRIMARY]}
        />
      }
    >
      {/* Başlık */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Text style={styles.budgetName}>{budget.name}</Text>
          <Text style={styles.budgetPeriod}>
            {budget.period === 'monthly' ? 'Aylık' : 
             budget.period === 'weekly' ? 'Haftalık' : 
             budget.period === 'yearly' ? 'Yıllık' : 'Özel'} Bütçe
          </Text>
          <Text style={styles.budgetDate}>{startDate} - {endDate}</Text>
        </View>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={navigateToBudgetForm}
          >
            <MaterialIcons name="edit" size={24} color={Colors.PRIMARY} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={deleteBudget}
          >
            <MaterialIcons name="delete" size={24} color={Colors.DANGER} />
          </TouchableOpacity>
        </View>
      </View>
      
      {/* Bütçe Özeti */}
      <View style={styles.summaryCard}>
        <Text style={styles.sectionTitle}>Bütçe Özeti</Text>
        
        <View style={styles.progressContainer}>
          <View style={styles.progressBarContainer}>
            <View 
              style={[
                styles.progressBar, 
                { width: `${budget.totalProgress}%` },
                budget.totalProgress > 80 ? styles.progressBarDanger : 
                budget.totalProgress > 60 ? styles.progressBarWarning : 
                styles.progressBarSuccess
              ]} 
            />
          </View>
          <Text style={styles.progressText}>{budget.totalProgress.toFixed(0)}%</Text>
        </View>
        
        <View style={styles.amountsContainer}>
          <View style={styles.amountItem}>
            <Text style={styles.amountLabel}>Toplam Bütçe</Text>
            <Text style={styles.amountValue}>
              {budget.totalBudget.toLocaleString('tr-TR')} ₺
            </Text>
          </View>
          <View style={styles.amountItem}>
            <Text style={styles.amountLabel}>Harcanan</Text>
            <Text style={styles.amountValue}>
              {budget.totalSpent.toLocaleString('tr-TR')} ₺
            </Text>
          </View>
          <View style={styles.amountItem}>
            <Text style={styles.amountLabel}>Kalan</Text>
            <Text style={[
              styles.amountValue,
              budget.totalRemaining < 0 ? styles.negativeAmount : null
            ]}>
              {budget.totalRemaining.toLocaleString('tr-TR')} ₺
            </Text>
          </View>
        </View>
        
        {budget.notes && (
          <View style={styles.notesContainer}>
            <Text style={styles.notesLabel}>Notlar:</Text>
            <Text style={styles.notesText}>{budget.notes}</Text>
          </View>
        )}
      </View>
      
      {/* Kategori Dağılımı */}
      <View style={styles.categoriesCard}>
        <Text style={styles.sectionTitle}>Kategori Dağılımı</Text>
        
        {displayCategories.map((category, index) => (
          <View key={index} style={styles.categoryItem}>
            <View style={styles.categoryHeader}>
              <View style={styles.categoryNameContainer}>
                {category.icon && (
                  <MaterialIcons 
                    name={category.icon} 
                    size={16} 
                    color={category.color || Colors.PRIMARY} 
                    style={styles.categoryIcon}
                  />
                )}
                <Text style={styles.categoryName}>{category.category_name}</Text>
              </View>
              <Text style={styles.categoryAmount}>
                {category.amount.toLocaleString('tr-TR')} ₺
              </Text>
            </View>
            
            <View style={styles.categoryProgressContainer}>
              <View style={styles.categoryProgressBarContainer}>
                <View 
                  style={[
                    styles.categoryProgressBar, 
                    { width: `${category.progress}%`, backgroundColor: category.color || Colors.PRIMARY }
                  ]} 
                />
              </View>
              <Text style={styles.categoryProgressText}>{category.progress.toFixed(0)}%</Text>
            </View>
            
            <View style={styles.categoryDetails}>
              <Text style={styles.categoryDetailText}>
                Harcanan: {(category.spent_amount || 0).toLocaleString('tr-TR')} ₺
              </Text>
              <Text style={[
                styles.categoryDetailText,
                category.remaining < 0 ? styles.negativeAmount : null
              ]}>
                Kalan: {category.remaining.toLocaleString('tr-TR')} ₺
              </Text>
            </View>
          </View>
        ))}
        
        {budget.categories.length > 5 && (
          <TouchableOpacity
            style={styles.showMoreButton}
            onPress={() => setShowAllCategories(!showAllCategories)}
          >
            <Text style={styles.showMoreButtonText}>
              {showAllCategories ? 'Daha Az Göster' : `${budget.categories.length - 5} Kategori Daha Göster`}
            </Text>
            <MaterialIcons 
              name={showAllCategories ? "expand-less" : "expand-more"} 
              size={20} 
              color={Colors.PRIMARY} 
            />
          </TouchableOpacity>
        )}
      </View>
      
      {/* Son İşlemler */}
      <View style={styles.transactionsCard}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Son İşlemler</Text>
          <TouchableOpacity
            style={styles.viewAllButton}
            onPress={() => navigation.navigate('BudgetTransactions', { budgetId })}
          >
            <Text style={styles.viewAllButtonText}>Tümünü Gör</Text>
            <MaterialIcons name="chevron-right" size={16} color={Colors.PRIMARY} />
          </TouchableOpacity>
        </View>
        
        {transactions.length === 0 ? (
          <Text style={styles.emptyTransactions}>Henüz işlem bulunmuyor</Text>
        ) : (
          transactions.slice(0, 5).map((transaction, index) => (
            <View key={index} style={styles.transactionItem}>
              <View style={styles.transactionLeft}>
                <View 
                  style={[
                    styles.transactionIconContainer,
                    { backgroundColor: transaction.category_color || Colors.PRIMARY }
                  ]}
                >
                  <MaterialIcons 
                    name={transaction.category_icon || "receipt"} 
                    size={16} 
                    color="#fff" 
                  />
                </View>
                <View style={styles.transactionInfo}>
                  <Text style={styles.transactionTitle}>{transaction.description}</Text>
                  <Text style={styles.transactionCategory}>{transaction.category_name}</Text>
                </View>
              </View>
              <View style={styles.transactionRight}>
                <Text style={styles.transactionAmount}>
                  {transaction.amount.toLocaleString('tr-TR')} ₺
                </Text>
                <Text style={styles.transactionDate}>
                  {format(parseISO(transaction.date), 'dd MMM', { locale: tr })}
                </Text>
              </View>
            </View>
          ))
        )}
      </View>
      
      {/* Rapor Oluştur */}
      <TouchableOpacity
        style={styles.reportButton}
        onPress={createBudgetReport}
      >
        <MaterialIcons name="assessment" size={20} color="#fff" />
        <Text style={styles.reportButtonText}>Rapor Oluştur</Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: Colors.PRIMARY,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    marginTop: 16,
    marginBottom: 24,
  },
  emptyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  emptyButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    backgroundColor: Colors.PRIMARY,
    padding: 20,
    paddingBottom: 30,
  },
  headerContent: {
    flex: 1,
  },
  budgetName: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 4,
  },
  budgetPeriod: {
    fontSize: 16,
    color: '#fff',
    opacity: 0.9,
    marginBottom: 4,
  },
  budgetDate: {
    fontSize: 14,
    color: '#fff',
    opacity: 0.8,
  },
  headerActions: {
    flexDirection: 'row',
  },
  actionButton: {
    padding: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 8,
    marginLeft: 8,
  },
  summaryCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    margin: 16,
    marginTop: -20,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    marginBottom: 16,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  progressBarContainer: {
    flex: 1,
    height: 12,
    backgroundColor: Colors.GRAY_200,
    borderRadius: 6,
    marginRight: 8,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    borderRadius: 6,
  },
  progressBarSuccess: {
    backgroundColor: Colors.SUCCESS,
  },
  progressBarWarning: {
    backgroundColor: Colors.WARNING,
  },
  progressBarDanger: {
    backgroundColor: Colors.DANGER,
  },
  progressText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    width: 50,
    textAlign: 'right',
  },
  amountsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  amountItem: {
    flex: 1,
    alignItems: 'center',
  },
  amountLabel: {
    fontSize: 14,
    color: Colors.TEXT_LIGHT,
    marginBottom: 4,
  },
  amountValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
  },
  negativeAmount: {
    color: Colors.DANGER,
  },
  notesContainer: {
    borderTopWidth: 1,
    borderTopColor: Colors.GRAY_200,
    paddingTop: 16,
  },
  notesLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    marginBottom: 4,
  },
  notesText: {
    fontSize: 14,
    color: Colors.TEXT_LIGHT,
  },
  categoriesCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    margin: 16,
    marginTop: 0,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  categoryItem: {
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200,
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryIcon: {
    marginRight: 8,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.TEXT_DARK,
  },
  categoryAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
  },
  categoryProgressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryProgressBarContainer: {
    flex: 1,
    height: 8,
    backgroundColor: Colors.GRAY_200,
    borderRadius: 4,
    marginRight: 8,
    overflow: 'hidden',
  },
  categoryProgressBar: {
    height: '100%',
    borderRadius: 4,
  },
  categoryProgressText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    width: 40,
    textAlign: 'right',
  },
  categoryDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  categoryDetailText: {
    fontSize: 14,
    color: Colors.TEXT_LIGHT,
  },
  showMoreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
  },
  showMoreButtonText: {
    fontSize: 14,
    color: Colors.PRIMARY,
    fontWeight: '500',
    marginRight: 4,
  },
  transactionsCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    margin: 16,
    marginTop: 0,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewAllButtonText: {
    fontSize: 14,
    color: Colors.PRIMARY,
    marginRight: 4,
  },
  emptyTransactions: {
    fontSize: 14,
    color: Colors.TEXT_LIGHT,
    textAlign: 'center',
    paddingVertical: 16,
  },
  transactionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200,
  },
  transactionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  transactionIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.TEXT_DARK,
    marginBottom: 2,
  },
  transactionCategory: {
    fontSize: 12,
    color: Colors.TEXT_LIGHT,
  },
  transactionRight: {
    alignItems: 'flex-end',
  },
  transactionAmount: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    marginBottom: 2,
  },
  transactionDate: {
    fontSize: 12,
    color: Colors.TEXT_LIGHT,
  },
  reportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.PRIMARY,
    borderRadius: 8,
    padding: 16,
    margin: 16,
    marginTop: 0,
    marginBottom: 32,
  },
  reportButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
    marginLeft: 8,
  },
});

export default BudgetDetailsScreen;
