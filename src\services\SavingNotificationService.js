import * as Notifications from 'expo-notifications';
import { format, addMonths, subMonths, isValid } from 'date-fns';
import { tr } from 'date-fns/locale';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useSQLiteContext } from 'expo-sqlite';

const NOTIFICATION_SETTINGS_KEY = '@saving_notification_settings';
const QUIET_HOURS = { start: '23:00', end: '07:00' };

/**
 * Güvenli tarih formatting fonksiyonu - geçersiz tarihleri ele alır
 * @param {Date|string} date - Format edilecek tarih
 * @param {string} formatStr - Format string'i
 * @returns {string} Formatlanmış tarih veya hata mesajı
 */
const safeFormatDate = (date, formatStr = 'HH:mm') => {
  try {
    const dateObj = date instanceof Date ? date : new Date(date);
    if (!isValid(dateObj)) {
      return '00:00';
    }
    return format(dateObj, formatStr);
  } catch (error) {
    console.warn('Date formatting error:', error);
    return '00:00';
  }
};

/**
 * <PERSON>irikim bildiri<PERSON>i servisi
 */
const SavingNotificationService = {
  /**
   * Bildirim izinlerini kontrol eder ve yapılandırır
   */
  initialize: async () => {
    try {
      // Kayıtlı ayarları yükle
      const settings = await SavingNotificationService.loadSettings();
      
      // Bildirimler kapalıysa işlem yapma
      if (!settings?.enabled) return false;

      // İzinleri kontrol et
      const { status } = await Notifications.getPermissionsAsync();
      if (status !== 'granted') {
        const { status: newStatus } = await Notifications.requestPermissionsAsync();
        if (newStatus !== 'granted') return false;
      }

      // Bildirim kanalını ayarla (Android için)
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('savings', {
          name: 'Birikim Bildirimleri',
          importance: Notifications.AndroidImportance.HIGH,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#2ecc71',
        });
      }

      return true;
    } catch (error) {
      console.error('Bildirim servisi başlatılamadı:', error);
      return false;
    }
  },

  /**
   * Bildirim ayarlarını yükler
   */
  loadSettings: async () => {
    try {
      const settings = await AsyncStorage.getItem(NOTIFICATION_SETTINGS_KEY);
      return settings ? JSON.parse(settings) : {
        enabled: true,
        quietHoursEnabled: true,
        quietHoursStart: QUIET_HOURS.start,
        quietHoursEnd: QUIET_HOURS.end,
        savingReminders: true,
        goalMilestones: true
      };
    } catch (error) {
      console.error('Bildirim ayarları yüklenemedi:', error);
      return null;
    }
  },

  /**
   * Bildirim ayarlarını kaydeder
   */
  saveSettings: async (settings) => {
    try {
      await AsyncStorage.setItem(NOTIFICATION_SETTINGS_KEY, JSON.stringify(settings));
      return true;
    } catch (error) {
      console.error('Bildirim ayarları kaydedilemedi:', error);
      return false;
    }
  },

  /**
   * Bildirimin gönderilebilir olup olmadığını kontrol eder
   */
  canSendNotification: async () => {
    try {
      const settings = await SavingNotificationService.loadSettings();
      if (!settings?.enabled) return false;      // Sessiz saatleri kontrol et
      if (settings.quietHoursEnabled) {
        const now = new Date();
        const currentTime = safeFormatDate(now, 'HH:mm');
        if (currentTime >= settings.quietHoursStart && 
            currentTime <= settings.quietHoursEnd) {
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error('Bildirim kontrolü yapılamadı:', error);
      return false;
    }
  },

  /**
   * Hedefli birikim bildirimi gönderir
   */
  scheduleTargetNotification: async ({ savingId, name, currentBalance, targetAmount, targetDate }) => {
    try {
      if (!await SavingNotificationService.canSendNotification()) return;

      const progress = (currentBalance / targetAmount) * 100;
      const milestones = [25, 50, 75, 90, 100];
      
      // Sadece en yakın dönüm noktası için bildirim
      const nextMilestone = milestones.find(m => progress < m);
      if (nextMilestone) {
        await Notifications.scheduleNotificationAsync({
          content: {
            title: 'Hedefinize Yaklaşıyorsunuz! 🎯',
            body: `${name} hedefinizde %${nextMilestone}'e ulaştınız!`,
            data: { type: 'target_milestone', savingId }
          },
          trigger: null // Hemen gönder
        });
      }

      // Hedef tarihine 1 ay kala hatırlatma
      if (targetDate) {
        const oneMonthBefore = subMonths(new Date(targetDate), 1);
        if (oneMonthBefore > new Date()) {
          await Notifications.scheduleNotificationAsync({
            content: {
              title: 'Hedef Tarihiniz Yaklaşıyor',
              body: `${name} için belirlediğiniz hedef tarihine 1 ay kaldı.`,
              data: { type: 'target_date_reminder', savingId }
            },
            trigger: { date: oneMonthBefore }
          });
        }
      }
    } catch (error) {
      console.error('Hedef bildirimi gönderilemedi:', error);
    }
  },

  /**
   * Düzenli birikim hatırlatıcısı gönderir
   */
  scheduleRegularReminder: async ({ savingId, name, frequency }) => {
    try {
      if (!await SavingNotificationService.canSendNotification()) return;

      const settings = await SavingNotificationService.loadSettings();
      if (!settings?.savingReminders) return;

      // Ayda bir kez hatırlatma
      const nextMonth = addMonths(new Date(), 1);
      nextMonth.setDate(1);
      nextMonth.setHours(10, 0, 0);

      await Notifications.scheduleNotificationAsync({
        content: {
          title: 'Birikim Hatırlatması',
          body: `${name} için düzenli birikim zamanı geldi.`,
          data: { type: 'regular_reminder', savingId }
        },
        trigger: { date: nextMonth }
      });
    } catch (error) {
      console.error('Düzenli birikim hatırlatması gönderilemedi:', error);
    }
  },

  /**
   * Uzun süre işlem yapılmadığında hatırlatma gönderir
   */
  scheduleInactivityReminder: async ({ savingId, name, lastTransactionDate }) => {
    try {
      if (!await SavingNotificationService.canSendNotification()) return;

      // 3 ay işlem yapılmadığında hatırlat
      const threemonthsLater = addMonths(new Date(lastTransactionDate), 3);
      
      if (threemonthsLater > new Date()) {
        await Notifications.scheduleNotificationAsync({
          content: {
            title: 'Birikiminizi Unutmayın',
            body: `${name} birikiminize 3 aydır katkıda bulunmadınız.`,
            data: { type: 'inactivity_reminder', savingId }
          },
          trigger: { date: threemonthsLater }
        });
      }
    } catch (error) {
      console.error('İnaktiflik hatırlatması gönderilemedi:', error);
    }
  },

  /**
   * Tüm bildirimleri iptal eder
   */
  cancelAllNotifications: async (savingId) => {
    try {
      const notifications = await Notifications.getAllScheduledNotificationsAsync();
      const savingNotifications = notifications.filter(
        n => n.content.data?.savingId === savingId
      );

      for (const notification of savingNotifications) {
        await Notifications.cancelScheduledNotificationAsync(notification.identifier);
      }
    } catch (error) {
      console.error('Bildirimler iptal edilemedi:', error);
    }
  }
};

export default SavingNotificationService;
