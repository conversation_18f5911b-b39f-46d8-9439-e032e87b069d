import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { COLORS, LAYOUT, TYPOGRAPHY } from '../../constants/themes';

/**
 * Hızlı erişim menüsü öğesi
 * 
 * @param {Object} props Bileşen özellikleri
 * @param {string} props.icon Menü öğesi ikonu
 * @param {string} props.title Menü öğesi başlığı
 * @param {string} props.description Menü öğesi açıklaması (isteğe bağlı)
 * @param {Function} props.onPress Tıklama işlevi
 * @param {string} props.color Özel renk (isteğe bağlı)
 * @returns {JSX.Element} MenuItem bileşeni
 */
const MenuItem = ({ 
  icon, 
  title, 
  description, 
  onPress,
  color = Colors.PRIMARY.main
}) => (
  <TouchableOpacity 
    style={styles.menuItem} 
    onPress={onPress}
    activeOpacity={0.8}
  >
    <View style={[styles.iconContainer, { backgroundColor: color + '15' }]}>
      <MaterialIcons name={icon} size={24} color={color} />
    </View>
    <Text style={styles.menuTitle}>{title}</Text>
    {description && <Text style={styles.menuDescription}>{description}</Text>}
  </TouchableOpacity>
);

/**
 * Hızlı erişim menüsü bileşeni
 * 
 * @param {Object} props Bileşen özellikleri
 * @param {Array} props.items Menü öğeleri dizisi
 * @param {Function} props.onItemPress Menü öğesine tıklama işlevi
 * @param {string} props.title Menü başlığı
 * @returns {JSX.Element} QuickAccessMenu bileşeni
 */
const QuickAccessMenu = ({ 
  items = [], 
  onItemPress,
  title = 'Hızlı Erişim'
}) => {
  // İçerik yoksa render etme
  if (!items || items.length === 0) return null;
  
  const defaultItems = [
    {
      id: 'add_income',
      icon: 'add-circle',
      title: 'Gelir Ekle',
      screenName: 'Transactions',
      params: { type: 'income' },
      color: #2ecc71
    },
    {
      id: 'add_expense',
      icon: 'remove-circle',
      title: 'Gider Ekle',
      screenName: 'Transactions',
      params: { type: 'expense' },
      color: #e74c3c
    },
    {
      id: 'add_saving',
      icon: 'savings',
      title: 'Birikim Ekle',
      screenName: 'Savings'
    },
    {
      id: 'add_investment',
      icon: 'trending-up',
      title: 'Yatırım Ekle',
      screenName: 'Investment'
    },
    {
      id: 'view_reports',
      icon: 'insert-chart',
      title: 'Raporlar',
      screenName: 'Reports'
    }
  ];

  const menuItems = items.length > 0 ? items : defaultItems;

  /**
   * Menü öğesine tıklama işlevi
   * @param {Object} item Tıklanan menü öğesi
   */
  const handleItemPress = (item) => {
    if (onItemPress) {
      onItemPress(item);
    }
  };

  return (
    <View style={styles.container}>
      {title && (
        <View style={styles.header}>
          <MaterialIcons name="apps" size={18} color={Colors.PRIMARY.main} />
          <Text style={styles.headerText}>{title}</Text>
        </View>
      )}
      
      <ScrollView 
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.menuContainer}
      >
        {menuItems.map((item) => (
          <MenuItem
            key={item.id}
            icon={item.icon}
            title={item.title}
            description={item.description}
            color={item.color}
            onPress={() => handleItemPress(item)}
          />
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: #FFFFFF,
    borderRadius: LAYOUT.radius.lg,
    padding: LAYOUT.spacing.lg,
    marginHorizontal: LAYOUT.spacing.md,
    marginVertical: LAYOUT.spacing.sm,
    ...LAYOUT.shadow.sm
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: LAYOUT.spacing.md
  },
  headerText: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: TYPOGRAPHY.fontWeight.semiBold,
    color: #374151,
    marginLeft: LAYOUT.spacing.xs
  },
  menuContainer: {
    flexDirection: 'row',
    paddingVertical: LAYOUT.spacing.xs
  },
  menuItem: {
    alignItems: 'center',
    marginRight: LAYOUT.spacing.lg,
    width: 70
  },
  iconContainer: {
    width: 56,
    height: 56,
    borderRadius: LAYOUT.radius.circle,
    backgroundColor: Colors.PRIMARY.light + '15',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: LAYOUT.spacing.xs
  },
  menuTitle: {
    fontSize: TYPOGRAPHY.fontSize.xs,
    fontWeight: TYPOGRAPHY.fontWeight.medium,
    color: #374151,
    textAlign: 'center',
    marginTop: LAYOUT.spacing.xs
  },
  menuDescription: {
    fontSize: TYPOGRAPHY.fontSize.xs,
    color: #6B7280,
    textAlign: 'center',
    marginTop: 2
  }
});

export default QuickAccessMenu;
