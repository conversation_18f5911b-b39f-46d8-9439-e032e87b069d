import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useSQLiteContext } from 'expo-sqlite';
import { Colors } from '../constants/colors';
import DateTimePicker from '@react-native-community/datetimepicker';
import { createSavingsGoalsTable } from '../db/createSavingsGoalsTable';

/**
 * Birikim Hedefi Ekleme/Düzenleme Ekranı
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @param {Object} props.route - Route nesnesi
 * @returns {JSX.Element} Birikim Hedefi Formu
 */
export default function SavingsGoalFormScreen({ navigation, route }) {
  const { goalId } = route.params || {};
  const db = useSQLiteContext();

  // Durum değişkenleri
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(goalId ? true : false);
  const [name, setName] = useState('');
  const [targetAmount, setTargetAmount] = useState('');
  const [currentAmount, setCurrentAmount] = useState('');
  const [targetDate, setTargetDate] = useState(new Date());
  const [currency, setCurrency] = useState('TRY');
  const [notes, setNotes] = useState('');

  const [showDatePicker, setShowDatePicker] = useState(false);

  // Tabloyu oluştur
  useEffect(() => {
    const initTable = async () => {
      try {
        await createSavingsGoalsTable(db);
      } catch (error) {
        console.error('Tablo oluşturma hatası:', error);
      }
    };

    initTable();
  }, [db]);

  // Mevcut hedefi yükle
  useEffect(() => {
    const loadSavingsGoal = async () => {
      if (!goalId) return;

      try {
        setInitialLoading(true);

        // Önce tabloyu oluştur (eğer yoksa)
        await createSavingsGoalsTable(db);

        const goal = await db.getFirstAsync(`
          SELECT * FROM savings_goals
          WHERE id = ?
        `, [goalId]);

        if (goal) {
          setName(goal.name);
          setTargetAmount(goal.target_amount.toString());
          setCurrentAmount(goal.current_amount.toString());
          setTargetDate(new Date(goal.target_date));
          setCurrency(goal.currency);
          setNotes(goal.notes || '');
        }

        setInitialLoading(false);
      } catch (error) {
        console.error('Birikim hedefi yükleme hatası:', error);
        Alert.alert('Hata', 'Birikim hedefi yüklenirken bir hata oluştu.');
        setInitialLoading(false);
      }
    };

    loadSavingsGoal();
  }, [goalId, db]);

  // Birikim hedefini kaydet
  const saveSavingsGoal = async () => {
    // Validasyon
    if (!name.trim()) {
      Alert.alert('Hata', 'Lütfen bir hedef adı girin.');
      return;
    }

    if (!targetAmount || isNaN(parseFloat(targetAmount)) || parseFloat(targetAmount) <= 0) {
      Alert.alert('Hata', 'Lütfen geçerli bir hedef tutar girin.');
      return;
    }

    if (!currentAmount || isNaN(parseFloat(currentAmount)) || parseFloat(currentAmount) < 0) {
      Alert.alert('Hata', 'Lütfen geçerli bir mevcut tutar girin.');
      return;
    }

    if (parseFloat(currentAmount) > parseFloat(targetAmount)) {
      Alert.alert('Hata', 'Mevcut tutar hedef tutardan büyük olamaz.');
      return;
    }

    try {
      setLoading(true);

      // Önce tabloyu oluştur (eğer yoksa)
      await createSavingsGoalsTable(db);

      const parsedTargetAmount = parseFloat(targetAmount);
      const parsedCurrentAmount = parseFloat(currentAmount);

      if (goalId) {
        // Mevcut hedefi güncelle
        await db.runAsync(`
          UPDATE savings_goals
          SET name = ?,
              target_amount = ?,
              current_amount = ?,
              target_date = ?,
              currency = ?,
              notes = ?,
              updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `, [
          name.trim(),
          parsedTargetAmount,
          parsedCurrentAmount,
          targetDate.toISOString().split('T')[0],
          currency,
          notes.trim(),
          goalId
        ]);

        Alert.alert('Başarılı', 'Birikim hedefi başarıyla güncellendi.');
      } else {
        // Yeni hedef ekle
        await db.runAsync(`
          INSERT INTO savings_goals (
            name, target_amount, current_amount, target_date,
            currency, notes, created_at, updated_at
          )
          VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        `, [
          name.trim(),
          parsedTargetAmount,
          parsedCurrentAmount,
          targetDate.toISOString().split('T')[0],
          currency,
          notes.trim()
        ]);

        Alert.alert('Başarılı', 'Birikim hedefi başarıyla eklendi.');
      }

      setLoading(false);
      navigation.goBack();
    } catch (error) {
      console.error('Birikim hedefi kaydetme hatası:', error);
      Alert.alert('Hata', 'Birikim hedefi kaydedilirken bir hata oluştu.');
      setLoading(false);
    }
  };

  // Tarih formatla
  const formatDate = (date) => {
    return date.toLocaleDateString('tr-TR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  // Tarih değişikliğini işle
  const handleDateChange = (_, selectedDate) => {
    setShowDatePicker(false);

    if (selectedDate) {
      setTargetDate(selectedDate);
    }
  };

  if (initialLoading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={styles.loadingText}>Birikim hedefi yükleniyor...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {goalId ? 'Hedefi Düzenle' : 'Yeni Hedef Ekle'}
        </Text>
        <View style={{ width: 24 }} />
      </View>

      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
      >
        <ScrollView style={styles.content}>
          <View style={styles.formGroup}>
            <Text style={styles.label}>Hedef Adı</Text>
            <TextInput
              style={styles.input}
              value={name}
              onChangeText={setName}
              placeholder="Örn: Araba, Tatil, Ev..."
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Hedef Tutar</Text>
            <View style={styles.inputWithIcon}>
              <Text style={styles.inputIcon}>{currency}</Text>
              <TextInput
                style={styles.inputWithIconField}
                value={targetAmount}
                onChangeText={setTargetAmount}
                keyboardType="numeric"
                placeholder="0.00"
              />
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Mevcut Birikim</Text>
            <View style={styles.inputWithIcon}>
              <Text style={styles.inputIcon}>{currency}</Text>
              <TextInput
                style={styles.inputWithIconField}
                value={currentAmount}
                onChangeText={setCurrentAmount}
                keyboardType="numeric"
                placeholder="0.00"
              />
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Hedef Tarihi</Text>
            <TouchableOpacity
              style={styles.dateSelector}
              onPress={() => setShowDatePicker(true)}
            >
              <MaterialIcons name="event" size={20} color={Colors.PRIMARY} />
              <Text style={styles.dateText}>{formatDate(targetDate)}</Text>
            </TouchableOpacity>

            {showDatePicker && (
              <DateTimePicker
                value={targetDate}
                mode="date"
                display="default"
                onChange={handleDateChange}
                minimumDate={new Date()}
              />
            )}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Para Birimi</Text>
            <View style={styles.currencySelector}>
              {['TRY', 'USD', 'EUR', 'GBP'].map(curr => (
                <TouchableOpacity
                  key={curr}
                  style={[
                    styles.currencyOption,
                    currency === curr && styles.selectedCurrencyOption
                  ]}
                  onPress={() => setCurrency(curr)}
                >
                  <Text
                    style={[
                      styles.currencyText,
                      currency === curr && styles.selectedCurrencyText
                    ]}
                  >
                    {curr}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Notlar</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={notes}
              onChangeText={setNotes}
              placeholder="Hedef hakkında notlar..."
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>

          <TouchableOpacity
            style={styles.saveButton}
            onPress={saveSavingsGoal}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <>
                <MaterialIcons name="save" size={20} color="#fff" />
                <Text style={styles.saveButtonText}>Kaydet</Text>
              </>
            )}
          </TouchableOpacity>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.PRIMARY,
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    color: '#333',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  inputWithIcon: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    backgroundColor: '#fff',
  },
  inputIcon: {
    fontSize: 16,
    color: '#666',
    marginRight: 8,
  },
  inputWithIconField: {
    flex: 1,
    paddingVertical: 10,
    fontSize: 16,
  },
  dateSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    backgroundColor: '#fff',
  },
  dateText: {
    fontSize: 16,
    color: '#333',
    marginLeft: 8,
  },
  currencySelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  currencyOption: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 10,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    marginHorizontal: 4,
    borderRadius: 8,
  },
  selectedCurrencyOption: {
    backgroundColor: Colors.PRIMARY,
    borderColor: Colors.PRIMARY,
  },
  currencyText: {
    fontSize: 16,
    color: '#333',
  },
  selectedCurrencyText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  textArea: {
    height: 100,
  },
  saveButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
    borderRadius: 8,
    paddingVertical: 12,
    marginBottom: 24,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
    marginLeft: 8,
  },
});
