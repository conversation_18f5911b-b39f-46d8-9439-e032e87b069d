import { useCallback, useMemo, useRef, useEffect, useState } from 'react';
import { InteractionManager, LayoutAnimation, Platform } from 'react-native';

/**
 * Performance Optimization Hooks
 * React Native performance iyileştirmeleri için hook'lar
 */

/**
 * Debounced değer hook'u
 * @param {*} value - Debounce edilecek değer
 * @param {number} delay - Gecikme süresi (ms)
 * @returns {*} Debounced değer
 */
export const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

/**
 * Throttled callback hook'u
 * @param {Function} callback - Throttle edilecek fonksiyon
 * @param {number} delay - <PERSON><PERSON><PERSON><PERSON> s<PERSON> (ms)
 * @returns {Function} Throttled callback
 */
export const useThrottle = (callback, delay) => {
  const lastRun = useRef(Date.now());

  return useCallback((...args) => {
    if (Date.now() - lastRun.current >= delay) {
      callback(...args);
      lastRun.current = Date.now();
    }
  }, [callback, delay]);
};

/**
 * Memoized selector hook'u
 * @param {Function} selector - Seçici fonksiyon
 * @param {Array} deps - Dependency array
 * @returns {*} Memoized değer
 */
export const useMemoizedSelector = (selector, deps) => {
  return useMemo(selector, deps);
};

/**
 * Stable callback hook'u
 * @param {Function} callback - Callback fonksiyon
 * @param {Array} deps - Dependency array
 * @returns {Function} Stable callback
 */
export const useStableCallback = (callback, deps) => {
  return useCallback(callback, deps);
};

/**
 * Interaction Manager hook'u
 * Ağır işlemleri interaction sonrasına erteler
 * @param {Function} callback - Ertelenecek fonksiyon
 * @param {Array} deps - Dependency array
 */
export const useInteractionManager = (callback, deps) => {
  useEffect(() => {
    const task = InteractionManager.runAfterInteractions(() => {
      callback();
    });

    return () => task.cancel();
  }, deps);
};

/**
 * Layout Animation hook'u
 * @param {Object} config - Animation konfigürasyonu
 */
export const useLayoutAnimation = (config = LayoutAnimation.Presets.easeInEaseOut) => {
  const animate = useCallback(() => {
    if (Platform.OS !== 'web') {
      LayoutAnimation.configureNext(config);
    }
  }, [config]);

  return animate;
};

/**
 * Memory cleanup hook'u
 * Component unmount'ta cleanup işlemleri yapar
 * @param {Function} cleanup - Cleanup fonksiyonu
 */
export const useCleanup = (cleanup) => {
  useEffect(() => {
    return cleanup;
  }, []);
};

/**
 * Optimized list hook'u
 * FlatList için optimize edilmiş ayarlar
 * @param {Array} data - Liste verisi
 * @param {Object} options - Optimizasyon seçenekleri
 * @returns {Object} Optimize edilmiş props
 */
export const useOptimizedList = (data, options = {}) => {
  const {
    itemHeight = 80,
    windowSize = 10,
    maxToRenderPerBatch = 5,
    updateCellsBatchingPeriod = 50,
    initialNumToRender = 10,
    removeClippedSubviews = true,
  } = options;

  const getItemLayout = useCallback((data, index) => ({
    length: itemHeight,
    offset: itemHeight * index,
    index,
  }), [itemHeight]);

  const keyExtractor = useCallback((item, index) => {
    return item.id?.toString() || index.toString();
  }, []);

  return useMemo(() => ({
    data,
    getItemLayout: itemHeight ? getItemLayout : undefined,
    keyExtractor,
    windowSize,
    maxToRenderPerBatch,
    updateCellsBatchingPeriod,
    initialNumToRender,
    removeClippedSubviews,
    // Performance optimizations
    disableVirtualization: false,
    legacyImplementation: false,
  }), [
    data,
    getItemLayout,
    keyExtractor,
    windowSize,
    maxToRenderPerBatch,
    updateCellsBatchingPeriod,
    initialNumToRender,
    removeClippedSubviews,
  ]);
};

/**
 * Batch updates hook'u
 * Birden fazla state güncellemesini batch'ler
 * @param {Object} initialState - Başlangıç state'i
 * @returns {Array} [state, batchUpdate]
 */
export const useBatchUpdates = (initialState) => {
  const [state, setState] = useState(initialState);
  const pendingUpdates = useRef({});
  const timeoutRef = useRef(null);

  const batchUpdate = useCallback((updates) => {
    // Pending updates'i birleştir
    Object.assign(pendingUpdates.current, updates);

    // Timeout'u temizle ve yenisini ayarla
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      setState(prevState => ({
        ...prevState,
        ...pendingUpdates.current
      }));
      pendingUpdates.current = {};
    }, 0);
  }, []);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return [state, batchUpdate];
};

/**
 * Async operation hook'u
 * Async işlemleri optimize eder
 * @param {Function} asyncFn - Async fonksiyon
 * @param {Array} deps - Dependency array
 * @returns {Object} { data, loading, error, refetch }
 */
export const useAsyncOperation = (asyncFn, deps = []) => {
  const [state, setState] = useState({
    data: null,
    loading: false,
    error: null,
  });

  const isMountedRef = useRef(true);
  const abortControllerRef = useRef(null);

  const execute = useCallback(async (...args) => {
    // Önceki request'i iptal et
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Yeni AbortController oluştur
    abortControllerRef.current = new AbortController();

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const result = await asyncFn(...args);
      
      if (isMountedRef.current) {
        setState({ data: result, loading: false, error: null });
      }
    } catch (error) {
      if (isMountedRef.current && error.name !== 'AbortError') {
        setState(prev => ({ ...prev, loading: false, error }));
      }
    }
  }, deps);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    ...state,
    execute,
    refetch: execute,
  };
};

/**
 * Performance monitor hook'u
 * Component render performansını izler
 * @param {string} componentName - Component adı
 */
export const usePerformanceMonitor = (componentName) => {
  const renderCount = useRef(0);
  const startTime = useRef(Date.now());

  useEffect(() => {
    renderCount.current += 1;
    const endTime = Date.now();
    const renderTime = endTime - startTime.current;

    if (__DEV__) {
      console.log(`[Performance] ${componentName} - Render #${renderCount.current} - ${renderTime}ms`);
    }

    startTime.current = endTime;
  });

  return renderCount.current;
};

export default {
  useDebounce,
  useThrottle,
  useMemoizedSelector,
  useStableCallback,
  useInteractionManager,
  useLayoutAnimation,
  useCleanup,
  useOptimizedList,
  useBatchUpdates,
  useAsyncOperation,
  usePerformanceMonitor,
};
