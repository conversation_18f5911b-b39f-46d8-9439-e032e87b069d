/**
 * Real Budget Service - Complete Database Integration
 * Follows BUDGET_MANAGEMENT_REDESIGN_PLAN.md Stage 1 implementation
 * Replaces mock/demo data with actual SQLite database operations
 * 
 * Features:
 * - Real database CRUD operations
 * - Multi-currency support (TRY/USD/EUR)
 * - Category-based budget management
 * - Real-time spending calculations
 * - Turkish localization
 */

import { formatSQLiteDate } from '../../utils/dateFormatters';

/**
 * Creates a new budget with categories and alert settings
 * @param {SQLiteDatabase} db - Database instance
 * @param {Object} budgetData - Budget configuration
 * @param {Array} categories - Category limits array
 * @returns {Promise<number>} Created budget ID
 */
export const createBudget = async (db, budgetData, categories = []) => {
  try {
    console.log('🏦 Creating new budget:', budgetData.name);
    
    return await db.withTransactionAsync(async () => {
      // Create main budget record
      const budgetResult = await db.runAsync(`
        INSERT INTO budgets 
        (name, description, type, period_type, start_date, end_date, total_limit, currency, status, auto_renew)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        budgetData.name,
        budgetData.description || '',
        budgetData.type || 'category_based',
        budgetData.period_type || 'monthly',
        formatSQLiteDate(budgetData.start_date),
        budgetData.end_date ? formatSQLiteDate(budgetData.end_date) : null,
        budgetData.total_limit || 0,
        budgetData.currency || 'TRY',
        budgetData.status || 'active',
        budgetData.auto_renew ? 1 : 0
      ]);

      const budgetId = budgetResult.lastInsertRowId;

      // Add category limits if provided
      if (categories && categories.length > 0) {
        for (const category of categories) {
          await db.runAsync(`
            INSERT INTO budget_categories 
            (budget_id, category_id, limit_amount, currency)
            VALUES (?, ?, ?, ?)
          `, [
            budgetId,
            category.category_id,
            category.limit_amount,
            budgetData.currency || 'TRY'
          ]);
        }
      }

      // Create default alert settings
      await db.runAsync(`
        INSERT INTO budget_alert_settings 
        (budget_id, threshold_75, threshold_90, threshold_100, daily_limit_exceeded, category_limit_exceeded, weekly_summary)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [
        budgetId, 
        budgetData.alerts?.threshold_75 ?? 1,
        budgetData.alerts?.threshold_90 ?? 1,
        budgetData.alerts?.threshold_100 ?? 1,
        budgetData.alerts?.daily_limit_exceeded ?? 1,
        budgetData.alerts?.category_limit_exceeded ?? 1,
        budgetData.alerts?.weekly_summary ?? 0
      ]);

      console.log(`✅ Budget created successfully: ${budgetData.name} (ID: ${budgetId})`);
      return budgetId;
    });
  } catch (error) {
    console.error('❌ Budget creation failed:', error);
    throw new Error(`Bütçe oluşturulamadı: ${error.message}`);
  }
};

/**
 * Gets all budgets with category information and spending data
 * @param {SQLiteDatabase} db - Database instance
 * @param {Object} filters - Filter options
 * @returns {Promise<Array>} Budgets array
 */
export const getBudgets = async (db, filters = {}) => {
  try {
    console.log('📊 Fetching budgets with filters:', filters);
    
    let query = `
      SELECT 
        b.*,
        COUNT(bc.id) as category_count,
        COALESCE(SUM(bc.limit_amount), 0) as total_allocated,
        COALESCE(SUM(bc.spent_amount), 0) as total_spent,
        CASE 
          WHEN b.total_limit > 0 THEN ROUND((COALESCE(SUM(bc.spent_amount), 0) / b.total_limit) * 100, 2)
          WHEN COALESCE(SUM(bc.limit_amount), 0) > 0 THEN ROUND((COALESCE(SUM(bc.spent_amount), 0) / COALESCE(SUM(bc.limit_amount), 1)) * 100, 2)
          ELSE 0
        END as spending_percentage
      FROM budgets b
      LEFT JOIN budget_categories bc ON b.id = bc.budget_id
      WHERE 1=1
    `;

    const params = [];

    // Apply filters
    if (filters.status) {
      query += ' AND b.status = ?';
      params.push(filters.status);
    }

    if (filters.type) {
      query += ' AND b.type = ?';
      params.push(filters.type);
    }

    if (filters.currency) {
      query += ' AND b.currency = ?';
      params.push(filters.currency);
    }

    if (filters.active_only) {
      query += ' AND b.status = "active" AND (b.end_date IS NULL OR DATE(b.end_date) >= DATE("now"))';
    }

    query += ` 
      GROUP BY b.id 
      ORDER BY 
        CASE b.status 
          WHEN 'active' THEN 1 
          WHEN 'paused' THEN 2 
          ELSE 3 
        END,
        b.created_at DESC
    `;

    const budgets = await db.getAllAsync(query, params);
    
    // Add category details for each budget
    const budgetsWithDetails = await Promise.all(
      budgets.map(async (budget) => {
        const categories = await getBudgetCategories(db, budget.id);
        const alertSettings = await getBudgetAlertSettings(db, budget.id);
        
        return {
          ...budget,
          categories,
          alert_settings: alertSettings,
          is_over_budget: budget.spending_percentage > 100,
          days_remaining: calculateDaysRemaining(budget.end_date),
          status_color: getBudgetStatusColor(budget.spending_percentage, budget.status)
        };
      })
    );

    console.log(`✅ Retrieved ${budgetsWithDetails.length} budgets`);
    return budgetsWithDetails;
  } catch (error) {
    console.error('❌ Budget retrieval failed:', error);
    throw new Error(`Bütçeler getirilemedi: ${error.message}`);
  }
};

/**
 * Gets a single budget by ID with complete details
 * @param {SQLiteDatabase} db - Database instance
 * @param {number} budgetId - Budget ID
 * @returns {Promise<Object|null>} Budget object or null
 */
export const getBudgetById = async (db, budgetId) => {
  try {
    console.log(`📋 Fetching budget details for ID: ${budgetId}`);
    
    const budget = await db.getFirstAsync(`
      SELECT 
        b.*,
        COUNT(bc.id) as category_count,
        COALESCE(SUM(bc.limit_amount), 0) as total_allocated,
        COALESCE(SUM(bc.spent_amount), 0) as total_spent
      FROM budgets b
      LEFT JOIN budget_categories bc ON b.id = bc.budget_id
      WHERE b.id = ?
      GROUP BY b.id
    `, [budgetId]);

    if (!budget) {
      console.log(`❌ Budget not found: ${budgetId}`);
      return null;
    }

    // Get detailed category information
    const categories = await getBudgetCategories(db, budgetId);
    const alertSettings = await getBudgetAlertSettings(db, budgetId);
    const history = await getBudgetHistory(db, budgetId);

    const budgetWithDetails = {
      ...budget,
      categories,
      alert_settings: alertSettings,
      history,
      spending_percentage: budget.total_limit > 0 
        ? Math.round((budget.total_spent / budget.total_limit) * 100) 
        : 0,
      is_over_budget: budget.total_spent > budget.total_limit,
      days_remaining: calculateDaysRemaining(budget.end_date),
      status_color: getBudgetStatusColor(budget.total_spent / budget.total_limit * 100, budget.status)
    };

    console.log(`✅ Budget details retrieved: ${budget.name}`);
    return budgetWithDetails;
  } catch (error) {
    console.error('❌ Budget detail retrieval failed:', error);
    throw new Error(`Bütçe detayları getirilemedi: ${error.message}`);
  }
};

/**
 * Gets category limits and spending for a budget
 * @param {SQLiteDatabase} db - Database instance
 * @param {number} budgetId - Budget ID
 * @returns {Promise<Array>} Category budget array
 */
export const getBudgetCategories = async (db, budgetId) => {
  try {
    const categories = await db.getAllAsync(`
      SELECT 
        bc.*,
        c.name as category_name,
        c.icon as category_icon,
        c.color as category_color,
        ROUND((bc.spent_amount / bc.limit_amount) * 100, 2) as usage_percentage,
        (bc.limit_amount - bc.spent_amount) as remaining_amount,
        CASE 
          WHEN bc.spent_amount > bc.limit_amount THEN 'over_budget'
          WHEN bc.spent_amount >= (bc.limit_amount * 0.9) THEN 'warning'
          WHEN bc.spent_amount >= (bc.limit_amount * 0.75) THEN 'caution'
          ELSE 'safe'
        END as status
      FROM budget_categories bc
      INNER JOIN categories c ON bc.category_id = c.id
      WHERE bc.budget_id = ?
      ORDER BY bc.spent_amount DESC, c.name ASC
    `, [budgetId]);

    return categories.map(category => ({
      ...category,
      usage_percentage: Math.min(category.usage_percentage || 0, 100),
      is_over_budget: category.spent_amount > category.limit_amount,
      progress_color: getCategoryProgressColor(category.usage_percentage || 0)
    }));
  } catch (error) {
    console.error('❌ Budget categories retrieval failed:', error);
    return [];
  }
};

/**
 * Updates budget spending amounts from transactions
 * @param {SQLiteDatabase} db - Database instance
 * @param {number} budgetId - Budget ID (optional, updates all if not provided)
 * @returns {Promise<void>}
 */
export const updateBudgetSpending = async (db, budgetId = null) => {
  try {
    console.log(`🔄 Updating budget spending amounts${budgetId ? ` for budget ${budgetId}` : ' for all budgets'}`);
    
    let whereClause = '';
    const params = [];
    
    if (budgetId) {
      whereClause = 'WHERE bc.budget_id = ?';
      params.push(budgetId);
    }
    
    await db.execAsync(`
      UPDATE budget_categories 
      SET spent_amount = (
        SELECT COALESCE(SUM(ABS(t.amount)), 0)
        FROM transactions t
        INNER JOIN budgets b ON budget_categories.budget_id = b.id
        WHERE t.category_id = budget_categories.category_id
          AND t.type = 'expense'
          AND DATE(t.date) >= DATE(b.start_date)
          AND (b.end_date IS NULL OR DATE(t.date) <= DATE(b.end_date))
          AND t.currency = budget_categories.currency
      ),
      updated_at = CURRENT_TIMESTAMP
      ${whereClause}
    `, params);
    
    console.log('✅ Budget spending amounts updated');
  } catch (error) {
    console.error('❌ Budget spending update failed:', error);
    throw new Error(`Bütçe harcama miktarları güncellenemedi: ${error.message}`);
  }
};

/**
 * Gets budget alert settings
 * @param {SQLiteDatabase} db - Database instance
 * @param {number} budgetId - Budget ID
 * @returns {Promise<Object>} Alert settings
 */
export const getBudgetAlertSettings = async (db, budgetId) => {
  try {
    const settings = await db.getFirstAsync(`
      SELECT * FROM budget_alert_settings WHERE budget_id = ?
    `, [budgetId]);
    
    return settings || {
      threshold_75: 1,
      threshold_90: 1,
      threshold_100: 1,
      daily_limit_exceeded: 1,
      category_limit_exceeded: 1,
      weekly_summary: 0
    };
  } catch (error) {
    console.error('❌ Budget alert settings retrieval failed:', error);
    return {};
  }
};

/**
 * Gets budget history for performance analysis
 * @param {SQLiteDatabase} db - Database instance
 * @param {number} budgetId - Budget ID
 * @returns {Promise<Array>} History array
 */
export const getBudgetHistory = async (db, budgetId) => {
  try {
    const history = await db.getAllAsync(`
      SELECT * FROM budget_history 
      WHERE budget_id = ? 
      ORDER BY period_start DESC 
      LIMIT 12
    `, [budgetId]);
    
    return history;
  } catch (error) {
    console.error('❌ Budget history retrieval failed:', error);
    return [];
  }
};

/**
 * Deletes a budget and all related data
 * @param {SQLiteDatabase} db - Database instance
 * @param {number} budgetId - Budget ID
 * @returns {Promise<boolean>} Success status
 */
export const deleteBudget = async (db, budgetId) => {
  try {
    console.log(`🗑️ Deleting budget: ${budgetId}`);
    
    return await db.withTransactionAsync(async () => {
      // Delete related data (cascading deletes should handle this, but explicit is better)
      await db.runAsync('DELETE FROM budget_notifications WHERE budget_id = ?', [budgetId]);
      await db.runAsync('DELETE FROM budget_history WHERE budget_id = ?', [budgetId]);
      await db.runAsync('DELETE FROM budget_alert_settings WHERE budget_id = ?', [budgetId]);
      await db.runAsync('DELETE FROM budget_categories WHERE budget_id = ?', [budgetId]);
      
      // Delete main budget record
      const result = await db.runAsync('DELETE FROM budgets WHERE id = ?', [budgetId]);
      
      const success = result.changes > 0;
      console.log(success ? '✅ Budget deleted successfully' : '❌ Budget not found');
      return success;
    });
  } catch (error) {
    console.error('❌ Budget deletion failed:', error);
    throw new Error(`Bütçe silinemedi: ${error.message}`);
  }
};

/**
 * Gets budget templates
 * @param {SQLiteDatabase} db - Database instance
 * @returns {Promise<Array>} Templates array
 */
export const getBudgetTemplates = async (db) => {
  try {
    const templates = await db.getAllAsync(`
      SELECT * FROM budget_templates 
      ORDER BY user_created ASC, name ASC
    `);
    
    return templates.map(template => ({
      ...template,
      template_data: JSON.parse(template.template_data)
    }));
  } catch (error) {
    console.error('❌ Budget templates retrieval failed:', error);
    return [];
  }
};

/**
 * Utility function to calculate days remaining in budget period
 * @param {string} endDate - End date string
 * @returns {number} Days remaining (null if no end date)
 */
const calculateDaysRemaining = (endDate) => {
  if (!endDate) return null;
  
  const end = new Date(endDate);
  const now = new Date();
  const diffTime = end - now;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return Math.max(0, diffDays);
};

/**
 * Gets budget status color based on spending percentage
 * @param {number} percentage - Spending percentage
 * @param {string} status - Budget status
 * @returns {string} Color code
 */
const getBudgetStatusColor = (percentage, status) => {
  if (status !== 'active') return '#666666';
  
  if (percentage >= 100) return '#ff7675'; // Red - over budget
  if (percentage >= 90) return '#fdcb6e'; // Orange - warning
  if (percentage >= 75) return '#e17055'; // Yellow - caution
  return '#00cec9'; // Green - safe
};

/**
 * Gets category progress color based on usage percentage
 * @param {number} percentage - Usage percentage
 * @returns {string} Color code
 */
const getCategoryProgressColor = (percentage) => {
  if (percentage >= 100) return '#ff7675'; // Red
  if (percentage >= 90) return '#fdcb6e'; // Orange
  if (percentage >= 75) return '#fab1a0'; // Light orange
  return '#00cec9'; // Green
};
