/**
 * Harcama Hızı Göstergesi Komponenti
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md - Stage 3, Phase 1
 * 
 * Harcama hızı ve trend analizi göstergesi
 * Maksimum 200 satır - Tek sorumluluk prensibi
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../../../context/ThemeContext';

/**
 * Harcama hızı göstergesi komponenti
 * @param {Object} props - Component props
 * @param {number} props.currentVelocity - Mevcut harcama hızı (günlük)
 * @param {number} props.targetVelocity - Hedef harcama hızı (günlük)
 * @param {string} props.trend - Harcama trendi ('increasing', 'decreasing', 'stable')
 * @param {number} props.velocityChange - Hız değişim yüzdesi
 * @param {string} props.currency - Para birimi ('TRY', 'USD', 'EUR')
 * @param {Object} props.theme - <PERSON><PERSON> obje<PERSON> (opsiyonel, context'ten alınır)
 */
const SpendingVelocityIndicator = ({ 
  currentVelocity = 0, 
  targetVelocity = 0, 
  trend = 'stable',
  velocityChange = 0,
  currency = 'TRY',
  theme: propTheme 
}) => {
  const { theme, isDarkMode } = useTheme();
  const currentTheme = propTheme || theme;

  /**
   * Para birimi sembolü
   * @returns {string} Para birimi sembolü
   */
  const getCurrencySymbol = () => {
    switch (currency) {
      case 'USD':
        return '$';
      case 'EUR':
        return '€';
      default:
        return '₺';
    }
  };

  /**
   * Hız durumu rengi belirleme
   * @returns {string} Durum rengi
   */
  const getVelocityColor = () => {
    const ratio = targetVelocity > 0 ? currentVelocity / targetVelocity : 0;
    
    if (ratio > 1.2) return currentTheme.ERROR;
    if (ratio > 1.0) return currentTheme.WARNING;
    if (ratio > 0.8) return currentTheme.SUCCESS;
    return currentTheme.INFO;
  };

  /**
   * Trend ikonu belirleme
   * @returns {string} İkon adı
   */
  const getTrendIcon = () => {
    switch (trend) {
      case 'increasing':
        return 'trending-up';
      case 'decreasing':
        return 'trending-down';
      default:
        return 'trending-flat';
    }
  };

  /**
   * Trend rengi belirleme
   * @returns {string} Trend rengi
   */
  const getTrendColor = () => {
    switch (trend) {
      case 'increasing':
        return currentVelocity > targetVelocity ? currentTheme.ERROR : currentTheme.WARNING;
      case 'decreasing':
        return currentTheme.SUCCESS;
      default:
        return currentTheme.INFO;
    }
  };

  /**
   * Durum mesajı belirleme
   * @returns {string} Durum mesajı
   */
  const getStatusMessage = () => {
    const ratio = targetVelocity > 0 ? currentVelocity / targetVelocity : 0;
    
    if (ratio > 1.2) return 'Çok hızlı harcıyorsunuz!';
    if (ratio > 1.0) return 'Hedefin üzerinde harcama';
    if (ratio > 0.8) return 'İdeal harcama hızı';
    if (ratio > 0.5) return 'Yavaş harcama';
    return 'Çok az harcama';
  };

  /**
   * Trend mesajı belirleme
   * @returns {string} Trend mesajı
   */
  const getTrendMessage = () => {
    switch (trend) {
      case 'increasing':
        return `Harcama %${Math.abs(velocityChange).toFixed(1)} arttı`;
      case 'decreasing':
        return `Harcama %${Math.abs(velocityChange).toFixed(1)} azaldı`;
      default:
        return 'Harcama stabil';
    }
  };

  const velocityColor = getVelocityColor();
  const trendIcon = getTrendIcon();
  const trendColor = getTrendColor();
  const statusMessage = getStatusMessage();
  const trendMessage = getTrendMessage();
  const currencySymbol = getCurrencySymbol();

  return (
    <View style={[styles.container, { backgroundColor: currentTheme.SURFACE }]}>
      {/* Header */}
      <View style={styles.header}>
        <MaterialIcons name="speed" size={20} color={currentTheme.PRIMARY} />
        <Text style={[styles.title, { color: currentTheme.TEXT_PRIMARY }]}>
          Harcama Hızı
        </Text>
        <View style={[styles.trendIcon, { backgroundColor: trendColor + '20' }]}>
          <MaterialIcons name={trendIcon} size={16} color={trendColor} />
        </View>
      </View>

      {/* Hız göstergesi */}
      <View style={styles.velocityDisplay}>
        <View style={styles.currentVelocity}>
          <Text style={[styles.velocityLabel, { color: currentTheme.TEXT_SECONDARY }]}>
            Günlük Ortalama
          </Text>
          <View style={styles.velocityAmount}>
            <Text style={[styles.currencySymbol, { color: velocityColor }]}>
              {currencySymbol}
            </Text>
            <Text style={[styles.velocityValue, { color: velocityColor }]}>
              {currentVelocity.toLocaleString('tr-TR', { maximumFractionDigits: 0 })}
            </Text>
          </View>
        </View>

        <View style={styles.velocityDivider}>
          <Text style={[styles.dividerText, { color: currentTheme.TEXT_SECONDARY }]}>
            /
          </Text>
        </View>

        <View style={styles.targetVelocity}>
          <Text style={[styles.velocityLabel, { color: currentTheme.TEXT_SECONDARY }]}>
            Hedef
          </Text>
          <View style={styles.velocityAmount}>
            <Text style={[styles.currencySymbol, { color: currentTheme.TEXT_PRIMARY }]}>
              {currencySymbol}
            </Text>
            <Text style={[styles.velocityValue, { color: currentTheme.TEXT_PRIMARY }]}>
              {targetVelocity.toLocaleString('tr-TR', { maximumFractionDigits: 0 })}
            </Text>
          </View>
        </View>
      </View>

      {/* İlerleme çubuğu */}
      <View style={styles.progressSection}>
        <View style={[styles.progressTrack, { backgroundColor: currentTheme.BORDER }]}>
          <View 
            style={[
              styles.progressFill,
              {
                backgroundColor: velocityColor,
                width: `${Math.min((currentVelocity / targetVelocity) * 100, 100)}%`
              }
            ]} 
          />
        </View>
      </View>

      {/* Durum ve trend bilgileri */}
      <View style={styles.infoSection}>
        <View style={[styles.statusMessage, { backgroundColor: velocityColor + '10' }]}>
          <Text style={[styles.statusText, { color: velocityColor }]}>
            {statusMessage}
          </Text>
        </View>

        <View style={styles.trendInfo}>
          <MaterialIcons name={trendIcon} size={16} color={trendColor} />
          <Text style={[styles.trendText, { color: trendColor }]}>
            {trendMessage}
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '500',
    flex: 1,
  },
  trendIcon: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },
  velocityDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  currentVelocity: {
    flex: 1,
    alignItems: 'center',
  },
  targetVelocity: {
    flex: 1,
    alignItems: 'center',
  },
  velocityDivider: {
    paddingHorizontal: 16,
  },
  dividerText: {
    fontSize: 20,
    fontWeight: '300',
  },
  velocityLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  velocityAmount: {
    flexDirection: 'row',
    alignItems: 'baseline',
    gap: 2,
  },
  currencySymbol: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  velocityValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  progressSection: {
    marginBottom: 16,
  },
  progressTrack: {
    height: 6,
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  infoSection: {
    gap: 8,
  },
  statusMessage: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  statusText: {
    fontSize: 14,
    fontWeight: '500',
  },
  trendInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 6,
  },
  trendText: {
    fontSize: 13,
    fontWeight: '500',
  },
});

export default SpendingVelocityIndicator;
