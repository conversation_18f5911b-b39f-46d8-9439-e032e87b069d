import React from 'react';
import { TouchableOpacity, Text, ActivityIndicator, StyleSheet } from 'react-native';

/**
 * Özelleştirilmiş button bileşeni
 * @param {Object} props Component props
 * @param {string} props.title Button metni
 * @param {Function} props.onPress Tıklama olayı
 * @param {boolean} props.disabled Devre dışı durumu
 * @param {boolean} props.loading Yükleniyor durumu
 * @param {Object} props.style Ek stiller
 */
export const Button = ({ 
  title, 
  onPress, 
  disabled = false, 
  loading = false,
  style
}) => (
  <TouchableOpacity
    style={[
      styles.button,
      disabled && styles.disabled,
      style
    ]}
    onPress={onPress}
    disabled={disabled || loading}
  >
    {loading ? (
      <ActivityIndicator color="#fff" />
    ) : (
      <Text style={styles.text}>{title}</Text>
    )}
  </TouchableOpacity>
);

const styles = StyleSheet.create({
  button: {
    backgroundColor: '#3498db',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    marginVertical: 8
  },
  disabled: {
    backgroundColor: '#bdc3c7',
    opacity: 0.7
  },
  text: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600'
  }
});
