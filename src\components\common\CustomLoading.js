import React from 'react';
import { View, StyleSheet, ActivityIndicator } from 'react-native';
import { useTheme } from '../../theme/ThemeProvider';

/**
 * Tema uyumlu yükleme göstergesi bileşeni
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {string} [props.size='large'] - <PERSON><PERSON><PERSON><PERSON><PERSON> boyut<PERSON> ('small' veya 'large')
 * @param {string} [props.color] - <PERSON><PERSON><PERSON><PERSON><PERSON> reng<PERSON> (tema rengini geçersiz kılar)
 * @param {Object} [props.style] - Ek stiller
 * @returns {JSX.Element} Yükleme göstergesi
 */
const CustomLoading = ({ size = 'large', color, style }) => {
  const { theme } = useTheme();
  
  return (
    <View style={[styles.container, style]}>
      <ActivityIndicator 
        size={size} 
        color={color || theme.colors.primary} 
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default CustomLoading;
