import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../../../context/ThemeContext';
import { formatCurrency } from '../../../../utils/reportUtils';

/**
 * Tablo önizleme bileşeni - yapılandırılmış tablonun önizlemesini gösterir
 * @param {Object} props - Bileşen props'ları
 * @param {Array} props.data - Tablo verisi
 * @param {Array} props.columns - Sütun yapılandırması
 * @param {Array} props.filters - Aktif filtreler
 * @param {Object} props.sorting - Sıralama yapılandırması
 * @param {Function} props.onSort - Sıralama değiştiğinde çağrılacak fonksiyon
 * @param {Number} props.pageSize - Sayfa başına öğe sayısı
 * @param {Number} props.currentPage - Mevcut sayfa
 * @param {Function} props.onPageChange - Sayfa değiştiğinde çağrılacak fonksiyon
 * @param {Boolean} props.loading - Yükleme durumu
 */
const TablePreview = ({ 
  data = [], 
  columns = [], 
  filters = [], 
  sorting = { column: null, direction: 'asc' },
  onSort,
  pageSize = 10,
  currentPage = 1,
  onPageChange,
  loading = false
}) => {
  const { theme } = useTheme();
  const [filteredData, setFilteredData] = useState([]);
  const [sortedData, setSortedData] = useState([]);
  const [paginatedData, setPaginatedData] = useState([]);
  const [totalPages, setTotalPages] = useState(0);

  // Tema güvenlik kontrolü
  if (!theme) {
    console.error('TablePreview: Theme objesi undefined!');
    return null;
  }

  const styles = {
    container: {
      flex: 1,
      backgroundColor: theme.BACKGROUND || theme.colors?.background || '#fff',
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: 16,
      backgroundColor: theme.SURFACE || theme.colors?.surface || '#f9f9f9',
      borderBottomWidth: 1,
      borderBottomColor: theme.BORDER || theme.colors?.border || '#e0e0e0',
    },
    headerTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
    },
    infoText: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    tableContainer: {
      flex: 1,
    },
    table: {
      backgroundColor: theme.colors.surface,
    },
    tableHeader: {
      flexDirection: 'row',
      backgroundColor: theme.colors.surfaceVariant,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
      minHeight: 44,
    },
    headerCell: {
      flex: 1,
      paddingHorizontal: 12,
      paddingVertical: 8,
      justifyContent: 'center',
      minWidth: 100,
    },
    sortableHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    headerText: {
      fontSize: 12,
      fontWeight: '600',
      color: theme.colors.text,
      flex: 1,
    },
    sortIcon: {
      marginLeft: 4,
    },
    tableBody: {
      flex: 1,
    },
    tableRow: {
      flexDirection: 'row',
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
      minHeight: 44,
    },
    tableRowEven: {
      backgroundColor: theme.colors.background,
    },
    tableRowOdd: {
      backgroundColor: theme.colors.surface,
    },
    tableCell: {
      flex: 1,
      paddingHorizontal: 12,
      paddingVertical: 8,
      justifyContent: 'center',
      minWidth: 100,
    },
    cellText: {
      fontSize: 12,
      color: theme.colors.text,
    },
    cellTextNumber: {
      textAlign: 'right',
      fontWeight: '500',
    },
    cellTextDate: {
      color: theme.colors.textSecondary,
    },
    emptyState: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 32,
    },
    emptyStateText: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      marginTop: 16,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 32,
    },
    loadingText: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      marginTop: 16,
    },
    pagination: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: 16,
      backgroundColor: theme.colors.surface,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
    },
    paginationText: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    paginationButtons: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    paginationButton: {
      padding: 8,
      marginHorizontal: 4,
      borderRadius: 4,
      backgroundColor: theme.colors.surfaceVariant,
    },
    paginationButtonDisabled: {
      opacity: 0.5,
    },
    paginationButtonText: {
      fontSize: 12,
      color: theme.colors.text,
    },
    summary: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      alignItems: 'center',
      padding: 12,
      backgroundColor: theme.colors.surfaceVariant,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
    },
    summaryItem: {
      alignItems: 'center',
    },
    summaryValue: {
      fontSize: 14,
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    summaryLabel: {
      fontSize: 10,
      color: theme.colors.textSecondary,
      marginTop: 2,
    },
  };

  /**
   * Verileri filtrele
   */
  const applyFilters = () => {
    if (filters.length === 0) {
      setFilteredData(data);
      return;
    }

    const filtered = data.filter(item => {
      return filters.every(filter => {
        const value = item[filter.column];
        const filterValue = filter.value;

        if (value === null || value === undefined) return false;

        switch (filter.operator) {
          case 'equals':
            return value.toString().toLowerCase() === filterValue.toLowerCase();
          case 'contains':
            return value.toString().toLowerCase().includes(filterValue.toLowerCase());
          case 'startsWith':
            return value.toString().toLowerCase().startsWith(filterValue.toLowerCase());
          case 'endsWith':
            return value.toString().toLowerCase().endsWith(filterValue.toLowerCase());
          case 'notContains':
            return !value.toString().toLowerCase().includes(filterValue.toLowerCase());
          case 'greaterThan':
            return parseFloat(value) > parseFloat(filterValue);
          case 'lessThan':
            return parseFloat(value) < parseFloat(filterValue);
          case 'greaterThanOrEqual':
            return parseFloat(value) >= parseFloat(filterValue);
          case 'lessThanOrEqual':
            return parseFloat(value) <= parseFloat(filterValue);
          case 'after':
            return new Date(value) > new Date(filterValue);
          case 'before':
            return new Date(value) < new Date(filterValue);
          default:
            return true;
        }
      });
    });

    setFilteredData(filtered);
  };

  /**
   * Verileri sırala
   */
  const applySorting = () => {
    if (!sorting.column) {
      setSortedData(filteredData);
      return;
    }

    const sorted = [...filteredData].sort((a, b) => {
      const aValue = a[sorting.column];
      const bValue = b[sorting.column];

      // Null/undefined değerleri en sona koy
      if (aValue === null || aValue === undefined) return 1;
      if (bValue === null || bValue === undefined) return -1;

      // Sayısal karşılaştırma
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sorting.direction === 'asc' ? aValue - bValue : bValue - aValue;
      }

      // Tarih karşılaştırması
      if (aValue instanceof Date && bValue instanceof Date) {
        return sorting.direction === 'asc' 
          ? aValue.getTime() - bValue.getTime() 
          : bValue.getTime() - aValue.getTime();
      }

      // String karşılaştırması
      const aStr = aValue.toString().toLowerCase();
      const bStr = bValue.toString().toLowerCase();
      
      if (sorting.direction === 'asc') {
        return aStr.localeCompare(bStr, 'tr');
      } else {
        return bStr.localeCompare(aStr, 'tr');
      }
    });

    setSortedData(sorted);
  };

  /**
   * Sayfalama uygula
   */
  const applyPagination = () => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginated = sortedData.slice(startIndex, endIndex);
    
    setPaginatedData(paginated);
    setTotalPages(Math.ceil(sortedData.length / pageSize));
  };

  /**
   * Sıralama değiştirme
   * @param {string} columnId - Sıralanacak sütun ID'si
   */
  const handleSort = (columnId) => {
    const newDirection = sorting.column === columnId && sorting.direction === 'asc' ? 'desc' : 'asc';
    onSort({ column: columnId, direction: newDirection });
  };

  /**
   * Hücre değerini formatla
   * @param {*} value - Hücre değeri
   * @param {Object} column - Sütun yapılandırması
   * @returns {string} Formatlanmış değer
   */
  const formatCellValue = (value, column) => {
    if (value === null || value === undefined) return '-';

    switch (column.type) {
      case 'number':
        return typeof value === 'number' ? value.toLocaleString('tr-TR') : value;
      case 'currency':
        return formatCurrency(value);
      case 'date':
        return new Date(value).toLocaleDateString('tr-TR');
      case 'percentage':
        return `${(value * 100).toFixed(2)}%`;
      default:
        return value.toString();
    }
  };

  /**
   * Özet istatistikleri hesapla
   * @returns {Object} Özet istatistikleri
   */
  const calculateSummary = () => {
    const numericColumns = columns.filter(col => 
      col.type === 'number' || col.type === 'currency'
    );

    const summary = {
      totalRows: sortedData.length,
      filteredRows: filteredData.length,
      columns: {}
    };

    numericColumns.forEach(column => {
      const values = sortedData
        .map(row => row[column.id])
        .filter(val => val !== null && val !== undefined && !isNaN(val));

      if (values.length > 0) {
        summary.columns[column.id] = {
          sum: values.reduce((sum, val) => sum + parseFloat(val), 0),
          avg: values.reduce((sum, val) => sum + parseFloat(val), 0) / values.length,
          min: Math.min(...values),
          max: Math.max(...values),
        };
      }
    });

    return summary;
  };

  // Effects
  useEffect(() => {
    applyFilters();
  }, [data, filters]);

  useEffect(() => {
    applySorting();
  }, [filteredData, sorting]);

  useEffect(() => {
    applyPagination();
  }, [sortedData, currentPage, pageSize]);

  const summary = calculateSummary();
  const visibleColumns = columns.filter(col => col.visible !== false);

  if (loading) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <Ionicons name="hourglass" size={48} color={theme.colors.textSecondary} />
          <Text style={styles.loadingText}>Veriler yükleniyor...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Tablo Önizleme</Text>
        <Text style={styles.infoText}>
          {filteredData.length} / {data.length} kayıt
        </Text>
      </View>

      <ScrollView style={styles.tableContainer} horizontal showsHorizontalScrollIndicator={false}>
        <View style={styles.table}>
          {/* Tablo Başlığı */}
          <View style={styles.tableHeader}>
            {visibleColumns.map(column => (
              <TouchableOpacity
                key={column.id}
                style={styles.headerCell}
                onPress={() => column.sortable && handleSort(column.id)}
              >
                <View style={styles.sortableHeader}>
                  <Text style={styles.headerText}>{column.name}</Text>
                  {column.sortable && (
                    <Ionicons
                      name={
                        sorting.column === column.id
                          ? sorting.direction === 'asc'
                            ? 'chevron-up'
                            : 'chevron-down'
                          : 'remove'
                      }
                      size={16}
                      color={
                        sorting.column === column.id
                          ? theme.colors.primary
                          : theme.colors.textSecondary
                      }
                      style={styles.sortIcon}
                    />
                  )}
                </View>
              </TouchableOpacity>
            ))}
          </View>

          {/* Tablo Gövdesi */}
          <ScrollView style={styles.tableBody}>
            {paginatedData.length > 0 ? (
              paginatedData.map((row, index) => (
                <View
                  key={index}
                  style={[
                    styles.tableRow,
                    index % 2 === 0 ? styles.tableRowEven : styles.tableRowOdd
                  ]}
                >
                  {visibleColumns.map(column => (
                    <View key={column.id} style={styles.tableCell}>
                      <Text
                        style={[
                          styles.cellText,
                          column.type === 'number' || column.type === 'currency'
                            ? styles.cellTextNumber
                            : null,
                          column.type === 'date' ? styles.cellTextDate : null,
                        ]}
                      >
                        {formatCellValue(row[column.id], column)}
                      </Text>
                    </View>
                  ))}
                </View>
              ))
            ) : (
              <View style={styles.emptyState}>
                <Ionicons name="document-outline" size={48} color={theme.colors.textSecondary} />
                <Text style={styles.emptyStateText}>
                  {filters.length > 0 ? 'Filtreye uygun kayıt bulunamadı' : 'Henüz veri yok'}
                </Text>
              </View>
            )}
          </ScrollView>
        </View>
      </ScrollView>

      {/* Sayfalama */}
      {totalPages > 1 && (
        <View style={styles.pagination}>
          <Text style={styles.paginationText}>
            Sayfa {currentPage} / {totalPages}
          </Text>
          <View style={styles.paginationButtons}>
            <TouchableOpacity
              style={[
                styles.paginationButton,
                currentPage === 1 && styles.paginationButtonDisabled
              ]}
              onPress={() => currentPage > 1 && onPageChange(currentPage - 1)}
              disabled={currentPage === 1}
            >
              <Ionicons name="chevron-back" size={16} color={theme.colors.text} />
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.paginationButton,
                currentPage === totalPages && styles.paginationButtonDisabled
              ]}
              onPress={() => currentPage < totalPages && onPageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              <Ionicons name="chevron-forward" size={16} color={theme.colors.text} />
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Özet İstatistikleri */}
      {sortedData.length > 0 && (
        <View style={styles.summary}>
          <View style={styles.summaryItem}>
            <Text style={styles.summaryValue}>{summary.totalRows}</Text>
            <Text style={styles.summaryLabel}>Toplam Kayıt</Text>
          </View>
          <View style={styles.summaryItem}>
            <Text style={styles.summaryValue}>{visibleColumns.length}</Text>
            <Text style={styles.summaryLabel}>Sütun</Text>
          </View>
          <View style={styles.summaryItem}>
            <Text style={styles.summaryValue}>{filters.length}</Text>
            <Text style={styles.summaryLabel}>Aktif Filtre</Text>
          </View>
        </View>
      )}
    </View>
  );
};

export default TablePreview;
