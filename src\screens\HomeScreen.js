import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, RefreshControl, Alert, Modal } from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Colors } from '../constants/colors';
import { useTheme } from '../context/ThemeContext';
import { useAppContext } from '../context/AppContext';
import TransactionForm from '../components/TransactionForm';
import TransactionList from '../components/TransactionList';
import BalanceSummary from '../components/BalanceSummary';
import QuickTransactionInput from '../components/QuickTransactionInput';
import CustomizableWidget from '../components/CustomizableWidget';
// New modern components
import ModernHomeHeader from '../components/home/<USER>';
import WidgetContainer from '../components/home/<USER>';
// import ComingSoonWidget from '../components/common/ComingSoonWidget';

// Design System imports - temporarily commented out
/*
import {
  Button,
  AnimatedButton,
  Card,
  CardHeader,
  CardBody,
  Typography,
  Title1,
  Title2,
  Headline,
  Body,
  Caption,
  useDesignSystem,
  responsive,
  flex,
  AnimationUtils
} from '../design-system';
*/

/**
 * Ana sayfa ekranı - Modern Home Screen with new design
 *
 * @returns {JSX.Element} Ana sayfa ekranı
 */
function HomeScreen({ navigation }) {
  const insets = useSafeAreaInsets();
  const db = useSQLiteContext();
  const { theme } = useTheme();
  const { 
    userProfile, 
    homeWidgets, 
    notifications, 
    fabConfig,
    updateWidgetConfig,
    markNotificationsRead 
  } = useAppContext();

  const [transactions, setTransactions] = useState([]);
  const [isAddingTransaction, setIsAddingTransaction] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [balanceSummary, setBalanceSummary] = useState({
    income: 0,
    expense: 0,
    balance: 0
  });

  // Modern widget system state
  const [lastTransaction, setLastTransaction] = useState(null);
  const [quickStats, setQuickStats] = useState({
    thisMonth: 0,
    lastWeek: 0,
    trend: 'neutral'
  });

  // Widget settings modal (legacy - will be replaced)
  const [showWidgetSettings, setShowWidgetSettings] = useState(false);

  // Modern data loading functions
  const loadData = async () => {
    try {
      // Son 5 işlemi al
      const recentTransactions = await db.getAllAsync(`
        SELECT t.*, c.name as category_name, c.color as category_color, c.icon as category_icon
        FROM transactions t
        LEFT JOIN categories c ON t.category_id = c.id
        ORDER BY t.date DESC, t.id DESC
        LIMIT 5
      `);

      setTransactions(recentTransactions);

      // Son işlemi ayarla
      if (recentTransactions.length > 0) {
        setLastTransaction(recentTransactions[0]);
      }

      // Özet bilgileri al
      const incomeResult = await db.getFirstAsync(`
        SELECT SUM(amount) as total
        FROM transactions
        WHERE type = 'income'
      `);

      const expenseResult = await db.getFirstAsync(`
        SELECT SUM(amount) as total
        FROM transactions
        WHERE type = 'expense'
      `);

      const income = incomeResult?.total || 0;
      const expense = expenseResult?.total || 0;

      setBalanceSummary({
        income,
        expense,
        balance: income - expense
      });

      // Hızlı istatistikler hesapla
      await loadQuickStats();
    } catch (error) {
      console.error('Veri yükleme hatası:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Hızlı istatistikleri yükle
  const loadQuickStats = async () => {
    try {
      // Bu ay ve geçen ay verilerini al
      const currentMonth = new Date().getMonth() + 1;
      const currentYear = new Date().getFullYear();
      
      // TODO: Gerçek trend hesaplama implementasyonu
      setQuickStats({
        thisMonth: 0,
        lastWeek: 0,
        trend: 'neutral'
      });
    } catch (error) {
      console.error('İstatistik yükleme hatası:', error);
    }
  };

  // Modern handlers for new components
  const handleHeaderAction = (action) => {
    switch (action) {
      case 'SETTINGS':
        // Navigate to settings tab
        navigation.getParent()?.navigate('Ayarlar');
        break;
      case 'STATS':
        // Navigate to statistics screen
        navigation.getParent()?.navigate('Özellikler', { 
          screen: 'Statistics'
        });
        break;
      case 'NOTIFICATIONS':
        markNotificationsRead();
        // Navigate to notifications screen
        navigation.navigate('Notifications');
        break;
      case 'PROFILE':
        // Navigate to profile/user settings
        navigation.navigate('UserProfile');
        break;
      default:
        break;
    }
  };

  const handleDefaultWidgetAction = (action) => {
    switch (action) {
      case 'CUSTOMIZE':
        setShowWidgetSettings(true);
        break;
      case 'ADD_INCOME':
        setIsAddingTransaction(true);
        break;
      case 'ADD_EXPENSE':
        setIsAddingTransaction(true);
        break;
      case 'VIEW_STATS':
        // Navigate to statistics screen
        navigation.getParent()?.navigate('Özellikler', { 
          screen: 'Statistics'
        });
        break;
      default:
        break;
    }
  };

  const handleWidgetAction = (widget, action, data) => {
    switch (action) {
      case 'income':
        setIsAddingTransaction(true);
        break;
      case 'expense':
        setIsAddingTransaction(true);
        break;
      case 'balance':
        // Navigate to statistics screen
        navigation.getParent()?.navigate('Özellikler', { 
          screen: 'Statistics'
        });
        break;
      case 'transaction':
        // Navigate to transaction edit screen
        navigation.navigate('TransactionEdit', { transaction: data });
        break;
      case 'viewAll':
        // Navigate to transactions screen
        navigation.getParent()?.navigate('İşlemler');
        break;
      case 'quickAction':
        // Handle quick actions from customizable widget
        switch (data) {
          case 'ADD_INCOME':
            setIsAddingTransaction(true);
            break;
          case 'ADD_EXPENSE':
            setIsAddingTransaction(true);
            break;
          case 'VIEW_STATS':
            navigation.getParent()?.navigate('Özellikler', { 
              screen: 'Statistics'
            });
            break;
          default:
            break;
        }
        break;
      default:
        // Default widget press - navigate to relevant screen
        if (widget.type === 'BALANCE_SUMMARY') {
          navigation.getParent()?.navigate('Özellikler', { 
            screen: 'Statistics'
          });
        } else if (widget.type === 'RECENT_TRANSACTIONS') {
          navigation.getParent()?.navigate('İşlemler');
        }
        break;
    }
  };

  const handleWidgetReorder = (newWidgets) => {
    updateWidgetConfig(newWidgets);
  };

  // İlk yükleme
  useEffect(() => {
    loadData();
  }, []);

  // Yenileme işlemi
  const onRefresh = () => {
    setRefreshing(true);
    loadData();
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      {/* Modern Personalized Header */}
      <ModernHomeHeader
        userName={userProfile.name || 'Kullanıcı'}
        userAvatar={userProfile.avatar || 'person'}
        currentBalance={balanceSummary.balance}
        unreadNotifications={notifications.unread || 0}
        onSettingsPress={() => handleHeaderAction('SETTINGS')}
        onStatsPress={() => handleHeaderAction('STATS')}
        onNotificationPress={() => handleHeaderAction('NOTIFICATIONS')}
        onProfilePress={() => handleHeaderAction('PROFILE')}
      />

      {/* Main Content */}
      <ScrollView
        style={[styles.content, { backgroundColor: theme.BACKGROUND }]}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Quick Transaction Input - Always visible */}
        <QuickTransactionInput onTransactionAdded={loadData} />

        {/* Modern Widget Container */}
        <WidgetContainer
          widgets={homeWidgets}
          widgetData={{
            balanceSummary,
            recentTransactions: transactions,
            lastTransaction,
            quickStats,
            onRefresh: loadData,
          }}
          onWidgetPress={handleWidgetAction}
          onWidgetReorder={handleWidgetReorder}
          onWidgetCustomize={() => setShowWidgetSettings(true)}
          onDefaultWidgetAction={handleDefaultWidgetAction}
        />
      </ScrollView>

      {/* Transaction Form Modal */}
      {isAddingTransaction && (
        <TransactionForm
          visible={isAddingTransaction}
          onClose={() => setIsAddingTransaction(false)}
          onSave={() => {
            setIsAddingTransaction(false);
            loadData();
          }}
        />
      )}

      {/* Legacy Widget Settings Modal - will be updated in next phase */}
      <Modal
        visible={showWidgetSettings}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowWidgetSettings(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modernModalContent, { backgroundColor: theme.CARD }]}>
            <View style={[styles.modernModalHeader, { borderBottomColor: theme.BORDER }]}>
              <Text style={[styles.modalTitle, { color: theme.TEXT_PRIMARY }]}>
                Ana Sayfa Widget'ları
              </Text>
              <TouchableOpacity
                onPress={() => setShowWidgetSettings(false)}
                style={styles.modalCloseButton}
              >
                <MaterialIcons name="close" size={24} color={theme.TEXT_PRIMARY} />
              </TouchableOpacity>
            </View>

            <View style={styles.modernModalBody}>
              <Text style={[styles.modalDescription, { color: theme.TEXT_SECONDARY }]}>
                Ana sayfada görmek istediğiniz widget'ları seçin:
              </Text>

              {/* Widget Options */}
              {homeWidgets.map((widget) => (
                <TouchableOpacity
                  key={widget.id}
                  style={[
                    styles.widgetOption, 
                    { 
                      borderColor: widget.enabled ? theme.PRIMARY : theme.BORDER,
                      backgroundColor: widget.enabled ? theme.PRIMARY + '20' : 'transparent'
                    }
                  ]}
                  onPress={() => {
                    const updatedWidgets = homeWidgets.map(w => 
                      w.id === widget.id ? { ...w, enabled: !w.enabled } : w
                    );
                    updateWidgetConfig(updatedWidgets);
                  }}
                >
                  <View style={styles.widgetOptionLeft}>
                    <View style={[styles.widgetOptionIcon, { backgroundColor: theme.PRIMARY + '20' }]}>
                      <MaterialIcons
                        name={widget.type === 'BALANCE_SUMMARY' ? 'account-balance-wallet' : 
                              widget.type === 'RECENT_TRANSACTIONS' ? 'receipt-long' : 'dashboard'}
                        size={24}
                        color={theme.PRIMARY}
                      />
                    </View>
                    <View style={styles.widgetOptionText}>
                      <Text style={[styles.widgetOptionTitle, { color: theme.TEXT_PRIMARY }]}>
                        {widget.type === 'BALANCE_SUMMARY' ? 'Bakiye Özeti' : 
                         widget.type === 'RECENT_TRANSACTIONS' ? 'Son İşlemler' : 'Özel Widget'}
                      </Text>
                      <Text style={[styles.widgetOptionDesc, { color: theme.TEXT_SECONDARY }]}>
                        {widget.type === 'BALANCE_SUMMARY' ? 'Toplam gelir, gider ve bakiye bilgileri' : 
                         widget.type === 'RECENT_TRANSACTIONS' ? 'En son eklenen 5 işlem' : 'Özelleştirilebilir dashboard widget\'ı'}
                      </Text>
                    </View>
                  </View>
                  <MaterialIcons
                    name={widget.enabled ? "check-circle" : "radio-button-unchecked"}
                    size={24}
                    color={widget.enabled ? theme.SUCCESS : theme.TEXT_SECONDARY}
                  />
                </TouchableOpacity>
              ))}
            </View>

            <View style={[styles.modernModalFooter, { borderTopColor: theme.BORDER }]}>
              <TouchableOpacity
                style={[styles.modalSaveButton, { backgroundColor: theme.PRIMARY }]}
                onPress={() => setShowWidgetSettings(false)}
              >
                <Text style={[styles.modalSaveButtonText, { color: '#fff' }]}>
                  Tamam
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 0, // Header'ın altında boşluk olmasın
  },
  // Modern Modal styles
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  modernModalContent: {
    width: '90%',
    maxWidth: 400,
    maxHeight: '80%',
    borderRadius: 16,
  },
  modernModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
  },
  modernModalBody: {
    padding: 20,
    maxHeight: 400,
  },
  modernModalFooter: {
    padding: 20,
    borderTopWidth: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  modalCloseButton: {
    padding: 4,
  },
  modalDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 20,
  },
  widgetOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderRadius: 12,
    marginBottom: 12,
  },
  widgetOptionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  widgetOptionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  widgetOptionText: {
    flex: 1,
  },
  widgetOptionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  widgetOptionDesc: {
    fontSize: 12,
    lineHeight: 16,
  },
  modalSaveButton: {
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: 'center',
  },
  modalSaveButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default HomeScreen;
