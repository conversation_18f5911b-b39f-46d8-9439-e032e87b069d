#!/usr/bin/env node

// Check package versions and compatibility
const fs = require('fs');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));

console.log('=== Package Version Analysis ===');
console.log('React:', packageJson.dependencies.react);
console.log('React Native:', packageJson.dependencies['react-native']);
console.log('Expo:', packageJson.dependencies.expo);
console.log('Expo SQLite:', packageJson.dependencies['expo-sqlite']);
console.log('React Navigation Native:', packageJson.dependencies['@react-navigation/native']);
console.log('Safe Area Context:', packageJson.dependencies['react-native-safe-area-context']);

console.log('\n=== Known Issues ===');
console.log('- React 19 is very new and might have compatibility issues');
console.log('- expo-sqlite@15.2.13 might not be fully compatible with React 19');
console.log('- Consider downgrading to React 18 for better compatibility');

console.log('\n=== Recommended Actions ===');
console.log('1. Try downgrading React to 18.x');
console.log('2. Update expo-sqlite to latest version');
console.log('3. Check if SQLiteProvider props are correct for current version');
