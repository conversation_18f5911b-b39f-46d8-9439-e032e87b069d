import React from 'react';
import { View, Text, TextInput, StyleSheet } from 'react-native';
import { Colors } from '../../constants/colors';

/**
 * Düzenli gelir ek bilgileri bileşeni
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {string} props.notes - Notlar
 * @param {Function} props.setNotes - Notlar değiştirme fonksiyonu
 * @returns {JSX.Element} Ek bilgiler bileşeni
 */
const RegularIncomeAdditionalInfo = React.memo(({ 
  notes, 
  setNotes, 
  getInputStyle = () => styles.input, 
  handleInputFocus = () => {}, 
  handleInputBlur = () => {} 
}) => {
  return (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Ek Bilgiler</Text>
      
      <Text style={styles.label}>Notlar</Text>
      <TextInput
        style={[
          getInputStyle ? getInputStyle('notes') : styles.input, 
          styles.multilineInput
        ]}
        value={notes}
        onChangeText={setNotes}
        placeholder="Bu gelirle ilgili ek notlar..."
        multiline
        textAlignVertical="top"
        accessibilityLabel="Notlar"
        accessibilityHint="Gelir ile ilgili ek notları buraya yazabilirsiniz"
        // Doğrudan fonksiyonu geçmek daha performanslı
        onFocus={handleInputFocus ? () => handleInputFocus('notes') : undefined}
        onBlur={handleInputBlur ? () => handleInputBlur('notes') : undefined}
      />
    </View>
  );
});

// Enhanced styles with Material Design 3.0 principles
const styles = StyleSheet.create({
  section: {
    marginBottom: 24,
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.GRAY_800,
    marginBottom: 16,
    letterSpacing: 0.5,
  },
  label: {
    fontSize: 16,
    color: Colors.GRAY_700,
    marginBottom: 8,
    fontWeight: '600',
    letterSpacing: 0.25,
  },
  input: {
    backgroundColor: '#fafafa',
    borderWidth: 1.5,
    borderColor: Colors.GRAY_300,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: Colors.GRAY_900,
    marginBottom: 16,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 4,
  },
  multilineInput: {
    height: 120,
    textAlignVertical: 'top',
    paddingTop: 16,
  },
});

// Follow ESM exports pattern
export default RegularIncomeAdditionalInfo;
