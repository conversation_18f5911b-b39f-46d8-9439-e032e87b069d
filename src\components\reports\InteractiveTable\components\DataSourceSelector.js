import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { useTheme } from '../../../../context/ThemeContext';

/**
 * <PERSON><PERSON> Kaynağı Seçici Bileşeni
 * Kullanıcının hangi veri kaynaklarını kullanacağını seçmesi
 * MAX 150 satır - Modularization Rule
 */
const DataSourceSelector = ({ 
  selectedSources = [], 
  onSourceChange,
  dataSources,
  dateRange,
  onDataSourcesChange,
  onDateRangeChange,
  onNext
}) => {
  const { theme } = useTheme();
  
  // Tema güvenlik kontrolü
  if (!theme) {
    return null;
  }

  // Mevcut veri kaynakları
  const defaultDataSources = [
    {
      id: 'transactions',
      name: '<PERSON><PERSON><PERSON><PERSON>',
      description: 'G<PERSON>r ve gider işlemleri',
      icon: '💰',
      count: '2,450 kayıt',
      selected: selectedSources.includes('transactions'),
    },
    {
      id: 'salary',
      name: '<PERSON><PERSON><PERSON>',
      description: '<PERSON>ylık maaş ödemeleri',
      icon: '💼',
      count: '12 kayıt',
      selected: selectedSources.includes('salary'),
    },
    {
      id: 'overtime',
      name: 'Mesai',
      description: 'Mesai ve fazla çalışma',
      icon: '⏰',
      count: '145 kayıt',
      selected: selectedSources.includes('overtime'),
    },
    {
      id: 'budgets',
      name: 'Bütçeler',
      description: 'Bütçe planları ve hedefler',
      icon: '🎯',
      count: '8 kayıt',
      selected: selectedSources.includes('budgets'),
    },
    {
      id: 'investments',
      name: 'Yatırımlar',
      description: 'Yatırım portföyü (Yakında)',
      icon: '📈',
      count: '0 kayıt',
      selected: false,
      disabled: true,
    },
  ];

  const activeDataSources = dataSources || defaultDataSources;

  /**
   * Veri kaynağı seçim/seçim kaldırma
   */
  const toggleSource = (sourceId) => {
    if (!onSourceChange) return;
    
    const updatedSources = selectedSources.includes(sourceId)
      ? selectedSources.filter(id => id !== sourceId)
      : [...selectedSources, sourceId];
    
    onSourceChange(updatedSources);
  };

  /**
   * Veri kaynağı kartı render
   */
  const renderSourceCard = (source) => (
    <TouchableOpacity
      key={source.id}
      style={[
        styles.sourceCard,
        { 
          backgroundColor: theme.SURFACE || theme.colors?.surface || '#f9f9f9',
          borderColor: source.selected ? (theme.PRIMARY || theme.colors?.primary || '#007AFF') : (theme.BORDER || theme.colors?.border || '#e0e0e0'),
        },
        source.disabled && styles.disabledCard,
      ]}
      onPress={() => !source.disabled && toggleSource(source.id)}
      disabled={source.disabled}
    >
      <View style={styles.cardHeader}>
        <Text style={styles.sourceIcon}>{source.icon}</Text>
        {source.selected && (
          <View style={[styles.checkmark, { backgroundColor: theme.PRIMARY || theme.colors?.primary || '#007AFF' }]}>
            <Text style={[styles.checkmarkText, { color: theme.SURFACE || theme.colors?.surface || '#f9f9f9' }]}>
              ✓
            </Text>
          </View>
        )}
      </View>
      
      <Text style={[
        styles.sourceName, 
        { 
          color: source.disabled ? (theme.TEXT_SECONDARY || theme.colors?.textSecondary || '#666') : (theme.TEXT_PRIMARY || theme.colors?.text || '#333') 
        }
      ]}>
        {source.name}
      </Text>
      
      <Text style={[styles.sourceDescription, { color: theme.TEXT_SECONDARY || theme.colors?.textSecondary || '#666' }]}>
        {source.description}
      </Text>
      
      <Text style={[styles.sourceCount, { color: theme.TEXT_SECONDARY || theme.colors?.textSecondary || '#666' }]}>
        {source.count}
      </Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.TEXT_PRIMARY || theme.colors?.text || '#333' }]}>
          🎯 Veri Kaynağı Seçimi
        </Text>
        <Text style={[styles.subtitle, { color: theme.TEXT_SECONDARY || theme.colors?.textSecondary || '#666' }]}>
          Tablonuzda kullanmak istediğiniz veri kaynaklarını seçin
        </Text>
      </View>

      <ScrollView style={styles.sourcesList} showsVerticalScrollIndicator={false}>
        <View style={styles.sourcesGrid}>
          {activeDataSources.map(renderSourceCard)}
        </View>
        
        <View style={styles.selectionSummary}>
          <Text style={[styles.summaryText, { color: theme.TEXT_SECONDARY || theme.colors?.textSecondary || '#666' }]}>
            {selectedSources.length} veri kaynağı seçildi
          </Text>
        </View>
        
        {onNext && (
          <TouchableOpacity
            style={[styles.nextButton, { backgroundColor: theme.PRIMARY || theme.colors?.primary || '#007AFF' }]}
            onPress={onNext}
          >
            <Text style={[styles.nextButtonText, { color: theme.SURFACE || theme.colors?.surface || '#f9f9f9' }]}>
              İleri: Sütun Yapılandırması
            </Text>
          </TouchableOpacity>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingVertical: 20,
  },
  header: {
    marginBottom: 24,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    lineHeight: 20,
  },
  sourcesList: {
    flex: 1,
  },
  sourcesGrid: {
    gap: 16,
  },
  sourceCard: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 2,
    position: 'relative',
  },
  disabledCard: {
    opacity: 0.6,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sourceIcon: {
    fontSize: 24,
  },
  checkmark: {
    width: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkmarkText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  sourceName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  sourceDescription: {
    fontSize: 14,
    marginBottom: 8,
  },
  sourceCount: {
    fontSize: 12,
    fontWeight: '500',
  },
  selectionSummary: {
    marginTop: 24,
    padding: 16,
    alignItems: 'center',
  },
  summaryText: {
    fontSize: 14,
    fontWeight: '500',
  },
  nextButton: {
    marginTop: 16,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  nextButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default DataSourceSelector;
