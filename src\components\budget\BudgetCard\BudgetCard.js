/**
 * Budget Card Component - Real Data Display
 * Modern card design with real database integration
 * 
 * Features:
 * - Real spending data display
 * - Animated progress indicators
 * - Multi-currency support
 * - Dark mode compatibility
 * - Interactive actions
 * - Turkish localization
 */

import React, { memo } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

/**
 * Individual Budget Card Component
 * @param {Object} budget - Budget data from database
 * @param {Function} onPress - Handle card press
 * @param {Function} onQuickExpense - Handle quick expense button
 * @param {Object} theme - Theme colors
 * @param {Object} rates - Exchange rates (optional)
 * @param {string} baseCurrency - Base currency (optional)
 */
const BudgetCard = memo(({ 
  budget, 
  onPress, 
  onQuickExpense, 
  theme, 
  rates = {}, 
  baseCurrency = 'TRY' 
}) => {
  /**
   * Gets progress color based on spending percentage
   */
  const getProgressColor = () => {
    const percentage = budget.spending_percentage || 0;
    
    if (percentage >= 100) return theme.DANGER;
    if (percentage >= 90) return theme.WARNING;
    if (percentage >= 75) return theme.WARNING_LIGHT;
    return theme.SUCCESS;
  };

  /**
   * Gets status icon based on budget state
   */
  const getStatusIcon = () => {
    if (budget.is_over_budget) return 'warning';
    if (budget.spending_percentage >= 90) return 'alert-circle';
    if (budget.spending_percentage >= 75) return 'information-circle';
    return 'checkmark-circle';
  };

  /**
   * Formats currency display
   */
  const formatCurrency = (amount, currency = 'TRY') => {
    const symbols = { TRY: '₺', USD: '$', EUR: '€' };
    const symbol = symbols[currency] || currency;
    
    return `${Math.abs(amount || 0).toLocaleString('tr-TR', { 
      minimumFractionDigits: 2, 
      maximumFractionDigits: 2 
    })} ${symbol}`;
  };

  /**
   * Gets days remaining text
   */
  const getDaysRemainingText = () => {
    if (!budget.days_remaining) return 'Süresiz';
    if (budget.days_remaining === 0) return 'Bugün bitiyor';
    if (budget.days_remaining === 1) return '1 gün kaldı';
    return `${budget.days_remaining} gün kaldı`;
  };

  /**
   * Gets period type display text
   */
  const getPeriodText = () => {
    const periodMap = {
      'monthly': 'Aylık',
      'weekly': 'Haftalık',
      'custom': 'Özel'
    };
    return periodMap[budget.period_type] || 'Aylık';
  };

  const progressColor = getProgressColor();
  const statusIcon = getStatusIcon();
  const totalLimit = budget.total_limit || 0;
  const totalSpent = budget.total_spent || 0;
  const remaining = totalLimit - totalSpent;
  const spendingPercentage = totalLimit > 0 ? Math.min((totalSpent / totalLimit) * 100, 100) : 0;

  return (
    <TouchableOpacity
      style={[
        styles.card,
        { 
          backgroundColor: theme.SURFACE,
          borderLeftColor: progressColor,
        }
      ]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      {/* Header Section */}
      <View style={styles.header}>
        <View style={styles.titleSection}>
          <Text style={[styles.budgetName, { color: theme.TEXT_PRIMARY }]}>
            {budget.name}
          </Text>
          {budget.description && (
            <Text style={[styles.budgetDescription, { color: theme.TEXT_SECONDARY }]}>
              {budget.description}
            </Text>
          )}
        </View>
        
        <View style={styles.statusSection}>
          <Ionicons 
            name={statusIcon} 
            size={24} 
            color={progressColor} 
          />
          <Text style={[styles.percentageText, { color: progressColor }]}>
            %{Math.round(spendingPercentage)}
          </Text>
        </View>
      </View>

      {/* Amount Section */}
      <View style={styles.amountSection}>
        <View style={styles.amountRow}>
          <View style={styles.amountItem}>
            <Text style={[styles.amountLabel, { color: theme.TEXT_SECONDARY }]}>
              Harcanan
            </Text>
            <Text style={[styles.amountValue, { color: theme.TEXT_PRIMARY }]}>
              {formatCurrency(totalSpent, budget.currency)}
            </Text>
          </View>
          
          <View style={styles.amountItem}>
            <Text style={[styles.amountLabel, { color: theme.TEXT_SECONDARY }]}>
              Toplam Bütçe
            </Text>
            <Text style={[styles.amountValue, { color: theme.TEXT_PRIMARY }]}>
              {formatCurrency(totalLimit, budget.currency)}
            </Text>
          </View>
        </View>

        <View style={styles.remainingSection}>
          <Text style={[styles.remainingLabel, { color: theme.TEXT_SECONDARY }]}>
            Kalan Miktar
          </Text>
          <Text 
            style={[
              styles.remainingValue, 
              { color: remaining >= 0 ? theme.SUCCESS : theme.DANGER }
            ]}
          >
            {remaining >= 0 ? '+' : ''}{formatCurrency(remaining, budget.currency)}
          </Text>
        </View>
      </View>

      {/* Progress Bar */}
      <View style={styles.progressSection}>
        <View style={[styles.progressTrack, { backgroundColor: theme.BORDER }]}>
          <View
            style={[
              styles.progressBar,
              {
                width: `${spendingPercentage}%`,
                backgroundColor: progressColor,
              }
            ]}
          />
        </View>
      </View>

      {/* Categories Summary */}
      {budget.categories && budget.categories.length > 0 && (
        <View style={styles.categoriesSection}>
          <Text style={[styles.categoriesTitle, { color: theme.TEXT_SECONDARY }]}>
            Kategoriler ({budget.category_count})
          </Text>
          <View style={styles.categoriesRow}>
            {budget.categories.slice(0, 3).map((category, index) => (
              <View key={category.id} style={styles.categoryChip}>
                <Text 
                  style={[
                    styles.categoryName, 
                    { color: category.usage_percentage > 100 ? theme.DANGER : theme.TEXT_SECONDARY }
                  ]}
                >
                  {category.category_name}
                </Text>
                <Text 
                  style={[
                    styles.categoryPercentage,
                    { color: category.usage_percentage > 100 ? theme.DANGER : theme.TEXT_PRIMARY }
                  ]}
                >
                  %{Math.round(category.usage_percentage || 0)}
                </Text>
              </View>
            ))}
            {budget.categories.length > 3 && (
              <Text style={[styles.moreCategories, { color: theme.TEXT_DISABLED }]}>
                +{budget.categories.length - 3} daha
              </Text>
            )}
          </View>
        </View>
      )}

      {/* Footer Section */}
      <View style={styles.footer}>
        <View style={styles.metaInfo}>
          <Text style={[styles.metaText, { color: theme.TEXT_DISABLED }]}>
            {getPeriodText()} • {getDaysRemainingText()}
          </Text>
          {budget.status !== 'active' && (
            <Text style={[styles.statusBadge, { color: theme.WARNING }]}>
              {budget.status === 'paused' ? 'Duraklatıldı' : 'Tamamlandı'}
            </Text>
          )}
        </View>
        
        {budget.status === 'active' && (
          <TouchableOpacity
            style={[styles.quickExpenseButton, { backgroundColor: theme.PRIMARY }]}
            onPress={(e) => {
              e.stopPropagation();
              onQuickExpense();
            }}
          >
            <Ionicons name="add" size={16} color={theme.ON_PRIMARY} />
            <Text style={[styles.quickExpenseText, { color: theme.ON_PRIMARY }]}>
              Hızlı Gider
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </TouchableOpacity>
  );
});

const styles = StyleSheet.create({
  card: {
    borderRadius: 12,
    padding: 16,
    marginVertical: 6,
    borderLeftWidth: 4,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  titleSection: {
    flex: 1,
    marginRight: 12,
  },
  budgetName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  budgetDescription: {
    fontSize: 14,
    lineHeight: 18,
  },
  statusSection: {
    alignItems: 'center',
    gap: 4,
  },
  percentageText: {
    fontSize: 14,
    fontWeight: '600',
  },
  amountSection: {
    marginBottom: 12,
  },
  amountRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  amountItem: {
    flex: 1,
  },
  amountLabel: {
    fontSize: 12,
    marginBottom: 2,
  },
  amountValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  remainingSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  remainingLabel: {
    fontSize: 14,
  },
  remainingValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  progressSection: {
    marginBottom: 12,
  },
  progressTrack: {
    height: 6,
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    borderRadius: 3,
  },
  categoriesSection: {
    marginBottom: 12,
  },
  categoriesTitle: {
    fontSize: 12,
    marginBottom: 6,
  },
  categoriesRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    alignItems: 'center',
  },
  categoryChip: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  categoryName: {
    fontSize: 12,
  },
  categoryPercentage: {
    fontSize: 12,
    fontWeight: '600',
  },
  moreCategories: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  metaInfo: {
    flex: 1,
  },
  metaText: {
    fontSize: 12,
    marginBottom: 2,
  },
  statusBadge: {
    fontSize: 12,
    fontWeight: '600',
  },
  quickExpenseButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    gap: 4,
  },
  quickExpenseText: {
    fontSize: 12,
    fontWeight: '600',
  },
});

export default BudgetCard;
