/**
 * Maaş servisi
 * Maaş işlemlerini yönetir
 */
import * as exchangeRateService from './exchangeRateService';

/**
 * Maaşları getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} options - So<PERSON><PERSON> seçenekleri
 * @param {boolean} options.activeOnly - Sadece aktif maaşları getir
 * @returns {Promise<Array>} Maaşlar
 */
export const getSalaries = async (db, options = {}) => {
  try {
    let query = `
      SELECT s.*,
             c.name as category_name,
             c.icon as category_icon,
             c.color as category_color
      FROM salaries s
      LEFT JOIN categories c ON s.category_id = c.id
      WHERE 1=1
    `;

    const params = [];

    if (options.activeOnly) {
      query += ' AND s.is_active = 1';
    }

    query += ' ORDER BY s.payment_day ASC, s.name';

    return await db.getAllAsync(query, params);
  } catch (error) {
    console.error('Maaşları getirme hatası:', error);
    throw error;
  }
};

/**
 * Maaş detaylarını getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} salaryId - Maaş ID'si
 * @returns {Promise<Object>} Maaş detayları
 */
export const getSalaryDetails = async (db, salaryId) => {
  try {
    // Maaş bilgilerini getir
    const salary = await db.getFirstAsync(`
      SELECT s.*,
             c.name as category_name,
             c.icon as category_icon,
             c.color as category_color
      FROM salaries s
      LEFT JOIN categories c ON s.category_id = c.id
      WHERE s.id = ?
    `, [salaryId]);

    if (!salary) {
      throw new Error('Maaş bulunamadı');
    }

    // Maaş ödemelerini getir
    const payments = await db.getAllAsync(`
      SELECT sp.*, t.id as transaction_exists
      FROM salary_payments sp
      LEFT JOIN transactions t ON sp.transaction_id = t.id
      WHERE sp.salary_id = ?
      ORDER BY sp.payment_date DESC
    `, [salaryId]);

    return {
      ...salary,
      payments
    };
  } catch (error) {
    console.error('Maaş detayları getirme hatası:', error);
    throw error;
  }
};

/**
 * Yeni maaş oluşturur
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} salary - Maaş verileri
 * @returns {Promise<number>} Oluşturulan maaş ID'si
 */
export const createSalary = async (db, salary) => {
  try {
    let salaryId = 0;

    // fixDatabase çağrısı kaldırıldı - migration'lar bu işi yapıyor

    try {
      await db.execAsync('BEGIN TRANSACTION');

      // Maaşı ekle
      const result = await db.runAsync(`
        INSERT INTO salaries (
          name, amount, currency, payment_day, is_active,
          category_id, tax_rate, notes
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        salary.name,
        salary.amount,
        salary.currency || 'TRY',
        salary.payment_day,
        salary.is_active !== undefined ? salary.is_active : 1,
        salary.category_id || null,
        salary.tax_rate || 0,
        salary.notes || null
      ]);

      salaryId = result.lastInsertRowId;

      // Döviz karşılıklarını hesapla
      const equivalents = await exchangeRateService.calculateCurrencyEquivalents(
        db,
        salary.amount,
        salary.currency || 'TRY',
        null,
        salary.custom_currency
      );

      // Döviz karşılıklarını doğrudan güncelle
      try {
        await db.runAsync(`
          UPDATE salaries
          SET usd_equivalent = ?,
              eur_equivalent = ?,
              custom_currency = ?,
              custom_equivalent = ?
          WHERE id = ?
        `, [
          equivalents.USD,
          equivalents.EUR,
          equivalents.custom.currency,
          equivalents.custom.amount,
          salaryId
        ]);
      } catch (updateError) {
        console.error('Döviz karşılıkları güncelleme hatası:', updateError);
        // Hata olsa bile devam et
      }

      await db.execAsync('COMMIT');

      // Gelecek ödemeleri oluştur
      if (salary.create_payments) {
        const today = new Date();
        const currentMonth = today.getMonth();
        const currentYear = today.getFullYear();

        // Sadece 1 ay için ödeme oluştur
        const paymentMonth = currentMonth;
        const paymentYear = currentYear;

        // Ödeme günü ayın son gününden büyükse, ayın son gününü kullan
        const lastDayOfMonth = new Date(paymentYear, paymentMonth + 1, 0).getDate();
        const paymentDay = Math.min(salary.payment_day, lastDayOfMonth);


        const paymentDate = new Date(paymentYear, paymentMonth, paymentDay);

        // Ödeme tarihini YYYY-MM-DD formatına dönüştür
        const formattedDate = paymentDate.toISOString().split('T')[0];

        // Ödeme oluştur
        try {
          const paymentResult = await db.runAsync(`
            INSERT INTO salary_payments (
              salary_id, amount, currency, payment_date, is_paid
            )
            VALUES (?, ?, ?, ?, ?)
          `, [
            salaryId,
            salary.amount,
            salary.currency || 'TRY',
            formattedDate,
            0 // Ödenmedi
          ]);

          // Döviz karşılıklarını hesapla
          const paymentEquivalents = await exchangeRateService.calculateCurrencyEquivalents(
            db,
            salary.amount,
            salary.currency || 'TRY',
            formattedDate,
            salary.custom_currency
          );

          // Döviz karşılıklarını doğrudan güncelle
          try {
            await db.runAsync(`
              UPDATE salary_payments
              SET usd_equivalent = ?,
                  eur_equivalent = ?,
                  custom_currency = ?,
                  custom_equivalent = ?
              WHERE id = ?
            `, [
              paymentEquivalents.USD,
              paymentEquivalents.EUR,
              paymentEquivalents.custom.currency,
              paymentEquivalents.custom.amount,
              paymentResult.lastInsertRowId
            ]);
          } catch (updateError) {
            console.error('Ödeme döviz karşılıkları güncelleme hatası:', updateError);
            // Hata olsa bile devam et
          }
        } catch (paymentError) {
          console.error('Ödeme oluşturma hatası:', paymentError);
        }
      }

      return salaryId;
    } catch (transactionError) {
      console.error('Maaş ekleme transaction hatası:', transactionError);
      try {
        await db.execAsync('ROLLBACK');
      } catch (rollbackError) {
        console.error('Rollback hatası:', rollbackError);
      }
      throw transactionError;
    }
  } catch (error) {
    console.error('Maaş ekleme hatası:', error);
    throw error;
  }
};

/**
 * Maaşı günceller
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} salaryId - Maaş ID'si
 * @param {Object} salary - Güncellenecek maaş verileri
 * @returns {Promise<void>}
 */
export const updateSalary = async (db, salaryId, salary) => {
  try {
    await db.withTransactionAsync(async () => {
      // Maaşı güncelle
      await db.runAsync(`
        UPDATE salaries
        SET name = ?, amount = ?, currency = ?, payment_day = ?, is_active = ?,
            category_id = ?, tax_rate = ?, notes = ?,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `, [
        salary.name,
        salary.amount,
        salary.currency || 'TRY',
        salary.payment_day,
        salary.is_active !== undefined ? salary.is_active : 1,
        salary.category_id || null,
        salary.tax_rate || 0,
        salary.notes || null,
        salaryId
      ]);

      // Döviz karşılıklarını güncelle
      await exchangeRateService.saveCurrencyEquivalents(
        db,
        'salaries',
        salaryId,
        salary.amount,
        salary.currency || 'TRY',
        null,
        salary.custom_currency
      );

      // Gelecek ödemeleri güncelle (henüz ödenmemiş olanlar)
      if (salary.update_future_payments) {
        await db.runAsync(`
          UPDATE salary_payments
          SET amount = ?, currency = ?, updated_at = CURRENT_TIMESTAMP
          WHERE salary_id = ? AND is_paid = 0
        `, [
          salary.amount,
          salary.currency || 'TRY',
          salaryId
        ]);
      }
    });
  } catch (error) {
    console.error('Maaş güncelleme hatası:', error);
    throw error;
  }
};

/**
 * Maaşı siler
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} salaryId - Maaş ID'si
 * @returns {Promise<void>}
 */
export const deleteSalary = async (db, salaryId) => {
  try {
    await db.withTransactionAsync(async () => {
      // Maaş ödemelerini sil
      await db.runAsync(`
        DELETE FROM salary_payments
        WHERE salary_id = ?
      `, [salaryId]);

      // Maaşı sil
      await db.runAsync(`
        DELETE FROM salaries
        WHERE id = ?
      `, [salaryId]);
    });
  } catch (error) {
    console.error('Maaş silme hatası:', error);
    throw error;
  }
};

/**
 * Maaş ödemesi oluşturur
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} payment - Ödeme verileri
 * @returns {Promise<number>} Oluşturulan ödeme ID'si
 */
export const createSalaryPayment = async (db, payment) => {
  try {
    const result = await db.runAsync(`
      INSERT INTO salary_payments (
        salary_id, amount, currency, payment_date, is_paid, notes
      )
      VALUES (?, ?, ?, ?, ?, ?)
    `, [
      payment.salary_id,
      payment.amount,
      payment.currency || 'TRY',
      payment.payment_date,
      payment.is_paid !== undefined ? payment.is_paid : 0,
      payment.notes || null
    ]);

    const paymentId = result.lastInsertRowId;

    // Ödeme tarihindeki döviz kurunu kullanarak döviz karşılıklarını hesapla ve kaydet
    const paymentEquivalents = await exchangeRateService.calculateCurrencyEquivalents(
      db,
      payment.amount,
      payment.currency || 'TRY',
      payment.payment_date, // Ödeme tarihini kullan
      payment.custom_currency
    );

    // Döviz karşılıklarını doğrudan güncelle
    await db.runAsync(`
      UPDATE salary_payments
      SET usd_equivalent = ?,
          eur_equivalent = ?,
          custom_currency = ?,
          custom_equivalent = ?
      WHERE id = ?
    `, [
      paymentEquivalents.USD,
      paymentEquivalents.EUR,
      paymentEquivalents.custom.currency,
      paymentEquivalents.custom.amount,
      paymentId
    ]);

    return paymentId;
  } catch (error) {
    console.error('Maaş ödemesi ekleme hatası:', error);
    throw error;
  }
};

/**
 * Maaş ödemesini günceller
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} paymentId - Ödeme ID'si
 * @param {Object} payment - Güncellenecek ödeme verileri
 * @returns {Promise<void>}
 */
export const updateSalaryPayment = async (db, paymentId, payment) => {
  try {
    await db.runAsync(`
      UPDATE salary_payments
      SET amount = ?, currency = ?, payment_date = ?, is_paid = ?, notes = ?,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [
      payment.amount,
      payment.currency || 'TRY',
      payment.payment_date,
      payment.is_paid !== undefined ? payment.is_paid : 0,
      payment.notes || null,
      paymentId
    ]);

    // Ödeme tarihindeki döviz kurunu kullanarak döviz karşılıklarını hesapla ve kaydet
    const paymentEquivalents = await exchangeRateService.calculateCurrencyEquivalents(
      db,
      payment.amount,
      payment.currency || 'TRY',
      payment.payment_date, // Ödeme tarihini kullan
      payment.custom_currency
    );

    // Döviz karşılıklarını doğrudan güncelle
    await db.runAsync(`
      UPDATE salary_payments
      SET usd_equivalent = ?,
          eur_equivalent = ?,
          custom_currency = ?,
          custom_equivalent = ?
      WHERE id = ?
    `, [
      paymentEquivalents.USD,
      paymentEquivalents.EUR,
      paymentEquivalents.custom.currency,
      paymentEquivalents.custom.amount,
      paymentId
    ]);
  } catch (error) {
    console.error('Maaş ödemesi güncelleme hatası:', error);
    throw error;
  }
};

/**
 * Maaş ödemesini siler
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} paymentId - Ödeme ID'si
 * @returns {Promise<void>}
 */
export const deleteSalaryPayment = async (db, paymentId) => {
  try {
    await db.runAsync(`
      DELETE FROM salary_payments
      WHERE id = ?
    `, [paymentId]);
  } catch (error) {
    console.error('Maaş ödemesi silme hatası:', error);
    throw error;
  }
};

/**
 * Maaş ödemesini işleme alır (gelir olarak kaydeder)
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} paymentId - Ödeme ID'si
 * @param {Object} options - İşlem seçenekleri
 * @returns {Promise<number>} Oluşturulan işlem ID'si
 */
export const processSalaryPayment = async (db, paymentId, options = {}) => {
  try {
    return await db.withTransactionAsync(async () => {
      // Ödeme bilgilerini getir
      const payment = await db.getFirstAsync(`
        SELECT sp.*, s.name as salary_name, s.category_id
        FROM salary_payments sp
        JOIN salaries s ON sp.salary_id = s.id
        WHERE sp.id = ?
      `, [paymentId]);

      if (!payment) {
        throw new Error('Ödeme bulunamadı');
      }

      // İşlem oluştur
      const result = await db.runAsync(`
        INSERT INTO transactions (
          type, amount, currency, date, category_id,
          description, notes, metadata
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        'income',
        payment.amount,
        payment.currency,
        payment.payment_date,
        payment.category_id || options.category_id || null,
        options.description || `${payment.salary_name} maaş ödemesi`,
        payment.notes || null,
        JSON.stringify({ salary_payment_id: payment.id })
      ]);

      const transactionId = result.lastInsertRowId;

      // Döviz karşılıklarını kaydet
      await exchangeRateService.saveCurrencyEquivalents(
        db,
        'transactions',
        transactionId,
        payment.amount,
        payment.currency,
        payment.payment_date
      );

      // Ödemeyi güncelle
      await db.runAsync(`
        UPDATE salary_payments
        SET is_paid = 1, transaction_id = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `, [transactionId, paymentId]);

      return transactionId;
    });
  } catch (error) {
    console.error('Maaş ödemesi işleme hatası:', error);
    throw error;
  }
};

/**
 * Yaklaşan maaş ödemelerini getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} days - Kaç gün içindeki ödemeleri getir
 * @returns {Promise<Array>} Yaklaşan ödemeler
 */
export const getUpcomingSalaryPayments = async (db, days = 30) => {
  try {
    const today = new Date().toISOString().split('T')[0];
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + days);
    const futureDateStr = futureDate.toISOString().split('T')[0];

    return await db.getAllAsync(`
      SELECT sp.*,
             s.name as salary_name,
             s.category_id,
             c.name as category_name,
             c.icon as category_icon,
             c.color as category_color
      FROM salary_payments sp
      JOIN salaries s ON sp.salary_id = s.id
      LEFT JOIN categories c ON s.category_id = c.id
      WHERE sp.payment_date >= ? AND sp.payment_date <= ? AND sp.is_paid = 0
      ORDER BY sp.payment_date ASC
    `, [today, futureDateStr]);
  } catch (error) {
    console.error('Yaklaşan maaş ödemeleri getirme hatası:', error);
    throw error;
  }
};

/**
 * Otomatik olarak gelecek maaş ödemelerini oluşturur
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} months - Kaç ay için ödeme oluşturulacak
 * @returns {Promise<number>} Oluşturulan ödeme sayısı
 */
export const generateFutureSalaryPayments = async (db, months = 3) => {
  try {
    return await db.withTransactionAsync(async () => {
      // Aktif maaşları getir
      const activeSalaries = await db.getAllAsync(`
        SELECT * FROM salaries WHERE is_active = 1
      `);

      if (activeSalaries.length === 0) {
        return 0;
      }

      let createdCount = 0;

      // Her maaş için gelecek ödemeleri kontrol et
      for (const salary of activeSalaries) {
        // Son ödeme tarihini bul
        const lastPayment = await db.getFirstAsync(`
          SELECT payment_date FROM salary_payments
          WHERE salary_id = ?
          ORDER BY payment_date DESC
          LIMIT 1
        `, [salary.id]);

        let lastPaymentDate = lastPayment ? new Date(lastPayment.payment_date) : new Date();

        // Belirtilen ay sayısı kadar ödeme oluştur
        for (let i = 0; i < months; i++) {
          // Bir sonraki ödeme ayını hesapla
          const nextPaymentMonth = new Date(lastPaymentDate);
          nextPaymentMonth.setMonth(nextPaymentMonth.getMonth() + 1);

          // Ödeme gününü ayarla
          const paymentYear = nextPaymentMonth.getFullYear();
          const paymentMonth = nextPaymentMonth.getMonth();

          // Ödeme günü ayın son gününden büyükse, ayın son gününü kullan
          const lastDayOfMonth = new Date(paymentYear, paymentMonth + 1, 0).getDate();
          const paymentDay = Math.min(salary.payment_day, lastDayOfMonth);

          const paymentDate = new Date(paymentYear, paymentMonth, paymentDay);

          // Ödeme tarihini YYYY-MM-DD formatına dönüştür
          const formattedDate = paymentDate.toISOString().split('T')[0];

          // Bu tarihte zaten ödeme var mı kontrol et
          const existingPayment = await db.getFirstAsync(`
            SELECT id FROM salary_payments
            WHERE salary_id = ? AND payment_date = ?
          `, [salary.id, formattedDate]);

          if (!existingPayment) {
            // Ödeme oluştur
            const paymentResult = await db.runAsync(`
              INSERT INTO salary_payments (
                salary_id, amount, currency, payment_date, is_paid
              )
              VALUES (?, ?, ?, ?, ?)
            `, [
              salary.id,
              salary.amount,
              salary.currency,
              formattedDate,
              0 // Ödenmedi
            ]);

            // Ödeme tarihindeki döviz kurunu kullanarak döviz karşılıklarını hesapla ve kaydet
            const paymentEquivalents = await exchangeRateService.calculateCurrencyEquivalents(
              db,
              salary.amount,
              salary.currency,
              formattedDate, // Ödeme tarihini kullan
              salary.custom_currency
            );

            // Döviz karşılıklarını doğrudan güncelle
            await db.runAsync(`
              UPDATE salary_payments
              SET usd_equivalent = ?,
                  eur_equivalent = ?,
                  custom_currency = ?,
                  custom_equivalent = ?
              WHERE id = ?
            `, [
              paymentEquivalents.USD,
              paymentEquivalents.EUR,
              paymentEquivalents.custom.currency,
              paymentEquivalents.custom.amount,
              paymentResult.lastInsertRowId
            ]);

            createdCount++;
          }

          // Son ödeme tarihini güncelle
          lastPaymentDate = paymentDate;
        }
      }

      return createdCount;
    });
  } catch (error) {
    console.error('Gelecek maaş ödemeleri oluşturma hatası:', error);
    throw error;
  }
};
