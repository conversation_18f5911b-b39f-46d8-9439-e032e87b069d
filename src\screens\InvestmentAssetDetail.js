import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
  Alert,
  FlatList
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import { useExchangeRate } from '../contexts/ExchangeRateContext';
import * as investmentService from '../services/investmentService';
import { formatCurrency } from '../utils/formatters';
import InvestmentTransactionForm from './InvestmentTransactionForm';
import InvestmentAssetForm from './InvestmentAssetForm';

/**
 * Yatırım varlığı detay ekranı
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @param {Object} props.route - Route nesnesi
 * @param {Object} props.route.params - Route parametreleri
 * @param {number} props.route.params.assetId - Varlık ID'si
 * @returns {JSX.Element} Yatırım varlığı detay ekranı
 */
export default function InvestmentAssetDetail({ navigation, route }) {
  const { assetId } = route.params;
  const db = useSQLiteContext();
  const insets = useSafeAreaInsets();
  const exchangeRateContext = useExchangeRate();
  
  // Durum değişkenleri
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [asset, setAsset] = useState(null);
  const [portfolio, setPortfolio] = useState(null);
  const [transactions, setTransactions] = useState([]);
  const [showTransactionForm, setShowTransactionForm] = useState(false);
  const [showAssetForm, setShowAssetForm] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState(null);
  
  // Verileri yükle
  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      
      // Varlık bilgilerini getir
      const assetData = await db.getFirstAsync(`
        SELECT * FROM investment_assets
        WHERE id = ?
      `, [assetId]);
      
      if (!assetData) {
        Alert.alert('Hata', 'Varlık bulunamadı.');
        navigation.goBack();
        return;
      }
      
      setAsset(assetData);
      
      // Portföy bilgilerini getir
      const portfolioData = await db.getFirstAsync(`
        SELECT * FROM investment_portfolio
        WHERE asset_id = ?
      `, [assetId]);
      
      setPortfolio(portfolioData);
      
      // İşlemleri getir
      const transactionsData = await investmentService.getInvestmentTransactions({
        asset_id: assetId
      });
      
      setTransactions(transactionsData);
      
      setLoading(false);
    } catch (error) {
      console.error('Varlık detayları yükleme hatası:', error);
      Alert.alert('Hata', 'Varlık detayları yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  }, [db, assetId]);
  
  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [loadData])
  );
  
  // Yenile
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  }, [loadData]);
  
  // Yeni işlem ekle
  const addNewTransaction = () => {
    setSelectedTransaction(null);
    setShowTransactionForm(true);
  };
  
  // İşlem düzenle
  const editTransaction = (transaction) => {
    setSelectedTransaction(transaction);
    setShowTransactionForm(true);
  };
  
  // İşlem sil
  const deleteTransaction = async (transactionId) => {
    Alert.alert(
      'İşlemi Sil',
      'Bu işlemi silmek istediğinize emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await investmentService.deleteInvestmentTransaction(transactionId);
              await loadData();
              Alert.alert('Başarılı', 'İşlem başarıyla silindi.');
            } catch (error) {
              console.error('İşlem silme hatası:', error);
              Alert.alert('Hata', 'İşlem silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };
  
  // Varlığı düzenle
  const editAsset = () => {
    setShowAssetForm(true);
  };
  
  // Varlığı sil
  const deleteAsset = async () => {
    Alert.alert(
      'Varlığı Sil',
      'Bu varlığı silmek istediğinize emin misiniz? Bu işlem geri alınamaz.',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await investmentService.deleteInvestmentAsset(assetId);
              navigation.goBack();
            } catch (error) {
              console.error('Varlık silme hatası:', error);
              Alert.alert('Hata', error.message || 'Varlık silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };
  
  // İşlem tipini formatla
  const formatTransactionType = (type) => {
    switch (type) {
      case 'buy': return 'Alım';
      case 'sell': return 'Satım';
      case 'dividend': return 'Temettü';
      case 'interest': return 'Faiz';
      case 'fee': return 'Komisyon/Ücret';
      case 'transfer': return 'Transfer';
      case 'other': return 'Diğer';
      default: return type;
    }
  };
  
  // Varlık tipini formatla
  const formatAssetType = (type) => {
    switch (type) {
      case 'stock': return 'Hisse Senedi';
      case 'crypto': return 'Kripto Para';
      case 'gold': return 'Altın';
      case 'silver': return 'Gümüş';
      case 'forex': return 'Döviz';
      case 'bond': return 'Tahvil/Bono';
      case 'fund': return 'Fon';
      case 'other': return 'Diğer';
      default: return type;
    }
  };
  
  // Kar/zarar durumuna göre renk
  const getProfitLossColor = (value) => {
    if (value > 0) return Colors.income.main;
    if (value < 0) return Colors.expense.main;
    return Colors.GRAY_600;
  };
  
  // İşlem tipine göre renk
  const getTransactionTypeColor = (type) => {
    switch (type) {
      case 'buy': return Colors.expense.main;
      case 'sell': return Colors.income.main;
      case 'dividend': return Colors.income.main;
      case 'interest': return Colors.income.main;
      case 'fee': return Colors.expense.main;
      case 'transfer': return Colors.PRIMARY;
      case 'other': return Colors.GRAY_600;
      default: return Colors.GRAY_600;
    }
  };
  
  // İşlem tipine göre ikon
  const getTransactionTypeIcon = (type) => {
    switch (type) {
      case 'buy': return 'arrow-downward';
      case 'sell': return 'arrow-upward';
      case 'dividend': return 'payments';
      case 'interest': return 'payments';
      case 'fee': return 'money-off';
      case 'transfer': return 'swap-horiz';
      case 'other': return 'more-horiz';
      default: return 'more-horiz';
    }
  };
  
  // İşlem öğesi
  const renderTransactionItem = ({ item }) => {
    return (
      <TouchableOpacity
        style={styles.transactionItem}
        onPress={() => editTransaction(item)}
      >
        <View style={styles.transactionItemLeft}>
          <View style={[
            styles.transactionTypeIcon,
            { backgroundColor: getTransactionTypeColor(item.type) }
          ]}>
            <MaterialIcons 
              name={getTransactionTypeIcon(item.type)} 
              size={16} 
              color="#fff" 
            />
          </View>
          <View style={styles.transactionItemDetails}>
            <Text style={styles.transactionItemType}>
              {formatTransactionType(item.type)}
            </Text>
            <Text style={styles.transactionItemDate}>
              {new Date(item.date).toLocaleDateString('tr-TR')}
            </Text>
          </View>
        </View>
        <View style={styles.transactionItemRight}>
          <Text style={styles.transactionItemAmount}>
            {item.type === 'buy' ? '-' : '+'}{formatCurrency(item.total_amount, item.currency || 'TRY')}
          </Text>
          <Text style={styles.transactionItemQuantity}>
            {item.type === 'buy' ? '+' : '-'}{item.quantity.toFixed(asset.type === 'crypto' ? 8 : 2)} {asset.symbol}
          </Text>
        </View>
        <TouchableOpacity
          style={styles.transactionItemDelete}
          onPress={() => deleteTransaction(item.id)}
        >
          <MaterialIcons name="delete" size={20} color={Colors.expense.main} />
        </TouchableOpacity>
      </TouchableOpacity>
    );
  };
  
  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {asset?.name || 'Varlık Detayı'}
        </Text>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={editAsset}
          >
            <MaterialIcons name="edit" size={24} color={Colors.PRIMARY} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={deleteAsset}
          >
            <MaterialIcons name="delete" size={24} color={Colors.expense.main} />
          </TouchableOpacity>
        </View>
      </View>
      
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.PRIMARY} />
          <Text style={styles.loadingText}>Yükleniyor...</Text>
        </View>
      ) : (
        <ScrollView
          style={styles.scrollView}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[Colors.PRIMARY]}
              tintColor={Colors.PRIMARY}
            />
          }
        >
          {/* Varlık Bilgileri */}
          <View style={styles.assetInfoCard}>
            <View style={styles.assetHeader}>
              <View style={styles.assetIconContainer}>
                <MaterialIcons 
                  name={asset.icon || 'category'} 
                  size={32} 
                  color={asset.color || Colors.GRAY_700} 
                />
              </View>
              <View style={styles.assetInfo}>
                <Text style={styles.assetName}>{asset.name}</Text>
                <Text style={styles.assetSymbol}>{asset.symbol}</Text>
              </View>
              <View style={styles.assetType}>
                <Text style={styles.assetTypeText}>
                  {formatAssetType(asset.type)}
                </Text>
              </View>
            </View>
            
            {asset.description && (
              <Text style={styles.assetDescription}>{asset.description}</Text>
            )}
          </View>
          
          {/* Portföy Özeti */}
          <View style={styles.portfolioCard}>
            <Text style={styles.cardTitle}>Portföy Özeti</Text>
            
            {portfolio ? (
              <>
                <View style={styles.portfolioRow}>
                  <Text style={styles.portfolioLabel}>Miktar</Text>
                  <Text style={styles.portfolioValue}>
                    {portfolio.quantity.toFixed(asset.type === 'crypto' ? 8 : 2)} {asset.symbol}
                  </Text>
                </View>
                
                <View style={styles.portfolioRow}>
                  <Text style={styles.portfolioLabel}>Ortalama Alış</Text>
                  <Text style={styles.portfolioValue}>
                    {formatCurrency(portfolio.average_buy_price, 'TRY')}
                  </Text>
                </View>
                
                <View style={styles.portfolioRow}>
                  <Text style={styles.portfolioLabel}>Güncel Fiyat</Text>
                  <Text style={styles.portfolioValue}>
                    {formatCurrency(portfolio.current_price, 'TRY')}
                  </Text>
                </View>
                
                <View style={styles.portfolioRow}>
                  <Text style={styles.portfolioLabel}>Güncel Değer</Text>
                  <Text style={styles.portfolioValue}>
                    {formatCurrency(portfolio.current_value, 'TRY')}
                  </Text>
                </View>
                
                <View style={styles.portfolioRow}>
                  <Text style={styles.portfolioLabel}>Kar/Zarar</Text>
                  <Text style={[
                    styles.portfolioValue,
                    { color: getProfitLossColor(portfolio.profit_loss) }
                  ]}>
                    {formatCurrency(portfolio.profit_loss, 'TRY')}
                    {' '}
                    ({portfolio.profit_loss_percentage > 0 ? '+' : ''}
                    {portfolio.profit_loss_percentage.toFixed(2)}%)
                  </Text>
                </View>
              </>
            ) : (
              <Text style={styles.emptyText}>Henüz portföy bilgisi bulunmuyor.</Text>
            )}
          </View>
          
          {/* İşlemler */}
          <View style={styles.transactionsCard}>
            <View style={styles.transactionsHeader}>
              <Text style={styles.cardTitle}>İşlemler</Text>
              <TouchableOpacity
                style={styles.addButton}
                onPress={addNewTransaction}
              >
                <MaterialIcons name="add" size={24} color={Colors.PRIMARY} />
              </TouchableOpacity>
            </View>
            
            {transactions.length === 0 ? (
              <View style={styles.emptyState}>
                <MaterialIcons name="receipt-long" size={48} color={Colors.GRAY_400} />
                <Text style={styles.emptyStateText}>Henüz işlem bulunmuyor.</Text>
                <TouchableOpacity
                  style={styles.emptyStateButton}
                  onPress={addNewTransaction}
                >
                  <Text style={styles.emptyStateButtonText}>İşlem Ekle</Text>
                </TouchableOpacity>
              </View>
            ) : (
              <FlatList
                data={transactions}
                renderItem={renderTransactionItem}
                keyExtractor={(item) => item.id.toString()}
                scrollEnabled={false}
              />
            )}
          </View>
        </ScrollView>
      )}
      
      {/* Yeni İşlem Butonu */}
      <TouchableOpacity
        style={styles.fab}
        onPress={addNewTransaction}
      >
        <MaterialIcons name="add" size={24} color="#fff" />
      </TouchableOpacity>
      
      {/* İşlem Formu */}
      <InvestmentTransactionForm
        visible={showTransactionForm}
        onClose={() => setShowTransactionForm(false)}
        onSave={loadData}
        transaction={selectedTransaction}
        assetId={assetId}
      />
      
      {/* Varlık Formu */}
      <InvestmentAssetForm
        visible={showAssetForm}
        onClose={() => setShowAssetForm(false)}
        onSave={loadData}
        asset={asset}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
    textAlign: 'center',
  },
  headerActions: {
    flexDirection: 'row',
  },
  headerButton: {
    padding: 4,
    marginLeft: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  scrollView: {
    flex: 1,
  },
  assetInfoCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    margin: 16,
    marginBottom: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  assetHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  assetIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  assetInfo: {
    flex: 1,
  },
  assetName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  assetSymbol: {
    fontSize: 14,
    color: '#666',
  },
  assetType: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  assetTypeText: {
    fontSize: 12,
    color: '#666',
  },
  assetDescription: {
    marginTop: 12,
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  portfolioCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    margin: 16,
    marginTop: 8,
    marginBottom: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  portfolioRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  portfolioLabel: {
    fontSize: 16,
    color: '#666',
  },
  portfolioValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  transactionsCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    margin: 16,
    marginTop: 8,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  transactionsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  addButton: {
    padding: 4,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#666',
    marginTop: 12,
    marginBottom: 16,
    textAlign: 'center',
  },
  emptyStateButton: {
    backgroundColor: Colors.PRIMARY,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  emptyStateButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    padding: 16,
  },
  transactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  transactionItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  transactionTypeIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  transactionItemDetails: {
    flex: 1,
  },
  transactionItemType: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  transactionItemDate: {
    fontSize: 14,
    color: '#666',
  },
  transactionItemRight: {
    alignItems: 'flex-end',
  },
  transactionItemAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  transactionItemQuantity: {
    fontSize: 14,
    color: '#666',
  },
  transactionItemDelete: {
    marginLeft: 12,
    padding: 4,
  },
  fab: {
    position: 'absolute',
    bottom: 16,
    right: 16,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
});
