/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> renk ve tema sabitleri
 */

export const COLORS = {
  common: {
    white: '#FFFFFF',
    black: '#000000',
    transparent: 'transparent'
  },
  primary: {
    main: '#3498db',
    light: '#5dade2',
    dark: '#2980b9'
  },
  neutral: {
    50: '#f8f9fa',
    100: '#f1f3f5',
    200: '#e9ecef',
    300: '#dee2e6',
    400: '#ced4da',
    500: '#adb5bd',
    600: '#868e96',
    700: '#495057',
    800: '#343a40',
    900: '#212529'
  },
  income: {
    main: '#2ecc71',
    light: '#78e08f',
    dark: '#27ae60'
  },
  expense: {
    main: '#e74c3c',
    light: '#ff7979',
    dark: '#c0392b'
  }
};

// Diğer tema sabitleri...

export const TYPOGRAPHY = {
  fontFamily: {
    primary: 'System',
    secondary: 'System'
  },
  fontSize: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 24,
    '2xl': 32
  },
  fontWeight: {
    light: '300',
    regular: '400',
    medium: '500',
    semiBold: '600',
    bold: '700'
  }
};

export const LAYOUT = {
  radius: {
    xs: 4,
    sm: 6,
    md: 8,
    lg: 12,
    xl: 16,
    '2xl': 24,
    circle: 9999
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    '2xl': 48,
    '3xl': 64
  },
  shadow: {
    xs: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 1
    },
    sm: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.15,
      shadowRadius: 3,
      elevation: 2
    },
    md: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 3 },
      shadowOpacity: 0.2,
      shadowRadius: 4,
      elevation: 3
    }
  }
};
