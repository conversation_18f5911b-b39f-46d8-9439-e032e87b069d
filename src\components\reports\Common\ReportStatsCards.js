import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

/**
 * Rapor İstatistik Kartları
 * Ana sayfa üzerindeki istatistik kartları
 */
const ReportStatsCards = ({ stats = {}, theme }) => {
  // Güvenlik kontrolü - eğer theme yoksa varsayılan renkleri kullan
  if (!theme) {
    return null;
  }

  // Default değerlerle güvenlik kontrolü
  const safeStats = {
    totalReports: 0,
    totalExports: 0,
    favoritesCount: 0,
    scheduledCount: 0,
    ...stats
  };

  const statsData = [
    {
      id: 'total',
      title: 'Toplam Rapor',
      value: safeStats.totalReports,
      icon: '📊',
      color: theme.PRIMARY,
    },
    {
      id: 'exports',
      title: 'Dışa Aktarma',
      value: safeStats.totalExports,
      icon: '📤',
      color: theme.SUCCESS,
    },
    {
      id: 'favorites',
      title: 'Favoriler',
      value: safeStats.favoritesCount,
      icon: '⭐',
      color: theme.WARNING,
    },
    {
      id: 'scheduled',
      title: 'Otomatik',
      value: safeStats.scheduledCount,
      icon: '⏰',
      color: theme.INFO,
    },
  ];

  return (
    <View style={styles.statsContainer}>
      {statsData.map((stat) => (
        <View 
          key={stat.id}
          style={[
            styles.statCard,
            { backgroundColor: theme.SURFACE || theme.colors?.surface || '#f9f9f9' }
          ]}
        >
          <Text style={styles.statIcon}>{stat.icon}</Text>
          <Text style={[styles.statValue, { color: stat.color }]}>
            {stat.value}
          </Text>
          <Text style={[styles.statTitle, { color: theme.TEXT_SECONDARY }]}>
            {stat.title}
          </Text>
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    justifyContent: 'space-between',
  },
  statCard: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 8,
    marginHorizontal: 4,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  statIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statTitle: {
    fontSize: 12,
    textAlign: 'center',
  },
});

export default ReportStatsCards;
