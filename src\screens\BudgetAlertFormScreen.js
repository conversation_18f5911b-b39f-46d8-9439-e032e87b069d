import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Switch,
  Alert,
  ActivityIndicator
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import * as budgetService from '../services/budgetService';
import * as budgetAlertService from '../services/budgetAlertService';
import { formatCurrency } from '../utils/formatters';

/**
 * Bütçe Uyarı Formu Ekranı
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @param {Object} props.route - Route nesnesi
 * @returns {JSX.Element} Bütçe Uyarı Formu Ekranı
 */
export default function BudgetAlertFormScreen({ navigation, route }) {
  const db = useSQLiteContext();
  const { budgetId, alertId } = route.params || {};
  const isEditing = !!alertId;

  // Durum değişkenleri
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [budget, setBudget] = useState(null);
  const [formData, setFormData] = useState({
    budget_id: budgetId,
    threshold_type: 'percentage', // percentage, amount
    threshold_value: '80',
    is_active: true,
    notification_type: 'once', // once, recurring
    notification_message: '',
  });

  // Verileri yükle
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);

        // Bütçe bilgilerini getir
        const budgetData = await budgetService.getBudgetById(db, budgetId);
        if (!budgetData) {
          Alert.alert('Hata', 'Bütçe bulunamadı');
          navigation.goBack();
          return;
        }
        setBudget(budgetData);

        // Düzenleme modunda ise uyarı bilgilerini getir
        if (isEditing) {
          const alert = await budgetAlertService.getBudgetAlertById(db, alertId);
          if (alert) {
            setFormData({
              budget_id: alert.budget_id,
              threshold_type: alert.threshold_type,
              threshold_value: alert.threshold_value.toString(),
              is_active: alert.is_active === 1,
              notification_type: alert.notification_type,
              notification_message: alert.notification_message || '',
            });
          }
        } else {
          // Yeni uyarı oluşturuluyorsa, varsayılan mesaj oluştur
          setFormData(prev => ({
            ...prev,
            notification_message: `${budgetData.name} bütçeniz belirlenen limiti aştı!`
          }));
        }

        setLoading(false);
      } catch (error) {
        console.error('Veri yükleme hatası:', error);
        Alert.alert('Hata', 'Veriler yüklenirken bir hata oluştu');
        setLoading(false);
      }
    };

    loadData();
  }, [db, budgetId, alertId, isEditing, navigation]);

  // Form alanını güncelle
  const updateFormField = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Formu doğrula
  const validateForm = () => {
    if (!formData.threshold_value || isNaN(parseFloat(formData.threshold_value))) {
      Alert.alert('Hata', 'Lütfen geçerli bir eşik değeri girin');
      return false;
    }

    const thresholdValue = parseFloat(formData.threshold_value);

    if (formData.threshold_type === 'percentage' && (thresholdValue < 0 || thresholdValue > 100)) {
      Alert.alert('Hata', 'Yüzde değeri 0-100 arasında olmalıdır');
      return false;
    }

    if (formData.threshold_type === 'amount' && thresholdValue <= 0) {
      Alert.alert('Hata', 'Tutar değeri 0\'dan büyük olmalıdır');
      return false;
    }

    if (!formData.notification_message.trim()) {
      Alert.alert('Hata', 'Lütfen bir bildirim mesajı girin');
      return false;
    }

    return true;
  };

  // Uyarıyı kaydet
  const saveAlert = async () => {
    if (!validateForm()) return;

    try {
      setSaving(true);

      const alertData = {
        budget_id: formData.budget_id,
        threshold_type: formData.threshold_type,
        threshold_value: parseFloat(formData.threshold_value),
        is_active: formData.is_active ? 1 : 0,
        notification_type: formData.notification_type,
        notification_message: formData.notification_message.trim()
      };

      if (isEditing) {
        await budgetAlertService.updateBudgetAlert(db, alertId, alertData);
        Alert.alert('Başarılı', 'Bütçe uyarısı güncellendi');
      } else {
        await budgetAlertService.createBudgetAlert(db, alertData);
        Alert.alert('Başarılı', 'Bütçe uyarısı oluşturuldu');
      }

      setSaving(false);
      navigation.goBack();
    } catch (error) {
      console.error('Uyarı kaydetme hatası:', error);
      Alert.alert('Hata', 'Uyarı kaydedilirken bir hata oluştu');
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={styles.loadingText}>Yükleniyor...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {isEditing ? 'Bütçe Uyarısını Düzenle' : 'Yeni Bütçe Uyarısı'}
        </Text>
        <TouchableOpacity
          style={styles.saveButton}
          onPress={saveAlert}
          disabled={saving}
        >
          {saving ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <MaterialIcons name="check" size={24} color="#fff" />
          )}
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        {/* Bütçe Bilgisi */}
        <View style={styles.budgetInfoCard}>
          <Text style={styles.budgetName}>{budget?.name}</Text>
          <Text style={styles.budgetPeriod}>
            {budget?.period === 'monthly' ? 'Aylık' :
             budget?.period === 'weekly' ? 'Haftalık' :
             budget?.period === 'daily' ? 'Günlük' : 'Yıllık'} Bütçe
          </Text>
        </View>

        {/* Form */}
        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>Uyarı Ayarları</Text>

          {/* Eşik Tipi */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>Eşik Tipi</Text>
            <View style={styles.segmentedControl}>
              <TouchableOpacity
                style={[
                  styles.segmentButton,
                  formData.threshold_type === 'percentage' && styles.segmentButtonActive
                ]}
                onPress={() => updateFormField('threshold_type', 'percentage')}
              >
                <Text style={[
                  styles.segmentButtonText,
                  formData.threshold_type === 'percentage' && styles.segmentButtonTextActive
                ]}>Yüzde</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.segmentButton,
                  formData.threshold_type === 'amount' && styles.segmentButtonActive
                ]}
                onPress={() => updateFormField('threshold_type', 'amount')}
              >
                <Text style={[
                  styles.segmentButtonText,
                  formData.threshold_type === 'amount' && styles.segmentButtonTextActive
                ]}>Tutar</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Eşik Değeri */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>
              Eşik Değeri
              {formData.threshold_type === 'percentage' ? ' (%)' : ` (${budget?.currency || 'TRY'})`}
            </Text>
            <TextInput
              style={styles.input}
              value={formData.threshold_value}
              onChangeText={(value) => updateFormField('threshold_value', value)}
              keyboardType="numeric"
              placeholder={formData.threshold_type === 'percentage' ? "Örn: 80" : "Örn: 1000"}
            />
            <Text style={styles.helperText}>
              {formData.threshold_type === 'percentage'
                ? 'Bütçenin bu yüzdesi kullanıldığında uyarı alacaksınız.'
                : 'Bu tutara ulaşıldığında uyarı alacaksınız.'}
            </Text>
          </View>

          {/* Bildirim Tipi */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>Bildirim Tipi</Text>
            <View style={styles.segmentedControl}>
              <TouchableOpacity
                style={[
                  styles.segmentButton,
                  formData.notification_type === 'once' && styles.segmentButtonActive
                ]}
                onPress={() => updateFormField('notification_type', 'once')}
              >
                <Text style={[
                  styles.segmentButtonText,
                  formData.notification_type === 'once' && styles.segmentButtonTextActive
                ]}>Bir Kez</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.segmentButton,
                  formData.notification_type === 'recurring' && styles.segmentButtonActive
                ]}
                onPress={() => updateFormField('notification_type', 'recurring')}
              >
                <Text style={[
                  styles.segmentButtonText,
                  formData.notification_type === 'recurring' && styles.segmentButtonTextActive
                ]}>Tekrarlayan</Text>
              </TouchableOpacity>
            </View>
            <Text style={styles.helperText}>
              {formData.notification_type === 'once'
                ? 'Eşik değerine ulaşıldığında sadece bir kez bildirim alacaksınız.'
                : 'Eşik değeri aşıldığında düzenli olarak bildirim alacaksınız.'}
            </Text>
          </View>

          {/* Bildirim Mesajı */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>Bildirim Mesajı</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={formData.notification_message}
              onChangeText={(value) => updateFormField('notification_message', value)}
              placeholder="Bildirim mesajını girin"
              multiline
            />
          </View>

          {/* Aktif/Pasif */}
          <View style={styles.formGroup}>
            <View style={styles.switchContainer}>
              <Text style={styles.label}>Uyarıyı Etkinleştir</Text>
              <Switch
                value={formData.is_active}
                onValueChange={(value) => updateFormField('is_active', value)}
                trackColor={{ false: '#d1d1d1', true: Colors.PRIMARY_LIGHT }}
                thumbColor={formData.is_active ? Colors.PRIMARY : '#f4f3f4'}
              />
            </View>
            <Text style={styles.helperText}>
              {formData.is_active
                ? 'Uyarı etkin, belirlenen eşik değerine ulaşıldığında bildirim alacaksınız.'
                : 'Uyarı devre dışı, bildirim almayacaksınız.'}
            </Text>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: Colors.PRIMARY,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  headerTitle: {
    flex: 1,
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    marginLeft: 8,
    marginRight: 8,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  saveButton: {
    padding: 8,
    marginLeft: 8,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  budgetInfoCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  budgetName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.GRAY_800,
    marginBottom: 4,
  },
  budgetPeriod: {
    fontSize: 14,
    color: Colors.GRAY_600,
  },
  formSection: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.GRAY_800,
    marginBottom: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.GRAY_700,
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: Colors.GRAY_300,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: Colors.GRAY_800,
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  helperText: {
    fontSize: 12,
    color: Colors.GRAY_500,
    marginTop: 4,
  },
  segmentedControl: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: Colors.GRAY_300,
    borderRadius: 8,
    overflow: 'hidden',
  },
  segmentButton: {
    flex: 1,
    paddingVertical: 10,
    alignItems: 'center',
    backgroundColor: Colors.GRAY_100,
  },
  segmentButtonActive: {
    backgroundColor: Colors.PRIMARY,
  },
  segmentButtonText: {
    fontSize: 14,
    color: Colors.GRAY_700,
  },
  segmentButtonTextActive: {
    color: '#fff',
    fontWeight: '500',
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
});
