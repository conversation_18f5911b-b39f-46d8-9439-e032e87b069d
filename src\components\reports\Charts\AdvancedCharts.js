/**
 * Gelişmiş Chart Bileşenleri
 * React Native i<PERSON><PERSON>, SVG tabanlı charts
 */

import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import Svg, { Circle, Path, Text as SvgText, G, Line, Rect } from 'react-native-svg';
import { useTheme } from '../../../context/ThemeContext';

const { width: screenWidth } = Dimensions.get('window');

/**
 * Gelişmiş Pie Chart
 */
export const AdvancedPieChart = ({ 
  data, 
  size = 200, 
  strokeWidth = 2,
  showLabels = true,
  showPercentages = true,
  centerText = null,
  colors = null
}) => {
  const { theme } = useTheme();
  
  const radius = size / 2 - strokeWidth;
  const center = size / 2;
  
  // Toplam değer hesapla
  const total = data.reduce((sum, item) => sum + Math.abs(item.value), 0);
  
  // Varsayılan renkler
  const defaultColors = [
    '#10B981', '#3B82F6', '#F59E0B', '#EF4444', '#8B5CF6',
    '#06B6D4', '#84CC16', '#F97316', '#EC4899', '#6366F1'
  ];
  
  const chartColors = colors || defaultColors;
  
  // Arc path hesapla
  const createPath = (startAngle, endAngle, radius, centerX, centerY) => {
    const start = polarToCartesian(centerX, centerY, radius, endAngle);
    const end = polarToCartesian(centerX, centerY, radius, startAngle);
    const largeArcFlag = endAngle - startAngle <= 180 ? "0" : "1";
    
    return [
      "M", centerX, centerY,
      "L", start.x, start.y,
      "A", radius, radius, 0, largeArcFlag, 0, end.x, end.y,
      "Z"
    ].join(" ");
  };
  
  const polarToCartesian = (centerX, centerY, radius, angleInDegrees) => {
    const angleInRadians = (angleInDegrees - 90) * Math.PI / 180.0;
    return {
      x: centerX + (radius * Math.cos(angleInRadians)),
      y: centerY + (radius * Math.sin(angleInRadians))
    };
  };
  
  let currentAngle = 0;
  
  return (
    <View style={[styles.chartContainer, { width: size, height: size }]}>
      <Svg width={size} height={size}>
        {data.map((item, index) => {
          const percentage = (Math.abs(item.value) / total) * 100;
          const angle = (percentage / 100) * 360;
          
          const path = createPath(currentAngle, currentAngle + angle, radius, center, center);
          const color = chartColors[index % chartColors.length];
          
          // Label pozisyonu
          const labelAngle = currentAngle + (angle / 2);
          const labelRadius = radius * 0.7;
          const labelPos = polarToCartesian(center, center, labelRadius, labelAngle);
          
          currentAngle += angle;
          
          return (
            <G key={index}>
              <Path
                d={path}
                fill={color}
                stroke={theme.SURFACE}
                strokeWidth={strokeWidth}
              />
              {showPercentages && percentage > 5 && (
                <SvgText
                  x={labelPos.x}
                  y={labelPos.y}
                  fontSize="12"
                  fill={theme.SURFACE}
                  textAnchor="middle"
                  fontWeight="600"
                >
                  {percentage.toFixed(0)}%
                </SvgText>
              )}
            </G>
          );
        })}
        
        {/* Center text */}
        {centerText && (
          <SvgText
            x={center}
            y={center}
            fontSize="16"
            fill={theme.TEXT_PRIMARY}
            textAnchor="middle"
            fontWeight="bold"
          >
            {centerText}
          </SvgText>
        )}
      </Svg>
      
      {/* Legend */}
      {showLabels && (
        <View style={styles.legend}>
          {data.map((item, index) => (
            <View key={index} style={styles.legendItem}>
              <View 
                style={[
                  styles.legendColor, 
                  { backgroundColor: chartColors[index % chartColors.length] }
                ]} 
              />
              <Text style={[styles.legendText, { color: theme.TEXT_PRIMARY }]}>
                {item.label}
              </Text>
              <Text style={[styles.legendValue, { color: theme.TEXT_SECONDARY }]}>
                {item.formattedValue || item.value}
              </Text>
            </View>
          ))}
        </View>
      )}
    </View>
  );
};

/**
 * Gelişmiş Bar Chart
 */
export const AdvancedBarChart = ({ 
  data, 
  width = screenWidth - 40, 
  height = 300,
  showValues = true,
  showGrid = true,
  animated = false,
  horizontal = false
}) => {
  const { theme } = useTheme();
  
  const padding = { top: 20, right: 20, bottom: 60, left: 60 };
  const chartWidth = width - padding.left - padding.right;
  const chartHeight = height - padding.top - padding.bottom;
  
  const maxValue = Math.max(...data.map(item => Math.abs(item.value)));
  const minValue = Math.min(...data.map(item => item.value));
  
  const barWidth = chartWidth / data.length * 0.8;
  const barSpacing = chartWidth / data.length * 0.2;
  
  return (
    <View style={[styles.chartContainer, { width, height }]}>
      <Svg width={width} height={height}>
        {/* Grid lines */}
        {showGrid && (
          <G>
            {[0, 0.25, 0.5, 0.75, 1].map((ratio, index) => {
              const y = padding.top + (chartHeight * ratio);
              return (
                <Line
                  key={index}
                  x1={padding.left}
                  y1={y}
                  x2={padding.left + chartWidth}
                  y2={y}
                  stroke={theme.BORDER}
                  strokeWidth={0.5}
                  opacity={0.3}
                />
              );
            })}
          </G>
        )}
        
        {/* Bars */}
        {data.map((item, index) => {
          const barHeight = (Math.abs(item.value) / maxValue) * chartHeight;
          const x = padding.left + (index * (barWidth + barSpacing)) + (barSpacing / 2);
          const y = item.value >= 0 
            ? padding.top + chartHeight - barHeight
            : padding.top + chartHeight;
          
          const color = item.color || (item.value >= 0 ? '#10B981' : '#EF4444');
          
          return (
            <G key={index}>
              <Rect
                x={x}
                y={y}
                width={barWidth}
                height={barHeight}
                fill={color}
                rx={4}
              />
              
              {showValues && (
                <SvgText
                  x={x + (barWidth / 2)}
                  y={y - 5}
                  fontSize="12"
                  fill={theme.TEXT_PRIMARY}
                  textAnchor="middle"
                  fontWeight="600"
                >
                  {item.formattedValue || item.value}
                </SvgText>
              )}
              
              {/* Label */}
              <SvgText
                x={x + (barWidth / 2)}
                y={height - padding.bottom + 15}
                fontSize="11"
                fill={theme.TEXT_SECONDARY}
                textAnchor="middle"
                transform={`rotate(-45 ${x + (barWidth / 2)} ${height - padding.bottom + 15})`}
              >
                {item.label}
              </SvgText>
            </G>
          );
        })}
        
        {/* Y-axis */}
        <Line
          x1={padding.left}
          y1={padding.top}
          x2={padding.left}
          y2={padding.top + chartHeight}
          stroke={theme.BORDER}
          strokeWidth={1}
        />
        
        {/* X-axis */}
        <Line
          x1={padding.left}
          y1={padding.top + chartHeight}
          x2={padding.left + chartWidth}
          y2={padding.top + chartHeight}
          stroke={theme.BORDER}
          strokeWidth={1}
        />
      </Svg>
    </View>
  );
};

/**
 * Gelişmiş Line Chart
 */
export const AdvancedLineChart = ({ 
  data, 
  width = screenWidth - 40, 
  height = 300,
  showDots = true,
  showArea = false,
  smooth = true,
  showGrid = true
}) => {
  const { theme } = useTheme();
  
  const padding = { top: 20, right: 20, bottom: 60, left: 60 };
  const chartWidth = width - padding.left - padding.right;
  const chartHeight = height - padding.top - padding.bottom;
  
  const maxValue = Math.max(...data.map(item => item.value));
  const minValue = Math.min(...data.map(item => item.value));
  const valueRange = maxValue - minValue;
  
  // Path hesapla
  const createPath = () => {
    let path = '';
    
    data.forEach((item, index) => {
      const x = padding.left + (index / (data.length - 1)) * chartWidth;
      const y = padding.top + chartHeight - ((item.value - minValue) / valueRange) * chartHeight;
      
      if (index === 0) {
        path += `M ${x} ${y}`;
      } else {
        if (smooth) {
          // Bezier curve için control points
          const prevX = padding.left + ((index - 1) / (data.length - 1)) * chartWidth;
          const prevY = padding.top + chartHeight - ((data[index - 1].value - minValue) / valueRange) * chartHeight;
          const cpX1 = prevX + (x - prevX) * 0.5;
          const cpY1 = prevY;
          const cpX2 = prevX + (x - prevX) * 0.5;
          const cpY2 = y;
          
          path += ` C ${cpX1} ${cpY1} ${cpX2} ${cpY2} ${x} ${y}`;
        } else {
          path += ` L ${x} ${y}`;
        }
      }
    });
    
    return path;
  };
  
  const linePath = createPath();
  
  return (
    <View style={[styles.chartContainer, { width, height }]}>
      <Svg width={width} height={height}>
        {/* Grid */}
        {showGrid && (
          <G>
            {[0, 0.25, 0.5, 0.75, 1].map((ratio, index) => {
              const y = padding.top + (chartHeight * ratio);
              return (
                <Line
                  key={index}
                  x1={padding.left}
                  y1={y}
                  x2={padding.left + chartWidth}
                  y2={y}
                  stroke={theme.BORDER}
                  strokeWidth={0.5}
                  opacity={0.3}
                />
              );
            })}
          </G>
        )}
        
        {/* Area fill */}
        {showArea && (
          <Path
            d={`${linePath} L ${padding.left + chartWidth} ${padding.top + chartHeight} L ${padding.left} ${padding.top + chartHeight} Z`}
            fill={theme.PRIMARY}
            opacity={0.1}
          />
        )}
        
        {/* Line */}
        <Path
          d={linePath}
          stroke={theme.PRIMARY}
          strokeWidth={3}
          fill="none"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        
        {/* Dots */}
        {showDots && data.map((item, index) => {
          const x = padding.left + (index / (data.length - 1)) * chartWidth;
          const y = padding.top + chartHeight - ((item.value - minValue) / valueRange) * chartHeight;
          
          return (
            <Circle
              key={index}
              cx={x}
              cy={y}
              r={4}
              fill={theme.SURFACE}
              stroke={theme.PRIMARY}
              strokeWidth={2}
            />
          );
        })}
        
        {/* Axes */}
        <Line
          x1={padding.left}
          y1={padding.top}
          x2={padding.left}
          y2={padding.top + chartHeight}
          stroke={theme.BORDER}
          strokeWidth={1}
        />
        <Line
          x1={padding.left}
          y1={padding.top + chartHeight}
          x2={padding.left + chartWidth}
          y2={padding.top + chartHeight}
          stroke={theme.BORDER}
          strokeWidth={1}
        />
      </Svg>
    </View>
  );
};

const styles = StyleSheet.create({
  chartContainer: {
    alignItems: 'center',
    marginVertical: 10,
  },
  legend: {
    marginTop: 20,
    width: '100%',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 4,
    paddingHorizontal: 10,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 10,
  },
  legendText: {
    flex: 1,
    fontSize: 14,
    fontWeight: '500',
  },
  legendValue: {
    fontSize: 12,
    fontWeight: '600',
  },
});
