import React, { createContext, useContext, useState, useEffect } from 'react';
import { useRealData } from '../hooks/useRealData';

/**
 * Data Integration Context
 * Manages the transition between mock and real data
 */
const DataIntegrationContext = createContext();

/**
 * Data Integration Provider
 * Provides unified access to both mock and real data
 */
export const DataIntegrationProvider = ({ children }) => {
  const [useRealDataMode, setUseRealDataMode] = useState(true);
  const [dataSource, setDataSource] = useState('auto'); // 'auto', 'real', 'mock'
  const [hasRealData, setHasRealData] = useState(false);
  const [dataFreshness, setDataFreshness] = useState(null);
  
  const realDataHook = useRealData();

  // Check for real data availability on mount
  useEffect(() => {
    const checkRealData = async () => {
      try {
        if (realDataHook.isReady) {
          const hasData = await realDataHook.hasRealData();
          setHasRealData(hasData);
          
          if (hasData) {
            const freshness = await realDataHook.getDataFreshness();
            setDataFreshness(freshness);
          }
        }
      } catch (error) {
        setHasRealData(false);
      }
    };

    checkRealData();
  }, [realDataHook.isReady]);

  // Auto-decide data source based on availability
  useEffect(() => {
    if (dataSource === 'auto') {
      setUseRealDataMode(hasRealData);
    } else if (dataSource === 'real') {
      setUseRealDataMode(true);
    } else if (dataSource === 'mock') {
      setUseRealDataMode(false);
    }
  }, [dataSource, hasRealData]);

  /**
   * Get monthly income/expense data
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Monthly data
   */
  const getMonthlyIncomeExpense = async (params = {}) => {
    if (useRealDataMode && hasRealData) {
      return await realDataHook.getMonthlyIncomeExpense(params);
    }
    
    // Return mock data
    return getMockMonthlyIncomeExpense(params);
  };

  /**
   * Get category distribution data
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Category distribution
   */
  const getCategoryDistribution = async (params = {}) => {
    if (useRealDataMode && hasRealData) {
      return await realDataHook.getCategoryDistribution(params);
    }
    
    // Return mock data
    return getMockCategoryDistribution(params);
  };

  /**
   * Get daily trends data
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Daily trends
   */
  const getDailyTrends = async (params = {}) => {
    if (useRealDataMode && hasRealData) {
      return await realDataHook.getDailyTrends(params);
    }
    
    // Return mock data
    return getMockDailyTrends(params);
  };

  /**
   * Get top transactions
   * @param {Object} params - Query parameters
   * @returns {Promise<Array>} Top transactions
   */
  const getTopTransactions = async (params = {}) => {
    if (useRealDataMode && hasRealData) {
      return await realDataHook.getTopTransactions(params);
    }
    
    // Return mock data
    return getMockTopTransactions(params);
  };

  /**
   * Get chart data for different chart types
   * @param {string} chartType - Chart type
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Chart data
   */
  const getChartData = async (chartType, params = {}) => {
    if (useRealDataMode && hasRealData) {
      return await realDataHook.getChartData(chartType, params);
    }
    
    // Return mock chart data
    return getMockChartData(chartType, params);
  };

  /**
   * Get table data for interactive table builder
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Table data
   */
  const getTableData = async (params = {}) => {
    if (useRealDataMode && hasRealData) {
      return await realDataHook.getTableData(params);
    }
    
    // Return mock table data
    return getMockTableData(params);
  };

  /**
   * Get report summary statistics
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Summary statistics
   */
  const getReportSummary = async (params = {}) => {
    if (useRealDataMode && hasRealData) {
      return await realDataHook.getReportSummary(params);
    }
    
    // Return mock summary
    return getMockReportSummary(params);
  };

  /**
   * Get regular income tracking data
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Regular income tracking data
   */
  const getRegularIncomeTracking = async (params = {}) => {
    try {
      if (useRealDataMode && realDataHook.isReady) {
        return await realDataHook.getRegularIncomeTracking(params);
      } else {
        return getMockRegularIncomeTracking(params);
      }
    } catch (error) {
      console.warn('Regular income tracking failed, using mock data:', error.message);
      return getMockRegularIncomeTracking(params);
    }
  };

  /**
   * Get overtime income data
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Overtime income data
   */
  const getOvertimeIncomeData = async (params = {}) => {
    try {
      if (useRealDataMode && realDataHook.isReady) {
        return await realDataHook.getOvertimeIncomeData(params);
      } else {
        return getMockOvertimeIncomeData(params);
      }
    } catch (error) {
      console.warn('Overtime income data failed, using mock data:', error.message);
      return getMockOvertimeIncomeData(params);
    }
  };

  // Mock data functions
  const getMockMonthlyIncomeExpense = (params) => {
    return {
      period: 'Ocak 2024',
      income: {
        total: 45000,
        categories: [
          { name: 'Maaş', amount: 35000, percentage: 77.8, color: '#28a745', count: 1 },
          { name: 'Freelance', amount: 8000, percentage: 17.8, color: '#17a2b8', count: 3 },
          { name: 'Diğer', amount: 2000, percentage: 4.4, color: '#ffc107', count: 2 }
        ]
      },
      expenses: {
        total: 32000,
        categories: [
          { name: 'Kira', amount: 15000, percentage: 46.9, color: '#dc3545', count: 1 },
          { name: 'Gıda', amount: 5000, percentage: 15.6, color: '#fd7e14', count: 20 },
          { name: 'Ulaşım', amount: 3000, percentage: 9.4, color: '#6f42c1', count: 15 },
          { name: 'Faturalar', amount: 4000, percentage: 12.5, color: '#20c997', count: 8 },
          { name: 'Diğer', amount: 5000, percentage: 15.6, color: '#6c757d', count: 12 }
        ]
      },
      netIncome: 13000,
      savings: 13000,
      savingsRate: 28.9
    };
  };

  const getMockCategoryDistribution = (params) => {
    const { type = 'expense' } = params;
    
    if (type === 'expense') {
      return {
        type: 'expense',
        period: 'Ocak 2024',
        totalAmount: 32000,
        categories: [
          { name: 'Kira', amount: 15000, percentage: 46.9, color: '#dc3545', icon: '🏠', count: 1, avgAmount: 15000 },
          { name: 'Gıda', amount: 5000, percentage: 15.6, color: '#fd7e14', icon: '🍕', count: 20, avgAmount: 250 },
          { name: 'Ulaşım', amount: 3000, percentage: 9.4, color: '#6f42c1', icon: '🚗', count: 15, avgAmount: 200 },
          { name: 'Faturalar', amount: 4000, percentage: 12.5, color: '#20c997', icon: '💡', count: 8, avgAmount: 500 },
          { name: 'Diğer', amount: 5000, percentage: 15.6, color: '#6c757d', icon: '📦', count: 12, avgAmount: 417 }
        ]
      };
    } else {
      return {
        type: 'income',
        period: 'Ocak 2024',
        totalAmount: 45000,
        categories: [
          { name: 'Maaş', amount: 35000, percentage: 77.8, color: '#28a745', icon: '💰', count: 1, avgAmount: 35000 },
          { name: 'Freelance', amount: 8000, percentage: 17.8, color: '#17a2b8', icon: '💻', count: 3, avgAmount: 2667 },
          { name: 'Diğer', amount: 2000, percentage: 4.4, color: '#ffc107', icon: '📈', count: 2, avgAmount: 1000 }
        ]
      };
    }
  };

  const getMockDailyTrends = (params) => {
    const labels = ['01/01', '02/01', '03/01', '04/01', '05/01', '06/01', '07/01'];
    const income = [0, 0, 2000, 0, 0, 35000, 0];
    const expenses = [500, 300, 800, 200, 450, 600, 750];
    
    return {
      period: 'Son 7 Gün',
      labels,
      income,
      expenses,
      netFlow: income.map((inc, i) => inc - expenses[i]),
      transactionCounts: [2, 1, 4, 1, 3, 2, 3],
      summary: {
        totalIncome: income.reduce((a, b) => a + b, 0),
        totalExpense: expenses.reduce((a, b) => a + b, 0),
        avgDailyIncome: income.reduce((a, b) => a + b, 0) / 7,
        avgDailyExpense: expenses.reduce((a, b) => a + b, 0) / 7
      }
    };
  };

  const getMockTopTransactions = (params) => {
    const { type, limit = 10 } = params;
    
    const mockTransactions = [
      { id: 1, amount: 35000, type: 'income', date: '2024-01-06', description: 'Maaş', category: { name: 'Maaş', color: '#28a745', icon: '💰' } },
      { id: 2, amount: 15000, type: 'expense', date: '2024-01-01', description: 'Kira', category: { name: 'Kira', color: '#dc3545', icon: '🏠' } },
      { id: 3, amount: 3000, type: 'income', date: '2024-01-15', description: 'Freelance Proje', category: { name: 'Freelance', color: '#17a2b8', icon: '💻' } },
      { id: 4, amount: 2000, type: 'expense', date: '2024-01-10', description: 'Faturalar', category: { name: 'Faturalar', color: '#20c997', icon: '💡' } },
      { id: 5, amount: 1500, type: 'expense', date: '2024-01-05', description: 'Market Alışverişi', category: { name: 'Gıda', color: '#fd7e14', icon: '🍕' } }
    ];

    let filtered = mockTransactions;
    if (type) {
      filtered = filtered.filter(t => t.type === type);
    }

    return filtered.slice(0, limit).map(t => ({
      ...t,
      formattedDate: new Date(t.date).toLocaleDateString('tr-TR'),
      formattedAmount: new Intl.NumberFormat('tr-TR', { style: 'currency', currency: 'TRY' }).format(t.amount)
    }));
  };

  const getMockChartData = (chartType, params) => {
    switch (chartType) {
      case 'line':
      case 'bar':
        return {
          labels: ['Oca', 'Şub', 'Mar', 'Nis', 'May', 'Haz'],
          datasets: [{
            data: [2400, 1398, 9800, 3908, 4800, 3800],
            color: (opacity = 1) => `rgba(0, 122, 255, ${opacity})`,
            strokeWidth: 2
          }]
        };
        
      case 'pie':
        return [
          { name: 'Gelir', population: 35000, color: '#28a745', legendFontColor: '#333', legendFontSize: 12 },
          { name: 'Gider', population: 25000, color: '#dc3545', legendFontColor: '#333', legendFontSize: 12 },
          { name: 'Yatırım', population: 15000, color: '#ffc107', legendFontColor: '#333', legendFontSize: 12 },
          { name: 'Tasarruf', population: 10000, color: '#17a2b8', legendFontColor: '#333', legendFontSize: 12 }
        ];
        
      case 'progress':
        return {
          labels: ['Hedef 1', 'Hedef 2', 'Hedef 3', 'Hedef 4'],
          data: [0.8, 0.6, 0.4, 0.9]
        };
        
      default:
        return null;
    }
  };

  const getMockTableData = (params) => {
    const { dataSource = 'transactions' } = params;
    
    switch (dataSource) {
      case 'transactions':
        return {
          columns: [
            { key: 'date', label: 'Tarih', type: 'date' },
            { key: 'description', label: 'Açıklama', type: 'text' },
            { key: 'category', label: 'Kategori', type: 'text' },
            { key: 'amount', label: 'Tutar', type: 'number' },
            { key: 'type', label: 'Tür', type: 'text' }
          ],
          rows: [
            { date: '2024-01-06', description: 'Maaş', category: 'Maaş', amount: 35000, type: 'Gelir' },
            { date: '2024-01-01', description: 'Kira', category: 'Kira', amount: 15000, type: 'Gider' },
            { date: '2024-01-15', description: 'Freelance', category: 'Freelance', amount: 3000, type: 'Gelir' },
            { date: '2024-01-10', description: 'Faturalar', category: 'Faturalar', amount: 2000, type: 'Gider' }
          ]
        };
      
      default:
        return { columns: [], rows: [] };
    }
  };

  const getMockReportSummary = (params) => {
    return {
      period: 'Ocak 2024',
      totalTransactions: 56,
      totalIncome: 45000,
      totalExpense: 32000,
      netIncome: 13000,
      avgIncome: 7500,
      avgExpense: 640,
      maxIncome: 35000,
      maxExpense: 15000,
      categoriesUsed: 8,
      savingsRate: 28.9,
      avgDailySpending: 1032
    };
  };

  const getMockRegularIncomeTracking = (params) => {
    const now = new Date();
    const startDate = params.startDate || new Date(now.getFullYear(), now.getMonth(), 1);
    const endDate = params.endDate || new Date(now.getFullYear(), now.getMonth() + 1, 0);

    const mockTransactions = [
      {
        date: '2024-01-01',
        amount: 15000,
        description: 'Ocak Maaşı',
        category: 'Maaş',
        color: '#2ECC71'
      },
      {
        date: '2024-01-15',
        amount: 2500,
        description: 'Performans Primi',
        category: 'Ek Gelir',
        color: '#27ae60'
      },
      {
        date: '2024-02-01',
        amount: 15000,
        description: 'Şubat Maaşı',
        category: 'Maaş',
        color: '#2ECC71'
      }
    ];

    const mockWorkPayments = [
      {
        periodStart: '2024-01-01',
        periodEnd: '2024-01-31',
        hours: 160,
        amount: 15000,
        paymentDate: '2024-02-01',
        isPaid: true
      },
      {
        periodStart: '2024-02-01',
        periodEnd: '2024-02-28',
        hours: 150,
        amount: 14000,
        paymentDate: '2024-03-01',
        isPaid: true
      }
    ];

    const totalIncome = mockTransactions.reduce((sum, t) => sum + t.amount, 0);
    const totalWorkPayments = mockWorkPayments.reduce((sum, p) => sum + p.amount, 0);
    const expectedIncome = 15000; // Expected monthly income

    return {
      period: `${startDate.toLocaleDateString('tr-TR')} - ${endDate.toLocaleDateString('tr-TR')}`,
      summary: {
        totalIncome: totalIncome + totalWorkPayments,
        totalRegularIncome: totalIncome,
        totalWorkPayments,
        monthlyAverage: (totalIncome + totalWorkPayments) / 2,
        expectedIncome,
        varianceFromExpected: (totalIncome + totalWorkPayments) - expectedIncome,
        variancePercentage: ((totalIncome + totalWorkPayments - expectedIncome) / expectedIncome) * 100
      },
      transactions: mockTransactions,
      workPayments: mockWorkPayments,
      settings: {
        hourlyRate: 95,
        weeklyWorkHours: 40,
        dailyWorkHours: 8,
        currency: 'TRY'
      }
    };
  };

  const getMockOvertimeIncomeData = (params) => {
    const now = new Date();
    const startDate = params.startDate || new Date(now.getFullYear(), now.getMonth(), 1);
    const endDate = params.endDate || new Date(now.getFullYear(), now.getMonth() + 1, 0);

    const mockShifts = [
      {
        date: '2024-01-15',
        startTime: '08:00',
        endTime: '20:00',
        duration: 10,
        overtimeMultiplier: 1.5,
        estimatedEarnings: 950,
        notes: 'Proje teslimi',
        status: 'completed'
      },
      {
        date: '2024-01-20',
        startTime: '08:00',
        endTime: '22:00',
        duration: 12,
        overtimeMultiplier: 1.5,
        estimatedEarnings: 1140,
        notes: 'Acil bakım',
        status: 'completed'
      },
      {
        date: '2024-02-10',
        startTime: '08:00',
        endTime: '18:00',
        duration: 8,
        overtimeMultiplier: 2.0,
        estimatedEarnings: 760,
        notes: 'Hafta sonu çalışma',
        status: 'completed'
      }
    ];

    const mockPayments = [
      {
        periodStart: '2024-01-01',
        periodEnd: '2024-01-31',
        hours: 25,
        amount: 3562.50,
        paymentDate: '2024-02-01',
        isPaid: true
      },
      {
        periodStart: '2024-02-01',
        periodEnd: '2024-02-28',
        hours: 18,
        amount: 2565.00,
        paymentDate: '2024-03-01',
        isPaid: false
      }
    ];

    const totalOvertimeAmount = mockPayments.reduce((sum, p) => sum + p.amount, 0);
    const totalOvertimeHours = mockPayments.reduce((sum, p) => sum + p.hours, 0);
    const totalEstimatedEarnings = mockShifts.reduce((sum, s) => sum + s.estimatedEarnings, 0);
    const totalEstimatedHours = mockShifts.reduce((sum, s) => sum + s.duration, 0);

    return {
      period: `${startDate.toLocaleDateString('tr-TR')} - ${endDate.toLocaleDateString('tr-TR')}`,
      summary: {
        totalOvertimeAmount,
        totalOvertimeHours,
        totalEstimatedEarnings,
        totalEstimatedHours,
        monthlyAverage: totalOvertimeAmount / 2,
        averageHourlyRate: totalOvertimeHours > 0 ? totalOvertimeAmount / totalOvertimeHours : 0,
        baseHourlyRate: 95,
        overtimeRate: 142.5
      },
      shifts: mockShifts,
      payments: mockPayments,
      settings: {
        hourlyRate: 95,
        overtimeRate: 142.5,
        weeklyWorkHours: 40,
        dailyWorkHours: 8,
        currency: 'TRY'
      }
    };
  };

  /**
   * Force refresh of data freshness
   */
  const refreshDataFreshness = async () => {
    if (realDataHook.isReady) {
      try {
        const freshness = await realDataHook.getDataFreshness();
        setDataFreshness(freshness);
        
        const hasData = await realDataHook.hasRealData();
        setHasRealData(hasData);
      } catch (error) {
        // Ignore errors during refresh
      }
    }
  };

  const value = {
    // Data methods
    getMonthlyIncomeExpense,
    getCategoryDistribution,
    getDailyTrends,
    getTopTransactions,
    getChartData,
    getTableData,
    getReportSummary,
    getRegularIncomeTracking,
    getOvertimeIncomeData,
    
    // Configuration
    dataSource,
    setDataSource,
    useRealDataMode,
    setUseRealDataMode,
    
    // State
    hasRealData,
    dataFreshness,
    loading: realDataHook.loading,
    error: realDataHook.error,
    
    // Utilities
    refreshDataFreshness,
    isReady: realDataHook.isReady
  };

  return (
    <DataIntegrationContext.Provider value={value}>
      {children}
    </DataIntegrationContext.Provider>
  );
};

/**
 * Hook to use data integration context
 * @returns {Object} Data integration context
 */
export const useDataIntegration = () => {
  const context = useContext(DataIntegrationContext);
  if (!context) {
    throw new Error('useDataIntegration must be used within a DataIntegrationProvider');
  }
  return context;
};
