import React, { useEffect, useState } from 'react';
import { View, ActivityIndicator, StyleSheet, Text, TouchableOpacity, Alert } from 'react-native';
import { useAuth } from '../context/AuthContext';
import { useAppContext } from '../context/AppContext';

/**
 * Kimlik Doğrulama Wrapper
 * Basitleştirilmiş giriş sistemi
 */
export default function AuthWrapper({ children }) {
  const { 
    isAuthenticated, 
    setIsAuthenticated,
    isLoading, 
    isFirstTime,
    resetAllData
  } = useAuth();
  const { theme } = useAppContext();
  const [authChecked, setAuthChecked] = useState(false);
  const [showDevMode, setShowDevMode] = useState(false);

  useEffect(() => {
    checkAuthentication();
  }, []);

  const checkAuthentication = async () => {
    try {
      // Basit authentication kontrolü
      setAuthChecked(true);
    } catch (error) {
      console.error('Authentication kontrol hatası:', error);
      setAuthChecked(true);
    }
  };

  // Test fonksiyonu - Tüm verileri sil
  const handleResetAllData = async () => {
    try {
      const result = await resetAllData();
      if (result.success) {
        Alert.alert('✅ Başarılı', 'Tüm veriler temizlendi.');
      } else {
        Alert.alert('❌ Hata', result.error || 'Veri temizleme başarısız');
      }
    } catch (error) {
      Alert.alert('❌ Hata', 'Veri temizleme sırasında hata oluştu');
    }
  };

  // Yükleniyor durumu
  if (isLoading || !authChecked) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.BACKGROUND }]}>
        <ActivityIndicator size="large" color={theme.PRIMARY} />
        <Text style={[styles.loadingText, { color: theme.TEXT_PRIMARY }]}>
          🚀 Hoş Geldiniz!
        </Text>
        
        {/* Geliştirici Test Butonları */}
        <TouchableOpacity
          style={[styles.devButton, { backgroundColor: theme.ERROR || '#FF6B6B' }]}
          onPress={() => setShowDevMode(!showDevMode)}
        >
          <Text style={styles.devButtonText}>
            {showDevMode ? '🔒 Dev Modunu Kapat' : '🔧 Dev Modu'}
          </Text>
        </TouchableOpacity>
        
        {showDevMode && (
          <View style={styles.devContainer}>
            <TouchableOpacity
              style={[styles.testButton, { backgroundColor: '#FF6B6B' }]}
              onPress={handleResetAllData}
            >
              <Text style={styles.testButtonText}>🗑️ Tüm Verileri Sil</Text>
            </TouchableOpacity>
            
            <Text style={[styles.devInfo, { color: theme.TEXT_SECONDARY }]}>
              ⚠️ Bu test amacıyla eklendi{'\n'}
              Tüm uygulama verileri silinir
            </Text>
          </View>
        )}
      </View>
    );
  }

  // İlk kez açılış - basit hoş geldin ekranı
  if (isFirstTime) {
    return (
      <View style={[styles.welcomeContainer, { backgroundColor: theme.BACKGROUND }]}>
        <Text style={[styles.welcomeTitle, { color: theme.TEXT_PRIMARY }]}>
          🎉 Hoş Geldiniz!
        </Text>
        <Text style={[styles.welcomeSubtitle, { color: theme.TEXT_SECONDARY }]}>
          Kişisel finans yönetim uygulamanız hazır
        </Text>
        <TouchableOpacity
          style={[styles.startButton, { backgroundColor: theme.PRIMARY }]}
          onPress={() => {
            setIsAuthenticated(true);
            setAuthChecked(true);
          }}
        >
          <Text style={[styles.startButtonText, { color: theme.WHITE }]}>
            🚀 Başlayalım
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Authentication gerekliyse basit giriş ekranı
  if (!isAuthenticated) {
    return (
      <View style={[styles.authContainer, { backgroundColor: theme.BACKGROUND }]}>
        <Text style={[styles.authTitle, { color: theme.TEXT_PRIMARY }]}>
          👋 Tekrar Hoş Geldiniz!
        </Text>
        <Text style={[styles.authSubtitle, { color: theme.TEXT_SECONDARY }]}>
          Uygulamaya devam etmek için giriş yapın
        </Text>
        <TouchableOpacity
          style={[styles.loginButton, { backgroundColor: theme.PRIMARY }]}
          onPress={() => {
            setIsAuthenticated(true);
            setAuthChecked(true);
          }}
        >
          <Text style={[styles.loginButtonText, { color: theme.WHITE }]}>
            🔓 Giriş Yap
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Authentication tamamlandı, ana uygulamayı göster
  return children;
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    fontSize: 24,
    fontWeight: '600',
    marginTop: 16,
    textAlign: 'center',
  },
  welcomeContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  welcomeTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
  },
  welcomeSubtitle: {
    fontSize: 18,
    textAlign: 'center',
    marginBottom: 40,
    lineHeight: 26,
  },
  startButton: {
    paddingHorizontal: 40,
    paddingVertical: 16,
    borderRadius: 25,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  startButtonText: {
    fontSize: 18,
    fontWeight: '600',
  },
  authContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  authTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
  },
  authSubtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 24,
  },
  loginButton: {
    paddingHorizontal: 40,
    paddingVertical: 16,
    borderRadius: 25,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  loginButtonText: {
    fontSize: 18,
    fontWeight: '600',
  },
  testButton: {
    marginTop: 32,
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderRadius: 12,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  testButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
  devButton: {
    marginTop: 24,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  devButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  devContainer: {
    marginTop: 16,
    alignItems: 'center',
  },
  devInfo: {
    fontSize: 12,
    textAlign: 'center',
    marginTop: 12,
    paddingHorizontal: 20,
    lineHeight: 16,
  },
});
