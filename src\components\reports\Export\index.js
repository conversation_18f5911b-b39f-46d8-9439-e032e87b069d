// Export System - Ana Export Modülü
export { ExportManager, ExportManagerComponent } from './ExportManager';
export { PDFGenerator } from './PDFGenerator';
export { ExcelExporter } from './ExcelExporter';
export { PowerPointExporter } from './PowerPointExporter';
export { ImageExporter } from './ImageExporter';
export { EmailExporter } from './EmailExporter';

// Export Types
export const EXPORT_TYPES = {
  PDF: 'pdf',
  EXCEL: 'excel',
  POWERPOINT: 'powerpoint',
  IMAGE: 'image',
  EMAIL: 'email'
};

// Export Formats
export const EXPORT_FORMATS = {
  PDF: 'pdf',
  CSV: 'csv',
  XLSX: 'xlsx',
  PPTX: 'pptx',
  HTML: 'html',
  PNG: 'png',
  JPEG: 'jpeg',
  SVG: 'svg'
};

// Export Configurations
export const DEFAULT_EXPORT_CONFIG = {
  pdf: {
    format: 'A4',
    orientation: 'portrait',
    margins: {
      top: 50,
      bottom: 50,
      left: 50,
      right: 50
    },
    includeCharts: true,
    includeData: true
  },
  excel: {
    format: 'csv',
    separator: ',',
    encoding: 'utf8',
    includeHeaders: true,
    includeMetadata: true
  },
  powerpoint: {
    format: 'html',
    theme: 'professional',
    includeCharts: true,
    includeAnimation: true,
    slideLayout: 'standard'
  },
  image: {
    format: 'png',
    quality: 1.0,
    width: 1920,
    height: 1080,
    backgroundColor: '#ffffff'
  },
  email: {
    isHtml: true,
    includeAttachments: true,
    attachmentFormats: ['pdf', 'excel']
  }
};

// Export Utility Functions
export const exportUtils = {
  /**
   * Dosya adı oluşturur
   * @param {string} title - Rapor başlığı
   * @param {string} type - Rapor türü
   * @param {string} format - Dosya formatı
   * @returns {string} Dosya adı
   */
  generateFileName: (title, type, format) => {
    const date = new Date();
    const dateStr = date.toISOString().split('T')[0];
    const timeStr = date.toTimeString().split(' ')[0].replace(/:/g, '-');
    
    const cleanTitle = title
      .replace(/[^a-zA-Z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .toLowerCase();
    
    return `${cleanTitle}-${dateStr}-${timeStr}.${format}`;
  },

  /**
   * Dosya boyutunu formatlar
   * @param {number} bytes - Byte cinsinden boyut
   * @returns {string} Formatlanmış boyut
   */
  formatFileSize: (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  /**
   * MIME türünü formatına göre döndürür
   * @param {string} format - Dosya formatı
   * @returns {string} MIME türü
   */
  getMimeType: (format) => {
    const mimeTypes = {
      pdf: 'application/pdf',
      csv: 'text/csv',
      xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      html: 'text/html',
      png: 'image/png',
      jpeg: 'image/jpeg',
      svg: 'image/svg+xml'
    };
    return mimeTypes[format] || 'application/octet-stream';
  },

  /**
   * Export yapılandırmasını birleştirir
   * @param {string} type - Export türü
   * @param {Object} customConfig - Özel yapılandırma
   * @returns {Object} Birleştirilmiş yapılandırma
   */
  mergeConfig: (type, customConfig = {}) => {
    const defaultConfig = DEFAULT_EXPORT_CONFIG[type] || {};
    return { ...defaultConfig, ...customConfig };
  },

  /**
   * Export durumunu takip eder
   * @param {string} exportId - Export ID'si
   * @returns {Object} Export durumu
   */
  getExportStatus: (exportId) => {
    // Gerçek implementasyonda Redux store veya AsyncStorage kullanılabilir
    return {
      id: exportId,
      status: 'completed',
      progress: 100,
      fileName: '',
      uri: '',
      error: null,
      timestamp: new Date().toISOString()
    };
  }
};

// Export Manager Helper
export const createExportManager = (config = {}) => {
  return {
    exportToPDF: (data, title, type, customConfig = {}) => {
      const mergedConfig = exportUtils.mergeConfig('pdf', customConfig);
      return PDFGenerator.generatePDF({ data, title, type, config: mergedConfig });
    },
    
    exportToExcel: (data, title, type, customConfig = {}) => {
      const mergedConfig = exportUtils.mergeConfig('excel', customConfig);
      return ExcelExporter.exportToExcel({ data, title, type, config: mergedConfig });
    },
    
    exportToPowerPoint: (data, title, type, customConfig = {}) => {
      const mergedConfig = exportUtils.mergeConfig('powerpoint', customConfig);
      return PowerPointExporter.exportToPowerPoint({ data, title, type, config: mergedConfig });
    },
    
    exportToImage: (data, title, type, customConfig = {}) => {
      const mergedConfig = exportUtils.mergeConfig('image', customConfig);
      return ImageExporter.exportToImage({ data, title, type, config: mergedConfig });
    },
    
    sendEmail: (data, title, type, customConfig = {}) => {
      const mergedConfig = exportUtils.mergeConfig('email', customConfig);
      return EmailExporter.sendReport({ data, title, type, config: mergedConfig });
    }
  };
};

export default ExportManager;
