import { useState, useCallback, useRef } from 'react';

/**
 * <PERSON>vas editörü hook
 * Widget yönetimi, s<PERSON><PERSON><PERSON><PERSON>-b<PERSON><PERSON>, geri al/yinele
 */
export const useCanvasEditor = () => {
  const [canvasElements, setCanvasElements] = useState([]);
  const [selectedElement, setSelectedElement] = useState(null);
  const [history, setHistory] = useState([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [clipboard, setClipboard] = useState(null);
  const nextIdRef = useRef(1);

  /**
   * Geçmişe ekle
   */
  const addToHistory = useCallback((elements) => {
    setHistory(prev => {
      const newHistory = prev.slice(0, historyIndex + 1);
      newHistory.push(elements);
      return newHistory;
    });
    setHistoryIndex(prev => prev + 1);
  }, [historyIndex]);

  /**
   * Yeni element ekle
   */
  const addElement = useCallback((elementData) => {
    const newElement = {
      id: nextIdRef.current.toString(),
      ...elementData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    nextIdRef.current += 1;
    
    setCanvasElements(prev => {
      const newElements = [...prev, newElement];
      addToHistory(newElements);
      return newElements;
    });
    
    setSelectedElement(newElement);
  }, [addToHistory]);

  /**
   * Element kaldır
   */
  const removeElement = useCallback((elementId) => {
    setCanvasElements(prev => {
      const newElements = prev.filter(el => el.id !== elementId);
      addToHistory(newElements);
      return newElements;
    });
    
    if (selectedElement?.id === elementId) {
      setSelectedElement(null);
    }
  }, [selectedElement, addToHistory]);

  /**
   * Element güncelle
   */
  const updateElement = useCallback((elementId, updates) => {
    setCanvasElements(prev => {
      const newElements = prev.map(el => 
        el.id === elementId 
          ? { ...el, ...updates, updatedAt: new Date().toISOString() }
          : el
      );
      addToHistory(newElements);
      return newElements;
    });
    
    if (selectedElement?.id === elementId) {
      setSelectedElement(prev => ({ ...prev, ...updates }));
    }
  }, [selectedElement, addToHistory]);

  /**
   * Element taşı
   */
  const moveElement = useCallback((elementId, x, y) => {
    setCanvasElements(prev => {
      const newElements = prev.map(el => 
        el.id === elementId 
          ? { ...el, x, y, updatedAt: new Date().toISOString() }
          : el
      );
      return newElements;
    });
    
    if (selectedElement?.id === elementId) {
      setSelectedElement(prev => ({ ...prev, x, y }));
    }
  }, [selectedElement]);

  /**
   * Element boyutlandır
   */
  const resizeElement = useCallback((elementId, width, height) => {
    setCanvasElements(prev => {
      const newElements = prev.map(el => 
        el.id === elementId 
          ? { ...el, width, height, updatedAt: new Date().toISOString() }
          : el
      );
      addToHistory(newElements);
      return newElements;
    });
    
    if (selectedElement?.id === elementId) {
      setSelectedElement(prev => ({ ...prev, width, height }));
    }
  }, [selectedElement, addToHistory]);

  /**
   * Element kopyala
   */
  const copyElement = useCallback((elementId) => {
    const element = canvasElements.find(el => el.id === elementId);
    if (element) {
      setClipboard(element);
    }
  }, [canvasElements]);

  /**
   * Element yapıştır
   */
  const pasteElement = useCallback(() => {
    if (clipboard) {
      const newElement = {
        ...clipboard,
        id: nextIdRef.current.toString(),
        x: clipboard.x + 20,
        y: clipboard.y + 20,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      nextIdRef.current += 1;
      addElement(newElement);
    }
  }, [clipboard, addElement]);

  /**
   * Elementleri hizala
   */
  const alignElements = useCallback((elementIds, alignment) => {
    if (elementIds.length < 2) return;
    
    const elements = canvasElements.filter(el => elementIds.includes(el.id));
    let updates = {};
    
    switch (alignment) {
      case 'left':
        const leftMost = Math.min(...elements.map(el => el.x));
        updates = elements.reduce((acc, el) => {
          acc[el.id] = { x: leftMost };
          return acc;
        }, {});
        break;
        
      case 'right':
        const rightMost = Math.max(...elements.map(el => el.x + el.width));
        updates = elements.reduce((acc, el) => {
          acc[el.id] = { x: rightMost - el.width };
          return acc;
        }, {});
        break;
        
      case 'top':
        const topMost = Math.min(...elements.map(el => el.y));
        updates = elements.reduce((acc, el) => {
          acc[el.id] = { y: topMost };
          return acc;
        }, {});
        break;
        
      case 'bottom':
        const bottomMost = Math.max(...elements.map(el => el.y + el.height));
        updates = elements.reduce((acc, el) => {
          acc[el.id] = { y: bottomMost - el.height };
          return acc;
        }, {});
        break;
        
      case 'center-horizontal':
        const centerX = elements.reduce((sum, el) => sum + el.x + el.width / 2, 0) / elements.length;
        updates = elements.reduce((acc, el) => {
          acc[el.id] = { x: centerX - el.width / 2 };
          return acc;
        }, {});
        break;
        
      case 'center-vertical':
        const centerY = elements.reduce((sum, el) => sum + el.y + el.height / 2, 0) / elements.length;
        updates = elements.reduce((acc, el) => {
          acc[el.id] = { y: centerY - el.height / 2 };
          return acc;
        }, {});
        break;
    }
    
    setCanvasElements(prev => {
      const newElements = prev.map(el => 
        updates[el.id] 
          ? { ...el, ...updates[el.id], updatedAt: new Date().toISOString() }
          : el
      );
      addToHistory(newElements);
      return newElements;
    });
  }, [canvasElements, addToHistory]);

  /**
   * Geri al
   */
  const undo = useCallback(() => {
    if (historyIndex > 0) {
      setHistoryIndex(prev => prev - 1);
      setCanvasElements(history[historyIndex - 1] || []);
    }
  }, [history, historyIndex]);

  /**
   * Yinele
   */
  const redo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(prev => prev + 1);
      setCanvasElements(history[historyIndex + 1] || []);
    }
  }, [history, historyIndex]);

  /**
   * Canvas'ı temizle
   */
  const clearCanvas = useCallback(() => {
    setCanvasElements([]);
    setSelectedElement(null);
    addToHistory([]);
  }, [addToHistory]);

  /**
   * Tüm elementleri seç
   */
  const selectAllElements = useCallback(() => {
    setCanvasElements(prev => 
      prev.map(el => ({ ...el, selected: true }))
    );
  }, []);

  /**
   * Seçimi kaldır
   */
  const clearSelection = useCallback(() => {
    setCanvasElements(prev => 
      prev.map(el => ({ ...el, selected: false }))
    );
    setSelectedElement(null);
  }, []);

  /**
   * Z-index yönetimi
   */
  const bringToFront = useCallback((elementId) => {
    const maxZIndex = Math.max(...canvasElements.map(el => el.zIndex || 0));
    updateElement(elementId, { zIndex: maxZIndex + 1 });
  }, [canvasElements, updateElement]);

  const sendToBack = useCallback((elementId) => {
    const minZIndex = Math.min(...canvasElements.map(el => el.zIndex || 0));
    updateElement(elementId, { zIndex: minZIndex - 1 });
  }, [canvasElements, updateElement]);

  // Can undo/redo state
  const canUndo = historyIndex > 0;
  const canRedo = historyIndex < history.length - 1;

  return {
    canvasElements,
    selectedElement,
    addElement,
    removeElement,
    updateElement,
    moveElement,
    resizeElement,
    copyElement,
    pasteElement,
    alignElements,
    undo,
    redo,
    clearCanvas,
    selectAllElements,
    clearSelection,
    bringToFront,
    sendToBack,
    canUndo,
    canRedo,
    clipboard,
  };
};
