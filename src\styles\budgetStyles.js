import { StyleSheet, Dimensions } from 'react-native';

const { width } = Dimensions.get('window');

/**
 * Enhanced Budget Styles
 * Modern design, animations, spacing ve typography iyileştirmeleri
 */
export const createBudgetStyles = (theme) => StyleSheet.create({
  // Container Styles
  container: {
    flex: 1,
    backgroundColor: theme.BACKGROUND,
  },
  
  // Card Styles - Modern Design
  modernCard: {
    backgroundColor: theme.SURFACE,
    borderRadius: 16,
    padding: 20,
    margin: 16,
    shadowColor: theme.SHADOW,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
    borderWidth: 1,
    borderColor: theme.BORDER + '20',
  },

  // Enhanced Card with Gradient Effect
  gradientCard: {
    backgroundColor: theme.SURFACE,
    borderRadius: 20,
    padding: 24,
    margin: 16,
    shadowColor: theme.PRIMARY,
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 12,
    borderWidth: 2,
    borderColor: theme.PRIMARY + '10',
  },

  // Typography - Enhanced
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: theme.TEXT_PRIMARY,
    letterSpacing: -0.5,
    lineHeight: 34,
  },

  subtitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.TEXT_SECONDARY,
    letterSpacing: -0.2,
    lineHeight: 24,
    marginTop: 4,
  },

  bodyText: {
    fontSize: 16,
    fontWeight: '400',
    color: theme.TEXT_PRIMARY,
    lineHeight: 24,
    letterSpacing: 0.1,
  },

  caption: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.TEXT_SECONDARY,
    lineHeight: 20,
    letterSpacing: 0.2,
  },

  // Button Styles - Modern
  primaryButton: {
    backgroundColor: theme.PRIMARY,
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: theme.PRIMARY,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
    gap: 8,
  },

  secondaryButton: {
    backgroundColor: theme.SURFACE,
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: theme.PRIMARY,
    gap: 8,
  },

  // Progress Styles - Enhanced
  progressContainer: {
    backgroundColor: theme.BACKGROUND,
    borderRadius: 12,
    padding: 4,
    marginVertical: 8,
  },

  progressBar: {
    height: 8,
    borderRadius: 6,
    backgroundColor: theme.PRIMARY,
    shadowColor: theme.PRIMARY,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 3,
  },

  // List Styles - Modern
  listItem: {
    backgroundColor: theme.SURFACE,
    borderRadius: 12,
    padding: 16,
    marginVertical: 6,
    marginHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: theme.SHADOW,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 4,
    borderWidth: 1,
    borderColor: theme.BORDER + '15',
  },

  // Input Styles - Enhanced
  modernInput: {
    backgroundColor: theme.SURFACE,
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 20,
    fontSize: 16,
    color: theme.TEXT_PRIMARY,
    borderWidth: 2,
    borderColor: theme.BORDER,
    shadowColor: theme.SHADOW,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },

  focusedInput: {
    borderColor: theme.PRIMARY,
    shadowColor: theme.PRIMARY,
    shadowOpacity: 0.2,
  },

  // Badge Styles - Modern
  badge: {
    backgroundColor: theme.PRIMARY + '20',
    borderRadius: 20,
    paddingVertical: 6,
    paddingHorizontal: 12,
    alignSelf: 'flex-start',
  },

  badgeText: {
    fontSize: 12,
    fontWeight: '600',
    color: theme.PRIMARY,
    letterSpacing: 0.5,
    textTransform: 'uppercase',
  },

  // Status Badges
  successBadge: {
    backgroundColor: theme.SUCCESS + '20',
    borderColor: theme.SUCCESS + '40',
    borderWidth: 1,
  },

  warningBadge: {
    backgroundColor: theme.WARNING + '20',
    borderColor: theme.WARNING + '40',
    borderWidth: 1,
  },

  dangerBadge: {
    backgroundColor: theme.DANGER + '20',
    borderColor: theme.DANGER + '40',
    borderWidth: 1,
  },

  // Spacing System
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48,
  },

  // Layout Helpers
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  spaceBetween: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  center: {
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Animation Styles
  fadeIn: {
    opacity: 1,
  },

  slideUp: {
    transform: [{ translateY: 0 }],
  },

  // Responsive Design
  responsive: {
    width: width > 768 ? '50%' : '100%',
    alignSelf: 'center',
  },

  // Chart Container - Enhanced
  chartContainer: {
    backgroundColor: theme.SURFACE,
    borderRadius: 16,
    padding: 20,
    margin: 16,
    shadowColor: theme.SHADOW,
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },

  // Header Styles - Modern
  headerContainer: {
    backgroundColor: theme.SURFACE,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    paddingTop: 20,
    paddingBottom: 24,
    paddingHorizontal: 20,
    shadowColor: theme.SHADOW,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 6,
  },

  // Tab Styles - Enhanced
  tabContainer: {
    backgroundColor: theme.SURFACE,
    borderRadius: 12,
    padding: 4,
    margin: 16,
    flexDirection: 'row',
    shadowColor: theme.SHADOW,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.08,
    shadowRadius: 6,
    elevation: 4,
  },

  tab: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },

  activeTab: {
    backgroundColor: theme.PRIMARY,
    shadowColor: theme.PRIMARY,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 3,
  },

  // Modal Styles - Enhanced
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },

  modalContent: {
    backgroundColor: theme.SURFACE,
    borderRadius: 20,
    padding: 24,
    margin: 20,
    maxHeight: '80%',
    width: '90%',
    shadowColor: theme.SHADOW,
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 15,
  },

  // Floating Action Button
  fab: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: theme.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: theme.PRIMARY,
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.4,
    shadowRadius: 12,
    elevation: 10,
  },

  // Divider
  divider: {
    height: 1,
    backgroundColor: theme.BORDER + '30',
    marginVertical: 16,
  },

  // Loading States
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.BACKGROUND,
  },

  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: theme.TEXT_SECONDARY,
    fontWeight: '500',
  },
});

// Animation Presets
export const animationPresets = {
  fadeIn: {
    from: { opacity: 0 },
    to: { opacity: 1 },
  },
  slideUp: {
    from: { transform: [{ translateY: 50 }] },
    to: { transform: [{ translateY: 0 }] },
  },
  scale: {
    from: { transform: [{ scale: 0.9 }] },
    to: { transform: [{ scale: 1 }] },
  },
};

// Color Utilities
export const colorUtils = {
  addOpacity: (color, opacity) => `${color}${Math.round(opacity * 255).toString(16).padStart(2, '0')}`,
  
  getStatusColor: (theme, status) => {
    switch (status) {
      case 'success': return theme.SUCCESS;
      case 'warning': return theme.WARNING;
      case 'danger': return theme.DANGER;
      case 'info': return theme.INFO;
      default: return theme.TEXT_SECONDARY;
    }
  },
};
