import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Animated,
  StatusBar
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';

/**
 * Modern özellikler ekranı - tema uyumlu, kategorizli liste tasarımı
 * 
 * @param {Object} props - Component props
 * @param {Object} props.navigation - Navigation objesi
 * @returns {JSX.Element} Modern features screen
 */
const ModernFeaturesScreen = ({ navigation }) => {
  const insets = useSafeAreaInsets();
  const { theme, isDarkMode } = useTheme();
  const [animatedValue] = useState(new Animated.Value(0));

  // Mount animasyonu
  useEffect(() => {
    Animated.timing(animatedValue, {
      toValue: 1,
      duration: 600,
      useNativeDriver: true,
    }).start();
  }, []);

  // Özellik kategorileri
  const featureCategories = [
    {
      id: 'main',
      title: '<PERSON>',
      features: [
        {
          id: 'statistics',
          title: 'İstatistikler',
          description: 'Gelir-gider analizi ve grafikler',
          icon: 'analytics',
          route: 'Statistics'
        },
        {
          id: 'reports',
          title: 'Raporlar',
          description: 'Detaylı finansal raporlar',
          icon: 'assessment',
          route: 'Reports'
        },
        {
          id: 'budget',
          title: 'Bütçe Yönetimi',
          description: 'Aylık bütçe planlama ve takip',
          icon: 'account-balance-wallet',
          route: 'Budgets'
        }
      ]
    },
    {
      id: 'tracking',
      title: 'Gelir & Takip',
      features: [
        {
          id: 'salary',
          title: 'Maaş Takibi',
          description: 'Maaş ödemeleri ve bordro yönetimi',
          icon: 'payments',
          route: 'Salaries'
        },
        {
          id: 'overtime',
          title: 'Mesai Takibi',
          description: 'Mesai saatleri ve ek ödeme hesaplama',
          icon: 'schedule',
          route: 'Overtime'
        },
        {
          id: 'investment',
          title: 'Yatırım Takibi',
          description: 'Hisse, döviz ve altın yatırımları',
          icon: 'trending-up',
          route: 'Investment'
        }
      ]
    },
    {
      id: 'tools',
      title: 'Araçlar',
      features: [
        {
          id: 'currency',
          title: 'Döviz Çevirici',
          description: 'Güncel kurlarla para birimi çevirisi',
          icon: 'currency-exchange',
          route: 'CurrencyConverter'
        },
        {
          id: 'shopping',
          title: 'Alışveriş Listesi',
          description: 'Planlı alışveriş ve harcama kontrolü',
          icon: 'shopping-cart',
          route: 'Shopping'
        },
        {
          id: 'reminders',
          title: 'Hatırlatıcılar',
          description: 'Fatura ve ödeme hatırlatmaları',
          icon: 'alarm',
          route: 'Reminders'
        }
      ]
    },
    {
      id: 'management',
      title: 'Yönetim',
      features: [
        {
          id: 'categories',
          title: 'Kategori Yönetimi',
          description: 'Gelir-gider kategorilerini düzenle',
          icon: 'category',
          route: 'Categories'
        },
        {
          id: 'security',
          title: 'Güvenlik Ayarları',
          description: 'PIN, biyometrik ve gizlilik ayarları',
          icon: 'security',
          route: 'SecuritySettings'
        },
        {
          id: 'notifications',
          title: 'Bildirim Ayarları',
          description: 'Uyarı ve bildirim tercihleri',
          icon: 'notifications',
          route: 'NotificationSettings'
        }
      ]
    }
  ];

  /**
   * Özelliğe navigation handler
   * @param {string} route - Navigasyon rotası
   */
  const navigateToFeature = (route) => {
    try {
      navigation.navigate(route);
    } catch (error) {
      console.warn('Navigation error:', error);
      // Fallback - parent navigator'a git
      navigation.getParent()?.navigate(route);
    }
  };

  /**
   * Kategori başlığı component'i
   */
  const CategoryHeader = ({ title, delay = 0 }) => (
    <Animated.View
      style={[
        styles.categoryHeader,
        {
          opacity: animatedValue,
          transform: [
            {
              translateY: animatedValue.interpolate({
                inputRange: [0, 1],
                outputRange: [20, 0],
              }),
            },
          ],
        },
      ]}
    >
      <View style={[styles.categoryLine, { backgroundColor: theme.BORDER || theme.colors?.border || '#e0e0e0' }]} />
      <Text style={[styles.categoryTitle, { color: theme.TEXT_SECONDARY || theme.colors?.textSecondary || '#666' }]}>
        {title}
      </Text>
      <View style={[styles.categoryLine, { backgroundColor: theme.BORDER || theme.colors?.border || '#e0e0e0' }]} />
    </Animated.View>
  );

  /**
   * Özellik öğesi component'i
   */
  const FeatureItem = ({ feature, delay = 0 }) => (
    <Animated.View
      style={{
        opacity: animatedValue,
        transform: [
          {
            translateY: animatedValue.interpolate({
              inputRange: [0, 1],
              outputRange: [30, 0],
            }),
          },
        ],
      }}
    >
      <TouchableOpacity
        style={[
          styles.featureItem,
          { 
            backgroundColor: theme.SURFACE || theme.colors?.surface || '#f9f9f9',
            borderBottomColor: theme.BORDER || theme.colors?.border || '#e0e0e0'
          }
        ]}
        onPress={() => navigateToFeature(feature.route)}
        activeOpacity={0.7}
      >
        <View style={styles.featureContent}>
          <View style={[styles.featureIcon, { backgroundColor: (theme.PRIMARY || theme.colors?.primary || '#007AFF') + '15' }]}>
            <MaterialIcons
              name={feature.icon}
              size={24}
              color={theme.PRIMARY || theme.colors?.primary || '#007AFF'}
            />
          </View>
          <View style={styles.featureTextContainer}>
            <Text style={[styles.featureTitle, { color: theme.TEXT_PRIMARY || theme.colors?.text || '#333' }]}>
              {feature.title}
            </Text>
            <Text style={[styles.featureDescription, { color: theme.TEXT_SECONDARY || theme.colors?.textSecondary || '#666' }]}>
              {feature.description}
            </Text>
          </View>
          <MaterialIcons
            name="chevron-right"
            size={20}
            color={theme.TEXT_SECONDARY || theme.colors?.textSecondary || '#666'}
          />
        </View>
      </TouchableOpacity>
    </Animated.View>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.BACKGROUND || theme.colors?.background || '#fff' }]}>
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor={theme.PRIMARY || theme.colors?.primary || '#007AFF'}
      />
      
      {/* Modern Header */}
      <View style={[
        styles.header,
        { 
          paddingTop: insets.top + 10,
          backgroundColor: theme.PRIMARY || theme.colors?.primary || '#007AFF'
        }
      ]}>
        <View style={styles.headerContent}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
            activeOpacity={0.7}
          >
            <MaterialIcons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
              <Text style={styles.headerTitle}>Özellikler</Text>
        
        <View style={styles.placeholder} />
        </View>
      </View>

      {/* Features List */}
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {featureCategories.map((category, categoryIndex) => (
          <View key={category.id} style={styles.categoryContainer}>
            <CategoryHeader title={category.title} delay={categoryIndex * 100} />
            
            <View style={[styles.featuresContainer, { backgroundColor: theme.SURFACE || theme.colors?.surface || '#f9f9f9' }]}>
              {category.features.map((feature, featureIndex) => (
                <FeatureItem
                  key={feature.id}
                  feature={feature}
                  delay={(categoryIndex * 100) + (featureIndex * 50)}
                />
              ))}
            </View>
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    fontSize: 20,
    fontWeight: '700',
    color: '#fff',
    marginHorizontal: 20,
  },
  headerRight: {
    width: 44,
  },
  themeButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 32,
  },
  categoryContainer: {
    marginBottom: 24,
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    paddingHorizontal: 4,
  },
  categoryLine: {
    flex: 1,
    height: 1,
  },
  categoryTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginHorizontal: 16,
    textTransform: 'uppercase',
    letterSpacing: 1,
  },
  featuresContainer: {
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  featureItem: {
    borderBottomWidth: 1,
  },
  featureContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  featureIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  featureTextContainer: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 12,
    lineHeight: 16,
  },
});

export default ModernFeaturesScreen;
