/**
 * Bütçe servisi
 * Bütçe işlemlerini yönetir
 */

/**
 * Bütçe ile ilgili işlemleri getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} budgetId - Bütçe ID'si
 * @returns {Promise<Array>} İşlemler
 */
export const getBudgetTransactions = async (db, budgetId) => {
  try {
    // Bütçe bilgilerini getir
    const budget = await db.getFirstAsync(`
      SELECT * FROM budgets WHERE id = ?
    `, [budgetId]);

    if (!budget) {
      throw new Error('Bütçe bulunamadı');
    }

    // Bütçe kategorilerini getir
    const categories = await db.getAllAsync(`
      SELECT category_id FROM budget_categories WHERE budget_id = ?
    `, [budgetId]);

    if (categories.length === 0) {
      return [];
    }

    // Kategori ID'lerini bir diziye dönüştür
    const categoryIds = categories.map(c => c.category_id);

    // Kategori ID'leri için parametre yer tutucuları oluştur
    const placeholders = categoryIds.map(() => '?').join(',');

    // İşlemleri getir
    return await db.getAllAsync(`
      SELECT t.*, c.name as category_name, c.icon as category_icon, c.color as category_color
      FROM transactions t
      JOIN categories c ON t.category_id = c.id
      WHERE t.category_id IN (${placeholders})
        AND t.type = 'expense'
        AND t.date >= ?
        AND (? IS NULL OR t.date <= ?)
      ORDER BY t.date DESC
    `, [...categoryIds, budget.start_date, budget.end_date, budget.end_date]);
  } catch (error) {
    console.error('Bütçe işlemleri getirme hatası:', error);
    throw error;
  }
};

/**
 * Bütçeleri getirir
 *
 * @param {Object} options - Sorgu seçenekleri
 * @param {boolean} options.activeOnly - Sadece aktif bütçeleri getir
 * @param {string} options.period - Bütçe periyodu (daily, weekly, monthly, yearly)
 * @param {string} options.date - Belirli bir tarihte aktif olan bütçeleri getir (YYYY-MM-DD)
 * @returns {Promise<Array>} Bütçeler
 */
export const getBudgets = async (db, options = {}) => {

  try {
    let query = `
      SELECT b.*,
             (SELECT SUM(amount) FROM budget_categories WHERE budget_id = b.id) as total_allocated,
             (SELECT COUNT(*) FROM budget_categories WHERE budget_id = b.id) as category_count
      FROM budgets b
      WHERE 1=1
    `;

    const params = [];

    if (options.activeOnly) {
      query += ' AND b.is_active = 1';
    }

    if (options.period) {
      query += ' AND b.period = ?';
      params.push(options.period);
    }

    if (options.date) {
      query += ' AND (b.start_date <= ? AND (b.end_date IS NULL OR b.end_date >= ?))';
      params.push(options.date, options.date);
    }

    query += ' ORDER BY b.start_date DESC, b.name';

    return await db.getAllAsync(query, params);
  } catch (error) {
    console.error('Bütçeleri getirme hatası:', error);
    throw error;
  }
};

/**
 * Bütçeyi ID'ye göre getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} budgetId - Bütçe ID'si
 * @returns {Promise<Object>} Bütçe
 */
export const getBudgetById = async (db, budgetId) => {
  try {
    return await db.getFirstAsync(`
      SELECT * FROM budgets WHERE id = ?
    `, [budgetId]);
  } catch (error) {
    console.error('Bütçe getirme hatası:', error);
    throw error;
  }
};

/**
 * Bütçe detaylarını getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} budgetId - Bütçe ID'si
 * @returns {Promise<Object>} Bütçe detayları
 */
export const getBudgetDetails = async (db, budgetId) => {

  try {
    // Bütçe bilgilerini getir
    const budget = await db.getFirstAsync(`
      SELECT * FROM budgets WHERE id = ?
    `, [budgetId]);

    if (!budget) {
      throw new Error('Bütçe bulunamadı');
    }

    // Bütçe kategorilerini getir
    const categories = await db.getAllAsync(`
      SELECT bc.*, c.name as category_name, c.icon, c.color, c.type as category_type,
             (SELECT SUM(amount) FROM transactions t
              WHERE t.category_id = bc.category_id
                AND t.type = 'expense'
                AND t.date >= ?
                AND (? IS NULL OR t.date <= ?)) as spent_amount
      FROM budget_categories bc
      JOIN categories c ON bc.category_id = c.id
      WHERE bc.budget_id = ?
      ORDER BY c.name
    `, [budget.start_date, budget.end_date, budget.end_date, budgetId]);

    // Toplam harcama ve kalan tutarları hesapla
    let totalBudget = 0;
    let totalSpent = 0;

    categories.forEach(category => {
      totalBudget += category.amount;
      totalSpent += category.spent_amount || 0;

      // Kategori bazında kalan tutarı hesapla
      category.remaining = category.amount - (category.spent_amount || 0);

      // Kategori bazında ilerleme yüzdesini hesapla
      category.progress = category.amount > 0 ? Math.min(((category.spent_amount || 0) / category.amount) * 100, 100) : 0;
    });

    // Genel ilerleme
    const totalRemaining = totalBudget - totalSpent;
    const totalProgress = totalBudget > 0 ? Math.min((totalSpent / totalBudget) * 100, 100) : 0;

    return {
      ...budget,
      categories,
      totalBudget,
      totalSpent,
      totalRemaining,
      totalProgress
    };
  } catch (error) {
    console.error('Bütçe detayları getirme hatası:', error);
    throw error;
  }
};

/**
 * Yeni bütçe oluşturur
 *
 * @param {Object} budget - Bütçe verileri
 * @param {Array} categories - Kategori verileri
 * @returns {Promise<number>} Oluşturulan bütçe ID'si
 */
export const createBudget = async (db, budget, categories) => {

  try {
    return await db.withTransactionAsync(async () => {
      // Bütçeyi ekle
      const result = await db.runAsync(`
        INSERT INTO budgets (name, amount, period, start_date, end_date, is_active, notes, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `, [
        budget.name,
        0, // amount alanı için varsayılan değer
        budget.period,
        budget.start_date,
        budget.end_date || null,
        budget.is_active !== undefined ? budget.is_active : 1,
        budget.notes || null
      ]);

      const budgetId = result.lastInsertRowId;

      // Kategorileri ekle
      for (const category of categories) {
        await db.runAsync(`
          INSERT INTO budget_categories (budget_id, category_id, amount)
          VALUES (?, ?, ?)
        `, [
          budgetId,
          category.category_id,
          category.amount
        ]);
      }

      return budgetId;
    });
  } catch (error) {
    console.error('Bütçe ekleme hatası:', error);
    throw error;
  }
};

/**
 * Bütçeyi günceller
 *
 * @param {number} budgetId - Bütçe ID'si
 * @param {Object} budget - Güncellenecek bütçe verileri
 * @param {Array} categories - Güncellenecek bütçe kategorileri
 * @returns {Promise<void>}
 */
export const updateBudget = async (db, budgetId, budget, categories = []) => {

  try {
    await db.withTransactionAsync(async () => {
      // Bütçeyi güncelle
      await db.runAsync(`
        UPDATE budgets
        SET name = ?, amount = ?, period = ?, start_date = ?, end_date = ?, is_active = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `, [
        budget.name,
        0, // amount alanı için varsayılan değer
        budget.period,
        budget.start_date,
        budget.end_date || null,
        budget.is_active !== undefined ? budget.is_active : 1,
        budget.notes || null,
        budgetId
      ]);

      // Mevcut kategorileri sil
      await db.runAsync(`
        DELETE FROM budget_categories
        WHERE budget_id = ?
      `, [budgetId]);

      // Yeni kategorileri ekle
      for (const category of categories) {
        await db.runAsync(`
          INSERT INTO budget_categories (budget_id, category_id, amount)
          VALUES (?, ?, ?)
        `, [
          budgetId,
          category.category_id,
          category.amount
        ]);
      }
    });
  } catch (error) {
    console.error('Bütçe güncelleme hatası:', error);
    throw error;
  }
};

/**
 * Bütçeyi siler
 *
 * @param {number} budgetId - Bütçe ID'si
 * @returns {Promise<void>}
 */
export const deleteBudget = async (db, budgetId) => {

  try {
    await db.withTransactionAsync(async () => {
      // Bütçe kategorilerini sil
      await db.runAsync(`
        DELETE FROM budget_categories
        WHERE budget_id = ?
      `, [budgetId]);

      // Bütçeyi sil
      await db.runAsync(`
        DELETE FROM budgets
        WHERE id = ?
      `, [budgetId]);
    });
  } catch (error) {
    console.error('Bütçe silme hatası:', error);
    throw error;
  }
};

/**
 * Bütçe kategorisi ekler
 *
 * @param {number} budgetId - Bütçe ID'si
 * @param {number} categoryId - Kategori ID'si
 * @param {number} amount - Tutar
 * @returns {Promise<number>} Eklenen bütçe kategorisinin ID'si
 */
export const addBudgetCategory = async (db, budgetId, categoryId, amount) => {

  try {
    const result = await db.runAsync(`
      INSERT INTO budget_categories (budget_id, category_id, amount)
      VALUES (?, ?, ?)
    `, [budgetId, categoryId, amount]);

    return result.lastInsertRowId;
  } catch (error) {
    console.error('Bütçe kategorisi ekleme hatası:', error);
    throw error;
  }
};

/**
 * Bütçe kategorisini günceller
 *
 * @param {number} budgetId - Bütçe ID'si
 * @param {number} categoryId - Kategori ID'si
 * @param {number} amount - Tutar
 * @returns {Promise<void>}
 */
export const updateBudgetCategory = async (db, budgetId, categoryId, amount) => {

  try {
    await db.runAsync(`
      UPDATE budget_categories
      SET amount = ?, updated_at = CURRENT_TIMESTAMP
      WHERE budget_id = ? AND category_id = ?
    `, [amount, budgetId, categoryId]);
  } catch (error) {
    console.error('Bütçe kategorisi güncelleme hatası:', error);
    throw error;
  }
};

/**
 * Bütçe kategorisini siler
 *
 * @param {number} budgetId - Bütçe ID'si
 * @param {number} categoryId - Kategori ID'si
 * @returns {Promise<void>}
 */
export const deleteBudgetCategory = async (db, budgetId, categoryId) => {

  try {
    await db.runAsync(`
      DELETE FROM budget_categories
      WHERE budget_id = ? AND category_id = ?
    `, [budgetId, categoryId]);
  } catch (error) {
    console.error('Bütçe kategorisi silme hatası:', error);
    throw error;
  }
};

/**
 * Aktif bütçe durumunu getirir
 *
 * @returns {Promise<Object>} Aktif bütçe durumu
 */
export const getCurrentBudgetStatus = async (db) => {

  try {
    const today = new Date().toISOString().split('T')[0];

    // Aktif aylık bütçeyi bul
    const activeBudget = await db.getFirstAsync(`
      SELECT * FROM budgets
      WHERE period = 'monthly'
        AND start_date <= ?
        AND (end_date IS NULL OR end_date >= ?)
        AND is_active = 1
      ORDER BY start_date DESC
      LIMIT 1
    `, [today, today]);

    if (!activeBudget) {
      return null;
    }

    // Bütçe detaylarını getir
    return await getBudgetDetails(db, activeBudget.id);
  } catch (error) {
    console.error('Aktif bütçe durumu getirme hatası:', error);
    throw error;
  }
};

/**
 * Bütçe özetini getirir
 *
 * @returns {Promise<Object>} Bütçe özeti
 */
export const getBudgetSummary = async (db) => {

  try {
    const today = new Date().toISOString().split('T')[0];

    // Aktif bütçeleri getir
    const activeBudgets = await db.getAllAsync(`
      SELECT * FROM budgets
      WHERE start_date <= ?
        AND (end_date IS NULL OR end_date >= ?)
        AND is_active = 1
      ORDER BY period, start_date DESC
    `, [today, today]);

    // Her bütçe için özet bilgileri hesapla
    const budgetSummaries = [];

    for (const budget of activeBudgets) {
      // Toplam bütçe tutarını hesapla
      const totalBudget = await db.getFirstAsync(`
        SELECT SUM(amount) as total FROM budget_categories
        WHERE budget_id = ?
      `, [budget.id]);

      // Toplam harcamayı hesapla
      const totalSpent = await db.getFirstAsync(`
        SELECT SUM(t.amount) as total
        FROM transactions t
        JOIN budget_categories bc ON t.category_id = bc.category_id
        WHERE bc.budget_id = ?
          AND t.type = 'expense'
          AND t.date >= ?
          AND (? IS NULL OR t.date <= ?)
      `, [budget.id, budget.start_date, budget.end_date, budget.end_date]);

      // En çok harcama yapılan kategorileri getir
      const topCategories = await db.getAllAsync(`
        SELECT c.name as category_name, c.icon, c.color, SUM(t.amount) as spent
        FROM transactions t
        JOIN budget_categories bc ON t.category_id = bc.category_id
        JOIN categories c ON bc.category_id = c.id
        WHERE bc.budget_id = ?
          AND t.type = 'expense'
          AND t.date >= ?
          AND (? IS NULL OR t.date <= ?)
        GROUP BY bc.category_id
        ORDER BY spent DESC
        LIMIT 3
      `, [budget.id, budget.start_date, budget.end_date, budget.end_date]);

      // Bütçe özetini oluştur
      budgetSummaries.push({
        id: budget.id,
        name: budget.name,
        period: budget.period,
        start_date: budget.start_date,
        end_date: budget.end_date,
        total_budget: totalBudget.total || 0,
        total_spent: totalSpent.total || 0,
        remaining: (totalBudget.total || 0) - (totalSpent.total || 0),
        progress: totalBudget.total > 0 ? Math.min(((totalSpent.total || 0) / totalBudget.total) * 100, 100) : 0,
        top_categories: topCategories
      });
    }

    return budgetSummaries;
  } catch (error) {
    console.error('Bütçe özeti getirme hatası:', error);
    throw error;
  }
};
