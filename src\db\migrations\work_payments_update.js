/**
 * work_payments tablosunu güncelleyen migrasyon
 *
 * @param {object} db - SQLite veritabanı nesnesi
 * @returns {Promise<void>}
 */
export const migrateWorkPayments = async (db) => {
  try {
    // work_payments tablosunu kontrol et
    const hasWorkPaymentsTable = await db.getFirstAsync(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='work_payments'
    `);

    if (hasWorkPaymentsTable) {
      // Tabloda eksik sütunlar var mı kontrol et
      const columns = await db.getAllAsync(`PRAGMA table_info(work_payments)`);

      // Sütun adlarını bir diziye dönüştür
      const columnNames = columns.map(col => col.name);

      // Eski sütun adları
      const oldColumns = ['title', 'period_start', 'period_end', 'amount', 'total_hours'];
      
      // Yeni sütun adları
      const newColumns = [
        'period_start_date', 'period_end_date', 'regular_hours', 'overtime_hours', 
        'regular_amount', 'overtime_amount', 'total_amount'
      ];

      // Eski sütunlar varsa ve yeni sütunlar yoksa, tabloyu yeniden oluştur
      const hasOldColumns = oldColumns.some(col => columnNames.includes(col));
      const hasNewColumns = newColumns.every(col => columnNames.includes(col));

      if (hasOldColumns && !hasNewColumns) {
        // Eski verileri yedekle
        const oldPayments = await db.getAllAsync(`SELECT * FROM work_payments`);
        
        // Tabloyu düşür
        await db.execAsync(`DROP TABLE work_payments`);
        
        // Yeni tabloyu oluştur
        await db.execAsync(`
          CREATE TABLE work_payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            period_start_date TEXT NOT NULL,
            period_end_date TEXT NOT NULL,
            regular_hours REAL NOT NULL DEFAULT 0,
            overtime_hours REAL NOT NULL DEFAULT 0,
            regular_amount REAL NOT NULL DEFAULT 0,
            overtime_amount REAL NOT NULL DEFAULT 0,
            total_amount REAL NOT NULL DEFAULT 0,
            payment_date TEXT,
            is_paid INTEGER DEFAULT 0,
            notes TEXT,
            created_at TEXT DEFAULT '2023-01-01 00:00:00',
            updated_at TEXT DEFAULT '2023-01-01 00:00:00'
          )
        `);
        
        // Eski verileri yeni tabloya aktar
        for (const payment of oldPayments) {
          try {
            await db.runAsync(`
              INSERT INTO work_payments (
                period_start_date, period_end_date, regular_hours,
                total_amount, payment_date, is_paid, notes
              ) VALUES (?, ?, ?, ?, ?, ?, ?)
            `, [
              payment.period_start || '2023-01-01',
              payment.period_end || '2023-01-31',
              payment.total_hours || 0,
              payment.amount || 0,
              payment.payment_date,
              payment.is_paid || 0,
              payment.notes
            ]);
          } catch (insertError) {
            console.error('Ödeme verisi aktarma hatası:', insertError);
          }
        }
      } else if (!hasNewColumns) {
        // Yeni sütunları ekle
        for (const column of newColumns) {
          if (!columnNames.includes(column)) {
            let columnType = 'TEXT';
            if (column.includes('hours') || column.includes('amount')) {
              columnType = 'REAL NOT NULL DEFAULT 0';
            }
            
            await db.execAsync(`ALTER TABLE work_payments ADD COLUMN ${column} ${columnType}`);
          }
        }
      }
    } else {
      // Yeni work_payments tablosunu oluştur
      await db.execAsync(`
        CREATE TABLE work_payments (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          period_start_date TEXT NOT NULL,
          period_end_date TEXT NOT NULL,
          regular_hours REAL NOT NULL DEFAULT 0,
          overtime_hours REAL NOT NULL DEFAULT 0,
          regular_amount REAL NOT NULL DEFAULT 0,
          overtime_amount REAL NOT NULL DEFAULT 0,
          total_amount REAL NOT NULL DEFAULT 0,
          payment_date TEXT,
          is_paid INTEGER DEFAULT 0,
          notes TEXT,
          created_at TEXT DEFAULT '2023-01-01 00:00:00',
          updated_at TEXT DEFAULT '2023-01-01 00:00:00'
        )
      `);
    }
  } catch (error) {
    console.error('work_payments tablosu güncelleme migrasyon hatası:', error);
    throw error;
  }
};
