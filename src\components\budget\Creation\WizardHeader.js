/**
 * Wizard Header Komponenti
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md - Stage 2, Phase 4
 * 
 * Bütçe oluşturma wizard'ının header'ı
 * Maksimum 150 satır - <PERSON><PERSON> so<PERSON>luk prensibi
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../../../context/ThemeContext';

/**
 * Wizard header komponenti
 * @param {Object} props - Component props
 * @param {number} props.currentStep - Mevcut adım (0-based)
 * @param {number} props.totalSteps - Toplam adım sayısı
 * @param {string} props.stepTitle - Mevcut adım başlığı
 * @param {Function} props.onClose - Kapatma callback fonksiyonu
 * @param {Object} props.theme - <PERSON><PERSON> objesi (opsiyonel, context'ten alınır)
 */
const WizardHeader = ({ 
  currentStep, 
  totalSteps, 
  stepTitle, 
  onClose,
  theme: propTheme 
}) => {
  const { theme } = useTheme();
  const currentTheme = propTheme || theme;

  // İlerleme yüzdesi
  const progressPercentage = ((currentStep + 1) / totalSteps) * 100;

  return (
    <View style={[styles.container, { backgroundColor: currentTheme.SURFACE }]}>
      {/* Header content */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.closeButton}
          onPress={onClose}
        >
          <MaterialIcons name="close" size={24} color={currentTheme.TEXT_PRIMARY} />
        </TouchableOpacity>

        <View style={styles.titleSection}>
          <Text style={[styles.title, { color: currentTheme.TEXT_PRIMARY }]}>
            Yeni Bütçe
          </Text>
          <Text style={[styles.stepTitle, { color: currentTheme.TEXT_SECONDARY }]}>
            {stepTitle}
          </Text>
        </View>

        <View style={styles.stepCounter}>
          <Text style={[styles.stepText, { color: currentTheme.TEXT_SECONDARY }]}>
            {currentStep + 1}/{totalSteps}
          </Text>
        </View>
      </View>

      {/* Progress bar */}
      <View style={styles.progressContainer}>
        <View style={[styles.progressTrack, { backgroundColor: currentTheme.BORDER }]}>
          <View 
            style={[
              styles.progressFill,
              { 
                backgroundColor: currentTheme.PRIMARY,
                width: `${progressPercentage}%`
              }
            ]} 
          />
        </View>
      </View>

      {/* Step indicators */}
      <View style={styles.stepIndicators}>
        {Array.from({ length: totalSteps }, (_, index) => (
          <View
            key={index}
            style={[
              styles.stepDot,
              {
                backgroundColor: index <= currentStep 
                  ? currentTheme.PRIMARY 
                  : currentTheme.BORDER
              }
            ]}
          />
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingTop: 8,
    paddingBottom: 16,
    paddingHorizontal: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  closeButton: {
    padding: 4,
  },
  titleSection: {
    flex: 1,
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  stepTitle: {
    fontSize: 14,
    marginTop: 2,
  },
  stepCounter: {
    minWidth: 40,
    alignItems: 'flex-end',
  },
  stepText: {
    fontSize: 14,
    fontWeight: '500',
  },
  progressContainer: {
    marginBottom: 12,
  },
  progressTrack: {
    height: 4,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  stepIndicators: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
  },
  stepDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
});

export default WizardHeader;
