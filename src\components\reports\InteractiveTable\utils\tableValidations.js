/**
 * Tablo doğrulama yardımcı fonksiyonları
 */

/**
 * Veri tipini doğrula
 * @param {*} value - Doğrulanacak değer
 * @param {string} type - Beklenen tip
 * @returns {boolean} Doğrulama sonucu
 */
export const validateDataType = (value, type) => {
  if (value === null || value === undefined) return true; // Null değerlere izin ver

  switch (type) {
    case 'text':
      return typeof value === 'string' || typeof value === 'number';
    case 'number':
      return !isNaN(parseFloat(value)) && isFinite(value);
    case 'currency':
      return !isNaN(parseFloat(value)) && isFinite(value) && parseFloat(value) >= 0;
    case 'date':
      return !isNaN(new Date(value).getTime());
    case 'boolean':
      return typeof value === 'boolean' || value === 'true' || value === 'false' || value === 1 || value === 0;
    case 'email':
      return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
    case 'url':
      try {
        new URL(value);
        return true;
      } catch {
        return false;
      }
    case 'phone':
      return /^[\+]?[0-9\s\-\(\)]+$/.test(value);
    default:
      return true;
  }
};

/**
 * Sütun yapılandırmasını doğrula
 * @param {Object} column - Sütun objesi
 * @returns {Object} Doğrulama sonucu
 */
export const validateColumn = (column) => {
  const errors = [];
  const warnings = [];

  // Zorunlu alanlar
  if (!column.id) {
    errors.push('Sütun ID\'si zorunludur');
  }
  if (!column.name) {
    errors.push('Sütun adı zorunludur');
  }

  // ID format kontrolü
  if (column.id && !/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(column.id)) {
    errors.push('Sütun ID\'si geçersiz format (sadece harf, rakam ve _ kullanın)');
  }

  // Veri tipi kontrolü
  const validTypes = ['text', 'number', 'currency', 'date', 'boolean', 'email', 'url', 'phone'];
  if (column.type && !validTypes.includes(column.type)) {
    errors.push(`Geçersiz veri tipi: ${column.type}`);
  }

  // Genişlik kontrolleri
  if (column.width && (typeof column.width !== 'number' || column.width < 0)) {
    errors.push('Sütun genişliği pozitif sayı olmalıdır');
  }
  if (column.minWidth && (typeof column.minWidth !== 'number' || column.minWidth < 0)) {
    errors.push('Minimum genişlik pozitif sayı olmalıdır');
  }
  if (column.maxWidth && (typeof column.maxWidth !== 'number' || column.maxWidth < 0)) {
    errors.push('Maksimum genişlik pozitif sayı olmalıdır');
  }
  if (column.minWidth && column.maxWidth && column.minWidth > column.maxWidth) {
    errors.push('Minimum genişlik, maksimum genişlikten büyük olamaz');
  }

  // Hizalama kontrolü
  const validAlignments = ['left', 'center', 'right'];
  if (column.align && !validAlignments.includes(column.align)) {
    errors.push(`Geçersiz hizalama: ${column.align}`);
  }

  // Uyarılar
  if (column.name && column.name.length > 50) {
    warnings.push('Sütun adı çok uzun, görüntülemede sorun olabilir');
  }
  if (column.width && column.width > 500) {
    warnings.push('Sütun genişliği çok büyük, mobil cihazlarda sorun olabilir');
  }
  if (column.width && column.width < 50) {
    warnings.push('Sütun genişliği çok küçük, içerik görüntülenemeyebilir');
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * Filtre yapılandırmasını doğrula
 * @param {Object} filter - Filtre objesi
 * @param {Array} columns - Sütun listesi
 * @returns {Object} Doğrulama sonucu
 */
export const validateFilter = (filter, columns = []) => {
  const errors = [];
  const warnings = [];

  // Zorunlu alanlar
  if (!filter.column) {
    errors.push('Filtre sütunu zorunludur');
  }
  if (!filter.operator) {
    errors.push('Filtre operatörü zorunludur');
  }
  if (filter.value === null || filter.value === undefined || filter.value === '') {
    errors.push('Filtre değeri zorunludur');
  }

  // Sütun varlığı kontrolü
  if (filter.column && !columns.find(col => col.id === filter.column)) {
    errors.push('Belirtilen sütun bulunamadı');
  }

  // Operatör kontrolü
  const column = columns.find(col => col.id === filter.column);
  if (column && filter.operator) {
    const validOperators = getValidOperators(column.type);
    if (!validOperators.includes(filter.operator)) {
      errors.push(`${column.type} tipi için geçersiz operatör: ${filter.operator}`);
    }
  }

  // Veri tipi uyumluluğu
  if (column && filter.value !== null && filter.value !== undefined) {
    if (!validateDataType(filter.value, column.type)) {
      errors.push(`Filtre değeri ${column.type} tipine uygun değil`);
    }
  }

  // Özel validasyonlar
  if (filter.operator === 'between' && !Array.isArray(filter.value)) {
    errors.push('Between operatörü için değer array olmalıdır');
  }
  if (filter.operator === 'between' && Array.isArray(filter.value) && filter.value.length !== 2) {
    errors.push('Between operatörü için tam 2 değer gereklidir');
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * Veri tipine göre geçerli operatörleri getir
 * @param {string} type - Veri tipi
 * @returns {Array} Geçerli operatörler
 */
export const getValidOperators = (type) => {
  const operators = {
    text: ['equals', 'notEquals', 'contains', 'notContains', 'startsWith', 'endsWith', 'isEmpty', 'isNotEmpty'],
    number: ['equals', 'notEquals', 'greaterThan', 'lessThan', 'greaterThanOrEqual', 'lessThanOrEqual', 'between'],
    currency: ['equals', 'notEquals', 'greaterThan', 'lessThan', 'greaterThanOrEqual', 'lessThanOrEqual', 'between'],
    date: ['equals', 'notEquals', 'after', 'before', 'between', 'thisWeek', 'thisMonth', 'thisYear'],
    boolean: ['equals', 'notEquals', 'isTrue', 'isFalse'],
    email: ['equals', 'notEquals', 'contains', 'notContains', 'isEmpty', 'isNotEmpty'],
    url: ['equals', 'notEquals', 'contains', 'notContains', 'isEmpty', 'isNotEmpty'],
    phone: ['equals', 'notEquals', 'contains', 'notContains', 'isEmpty', 'isNotEmpty'],
  };

  return operators[type] || operators.text;
};

/**
 * Tablo verilerini doğrula
 * @param {Array} data - Veri dizisi
 * @param {Array} columns - Sütun dizisi
 * @returns {Object} Doğrulama sonucu
 */
export const validateTableData = (data, columns) => {
  const errors = [];
  const warnings = [];
  const statistics = {
    totalRows: data.length,
    validRows: 0,
    invalidRows: 0,
    columnErrors: {},
    typeErrors: {},
  };

  if (!Array.isArray(data)) {
    errors.push('Veri array olmalıdır');
    return { valid: false, errors, warnings, statistics };
  }

  if (!Array.isArray(columns)) {
    errors.push('Sütunlar array olmalıdır');
    return { valid: false, errors, warnings, statistics };
  }

  // Her satırı doğrula
  data.forEach((row, rowIndex) => {
    let rowValid = true;

    if (typeof row !== 'object' || row === null) {
      errors.push(`Satır ${rowIndex + 1}: Geçersiz veri formatı`);
      rowValid = false;
    } else {
      // Her sütunu doğrula
      columns.forEach(column => {
        const value = row[column.id];
        
        if (!validateDataType(value, column.type)) {
          const errorKey = `${column.id}_${column.type}`;
          if (!statistics.columnErrors[column.id]) {
            statistics.columnErrors[column.id] = 0;
          }
          if (!statistics.typeErrors[errorKey]) {
            statistics.typeErrors[errorKey] = 0;
          }
          statistics.columnErrors[column.id]++;
          statistics.typeErrors[errorKey]++;
          
          errors.push(`Satır ${rowIndex + 1}, Sütun ${column.name}: ${column.type} tipi bekleniyor`);
          rowValid = false;
        }

        // Zorunlu alan kontrolü
        if (column.required && (value === null || value === undefined || value === '')) {
          errors.push(`Satır ${rowIndex + 1}, Sütun ${column.name}: Zorunlu alan boş olamaz`);
          rowValid = false;
        }

        // Benzersizlik kontrolü
        if (column.unique && value !== null && value !== undefined && value !== '') {
          const duplicateIndex = data.findIndex((otherRow, otherIndex) => 
            otherIndex !== rowIndex && otherRow[column.id] === value
          );
          if (duplicateIndex !== -1) {
            errors.push(`Satır ${rowIndex + 1}, Sütun ${column.name}: Değer benzersiz olmalıdır`);
            rowValid = false;
          }
        }
      });
    }

    if (rowValid) {
      statistics.validRows++;
    } else {
      statistics.invalidRows++;
    }
  });

  // Genel uyarılar
  if (data.length === 0) {
    warnings.push('Veri boş');
  }
  if (data.length > 10000) {
    warnings.push('Çok fazla veri, performans sorunları yaşanabilir');
  }

  // Sütun uyarıları
  columns.forEach(column => {
    if (statistics.columnErrors[column.id] > 0) {
      const errorRate = (statistics.columnErrors[column.id] / data.length) * 100;
      if (errorRate > 50) {
        warnings.push(`${column.name} sütununda %${errorRate.toFixed(1)} hata oranı var`);
      }
    }
  });

  return {
    valid: errors.length === 0,
    errors,
    warnings,
    statistics,
  };
};

/**
 * Sütun adı doğrulama
 * @param {string} name - Sütun adı
 * @param {Array} existingColumns - Mevcut sütunlar
 * @returns {Object} Doğrulama sonucu
 */
export const validateColumnName = (name, existingColumns = []) => {
  const errors = [];
  const warnings = [];

  if (!name || name.trim() === '') {
    errors.push('Sütun adı boş olamaz');
  }

  if (name && name.length > 100) {
    errors.push('Sütun adı çok uzun (maksimum 100 karakter)');
  }

  if (name && name.length < 2) {
    errors.push('Sütun adı çok kısa (minimum 2 karakter)');
  }

  // Özel karakter kontrolü
  if (name && /[<>\"'&]/.test(name)) {
    errors.push('Sütun adında geçersiz karakterler var');
  }

  // Benzersizlik kontrolü
  if (name && existingColumns.some(col => col.name === name)) {
    errors.push('Bu sütun adı zaten kullanılıyor');
  }

  // Uyarılar
  if (name && name.length > 30) {
    warnings.push('Sütun adı uzun, görüntülemede sorun olabilir');
  }

  if (name && /^\d/.test(name)) {
    warnings.push('Sütun adının rakamla başlaması önerilmez');
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * Filtre değeri doğrulama
 * @param {*} value - Filtre değeri
 * @param {string} operator - Operatör
 * @param {string} type - Veri tipi
 * @returns {Object} Doğrulama sonucu
 */
export const validateFilterValue = (value, operator, type) => {
  const errors = [];
  const warnings = [];

  if (value === null || value === undefined || value === '') {
    errors.push('Filtre değeri gereklidir');
    return { valid: false, errors, warnings };
  }

  // Operatöre göre özel kontroller
  switch (operator) {
    case 'between':
      if (!Array.isArray(value)) {
        errors.push('Between operatörü için değer array olmalıdır');
      } else if (value.length !== 2) {
        errors.push('Between operatörü için tam 2 değer gereklidir');
      } else {
        value.forEach((val, index) => {
          if (!validateDataType(val, type)) {
            errors.push(`${index + 1}. değer ${type} tipine uygun değil`);
          }
        });
        if (type === 'number' || type === 'currency') {
          if (parseFloat(value[0]) > parseFloat(value[1])) {
            errors.push('İlk değer ikinci değerden büyük olamaz');
          }
        }
      }
      break;

    default:
      if (!validateDataType(value, type)) {
        errors.push(`Değer ${type} tipine uygun değil`);
      }
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * Tablo boyutlarını doğrula
 * @param {number} rowCount - Satır sayısı
 * @param {number} columnCount - Sütun sayısı
 * @returns {Object} Doğrulama sonucu
 */
export const validateTableSize = (rowCount, columnCount) => {
  const errors = [];
  const warnings = [];

  if (rowCount < 0) {
    errors.push('Satır sayısı negatif olamaz');
  }
  if (columnCount < 0) {
    errors.push('Sütun sayısı negatif olamaz');
  }

  if (rowCount > 50000) {
    warnings.push('Çok fazla satır, performans sorunları yaşanabilir');
  }
  if (columnCount > 100) {
    warnings.push('Çok fazla sütun, kullanılabilirlik sorunları yaşanabilir');
  }

  const totalCells = rowCount * columnCount;
  if (totalCells > 1000000) {
    warnings.push('Çok fazla hücre, bellek sorunları yaşanabilir');
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings,
  };
};
