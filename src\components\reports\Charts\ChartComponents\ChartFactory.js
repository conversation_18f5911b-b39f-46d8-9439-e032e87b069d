import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import <PERSON><PERSON><PERSON> from './LineChart';
import <PERSON><PERSON><PERSON> from './BarChart';
import <PERSON><PERSON><PERSON> from './PieChart';
import Progress<PERSON>hart from './ProgressChart';
import Contribution<PERSON>hart from './ContributionChart';

/**
 * Chart Factory component - renders different chart types
 * @param {Object} props - Component props
 * @param {string} props.type - Chart type
 * @param {Object} props.data - Chart data
 * @param {Object} props.theme - Theme object
 * @param {string} props.title - Chart title
 * @param {Object} props.config - Chart configuration
 * @returns {JSX.Element} Chart component
 */
const ChartFactory = ({ type, data, theme, title, config = {} }) => {
  /**
   * Get safe theme value
   * @param {string} property - Theme property
   * @param {string} fallback - Fallback value
   * @returns {string} Safe theme value
   */
  const getSafeThemeValue = (property, fallback) => {
    return theme?.[property] || theme?.colors?.[property.toLowerCase()] || fallback;
  };

  /**
   * Render chart based on type
   * @returns {JSX.Element} Chart component
   */
  const renderChart = () => {
    const chartProps = { data, theme, title, config };
    
    switch (type) {
      case 'line':
        return <LineChart {...chartProps} />;
      case 'bar':
        return <BarChart {...chartProps} />;
      case 'pie':
        return <PieChart {...chartProps} />;
      case 'progress':
        return <ProgressChart {...chartProps} />;
      case 'contribution':
        return <ContributionChart {...chartProps} />;
      default:
        return (
          <View style={[styles.errorContainer, { backgroundColor: getSafeThemeValue('BACKGROUND', '#ffffff') }]}>
            <Text style={[styles.errorIcon, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
              ⚠️
            </Text>
            <Text style={[styles.errorText, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
              Desteklenmeyen grafik türü: {type}
            </Text>
          </View>
        );
    }
  };

  return (
    <View style={styles.container}>
      {renderChart()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  errorContainer: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    marginVertical: 8,
  },
  errorIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  errorText: {
    fontSize: 14,
    textAlign: 'center',
  },
});

export default ChartFactory;
