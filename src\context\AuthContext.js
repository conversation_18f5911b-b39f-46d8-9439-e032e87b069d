import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

const AuthContext = createContext();

/**
 * Authentication Context Provider
 * PIN sistemi kaldırıldı - Basit authentication state yönetimi
 */
export const AuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isFirstTime, setIsFirstTime] = useState(false);

  useEffect(() => {
    checkFirstTime();
  }, []);

  /**
   * İlk kez açılış kontrolü
   */
  const checkFirstTime = async () => {
    try {
      const hasLaunched = await AsyncStorage.getItem('hasLaunched');
      if (!hasLaunched) {
        setIsFirstTime(true);
        await AsyncStorage.setItem('hasLaunched', 'true');
      }
    } catch (error) {
      console.error('İlk açılış kontrolü hatası:', error);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Tüm uygulama verilerini sıfırla
   */
  const resetAllData = async () => {
    try {
      // AsyncStorage'daki tüm anahtarları al
      const keys = await AsyncStorage.getAllKeys();
      
      // Tüm verileri sil
      await AsyncStorage.multiRemove(keys);
      
      // State'leri sıfırla
      setIsAuthenticated(false);
      setIsFirstTime(true);
      
      console.log('✅ Tüm veriler başarıyla temizlendi');
      
      return { 
        success: true, 
        message: 'Tüm veriler başarıyla temizlendi' 
      };
    } catch (error) {
      console.error('❌ Veri temizleme hatası:', error);
      return { 
        success: false, 
        error: 'Veri temizleme sırasında hata oluştu: ' + error.message 
      };
    }
  };

  /**
   * Çıkış yap
   */
  const logout = () => {
    setIsAuthenticated(false);
  };

  const value = {
    // State
    isAuthenticated,
    isLoading,
    isFirstTime,
    
    // Actions
    setIsAuthenticated,
    resetAllData,
    logout
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

/**
 * Auth Context kullanımı için hook
 */
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
