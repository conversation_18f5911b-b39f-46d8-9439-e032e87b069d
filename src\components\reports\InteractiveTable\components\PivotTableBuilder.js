import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useTheme } from '../../../../context/ThemeContext';

/**
 * Pivot Tablo Oluşturucu
 * Verileri gruplandırma ve özetleme için gelişmiş pivot tablo desteği
 * Sürükle-bırak ile satır/sütun/değer alanları yapılandırması
 */
const PivotTableBuilder = ({ 
  data = [], 
  onPivotChange, 
  initialConfig = null 
}) => {
  const { theme } = useTheme();
  
  const [pivotConfig, setPivotConfig] = useState({
    rows: [],
    columns: [],
    values: [],
    aggregations: {},
    filters: {}
  });
  
  const [availableFields, setAvailableFields] = useState([]);
  const [draggedField, setDraggedField] = useState(null);

  useEffect(() => {
    if (initialConfig) {
      setPivotConfig(initialConfig);
    }
  }, [initialConfig]);

  useEffect(() => {
    if (data.length > 0) {
      const fields = Object.keys(data[0]).map(key => ({
        name: key,
        type: getFieldType(data[0][key]),
        displayName: formatFieldName(key)
      }));
      setAvailableFields(fields);
    }
  }, [data]);

  useEffect(() => {
    if (onPivotChange) {
      onPivotChange(pivotConfig);
    }
  }, [pivotConfig, onPivotChange]);

  /**
   * Alan tipini belirle
   */
  const getFieldType = (value) => {
    if (typeof value === 'number') return 'number';
    if (value instanceof Date) return 'date';
    if (typeof value === 'boolean') return 'boolean';
    return 'text';
  };

  /**
   * Alan adını formatla
   */
  const formatFieldName = (fieldName) => {
    return fieldName
      .replace(/_/g, ' ')
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase());
  };

  /**
   * Alan ekleme
   */
  const addFieldToArea = (field, area) => {
    setPivotConfig(prev => ({
      ...prev,
      [area]: [...prev[area], field]
    }));
  };

  /**
   * Alan çıkarma
   */
  const removeFieldFromArea = (fieldIndex, area) => {
    setPivotConfig(prev => ({
      ...prev,
      [area]: prev[area].filter((_, index) => index !== fieldIndex)
    }));
  };

  /**
   * Toplama fonksiyonu değiştirme
   */
  const changeAggregation = (fieldName, aggregationType) => {
    setPivotConfig(prev => ({
      ...prev,
      aggregations: {
        ...prev.aggregations,
        [fieldName]: aggregationType
      }
    }));
  };

  /**
   * Pivot tabloyu sıfırla
   */
  const resetPivot = () => {
    setPivotConfig({
      rows: [],
      columns: [],
      values: [],
      aggregations: {},
      filters: {}
    });
  };

  /**
   * Alan render etme
   */
  const renderField = (field, area, index) => (
    <View key={`${area}-${index}`} style={[styles.fieldChip, { backgroundColor: theme.PRIMARY + '20' }]}>
      <Text style={[styles.fieldText, { color: theme.TEXT_PRIMARY }]}>
        {field.displayName}
      </Text>
      {area === 'values' && (
        <TouchableOpacity
          style={styles.aggregationButton}
          onPress={() => showAggregationOptions(field.name)}
        >
          <Text style={[styles.aggregationText, { color: theme.PRIMARY }]}>
            {pivotConfig.aggregations[field.name] || 'SUM'}
          </Text>
        </TouchableOpacity>
      )}
      <TouchableOpacity
        style={styles.removeButton}
        onPress={() => removeFieldFromArea(index, area)}
      >
        <Text style={styles.removeText}>×</Text>
      </TouchableOpacity>
    </View>
  );

  /**
   * Toplama seçeneklerini göster
   */
  const showAggregationOptions = (fieldName) => {
    const options = ['SUM', 'COUNT', 'AVG', 'MIN', 'MAX'];
    Alert.alert(
      'Toplama Fonksiyonu Seç',
      'Bu alan için kullanılacak toplama fonksiyonunu seçin:',
      options.map(option => ({
        text: option,
        onPress: () => changeAggregation(fieldName, option)
      }))
    );
  };

  /**
   * Drop area render etme
   */
  const renderDropArea = (area, title, icon) => (
    <View style={[styles.dropArea, { backgroundColor: theme.SURFACE }]}>
      <View style={styles.dropAreaHeader}>
        <Text style={styles.dropAreaIcon}>{icon}</Text>
        <Text style={[styles.dropAreaTitle, { color: theme.TEXT_PRIMARY }]}>
          {title}
        </Text>
      </View>
      
      <ScrollView 
        horizontal 
        style={styles.fieldsContainer}
        showsHorizontalScrollIndicator={false}
      >
        {pivotConfig[area].map((field, index) => 
          renderField(field, area, index)
        )}
        
        {pivotConfig[area].length === 0 && (
          <Text style={[styles.emptyText, { color: theme.TEXT_SECONDARY }]}>
            Alanları buraya sürükleyin
          </Text>
        )}
      </ScrollView>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.SURFACE }]}>
        <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
          🔄 Pivot Tablo Yapılandırması
        </Text>
        <TouchableOpacity
          style={[styles.resetButton, { backgroundColor: theme.WARNING }]}
          onPress={resetPivot}
        >
          <Text style={[styles.resetButtonText, { color: theme.SURFACE }]}>
            Sıfırla
          </Text>
        </TouchableOpacity>
      </View>

      {/* Available Fields */}
      <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
        <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
          📋 Kullanılabilir Alanlar
        </Text>
        <ScrollView 
          horizontal 
          style={styles.fieldsContainer}
          showsHorizontalScrollIndicator={false}
        >
          {availableFields.map((field, index) => (
            <TouchableOpacity
              key={index}
              style={[styles.availableField, { backgroundColor: theme.PRIMARY + '10' }]}
              onPress={() => {
                // Otomatik alan ataması
                if (field.type === 'number') {
                  addFieldToArea(field, 'values');
                } else {
                  addFieldToArea(field, 'rows');
                }
              }}
            >
              <Text style={[styles.fieldText, { color: theme.TEXT_PRIMARY }]}>
                {field.displayName}
              </Text>
              <Text style={[styles.fieldType, { color: theme.TEXT_SECONDARY }]}>
                {field.type}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Drop Areas */}
      <ScrollView style={styles.dropAreasContainer}>
        {renderDropArea('rows', 'Satırlar', '📊')}
        {renderDropArea('columns', 'Sütunlar', '📈')}
        {renderDropArea('values', 'Değerler', '🔢')}
      </ScrollView>

      {/* Configuration Summary */}
      <View style={[styles.summary, { backgroundColor: theme.SURFACE }]}>
        <Text style={[styles.summaryTitle, { color: theme.TEXT_PRIMARY }]}>
          📋 Yapılandırma Özeti
        </Text>
        <Text style={[styles.summaryText, { color: theme.TEXT_SECONDARY }]}>
          Satırlar: {pivotConfig.rows.length} • 
          Sütunlar: {pivotConfig.columns.length} • 
          Değerler: {pivotConfig.values.length}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  resetButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  resetButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  section: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  fieldsContainer: {
    flexDirection: 'row',
  },
  availableField: {
    padding: 12,
    borderRadius: 8,
    marginRight: 8,
    alignItems: 'center',
    minWidth: 100,
  },
  fieldText: {
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
  fieldType: {
    fontSize: 12,
    marginTop: 4,
  },
  dropAreasContainer: {
    flex: 1,
  },
  dropArea: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    minHeight: 80,
  },
  dropAreaHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  dropAreaIcon: {
    fontSize: 16,
    marginRight: 8,
  },
  dropAreaTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  fieldChip: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 8,
    marginRight: 8,
  },
  aggregationButton: {
    marginLeft: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    backgroundColor: 'rgba(255,255,255,0.2)',
  },
  aggregationText: {
    fontSize: 10,
    fontWeight: 'bold',
  },
  removeButton: {
    marginLeft: 8,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: 'rgba(255,0,0,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  removeText: {
    color: '#ff0000',
    fontSize: 14,
    fontWeight: 'bold',
  },
  emptyText: {
    fontSize: 14,
    fontStyle: 'italic',
    padding: 16,
  },
  summary: {
    padding: 16,
    borderRadius: 12,
    marginTop: 16,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  summaryText: {
    fontSize: 14,
  },
});

export default PivotTableBuilder;
