import { useSQLiteContext } from 'expo-sqlite';

/**
 * Veritabanı şemasını günceller
 */
export const runMigrations = async () => {
  const db = useSQLiteContext();

  try {
    console.log('Migration başlatılıyor...');

    // Güvenli bir <PERSON>ekilde `updated_at` sütununu ekleyelim
    await db.runAsync(`
      BEGIN TRANSACTION;

      -- Geçici tablo oluştur
      CREATE TABLE IF NOT EXISTS transactions_temp (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        amount DECIMAL(10,2) NOT NULL,
        category_id INTEGER,
        is_income BOOLEAN NOT NULL DEFAULT 0,
        transaction_date DATE NOT NULL,
        description TEXT,
        frequency TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      -- Mevcut verileri yeni tabloya kopyala
      INSERT INTO transactions_temp (
        id, 
        amount, 
        category_id, 
        is_income, 
        transaction_date, 
        description, 
        frequency, 
        created_at,
        updated_at
      )
      SELECT 
        id, 
        amount, 
        category_id, 
        is_income, 
        transaction_date, 
        description, 
        frequency, 
        created_at,
        created_at
      FROM transactions;

      -- Eski tabloyu sil
      DROP TABLE IF EXISTS transactions;

      -- Yeni tabloyu yeniden adlandır
      ALTER TABLE transactions_temp RENAME TO transactions;

      COMMIT;
    `);

    console.log('Migration başarılı: transactions tablosu güncellendi');

    // Tablo yapısını kontrol et
    const tableInfo = await db.getAllAsync("PRAGMA table_info(transactions)");
    console.log('Güncel tablo yapısı:', JSON.stringify(tableInfo, null, 2));

  } catch (error) {
    console.error('Migration hatası:', error);
    // Transaction'ı geri al
    await db.runAsync('ROLLBACK');
    throw error;
  }
};
