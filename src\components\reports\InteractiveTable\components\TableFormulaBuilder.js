import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  TextInput,
  Modal,
} from 'react-native';

/**
 * Table Formula Builder Component
 * Allows users to create and edit formulas for table columns
 * Follows REPORTS_SCREEN_REDESIGN_PLAN.md specifications
 */
const TableFormulaBuilder = ({ 
  visible,
  onClose,
  onSave,
  initialFormula = '',
  availableColumns = [],
  theme 
}) => {
  const [formula, setFormula] = useState(initialFormula);
  const [selectedFunction, setSelectedFunction] = useState(null);
  const [showFunctionHelp, setShowFunctionHelp] = useState(false);

  // Available functions
  const functions = [
    {
      name: 'SUM',
      description: 'Toplam hesaplar',
      syntax: 'SUM(column)',
      example: 'SUM(amount)',
      category: 'Matematik',
    },
    {
      name: 'AVG',
      description: 'Ortalama hesaplar',
      syntax: 'AVG(column)',
      example: 'AVG(amount)',
      category: 'Matematik',
    },
    {
      name: 'COUNT',
      description: 'Sayım yapar',
      syntax: 'COUNT(column)',
      example: 'COUNT(id)',
      category: 'Matematik',
    },
    {
      name: 'MAX',
      description: 'En büyük değeri bulur',
      syntax: 'MAX(column)',
      example: 'MAX(amount)',
      category: 'Matematik',
    },
    {
      name: 'MIN',
      description: 'En küçük değeri bulur',
      syntax: 'MIN(column)',
      example: 'MIN(amount)',
      category: 'Matematik',
    },
    {
      name: 'IF',
      description: 'Koşullu işlem',
      syntax: 'IF(condition, true_value, false_value)',
      example: 'IF(amount > 1000, "Yüksek", "Düşük")',
      category: 'Mantık',
    },
    {
      name: 'CONCAT',
      description: 'Metinleri birleştirir',
      syntax: 'CONCAT(text1, text2)',
      example: 'CONCAT(first_name, " ", last_name)',
      category: 'Metin',
    },
    {
      name: 'FORMAT',
      description: 'Sayıları formatlar',
      syntax: 'FORMAT(number, format)',
      example: 'FORMAT(amount, "₺#,##0.00")',
      category: 'Format',
    },
  ];

  const handleInsertFunction = (func) => {
    const cursorPosition = formula.length;
    const newFormula = formula + func.syntax;
    setFormula(newFormula);
    setSelectedFunction(func);
  };

  const handleInsertColumn = (column) => {
    const cursorPosition = formula.length;
    const newFormula = formula + `[${column.name}]`;
    setFormula(newFormula);
  };

  const handleSave = () => {
    if (formula.trim()) {
      onSave(formula);
      onClose();
    }
  };

  const validateFormula = () => {
    // Basic formula validation
    try {
      // Check for balanced brackets
      const openBrackets = (formula.match(/\[/g) || []).length;
      const closeBrackets = (formula.match(/\]/g) || []).length;
      const openParens = (formula.match(/\(/g) || []).length;
      const closeParens = (formula.match(/\)/g) || []).length;
      
      return openBrackets === closeBrackets && openParens === closeParens;
    } catch (error) {
      return false;
    }
  };

  const isValid = validateFormula();

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
        {/* Header */}
        <View style={[styles.header, { backgroundColor: theme.SURFACE }]}>
          <TouchableOpacity onPress={onClose}>
            <Text style={[styles.cancelButton, { color: theme.ERROR }]}>
              İptal
            </Text>
          </TouchableOpacity>
          
          <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
            Formül Editörü
          </Text>
          
          <TouchableOpacity 
            onPress={handleSave}
            disabled={!isValid || !formula.trim()}
            style={[
              styles.saveButton,
              { 
                backgroundColor: isValid && formula.trim() ? theme.SUCCESS : theme.DISABLED,
                opacity: isValid && formula.trim() ? 1 : 0.5
              }
            ]}
          >
            <Text style={[styles.saveButtonText, { color: theme.SURFACE }]}>
              Kaydet
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content}>
          {/* Formula Input */}
          <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
            <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
              Formül
            </Text>
            
            <TextInput
              style={[
                styles.formulaInput,
                { 
                  backgroundColor: theme.BACKGROUND,
                  color: theme.TEXT_PRIMARY,
                  borderColor: isValid ? theme.SUCCESS : theme.ERROR
                }
              ]}
              value={formula}
              onChangeText={setFormula}
              placeholder="Formülünüzü buraya yazın..."
              placeholderTextColor={theme.TEXT_SECONDARY}
              multiline
              numberOfLines={3}
            />
            
            {!isValid && formula.trim() && (
              <Text style={[styles.errorText, { color: theme.ERROR }]}>
                Formül geçersiz. Parantez ve köşeli parantezleri kontrol edin.
              </Text>
            )}
          </View>

          {/* Available Columns */}
          <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
            <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
              Kullanılabilir Sütunlar
            </Text>
            
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              {availableColumns.map((column, index) => (
                <TouchableOpacity
                  key={index}
                  style={[styles.columnChip, { backgroundColor: theme.PRIMARY }]}
                  onPress={() => handleInsertColumn(column)}
                >
                  <Text style={[styles.columnChipText, { color: theme.SURFACE }]}>
                    {column.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>

          {/* Functions */}
          <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
            <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
              Fonksiyonlar
            </Text>
            
            {functions.map((func, index) => (
              <TouchableOpacity
                key={index}
                style={[styles.functionItem, { backgroundColor: theme.BACKGROUND }]}
                onPress={() => handleInsertFunction(func)}
              >
                <View style={styles.functionHeader}>
                  <Text style={[styles.functionName, { color: theme.PRIMARY }]}>
                    {func.name}
                  </Text>
                  <Text style={[styles.functionCategory, { color: theme.TEXT_SECONDARY }]}>
                    {func.category}
                  </Text>
                </View>
                
                <Text style={[styles.functionDescription, { color: theme.TEXT_PRIMARY }]}>
                  {func.description}
                </Text>
                
                <Text style={[styles.functionSyntax, { color: theme.TEXT_SECONDARY }]}>
                  Sözdizimi: {func.syntax}
                </Text>
                
                <Text style={[styles.functionExample, { color: theme.INFO }]}>
                  Örnek: {func.example}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  cancelButton: {
    fontSize: 16,
    fontWeight: '600',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 16,
    padding: 16,
    borderRadius: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  formulaInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    fontFamily: 'monospace',
    textAlignVertical: 'top',
  },
  errorText: {
    fontSize: 12,
    marginTop: 8,
  },
  columnChip: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
  },
  columnChipText: {
    fontSize: 14,
    fontWeight: '600',
  },
  functionItem: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  functionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  functionName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  functionCategory: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  functionDescription: {
    fontSize: 14,
    marginBottom: 4,
  },
  functionSyntax: {
    fontSize: 12,
    fontFamily: 'monospace',
    marginBottom: 2,
  },
  functionExample: {
    fontSize: 12,
    fontFamily: 'monospace',
    fontStyle: 'italic',
  },
});

export default TableFormulaBuilder;
