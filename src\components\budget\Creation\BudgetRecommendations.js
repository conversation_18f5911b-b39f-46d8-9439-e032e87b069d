/**
 * Bütçe Önerileri Komponenti
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md - Stage 2, Phase 3
 * 
 * Kişiselleştirilmiş bütçe önerileri ve akıllı tavsiyeleri
 * Maksimum 250 satır - Tek sorumluluk prensibi
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../../../context/ThemeContext';
import { formatTRY, formatUSD, formatEUR } from '../../../utils/formatters';

/**
 * Bütçe önerileri komponenti
 * @param {Object} props - Component props
 * @param {Object} props.analysisData - Geçmiş analiz verileri
 * @param {Array} props.selectedCategories - <PERSON><PERSON><PERSON> kate<PERSON>
 * @param {string} props.budgetType - Bütçe türü ('total', 'category_based', 'flexible')
 * @param {string} props.currency - Para birimi ('TRY', 'USD', 'EUR')
 * @param {Function} props.onRecommendationApply - Öneri uygulama callback
 * @param {Object} props.theme - Tema objesi (opsiyonel, context'ten alınır)
 */
const BudgetRecommendations = ({ 
  analysisData, 
  selectedCategories = [], 
  budgetType = 'category_based',
  currency = 'TRY',
  onRecommendationApply,
  theme: propTheme 
}) => {
  const { theme } = useTheme();
  const currentTheme = propTheme || theme;

  // State yönetimi
  const [recommendations, setRecommendations] = useState([]);
  const [selectedRecommendations, setSelectedRecommendations] = useState(new Set());

  /**
   * Önerileri oluştur
   */
  const generateRecommendations = () => {
    if (!analysisData || !analysisData.categories) {
      return [];
    }

    const recs = [];

    // Kategori bazlı öneriler
    analysisData.categories.forEach(category => {
      // Tutarlı harcama önerisi
      if (category.consistency > 0.7) {
        recs.push({
          id: `consistent_${category.id}`,
          type: 'consistent',
          title: `${category.name} - Tutarlı Bütçe`,
          description: 'Geçmiş harcamalarınıza dayalı tutarlı limit',
          categoryId: category.id,
          categoryName: category.name,
          categoryIcon: category.icon,
          suggestedAmount: Math.round(category.average),
          confidence: 'Yüksek',
          reasoning: `Son ${analysisData.monthsAnalyzed} ayda tutarlı harcama`,
          icon: 'check-circle',
          color: currentTheme.SUCCESS,
          priority: 1
        });
      }

      // Artan trend uyarısı
      if (category.trend === 'increasing') {
        recs.push({
          id: `increasing_${category.id}`,
          type: 'warning',
          title: `${category.name} - Artış Kontrolü`,
          description: 'Harcamalarınız artış gösteriyor, dikkatli olun',
          categoryId: category.id,
          categoryName: category.name,
          categoryIcon: category.icon,
          suggestedAmount: Math.round(category.average * 1.1),
          confidence: 'Orta',
          reasoning: 'Artan harcama trendi tespit edildi',
          icon: 'trending-up',
          color: currentTheme.WARNING,
          priority: 2
        });
      }

      // Azalan trend fırsatı
      if (category.trend === 'decreasing') {
        recs.push({
          id: `decreasing_${category.id}`,
          type: 'opportunity',
          title: `${category.name} - Tasarruf Fırsatı`,
          description: 'Harcamalarınız azalıyor, daha düşük limit belirleyebilirsiniz',
          categoryId: category.id,
          categoryName: category.name,
          categoryIcon: category.icon,
          suggestedAmount: Math.round(category.average * 0.9),
          confidence: 'Orta',
          reasoning: 'Azalan harcama trendi fırsatı',
          icon: 'trending-down',
          color: currentTheme.SUCCESS,
          priority: 3
        });
      }
    });

    // Genel öneriler
    if (budgetType === 'total') {
      recs.push({
        id: 'total_conservative',
        type: 'total',
        title: 'Muhafazakar Toplam Bütçe',
        description: 'Geçmiş ortalamadan %5 daha az',
        suggestedAmount: Math.round(analysisData.averageMonthly * 0.95),
        confidence: 'Yüksek',
        reasoning: 'Tasarruf odaklı yaklaşım',
        icon: 'account-balance-wallet',
        color: currentTheme.PRIMARY,
        priority: 1
      });

      recs.push({
        id: 'total_realistic',
        type: 'total',
        title: 'Gerçekçi Toplam Bütçe',
        description: 'Geçmiş ortalama harcama',
        suggestedAmount: Math.round(analysisData.averageMonthly),
        confidence: 'Yüksek',
        reasoning: 'Mevcut harcama alışkanlıklarınıza uygun',
        icon: 'balance',
        color: currentTheme.INFO,
        priority: 2
      });
    }

    // Önceliklere göre sırala
    return recs.sort((a, b) => a.priority - b.priority);
  };

  // Analiz verileri değiştiğinde önerileri güncelle
  useEffect(() => {
    const newRecommendations = generateRecommendations();
    setRecommendations(newRecommendations);
  }, [analysisData, budgetType, selectedCategories]);

  /**
   * Öneri seçim işleyicisi
   * @param {string} recommendationId - Öneri ID'si
   */
  const handleRecommendationToggle = (recommendationId) => {
    const newSelected = new Set(selectedRecommendations);
    
    if (newSelected.has(recommendationId)) {
      newSelected.delete(recommendationId);
    } else {
      newSelected.add(recommendationId);
    }
    
    setSelectedRecommendations(newSelected);
  };

  /**
   * Seçili önerileri uygula
   */
  const handleApplySelected = () => {
    const selectedRecs = recommendations.filter(rec => 
      selectedRecommendations.has(rec.id)
    );

    if (selectedRecs.length === 0) {
      Alert.alert('Uyarı', 'Lütfen uygulamak istediğiniz önerileri seçin.');
      return;
    }

    Alert.alert(
      'Önerileri Uygula',
      `${selectedRecs.length} öneriyi uygulamak istediğinizden emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Uygula',
          onPress: () => {
            if (onRecommendationApply) {
              onRecommendationApply(selectedRecs);
            }
            setSelectedRecommendations(new Set());
          }
        }
      ]
    );
  };

  /**
   * Para birimi formatı
   * @param {number} value - Formatlanacak değer
   * @returns {string} Formatlanmış değer
   */
  const formatCurrency = (value) => {
    switch (currency) {
      case 'USD':
        return formatUSD(value);
      case 'EUR':
        return formatEUR(value);
      default:
        return formatTRY(value);
    }
  };

  /**
   * Para birimi sembolü
   * @returns {string} Para birimi sembolü
   */
  const getCurrencySymbol = () => {
    switch (currency) {
      case 'USD':
        return '$';
      case 'EUR':
        return '€';
      default:
        return '₺';
    }
  };

  /**
   * Güven seviyesi rengi
   * @param {string} confidence - Güven seviyesi
   * @returns {string} Renk kodu
   */
  const getConfidenceColor = (confidence) => {
    switch (confidence) {
      case 'Yüksek':
        return currentTheme.SUCCESS;
      case 'Orta':
        return currentTheme.WARNING;
      default:
        return currentTheme.TEXT_SECONDARY;
    }
  };

  if (!analysisData || recommendations.length === 0) {
    return (
      <View style={[styles.emptyContainer, { backgroundColor: currentTheme.BACKGROUND }]}>
        <MaterialIcons name="lightbulb-outline" size={64} color={currentTheme.TEXT_SECONDARY} />
        <Text style={[styles.emptyTitle, { color: currentTheme.TEXT_PRIMARY }]}>
          Henüz öneri yok
        </Text>
        <Text style={[styles.emptySubtitle, { color: currentTheme.TEXT_SECONDARY }]}>
          Geçmiş harcama analizi tamamlandıktan sonra öneriler görünecek
        </Text>
      </View>
    );
  }

  const currencySymbol = getCurrencySymbol();

  return (
    <View style={[styles.container, { backgroundColor: currentTheme.BACKGROUND }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: currentTheme.TEXT_PRIMARY }]}>
          Akıllı Bütçe Önerileri
        </Text>
        <Text style={[styles.subtitle, { color: currentTheme.TEXT_SECONDARY }]}>
          Geçmiş verilerinize dayalı kişiselleştirilmiş öneriler
        </Text>
      </View>

      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {recommendations.map((recommendation) => {
          const isSelected = selectedRecommendations.has(recommendation.id);
          
          return (
            <TouchableOpacity
              key={recommendation.id}
              style={[
                styles.recommendationCard,
                {
                  backgroundColor: currentTheme.SURFACE,
                  borderColor: isSelected 
                    ? currentTheme.PRIMARY 
                    : currentTheme.BORDER,
                  borderWidth: isSelected ? 2 : 1,
                }
              ]}
              onPress={() => handleRecommendationToggle(recommendation.id)}
              activeOpacity={0.7}
            >
              <View style={styles.recommendationHeader}>
                <View style={[styles.recommendationIcon, { backgroundColor: recommendation.color + '20' }]}>
                  <MaterialIcons 
                    name={recommendation.icon} 
                    size={24} 
                    color={recommendation.color} 
                  />
                </View>
                
                <View style={styles.recommendationInfo}>
                  <Text style={[styles.recommendationTitle, { color: currentTheme.TEXT_PRIMARY }]}>
                    {recommendation.title}
                  </Text>
                  <Text style={[styles.recommendationDescription, { color: currentTheme.TEXT_SECONDARY }]}>
                    {recommendation.description}
                  </Text>
                </View>

                <MaterialIcons
                  name={isSelected ? 'check-box' : 'check-box-outline-blank'}
                  size={24}
                  color={isSelected ? currentTheme.PRIMARY : currentTheme.TEXT_SECONDARY}
                />
              </View>

              <View style={styles.recommendationDetails}>
                <View style={styles.amountSection}>
                  <Text style={[styles.suggestedAmount, { color: currentTheme.TEXT_PRIMARY }]}>
                    {currencySymbol}{recommendation.suggestedAmount.toLocaleString('tr-TR')}
                  </Text>
                  <Text style={[styles.amountFormatted, { color: currentTheme.TEXT_SECONDARY }]}>
                    {formatCurrency(recommendation.suggestedAmount)}
                  </Text>
                </View>

                <View style={styles.metaInfo}>
                  <View style={[styles.confidenceBadge, { backgroundColor: getConfidenceColor(recommendation.confidence) + '20' }]}>
                    <Text style={[styles.confidenceText, { color: getConfidenceColor(recommendation.confidence) }]}>
                      {recommendation.confidence} Güven
                    </Text>
                  </View>
                  
                  <Text style={[styles.reasoning, { color: currentTheme.TEXT_SECONDARY }]}>
                    {recommendation.reasoning}
                  </Text>
                </View>
              </View>
            </TouchableOpacity>
          );
        })}
      </ScrollView>

      {/* Uygula butonu */}
      {selectedRecommendations.size > 0 && (
        <View style={styles.actionSection}>
          <TouchableOpacity
            style={[styles.applyButton, { backgroundColor: currentTheme.PRIMARY }]}
            onPress={handleApplySelected}
          >
            <MaterialIcons name="check" size={24} color={currentTheme.WHITE} />
            <Text style={[styles.applyButtonText, { color: currentTheme.WHITE }]}>
              {selectedRecommendations.size} Öneriyi Uygula
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  header: {
    paddingVertical: 16,
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 80,
  },
  recommendationCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  recommendationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 12,
  },
  recommendationIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  recommendationInfo: {
    flex: 1,
  },
  recommendationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  recommendationDescription: {
    fontSize: 13,
    lineHeight: 18,
  },
  recommendationDetails: {
    gap: 8,
  },
  amountSection: {
    alignItems: 'center',
  },
  suggestedAmount: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  amountFormatted: {
    fontSize: 12,
  },
  metaInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  confidenceBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  confidenceText: {
    fontSize: 11,
    fontWeight: 'bold',
  },
  reasoning: {
    fontSize: 11,
    flex: 1,
    textAlign: 'right',
    marginLeft: 8,
  },
  actionSection: {
    position: 'absolute',
    bottom: 16,
    left: 16,
    right: 16,
  },
  applyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
  },
  applyButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default BudgetRecommendations;
