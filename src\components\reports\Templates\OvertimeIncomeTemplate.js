import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useTheme } from '../../../context/ThemeContext';
import { useDataIntegration } from '../../../context/DataIntegrationContext';
import { formatCurrency } from '../../../utils/formatters';
import { ExportManager } from '../Export';

/**
 * Vardiya/Mesai Gelir Şablonu
 * Mesai/vardiya gelirleri raporu - Real Data Integration ile güncellenmiş
 */
const OvertimeIncomeTemplate = ({ 
  templateConfig, 
  customParams, 
  onExport, 
  onSave 
}) => {
  const { theme } = useTheme();
  const { getOvertimeIncomeData, loading, error } = useDataIntegration();
  const [reportData, setReportData] = useState(null);

  /**
   * Get safe theme value
   * @param {string} property - Theme property
   * @param {string} fallback - Fallback value
   * @returns {string} Safe theme value
   */
  const getSafeThemeValue = (property, fallback) => {
    return theme?.[property] || theme?.colors?.[property.toLowerCase()] || fallback;
  };

  // Load report data on component mount
  useEffect(() => {
    loadReportData();
  }, [customParams]);

  /**
   * Load overtime income data
   */
  const loadReportData = async () => {
    try {
      const data = await getOvertimeIncomeData(customParams);
      setReportData(data);
    } catch (err) {
      // Error is handled by DataIntegrationContext
      setReportData(null);
    }
  };

  // Loading state
  if (loading && !reportData) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: getSafeThemeValue('BACKGROUND', '#FFFFFF') }]}>
        <ActivityIndicator size="large" color={getSafeThemeValue('PRIMARY', '#007AFF')} />
        <Text style={[styles.loadingText, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
          Mesai gelir verileri yükleniyor...
        </Text>
      </View>
    );
  }

  // Error state
  if (error && !reportData) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: getSafeThemeValue('BACKGROUND', '#FFFFFF') }]}>
        <Text style={[styles.errorText, { color: getSafeThemeValue('ERROR', '#FF3B30') }]}>
          ⚠️ Veri Yüklenemedi
        </Text>
        <Text style={[styles.errorDetail, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
          {error}
        </Text>
        <TouchableOpacity
          style={[styles.retryButton, { backgroundColor: getSafeThemeValue('PRIMARY', '#007AFF') }]}
          onPress={loadReportData}
        >
          <Text style={[styles.retryButtonText, { color: getSafeThemeValue('WHITE', '#FFFFFF') }]}>
            Tekrar Dene
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Empty state
  if (!reportData || (!reportData.shifts?.length && !reportData.payments?.length)) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: getSafeThemeValue('BACKGROUND', '#FFFFFF') }]}>
        <Text style={[styles.emptyTitle, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
          ⏰ Mesai Kaydı Bulunamadı
        </Text>
        <Text style={[styles.emptySubtitle, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
          Seçilen dönemde mesai/vardiya kaydı bulunamadı.
        </Text>
        <Text style={[styles.emptyHint, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
          Mesai kayıtları ve vardiya bilgilerini ekleyerek bu raporu görüntüleyebilirsiniz.
        </Text>
      </View>
    );
  }

  const renderSummaryCard = () => (
    <View style={[styles.summaryCard, { backgroundColor: getSafeThemeValue('CARD_BACKGROUND', '#F8F9FA') }]}>
      <Text style={[styles.cardTitle, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
        📊 Mesai Gelir Özeti
      </Text>
      <View style={styles.summaryGrid}>
        <View style={styles.summaryItem}>
          <Text style={[styles.summaryLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
            Toplam Mesai Geliri
          </Text>
          <Text style={[styles.summaryValue, { color: getSafeThemeValue('SUCCESS', '#28A745') }]}>
            {formatCurrency(reportData.summary.totalOvertimeAmount)}
          </Text>
        </View>
        <View style={styles.summaryItem}>
          <Text style={[styles.summaryLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
            Toplam Mesai Saati
          </Text>
          <Text style={[styles.summaryValue, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
            {reportData.summary.totalOvertimeHours} saat
          </Text>
        </View>
        <View style={styles.summaryItem}>
          <Text style={[styles.summaryLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
            Aylık Ortalama
          </Text>
          <Text style={[styles.summaryValue, { color: getSafeThemeValue('INFO', '#17A2B8') }]}>
            {formatCurrency(reportData.summary.monthlyAverage)}
          </Text>
        </View>
        <View style={styles.summaryItem}>
          <Text style={[styles.summaryLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
            Ortalama Saatlik
          </Text>
          <Text style={[styles.summaryValue, { color: getSafeThemeValue('WARNING', '#FFC107') }]}>
            {formatCurrency(reportData.summary.averageHourlyRate)}
          </Text>
        </View>
      </View>
    </View>
  );

  const renderRatesCard = () => (
    <View style={[styles.summaryCard, { backgroundColor: getSafeThemeValue('CARD_BACKGROUND', '#F8F9FA') }]}>
      <Text style={[styles.cardTitle, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
        💰 Ücret Bilgileri
      </Text>
      <View style={styles.ratesGrid}>
        <View style={styles.rateItem}>
          <Text style={[styles.rateLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
            Normal Saatlik Ücret
          </Text>
          <Text style={[styles.rateValue, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
            {formatCurrency(reportData.summary.baseHourlyRate)}/saat
          </Text>
        </View>
        <View style={styles.rateItem}>
          <Text style={[styles.rateLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
            Mesai Saatlik Ücret
          </Text>
          <Text style={[styles.rateValue, { color: getSafeThemeValue('SUCCESS', '#28A745') }]}>
            {formatCurrency(reportData.summary.overtimeRate)}/saat
          </Text>
        </View>
        <View style={styles.rateItem}>
          <Text style={[styles.rateLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
            Mesai Çarpanı
          </Text>
          <Text style={[styles.rateValue, { color: getSafeThemeValue('INFO', '#17A2B8') }]}>
            {reportData.summary.baseHourlyRate > 0 ? 
              (reportData.summary.overtimeRate / reportData.summary.baseHourlyRate).toFixed(1) + 'x' : 
              'N/A'
            }
          </Text>
        </View>
        <View style={styles.rateItem}>
          <Text style={[styles.rateLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
            Para Birimi
          </Text>
          <Text style={[styles.rateValue, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
            {reportData.settings.currency}
          </Text>
        </View>
      </View>
    </View>
  );

  const renderShiftsList = () => (
    <View style={[styles.summaryCard, { backgroundColor: getSafeThemeValue('CARD_BACKGROUND', '#F8F9FA') }]}>
      <Text style={[styles.cardTitle, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
        ⏰ Mesai Kayıtları
      </Text>
      {reportData.shifts.map((shift, index) => (
        <View key={index} style={[styles.shiftItem, { borderBottomColor: getSafeThemeValue('BORDER', '#E5E5E5') }]}>
          <View style={styles.shiftInfo}>
            <Text style={[styles.shiftDate, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
              {new Date(shift.date).toLocaleDateString('tr-TR')}
            </Text>
            <Text style={[styles.shiftTime, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
              {shift.startTime} - {shift.endTime}
            </Text>
            <Text style={[styles.shiftDuration, { color: getSafeThemeValue('INFO', '#17A2B8') }]}>
              {shift.duration.toFixed(1)} saat (x{shift.overtimeMultiplier})
            </Text>
            {shift.notes && (
              <Text style={[styles.shiftNotes, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
                {shift.notes}
              </Text>
            )}
          </View>
          <View style={styles.shiftAmount}>
            <Text style={[styles.shiftEarnings, { color: getSafeThemeValue('SUCCESS', '#28A745') }]}>
              {formatCurrency(shift.estimatedEarnings)}
            </Text>
            <Text style={[styles.shiftStatus, { 
              color: shift.status === 'completed' ? 
                getSafeThemeValue('SUCCESS', '#28A745') : 
                getSafeThemeValue('WARNING', '#FFC107') 
            }]}>
              {shift.status === 'completed' ? '✅ Tamamlandı' : '⏳ Devam Ediyor'}
            </Text>
          </View>
        </View>
      ))}
    </View>
  );

  const renderPaymentsList = () => (
    <View style={[styles.summaryCard, { backgroundColor: getSafeThemeValue('CARD_BACKGROUND', '#F8F9FA') }]}>
      <Text style={[styles.cardTitle, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
        💸 Mesai Ödemeleri
      </Text>
      {reportData.payments.map((payment, index) => (
        <View key={index} style={[styles.paymentItem, { borderBottomColor: getSafeThemeValue('BORDER', '#E5E5E5') }]}>
          <View style={styles.paymentInfo}>
            <Text style={[styles.paymentPeriod, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
              {new Date(payment.periodStart).toLocaleDateString('tr-TR')} - {new Date(payment.periodEnd).toLocaleDateString('tr-TR')}
            </Text>
            <Text style={[styles.paymentHours, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
              {payment.hours} mesai saati
            </Text>
            <Text style={[styles.paymentStatus, { 
              color: payment.isPaid ? getSafeThemeValue('SUCCESS', '#28A745') : getSafeThemeValue('WARNING', '#FFC107') 
            }]}>
              {payment.isPaid ? '✅ Ödendi' : '⏳ Ödeme Bekliyor'}
            </Text>
          </View>
          <View style={styles.paymentAmount}>
            <Text style={[styles.paymentAmountText, { color: getSafeThemeValue('SUCCESS', '#28A745') }]}>
              {formatCurrency(payment.amount)}
            </Text>
            {payment.paymentDate && (
              <Text style={[styles.paymentDate, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
                Ödeme: {new Date(payment.paymentDate).toLocaleDateString('tr-TR')}
              </Text>
            )}
          </View>
        </View>
      ))}
    </View>
  );

  const renderStatisticsCard = () => (
    <View style={[styles.summaryCard, { backgroundColor: getSafeThemeValue('CARD_BACKGROUND', '#F8F9FA') }]}>
      <Text style={[styles.cardTitle, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
        📈 İstatistikler
      </Text>
      <View style={styles.statisticsGrid}>
        <View style={styles.statisticItem}>
          <Text style={[styles.statisticLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
            Toplam Vardiya
          </Text>
          <Text style={[styles.statisticValue, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
            {reportData.shifts.length}
          </Text>
        </View>
        <View style={styles.statisticItem}>
          <Text style={[styles.statisticLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
            Ortalama Vardiya Süresi
          </Text>
          <Text style={[styles.statisticValue, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
            {reportData.shifts.length > 0 ? 
              (reportData.summary.totalEstimatedHours / reportData.shifts.length).toFixed(1) + ' saat' : 
              'N/A'
            }
          </Text>
        </View>
        <View style={styles.statisticItem}>
          <Text style={[styles.statisticLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
            Günlük Ortalama
          </Text>
          <Text style={[styles.statisticValue, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
            {formatCurrency(reportData.summary.totalOvertimeAmount / Math.max(reportData.shifts.length, 1))}
          </Text>
        </View>
        <View style={styles.statisticItem}>
          <Text style={[styles.statisticLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
            Tamamlanan Ödemeler
          </Text>
          <Text style={[styles.statisticValue, { color: getSafeThemeValue('SUCCESS', '#28A745') }]}>
            {reportData.payments.filter(p => p.isPaid).length}/{reportData.payments.length}
          </Text>
        </View>
      </View>
    </View>
  );

  return (
    <ScrollView style={[styles.container, { backgroundColor: getSafeThemeValue('BACKGROUND', '#FFFFFF') }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
          ⏰ Vardiya/Mesai Gelir Raporu
        </Text>
        <Text style={[styles.subtitle, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
          {reportData.period}
        </Text>
      </View>

      {renderSummaryCard()}
      {renderRatesCard()}
      {renderStatisticsCard()}
      
      {reportData.shifts?.length > 0 && renderShiftsList()}
      {reportData.payments?.length > 0 && renderPaymentsList()}

      {(onExport || onSave) && (
        <View style={styles.actions}>
          <ExportManager 
            reportData={reportData}
            reportTitle="Vardiya/Mesai Gelir Raporu"
            reportType="overtime_income"
            buttonStyle={styles.exportButton}
            buttonTextStyle={styles.exportButtonText}
            theme={theme}
          />
          
          {onSave && (
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: getSafeThemeValue('SECONDARY', '#6C757D') }]}
              onPress={() => onSave(reportData)}
            >
              <Text style={[styles.actionButtonText, { color: getSafeThemeValue('WHITE', '#FFFFFF') }]}>
                � Kaydet
              </Text>
            </TouchableOpacity>
          )}
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  header: {
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    textAlign: 'center',
  },
  errorText: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  errorDetail: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 8,
  },
  emptyHint: {
    fontSize: 14,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  summaryCard: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  summaryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  summaryItem: {
    width: '48%',
    marginBottom: 16,
  },
  summaryLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  ratesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  rateItem: {
    width: '48%',
    marginBottom: 12,
  },
  rateLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  rateValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  statisticsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statisticItem: {
    width: '48%',
    marginBottom: 12,
  },
  statisticLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  statisticValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  shiftItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  shiftInfo: {
    flex: 1,
  },
  shiftDate: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  shiftTime: {
    fontSize: 14,
    marginBottom: 2,
  },
  shiftDuration: {
    fontSize: 14,
    marginBottom: 2,
  },
  shiftNotes: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  shiftAmount: {
    alignItems: 'flex-end',
  },
  shiftEarnings: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  shiftStatus: {
    fontSize: 12,
    marginTop: 2,
  },
  paymentItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  paymentInfo: {
    flex: 1,
  },
  paymentPeriod: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  paymentHours: {
    fontSize: 12,
    marginBottom: 4,
  },
  paymentStatus: {
    fontSize: 12,
    fontWeight: '500',
  },
  paymentAmount: {
    alignItems: 'flex-end',
  },
  paymentAmountText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  paymentDate: {
    fontSize: 12,
    marginTop: 2,
  },
  actions: {
    flexDirection: 'column',
    justifyContent: 'space-around',
    padding: 20,
    gap: 12,
  },
  exportButton: {
    marginBottom: 8,
  },
  exportButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  actionButton: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    minWidth: 120,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

export default OvertimeIncomeTemplate;
