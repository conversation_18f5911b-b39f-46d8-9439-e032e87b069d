/**
 * Vardiya türleri tablosunu oluşturan migrasyon
 *
 * @param {object} db - SQLite veritabanı nesnesi
 * @returns {Promise<void>}
 */
export const migrateWorkShiftTypes = async (db) => {
  try {
    console.log('Vardiya türleri tablosu migrasyonu başlatılıyor...');

    // Vardiya türleri tablosunu oluştur
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS work_shift_types (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        start_time TEXT NOT NULL,
        end_time TEXT NOT NULL,
        color TEXT NOT NULL,
        is_night_shift INTEGER DEFAULT 0,
        break_duration INTEGER DEFAULT 0,
        notes TEXT,
        created_at TEXT DEFAULT '2023-01-01 00:00:00',
        updated_at TEXT DEFAULT '2023-01-01 00:00:00'
      )
    `);

    // Varsay<PERSON><PERSON> vardiya türle<PERSON> ekle
    const defaultShiftTypes = [
      {
        name: 'G<PERSON>ndüz Vardiyası',
        start_time: '08:00:00',
        end_time: '17:00:00',
        color: '#4CAF50',
        is_night_shift: 0,
        break_duration: 60
      },
      {
        name: 'Akşam Vardiyası',
        start_time: '16:00:00',
        end_time: '00:00:00',
        color: '#FF9800',
        is_night_shift: 0,
        break_duration: 60
      },
      {
        name: 'Gece Vardiyası',
        start_time: '00:00:00',
        end_time: '08:00:00',
        color: '#2196F3',
        is_night_shift: 1,
        break_duration: 60
      },
      {
        name: 'Yarım Gün (Sabah)',
        start_time: '08:00:00',
        end_time: '12:00:00',
        color: '#9C27B0',
        is_night_shift: 0,
        break_duration: 0
      },
      {
        name: 'Yarım Gün (Öğleden Sonra)',
        start_time: '13:00:00',
        end_time: '17:00:00',
        color: '#E91E63',
        is_night_shift: 0,
        break_duration: 0
      }
    ];

    // Mevcut vardiya türlerini kontrol et
    const existingTypes = await db.getAllAsync(`SELECT name FROM work_shift_types`);
    const existingTypeNames = existingTypes.map(type => type.name);

    // Eksik olan varsayılan vardiya türlerini ekle
    for (const shiftType of defaultShiftTypes) {
      if (!existingTypeNames.includes(shiftType.name)) {
        await db.runAsync(`
          INSERT INTO work_shift_types (
            name, start_time, end_time, color, is_night_shift, break_duration
          ) VALUES (?, ?, ?, ?, ?, ?)
        `, [
          shiftType.name,
          shiftType.start_time,
          shiftType.end_time,
          shiftType.color,
          shiftType.is_night_shift,
          shiftType.break_duration
        ]);
      }
    }

    console.log('Vardiya türleri tablosu migrasyonu tamamlandı.');
  } catch (error) {
    console.error('Vardiya türleri tablosu migrasyonu hatası:', error);
    throw error;
  }
};
