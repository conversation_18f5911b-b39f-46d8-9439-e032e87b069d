/**
 * <PERSON><PERSON>tçe Hesaplama Servisi
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md Stage 1 implementasyonu
 * 
 * Bu servis bütçe hesaplamaları, analizler ve performans metrikleri sağlar
 */

/**
 * Bütçe performansını hesaplar
 * @param {Object} db - SQLite database instance
 * @param {number} budgetId - Bütçe ID'si
 * @returns {Promise<Object>} Performans metrikleri
 */
export const calculateBudgetPerformance = async (db, budgetId) => {
  try {
    // Bütçe detaylarını getir
    const budget = await db.getFirstAsync(`
      SELECT * FROM budgets_enhanced WHERE id = ?
    `, [budgetId]);

    if (!budget) {
      throw new Error('Bütçe bulunamadı');
    }

    // Kategori bazlı harcamaları hesapla
    const categories = await db.getAllAsync(`
      SELECT 
        bc.*,
        c.name as category_name
      FROM budget_categories_enhanced bc
      JOIN categories c ON bc.category_id = c.id
      WHERE bc.budget_id = ?
    `, [budgetId]);

    let totalAllocated = 0;
    let totalSpent = 0;
    const categoryPerformance = [];

    for (const category of categories) {
      const spent = await calculateCategorySpending(db, budgetId, category.category_id);
      
      totalAllocated += category.limit_amount;
      totalSpent += spent;

      categoryPerformance.push({
        category_id: category.category_id,
        category_name: category.category_name,
        allocated: category.limit_amount,
        spent: spent,
        remaining: category.limit_amount - spent,
        percentage: category.limit_amount > 0 ? (spent / category.limit_amount) * 100 : 0,
        status: getSpendingStatus(spent, category.limit_amount)
      });
    }

    // Genel performans metrikleri
    const overallPercentage = totalAllocated > 0 ? (totalSpent / totalAllocated) * 100 : 0;
    const remainingBudget = totalAllocated - totalSpent;
    const daysInPeriod = calculateDaysInPeriod(budget.start_date, budget.end_date);
    const daysRemaining = calculateDaysRemaining(budget.end_date);
    const dailyBudget = daysInPeriod > 0 ? totalAllocated / daysInPeriod : 0;
    const dailySpending = daysInPeriod > 0 ? totalSpent / (daysInPeriod - daysRemaining) : 0;

    return {
      budget_id: budgetId,
      budget_name: budget.name,
      period: {
        start_date: budget.start_date,
        end_date: budget.end_date,
        days_total: daysInPeriod,
        days_remaining: daysRemaining,
        days_elapsed: daysInPeriod - daysRemaining
      },
      totals: {
        allocated: totalAllocated,
        spent: totalSpent,
        remaining: remainingBudget,
        percentage: overallPercentage
      },
      daily: {
        budget: dailyBudget,
        spending: dailySpending,
        recommended: daysRemaining > 0 ? remainingBudget / daysRemaining : 0
      },
      categories: categoryPerformance,
      status: getSpendingStatus(totalSpent, totalAllocated),
      alerts: generateBudgetAlerts(overallPercentage, categoryPerformance)
    };

  } catch (error) {
    console.error('❌ Bütçe performans hesaplama hatası:', error);
    throw error;
  }
};

/**
 * Kategori harcama miktarını hesaplar
 * @param {Object} db - SQLite database instance
 * @param {number} budgetId - Bütçe ID'si
 * @param {string} categoryId - Kategori ID'si
 * @returns {Promise<number>} Harcama miktarı
 */
const calculateCategorySpending = async (db, budgetId, categoryId) => {
  try {
    const budget = await db.getFirstAsync(`
      SELECT start_date, end_date FROM budgets_enhanced WHERE id = ?
    `, [budgetId]);

    if (!budget) return 0;

    const result = await db.getFirstAsync(`
      SELECT SUM(amount) as total
      FROM transactions
      WHERE category_id = ? 
        AND type = 'expense'
        AND date >= ?
        AND (? IS NULL OR date <= ?)
    `, [
      categoryId,
      budget.start_date,
      budget.end_date,
      budget.end_date
    ]);

    return result?.total || 0;
  } catch (error) {
    console.error('❌ Kategori harcama hesaplama hatası:', error);
    return 0;
  }
};

/**
 * Harcama durumunu belirler
 * @param {number} spent - Harcanan miktar
 * @param {number} allocated - Ayrılan miktar
 * @returns {string} Durum ('good', 'warning', 'danger', 'exceeded')
 */
const getSpendingStatus = (spent, allocated) => {
  if (allocated === 0) return 'good';
  
  const percentage = (spent / allocated) * 100;
  
  if (percentage >= 100) return 'exceeded';
  if (percentage >= 90) return 'danger';
  if (percentage >= 75) return 'warning';
  return 'good';
};

/**
 * Dönem içindeki toplam gün sayısını hesaplar
 * @param {string} startDate - Başlangıç tarihi
 * @param {string} endDate - Bitiş tarihi
 * @returns {number} Gün sayısı
 */
const calculateDaysInPeriod = (startDate, endDate) => {
  if (!endDate) {
    // Eğer bitiş tarihi yoksa, başlangıçtan bugüne kadar
    const start = new Date(startDate);
    const today = new Date();
    return Math.ceil((today - start) / (1000 * 60 * 60 * 24)) + 1;
  }
  
  const start = new Date(startDate);
  const end = new Date(endDate);
  return Math.ceil((end - start) / (1000 * 60 * 60 * 24)) + 1;
};

/**
 * Kalan gün sayısını hesaplar
 * @param {string} endDate - Bitiş tarihi
 * @returns {number} Kalan gün sayısı
 */
const calculateDaysRemaining = (endDate) => {
  if (!endDate) return 0;
  
  const today = new Date();
  const end = new Date(endDate);
  const remaining = Math.ceil((end - today) / (1000 * 60 * 60 * 24));
  return Math.max(0, remaining);
};

/**
 * Bütçe uyarıları oluşturur
 * @param {number} overallPercentage - Genel harcama yüzdesi
 * @param {Array} categoryPerformance - Kategori performansları
 * @returns {Array} Uyarı listesi
 */
const generateBudgetAlerts = (overallPercentage, categoryPerformance) => {
  const alerts = [];

  // Genel bütçe uyarıları
  if (overallPercentage >= 100) {
    alerts.push({
      type: 'budget_exceeded',
      severity: 'high',
      message: 'Bütçe limitini aştınız!',
      percentage: overallPercentage
    });
  } else if (overallPercentage >= 90) {
    alerts.push({
      type: 'budget_warning',
      severity: 'medium',
      message: 'Bütçenizin %90\'ını kullandınız',
      percentage: overallPercentage
    });
  } else if (overallPercentage >= 75) {
    alerts.push({
      type: 'budget_caution',
      severity: 'low',
      message: 'Bütçenizin %75\'ini kullandınız',
      percentage: overallPercentage
    });
  }

  // Kategori bazlı uyarılar
  categoryPerformance.forEach(category => {
    if (category.status === 'exceeded') {
      alerts.push({
        type: 'category_exceeded',
        severity: 'high',
        message: `${category.category_name} kategorisi limitini aştı`,
        category: category.category_name,
        percentage: category.percentage
      });
    } else if (category.status === 'danger') {
      alerts.push({
        type: 'category_warning',
        severity: 'medium',
        message: `${category.category_name} kategorisi limitine yaklaştı`,
        category: category.category_name,
        percentage: category.percentage
      });
    }
  });

  return alerts;
};

/**
 * Aylık bütçe önerisi hesaplar
 * @param {Object} db - SQLite database instance
 * @param {Object} options - Hesaplama seçenekleri
 * @returns {Promise<Object>} Bütçe önerisi
 */
export const calculateBudgetSuggestion = async (db, options = {}) => {
  try {
    const { months = 3, includeCategories = true } = options;
    
    // Son X ay harcama verilerini analiz et
    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - months);

    const spendingData = await db.getAllAsync(`
      SELECT 
        c.id as category_id,
        c.name as category_name,
        SUM(t.amount) as total_spent,
        COUNT(t.id) as transaction_count,
        AVG(t.amount) as avg_transaction
      FROM transactions t
      JOIN categories c ON t.category_id = c.id
      WHERE t.type = 'expense'
        AND t.date >= ?
        AND t.date <= ?
      GROUP BY c.id, c.name
      ORDER BY total_spent DESC
    `, [
      startDate.toISOString().split('T')[0],
      endDate.toISOString().split('T')[0]
    ]);

    const totalSpending = spendingData.reduce((sum, cat) => sum + cat.total_spent, 0);
    const monthlyAverage = totalSpending / months;

    const suggestions = {
      recommended_total: Math.ceil(monthlyAverage * 1.1), // %10 buffer
      analysis_period: `${months} ay`,
      total_analyzed: totalSpending,
      monthly_average: monthlyAverage,
      categories: spendingData.map(cat => ({
        category_id: cat.category_id,
        category_name: cat.category_name,
        monthly_average: cat.total_spent / months,
        suggested_limit: Math.ceil((cat.total_spent / months) * 1.15), // %15 buffer
        percentage_of_total: (cat.total_spent / totalSpending) * 100,
        transaction_frequency: cat.transaction_count / months
      }))
    };

    return suggestions;

  } catch (error) {
    console.error('❌ Bütçe önerisi hesaplama hatası:', error);
    throw error;
  }
};

/**
 * Bütçe geçmişi kaydı oluşturur
 * @param {Object} db - SQLite database instance
 * @param {number} budgetId - Bütçe ID'si
 * @returns {Promise<boolean>} Başarı durumu
 */
export const createBudgetHistoryRecord = async (db, budgetId) => {
  try {
    const performance = await calculateBudgetPerformance(db, budgetId);
    
    await db.runAsync(`
      INSERT INTO budget_history 
      (budget_id, period_start, period_end, total_spent, total_limit, success_rate, categories_data)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [
      budgetId,
      performance.period.start_date,
      performance.period.end_date,
      performance.totals.spent,
      performance.totals.allocated,
      100 - performance.totals.percentage, // Success rate as remaining percentage
      JSON.stringify(performance.categories)
    ]);

    console.log(`✅ Bütçe geçmişi kaydı oluşturuldu (Budget ID: ${budgetId})`);
    return true;
  } catch (error) {
    console.error('❌ Bütçe geçmişi kaydetme hatası:', error);
    throw error;
  }
};
