import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import { Alert } from 'react-native';

/**
 * PowerPoint Exporter - PowerPoint benzeri HTML sunumu oluşturur
 */
export class PowerPointExporter {

  /**
   * PowerPoint benzeri HTML sunumu oluşturur
   * @param {Object} params - PowerPoint parametreleri
   * @returns {Promise<Object>} PowerPoint oluşturma sonucu
   */
  static async exportToPowerPoint({ data, title, type, config = {} }) {
    try {
      const htmlContent = this.generatePresentationHTML(data, title, type, config);
      const fileName = `${title.replace(/[^a-zA-Z0-9]/g, '_')}_sunum_${new Date().getTime()}.html`;
      const fileUri = FileSystem.documentDirectory + fileName;

      await FileSystem.writeAsStringAsync(fileUri, htmlContent, {
        encoding: FileSystem.EncodingType.UTF8,
      });

      // Share the HTML presentation
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri, {
          mimeType: 'text/html',
          dialogTitle: 'HTML Sunumu Paylaş',
        });
      }

      return {
        success: true,
        message: 'HTML sunumu başarıyla oluşturuldu',
        filePath: fileUri
      };
    } catch (error) {
      console.error('PowerPoint export error:', error);
      return {
        success: false,
        message: `Sunum oluşturulurken hata oluştu: ${error.message}`,
        filePath: null
      };
    }
  }

  /**
   * HTML sunum içeriği oluşturur
   */
  static generatePresentationHTML(data, title, type, config) {
    const currentDate = new Date().toLocaleDateString('tr-TR');

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>${title} - Sunum</title>
        <style>
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
          }
          .slide {
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 40px;
            box-sizing: border-box;
            page-break-after: always;
          }
          .slide-title {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 30px;
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
          }
          .slide-content {
            font-size: 24px;
            text-align: center;
            max-width: 80%;
            line-height: 1.6;
          }
          .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            width: 100%;
            max-width: 1000px;
          }
          .summary-card {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
          }
          .summary-value {
            font-size: 36px;
            font-weight: bold;
            margin-top: 10px;
          }
          .chart-placeholder {
            width: 400px;
            height: 300px;
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px auto;
            border: 2px dashed rgba(255,255,255,0.3);
          }
          .navigation {
            position: fixed;
            bottom: 20px;
            right: 20px;
            font-size: 14px;
            opacity: 0.7;
          }
          @media print {
            .slide { page-break-after: always; }
          }
        </style>
      </head>
      <body>
        <!-- Slide 1: Title -->
        <div class="slide">
          <div class="slide-title">${title}</div>
          <div class="slide-content">
            <p>Finansal Rapor Sunumu</p>
            <p style="font-size: 18px; margin-top: 40px;">Rapor Tarihi: ${currentDate}</p>
          </div>
        </div>

        <!-- Slide 2: Summary -->
        <div class="slide">
          <div class="slide-title">Finansal Özet</div>
          <div class="summary-grid">
            ${this.generateSummaryCards(data)}
          </div>
        </div>

        <!-- Slide 3: Charts -->
        <div class="slide">
          <div class="slide-title">Görsel Analiz</div>
          <div class="slide-content">
            <div class="chart-placeholder">
              <p>Gelir/Gider Grafiği<br/>(Grafik burada görüntülenecek)</p>
            </div>
            <div class="chart-placeholder">
              <p>Kategori Dağılımı<br/>(Grafik burada görüntülenecek)</p>
            </div>
          </div>
        </div>

        <!-- Slide 4: Recent Transactions -->
        <div class="slide">
          <div class="slide-title">Son İşlemler</div>
          <div class="slide-content">
            ${this.generateTransactionsSummary(data)}
          </div>
        </div>

        <div class="navigation">
          Sayfa geçişi için kaydırın veya yazdırın
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Özet kartları oluşturur
   */
  static generateSummaryCards(data) {
    if (!data || !data.summary) {
      return `
        <div class="summary-card">
          <div>Toplam Gelir</div>
          <div class="summary-value">0 ₺</div>
        </div>
        <div class="summary-card">
          <div>Toplam Gider</div>
          <div class="summary-value">0 ₺</div>
        </div>
        <div class="summary-card">
          <div>Net Gelir</div>
          <div class="summary-value">0 ₺</div>
        </div>
      `;
    }

    return `
      <div class="summary-card">
        <div>Toplam Gelir</div>
        <div class="summary-value">${data.summary.totalIncome || 0} ₺</div>
      </div>
      <div class="summary-card">
        <div>Toplam Gider</div>
        <div class="summary-value">${data.summary.totalExpense || 0} ₺</div>
      </div>
      <div class="summary-card">
        <div>Net Gelir</div>
        <div class="summary-value">${data.summary.netIncome || 0} ₺</div>
      </div>
    `;
  }

  /**
   * İşlemler özetini oluşturur
   */
  static generateTransactionsSummary(data) {
    if (!data || !data.transactions || data.transactions.length === 0) {
      return '<p>Henüz işlem bulunmamaktadır.</p>';
    }

    const recentTransactions = data.transactions.slice(0, 5);
    let html = '<div style="text-align: left; max-width: 600px;">';

    recentTransactions.forEach(transaction => {
      const date = new Date(transaction.date).toLocaleDateString('tr-TR');
      const type = transaction.type === 'income' ? 'Gelir' : 'Gider';
      const color = transaction.type === 'income' ? '#4CAF50' : '#F44336';

      html += `
        <div style="margin-bottom: 15px; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 8px;">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
              <div style="font-weight: bold;">${transaction.description || 'İsimsiz İşlem'}</div>
              <div style="font-size: 16px; opacity: 0.8;">${date} - ${transaction.category_name || 'Kategori yok'}</div>
            </div>
            <div style="color: ${color}; font-weight: bold; font-size: 20px;">
              ${transaction.amount} ₺ (${type})
            </div>
          </div>
        </div>
      `;
    });

    html += '</div>';

    if (data.transactions.length > 5) {
      html += `<p style="margin-top: 20px; opacity: 0.8;">ve ${data.transactions.length - 5} işlem daha...</p>`;
    }

    return html;
  }
}
