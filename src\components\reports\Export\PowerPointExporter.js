/**
 * PowerPoint Exporter - PowerPoint dosyaları oluşturur
 * Temporary mock implementation for testing ExportManager
 */
export class PowerPointExporter {
  
  /**
   * PowerPoint dosyası oluşturur
   * @param {Object} params - PowerPoint parametreleri
   * @returns {Promise<Object>} PowerPoint oluşturma sonucu
   */
  static async exportToPowerPoint({ data, title, type, config = {} }) {
    console.log('🔍 PowerPointExporter.exportToPowerPoint called with:', { title, type });
    
    // Temporary mock implementation
    return {
      success: true,
      message: 'PowerPoint export mocked (packages removed for testing)',
      filePath: null
    };
  }
}
