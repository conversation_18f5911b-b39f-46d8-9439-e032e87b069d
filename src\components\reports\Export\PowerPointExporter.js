import { Platform } from 'react-native';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';

/**
 * PowerPoint Exporter - Sunum formatında raporlar oluşturur
 * React Native'de gerçek PowerPoint dosyası oluşturmak için HTML formatını kullanır
 * Gelecekte pptx kütüphanesi entegrasyonu yapılabilir
 */
export class PowerPointExporter {
  
  /**
   * PowerPoint sunumu oluşturur (HTML formatında)
   * @param {Object} params - PowerPoint parametreleri
   * @param {Object} params.data - Rapor verisi
   * @param {string} params.title - Rapor başlığı
   * @param {string} params.type - Rapor türü
   * @param {Object} params.config - PowerPoint yapılandırması
   * @returns {Promise<Object>} PowerPoint oluşturma sonucu
   */
  static async exportToPowerPoint({ data, title, type, config = {} }) {
    try {
      // PowerPoint yapılandırması
      const pptConfig = {
        format: config.format || 'html',
        theme: config.theme || 'professional',
        includeCharts: config.includeCharts !== false,
        includeAnimation: config.includeAnimation !== false,
        slideLayout: config.slideLayout || 'standard',
        ...config
      };

      // PowerPoint içeriğini oluştur
      const htmlContent = this.generatePresentationHTML({
        data,
        title,
        type,
        config: pptConfig
      });

      // Dosya adını oluştur
      const fileName = this.generateFileName(title, type);
      const fileUri = `${FileSystem.documentDirectory}${fileName}`;

      // Dosyayı yaz
      await FileSystem.writeAsStringAsync(fileUri, htmlContent, {
        encoding: 'utf8'
      });

      // Dosyayı paylaş
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri, {
          mimeType: 'text/html',
          dialogTitle: 'PowerPoint Sunumu Paylaş'
        });
      }

      return {
        success: true,
        uri: fileUri,
        fileName
      };

    } catch (error) {
      console.error('PowerPoint Export Error:', error);
      return {
        success: false,
        error: error.message || 'PowerPoint sunumu oluşturulurken bilinmeyen hata'
      };
    }
  }

  /**
   * Sunum HTML içeriğini oluşturur
   * @param {Object} params - HTML parametreleri
   * @returns {string} HTML içeriği
   */
  static generatePresentationHTML({ data, title, type, config }) {
    const slides = this.generateSlides({ data, title, type, config });
    const styles = this.getPresentationStyles(config);
    
    return `
      <!DOCTYPE html>
      <html lang="tr">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>${title}</title>
          <style>${styles}</style>
        </head>
        <body>
          <div class="presentation-container">
            <div class="presentation-header">
              <h1>${title}</h1>
              <p>Oluşturulma Tarihi: ${new Date().toLocaleDateString('tr-TR')}</p>
            </div>
            
            <div class="slides-container">
              ${slides}
            </div>
            
            <div class="presentation-footer">
              <p>Maaş Takip Uygulaması</p>
            </div>
          </div>
          
          <script>
            ${this.getInteractiveScript()}
          </script>
        </body>
      </html>
    `;
  }

  /**
   * Sunum stillerini döndürür
   * @param {Object} config - Yapılandırma
   * @returns {string} CSS stilleri
   */
  static getPresentationStyles(config) {
    const theme = config.theme || 'professional';
    const baseStyles = this.getBaseStyles();
    const themeStyles = this.getThemeStyles(theme);
    
    return baseStyles + themeStyles;
  }

  /**
   * Temel CSS stilleri
   */
  static getBaseStyles() {
    return `
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
        line-height: 1.6;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
      }
      
      .presentation-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }
      
      .presentation-header {
        text-align: center;
        margin-bottom: 40px;
        color: white;
        background: rgba(0, 0, 0, 0.3);
        padding: 30px;
        border-radius: 15px;
        backdrop-filter: blur(10px);
      }
      
      .presentation-header h1 {
        font-size: 2.5rem;
        margin-bottom: 10px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
      }
      
      .presentation-header p {
        font-size: 1.2rem;
        opacity: 0.9;
      }
      
      .slides-container {
        display: grid;
        gap: 30px;
      }
      
      .slide {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        min-height: 500px;
        display: flex;
        flex-direction: column;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      
      .slide:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
      }
      
      .slide-header {
        text-align: center;
        margin-bottom: 30px;
        border-bottom: 3px solid #667eea;
        padding-bottom: 15px;
      }
      
      .slide-title {
        font-size: 2rem;
        color: #333;
        margin-bottom: 10px;
      }
      
      .slide-subtitle {
        font-size: 1.1rem;
        color: #666;
        font-style: italic;
      }
      
      .slide-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }
      
      .metric-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin: 20px 0;
      }
      
      .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 25px;
        border-radius: 12px;
        text-align: center;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        transition: transform 0.3s ease;
      }
      
      .metric-card:hover {
        transform: scale(1.05);
      }
      
      .metric-value {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 8px;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
      }
      
      .metric-label {
        font-size: 1rem;
        opacity: 0.9;
      }
      
      .data-table {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;
        background: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      }
      
      .data-table th,
      .data-table td {
        padding: 15px;
        text-align: left;
        border-bottom: 1px solid #eee;
      }
      
      .data-table th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
      
      .data-table tr:nth-child(even) {
        background-color: #f8f9fa;
      }
      
      .data-table tr:hover {
        background-color: #e3f2fd;
      }
      
      .chart-placeholder {
        width: 100%;
        height: 300px;
        background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
                    linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
                    linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
                    linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
        background-size: 20px 20px;
        background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #666;
        font-size: 1.2rem;
        margin: 20px 0;
        border: 3px dashed #ddd;
      }
      
      .presentation-footer {
        text-align: center;
        margin-top: 40px;
        color: white;
        font-size: 1.1rem;
        opacity: 0.8;
      }
      
      .slide-number {
        position: absolute;
        top: 20px;
        right: 20px;
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 8px 12px;
        border-radius: 20px;
        font-size: 0.9rem;
      }
      
      .highlight-positive {
        color: #27ae60;
        font-weight: bold;
      }
      
      .highlight-negative {
        color: #e74c3c;
        font-weight: bold;
      }
      
      .highlight-neutral {
        color: #f39c12;
        font-weight: bold;
      }
      
      @media (max-width: 768px) {
        .presentation-header h1 {
          font-size: 2rem;
        }
        
        .slide-title {
          font-size: 1.5rem;
        }
        
        .metric-grid {
          grid-template-columns: 1fr;
        }
        
        .metric-value {
          font-size: 2rem;
        }
      }
    `;
  }

  /**
   * Tema stillerini döndürür
   * @param {string} theme - Tema adı
   * @returns {string} Tema CSS stilleri
   */
  static getThemeStyles(theme) {
    const themes = {
      professional: `
        .slide {
          border-left: 5px solid #667eea;
        }
        .metric-card {
          background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
        }
      `,
      vibrant: `
        .slide {
          border-left: 5px solid #e74c3c;
        }
        .metric-card {
          background: linear-gradient(135deg, #e74c3c 0%, #f39c12 100%);
        }
      `,
      elegant: `
        .slide {
          border-left: 5px solid #8e44ad;
        }
        .metric-card {
          background: linear-gradient(135deg, #8e44ad 0%, #2c3e50 100%);
        }
      `
    };
    
    return themes[theme] || themes.professional;
  }

  /**
   * Slaytları oluşturur
   * @param {Object} params - Slayt parametreleri
   * @returns {string} Slayt HTML içeriği
   */
  static generateSlides({ data, title, type, config }) {
    const slides = [];
    
    // Başlık slaytı
    slides.push(this.generateTitleSlide(title, type));
    
    // Rapor türüne göre içerik slaytları
    switch (type) {
      case 'monthly-income-expense':
        slides.push(...this.generateMonthlyIncomeExpenseSlides(data));
        break;
      case 'basic-summary':
        slides.push(...this.generateBasicSummarySlides(data));
        break;
      case 'category-distribution':
        slides.push(...this.generateCategoryDistributionSlides(data));
        break;
      case 'cash-flow':
        slides.push(...this.generateCashFlowSlides(data));
        break;
      case 'budget-vs-actual':
        slides.push(...this.generateBudgetVsActualSlides(data));
        break;
      case 'transaction-list':
        slides.push(...this.generateTransactionListSlides(data));
        break;
      default:
        slides.push(...this.generateGenericSlides(data));
    }
    
    // Özet/kapanış slaytı
    slides.push(this.generateClosingSlide(data));
    
    return slides.join('');
  }

  /**
   * Başlık slaytı
   */
  static generateTitleSlide(title, type) {
    return `
      <div class="slide" style="position: relative;">
        <div class="slide-number">1</div>
        <div class="slide-content" style="text-align: center; justify-content: center;">
          <h1 style="font-size: 3rem; color: #333; margin-bottom: 20px;">${title}</h1>
          <p style="font-size: 1.5rem; color: #666; margin-bottom: 30px;">Finansal Rapor Sunumu</p>
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                     color: white; padding: 20px; border-radius: 12px; display: inline-block;">
            <p style="font-size: 1.2rem;">Hazırlanma Tarihi: ${new Date().toLocaleDateString('tr-TR')}</p>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Kapanış slaytı
   */
  static generateClosingSlide(data) {
    return `
      <div class="slide" style="position: relative;">
        <div class="slide-number">Son</div>
        <div class="slide-content" style="text-align: center; justify-content: center;">
          <h2 style="font-size: 2.5rem; color: #333; margin-bottom: 30px;">Teşekkürler</h2>
          <p style="font-size: 1.3rem; color: #666; margin-bottom: 40px;">
            Bu rapor Maaş Takip Uygulaması ile oluşturulmuştur.
          </p>
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                     color: white; padding: 30px; border-radius: 15px; display: inline-block;">
            <p style="font-size: 1.1rem;">
              Sorularınız için iletişime geçebilirsiniz.
            </p>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Aylık gelir-gider slaytları
   */
  static generateMonthlyIncomeExpenseSlides(data) {
    const slides = [];
    const { summary, monthlyData } = data;
    
    // Özet slaytı
    slides.push(`
      <div class="slide" style="position: relative;">
        <div class="slide-number">2</div>
        <div class="slide-header">
          <h2 class="slide-title">Aylık Gelir-Gider Özeti</h2>
          <p class="slide-subtitle">Genel finansal durum görünümü</p>
        </div>
        <div class="slide-content">
          <div class="metric-grid">
            <div class="metric-card">
              <div class="metric-value">₺${summary.totalIncome?.toLocaleString('tr-TR') || '0'}</div>
              <div class="metric-label">Toplam Gelir</div>
            </div>
            <div class="metric-card">
              <div class="metric-value">₺${summary.totalExpense?.toLocaleString('tr-TR') || '0'}</div>
              <div class="metric-label">Toplam Gider</div>
            </div>
            <div class="metric-card">
              <div class="metric-value">₺${summary.netIncome?.toLocaleString('tr-TR') || '0'}</div>
              <div class="metric-label">Net Gelir</div>
            </div>
          </div>
        </div>
      </div>
    `);
    
    // Detay slaytı
    if (monthlyData && monthlyData.length > 0) {
      slides.push(`
        <div class="slide" style="position: relative;">
          <div class="slide-number">3</div>
          <div class="slide-header">
            <h2 class="slide-title">Aylık Detaylar</h2>
            <p class="slide-subtitle">Ay bazında gelir-gider analizi</p>
          </div>
          <div class="slide-content">
            <table class="data-table">
              <thead>
                <tr>
                  <th>Ay</th>
                  <th>Gelir</th>
                  <th>Gider</th>
                  <th>Net</th>
                </tr>
              </thead>
              <tbody>
                ${monthlyData.map(item => `
                  <tr>
                    <td>${item.month}</td>
                    <td class="highlight-positive">₺${item.income?.toLocaleString('tr-TR') || '0'}</td>
                    <td class="highlight-negative">₺${item.expense?.toLocaleString('tr-TR') || '0'}</td>
                    <td class="${item.net >= 0 ? 'highlight-positive' : 'highlight-negative'}">
                      ₺${item.net?.toLocaleString('tr-TR') || '0'}
                    </td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
        </div>
      `);
    }
    
    return slides;
  }

  /**
   * Temel özet slaytları
   */
  static generateBasicSummarySlides(data) {
    const { summary } = data;
    
    return [`
      <div class="slide" style="position: relative;">
        <div class="slide-number">2</div>
        <div class="slide-header">
          <h2 class="slide-title">Finansal Özet</h2>
          <p class="slide-subtitle">Genel finansal performans göstergeleri</p>
        </div>
        <div class="slide-content">
          <div class="metric-grid">
            <div class="metric-card">
              <div class="metric-value">₺${summary.totalIncome?.toLocaleString('tr-TR') || '0'}</div>
              <div class="metric-label">Toplam Gelir</div>
            </div>
            <div class="metric-card">
              <div class="metric-value">₺${summary.totalExpense?.toLocaleString('tr-TR') || '0'}</div>
              <div class="metric-label">Toplam Gider</div>
            </div>
            <div class="metric-card">
              <div class="metric-value">₺${summary.averageIncome?.toLocaleString('tr-TR') || '0'}</div>
              <div class="metric-label">Ortalama Gelir</div>
            </div>
            <div class="metric-card">
              <div class="metric-value">₺${summary.averageExpense?.toLocaleString('tr-TR') || '0'}</div>
              <div class="metric-label">Ortalama Gider</div>
            </div>
          </div>
        </div>
      </div>
    `];
  }

  /**
   * Kategori dağılımı slaytları
   */
  static generateCategoryDistributionSlides(data) {
    const { categories } = data;
    
    if (!categories || categories.length === 0) {
      return [`
        <div class="slide">
          <div class="slide-header">
            <h2 class="slide-title">Kategori Dağılımı</h2>
          </div>
          <div class="slide-content">
            <p style="text-align: center; font-size: 1.2rem; color: #666;">
              Kategori verisi bulunamadı.
            </p>
          </div>
        </div>
      `];
    }

    return [`
      <div class="slide" style="position: relative;">
        <div class="slide-number">2</div>
        <div class="slide-header">
          <h2 class="slide-title">Kategori Dağılımı</h2>
          <p class="slide-subtitle">Harcama ve gelir kategorilerine göre dağılım</p>
        </div>
        <div class="slide-content">
          <table class="data-table">
            <thead>
              <tr>
                <th>Kategori</th>
                <th>Tutar</th>
                <th>Yüzde</th>
                <th>İşlem Sayısı</th>
              </tr>
            </thead>
            <tbody>
              ${categories.map(category => `
                <tr>
                  <td>${category.name}</td>
                  <td class="${category.type === 'income' ? 'highlight-positive' : 'highlight-negative'}">
                    ₺${category.amount?.toLocaleString('tr-TR') || '0'}
                  </td>
                  <td>${category.percentage?.toFixed(1) || '0'}%</td>
                  <td>${category.count || 0}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
      </div>
    `];
  }

  /**
   * Nakit akış slaytları
   */
  static generateCashFlowSlides(data) {
    const { cashFlowData } = data;
    
    if (!cashFlowData || cashFlowData.length === 0) {
      return [`
        <div class="slide">
          <div class="slide-header">
            <h2 class="slide-title">Nakit Akış</h2>
          </div>
          <div class="slide-content">
            <p style="text-align: center; font-size: 1.2rem; color: #666;">
              Nakit akış verisi bulunamadı.
            </p>
          </div>
        </div>
      `];
    }

    return [`
      <div class="slide" style="position: relative;">
        <div class="slide-number">2</div>
        <div class="slide-header">
          <h2 class="slide-title">Nakit Akış</h2>
          <p class="slide-subtitle">Dönemsel nakit akış analizi</p>
        </div>
        <div class="slide-content">
          <table class="data-table">
            <thead>
              <tr>
                <th>Période</th>
                <th>Başlangıç</th>
                <th>Gelir</th>
                <th>Gider</th>
                <th>Bitiş</th>
              </tr>
            </thead>
            <tbody>
              ${cashFlowData.map(item => `
                <tr>
                  <td>${item.period}</td>
                  <td>₺${item.startBalance?.toLocaleString('tr-TR') || '0'}</td>
                  <td class="highlight-positive">₺${item.income?.toLocaleString('tr-TR') || '0'}</td>
                  <td class="highlight-negative">₺${item.expense?.toLocaleString('tr-TR') || '0'}</td>
                  <td class="${item.endBalance >= 0 ? 'highlight-positive' : 'highlight-negative'}">
                    ₺${item.endBalance?.toLocaleString('tr-TR') || '0'}
                  </td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
      </div>
    `];
  }

  /**
   * Bütçe vs gerçekleşen slaytları
   */
  static generateBudgetVsActualSlides(data) {
    const { budgetData } = data;
    
    if (!budgetData || budgetData.length === 0) {
      return [`
        <div class="slide">
          <div class="slide-header">
            <h2 class="slide-title">Bütçe vs Gerçekleşen</h2>
          </div>
          <div class="slide-content">
            <p style="text-align: center; font-size: 1.2rem; color: #666;">
              Bütçe verisi bulunamadı.
            </p>
          </div>
        </div>
      `];
    }

    return [`
      <div class="slide" style="position: relative;">
        <div class="slide-number">2</div>
        <div class="slide-header">
          <h2 class="slide-title">Bütçe vs Gerçekleşen</h2>
          <p class="slide-subtitle">Bütçe planlaması ve gerçekleşme karşılaştırması</p>
        </div>
        <div class="slide-content">
          <table class="data-table">
            <thead>
              <tr>
                <th>Kategori</th>
                <th>Bütçe</th>
                <th>Gerçekleşen</th>
                <th>Fark</th>
                <th>%</th>
              </tr>
            </thead>
            <tbody>
              ${budgetData.map(item => `
                <tr>
                  <td>${item.category}</td>
                  <td>₺${item.budget?.toLocaleString('tr-TR') || '0'}</td>
                  <td>₺${item.actual?.toLocaleString('tr-TR') || '0'}</td>
                  <td class="${item.difference >= 0 ? 'highlight-positive' : 'highlight-negative'}">
                    ₺${item.difference?.toLocaleString('tr-TR') || '0'}
                  </td>
                  <td>${item.percentage?.toFixed(1) || '0'}%</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
      </div>
    `];
  }

  /**
   * İşlem listesi slaytları
   */
  static generateTransactionListSlides(data) {
    const { transactions } = data;
    
    if (!transactions || transactions.length === 0) {
      return [`
        <div class="slide">
          <div class="slide-header">
            <h2 class="slide-title">İşlem Listesi</h2>
          </div>
          <div class="slide-content">
            <p style="text-align: center; font-size: 1.2rem; color: #666;">
              İşlem verisi bulunamadı.
            </p>
          </div>
        </div>
      `];
    }

    // İşlemleri sayfalara böl (her sayfada 10 işlem)
    const itemsPerPage = 10;
    const pages = [];
    
    for (let i = 0; i < transactions.length; i += itemsPerPage) {
      const pageTransactions = transactions.slice(i, i + itemsPerPage);
      const pageNumber = Math.floor(i / itemsPerPage) + 2;
      
      pages.push(`
        <div class="slide" style="position: relative;">
          <div class="slide-number">${pageNumber}</div>
          <div class="slide-header">
            <h2 class="slide-title">İşlem Listesi</h2>
            <p class="slide-subtitle">Sayfa ${pageNumber - 1} / ${Math.ceil(transactions.length / itemsPerPage)}</p>
          </div>
          <div class="slide-content">
            <table class="data-table">
              <thead>
                <tr>
                  <th>Tarih</th>
                  <th>Açıklama</th>
                  <th>Kategori</th>
                  <th>Tutar</th>
                  <th>Tip</th>
                </tr>
              </thead>
              <tbody>
                ${pageTransactions.map(transaction => `
                  <tr>
                    <td>${transaction.date}</td>
                    <td>${transaction.description}</td>
                    <td>${transaction.category}</td>
                    <td class="${transaction.type === 'income' ? 'highlight-positive' : 'highlight-negative'}">
                      ₺${transaction.amount?.toLocaleString('tr-TR') || '0'}
                    </td>
                    <td>${transaction.type === 'income' ? 'Gelir' : 'Gider'}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
        </div>
      `);
    }
    
    return pages;
  }

  /**
   * Genel slaytlar
   */
  static generateGenericSlides(data) {
    return [`
      <div class="slide" style="position: relative;">
        <div class="slide-number">2</div>
        <div class="slide-header">
          <h2 class="slide-title">Rapor Verisi</h2>
          <p class="slide-subtitle">Ham veri görünümü</p>
        </div>
        <div class="slide-content">
          <div class="chart-placeholder">
            <div style="text-align: center;">
              <p style="font-size: 1.2rem; margin-bottom: 10px;">📊 Veri Görselleştirmesi</p>
              <p style="font-size: 1rem; color: #888;">Grafik burada gösterilecek</p>
            </div>
          </div>
        </div>
      </div>
    `];
  }

  /**
   * İnteraktif JavaScript kodları
   */
  static getInteractiveScript() {
    return `
      // Basit slayt navigasyonu
      let currentSlide = 0;
      const slides = document.querySelectorAll('.slide');
      
      function showSlide(n) {
        slides.forEach((slide, index) => {
          slide.style.display = index === n ? 'flex' : 'none';
        });
      }
      
      function nextSlide() {
        currentSlide = (currentSlide + 1) % slides.length;
        showSlide(currentSlide);
      }
      
      function prevSlide() {
        currentSlide = (currentSlide - 1 + slides.length) % slides.length;
        showSlide(currentSlide);
      }
      
      // Klavye kontrolleri
      document.addEventListener('keydown', (e) => {
        if (e.key === 'ArrowRight' || e.key === ' ') {
          nextSlide();
        } else if (e.key === 'ArrowLeft') {
          prevSlide();
        }
      });
      
      // Başlangıçta ilk slaytı göster
      showSlide(0);
      
      // Otomatik geçiş (isteğe bağlı)
      // setInterval(nextSlide, 10000); // 10 saniye
    `;
  }

  /**
   * Dosya adı oluşturur
   * @param {string} title - Rapor başlığı
   * @param {string} type - Rapor türü
   * @returns {string} Dosya adı
   */
  static generateFileName(title, type) {
    const date = new Date();
    const dateStr = date.toISOString().split('T')[0];
    const timeStr = date.toTimeString().split(' ')[0].replace(/:/g, '-');
    
    const cleanTitle = title
      .replace(/[^a-zA-Z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .toLowerCase();
    
    return `${cleanTitle}-presentation-${dateStr}-${timeStr}.html`;
  }

  /**
   * Gelecekte PPTX desteği için placeholder
   * @param {Object} params - PPTX parametreleri
   * @returns {Promise<Object>} PPTX oluşturma sonucu
   */
  static async exportToPPTX(params) {
    // TODO: pptx kütüphanesi entegrasyonu
    // import PptxGenJS from 'pptxgenjs';
    
    console.warn('PPTX export henüz desteklenmiyor. HTML formatı kullanılacak.');
    return this.exportToPowerPoint(params);
  }
}
