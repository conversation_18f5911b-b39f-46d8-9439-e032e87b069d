import React from 'react';
import { SafeAreaView, StatusBar, View, StyleSheet, Text } from 'react-native';
import ReportHeader from './ReportHeader';
import MonthlyIncomeExpenseTemplate from '../Templates/MonthlyIncomeExpenseTemplate';
import CategoryDistributionTemplate from '../Templates/CategoryDistributionTemplate';
import CashFlowTemplate from '../Templates/CashFlowTemplate';
import BudgetVsActualTemplate from '../Templates/BudgetVsActualTemplate';
import BasicSummaryTemplate from '../Templates/BasicSummaryTemplate';
import RegularIncomeTrackingTemplate from '../Templates/RegularIncomeTrackingTemplate';
import OvertimeIncomeTemplate from '../Templates/OvertimeIncomeTemplate';
import { TEMPLATE_TYPES } from '../Templates/TemplateConfig';

/**
 * Aktif Rapor Görünümü
 * Seçilen rapor şablonunun görüntülendiği bileşen
 */
const ActiveReportView = ({ 
  activeReport, 
  theme, 
  onCloseReport, 
  onExport, 
  onSave 
}) => {
  if (!activeReport) return null;

  /**
   * Şablon tipine göre uygun bileşeni render et
   */
  const renderTemplate = () => {
    const templateId = activeReport.template.id;
    const templateConfig = activeReport.template;
    const customParams = activeReport.config || {};

    switch (templateId) {
      case TEMPLATE_TYPES.MONTHLY_INCOME_EXPENSE:
      case 'monthly_income_expense':
        return (
          <MonthlyIncomeExpenseTemplate
            templateConfig={templateConfig}
            customParams={customParams}
            onExport={onExport}
            onSave={onSave}
          />
        );
      
      case TEMPLATE_TYPES.CATEGORY_DISTRIBUTION:
      case 'category_distribution':
        return (
          <CategoryDistributionTemplate
            templateConfig={templateConfig}
            customParams={customParams}
            onExport={onExport}
            onSave={onSave}
          />
        );
      
      case TEMPLATE_TYPES.CASH_FLOW:
      case 'cash_flow':
        return (
          <CashFlowTemplate
            templateConfig={templateConfig}
            customParams={customParams}
            onExport={onExport}
            onSave={onSave}
          />
        );
      
      case TEMPLATE_TYPES.BUDGET_VS_ACTUAL:
      case 'budget_vs_actual':
        return (
          <BudgetVsActualTemplate
            templateConfig={templateConfig}
            customParams={customParams}
            onExport={onExport}
            onSave={onSave}
          />
        );
      
      case TEMPLATE_TYPES.REGULAR_INCOME:
      case 'regular_income':
        return (
          <RegularIncomeTrackingTemplate
            templateConfig={templateConfig}
            customParams={customParams}
            onExport={onExport}
            onSave={onSave}
          />
        );
      
      case TEMPLATE_TYPES.SHIFT_INCOME:
      case 'shift_income':
        return (
          <OvertimeIncomeTemplate
            templateConfig={templateConfig}
            customParams={customParams}
            onExport={onExport}
            onSave={onSave}
          />
        );
      
      case TEMPLATE_TYPES.SUMMARY_OVERVIEW:
      case 'summary_overview':
        return (
          <BasicSummaryTemplate
            templateConfig={templateConfig}
            customParams={customParams}
            onExport={onExport}
            onSave={onSave}
          />
        );
      
      default:
        return (
          <View style={[styles.defaultTemplate, { backgroundColor: theme.SURFACE || theme.colors?.surface || '#f9f9f9' }]}>
            <Text style={[styles.defaultTemplateText, { color: theme.TEXT_PRIMARY }]}>
              📊 Rapor Şablonu
            </Text>
            <Text style={[styles.defaultTemplateSubtext, { color: theme.TEXT_SECONDARY }]}>
              {templateConfig.name || 'Şablon'} hazırlanıyor...
            </Text>
          </View>
        );
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      <StatusBar barStyle={theme.STATUS_BAR_STYLE} backgroundColor={theme.STATUS_BAR_COLOR} />
      
      {/* Rapor Header */}
      <ReportHeader
        title={activeReport.template.name}
        subtitle={activeReport.template.description}
        onBackPress={onCloseReport}
        theme={theme}
      />

      {/* Rapor İçeriği */}
      <View style={styles.reportContent}>
        {renderTemplate()}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  reportContent: {
    flex: 1,
  },
  defaultTemplate: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    margin: 16,
    borderRadius: 12,
  },
  defaultTemplateText: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  defaultTemplateSubtext: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
});

export default ActiveReportView;
