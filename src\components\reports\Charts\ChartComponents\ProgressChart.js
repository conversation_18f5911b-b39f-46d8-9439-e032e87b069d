import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { Progress<PERSON>hart as RNProgress<PERSON><PERSON> } from 'react-native-chart-kit';

const screenWidth = Dimensions.get('window').width;

/**
 * Progress Chart (Donut Chart) component using react-native-chart-kit
 * @param {Object} props - Component props
 * @param {Array} props.data - Chart data
 * @param {Object} props.theme - Theme object
 * @param {string} props.title - Chart title
 * @param {Object} props.config - Chart configuration
 * @returns {JSX.Element} Progress chart component
 */
const ProgressChart = ({ data, theme, title, config = {} }) => {
  /**
   * Get safe theme value
   * @param {string} property - Theme property
   * @param {string} fallback - Fallback value
   * @returns {string} Safe theme value
   */
  const getSafeThemeValue = (property, fallback) => {
    return theme?.[property] || theme?.colors?.[property.toLowerCase()] || fallback;
  };

  const chartConfig = {
    backgroundColor: getSafeThemeValue('BACKGROUND', '#ffffff'),
    backgroundGradientFrom: getSafeThemeValue('BACKGROUND', '#ffffff'),
    backgroundGradientTo: getSafeThemeValue('BACKGROUND', '#ffffff'),
    decimalPlaces: config.decimalPlaces || 2,
    color: (opacity = 1) => getSafeThemeValue('PRIMARY', '#007AFF').replace(')', `, ${opacity})`).replace('#', 'rgba(').replace(/^rgba\((\d+), (\d+), (\d+)/, 'rgba($1, $2, $3'),
    labelColor: (opacity = 1) => getSafeThemeValue('TEXT_PRIMARY', '#333333').replace(')', `, ${opacity})`).replace('#', 'rgba(').replace(/^rgba\((\d+), (\d+), (\d+)/, 'rgba($1, $2, $3'),
    style: {
      borderRadius: 16,
    },
    ...config,
  };

  const defaultData = {
    labels: data?.labels || ['Gelir', 'Gider', 'Yatırım', 'Tasarruf'],
    data: data?.data || [0.8, 0.6, 0.4, 0.9],
  };

  return (
    <View style={[styles.container, { backgroundColor: getSafeThemeValue('BACKGROUND', '#ffffff') }]}>
      {title && (
        <Text style={[styles.title, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
          {title}
        </Text>
      )}
      <RNProgressChart
        data={defaultData}
        width={screenWidth - 32}
        height={220}
        chartConfig={chartConfig}
        style={styles.chart}
        strokeWidth={16}
        radius={32}
        hideLegend={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    borderRadius: 8,
    marginVertical: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
    textAlign: 'center',
  },
  chart: {
    borderRadius: 8,
  },
});

export default ProgressChart;
