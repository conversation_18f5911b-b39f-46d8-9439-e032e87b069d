import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useAppContext } from '../context/AppContext';

/**
 * Görünüm ayarları ekranı - Dark mode desteği ile
 * @returns {JSX.Element} AppearanceSettingsScreen bileşeni
 */
export default function AppearanceSettingsScreen() {
  const insets = useSafeAreaInsets();
  const { theme, themePreference, isDarkMode, toggleTheme } = useAppContext();

  // Tema değiştirme işlevi
  const handleChangeTheme = (newTheme) => {
    if (newTheme === themePreference) return;

    toggleTheme(newTheme);
    Alert.alert(
      '<PERSON><PERSON>',
      `Tema ${newTheme === 'dark' ? 'karanlık' : newTheme === 'light' ? 'açık' : 'sistem'} moda ayarlandı.`,
      [{ text: 'Tamam' }]
    );
  };

  return (
    <ScrollView style={[styles.container, { paddingTop: insets.top, backgroundColor: theme.BACKGROUND }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.PRIMARY }]}>
        <Text style={[styles.title, { color: theme.WHITE }]}>
          Görünüm Ayarları
        </Text>
        <Text style={[styles.subtitle, { color: theme.WHITE + 'CC' }]}>
          Uygulamanın görünümünü özelleştirin
        </Text>
      </View>

      {/* Tema Seçimi */}
      <View style={[styles.section, { backgroundColor: theme.CARD }]}>
        <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
          Tema Seçimi
        </Text>

        <View style={styles.themeOptions}>
          {/* Açık Tema */}
          <TouchableOpacity
            style={[
              styles.themeOption,
              { backgroundColor: theme.SURFACE, borderColor: theme.BORDER },
              themePreference === 'light' && { borderColor: theme.PRIMARY, borderWidth: 2 }
            ]}
            onPress={() => handleChangeTheme('light')}
          >
            <View style={[styles.themePreview, styles.lightThemePreview]}>
              <View style={[styles.previewHeader, { backgroundColor: '#6c5ce7' }]} />
              <View style={[styles.previewContent, { backgroundColor: '#fff' }]} />
            </View>
            <View style={styles.themeInfo}>
              <MaterialIcons 
                name="wb-sunny" 
                size={24} 
                color={themePreference === 'light' ? theme.PRIMARY : theme.TEXT_SECONDARY} 
              />
              <Text style={[
                styles.themeTitle, 
                { color: themePreference === 'light' ? theme.PRIMARY : theme.TEXT_PRIMARY }
              ]}>
                Açık Tema
              </Text>
            </View>
          </TouchableOpacity>

          {/* Karanlık Tema */}
          <TouchableOpacity
            style={[
              styles.themeOption,
              { backgroundColor: theme.SURFACE, borderColor: theme.BORDER },
              themePreference === 'dark' && { borderColor: theme.PRIMARY, borderWidth: 2 }
            ]}
            onPress={() => handleChangeTheme('dark')}
          >
            <View style={[styles.themePreview, styles.darkThemePreview]}>
              <View style={[styles.previewHeader, { backgroundColor: '#339cff' }]} />
              <View style={[styles.previewContent, { backgroundColor: '#2d2d2d' }]} />
            </View>
            <View style={styles.themeInfo}>
              <MaterialIcons 
                name="nights-stay" 
                size={24} 
                color={themePreference === 'dark' ? theme.PRIMARY : theme.TEXT_SECONDARY} 
              />
              <Text style={[
                styles.themeTitle, 
                { color: themePreference === 'dark' ? theme.PRIMARY : theme.TEXT_PRIMARY }
              ]}>
                Karanlık Tema
              </Text>
            </View>
          </TouchableOpacity>

          {/* Sistem Teması */}
          <TouchableOpacity
            style={[
              styles.themeOption,
              styles.fullWidthOption,
              { backgroundColor: theme.SURFACE, borderColor: theme.BORDER },
              themePreference === 'system' && { borderColor: theme.PRIMARY, borderWidth: 2 }
            ]}
            onPress={() => handleChangeTheme('system')}
          >
            <View style={[styles.themePreview, styles.systemThemePreview]}>
              <View style={styles.systemPreviewContainer}>
                <View style={[styles.previewHeader, { backgroundColor: '#6c5ce7' }]} />
                <View style={[styles.previewContent, { backgroundColor: '#fff' }]} />
              </View>
              <View style={styles.systemPreviewContainer}>
                <View style={[styles.previewHeader, { backgroundColor: '#339cff' }]} />
                <View style={[styles.previewContent, { backgroundColor: '#2d2d2d' }]} />
              </View>
            </View>
            <View style={styles.themeInfo}>
              <MaterialIcons 
                name="settings-suggest" 
                size={24} 
                color={themePreference === 'system' ? theme.PRIMARY : theme.TEXT_SECONDARY} 
              />
              <Text style={[
                styles.themeTitle, 
                { color: themePreference === 'system' ? theme.PRIMARY : theme.TEXT_PRIMARY }
              ]}>
                Sistem Teması
              </Text>
              <Text style={[styles.themeDescription, { color: theme.TEXT_SECONDARY }]}>
                Cihaz ayarlarınıza göre otomatik
              </Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>

      {/* Mevcut Tema Bilgisi */}
      <View style={[styles.section, { backgroundColor: theme.CARD }]}>
        <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
          Mevcut Durum
        </Text>
        
        <View style={[styles.statusRow, { backgroundColor: theme.SURFACE }]}>
          <MaterialIcons 
            name={isDarkMode ? "nights-stay" : "wb-sunny"} 
            size={24} 
            color={theme.PRIMARY} 
          />
          <View style={styles.statusInfo}>
            <Text style={[styles.statusTitle, { color: theme.TEXT_PRIMARY }]}>
              {isDarkMode ? 'Karanlık Tema' : 'Açık Tema'} Aktif
            </Text>
            <Text style={[styles.statusDescription, { color: theme.TEXT_SECONDARY }]}>
              Tema Tercihi: {
                themePreference === 'light' ? 'Açık' : 
                themePreference === 'dark' ? 'Karanlık' : 'Sistem'
              }
            </Text>
          </View>
        </View>
      </View>

      {/* Tema Avantajları */}
      <View style={[styles.section, { backgroundColor: theme.CARD }]}>
        <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
          Tema Hakkında
        </Text>
        
        <View style={styles.infoList}>
          <View style={styles.infoItem}>
            <MaterialIcons name="visibility" size={20} color={theme.PRIMARY} />
            <Text style={[styles.infoText, { color: theme.TEXT_PRIMARY }]}>
              Göz yorgunluğunu azaltır
            </Text>
          </View>
          
          <View style={styles.infoItem}>
            <MaterialIcons name="battery-saver" size={20} color={theme.PRIMARY} />
            <Text style={[styles.infoText, { color: theme.TEXT_PRIMARY }]}>
              Karanlık tema pil tasarrufu sağlar
            </Text>
          </View>
          
          <View style={styles.infoItem}>
            <MaterialIcons name="accessibility" size={20} color={theme.PRIMARY} />
            <Text style={[styles.infoText, { color: theme.TEXT_PRIMARY }]}>
              Erişilebilirliği artırır
            </Text>
          </View>
          
          <View style={styles.infoItem}>
            <MaterialIcons name="auto-awesome" size={20} color={theme.PRIMARY} />
            <Text style={[styles.infoText, { color: theme.TEXT_PRIMARY }]}>
              Sistem teması otomatik geçiş sağlar
            </Text>
          </View>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 24,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
  },
  section: {
    marginHorizontal: 16,
    marginTop: 16,
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  themeOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  themeOption: {
    width: '48%',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 12,
    alignItems: 'center',
  },
  fullWidthOption: {
    width: '100%',
  },
  themePreview: {
    width: 80,
    height: 60,
    borderRadius: 8,
    overflow: 'hidden',
    marginBottom: 12,
  },
  previewHeader: {
    height: 20,
  },
  previewContent: {
    flex: 1,
    padding: 8,
  },
  systemThemePreview: {
    flexDirection: 'row',
    width: 120,
  },
  systemPreviewContainer: {
    flex: 1,
    marginHorizontal: 2,
    borderRadius: 6,
    overflow: 'hidden',
  },
  themeInfo: {
    alignItems: 'center',
  },
  themeTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginTop: 4,
  },
  themeDescription: {
    fontSize: 12,
    textAlign: 'center',
    marginTop: 2,
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
  },
  statusInfo: {
    marginLeft: 12,
    flex: 1,
  },
  statusTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  statusDescription: {
    fontSize: 14,
    marginTop: 2,
  },
  infoList: {
    marginTop: 8,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoText: {
    fontSize: 14,
    marginLeft: 12,
    flex: 1,
  },
});
