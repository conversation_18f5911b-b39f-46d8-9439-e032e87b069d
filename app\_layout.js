import React, { useState, useEffect, useCallback } from 'react';
import { View, StatusBar } from 'react-native';
import { SplashScreen, Stack } from 'expo-router';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { SQLiteProvider } from 'expo-sqlite';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import '../src/utils/reanimatedInit';
import { initializeDatabase } from '../src/db/initialMigration';
import { Colors } from '../src/constants/colors';
import { ExchangeRateProvider } from '../src/context/ExchangeRateContext';
import { AppProvider } from '../src/context/AppContext';
import * as notificationService from '../src/services/NotificationService';
import * as notificationScheduler from '../src/services/notificationScheduler';
import { initShiftBackgroundService } from '../src/services/shiftBackgroundService';

// Artık Colors doğrudan constants/colors.js'den içe aktarılıyor

// Splash screen yönetimi
SplashScreen.preventAutoHideAsync()
  .catch(e => console.warn('Splash screen prevention failed:', e));

/**
 * Veritabanını başlatan ve veritabanı şemasını yöneten fonksiyon
 *
 * @param {SQLiteDatabase} db - SQLite veritabanı nesnesi
 * @returns {Promise<void>}
 */
const initDatabase = async (db) => {
  try {
    // Veritabanı şemasını oluştur
    await initializeDatabase(db);

    // Gerekli tabloların varlığını doğrula
    const tables = ['categories', 'transactions'];
    for (const table of tables) {
      const hasTable = await db.getFirstAsync(`
        SELECT name FROM sqlite_master
        WHERE type='table' AND name=?
      `, [table]);

      if (!hasTable) {
        console.error(`Kritik tablo bulunamadı: ${table}`);
      }
    }
  } catch (error) {
    console.error('Veritabanı başlatma hatası:', error);
  }
};

/**
 * Ana uygulama layout'u
 * Tüm uygulama için temel yapıyı ve sağlayıcıları içerir
 *
 * @returns {JSX.Element} Ana layout bileşeni
 */
export default function RootLayout() {
  const [appIsReady, setAppIsReady] = useState(false);
  useEffect(() => {
    /**
     * Uygulamayı başlatmaya hazırlar
     */
    const prepareApp = async () => {
      try {
        // Notification servisleri başlat
        await notificationService.initNotifications();
        await notificationScheduler.initNotificationScheduler();
        
        // Vardiya arka plan servisi başlat
        await initShiftBackgroundService();
        
        // Gerekli inizalizasyon işlemleri
        await new Promise(resolve => setTimeout(resolve, 500));
      } catch (error) {
        console.warn('Uygulama hazırlık hatası:', error);
      } finally {
        setAppIsReady(true);
      }
    };

    prepareApp();
  }, []);

  const onLayoutRootView = useCallback(async () => {
    if (appIsReady) {
      try {
        await SplashScreen.hideAsync();
      } catch (e) {
        console.warn('Splash screen hide failed:', e);
      }
    }
  }, [appIsReady]);

  if (!appIsReady) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#f5f5f5' }}>
        {/* Splash screen gösterilirken boş bir view döndür */}
      </View>
    );
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <SafeAreaProvider>
        <SQLiteProvider
          databaseName="maas.db"
          onInit={initDatabase}
        >
          <RootLayoutContent onLayoutRootView={onLayoutRootView} />
        </SQLiteProvider>
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
}

/**
 * Ana düzen bileşeni
 */
function RootLayoutContent({ onLayoutRootView }) {
  return (
    <ExchangeRateProvider>
      <AppProvider>
        <View style={{ flex: 1, backgroundColor: '#f5f5f5' }} onLayout={onLayoutRootView}>
          <StatusBar
            barStyle="dark-content"
            backgroundColor="#f5f5f5"
          />
          <Stack screenOptions={{
            headerShown: false,
            contentStyle: { backgroundColor: '#f5f5f5' }
          }}>
            <Stack.Screen name="auth" options={{ headerShown: false }} />
            <Stack.Screen name="pin" options={{ headerShown: false }} />

            <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
            <Stack.Screen
              name="AppearanceSettings"
              options={{
                headerShown: true,
                title: "Görünüm Ayarları",
                headerStyle: { backgroundColor: '#fff' },
                headerTintColor: '#333',
              }}
            />
            <Stack.Screen
              name="TabBarCustomization"
              options={{
                headerShown: true,
                title: "Alt Navigasyon Çubuğu",
                headerStyle: { backgroundColor: '#fff' },
                headerTintColor: '#333',
              }}
            />

            <Stack.Screen
              name="category/edit"
              options={{
                headerShown: false,
                title: "Kategori Ekle/Düzenle",
                headerStyle: { backgroundColor: '#fff' },
                headerTintColor: '#333',
              }}
            />
            
            {/* Modal screens */}
            <Stack.Screen
              name="modal"
              options={{
                presentation: 'modal',
                headerShown: false,
              }}
            />
          </Stack>
        </View>
      </AppProvider>
    </ExchangeRateProvider>
  );
}
