import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  Modal,
  TextInput,
  ScrollView
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../../constants/colors';
import DateTimePicker from '@react-native-community/datetimepicker';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import CategorySelector from './CategorySelector';

/**
 * İşlem filtreleme bileşeni
 * 
 * @param {Object} props - Bileşen props'ları
 * @param {Object} props.filters - Filtre değerleri
 * @param {Function} props.onApplyFilters - Filtreler uygulandığında çağrılacak fonksiyon
 * @param {Function} props.onClose - Modal kapatıldığında çağrılacak fonksiyon
 * @param {Array} props.categories - Kategoriler listesi
 * @returns {JSX.Element} İşlem filtreleme bileşeni
 */
const TransactionFilter = ({ 
  filters = {}, 
  onApplyFilters, 
  onClose,
  categories = []
}) => {
  const [filterValues, setFilterValues] = useState({
    startDate: filters.startDate ? new Date(filters.startDate) : null,
    endDate: filters.endDate ? new Date(filters.endDate) : null,
    categoryId: filters.categoryId,
    minAmount: filters.minAmount ? filters.minAmount.toString() : '',
    maxAmount: filters.maxAmount ? filters.maxAmount.toString() : ''
  });
  
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [showCategorySelector, setShowCategorySelector] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(
    categories.find(c => c.id === filters.categoryId) || null
  );
  
  // Tarihi formatla
  const formatDate = (date) => {
    if (!date) return '';
    return format(date, 'd MMMM yyyy', { locale: tr });
  };
  
  // Başlangıç tarihi değiştiğinde
  const handleStartDateChange = (event, selectedDate) => {
    setShowStartDatePicker(false);
    
    if (selectedDate) {
      setFilterValues({ ...filterValues, startDate: selectedDate });
    }
  };
  
  // Bitiş tarihi değiştiğinde
  const handleEndDateChange = (event, selectedDate) => {
    setShowEndDatePicker(false);
    
    if (selectedDate) {
      setFilterValues({ ...filterValues, endDate: selectedDate });
    }
  };
  
  // Kategori seçildiğinde
  const handleCategorySelect = (category) => {
    setSelectedCategory(category);
    setFilterValues({ ...filterValues, categoryId: category.id });
    setShowCategorySelector(false);
  };
  
  // Filtreleri temizle
  const handleClearFilters = () => {
    setFilterValues({
      startDate: null,
      endDate: null,
      categoryId: null,
      minAmount: '',
      maxAmount: ''
    });
    setSelectedCategory(null);
  };
  
  // Filtreleri uygula
  const handleApplyFilters = () => {
    const filters = {
      startDate: filterValues.startDate ? filterValues.startDate.toISOString().split('T')[0] : null,
      endDate: filterValues.endDate ? filterValues.endDate.toISOString().split('T')[0] : null,
      categoryId: filterValues.categoryId,
      minAmount: filterValues.minAmount ? parseFloat(filterValues.minAmount) : null,
      maxAmount: filterValues.maxAmount ? parseFloat(filterValues.maxAmount) : null
    };
    
    onApplyFilters(filters);
  };
  
  return (
    <Modal
      visible={true}
      animationType="slide"
      transparent={false}
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Başlık */}
        <View style={styles.header}>
          <TouchableOpacity 
            style={styles.closeButton}
            onPress={onClose}
          >
            <MaterialIcons name="close" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.title}>Filtreleme</Text>
          <TouchableOpacity 
            style={styles.clearButton}
            onPress={handleClearFilters}
          >
            <Text style={styles.clearButtonText}>Temizle</Text>
          </TouchableOpacity>
        </View>
        
        <ScrollView style={styles.content}>
          {/* Tarih Aralığı */}
          <View style={styles.filterGroup}>
            <Text style={styles.filterLabel}>Tarih Aralığı</Text>
            
            <View style={styles.dateRangeContainer}>
              <TouchableOpacity 
                style={styles.dateSelector}
                onPress={() => setShowStartDatePicker(true)}
              >
                <MaterialIcons name="calendar-today" size={20} color="#757575" />
                <Text style={[
                  styles.dateText,
                  !filterValues.startDate && styles.placeholderText
                ]}>
                  {filterValues.startDate ? formatDate(filterValues.startDate) : 'Başlangıç'}
                </Text>
              </TouchableOpacity>
              
              <Text style={styles.dateRangeSeparator}>-</Text>
              
              <TouchableOpacity 
                style={styles.dateSelector}
                onPress={() => setShowEndDatePicker(true)}
              >
                <MaterialIcons name="calendar-today" size={20} color="#757575" />
                <Text style={[
                  styles.dateText,
                  !filterValues.endDate && styles.placeholderText
                ]}>
                  {filterValues.endDate ? formatDate(filterValues.endDate) : 'Bitiş'}
                </Text>
              </TouchableOpacity>
            </View>
            
            {showStartDatePicker && (
              <DateTimePicker
                value={filterValues.startDate || new Date()}
                mode="date"
                display="default"
                onChange={handleStartDateChange}
                maximumDate={filterValues.endDate || new Date()}
              />
            )}
            
            {showEndDatePicker && (
              <DateTimePicker
                value={filterValues.endDate || new Date()}
                mode="date"
                display="default"
                onChange={handleEndDateChange}
                minimumDate={filterValues.startDate}
                maximumDate={new Date()}
              />
            )}
          </View>
          
          {/* Kategori */}
          <View style={styles.filterGroup}>
            <Text style={styles.filterLabel}>Kategori</Text>
            
            <TouchableOpacity 
              style={styles.categorySelector}
              onPress={() => setShowCategorySelector(true)}
            >
              {selectedCategory ? (
                <View style={styles.selectedCategory}>
                  <View 
                    style={[
                      styles.categoryIcon, 
                      { backgroundColor: `${selectedCategory.color}20` }
                    ]}
                  >
                    <MaterialIcons 
                      name={selectedCategory.icon || 'category'} 
                      size={24} 
                      color={selectedCategory.color} 
                    />
                  </View>
                  <Text style={styles.categoryName}>{selectedCategory.name}</Text>
                </View>
              ) : (
                <Text style={styles.placeholderText}>Tüm Kategoriler</Text>
              )}
              <MaterialIcons name="chevron-right" size={24} color="#757575" />
            </TouchableOpacity>
          </View>
          
          {/* Tutar Aralığı */}
          <View style={styles.filterGroup}>
            <Text style={styles.filterLabel}>Tutar Aralığı</Text>
            
            <View style={styles.amountRangeContainer}>
              <View style={styles.amountInput}>
                <Text style={styles.currencySymbol}>₺</Text>
                <TextInput
                  style={styles.input}
                  value={filterValues.minAmount}
                  onChangeText={(text) => setFilterValues({ 
                    ...filterValues, 
                    minAmount: text.replace(/[^0-9.]/g, '') 
                  })}
                  placeholder="Min"
                  placeholderTextColor="#999"
                  keyboardType="numeric"
                />
              </View>
              
              <Text style={styles.amountRangeSeparator}>-</Text>
              
              <View style={styles.amountInput}>
                <Text style={styles.currencySymbol}>₺</Text>
                <TextInput
                  style={styles.input}
                  value={filterValues.maxAmount}
                  onChangeText={(text) => setFilterValues({ 
                    ...filterValues, 
                    maxAmount: text.replace(/[^0-9.]/g, '') 
                  })}
                  placeholder="Max"
                  placeholderTextColor="#999"
                  keyboardType="numeric"
                />
              </View>
            </View>
          </View>
        </ScrollView>
        
        {/* Uygula Butonu */}
        <View style={styles.footer}>
          <TouchableOpacity 
            style={styles.applyButton}
            onPress={handleApplyFilters}
          >
            <Text style={styles.applyButtonText}>Filtreleri Uygula</Text>
          </TouchableOpacity>
        </View>
        
        {/* Kategori Seçici Modal */}
        {showCategorySelector && (
          <CategorySelector 
            categories={categories}
            selectedCategoryId={filterValues.categoryId}
            onSelect={handleCategorySelect}
            onClose={() => setShowCategorySelector(false)}
          />
        )}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  closeButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  clearButton: {
    padding: 8,
  },
  clearButtonText: {
    color: Colors.PRIMARY,
    fontWeight: '500',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  filterGroup: {
    marginBottom: 24,
  },
  filterLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 12,
  },
  dateRangeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateSelector: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  dateText: {
    flex: 1,
    fontSize: 14,
    color: '#333',
    marginLeft: 8,
  },
  dateRangeSeparator: {
    marginHorizontal: 8,
    fontSize: 16,
    color: '#757575',
  },
  categorySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  selectedCategory: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  categoryName: {
    fontSize: 14,
    color: '#333',
  },
  amountRangeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  amountInput: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    paddingHorizontal: 12,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  currencySymbol: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginRight: 4,
  },
  input: {
    flex: 1,
    fontSize: 14,
    color: '#333',
    paddingVertical: 12,
  },
  amountRangeSeparator: {
    marginHorizontal: 8,
    fontSize: 16,
    color: '#757575',
  },
  placeholderText: {
    color: '#999',
  },
  footer: {
    backgroundColor: '#fff',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  applyButton: {
    backgroundColor: Colors.PRIMARY,
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
  },
  applyButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default TransactionFilter;
