/**
 * <PERSON><PERSON><PERSON> Detay Raporu Komponenti
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md - Stage 4, Phase 2
 * 
 * <PERSON>irli bir kategorinin detaylı analizi ve raporu
 * Maksimum 250 satır - Tek sorumluluk prensibi
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../../../context/ThemeContext';
import { formatTRY, formatUSD, formatEUR } from '../../../utils/formatters';

/**
 * <PERSON>gori detay raporu komponenti
 * @param {Object} props - Component props
 * @param {Object} props.categoryData - Kategori verileri
 * @param {Array} props.transactions - İşlem listesi
 * @param {string} props.currency - Para birimi ('TRY', 'USD', 'EUR')
 * @param {string} props.period - <PERSON>or dönemi
 * @param {Function} props.onTransactionPress - <PERSON><PERSON><PERSON> tıklama callback
 * @param {Object} props.theme - <PERSON><PERSON> obje<PERSON> (opsiyonel, context'ten alınır)
 */
const CategoryDetailReport = ({ 
  categoryData = {}, 
  transactions = [],
  currency = 'TRY',
  period = 'Bu Ay',
  onTransactionPress,
  theme: propTheme 
}) => {
  const { theme, isDarkMode } = useTheme();
  const currentTheme = propTheme || theme;

  /**
   * Para birimi formatı
   * @param {number} value - Formatlanacak değer
   * @returns {string} Formatlanmış değer
   */
  const formatCurrency = (value) => {
    switch (currency) {
      case 'USD':
        return formatUSD(value);
      case 'EUR':
        return formatEUR(value);
      default:
        return formatTRY(value);
    }
  };

  /**
   * Para birimi sembolü
   * @returns {string} Para birimi sembolü
   */
  const getCurrencySymbol = () => {
    switch (currency) {
      case 'USD':
        return '$';
      case 'EUR':
        return '€';
      default:
        return '₺';
    }
  };

  /**
   * Tarih formatı
   * @param {string} dateString - Tarih string
   * @returns {string} Formatlanmış tarih
   */
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return `${date.getDate().toString().padStart(2, '0')}.${(date.getMonth() + 1).toString().padStart(2, '0')}.${date.getFullYear()}`;
  };

  /**
   * Varsayılan kategori verileri
   */
  const defaultCategory = {
    id: '',
    name: 'Kategori',
    icon: 'category',
    budget: 0,
    spent: 0,
    remaining: 0,
    utilizationRate: 0,
    transactionCount: 0,
    averageTransaction: 0,
    largestTransaction: 0,
    dailyAverage: 0,
    weeklyTrend: 0,
    monthlyComparison: 0
  };

  const category = { ...defaultCategory, ...categoryData };
  const currencySymbol = getCurrencySymbol();

  // Kategori durumu
  const isOverBudget = category.spent > category.budget;
  const statusColor = isOverBudget 
    ? currentTheme.ERROR 
    : category.utilizationRate > 90 
      ? currentTheme.WARNING 
      : currentTheme.SUCCESS;

  // Son işlemler (en fazla 10)
  const recentTransactions = transactions.slice(0, 10);

  // Haftalık trend
  const trendIcon = category.weeklyTrend > 0 ? 'trending-up' : 
                   category.weeklyTrend < 0 ? 'trending-down' : 'trending-flat';
  const trendColor = category.weeklyTrend > 0 ? currentTheme.WARNING : 
                    category.weeklyTrend < 0 ? currentTheme.SUCCESS : currentTheme.INFO;

  return (
    <View style={[styles.container, { backgroundColor: currentTheme.SURFACE }]}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <MaterialIcons name={category.icon} size={24} color={currentTheme.PRIMARY} />
          <View style={styles.headerText}>
            <Text style={[styles.title, { color: currentTheme.TEXT_PRIMARY }]}>
              {category.name}
            </Text>
            <Text style={[styles.period, { color: currentTheme.TEXT_SECONDARY }]}>
              {period} Raporu
            </Text>
          </View>
        </View>
        
        <View style={[styles.statusBadge, { backgroundColor: statusColor + '20' }]}>
          <MaterialIcons 
            name={isOverBudget ? 'warning' : 'check-circle'} 
            size={16} 
            color={statusColor} 
          />
          <Text style={[styles.statusText, { color: statusColor }]}>
            {isOverBudget ? 'Aşım' : 'Normal'}
          </Text>
        </View>
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Ana metrikler */}
        <View style={styles.metricsSection}>
          <View style={styles.metricsGrid}>
            <View style={styles.metricCard}>
              <Text style={[styles.metricValue, { color: currentTheme.TEXT_PRIMARY }]}>
                {currencySymbol}{category.spent.toLocaleString('tr-TR')}
              </Text>
              <Text style={[styles.metricLabel, { color: currentTheme.TEXT_SECONDARY }]}>
                Toplam Harcama
              </Text>
            </View>

            <View style={styles.metricCard}>
              <Text style={[styles.metricValue, { color: currentTheme.TEXT_PRIMARY }]}>
                {currencySymbol}{category.budget.toLocaleString('tr-TR')}
              </Text>
              <Text style={[styles.metricLabel, { color: currentTheme.TEXT_SECONDARY }]}>
                Bütçe Limiti
              </Text>
            </View>

            <View style={styles.metricCard}>
              <Text style={[
                styles.metricValue, 
                { color: isOverBudget ? currentTheme.ERROR : currentTheme.SUCCESS }
              ]}>
                {currencySymbol}{Math.abs(category.remaining).toLocaleString('tr-TR')}
              </Text>
              <Text style={[styles.metricLabel, { color: currentTheme.TEXT_SECONDARY }]}>
                {isOverBudget ? 'Aşım' : 'Kalan'}
              </Text>
            </View>

            <View style={styles.metricCard}>
              <Text style={[styles.metricValue, { color: statusColor }]}>
                %{category.utilizationRate.toFixed(1)}
              </Text>
              <Text style={[styles.metricLabel, { color: currentTheme.TEXT_SECONDARY }]}>
                Kullanım Oranı
              </Text>
            </View>
          </View>
        </View>

        {/* Kullanım çubuğu */}
        <View style={styles.utilizationSection}>
          <Text style={[styles.sectionTitle, { color: currentTheme.TEXT_PRIMARY }]}>
            Bütçe Kullanımı
          </Text>
          
          <View style={[styles.progressTrack, { backgroundColor: currentTheme.BORDER }]}>
            <View 
              style={[
                styles.progressFill,
                {
                  backgroundColor: statusColor,
                  width: `${Math.min(category.utilizationRate, 100)}%`
                }
              ]} 
            />
          </View>
          
          <View style={styles.utilizationLabels}>
            <Text style={[styles.utilizationText, { color: currentTheme.TEXT_SECONDARY }]}>
              {currencySymbol}{category.spent.toLocaleString('tr-TR')}
            </Text>
            <Text style={[styles.utilizationText, { color: currentTheme.TEXT_SECONDARY }]}>
              {currencySymbol}{category.budget.toLocaleString('tr-TR')}
            </Text>
          </View>
        </View>

        {/* İstatistikler */}
        <View style={styles.statsSection}>
          <Text style={[styles.sectionTitle, { color: currentTheme.TEXT_PRIMARY }]}>
            İstatistikler
          </Text>
          
          <View style={styles.statsGrid}>
            <View style={styles.statItem}>
              <MaterialIcons name="receipt" size={20} color={currentTheme.INFO} />
              <Text style={[styles.statValue, { color: currentTheme.TEXT_PRIMARY }]}>
                {category.transactionCount}
              </Text>
              <Text style={[styles.statLabel, { color: currentTheme.TEXT_SECONDARY }]}>
                İşlem Sayısı
              </Text>
            </View>

            <View style={styles.statItem}>
              <MaterialIcons name="calculate" size={20} color={currentTheme.PRIMARY} />
              <Text style={[styles.statValue, { color: currentTheme.TEXT_PRIMARY }]}>
                {currencySymbol}{category.averageTransaction.toLocaleString('tr-TR')}
              </Text>
              <Text style={[styles.statLabel, { color: currentTheme.TEXT_SECONDARY }]}>
                Ortalama İşlem
              </Text>
            </View>

            <View style={styles.statItem}>
              <MaterialIcons name="trending-up" size={20} color={currentTheme.WARNING} />
              <Text style={[styles.statValue, { color: currentTheme.TEXT_PRIMARY }]}>
                {currencySymbol}{category.largestTransaction.toLocaleString('tr-TR')}
              </Text>
              <Text style={[styles.statLabel, { color: currentTheme.TEXT_SECONDARY }]}>
                En Büyük İşlem
              </Text>
            </View>

            <View style={styles.statItem}>
              <MaterialIcons name="today" size={20} color={currentTheme.SUCCESS} />
              <Text style={[styles.statValue, { color: currentTheme.TEXT_PRIMARY }]}>
                {currencySymbol}{category.dailyAverage.toLocaleString('tr-TR')}
              </Text>
              <Text style={[styles.statLabel, { color: currentTheme.TEXT_SECONDARY }]}>
                Günlük Ortalama
              </Text>
            </View>
          </View>
        </View>

        {/* Trend analizi */}
        <View style={styles.trendSection}>
          <Text style={[styles.sectionTitle, { color: currentTheme.TEXT_PRIMARY }]}>
            Trend Analizi
          </Text>
          
          <View style={[styles.trendCard, { backgroundColor: trendColor + '20' }]}>
            <MaterialIcons name={trendIcon} size={24} color={trendColor} />
            <View style={styles.trendContent}>
              <Text style={[styles.trendTitle, { color: currentTheme.TEXT_PRIMARY }]}>
                Haftalık Trend
              </Text>
              <Text style={[styles.trendValue, { color: trendColor }]}>
                {category.weeklyTrend > 0 ? '+' : ''}{category.weeklyTrend.toFixed(1)}%
              </Text>
              <Text style={[styles.trendDescription, { color: currentTheme.TEXT_SECONDARY }]}>
                {category.weeklyTrend > 0 
                  ? 'Harcamalar artış gösteriyor'
                  : category.weeklyTrend < 0 
                    ? 'Harcamalar azalış gösteriyor'
                    : 'Harcamalar stabil'
                }
              </Text>
            </View>
          </View>
        </View>

        {/* Son işlemler */}
        {recentTransactions.length > 0 && (
          <View style={styles.transactionsSection}>
            <Text style={[styles.sectionTitle, { color: currentTheme.TEXT_PRIMARY }]}>
              Son İşlemler
            </Text>
            
            {recentTransactions.map((transaction, index) => (
              <TouchableOpacity
                key={transaction.id || index}
                style={styles.transactionItem}
                onPress={() => onTransactionPress && onTransactionPress(transaction)}
                activeOpacity={0.7}
              >
                <View style={styles.transactionLeft}>
                  <MaterialIcons 
                    name={transaction.icon || 'receipt'} 
                    size={20} 
                    color={currentTheme.TEXT_PRIMARY} 
                  />
                  <View style={styles.transactionInfo}>
                    <Text style={[styles.transactionDescription, { color: currentTheme.TEXT_PRIMARY }]}>
                      {transaction.description || 'İşlem'}
                    </Text>
                    <Text style={[styles.transactionDate, { color: currentTheme.TEXT_SECONDARY }]}>
                      {formatDate(transaction.date)}
                    </Text>
                  </View>
                </View>
                
                <Text style={[styles.transactionAmount, { color: currentTheme.TEXT_PRIMARY }]}>
                  {currencySymbol}{transaction.amount.toLocaleString('tr-TR')}
                </Text>
              </TouchableOpacity>
            ))}
            
            {transactions.length > 10 && (
              <TouchableOpacity style={styles.showMoreButton}>
                <Text style={[styles.showMoreText, { color: currentTheme.PRIMARY }]}>
                  Tüm İşlemleri Görüntüle ({transactions.length - 10} daha)
                </Text>
              </TouchableOpacity>
            )}
          </View>
        )}

        {/* Öneriler */}
        <View style={styles.recommendationsSection}>
          <Text style={[styles.sectionTitle, { color: currentTheme.TEXT_PRIMARY }]}>
            Öneriler
          </Text>
          
          {isOverBudget && (
            <View style={[styles.recommendationItem, { backgroundColor: currentTheme.ERROR + '10' }]}>
              <MaterialIcons name="warning" size={16} color={currentTheme.ERROR} />
              <Text style={[styles.recommendationText, { color: currentTheme.TEXT_SECONDARY }]}>
                Bu kategoride bütçe aşımı var. Harcamalarınızı gözden geçirin.
              </Text>
            </View>
          )}

          {category.utilizationRate > 90 && !isOverBudget && (
            <View style={[styles.recommendationItem, { backgroundColor: currentTheme.WARNING + '10' }]}>
              <MaterialIcons name="info" size={16} color={currentTheme.WARNING} />
              <Text style={[styles.recommendationText, { color: currentTheme.TEXT_SECONDARY }]}>
                Bütçe sınırına yaklaşıyorsunuz. Dikkatli harcama yapın.
              </Text>
            </View>
          )}

          {category.weeklyTrend > 10 && (
            <View style={[styles.recommendationItem, { backgroundColor: currentTheme.INFO + '10' }]}>
              <MaterialIcons name="trending-up" size={16} color={currentTheme.INFO} />
              <Text style={[styles.recommendationText, { color: currentTheme.TEXT_SECONDARY }]}>
                Bu kategorideki harcamalarınız artış gösteriyor. Trend analizi yapın.
              </Text>
            </View>
          )}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 12,
  },
  headerText: {
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  period: {
    fontSize: 12,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  metricsSection: {
    marginBottom: 20,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  metricCard: {
    width: '48%',
    alignItems: 'center',
    marginBottom: 12,
  },
  metricValue: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  metricLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  utilizationSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 12,
  },
  progressTrack: {
    height: 8,
    borderRadius: 4,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  utilizationLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  utilizationText: {
    fontSize: 12,
  },
  statsSection: {
    marginBottom: 20,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statItem: {
    width: '48%',
    alignItems: 'center',
    marginBottom: 12,
  },
  statValue: {
    fontSize: 14,
    fontWeight: 'bold',
    marginVertical: 4,
  },
  statLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  trendSection: {
    marginBottom: 20,
  },
  trendCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 12,
  },
  trendContent: {
    flex: 1,
  },
  trendTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 2,
  },
  trendValue: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  trendDescription: {
    fontSize: 12,
  },
  transactionsSection: {
    marginBottom: 20,
  },
  transactionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  transactionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 8,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionDescription: {
    fontSize: 14,
    marginBottom: 2,
  },
  transactionDate: {
    fontSize: 12,
  },
  transactionAmount: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  showMoreButton: {
    alignItems: 'center',
    paddingVertical: 8,
  },
  showMoreText: {
    fontSize: 14,
    fontWeight: '500',
  },
  recommendationsSection: {
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
    paddingTop: 16,
  },
  recommendationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    gap: 8,
  },
  recommendationText: {
    fontSize: 13,
    flex: 1,
    lineHeight: 18,
  },
});

export default CategoryDetailReport;
