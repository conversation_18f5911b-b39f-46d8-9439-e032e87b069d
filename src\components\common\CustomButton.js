import React from 'react';
import {
    TouchableOpacity,
    Text,
    ActivityIndicator,
    StyleSheet,
    TouchableOpacityProps,
    ViewStyle,
    TextStyle
} from 'react-native';

/**
 * Özel buton bileşeni özellikleri
 * @typedef {Object} CustomButtonProps
 * @property {string} title - Buton üzerinde gösterilecek metin
 * @property {() => void} onPress - Butona tıklandığında çalışacak fonksiyon
 * @property {boolean} [disabled] - But<PERSON>un devre dışı olup olmadığı
 * @property {boolean} [loading] - Yükleniyor durumu
 * @property {ViewStyle} [style] - Buton için özel stiller
 * @property {TextStyle} [textStyle] - Buton metni için özel stiller
 * @property {'primary' | 'secondary' | 'danger'} [variant] - Buton varyantı
 * @property {boolean} [fullWidth] - Butonun tam genişlikte olup olmayacağı
 * @property {'small' | 'medium' | 'large'} [size] - <PERSON><PERSON> boyutu
 */

/**
 * Özelleştirilebilir buton bileşeni
 * @param {CustomButtonProps} props 
 * @returns {JSX.Element}
 */
const CustomButton = ({
    title,
    onPress,
    disabled = false,
    loading = false,
    style,
    textStyle,
    variant = 'primary',
    fullWidth = false,
    size = 'medium',
    ...props
}) => {
    // loading ve disabled değerlerini boolean'a çevir
    const isLoading = Boolean(loading);
    const isDisabled = Boolean(disabled);

    const getVariantStyle = () => {
        if (isDisabled) return styles.disabled;

        if (variant === 'secondary') return styles.secondary;
        if (variant === 'danger') return styles.danger;
        return styles.primary;
    };

    const getSizeStyle = () => {
        if (size === 'small') return styles.smallButton;
        if (size === 'large') return styles.largeButton;
        return styles.mediumButton;
    };

    return (
        <TouchableOpacity
            style={[
                styles.button,
                getVariantStyle(),
                getSizeStyle(),
                fullWidth && styles.fullWidth,
                style
            ]}
            onPress={onPress}
            disabled={isDisabled || isLoading}
            activeOpacity={0.7}
            {...props}
        >
            {isLoading ? (
                <ActivityIndicator color="#ffffff" />
            ) : (
                <Text style={[
                    styles.text,
                    variant === 'secondary' && styles.secondaryText,
                    textStyle
                ]}>
                    {title}
                </Text>
            )}
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    button: {
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'row',
    },
    primary: {
        backgroundColor: '#3498db',
    },
    secondary: {
        backgroundColor: 'transparent',
        borderWidth: 1,
        borderColor: '#3498db',
    },
    danger: {
        backgroundColor: '#e74c3c',
    },
    disabled: {
        backgroundColor: '#bdc3c7',
    },
    smallButton: {
        paddingVertical: 8,
        paddingHorizontal: 16,
    },
    mediumButton: {
        paddingVertical: 12,
        paddingHorizontal: 24,
    },
    largeButton: {
        paddingVertical: 16,
        paddingHorizontal: 32,
    },
    fullWidth: {
        width: '100%',
    },
    text: {
        color: '#ffffff',
        fontSize: 16,
        fontWeight: '600',
    },
    secondaryText: {
        color: '#3498db',
    },
});

export default CustomButton;