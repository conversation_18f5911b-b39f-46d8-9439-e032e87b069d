import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
} from 'react-native';
import { useTheme } from '../../../../context/ThemeContext';

/**
 * Layout Manager - Widget düzeni yönetimi
 */
const LayoutManager = ({ visible, onClose, widgets, onUpdateLayout }) => {
  const { theme } = useTheme();

  // Theme kontrolü
  if (!theme) {
    return null;
  }

  /**
   * Güvenli tema değeri alma
   * @param {string} property - Te<PERSON>
   * @param {string} fallback - <PERSON><PERSON><PERSON><PERSON><PERSON> de<PERSON>er
   * @returns {string} Gü<PERSON>li tema değeri
   */
  const getSafeThemeValue = (property, fallback) => {
    return theme[property] || theme.colors?.[property.toLowerCase()] || fallback;
  };

  /**
   * Layout presetleri
   */
  const layoutPresets = [
    {
      id: 'grid-2x2',
      name: '2x2 Grid',
      description: '4 eşit widget',
      icon: '⬜',
    },
    {
      id: 'grid-3x2',
      name: '3x2 Grid',
      description: '6 widget düzeni',
      icon: '⬛',
    },
    {
      id: 'dashboard',
      name: 'Dashboard',
      description: 'Büyük KPI + grafikler',
      icon: '📊',
    },
    {
      id: 'report',
      name: 'Rapor',
      description: 'Başlık + tablo + grafik',
      icon: '📋',
    },
  ];

  /**
   * Layout uygulama
   */
  const applyLayout = (preset) => {
    // Layout preset'ine göre widget'ları yeniden konumlandır
    const newLayout = generateLayoutPositions(preset, widgets);
    newLayout.forEach(layout => {
      onUpdateLayout(layout.widgetId, {
        position: layout.position,
        size: layout.size,
      });
    });
    onClose();
  };

  /**
   * Layout pozisyonları oluştur
   */
  const generateLayoutPositions = (preset, widgets) => {
    const layouts = [];
    const margin = 20;
    const canvasWidth = 800;
    const canvasHeight = 600;

    switch (preset.id) {
      case 'grid-2x2':
        widgets.slice(0, 4).forEach((widget, index) => {
          const col = index % 2;
          const row = Math.floor(index / 2);
          layouts.push({
            widgetId: widget.id,
            position: {
              x: margin + col * (canvasWidth / 2),
              y: margin + row * (canvasHeight / 2),
            },
            size: {
              width: (canvasWidth / 2) - margin * 1.5,
              height: (canvasHeight / 2) - margin * 1.5,
            },
          });
        });
        break;

      case 'grid-3x2':
        widgets.slice(0, 6).forEach((widget, index) => {
          const col = index % 3;
          const row = Math.floor(index / 3);
          layouts.push({
            widgetId: widget.id,
            position: {
              x: margin + col * (canvasWidth / 3),
              y: margin + row * (canvasHeight / 2),
            },
            size: {
              width: (canvasWidth / 3) - margin * 1.33,
              height: (canvasHeight / 2) - margin * 1.5,
            },
          });
        });
        break;

      default:
        // Default layout - widgets'ları mevcut pozisyonlarında bırak
        break;
    }

    return layouts;
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.container, { backgroundColor: getSafeThemeValue('BACKGROUND', '#fff') }]}>
        {/* Header */}
        <View style={[styles.header, { backgroundColor: getSafeThemeValue('SURFACE', '#f9f9f9') }]}>
          <Text style={[styles.title, { color: getSafeThemeValue('TEXT_PRIMARY', '#000') }]}>
            Layout Manager
          </Text>
          <TouchableOpacity
            style={[styles.closeButton, { backgroundColor: getSafeThemeValue('ERROR', '#dc3545') }]}
            onPress={onClose}
          >
            <Text style={[styles.closeButtonText, { color: getSafeThemeValue('SURFACE', '#fff') }]}>
              ×
            </Text>
          </TouchableOpacity>
        </View>

        {/* Content */}
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <Text style={[styles.sectionTitle, { color: getSafeThemeValue('TEXT_PRIMARY', '#000') }]}>
            Layout Presetleri
          </Text>
          
          <View style={styles.presetsGrid}>
            {layoutPresets.map((preset) => (
              <TouchableOpacity
                key={preset.id}
                style={[
                  styles.presetCard,
                  { 
                    backgroundColor: getSafeThemeValue('SURFACE', '#f9f9f9'),
                    borderColor: getSafeThemeValue('BORDER', '#e0e0e0'),
                  },
                ]}
                onPress={() => applyLayout(preset)}
                activeOpacity={0.8}
              >
                <Text style={[styles.presetIcon, { color: getSafeThemeValue('PRIMARY', '#007AFF') }]}>
                  {preset.icon}
                </Text>
                <Text style={[styles.presetName, { color: getSafeThemeValue('TEXT_PRIMARY', '#000') }]}>
                  {preset.name}
                </Text>
                <Text style={[styles.presetDescription, { color: getSafeThemeValue('TEXT_SECONDARY', '#666') }]}>
                  {preset.description}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          <Text style={[styles.sectionTitle, { color: getSafeThemeValue('TEXT_PRIMARY', '#000') }]}>
            Widget Listesi
          </Text>
          
          <View style={styles.widgetsList}>
            {widgets.map((widget, index) => (
              <View
                key={widget.id}
                style={[
                  styles.widgetItem,
                  { 
                    backgroundColor: getSafeThemeValue('SURFACE', '#f9f9f9'),
                    borderColor: getSafeThemeValue('BORDER', '#e0e0e0'),
                  },
                ]}
              >
                <Text style={[styles.widgetIndex, { color: getSafeThemeValue('TEXT_SECONDARY', '#666') }]}>
                  {index + 1}
                </Text>
                <Text style={[styles.widgetTitle, { color: getSafeThemeValue('TEXT_PRIMARY', '#000') }]}>
                  {widget.title}
                </Text>
                <Text style={[styles.widgetType, { color: getSafeThemeValue('TEXT_SECONDARY', '#666') }]}>
                  {widget.type}
                </Text>
              </View>
            ))}
          </View>
        </ScrollView>

        {/* Footer */}
        <View style={[styles.footer, { backgroundColor: getSafeThemeValue('SURFACE', '#f9f9f9') }]}>
          <Text style={[styles.footerText, { color: getSafeThemeValue('TEXT_SECONDARY', '#666') }]}>
            {widgets.length} widget mevcut
          </Text>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeButtonText: {
    fontSize: 18,
    fontWeight: '700',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    marginTop: 8,
  },
  presetsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 24,
  },
  presetCard: {
    width: '48%',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  presetIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  presetName: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  presetDescription: {
    fontSize: 12,
    textAlign: 'center',
  },
  widgetsList: {
    gap: 8,
  },
  widgetItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  widgetIndex: {
    fontSize: 12,
    fontWeight: '600',
    marginRight: 12,
    minWidth: 20,
  },
  widgetTitle: {
    fontSize: 14,
    fontWeight: '600',
    flex: 1,
  },
  widgetType: {
    fontSize: 12,
    textTransform: 'capitalize',
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  footerText: {
    fontSize: 12,
    textAlign: 'center',
  },
});

export default LayoutManager;
