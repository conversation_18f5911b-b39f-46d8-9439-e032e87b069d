/**
 * Özel Tarih Seçici Komponenti
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md - Stage 2, Phase 1
 * 
 * Özel tarih aralığı seçimi
 * Maksimum 250 satır - Tek sorumluluk prensibi
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Alert,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useTheme } from '../../../context/ThemeContext';
import { formatDate } from '../../../utils/dateFormatters';

/**
 * Özel tarih seçici komponenti
 * @param {Object} props - Component props
 * @param {string} props.startDate - Başlangıç tarihi (YYYY-MM-DD)
 * @param {string} props.endDate - <PERSON><PERSON><PERSON> tarihi (YYYY-MM-DD)
 * @param {Function} props.onDateChange - <PERSON><PERSON><PERSON> callback fonksiyonu
 * @param {Object} props.theme - <PERSON><PERSON> ob<PERSON> (opsiyonel, context'ten alınır)
 */
const CustomDatePicker = ({ 
  startDate, 
  endDate, 
  onDateChange, 
  theme: propTheme 
}) => {
  const { theme, isDarkMode } = useTheme();
  const currentTheme = propTheme || theme;

  // State yönetimi
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [selectingType, setSelectingType] = useState('start'); // 'start' veya 'end'
  const [tempStartDate, setTempStartDate] = useState(startDate);
  const [tempEndDate, setTempEndDate] = useState(endDate);

  /**
   * Başlangıç tarihi seçim işleyicisi
   */
  const handleStartDatePress = () => {
    setSelectingType('start');
    setShowDatePicker(true);
  };

  /**
   * Bitiş tarihi seçim işleyicisi
   */
  const handleEndDatePress = () => {
    setSelectingType('end');
    setShowDatePicker(true);
  };

  /**
   * DateTimePicker tarih seçim işleyicisi
   * @param {Object} event - Event objesi
   * @param {Date} selectedDate - Seçilen tarih
   */
  const handleDateChange = (event, selectedDate) => {
    if (event.type === 'dismissed') {
      setShowDatePicker(false);
      return;
    }

    if (selectedDate) {
      const dateString = selectedDate.toISOString().split('T')[0];

      if (selectingType === 'start') {
        setTempStartDate(dateString);

        // Eğer bitiş tarihi başlangıç tarihinden önce ise, bitiş tarihini sıfırla
        if (tempEndDate && dateString > tempEndDate) {
          setTempEndDate(null);
        }
      } else {
        // Bitiş tarihi başlangıç tarihinden önce olamaz
        if (tempStartDate && dateString < tempStartDate) {
          Alert.alert(
            'Geçersiz Tarih',
            'Bitiş tarihi başlangıç tarihinden önce olamaz.'
          );
          setShowDatePicker(false);
          return;
        }
        setTempEndDate(dateString);
      }

      setShowDatePicker(false);
    }
  };

  /**
   * Tarih onaylama işleyicisi
   */
  const handleConfirmDates = () => {
    if (!tempStartDate) {
      Alert.alert('Eksik Bilgi', 'Lütfen başlangıç tarihini seçin.');
      return;
    }

    if (!tempEndDate) {
      Alert.alert('Eksik Bilgi', 'Lütfen bitiş tarihini seçin.');
      return;
    }

    if (onDateChange) {
      onDateChange({
        startDate: tempStartDate,
        endDate: tempEndDate
      });
    }
  };

  /**
   * Tarih iptal işleyicisi
   */
  const handleCancelDates = () => {
    setTempStartDate(startDate);
    setTempEndDate(endDate);
  };

  /**
   * Tarih aralığı hesaplama
   * @returns {number} Gün sayısı
   */
  const calculateDaysDifference = () => {
    if (!startDate || !endDate) return 0;
    
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
    
    return diffDays;
  };



  const daysDifference = calculateDaysDifference();

  return (
    <View style={[styles.container, { backgroundColor: currentTheme.BACKGROUND }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: currentTheme.TEXT_PRIMARY }]}>
          Özel Tarih Aralığı
        </Text>
        <Text style={[styles.subtitle, { color: currentTheme.TEXT_SECONDARY }]}>
          Bütçenizin başlangıç ve bitiş tarihlerini seçin
        </Text>
      </View>

      {/* Tarih seçim butonları */}
      <View style={styles.dateSelectors}>
        <TouchableOpacity
          style={[styles.dateButton, { backgroundColor: currentTheme.SURFACE }]}
          onPress={handleStartDatePress}
        >
          <MaterialIcons name="event" size={24} color={currentTheme.PRIMARY} />
          <View style={styles.dateInfo}>
            <Text style={[styles.dateLabel, { color: currentTheme.TEXT_SECONDARY }]}>
              Başlangıç Tarihi
            </Text>
            <Text style={[styles.dateValue, { color: currentTheme.TEXT_PRIMARY }]}>
              {startDate ? formatDate(startDate) : 'Tarih seçin'}
            </Text>
          </View>
          <MaterialIcons name="chevron-right" size={24} color={currentTheme.TEXT_SECONDARY} />
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.dateButton, { backgroundColor: currentTheme.SURFACE }]}
          onPress={handleEndDatePress}
        >
          <MaterialIcons name="event" size={24} color={currentTheme.PRIMARY} />
          <View style={styles.dateInfo}>
            <Text style={[styles.dateLabel, { color: currentTheme.TEXT_SECONDARY }]}>
              Bitiş Tarihi
            </Text>
            <Text style={[styles.dateValue, { color: currentTheme.TEXT_PRIMARY }]}>
              {endDate ? formatDate(endDate) : 'Tarih seçin'}
            </Text>
          </View>
          <MaterialIcons name="chevron-right" size={24} color={currentTheme.TEXT_SECONDARY} />
        </TouchableOpacity>
      </View>

      {/* Özet bilgi */}
      {startDate && endDate && (
        <View style={[styles.summaryCard, { backgroundColor: currentTheme.SURFACE }]}>
          <MaterialIcons name="info" size={20} color={currentTheme.INFO} />
          <View style={styles.summaryInfo}>
            <Text style={[styles.summaryTitle, { color: currentTheme.TEXT_PRIMARY }]}>
              Bütçe Süresi
            </Text>
            <Text style={[styles.summaryValue, { color: currentTheme.TEXT_SECONDARY }]}>
              {daysDifference} gün ({Math.ceil(daysDifference / 7)} hafta)
            </Text>
          </View>
        </View>
      )}

      {/* DateTimePicker */}
      {showDatePicker && (
        <DateTimePicker
          value={selectingType === 'start'
            ? (tempStartDate ? new Date(tempStartDate) : new Date())
            : (tempEndDate ? new Date(tempEndDate) : new Date())
          }
          mode="date"
          display="default"
          onChange={handleDateChange}
          locale="tr-TR"
        />
      )}

      {/* Onay Modal */}
      {tempStartDate && tempEndDate && (
        <Modal
          visible={false} // Sadece gerektiğinde gösterilecek
          animationType="slide"
          transparent={true}
        >
          <View style={styles.modalOverlay}>
            <View style={[styles.modalContent, { backgroundColor: currentTheme.SURFACE }]}>
              <View style={styles.modalHeader}>
                <Text style={[styles.modalTitle, { color: currentTheme.TEXT_PRIMARY }]}>
                  Tarih Aralığını Onayla
                </Text>
              </View>

              <View style={styles.datePreview}>
                <Text style={[styles.previewText, { color: currentTheme.TEXT_PRIMARY }]}>
                  Başlangıç: {formatDate(tempStartDate)}
                </Text>
                <Text style={[styles.previewText, { color: currentTheme.TEXT_PRIMARY }]}>
                  Bitiş: {formatDate(tempEndDate)}
                </Text>
              </View>

              <View style={styles.modalActions}>
                <TouchableOpacity
                  style={[styles.modalButton, styles.cancelButton, { borderColor: currentTheme.BORDER }]}
                  onPress={handleCancelDates}
                >
                  <Text style={[styles.cancelButtonText, { color: currentTheme.TEXT_SECONDARY }]}>
                    İptal
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.modalButton, styles.confirmButton, { backgroundColor: currentTheme.PRIMARY }]}
                  onPress={handleConfirmDates}
                >
                  <Text style={[styles.confirmButtonText, { color: currentTheme.WHITE }]}>
                    Onayla
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
  },
  header: {
    paddingVertical: 16,
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  dateSelectors: {
    gap: 12,
    marginBottom: 16,
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    gap: 12,
  },
  dateInfo: {
    flex: 1,
  },
  dateLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  dateValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  summaryCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    gap: 12,
  },
  summaryInfo: {
    flex: 1,
  },
  summaryTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 2,
  },
  summaryValue: {
    fontSize: 13,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    borderRadius: 16,
    padding: 16,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  modalActions: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 16,
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    borderWidth: 1,
  },
  confirmButton: {
    // backgroundColor set via style prop
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  datePreview: {
    paddingVertical: 16,
    alignItems: 'center',
  },
  previewText: {
    fontSize: 16,
    marginBottom: 8,
  },
});

export default CustomDatePicker;
