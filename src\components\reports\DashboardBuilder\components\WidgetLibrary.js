import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
  Alert,
} from 'react-native';
import { useTheme } from '../../../../context/ThemeContext';

/**
 * Widget Library - Kullanılabilir widget'ları gösterir
 */
const WidgetLibrary = ({ visible, onClose, onAddWidget }) => {
  const { theme } = useTheme();

  // Theme kontrolü
  if (!theme) {
    return null;
  }

  /**
   * Güvenli tema değeri alma
   * @param {string} property - Tema <PERSON>ğ<PERSON>
   * @param {string} fallback - <PERSON>ars<PERSON><PERSON><PERSON> değer
   * @returns {string} Güvenli tema değeri
   */
  const getSafeThemeValue = (property, fallback) => {
    return theme[property] || theme.colors?.[property.toLowerCase()] || fallback;
  };

  /**
   * Widget türleri
   */
  const getWidgetTypes = () => [
    {
      type: 'chart',
      title: 'Grafik Widget',
      description: '<PERSON><PERSON><PERSON>, çizgi, pasta grafikleri',
      icon: '📊',
      color: getSafeThemeValue('PRIMARY', '#007AFF'),
      category: 'Görselleştirme',
      defaultConfig: {
        chartType: 'line',
        dataSource: 'transactions',
        xAxis: 'date',
        yAxis: 'amount',
        groupBy: 'category',
      },
    },
    {
      type: 'table',
      title: 'Tablo Widget',
      description: 'Veri tablosu ve listeleri',
      icon: '📋',
      color: getSafeThemeValue('SUCCESS', '#28a745'),
      category: 'Veri',
      defaultConfig: {
        dataSource: 'transactions',
        columns: ['date', 'description', 'amount', 'category'],
        pageSize: 10,
        sortable: true,
        filterable: true,
      },
    },
    {
      type: 'kpi',
      title: 'KPI Widget',
      description: 'Anahtar performans göstergeleri',
      icon: '🎯',
      color: getSafeThemeValue('INFO', '#17a2b8'),
      category: 'Metrik',
      defaultConfig: {
        metric: 'totalIncome',
        format: 'currency',
        showTrend: true,
        comparisonPeriod: 'previous_month',
      },
    },
    {
      type: 'metric',
      title: 'Metrik Widget',
      description: 'Sayısal göstergeler',
      icon: '📈',
      color: getSafeThemeValue('WARNING', '#ffc107'),
      category: 'Metrik',
      defaultConfig: {
        metrics: ['income', 'expense', 'balance'],
        layout: 'horizontal',
        showIcons: true,
        showColors: true,
      },
    },
    {
      type: 'text',
      title: 'Metin Widget',
      description: 'Başlık, açıklama ve notlar',
      icon: '📝',
      color: getSafeThemeValue('SECONDARY', '#6c757d'),
      category: 'İçerik',
      defaultConfig: {
        content: 'Metin içeriği',
        fontSize: 16,
        fontWeight: 'normal',
        textAlign: 'left',
        textColor: '#000000',
      },
    },
    {
      type: 'filter',
      title: 'Filtre Widget',
      description: 'Veri filtreleme kontrolleri',
      icon: '🔍',
      color: getSafeThemeValue('ACCENT', '#6f42c1'),
      category: 'Kontrol',
      defaultConfig: {
        filterType: 'dateRange',
        allowMultiple: true,
        defaultValue: null,
        options: [],
      },
    },
    {
      type: 'image',
      title: 'Resim Widget',
      description: 'Logo, grafik ve resimler',
      icon: '🖼️',
      color: getSafeThemeValue('DARK', '#343a40'),
      category: 'İçerik',
      defaultConfig: {
        src: null,
        alt: 'Resim',
        fit: 'contain',
        borderRadius: 8,
      },
    },
  ];

  /**
   * Widget kategorileri
   */
  const getWidgetCategories = () => {
    const widgets = getWidgetTypes();
    const categories = [...new Set(widgets.map(w => w.category))];
    return categories.map(category => ({
      name: category,
      widgets: widgets.filter(w => w.category === category),
    }));
  };

  /**
   * Widget ekleme
   */
  const handleAddWidget = (widgetType) => {
    onAddWidget(widgetType);
    onClose();
  };

  /**
   * Widget önizleme
   */
  const showWidgetPreview = (widgetType) => {
    Alert.alert(
      widgetType.title,
      widgetType.description,
      [
        { text: 'İptal', style: 'cancel' },
        { text: 'Ekle', onPress: () => handleAddWidget(widgetType) },
      ]
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.container, { backgroundColor: getSafeThemeValue('BACKGROUND', '#fff') }]}>
        {/* Header */}
        <View style={[styles.header, { backgroundColor: getSafeThemeValue('SURFACE', '#f9f9f9') }]}>
          <Text style={[styles.title, { color: getSafeThemeValue('TEXT_PRIMARY', '#000') }]}>
            Widget Kütüphanesi
          </Text>
          <TouchableOpacity
            style={[styles.closeButton, { backgroundColor: getSafeThemeValue('ERROR', '#dc3545') }]}
            onPress={onClose}
          >
            <Text style={[styles.closeButtonText, { color: getSafeThemeValue('SURFACE', '#fff') }]}>
              ×
            </Text>
          </TouchableOpacity>
        </View>

        {/* Content */}
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {getWidgetCategories().map((category) => (
            <View key={category.name} style={styles.categorySection}>
              <Text style={[styles.categoryTitle, { color: getSafeThemeValue('TEXT_PRIMARY', '#000') }]}>
                {category.name}
              </Text>
              
              <View style={styles.widgetGrid}>
                {category.widgets.map((widget) => (
                  <TouchableOpacity
                    key={widget.type}
                    style={[
                      styles.widgetCard,
                      { 
                        backgroundColor: getSafeThemeValue('SURFACE', '#f9f9f9'),
                        borderColor: getSafeThemeValue('BORDER', '#e0e0e0'),
                      },
                    ]}
                    onPress={() => handleAddWidget(widget)}
                    onLongPress={() => showWidgetPreview(widget)}
                    activeOpacity={0.8}
                  >
                    <View style={[styles.widgetIcon, { backgroundColor: widget.color }]}>
                      <Text style={[styles.widgetIconText, { color: getSafeThemeValue('SURFACE', '#fff') }]}>
                        {widget.icon}
                      </Text>
                    </View>
                    
                    <View style={styles.widgetInfo}>
                      <Text style={[styles.widgetTitle, { color: getSafeThemeValue('TEXT_PRIMARY', '#000') }]}>
                        {widget.title}
                      </Text>
                      <Text style={[styles.widgetDescription, { color: getSafeThemeValue('TEXT_SECONDARY', '#666') }]}>
                        {widget.description}
                      </Text>
                    </View>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          ))}
        </ScrollView>

        {/* Footer */}
        <View style={[styles.footer, { backgroundColor: getSafeThemeValue('SURFACE', '#f9f9f9') }]}>
          <Text style={[styles.footerText, { color: getSafeThemeValue('TEXT_SECONDARY', '#666') }]}>
            Widget'a uzun basarak önizleme yapabilirsiniz
          </Text>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeButtonText: {
    fontSize: 18,
    fontWeight: '700',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  categorySection: {
    marginBottom: 24,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  widgetGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  widgetCard: {
    width: '48%',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  widgetIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  widgetIconText: {
    fontSize: 18,
  },
  widgetInfo: {
    flex: 1,
  },
  widgetTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  widgetDescription: {
    fontSize: 11,
    lineHeight: 14,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  footerText: {
    fontSize: 12,
    textAlign: 'center',
  },
});

export default WidgetLibrary;
