import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { useSQLiteContext } from 'expo-sqlite';
import { Colors } from '../constants/colors';
import DateTimePicker from '@react-native-community/datetimepicker';
import CategorySelectorSimple from '../components/transaction/CategorySelectorSimple';

/**
 * İşlem ekleme/düzenleme ekranı
 * @param {Object} props Component props
 * @param {Object} props.navigation Navigation objesi
 * @param {Object} props.route Route objesi
 */
const TransactionEditScreen = ({ navigation, route }) => {
  const insets = useSafeAreaInsets();
  const db = useSQLiteContext();

  const { transaction, type: initialType } = route.params || {};

  const [isLoading, setIsLoading] = useState(false);
  const [type, setType] = useState(transaction?.type || initialType || 'expense');
  const [amount, setAmount] = useState(transaction?.amount?.toString() || '');
  const [description, setDescription] = useState(transaction?.description || '');
  const [date, setDate] = useState(transaction?.date ? new Date(transaction.date) : new Date());
  const [categoryId, setCategoryId] = useState(transaction?.category_id || null);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [categories, setCategories] = useState([]);

  // Kategorileri yükle
  useEffect(() => {
    const loadCategories = async () => {
      try {
        const result = await db.getAllAsync(`
          SELECT * FROM categories
          WHERE type = ? OR type = 'both'
          ORDER BY name
        `, [type]);

        setCategories(result);

        // Eğer seçili kategori yoksa veya seçili kategori türü uygun değilse
        if (!categoryId || !result.some(c => c.id === categoryId)) {
          const defaultCategory = result.find(c => c.is_default && (c.type === type || c.type === 'both'));
          setCategoryId(defaultCategory?.id || (result.length > 0 ? result[0].id : null));
        }
      } catch (error) {
        console.error('Kategori yükleme hatası:', error);
      }
    };

    loadCategories();
  }, [type]);

  // İşlemi kaydet
  const saveTransaction = async () => {
    try {
      if (!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
        Alert.alert('Hata', 'Lütfen geçerli bir tutar girin.');
        return;
      }

      if (!categoryId) {
        Alert.alert('Hata', 'Lütfen bir kategori seçin.');
        return;
      }

      setIsLoading(true);

      const formattedDate = date.toISOString().split('T')[0];
      const parsedAmount = parseFloat(amount);

      if (transaction?.id) {
        // Mevcut işlemi güncelle
        await db.runAsync(`
          UPDATE transactions
          SET type = ?, amount = ?, description = ?, date = ?, category_id = ?
          WHERE id = ?
        `, [type, parsedAmount, description, formattedDate, categoryId, transaction.id]);
      } else {
        // Yeni işlem ekle
        await db.runAsync(`
          INSERT INTO transactions (type, amount, description, date, category_id)
          VALUES (?, ?, ?, ?, ?)
        `, [type, parsedAmount, description, formattedDate, categoryId]);
      }

      navigation.goBack();
    } catch (error) {
      console.error('İşlem kaydetme hatası:', error);
      Alert.alert('Hata', 'İşlem kaydedilirken bir hata oluştu.');
    } finally {
      setIsLoading(false);
    }
  };

  // Tarih değiştiğinde
  const handleDateChange = (_, selectedDate) => {
    setShowDatePicker(false);

    if (selectedDate) {
      const formattedDate = selectedDate.toISOString().split('T')[0];
      setDate(new Date(formattedDate));
    }
  };

  // Tarih formatla
  const formatDate = (date) => {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}.${month}.${year}`;
  };

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <MaterialIcons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.title}>
          {transaction?.id ? 'İşlemi Düzenle' : 'Yeni İşlem Ekle'}
        </Text>
        <View style={{ width: 24 }} />
      </View>

      {isLoading ? (
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
      ) : (
        <KeyboardAvoidingView
          style={styles.container}
          behavior={'padding'}
          keyboardVerticalOffset={64}
        >
          <ScrollView style={styles.content}>
            <View style={styles.typeSelector}>
              <TouchableOpacity
                style={[
                  styles.typeButton,
                  type === 'expense' && styles.activeTypeButton
                ]}
                onPress={() => setType('expense')}
              >
                <MaterialIcons
                  name="arrow-downward"
                  size={20}
                  color={type === 'expense' ? '#fff' : Colors.EXPENSE}
                />
                <Text
                  style={[
                    styles.typeButtonText,
                    type === 'expense' && styles.activeTypeButtonText
                  ]}
                >
                  Gider
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.typeButton,
                  type === 'income' && styles.activeIncomeButton
                ]}
                onPress={() => setType('income')}
              >
                <MaterialIcons
                  name="arrow-upward"
                  size={20}
                  color={type === 'income' ? '#fff' : Colors.INCOME}
                />
                <Text
                  style={[
                    styles.typeButtonText,
                    type === 'income' && styles.activeTypeButtonText
                  ]}
                >
                  Gelir
                </Text>
              </TouchableOpacity>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Tutar</Text>
              <View style={styles.amountInputContainer}>
                <Text style={styles.currencySymbol}>₺</Text>
                <TextInput
                  style={styles.amountInput}
                  value={amount}
                  onChangeText={setAmount}
                  keyboardType="numeric"
                  placeholder="0.00"
                />
              </View>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Açıklama</Text>
              <TextInput
                style={styles.input}
                value={description}
                onChangeText={setDescription}
                placeholder="İşlem açıklaması"
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Tarih</Text>
              <TouchableOpacity
                style={styles.dateSelector}
                onPress={() => setShowDatePicker(true)}
              >
                <Text style={styles.dateText}>{formatDate(date)}</Text>
                <MaterialIcons name="calendar-today" size={20} color={Colors.PRIMARY} />
              </TouchableOpacity>

              {showDatePicker && (
                <DateTimePicker
                  value={date}
                  mode="date"
                  display="default"
                  onChange={handleDateChange}
                />
              )}
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Kategori</Text>
              <CategorySelectorSimple
                categories={categories}
                selectedCategoryId={categoryId}
                onSelectCategory={setCategoryId}
              />
            </View>
          </ScrollView>

          <View style={styles.footer}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => navigation.goBack()}
            >
              <Text style={styles.cancelButtonText}>İptal</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.saveButton}
              onPress={saveTransaction}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <Text style={styles.saveButtonText}>Kaydet</Text>
              )}
            </TouchableOpacity>
          </View>
        </KeyboardAvoidingView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.PRIMARY,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  typeSelector: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  typeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#eee',
    borderRadius: 8,
    marginRight: 8,
  },
  activeTypeButton: {
    backgroundColor: Colors.EXPENSE,
    borderColor: Colors.EXPENSE,
  },
  activeIncomeButton: {
    backgroundColor: Colors.INCOME,
    borderColor: Colors.INCOME,
  },
  typeButtonText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  activeTypeButtonText: {
    color: '#fff',
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  amountInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
  },
  currencySymbol: {
    fontSize: 18,
    color: '#333',
    marginRight: 8,
  },
  amountInput: {
    flex: 1,
    fontSize: 18,
    paddingVertical: 12,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
  },
  dateSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  dateText: {
    fontSize: 16,
    color: '#333',
  },
  footer: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
    backgroundColor: '#fff',
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    marginRight: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#333',
  },
  saveButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
    borderRadius: 8,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
  },
});

export default TransactionEditScreen;
