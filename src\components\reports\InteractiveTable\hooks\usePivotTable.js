import { useState, useEffect, useCallback, useMemo } from 'react';
import { 
  generatePivotTable, 
  validatePivotConfig, 
  pivotTableToCSV,
  preparePivotForExcel 
} from '../services/pivotTableService';

/**
 * Pivot Tablo Hook
 * Pivot tablo state yönetimi ve hesaplamaları
 */
export const usePivotTable = (initialData = [], initialConfig = null) => {
  const [data, setData] = useState(initialData);
  const [config, setConfig] = useState(initialConfig || {
    rows: [],
    columns: [],
    values: [],
    aggregations: {},
    filters: {}
  });
  const [pivotData, setPivotData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [validationResult, setValidationResult] = useState({ isValid: true, errors: [], warnings: [] });

  // Pivot tabloyu hesapla
  const calculatePivotTable = useCallback(async () => {
    if (!data || data.length === 0) {
      setPivotData(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Yapılandırmayı doğrula
      const validation = validatePivotConfig(config);
      setValidationResult(validation);

      if (!validation.isValid) {
        setError(validation.errors.join(', '));
        return;
      }

      // Pivot tabloyu hesapla
      const result = generatePivotTable(data, config);
      setPivotData(result);
    } catch (err) {
      setError(err.message);
      console.error('Pivot table calculation error:', err);
    } finally {
      setIsLoading(false);
    }
  }, [data, config]);

  // Veri veya yapılandırma değiştiğinde pivot tabloyu yeniden hesapla
  useEffect(() => {
    calculatePivotTable();
  }, [calculatePivotTable]);

  // Yapılandırma güncelleme
  const updateConfig = useCallback((newConfig) => {
    setConfig(prev => ({
      ...prev,
      ...newConfig
    }));
  }, []);

  // Satır alanı ekleme
  const addRowField = useCallback((field) => {
    setConfig(prev => ({
      ...prev,
      rows: [...prev.rows, field]
    }));
  }, []);

  // Sütun alanı ekleme
  const addColumnField = useCallback((field) => {
    setConfig(prev => ({
      ...prev,
      columns: [...prev.columns, field]
    }));
  }, []);

  // Değer alanı ekleme
  const addValueField = useCallback((field, aggregationType = 'SUM') => {
    setConfig(prev => ({
      ...prev,
      values: [...prev.values, field],
      aggregations: {
        ...prev.aggregations,
        [field.name]: aggregationType
      }
    }));
  }, []);

  // Alan kaldırma
  const removeField = useCallback((fieldName, area) => {
    setConfig(prev => {
      const newConfig = { ...prev };
      newConfig[area] = prev[area].filter(field => field.name !== fieldName);
      
      // Eğer değer alanından kaldırılıyorsa, aggregation'ı da kaldır
      if (area === 'values') {
        const { [fieldName]: removed, ...remainingAggregations } = prev.aggregations;
        newConfig.aggregations = remainingAggregations;
      }
      
      return newConfig;
    });
  }, []);

  // Toplama fonksiyonu değiştirme
  const changeAggregation = useCallback((fieldName, aggregationType) => {
    setConfig(prev => ({
      ...prev,
      aggregations: {
        ...prev.aggregations,
        [fieldName]: aggregationType
      }
    }));
  }, []);

  // Filtre ekleme/güncelleme
  const updateFilter = useCallback((fieldName, filterConfig) => {
    setConfig(prev => ({
      ...prev,
      filters: {
        ...prev.filters,
        [fieldName]: filterConfig
      }
    }));
  }, []);

  // Filtre kaldırma
  const removeFilter = useCallback((fieldName) => {
    setConfig(prev => {
      const { [fieldName]: removed, ...remainingFilters } = prev.filters;
      return {
        ...prev,
        filters: remainingFilters
      };
    });
  }, []);

  // Yapılandırmayı sıfırla
  const resetConfig = useCallback(() => {
    setConfig({
      rows: [],
      columns: [],
      values: [],
      aggregations: {},
      filters: {}
    });
  }, []);

  // CSV export
  const exportToCSV = useCallback(() => {
    if (!pivotData) {
      throw new Error('Pivot tablo verisi bulunamadı');
    }
    return pivotTableToCSV(pivotData);
  }, [pivotData]);

  // Excel export hazırlığı
  const prepareForExcel = useCallback(() => {
    if (!pivotData) {
      throw new Error('Pivot tablo verisi bulunamadı');
    }
    return preparePivotForExcel(pivotData);
  }, [pivotData]);

  // Mevcut alanları al
  const availableFields = useMemo(() => {
    if (!data || data.length === 0) return [];
    
    return Object.keys(data[0]).map(key => ({
      name: key,
      displayName: key.replace(/_/g, ' ').replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()),
      type: getFieldType(data[0][key])
    }));
  }, [data]);

  // Alan tipini belirle
  const getFieldType = (value) => {
    if (typeof value === 'number') return 'number';
    if (value instanceof Date) return 'date';
    if (typeof value === 'boolean') return 'boolean';
    return 'text';
  };

  // Kullanılmayan alanları al
  const unusedFields = useMemo(() => {
    const usedFieldNames = [
      ...config.rows.map(f => f.name),
      ...config.columns.map(f => f.name),
      ...config.values.map(f => f.name)
    ];
    
    return availableFields.filter(field => !usedFieldNames.includes(field.name));
  }, [availableFields, config]);

  // Pivot tablo istatistikleri
  const statistics = useMemo(() => {
    if (!pivotData) return null;
    
    return {
      totalRows: pivotData.metadata.totalRows,
      totalColumns: pivotData.metadata.totalColumns,
      originalDataCount: pivotData.metadata.originalDataCount,
      filteredDataCount: pivotData.metadata.filteredDataCount,
      configuredFields: {
        rows: config.rows.length,
        columns: config.columns.length,
        values: config.values.length,
        filters: Object.keys(config.filters).length
      }
    };
  }, [pivotData, config]);

  // Pivot tablo boş mu kontrol et
  const isEmpty = useMemo(() => {
    return !pivotData || !pivotData.rows || pivotData.rows.length === 0;
  }, [pivotData]);

  // Yapılandırma geçerli mi kontrol et
  const isConfigValid = useMemo(() => {
    return validationResult.isValid && config.values.length > 0;
  }, [validationResult, config]);

  return {
    // State
    data,
    config,
    pivotData,
    isLoading,
    error,
    validationResult,
    availableFields,
    unusedFields,
    statistics,
    isEmpty,
    isConfigValid,

    // Actions
    setData,
    updateConfig,
    addRowField,
    addColumnField,
    addValueField,
    removeField,
    changeAggregation,
    updateFilter,
    removeFilter,
    resetConfig,
    calculatePivotTable,

    // Export
    exportToCSV,
    prepareForExcel
  };
};
