import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
} from 'react-native';
import { useTheme } from '../../../context/ThemeContext';
import { useNavigation } from '@react-navigation/native';

// Modular components
import CanvasEditor from './components/CanvasEditor';
import TemplateSelector from './components/TemplateSelector';
import DataSourceSelector from './components/DataSourceSelector';
import FilterBuilder from './components/FilterBuilder';
import VisualizationBuilder from './components/VisualizationBuilder';
import FormulaEditor from './components/FormulaEditor';
import PreviewPanel from './components/PreviewPanel';

// Hooks
import { useReportBuilder } from './hooks/useReportBuilder';
import { useCanvasEditor } from './hooks/useCanvasEditor';

/**
 * Görsel Rapor Editörü - Canvas-Based Editor
 * Sürükle-bırak ile rapor tasarlama
 * Widget sistemi ve gerçek zamanlı önizleme
 */
const ReportBuilder = () => {
  const { theme } = useTheme();
  const navigation = useNavigation();
  
  // Theme kontrolü
  if (!theme) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text>Theme yükleniyor...</Text>
      </View>
    );
  }
  
  // State management
  const [activeTab, setActiveTab] = useState('canvas');
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  
  // Custom hooks
  const {
    reportData,
    reportConfig,
    dataSources,
    filters,
    updateReportConfig,
    saveReport,
    loadReport,
    exportReport,
    isLoading,
    error,
  } = useReportBuilder();
  
  const {
    canvasElements,
    selectedElement,
    addElement,
    removeElement,
    updateElement,
    moveElement,
    resizeElement,
    canUndo,
    canRedo,
    undo,
    redo,
    clearCanvas,
  } = useCanvasEditor();

  /**
   * Rapor kaydetme işlemi
   */
  const handleSaveReport = async () => {
    try {
      const reportName = await promptForReportName();
      if (!reportName) return;
      
      const savedReport = await saveReport({
        name: reportName,
        template: selectedTemplate,
        canvasElements,
        dataSources,
        filters,
        config: reportConfig,
      });
      
      Alert.alert('Başarılı', `Rapor "${reportName}" kaydedildi.`);
    } catch (error) {
      Alert.alert('Hata', 'Rapor kaydedilemedi: ' + error.message);
    }
  };

  /**
   * Rapor dışa aktarma işlemi
   */
  const handleExportReport = async () => {
    try {
      const formats = ['PDF', 'Excel', 'PowerPoint', 'PNG'];
      const selectedFormat = await promptForExportFormat(formats);
      
      if (selectedFormat) {
        await exportReport({
          format: selectedFormat,
          canvasElements,
          reportData,
          config: reportConfig,
        });
        
        Alert.alert('Başarılı', `Rapor ${selectedFormat} formatında dışa aktarıldı.`);
      }
    } catch (error) {
      Alert.alert('Hata', 'Rapor dışa aktarılamadı: ' + error.message);
    }
  };

  /**
   * Rapor adı için prompt
   */
  const promptForReportName = () => {
    return new Promise((resolve) => {
      Alert.prompt(
        'Rapor Adı',
        'Rapor için bir ad girin:',
        [
          { text: 'İptal', onPress: () => resolve(null), style: 'cancel' },
          { text: 'Kaydet', onPress: (name) => resolve(name) },
        ],
        'plain-text'
      );
    });
  };

  /**
   * Dışa aktarma formatı seçimi
   */
  const promptForExportFormat = (formats) => {
    return new Promise((resolve) => {
      const buttons = formats.map(format => ({
        text: format,
        onPress: () => resolve(format),
      }));
      
      buttons.push({ text: 'İptal', onPress: () => resolve(null), style: 'cancel' });
      
      Alert.alert('Dışa Aktarma Formatı', 'Hangi formatta dışa aktarılsın?', buttons);
    });
  };

  /**
   * Tab render helper
   */
  const renderTabButton = (tabId, title, icon) => (
    <TouchableOpacity
      style={[
        styles.tabButton,
        { backgroundColor: activeTab === tabId ? theme.PRIMARY : theme.BACKGROUND },
      ]}
      onPress={() => setActiveTab(tabId)}
    >
      <Text style={styles.tabIcon}>{icon}</Text>
      <Text
        style={[
          styles.tabTitle,
          { color: activeTab === tabId ? theme.SURFACE : theme.TEXT_PRIMARY },
        ]}
      >
        {title}
      </Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      <StatusBar barStyle={theme.STATUS_BAR_STYLE} backgroundColor={theme.STATUS_BAR_COLOR} />
      
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.SURFACE }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={[styles.backButtonText, { color: theme.TEXT_PRIMARY }]}>
            ← Geri
          </Text>
        </TouchableOpacity>
        
        <View style={styles.headerContent}>
          <Text style={[styles.headerTitle, { color: theme.TEXT_PRIMARY }]}>
            🎨 Rapor Editörü
          </Text>
          <Text style={[styles.headerSubtitle, { color: theme.TEXT_SECONDARY }]}>
            Görsel Rapor Tasarla
          </Text>
        </View>
        
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.SUCCESS }]}
            onPress={handleSaveReport}
          >
            <Text style={[styles.actionButtonText, { color: theme.SURFACE }]}>
              💾 Kaydet
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.PRIMARY }]}
            onPress={handleExportReport}
          >
            <Text style={[styles.actionButtonText, { color: theme.SURFACE }]}>
              📤 Dışa Aktar
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Tab Navigation */}
      <View style={[styles.tabContainer, { backgroundColor: theme.SURFACE }]}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {renderTabButton('canvas', 'Canvas', '🎯')}
          {renderTabButton('template', 'Şablonlar', '📋')}
          {renderTabButton('data', 'Veri', '📊')}
          {renderTabButton('filter', 'Filtreler', '🔍')}
          {renderTabButton('charts', 'Grafikler', '📈')}
          {renderTabButton('formula', 'Formüller', '🧮')}
          {renderTabButton('preview', 'Önizleme', '👁')}
        </ScrollView>
      </View>

      {/* Tab Content */}
      <View style={styles.contentContainer}>
        {activeTab === 'canvas' && (
          <CanvasEditor
            elements={canvasElements}
            selectedElement={selectedElement}
            onAddElement={addElement}
            onRemoveElement={removeElement}
            onUpdateElement={updateElement}
            onMoveElement={moveElement}
            onResizeElement={resizeElement}
            onUndo={undo}
            onRedo={redo}
            onClear={clearCanvas}
            canUndo={canUndo}
            canRedo={canRedo}
          />
        )}
        
        {activeTab === 'template' && (
          <TemplateSelector
            selectedTemplate={selectedTemplate}
            onTemplateSelect={setSelectedTemplate}
            onLoadTemplate={(template) => {
              setSelectedTemplate(template);
              setActiveTab('canvas');
            }}
          />
        )}
        
        {activeTab === 'data' && (
          <DataSourceSelector
            dataSources={dataSources}
            onDataSourcesChange={updateReportConfig}
          />
        )}
        
        {activeTab === 'filter' && (
          <FilterBuilder
            filters={filters}
            dataSources={dataSources}
            onFiltersChange={updateReportConfig}
          />
        )}
        
        {activeTab === 'charts' && (
          <VisualizationBuilder
            reportData={reportData}
            onAddChart={addElement}
            onUpdateChart={updateElement}
          />
        )}
        
        {activeTab === 'formula' && (
          <FormulaEditor
            reportData={reportData}
            onFormulaApply={updateReportConfig}
          />
        )}
        
        {activeTab === 'preview' && (
          <PreviewPanel
            elements={canvasElements}
            reportData={reportData}
            isPreviewMode={isPreviewMode}
            onTogglePreview={() => setIsPreviewMode(!isPreviewMode)}
          />
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  backButton: {
    marginRight: 12,
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  headerContent: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 2,
  },
  headerSubtitle: {
    fontSize: 12,
    opacity: 0.8,
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: '600',
  },
  tabContainer: {
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
    paddingHorizontal: 16,
  },
  tabButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
    gap: 4,
  },
  tabIcon: {
    fontSize: 14,
  },
  tabTitle: {
    fontSize: 12,
    fontWeight: '500',
  },
  contentContainer: {
    flex: 1,
  },
});

export default ReportBuilder;
