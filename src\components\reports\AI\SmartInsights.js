import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Animated,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const { width: screenWidth } = Dimensions.get('window');

/**
 * Smart Insights Component
 * Displays AI-generated financial insights with interactive cards
 * Provides actionable recommendations and trend analysis
 */
const SmartInsights = ({ 
  insights = [],
  onInsightAction,
  onDismissInsight,
  autoRefresh = true,
  theme 
}) => {
  const [visibleInsights, setVisibleInsights] = useState([]);
  const [expandedInsight, setExpandedInsight] = useState(null);
  const [animatedValues, setAnimatedValues] = useState({});

  useEffect(() => {
    setVisibleInsights(insights);
    
    // Initialize animation values for each insight
    const newAnimatedValues = {};
    insights.forEach(insight => {
      newAnimatedValues[insight.id] = new Animated.Value(0);
    });
    setAnimatedValues(newAnimatedValues);
    
    // Animate insights in
    insights.forEach((insight, index) => {
      Animated.timing(newAnimatedValues[insight.id], {
        toValue: 1,
        duration: 300,
        delay: index * 100,
        useNativeDriver: true,
      }).start();
    });
  }, [insights]);

  const handleInsightPress = (insight) => {
    setExpandedInsight(expandedInsight === insight.id ? null : insight.id);
  };

  const handleActionPress = (insight, action) => {
    if (onInsightAction) {
      onInsightAction(insight, action);
    }
  };

  const handleDismiss = (insightId) => {
    // Animate out
    Animated.timing(animatedValues[insightId], {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start(() => {
      setVisibleInsights(prev => prev.filter(insight => insight.id !== insightId));
      if (onDismissInsight) {
        onDismissInsight(insightId);
      }
    });
  };

  const getInsightIcon = (type) => {
    switch (type) {
      case 'spending': return 'card';
      case 'income': return 'trending-up';
      case 'savings': return 'wallet';
      case 'budget': return 'pie-chart';
      case 'goal': return 'flag';
      case 'warning': return 'warning';
      case 'tip': return 'bulb';
      case 'achievement': return 'trophy';
      default: return 'information-circle';
    }
  };

  const getInsightColor = (type, priority) => {
    if (priority === 'high') {
      switch (type) {
        case 'warning': return theme.ERROR;
        case 'achievement': return theme.SUCCESS;
        default: return theme.PRIMARY;
      }
    } else if (priority === 'medium') {
      return theme.WARNING;
    } else {
      return theme.INFO;
    }
  };

  const getPriorityBadge = (priority) => {
    const colors = {
      high: theme.ERROR,
      medium: theme.WARNING,
      low: theme.INFO,
    };
    
    const labels = {
      high: 'Yüksek',
      medium: 'Orta',
      low: 'Düşük',
    };

    return {
      color: colors[priority] || theme.INFO,
      label: labels[priority] || 'Normal',
    };
  };

  const renderInsightCard = (insight) => {
    const isExpanded = expandedInsight === insight.id;
    const insightColor = getInsightColor(insight.type, insight.priority);
    const priorityBadge = getPriorityBadge(insight.priority);
    const animatedValue = animatedValues[insight.id] || new Animated.Value(1);

    return (
      <Animated.View
        key={insight.id}
        style={[
          styles.insightCard,
          {
            backgroundColor: theme.SURFACE,
            borderLeftColor: insightColor,
            opacity: animatedValue,
            transform: [{
              translateY: animatedValue.interpolate({
                inputRange: [0, 1],
                outputRange: [50, 0],
              }),
            }],
          }
        ]}
      >
        <TouchableOpacity
          onPress={() => handleInsightPress(insight)}
          activeOpacity={0.7}
        >
          {/* Header */}
          <View style={styles.insightHeader}>
            <View style={styles.insightTitleContainer}>
              <Ionicons 
                name={getInsightIcon(insight.type)} 
                size={20} 
                color={insightColor} 
              />
              <Text style={[styles.insightTitle, { color: theme.TEXT_PRIMARY }]}>
                {insight.title}
              </Text>
            </View>
            
            <View style={styles.insightMeta}>
              <View style={[styles.priorityBadge, { backgroundColor: priorityBadge.color + '20' }]}>
                <Text style={[styles.priorityText, { color: priorityBadge.color }]}>
                  {priorityBadge.label}
                </Text>
              </View>
              
              <TouchableOpacity
                onPress={() => handleDismiss(insight.id)}
                style={styles.dismissButton}
              >
                <Ionicons name="close" size={16} color={theme.TEXT_SECONDARY} />
              </TouchableOpacity>
            </View>
          </View>

          {/* Content */}
          <Text style={[styles.insightDescription, { color: theme.TEXT_SECONDARY }]}>
            {insight.description}
          </Text>

          {/* Metrics */}
          {insight.metrics && (
            <View style={styles.metricsContainer}>
              {insight.metrics.map((metric, index) => (
                <View key={index} style={styles.metric}>
                  <Text style={[styles.metricValue, { color: insightColor }]}>
                    {metric.value}
                  </Text>
                  <Text style={[styles.metricLabel, { color: theme.TEXT_SECONDARY }]}>
                    {metric.label}
                  </Text>
                </View>
              ))}
            </View>
          )}

          {/* Expanded Content */}
          {isExpanded && insight.details && (
            <View style={styles.expandedContent}>
              <Text style={[styles.detailsTitle, { color: theme.TEXT_PRIMARY }]}>
                Detaylar
              </Text>
              <Text style={[styles.detailsText, { color: theme.TEXT_SECONDARY }]}>
                {insight.details}
              </Text>
              
              {insight.recommendations && (
                <>
                  <Text style={[styles.detailsTitle, { color: theme.TEXT_PRIMARY }]}>
                    Öneriler
                  </Text>
                  {insight.recommendations.map((rec, index) => (
                    <View key={index} style={styles.recommendation}>
                      <Ionicons name="checkmark-circle" size={16} color={theme.SUCCESS} />
                      <Text style={[styles.recommendationText, { color: theme.TEXT_SECONDARY }]}>
                        {rec}
                      </Text>
                    </View>
                  ))}
                </>
              )}
            </View>
          )}
        </TouchableOpacity>

        {/* Actions */}
        {insight.actions && insight.actions.length > 0 && (
          <View style={styles.actionsContainer}>
            {insight.actions.map((action, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.actionButton,
                  {
                    backgroundColor: action.primary ? insightColor : theme.BACKGROUND,
                    borderColor: insightColor,
                  }
                ]}
                onPress={() => handleActionPress(insight, action)}
              >
                <Ionicons 
                  name={action.icon} 
                  size={14} 
                  color={action.primary ? theme.SURFACE : insightColor} 
                />
                <Text style={[
                  styles.actionText,
                  { 
                    color: action.primary ? theme.SURFACE : insightColor 
                  }
                ]}>
                  {action.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        )}

        {/* Timestamp */}
        {insight.timestamp && (
          <Text style={[styles.timestamp, { color: theme.TEXT_SECONDARY }]}>
            {new Date(insight.timestamp).toLocaleString('tr-TR')}
          </Text>
        )}
      </Animated.View>
    );
  };

  const renderInsightCategories = () => {
    const categories = [...new Set(visibleInsights.map(insight => insight.type))];
    
    return (
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.categoriesContainer}
      >
        <TouchableOpacity
          style={[
            styles.categoryChip,
            { backgroundColor: theme.PRIMARY, borderColor: theme.PRIMARY }
          ]}
        >
          <Text style={[styles.categoryText, { color: theme.SURFACE }]}>
            Tümü ({visibleInsights.length})
          </Text>
        </TouchableOpacity>
        
        {categories.map((category) => {
          const count = visibleInsights.filter(insight => insight.type === category).length;
          return (
            <TouchableOpacity
              key={category}
              style={[
                styles.categoryChip,
                { backgroundColor: theme.BACKGROUND, borderColor: theme.BORDER }
              ]}
            >
              <Ionicons 
                name={getInsightIcon(category)} 
                size={14} 
                color={theme.TEXT_PRIMARY} 
              />
              <Text style={[styles.categoryText, { color: theme.TEXT_PRIMARY }]}>
                {category} ({count})
              </Text>
            </TouchableOpacity>
          );
        })}
      </ScrollView>
    );
  };

  if (visibleInsights.length === 0) {
    return (
      <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
        <View style={styles.emptyState}>
          <Ionicons name="bulb-outline" size={48} color={theme.TEXT_SECONDARY} />
          <Text style={[styles.emptyStateTitle, { color: theme.TEXT_PRIMARY }]}>
            Henüz İçgörü Yok
          </Text>
          <Text style={[styles.emptyStateText, { color: theme.TEXT_SECONDARY }]}>
            Finansal verileriniz analiz edildikçe burada akıllı öneriler görünecek
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.SURFACE }]}>
        <View style={styles.headerContent}>
          <Ionicons name="bulb" size={24} color={theme.PRIMARY} />
          <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
            Akıllı İçgörüler
          </Text>
        </View>
        
        <Text style={[styles.subtitle, { color: theme.TEXT_SECONDARY }]}>
          {visibleInsights.length} aktif içgörü
        </Text>
      </View>

      {/* Categories */}
      {renderInsightCategories()}

      {/* Insights List */}
      <ScrollView 
        style={styles.insightsList}
        showsVerticalScrollIndicator={false}
      >
        {visibleInsights.map(renderInsightCard)}
        
        {/* Auto-refresh indicator */}
        {autoRefresh && (
          <View style={styles.autoRefreshIndicator}>
            <Ionicons name="refresh" size={16} color={theme.TEXT_SECONDARY} />
            <Text style={[styles.autoRefreshText, { color: theme.TEXT_SECONDARY }]}>
              İçgörüler otomatik olarak güncelleniyor
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 4,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  subtitle: {
    fontSize: 14,
  },
  categoriesContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  categoryChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    marginRight: 8,
    gap: 6,
  },
  categoryText: {
    fontSize: 12,
    fontWeight: '600',
  },
  insightsList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  insightCard: {
    borderRadius: 12,
    borderLeftWidth: 4,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  insightHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  insightTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    flex: 1,
  },
  insightTitle: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  insightMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  priorityText: {
    fontSize: 10,
    fontWeight: '600',
  },
  dismissButton: {
    padding: 4,
  },
  insightDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  metricsContainer: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 12,
  },
  metric: {
    alignItems: 'center',
  },
  metricValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  metricLabel: {
    fontSize: 12,
    marginTop: 2,
  },
  expandedContent: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  detailsTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  detailsText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  recommendation: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 8,
    marginBottom: 6,
  },
  recommendationText: {
    fontSize: 14,
    lineHeight: 20,
    flex: 1,
  },
  actionsContainer: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 12,
    flexWrap: 'wrap',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    gap: 4,
  },
  actionText: {
    fontSize: 12,
    fontWeight: '600',
  },
  timestamp: {
    fontSize: 12,
    marginTop: 8,
    textAlign: 'right',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  autoRefreshIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    padding: 16,
  },
  autoRefreshText: {
    fontSize: 12,
  },
});

export default SmartInsights;
