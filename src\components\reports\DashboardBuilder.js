/**
 * Dashboard Builder Component
 * Kullanıcıların özelleştirilmiş dashboard'lar oluşturmasını sağlar
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Modal,
  FlatList,
  Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../../context/ThemeContext';
import { getTemplateConfig } from '../Templates/TemplateConfig';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

/**
 * Dashboard widget types
 */
const WIDGET_TYPES = {
  CHART: 'chart',
  TABLE: 'table',
  KPI: 'kpi',
  SUMMARY: 'summary'
};

/**
 * Dashboard layout grid
 */
const GRID_SIZE = 4;

const DashboardBuilder = ({ visible, onClose, onSave }) => {
  const { theme } = useTheme();
  const [dashboardTitle, setDashboardTitle] = useState('Yeni Dashboard');
  const [widgets, setWidgets] = useState([]);
  const [selectedWidget, setSelectedWidget] = useState(null);
  const [showWidgetPicker, setShowWidgetPicker] = useState(false);
  const [gridLayout, setGridLayout] = useState(
    Array(GRID_SIZE * GRID_SIZE).fill(null)
  );

  /**
   * Kullanılabilir widget'ları getir
   */
  const getAvailableWidgets = () => [
    {
      id: 'income_summary',
      name: 'Gelir Özeti',
      type: WIDGET_TYPES.KPI,
      icon: '💰',
      size: { width: 2, height: 1 },
      color: '#10B981'
    },
    {
      id: 'expense_summary',
      name: 'Gider Özeti',
      type: WIDGET_TYPES.KPI,
      icon: '💸',
      size: { width: 2, height: 1 },
      color: '#EF4444'
    },
    {
      id: 'monthly_trend',
      name: 'Aylık Trend',
      type: WIDGET_TYPES.CHART,
      icon: '📈',
      size: { width: 4, height: 2 },
      color: '#3B82F6'
    },
    {
      id: 'category_breakdown',
      name: 'Kategori Dağılımı',
      type: WIDGET_TYPES.CHART,
      icon: '🥧',
      size: { width: 2, height: 2 },
      color: '#8B5CF6'
    },
    {
      id: 'recent_transactions',
      name: 'Son İşlemler',
      type: WIDGET_TYPES.TABLE,
      icon: '📋',
      size: { width: 4, height: 2 },
      color: '#F59E0B'
    },
    {
      id: 'budget_progress',
      name: 'Bütçe Durumu',
      type: WIDGET_TYPES.SUMMARY,
      icon: '🎯',
      size: { width: 2, height: 1 },
      color: '#06B6D4'
    }
  ];

  /**
   * Widget ekle
   */
  const addWidget = (widget) => {
    const newWidget = {
      ...widget,
      id: `${widget.id}_${Date.now()}`,
      position: findEmptyPosition(widget.size)
    };

    if (newWidget.position) {
      setWidgets([...widgets, newWidget]);
      updateGridLayout([...widgets, newWidget]);
    } else {
      Alert.alert('Hata', 'Widget için yeterli alan yok.');
    }
    
    setShowWidgetPicker(false);
  };

  /**
   * Boş pozisyon bul
   */
  const findEmptyPosition = (size) => {
    for (let row = 0; row <= GRID_SIZE - size.height; row++) {
      for (let col = 0; col <= GRID_SIZE - size.width; col++) {
        if (canPlaceWidget(row, col, size)) {
          return { row, col };
        }
      }
    }
    return null;
  };

  /**
   * Widget yerleştirilebilir mi kontrol et
   */
  const canPlaceWidget = (row, col, size) => {
    for (let r = row; r < row + size.height; r++) {
      for (let c = col; c < col + size.width; c++) {
        const index = r * GRID_SIZE + c;
        if (gridLayout[index] !== null) {
          return false;
        }
      }
    }
    return true;
  };

  /**
   * Grid layout'u güncelle
   */
  const updateGridLayout = (widgetsList) => {
    const newLayout = Array(GRID_SIZE * GRID_SIZE).fill(null);
    
    widgetsList.forEach((widget, widgetIndex) => {
      if (widget.position) {
        const { row, col } = widget.position;
        for (let r = row; r < row + widget.size.height; r++) {
          for (let c = col; c < col + widget.size.width; c++) {
            const index = r * GRID_SIZE + c;
            newLayout[index] = widgetIndex;
          }
        }
      }
    });
    
    setGridLayout(newLayout);
  };

  /**
   * Widget sil
   */
  const removeWidget = (widgetId) => {
    const newWidgets = widgets.filter(w => w.id !== widgetId);
    setWidgets(newWidgets);
    updateGridLayout(newWidgets);
  };

  /**
   * Dashboard kaydet
   */
  const saveDashboard = () => {
    if (widgets.length === 0) {
      Alert.alert('Hata', 'Dashboard\'a en az bir widget ekleyin.');
      return;
    }

    const dashboardData = {
      title: dashboardTitle,
      widgets: widgets,
      layout: gridLayout,
      createdAt: new Date().toISOString()
    };

    onSave && onSave(dashboardData);
    onClose();
  };

  /**
   * Grid hücresini render et
   */
  const renderGridCell = (item, index) => {
    const row = Math.floor(index / GRID_SIZE);
    const col = index % GRID_SIZE;
    const widgetIndex = gridLayout[index];
    const widget = widgetIndex !== null ? widgets[widgetIndex] : null;

    // Widget'ın başlangıç pozisyonu mu kontrol et
    const isWidgetStart = widget && 
      widget.position.row === row && 
      widget.position.col === col;

    if (widget && isWidgetStart) {
      return (
        <TouchableOpacity
          key={`widget-${widget.id}`}
          style={[
            styles.gridCell,
            {
              backgroundColor: widget.color,
              width: (screenWidth - 48) / GRID_SIZE * widget.size.width,
              height: 80 * widget.size.height,
            }
          ]}
          onPress={() => setSelectedWidget(widget)}
        >
          <Text style={styles.widgetIcon}>{widget.icon}</Text>
          <Text style={styles.widgetName}>{widget.name}</Text>
          
          <TouchableOpacity
            style={styles.removeButton}
            onPress={() => removeWidget(widget.id)}
          >
            <Ionicons name="close" size={16} color="#fff" />
          </TouchableOpacity>
        </TouchableOpacity>
      );
    } else if (widget) {
      // Widget'ın devamı - boş döndür
      return null;
    } else {
      // Boş hücre
      return (
        <TouchableOpacity
          key={`empty-${index}`}
          style={[
            styles.gridCell,
            styles.emptyCell,
            { backgroundColor: theme.cardBackground }
          ]}
          onPress={() => setShowWidgetPicker(true)}
        >
          <Ionicons name="add" size={24} color={theme.textSecondary} />
        </TouchableOpacity>
      );
    }
  };

  /**
   * Widget picker render et
   */
  const renderWidgetPicker = () => (
    <Modal
      visible={showWidgetPicker}
      animationType="slide"
      transparent={true}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.pickerModal, { backgroundColor: theme.cardBackground }]}>
          <Text style={[styles.pickerTitle, { color: theme.textColor }]}>
            Widget Seçin
          </Text>
          
          <FlatList
            data={getAvailableWidgets()}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={[styles.widgetOption, { borderColor: theme.border }]}
                onPress={() => addWidget(item)}
              >
                <Text style={styles.widgetOptionIcon}>{item.icon}</Text>
                <View style={styles.widgetOptionInfo}>
                  <Text style={[styles.widgetOptionName, { color: theme.textColor }]}>
                    {item.name}
                  </Text>
                  <Text style={[styles.widgetOptionSize, { color: theme.textSecondary }]}>
                    {item.size.width}x{item.size.height}
                  </Text>
                </View>
              </TouchableOpacity>
            )}
            keyExtractor={item => item.id}
          />
          
          <TouchableOpacity
            style={[styles.cancelButton, { backgroundColor: theme.textSecondary }]}
            onPress={() => setShowWidgetPicker(false)}
          >
            <Text style={styles.cancelButtonText}>İptal</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );

  return (
    <Modal visible={visible} animationType="slide">
      <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
        <View style={[styles.header, { backgroundColor: theme.cardBackground }]}>
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="close" size={24} color={theme.textColor} />
          </TouchableOpacity>
          
          <Text style={[styles.title, { color: theme.textColor }]}>
            Dashboard Builder
          </Text>
          
          <TouchableOpacity onPress={saveDashboard}>
            <Text style={[styles.saveButton, { color: theme.primary }]}>
              Kaydet
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content}>
          <View style={styles.grid}>
            {gridLayout.map(renderGridCell)}
          </View>
        </ScrollView>

        {renderWidgetPicker()}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  saveButton: {
    fontSize: 16,
    fontWeight: '500',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 4,
  },
  gridCell: {
    width: (screenWidth - 48) / GRID_SIZE,
    height: 80,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  emptyCell: {
    borderWidth: 2,
    borderStyle: 'dashed',
  },
  widgetIcon: {
    fontSize: 24,
    marginBottom: 4,
  },
  widgetName: {
    fontSize: 12,
    fontWeight: '500',
    color: '#fff',
    textAlign: 'center',
  },
  removeButton: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: 'rgba(0,0,0,0.3)',
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  pickerModal: {
    width: screenWidth * 0.9,
    maxHeight: screenHeight * 0.7,
    borderRadius: 12,
    padding: 16,
  },
  pickerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  widgetOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    gap: 12,
  },
  widgetOptionIcon: {
    fontSize: 32,
  },
  widgetOptionInfo: {
    flex: 1,
  },
  widgetOptionName: {
    fontSize: 16,
    fontWeight: '500',
  },
  widgetOptionSize: {
    fontSize: 12,
  },
  cancelButton: {
    marginTop: 16,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default DashboardBuilder;
