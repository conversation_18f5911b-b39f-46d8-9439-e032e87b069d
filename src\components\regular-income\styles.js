import { Colors } from '../../constants/colors';

/**
 * Enhanced modern form styles for regular income components
 * Following Material Design 3.0 principles with improved visual hierarchy
 */
export const formStyles = {
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: Colors.GRAY_50,
  },
  header: {
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: '800',
    color: Colors.GRAY_900,
    marginBottom: 12,
    letterSpacing: 0.3,
  },
  section: {
    backgroundColor: Colors.WHITE,
    borderRadius: 20,
    marginBottom: 28,
    paddingVertical: 28,
    paddingHorizontal: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.12,
    shadowRadius: 16,
    elevation: 8,
    borderWidth: 1,
    borderColor: Colors.GRAY_100,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '800',
    color: Colors.GRAY_800,
    marginBottom: 16,
    letterSpacing: 0.3,
  },
  sectionDivider: {
    height: 2,
    backgroundColor: Colors.GRAY_200,
    marginVertical: 16,
    borderRadius: 1,
  },
  label: {
    fontSize: 16,
    color: Colors.GRAY_700,
    marginBottom: 10,
    fontWeight: '700',
    letterSpacing: 0.2,
  },
  input: {
    borderWidth: 2,
    borderColor: Colors.GRAY_200,
    borderRadius: 16,
    padding: 18,
    fontSize: 16,
    color: Colors.GRAY_800,
    backgroundColor: Colors.WHITE,
    fontWeight: '500',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  inputFocused: {
    borderColor: Colors.PRIMARY,
    backgroundColor: Colors.PRIMARY + '08',
    shadowColor: Colors.PRIMARY,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
  },
  inputError: {
    borderColor: Colors.DANGER,
    backgroundColor: Colors.DANGER + '08',
    shadowColor: Colors.DANGER,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
  },
  summaryContainer: {
    backgroundColor: Colors.INFO + '12',
    borderRadius: 16,
    padding: 20,
    marginVertical: 16,
    borderLeftWidth: 5,
    borderLeftColor: Colors.INFO,
    shadowColor: Colors.INFO,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 4,
  },
  fieldErrorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    marginBottom: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.DANGER + '12',
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: Colors.DANGER,
    shadowColor: Colors.DANGER,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
  },
  fieldErrorText: {
    fontSize: 14,
    color: Colors.DANGER,
    marginLeft: 10,
    flex: 1,
    fontWeight: '600',
    lineHeight: 20,
  },
  errorIndicatorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    marginBottom: 16,
    backgroundColor: Colors.DANGER + '12',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: Colors.DANGER,
  },
  errorIndicator: {
    fontSize: 14,
    color: Colors.DANGER,
    marginLeft: 10,
    fontWeight: '600',
  },
  button: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 16,
    borderRadius: 16,
    backgroundColor: Colors.PRIMARY,
    shadowColor: Colors.PRIMARY,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  buttonText: {
    color: Colors.WHITE,
    fontSize: 16,
    fontWeight: '700',
    letterSpacing: 0.3,
  },
};
