/**
 * Bütçe Oluşturma Ekranı
 * Basit ve çalışır bir bütçe oluşturma formu
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useSQLiteContext } from 'expo-sqlite';
import { useTheme } from '../../context/ThemeContext';
import { MaterialIcons } from '@expo/vector-icons';
import { createBudget } from '../../services/budget';

/**
 * Bütçe oluşturma ekranı
 */
const BudgetCreateScreen = ({ navigation, route }) => {
  const db = useSQLiteContext();
  const { theme } = useTheme();
  const [loading, setLoading] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: 'category_based',
    period_type: 'monthly',
    total_limit: '',
    currency: 'TRY',
    start_date: new Date().toISOString().split('T')[0],
    end_date: null,
  });

  const [categories, setCategories] = useState([]);
  const [selectedCategories, setSelectedCategories] = useState([]);

  // Get theme-safe styles
  const getSafeThemeValue = (path, fallback) => {
    return theme?.colors?.[path] || theme?.[path] || fallback;
  };

  // Load categories
  useEffect(() => {
    loadCategories();
  }, []);

  const loadCategories = async () => {
    try {
      const categoryList = await db.getAllAsync(`
        SELECT id, name, color, icon FROM categories 
        WHERE is_income = 0 
        ORDER BY name ASC
      `);
      setCategories(categoryList);
    } catch (error) {
      console.error('Kategoriler yüklenemedi:', error);
    }
  };

  // Handle form submit
  const handleSubmit = async () => {
    if (!formData.name.trim()) {
      Alert.alert('Hata', 'Bütçe adı gereklidir.');
      return;
    }

    if (!formData.total_limit || parseFloat(formData.total_limit) <= 0) {
      Alert.alert('Hata', 'Geçerli bir bütçe limiti giriniz.');
      return;
    }

    setLoading(true);
    try {
      const budgetData = {
        ...formData,
        total_limit: parseFloat(formData.total_limit),
      };

      const budgetId = await createBudget(db, budgetData, selectedCategories);
      
      Alert.alert(
        'Başarılı', 
        'Bütçe başarıyla oluşturuldu!',
        [{ text: 'Tamam', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      console.error('Bütçe oluşturma hatası:', error);
      Alert.alert('Hata', 'Bütçe oluşturulurken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: getSafeThemeValue('background', '#ffffff'),
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 20,
      borderBottomWidth: 1,
      borderBottomColor: getSafeThemeValue('border', '#e0e0e0'),
    },
    backButton: {
      padding: 8,
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: getSafeThemeValue('text', '#333333'),
      flex: 1,
      textAlign: 'center',
    },
    scrollContent: {
      padding: 20,
    },
    header: {
      marginBottom: 30,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      color: getSafeThemeValue('text', '#333333'),
      marginBottom: 10,
    },
    subtitle: {
      fontSize: 16,
      color: getSafeThemeValue('textSecondary', '#666666'),
    },
    section: {
      marginBottom: 25,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: getSafeThemeValue('text', '#333333'),
      marginBottom: 15,
    },
    formGroup: {
      marginBottom: 20,
    },
    label: {
      fontSize: 16,
      fontWeight: '500',
      color: getSafeThemeValue('text', '#333333'),
      marginBottom: 8,
    },
    input: {
      borderWidth: 1,
      borderColor: getSafeThemeValue('border', '#e0e0e0'),
      borderRadius: 8,
      padding: 12,
      fontSize: 16,
      color: getSafeThemeValue('text', '#333333'),
      backgroundColor: getSafeThemeValue('surface', '#f9f9f9'),
    },
    typeSelector: {
      flexDirection: 'row',
      gap: 10,
    },
    typeButton: {
      flex: 1,
      padding: 15,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: getSafeThemeValue('border', '#e0e0e0'),
      alignItems: 'center',
    },
    typeButtonActive: {
      backgroundColor: getSafeThemeValue('primary', '#007AFF'),
      borderColor: getSafeThemeValue('primary', '#007AFF'),
    },
    typeButtonText: {
      fontSize: 14,
      color: getSafeThemeValue('text', '#333333'),
    },
    typeButtonTextActive: {
      color: '#ffffff',
      fontWeight: '600',
    },
    submitButton: {
      backgroundColor: getSafeThemeValue('primary', '#007AFF'),
      padding: 16,
      borderRadius: 10,
      alignItems: 'center',
      marginTop: 20,
      marginBottom: 30,
    },
    submitButtonDisabled: {
      backgroundColor: getSafeThemeValue('disabled', '#cccccc'),
    },
    submitButtonText: {
      color: '#ffffff',
      fontSize: 18,
      fontWeight: '600',
    },
    backButton: {
      backgroundColor: getSafeThemeValue('secondary', '#6c757d'),
      padding: 12,
      borderRadius: 8,
      alignItems: 'center',
      marginTop: 10,
    },
    backButtonText: {
      color: '#ffffff',
      fontSize: 16,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons 
            name="arrow-back" 
            size={24} 
            color={getSafeThemeValue('text', '#333333')} 
          />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Yeni Bütçe</Text>
        <View style={{ width: 40 }} />
      </View>
      
      <ScrollView style={styles.scrollContent}>
        <View style={styles.header}>
          <Text style={styles.title}>Yeni Bütçe Oluştur</Text>
          <Text style={styles.subtitle}>
            Harcamalarınızı takip etmek için bir bütçe oluşturun
          </Text>
        </View>

        <View style={styles.section}>
          <View style={styles.formGroup}>
            <Text style={styles.label}>Bütçe Adı *</Text>
            <TextInput
              style={styles.input}
              value={formData.name}
              onChangeText={(text) => setFormData(prev => ({ ...prev, name: text }))}
              placeholder="Örnek: Aylık Bütçe"
              placeholderTextColor={getSafeThemeValue('textSecondary', '#999999')}
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Açıklama</Text>
            <TextInput
              style={[styles.input, { height: 80 }]}
              value={formData.description}
              onChangeText={(text) => setFormData(prev => ({ ...prev, description: text }))}
              placeholder="Bütçe hakkında notlar..."
              placeholderTextColor={getSafeThemeValue('textSecondary', '#999999')}
              multiline
              textAlignVertical="top"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Toplam Limit *</Text>
            <TextInput
              style={styles.input}
              value={formData.total_limit}
              onChangeText={(text) => setFormData(prev => ({ ...prev, total_limit: text }))}
              placeholder="0.00"
              placeholderTextColor={getSafeThemeValue('textSecondary', '#999999')}
              keyboardType="numeric"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Dönem Türü</Text>
            <View style={styles.typeSelector}>
              <TouchableOpacity
                style={[
                  styles.typeButton,
                  formData.period_type === 'monthly' && styles.typeButtonActive
                ]}
                onPress={() => setFormData(prev => ({ ...prev, period_type: 'monthly' }))}
              >
                <Text style={[
                  styles.typeButtonText,
                  formData.period_type === 'monthly' && styles.typeButtonTextActive
                ]}>
                  Aylık
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.typeButton,
                  formData.period_type === 'weekly' && styles.typeButtonActive
                ]}
                onPress={() => setFormData(prev => ({ ...prev, period_type: 'weekly' }))}
              >
                <Text style={[
                  styles.typeButtonText,
                  formData.period_type === 'weekly' && styles.typeButtonTextActive
                ]}>
                  Haftalık
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        <TouchableOpacity
          style={[styles.submitButton, loading && styles.submitButtonDisabled]}
          onPress={handleSubmit}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator color="#ffffff" />
          ) : (
            <Text style={styles.submitButtonText}>Bütçe Oluştur</Text>
          )}
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>İptal</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

export default BudgetCreateScreen;
