/**
 * <PERSON>ütçe Oluşturma Ekranı
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md - Stage 2, Phase 4
 * 
 * Tüm bütçe oluşturma componentlerini bir araya getiren ana ekran
 * Maksimum 300 satır - Wizard flow yönetimi
 */

import React, { useState, useRef } from 'react';
import {
  View,
  StyleSheet,
  StatusBar,
  Alert,
  Animated,
  PanResponder,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useSQLiteContext } from 'expo-sqlite';
import { useAppContext } from '../../context/AppContext';
import { useTheme } from '../../context/ThemeContext';
import { createBudget } from '../../services/budget';

// Import all creation components
import {
  BudgetTypeSelector,
  BudgetPeriodSelector,
  CustomDatePicker,
  BudgetLimitInput,
  CurrencySelector,
  CategorySelector,
  CategoryList,
  SmartSuggestions,
  HistoricalAnalysis,
  BudgetRecommendations
} from '../../components/budget/Creation';

// Import wizard components
import {
  WizardHeader,
  WizardNavigation
} from '../../components/budget/Creation';

/**
 * Bütçe oluşturma ekranı
 * @param {Object} props - Component props
 * @param {Object} props.navigation - Navigation objesi
 * @param {Object} props.route - Route objesi
 */
const BudgetCreateScreen = ({ navigation, route }) => {
  const db = useSQLiteContext();
  const { isDarkMode } = useAppContext();
  const { theme } = useTheme();

  // Wizard steps
  const steps = [
    { id: 'type', title: 'Bütçe Türü', component: 'BudgetTypeSelector' },
    { id: 'period', title: 'Dönem', component: 'BudgetPeriodSelector' },
    { id: 'amount', title: 'Limit', component: 'BudgetLimitInput' },
    { id: 'categories', title: 'Kategoriler', component: 'CategorySelector' },
    { id: 'analysis', title: 'Analiz', component: 'HistoricalAnalysis' },
    { id: 'recommendations', title: 'Öneriler', component: 'BudgetRecommendations' },
    { id: 'review', title: 'Özet', component: 'BudgetReview' }
  ];

  // State yönetimi
  const [currentStep, setCurrentStep] = useState(0);
  const [budgetData, setBudgetData] = useState({
    name: '',
    description: '',
    type: 'category_based',
    period_type: 'monthly',
    start_date: new Date().toISOString().split('T')[0],
    end_date: null,
    total_limit: 0,
    currency: 'TRY',
    categories: [],
    categoryBudgets: {},
    analysisData: null
  });
  const [loading, setLoading] = useState(false);

  // Animation
  const slideAnim = useRef(new Animated.Value(0)).current;

  /**
   * Sonraki adıma geç
   */
  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      Animated.timing(slideAnim, {
        toValue: -(currentStep + 1) * 100,
        duration: 300,
        useNativeDriver: true,
      }).start();
      setCurrentStep(currentStep + 1);
    }
  };

  /**
   * Önceki adıma geç
   */
  const handlePrevious = () => {
    if (currentStep > 0) {
      Animated.timing(slideAnim, {
        toValue: -(currentStep - 1) * 100,
        duration: 300,
        useNativeDriver: true,
      }).start();
      setCurrentStep(currentStep - 1);
    }
  };

  /**
   * Belirli adıma git
   * @param {number} stepIndex - Adım indeksi
   */
  const goToStep = (stepIndex) => {
    if (stepIndex >= 0 && stepIndex < steps.length) {
      Animated.timing(slideAnim, {
        toValue: -stepIndex * 100,
        duration: 300,
        useNativeDriver: true,
      }).start();
      setCurrentStep(stepIndex);
    }
  };

  /**
   * Bütçe verilerini güncelle
   * @param {Object} updates - Güncellenecek veriler
   */
  const updateBudgetData = (updates) => {
    setBudgetData(prev => ({ ...prev, ...updates }));
  };

  /**
   * Bütçeyi kaydet
   */
  const handleSaveBudget = async () => {
    try {
      setLoading(true);

      // Validasyon
      if (!budgetData.name.trim()) {
        Alert.alert('Hata', 'Lütfen bütçe adını girin.');
        return;
      }

      if (budgetData.total_limit <= 0) {
        Alert.alert('Hata', 'Lütfen geçerli bir bütçe limiti girin.');
        return;
      }

      if (budgetData.type === 'category_based' && budgetData.categories.length === 0) {
        Alert.alert('Hata', 'Lütfen en az bir kategori seçin.');
        return;
      }

      // Kategori bütçelerini hazırla
      const categoryBudgets = budgetData.categories.map(category => ({
        category_id: category.id,
        limit_amount: budgetData.categoryBudgets[category.id] || 0
      }));

      // Bütçeyi oluştur
      const budgetId = await createBudget(db, budgetData, categoryBudgets);

      Alert.alert(
        'Başarılı',
        'Bütçe başarıyla oluşturuldu.',
        [
          {
            text: 'Tamam',
            onPress: () => navigation.goBack()
          }
        ]
      );

    } catch (error) {
      console.error('Bütçe oluşturma hatası:', error);
      Alert.alert('Hata', 'Bütçe oluşturulurken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Mevcut adımın componentini render et
   */
  const renderCurrentStep = () => {
    const step = steps[currentStep];
    
    switch (step.component) {
      case 'BudgetTypeSelector':
        return (
          <BudgetTypeSelector
            selectedType={budgetData.type}
            onTypeSelect={(type) => updateBudgetData({ type })}
          />
        );

      case 'BudgetPeriodSelector':
        return (
          <BudgetPeriodSelector
            selectedPeriod={budgetData.period_type}
            onPeriodSelect={(period) => updateBudgetData({ period_type: period })}
          />
        );

      case 'BudgetLimitInput':
        return (
          <BudgetLimitInput
            amount={budgetData.total_limit}
            currency={budgetData.currency}
            onAmountChange={(amount) => updateBudgetData({ total_limit: amount })}
            onCurrencyChange={(currency) => updateBudgetData({ currency })}
          />
        );

      case 'CategorySelector':
        return (
          <CategorySelector
            selectedCategories={budgetData.categories.map(cat => cat.id)}
            onCategoriesChange={(selectedIds) => {
              // Mock kategoriler - gerçek implementasyonda database'den gelecek
              const mockCategories = [
                { id: '1', name: 'Yemek', icon: 'restaurant' },
                { id: '2', name: 'Ulaşım', icon: 'directions-car' },
                { id: '3', name: 'Eğlence', icon: 'movie' },
                { id: '4', name: 'Alışveriş', icon: 'shopping-bag' }
              ];

              const selectedCategories = mockCategories.filter(cat =>
                selectedIds.includes(cat.id)
              );

              updateBudgetData({ categories: selectedCategories });
            }}
            multiSelect={true}
          />
        );

      case 'HistoricalAnalysis':
        return (
          <HistoricalAnalysis
            selectedCategories={budgetData.categories}
            period={budgetData.period_type}
            currency={budgetData.currency}
            onAnalysisComplete={(analysisData) => {
              updateBudgetData({ analysisData });
            }}
          />
        );

      case 'BudgetRecommendations':
        return (
          <BudgetRecommendations
            analysisData={budgetData.analysisData}
            selectedCategories={budgetData.categories}
            budgetType={budgetData.type}
            currency={budgetData.currency}
            onRecommendationApply={(recommendations) => {
              // Apply recommendations to budget data
              const newCategoryBudgets = { ...budgetData.categoryBudgets };
              recommendations.forEach(rec => {
                if (rec.categoryId) {
                  newCategoryBudgets[rec.categoryId] = rec.suggestedAmount;
                }
              });
              updateBudgetData({ categoryBudgets: newCategoryBudgets });
            }}
          />
        );

      default:
        return null;
    }
  };

  /**
   * Adım geçerli mi kontrol et
   */
  const isStepValid = () => {
    const step = steps[currentStep];
    
    switch (step.component) {
      case 'BudgetTypeSelector':
        return budgetData.type !== '';
      case 'BudgetPeriodSelector':
        return budgetData.period_type !== '';
      case 'BudgetLimitInput':
        return budgetData.total_limit > 0;
      case 'CategorySelector':
        return budgetData.type !== 'category_based' || budgetData.categories.length > 0;
      default:
        return true;
    }
  };

  return (
    <SafeAreaView 
      style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
      edges={['top']}
    >
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor={theme.BACKGROUND}
      />

      {/* Wizard Header */}
      <WizardHeader
        currentStep={currentStep}
        totalSteps={steps.length}
        stepTitle={steps[currentStep].title}
        onClose={() => navigation.goBack()}
      />

      {/* Main Content */}
      <View style={styles.content}>
        <Animated.View
          style={[
            styles.stepsContainer,
            {
              transform: [
                {
                  translateX: slideAnim.interpolate({
                    inputRange: [-(steps.length - 1) * 100, 0],
                    outputRange: [-(steps.length - 1) * 100, 0],
                    extrapolate: 'clamp',
                  }),
                },
              ],
            },
          ]}
        >
          {steps.map((step, index) => (
            <View key={step.id} style={styles.stepContent}>
              {index === currentStep && renderCurrentStep()}
            </View>
          ))}
        </Animated.View>
      </View>

      {/* Navigation */}
      <WizardNavigation
        currentStep={currentStep}
        totalSteps={steps.length}
        onPrevious={handlePrevious}
        onNext={handleNext}
        onSave={handleSaveBudget}
        isStepValid={isStepValid()}
        loading={loading}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    overflow: 'hidden',
  },
  stepsContainer: {
    flexDirection: 'row',
    flex: 1,
  },
  stepContent: {
    flex: 1,
    width: '100%',
  },
});

export default BudgetCreateScreen;
