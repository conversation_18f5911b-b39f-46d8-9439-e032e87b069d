import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';

/**
 * <PERSON><PERSON>
 * <PERSON><PERSON> tü<PERSON> filtrelemek için kategori se<PERSON>
 */
const ReportCategories = ({ 
  categories, 
  selectedCategory, 
  onCategorySelect, 
  theme 
}) => {
  return (
    <View style={styles.container}>
      <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
        🏷️ Kategoriler
      </Text>
      
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.categoryList}
      >
        {categories.map((category) => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryCard,
              { backgroundColor: theme.SURFACE || theme.colors?.surface || '#f9f9f9' },
              selectedCategory === category.id && {
                backgroundColor: theme.PRIMARY,
                borderColor: theme.PRIMARY,
              }
            ]}
            onPress={() => onCategorySelect(category.id)}
          >
            <Text style={[
              styles.categoryName,
              { color: theme.TEXT_PRIMARY },
              selectedCategory === category.id && { color: '#fff' }
            ]}>
              {category.name}
            </Text>
            
            <View style={[
              styles.categoryBadge,
              { backgroundColor: theme.BACKGROUND },
              selectedCategory === category.id && { backgroundColor: 'rgba(255,255,255,0.2)' }
            ]}>
              <Text style={[
                styles.categoryCount,
                { color: theme.TEXT_SECONDARY },
                selectedCategory === category.id && { color: '#fff' }
              ]}>
                {category.count}
              </Text>
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  categoryList: {
    paddingHorizontal: 12,
  },
  categoryCard: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginHorizontal: 4,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'transparent',
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  categoryName: {
    fontSize: 14,
    fontWeight: '500',
    marginRight: 8,
  },
  categoryBadge: {
    minWidth: 24,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 6,
  },
  categoryCount: {
    fontSize: 12,
    fontWeight: 'bold',
  },
});

export default ReportCategories;
