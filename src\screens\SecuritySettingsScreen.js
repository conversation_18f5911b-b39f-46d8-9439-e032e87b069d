import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useAppContext } from '../context/AppContext';
import { useAuth } from '../context/AuthContext';

/**
 * Güvenlik Ayarları Ekranı
 * PIN sistemi kaldırıldı - Sadece temel güvenlik ayarları
 */
export default function SecuritySettingsScreen({ navigation }) {
  const { theme } = useAppContext();
  const { resetAllData } = useAuth();

  // Test için tüm verileri sıfırlama
  const handleResetAllData = async () => {
    Alert.alert(
      '⚠️ TEHLİKELİ İŞLEM',
      'Bu işlem tüm uygulama verilerini silecek. Bu işlem GERİ ALINAMAZ!\n\nDevam etmek istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'EVET, HEPSİNİ SİL',
          style: 'destructive',
          onPress: async () => {
            try {
              const result = await resetAllData();
              if (result.success) {
                Alert.alert(
                  '✅ Başarılı',
                  'Tüm veriler temizlendi.',
                  [{ text: 'Tamam', onPress: () => navigation.goBack() }]
                );
              } else {
                Alert.alert('❌ Hata', result.error || 'Veri temizleme başarısız');
              }
            } catch (error) {
              Alert.alert('❌ Hata', 'Veri temizleme sırasında hata oluştu');
            }
          }
        }
      ]
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.PRIMARY }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color={theme.WHITE} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.WHITE }]}>Güvenlik</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.content}>
        {/* PIN Ayarları - Kaldırıldı Bilgilendirmesi */}
        <View style={[styles.section, { backgroundColor: theme.CARD }]}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>Güvenlik Ayarları</Text>
          
          <TouchableOpacity
            style={styles.settingItem}
            onPress={() => navigation.navigate('PinSetupFlow')}
          >
            <View style={styles.settingLeft}>
              <MaterialIcons name="lock" size={24} color={theme.TEXT_PRIMARY} />
              <View style={styles.settingText}>
                <Text style={[styles.settingTitle, { color: theme.TEXT_PRIMARY }]}>PIN Koruması</Text>
                <Text style={[styles.settingDescription, { color: theme.TEXT_SECONDARY }]}>
                  Uygulama için PIN güvenliği kurulumu
                </Text>
              </View>
            </View>
            <MaterialIcons name="arrow-forward-ios" size={20} color={theme.TEXT_SECONDARY} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={() => navigation.navigate('SecurityQuestions', { mode: 'setup' })}
          >
            <View style={styles.settingLeft}>
              <MaterialIcons name="help-outline" size={24} color={theme.TEXT_PRIMARY} />
              <View style={styles.settingText}>
                <Text style={[styles.settingTitle, { color: theme.TEXT_PRIMARY }]}>Güvenlik Soruları</Text>
                <Text style={[styles.settingDescription, { color: theme.TEXT_SECONDARY }]}>
                  PIN unutulduğunda hesap kurtarma soruları
                </Text>
              </View>
            </View>
            <MaterialIcons name="arrow-forward-ios" size={20} color={theme.TEXT_SECONDARY} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={() => navigation.navigate('PinSecurity', { mode: 'change' })}
          >
            <View style={styles.settingLeft}>
              <MaterialIcons name="edit" size={24} color={theme.TEXT_PRIMARY} />
              <View style={styles.settingText}>
                <Text style={[styles.settingTitle, { color: theme.TEXT_PRIMARY }]}>PIN Değiştir</Text>
                <Text style={[styles.settingDescription, { color: theme.TEXT_SECONDARY }]}>
                  Mevcut PIN'inizi değiştirin
                </Text>
              </View>
            </View>
            <MaterialIcons name="arrow-forward-ios" size={20} color={theme.TEXT_SECONDARY} />
          </TouchableOpacity>
        </View>

        {/* Gelecekteki Özellikler */}
        <View style={[styles.section, { backgroundColor: theme.CARD }]}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>Gelecek Özellikler</Text>
          
          <View style={[styles.settingItem, { opacity: 0.5 }]}>
            <View style={styles.settingLeft}>
              <MaterialIcons name="lock" size={24} color={theme.TEXT_SECONDARY} />
              <View style={styles.settingText}>
                <Text style={[styles.settingTitle, { color: theme.TEXT_SECONDARY }]}>PIN Koruması</Text>
                <Text style={[styles.settingDescription, { color: theme.TEXT_SECONDARY }]}>
                  4 haneli PIN ile uygulama koruması (Yakında)
                </Text>
              </View>
            </View>
            <Switch
              value={false}
              disabled={true}
              trackColor={{ false: theme.BORDER, true: theme.PRIMARY }}
              thumbColor={theme.WHITE}
            />
          </View>

          <View style={[styles.settingItem, { opacity: 0.5 }]}>
            <View style={styles.settingLeft}>
              <MaterialIcons name="fingerprint" size={24} color={theme.TEXT_SECONDARY} />
              <View style={styles.settingText}>
                <Text style={[styles.settingTitle, { color: theme.TEXT_SECONDARY }]}>Biometrik Kimlik</Text>
                <Text style={[styles.settingDescription, { color: theme.TEXT_SECONDARY }]}>
                  Parmak izi veya yüz tanıma (Yakında)
                </Text>
              </View>
            </View>
            <Switch
              value={false}
              disabled={true}
              trackColor={{ false: theme.BORDER, true: theme.PRIMARY }}
              thumbColor={theme.WHITE}
            />
          </View>

          <View style={[styles.settingItem, { opacity: 0.5, borderBottomWidth: 0 }]}>
            <View style={styles.settingLeft}>
              <MaterialIcons name="timer" size={24} color={theme.TEXT_SECONDARY} />
              <View style={styles.settingText}>
                <Text style={[styles.settingTitle, { color: theme.TEXT_SECONDARY }]}>Otomatik Kilit</Text>
                <Text style={[styles.settingDescription, { color: theme.TEXT_SECONDARY }]}>
                  Belirli süre sonra uygulamayı kilitle (Yakında)
                </Text>
              </View>
            </View>
            <Switch
              value={false}
              disabled={true}
              trackColor={{ false: theme.BORDER, true: theme.PRIMARY }}
              thumbColor={theme.WHITE}
            />
          </View>
        </View>

        {/* Uygulama Güvenliği */}
        <View style={[styles.section, { backgroundColor: theme.CARD }]}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>Uygulama Güvenliği</Text>
          
          <View style={styles.settingItem}>
            <View style={styles.settingLeft}>
              <MaterialIcons name="security" size={24} color="#4CAF50" />
              <View style={styles.settingText}>
                <Text style={[styles.settingTitle, { color: theme.TEXT_PRIMARY }]}>Veri Şifrelemesi</Text>
                <Text style={[styles.settingDescription, { color: theme.TEXT_SECONDARY }]}>
                  Tüm verileriniz yerel olarak şifrelenir
                </Text>
              </View>
            </View>
            <MaterialIcons name="check-circle" size={24} color="#4CAF50" />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingLeft}>
              <MaterialIcons name="offline-bolt" size={24} color="#4CAF50" />
              <View style={styles.settingText}>
                <Text style={[styles.settingTitle, { color: theme.TEXT_PRIMARY }]}>Çevrimdışı Çalışma</Text>
                <Text style={[styles.settingDescription, { color: theme.TEXT_SECONDARY }]}>
                  Verileriniz sadece cihazınızda saklanır
                </Text>
              </View>
            </View>
            <MaterialIcons name="check-circle" size={24} color="#4CAF50" />
          </View>

          <View style={[styles.settingItem, { borderBottomWidth: 0 }]}>
            <View style={styles.settingLeft}>
              <MaterialIcons name="no-accounts" size={24} color="#4CAF50" />
              <View style={styles.settingText}>
                <Text style={[styles.settingTitle, { color: theme.TEXT_PRIMARY }]}>Hesap Gerektirmez</Text>
                <Text style={[styles.settingDescription, { color: theme.TEXT_SECONDARY }]}>
                  Kayıt olmadan kullanabilirsiniz
                </Text>
              </View>
            </View>
            <MaterialIcons name="check-circle" size={24} color="#4CAF50" />
          </View>
        </View>

        {/* Tehlikeli İşlemler */}
        <View style={[styles.section, { backgroundColor: theme.CARD }]}>
          <Text style={[styles.sectionTitle, { color: '#FF6B6B' }]}>Tehlikeli İşlemler</Text>
          
          <TouchableOpacity 
            style={[styles.settingItem, { borderBottomWidth: 0 }]}
            onPress={handleResetAllData}
          >
            <View style={styles.settingLeft}>
              <MaterialIcons name="delete-forever" size={24} color="#FF6B6B" />
              <View style={styles.settingText}>
                <Text style={[styles.settingTitle, { color: '#FF6B6B' }]}>Tüm Verileri Sıfırla</Text>
                <Text style={[styles.settingDescription, { color: theme.TEXT_SECONDARY }]}>
                  Tüm uygulama verilerini kalıcı olarak sil
                </Text>
              </View>
            </View>
            <MaterialIcons name="chevron-right" size={24} color="#FF6B6B" />
          </TouchableOpacity>
        </View>

        {/* Bilgilendirme */}
        <View style={[styles.infoSection, { backgroundColor: theme.CARD }]}>
          <MaterialIcons name="info" size={24} color="#2196F3" />
          <Text style={[styles.infoText, { color: theme.TEXT_SECONDARY }]}>
            Bu uygulama tamamen çevrimdışı çalışır. Verileriniz sadece cihazınızda saklanır ve hiçbir sunucuya gönderilmez.
            PIN ve biometrik doğrulama özellikleri gelecek güncellemelerde eklenecektir.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  headerTitle: {
    flex: 1,
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingText: {
    marginLeft: 16,
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  settingDescription: {
    fontSize: 14,
  },
  infoCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 16,
    backgroundColor: '#E8F5E8',
    borderRadius: 12,
    marginBottom: 8,
  },
  infoTextContainer: {
    marginLeft: 12,
    flex: 1,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  infoDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  infoSection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 16,
    borderRadius: 12,
    marginTop: 8,
  },
  infoText: {
    fontSize: 14,
    lineHeight: 20,
    marginLeft: 12,
    flex: 1,
  },
});
