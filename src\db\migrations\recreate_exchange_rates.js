/**
 * Exchange rates tablosunu yeniden oluşturur
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<void>}
 */
export const recreateExchangeRatesTable = async (db) => {
  try {
    console.log('Exchange rates tablosu yeniden oluşturuluyor...');
    
    // Tabloyu sil
    await db.execAsync(`DROP TABLE IF EXISTS exchange_rates`);
    
    // Tabloyu yeniden oluştur
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS exchange_rates (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        base_currency TEXT NOT NULL DEFAULT 'TRY',
        target_currency TEXT NOT NULL,
        rate DECIMAL(20,10) NOT NULL,
        date TEXT NOT NULL DEFAULT (date('now')),
        fetch_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    // İndeks oluştur
    await db.execAsync(`
      CREATE INDEX IF NOT EXISTS idx_exchange_rates_lookup
      ON exchange_rates (base_currency, date)
    `);
    
    console.log('Exchange rates tablosu başarıyla yeniden oluşturuldu.');
  } catch (error) {
    console.error('Exchange rates tablosu yeniden oluşturma hatası:', error);
    throw error;
  }
};
