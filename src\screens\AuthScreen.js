import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContext';
import { useAppContext } from '../context/AppContext';

/**
 * Basit kimlik doğrulama ekranı
 * PIN sistemi kaldırıldı
 */
function AuthScreen({ navigation }) {
  const { theme } = useAppContext();
  const { setIsAuthenticated } = useAuth();

  const handleLogin = () => {
    setIsAuthenticated(true);
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      <View style={styles.content}>
        <View style={styles.logoContainer}>
          <MaterialIcons name="account-balance-wallet" size={80} color={theme.PRIMARY} />
          <Text style={[styles.appName, { color: theme.TEXT_PRIMARY }]}>
            Finans Uygulaması
          </Text>
          <Text style={[styles.subtitle, { color: theme.TEXT_SECONDARY }]}>
            Kişisel finans yönetiminiz
          </Text>
        </View>

        <View style={styles.authSection}>
          <Text style={[styles.welcomeText, { color: theme.TEXT_PRIMARY }]}>
            👋 Hoş Geldiniz!
          </Text>
          <Text style={[styles.description, { color: theme.TEXT_SECONDARY }]}>
            PIN sistemi kaldırıldı. Basit giriş ile devam edin.
          </Text>

          <TouchableOpacity
            style={[styles.loginButton, { backgroundColor: theme.PRIMARY }]}
            onPress={handleLogin}
          >
            <MaterialIcons name="login" size={24} color={theme.WHITE} />
            <Text style={[styles.loginButtonText, { color: theme.WHITE }]}>
              Giriş Yap
            </Text>
          </TouchableOpacity>

          <View style={styles.infoContainer}>
            <MaterialIcons name="info" size={20} color={theme.TEXT_SECONDARY} />
            <Text style={[styles.infoText, { color: theme.TEXT_SECONDARY }]}>
              Artık PIN veya biometrik doğrulama gerektirmez
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 24,
    justifyContent: 'center',
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 60,
  },
  appName: {
    fontSize: 28,
    fontWeight: 'bold',
    marginTop: 16,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    marginTop: 8,
    textAlign: 'center',
  },
  authSection: {
    alignItems: 'center',
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 24,
  },
  loginButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 40,
    paddingVertical: 16,
    borderRadius: 25,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  loginButtonText: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  infoText: {
    fontSize: 14,
    marginLeft: 8,
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default AuthScreen;
