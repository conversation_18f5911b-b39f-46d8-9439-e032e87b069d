import React, { useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';

/**
 * ExportManager Test Component
 * Bu component ExportManager'ın yüklenip yüklenmediğini test eder
 */
const ExportManagerTest = () => {
  useEffect(() => {
    console.log('🔍 ExportManagerTest: Component mounted');
    
    // Test 1: Direct import test
    try {
      console.log('🔍 ExportManagerTest: Attempting to import ExportManager...');
      
      // Dynamic import test
      import('./ExportManager')
        .then((module) => {
          console.log('🔍 ExportManagerTest: Dynamic import successful');
          console.log('🔍 ExportManagerTest: Module keys:', Object.keys(module));
          console.log('🔍 ExportManagerTest: Default export type:', typeof module.default);
          console.log('🔍 ExportManagerTest: ExportManagerComponent type:', typeof module.ExportManagerComponent);
          
          if (module.default && typeof module.default.exportReport === 'function') {
            console.log('✅ ExportManagerTest: ExportManager.exportReport is available');
          } else {
            console.log('❌ ExportManagerTest: ExportManager.exportReport is NOT available');
          }
        })
        .catch((error) => {
          console.error('❌ ExportManagerTest: Dynamic import failed:', error);
        });
        
    } catch (error) {
      console.error('❌ ExportManagerTest: Import test failed:', error);
    }
  }, []);

  const testStaticImport = async () => {
    try {
      console.log('🔍 ExportManagerTest: Testing static import...');
      
      // Static import test
      const ExportManager = require('./ExportManager').default;
      console.log('🔍 ExportManagerTest: Static import type:', typeof ExportManager);
      console.log('🔍 ExportManagerTest: Static import exportReport:', typeof ExportManager?.exportReport);
      
      if (ExportManager && typeof ExportManager.exportReport === 'function') {
        console.log('✅ ExportManagerTest: Static import successful');
        Alert.alert('Test Başarılı', 'ExportManager static import çalışıyor');
      } else {
        console.log('❌ ExportManagerTest: Static import failed');
        Alert.alert('Test Başarısız', 'ExportManager static import çalışmıyor');
      }
    } catch (error) {
      console.error('❌ ExportManagerTest: Static import error:', error);
      Alert.alert('Test Hatası', `Static import hatası: ${error.message}`);
    }
  };

  const testExportCall = async () => {
    try {
      console.log('🔍 ExportManagerTest: Testing export call...');
      
      const ExportManager = require('./ExportManager').default;
      
      const testData = {
        title: 'Test Raporu',
        data: [{ id: 1, name: 'Test', value: 100 }],
        config: { format: 'pdf' }
      };
      
      await ExportManager.exportReport('pdf', testData);
      console.log('✅ ExportManagerTest: Export call successful');
      Alert.alert('Export Test Başarılı', 'ExportManager.exportReport çalışıyor');
      
    } catch (error) {
      console.error('❌ ExportManagerTest: Export call error:', error);
      Alert.alert('Export Test Hatası', `Export call hatası: ${error.message}`);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>ExportManager Test</Text>
      
      <TouchableOpacity style={styles.button} onPress={testStaticImport}>
        <Text style={styles.buttonText}>Test Static Import</Text>
      </TouchableOpacity>
      
      <TouchableOpacity style={styles.button} onPress={testExportCall}>
        <Text style={styles.buttonText}>Test Export Call</Text>
      </TouchableOpacity>
      
      <Text style={styles.note}>
        Console loglarını kontrol edin
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 30,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
    marginVertical: 10,
    minWidth: 200,
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  note: {
    marginTop: 30,
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
});

export default ExportManagerTest;
