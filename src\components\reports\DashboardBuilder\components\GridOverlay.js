import React from 'react';
import { View, StyleSheet } from 'react-native';
import Svg, { Defs, Pattern, Rect } from 'react-native-svg';

/**
 * Grid Overlay - Dashboard canvas için grid arka planı
 */
const GridOverlay = ({
  width,
  height,
  gridSize = 20,
  color = '#e0e0e0',
  opacity = 0.3,
}) => {
  return (
    <View style={[styles.container, { width, height }]}>
      <Svg width={width} height={height} style={styles.svg}>
        <Defs>
          <Pattern
            id="grid"
            width={gridSize}
            height={gridSize}
            patternUnits="userSpaceOnUse"
          >
            <Rect
              width={gridSize}
              height={gridSize}
              fill="none"
              stroke={color}
              strokeWidth={0.5}
              opacity={opacity}
            />
          </Pattern>
        </Defs>
        <Rect
          width={width}
          height={height}
          fill="url(#grid)"
        />
      </Svg>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    pointerEvents: 'none',
  },
  svg: {
    position: 'absolute',
    top: 0,
    left: 0,
  },
});

export default GridOverlay;
