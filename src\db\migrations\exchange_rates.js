/**
 * <PERSON>ö<PERSON>z kurları tablosu için migrasyon
 *
 * @param {object} db - SQLite veritabanı nesnesi
 * @returns {Promise<void>}
 */
export const migrateExchangeRates = async (db) => {
  try {
    console.log('<PERSON>öviz kurları tablosu migrasyonu başlatılıyor...');

    // Eski tabloyu temizle
    await db.execAsync(`DROP TABLE IF EXISTS exchange_rates`);

    // Yeni tabloyu oluştur
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS exchange_rates (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        base_currency TEXT NOT NULL,
        target_currency TEXT NOT NULL,
        currency TEXT NOT NULL,
        rate DECIMAL(20,10) NOT NULL,
        date TEXT NOT NULL,
        fetch_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(base_currency, target_currency, date)
      )
    `);

    // İndeks oluştur
    await db.execAsync(`
      CREATE INDEX IF NOT EXISTS idx_exchange_rates_lookup
      ON exchange_rates (base_currency, date)
    `);

    console.log('Döviz kurları tablosu migrasyonu tamamlandı.');
  } catch (error) {
    console.error('Döviz kurları tablosu migrasyon hatası:', error);
    throw error;
  }
};
