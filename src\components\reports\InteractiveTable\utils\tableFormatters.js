/**
 * Tablo biçimlendirme yardımcı fonksiyonları
 */

/**
 * Para birimi formatla
 * @param {number} value - Formatlanacak değer
 * @param {string} currency - Para birimi kodu (TRY, USD, EUR)
 * @param {string} locale - <PERSON><PERSON> ayar (tr-TR, en-US)
 * @returns {string} Formatlanmış değer
 */
export const formatCurrency = (value, currency = 'TRY', locale = 'tr-TR') => {
  if (value === null || value === undefined || isNaN(value)) return '-';
  
  try {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  } catch (error) {
    // Fallback için basit formatla
    return `${value.toFixed(2)} ${currency}`;
  }
};

/**
 * Sayı formatla
 * @param {number} value - Formatlanacak değer
 * @param {number} decimals - Ondalık basamak sayısı
 * @param {string} locale - <PERSON><PERSON> ayar
 * @returns {string} Formatlanmış değer
 */
export const formatNumber = (value, decimals = 2, locale = 'tr-TR') => {
  if (value === null || value === undefined || isNaN(value)) return '-';
  
  try {
    return new Intl.NumberFormat(locale, {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
    }).format(value);
  } catch (error) {
    return value.toFixed(decimals);
  }
};

/**
 * Yüzde formatla
 * @param {number} value - Formatlanacak değer (0-1 arası)
 * @param {number} decimals - Ondalık basamak sayısı
 * @param {string} locale - Yerel ayar
 * @returns {string} Formatlanmış değer
 */
export const formatPercentage = (value, decimals = 2, locale = 'tr-TR') => {
  if (value === null || value === undefined || isNaN(value)) return '-';
  
  try {
    return new Intl.NumberFormat(locale, {
      style: 'percent',
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
    }).format(value);
  } catch (error) {
    return `${(value * 100).toFixed(decimals)}%`;
  }
};

/**
 * Tarih formatla
 * @param {string|Date} value - Formatlanacak tarih
 * @param {string} format - Format türü (short, medium, long, full)
 * @param {string} locale - Yerel ayar
 * @returns {string} Formatlanmış tarih
 */
export const formatDate = (value, format = 'short', locale = 'tr-TR') => {
  if (!value) return '-';
  
  const date = new Date(value);
  if (isNaN(date.getTime())) return 'Geçersiz tarih';
  
  try {
    const options = getDateFormatOptions(format);
    return new Intl.DateTimeFormat(locale, options).format(date);
  } catch (error) {
    return date.toLocaleDateString();
  }
};

/**
 * Zaman formatla
 * @param {string|Date} value - Formatlanacak zaman
 * @param {boolean} includeSeconds - Saniye dahil edilsin mi
 * @param {string} locale - Yerel ayar
 * @returns {string} Formatlanmış zaman
 */
export const formatTime = (value, includeSeconds = false, locale = 'tr-TR') => {
  if (!value) return '-';
  
  const date = new Date(value);
  if (isNaN(date.getTime())) return 'Geçersiz zaman';
  
  try {
    const options = {
      hour: '2-digit',
      minute: '2-digit',
      ...(includeSeconds && { second: '2-digit' }),
    };
    return new Intl.DateTimeFormat(locale, options).format(date);
  } catch (error) {
    return includeSeconds ? date.toLocaleTimeString() : date.toLocaleTimeString().slice(0, -3);
  }
};

/**
 * Tarih ve zaman formatla
 * @param {string|Date} value - Formatlanacak tarih-zaman
 * @param {string} dateFormat - Tarih formatı
 * @param {boolean} includeSeconds - Saniye dahil edilsin mi
 * @param {string} locale - Yerel ayar
 * @returns {string} Formatlanmış tarih-zaman
 */
export const formatDateTime = (value, dateFormat = 'short', includeSeconds = false, locale = 'tr-TR') => {
  if (!value) return '-';
  
  const date = new Date(value);
  if (isNaN(date.getTime())) return 'Geçersiz tarih-zaman';
  
  try {
    const dateOptions = getDateFormatOptions(dateFormat);
    const timeOptions = {
      hour: '2-digit',
      minute: '2-digit',
      ...(includeSeconds && { second: '2-digit' }),
    };
    
    const options = { ...dateOptions, ...timeOptions };
    return new Intl.DateTimeFormat(locale, options).format(date);
  } catch (error) {
    return date.toLocaleString();
  }
};

/**
 * Boolean değeri formatla
 * @param {boolean} value - Formatlanacak boolean değer
 * @param {Object} labels - Özel etiketler
 * @returns {string} Formatlanmış değer
 */
export const formatBoolean = (value, labels = { true: 'Evet', false: 'Hayır' }) => {
  if (value === null || value === undefined) return '-';
  return Boolean(value) ? labels.true : labels.false;
};

/**
 * Telefon numarası formatla
 * @param {string} value - Formatlanacak telefon numarası
 * @param {string} format - Format türü (international, national, local)
 * @returns {string} Formatlanmış telefon numarası
 */
export const formatPhone = (value, format = 'national') => {
  if (!value) return '-';
  
  // Sadece rakamları al
  const numbers = value.toString().replace(/\D/g, '');
  
  if (numbers.length === 0) return '-';
  
  // Türkiye telefon numarası formatı
  if (numbers.startsWith('90') && numbers.length === 12) {
    const areaCode = numbers.slice(2, 5);
    const first = numbers.slice(5, 8);
    const second = numbers.slice(8, 10);
    const third = numbers.slice(10, 12);
    
    switch (format) {
      case 'international':
        return `+90 ${areaCode} ${first} ${second} ${third}`;
      case 'national':
        return `0${areaCode} ${first} ${second} ${third}`;
      case 'local':
        return `${first} ${second} ${third}`;
      default:
        return `0${areaCode} ${first} ${second} ${third}`;
    }
  }
  
  // 11 haneli Türkiye numarası
  if (numbers.length === 11 && numbers.startsWith('0')) {
    const areaCode = numbers.slice(1, 4);
    const first = numbers.slice(4, 7);
    const second = numbers.slice(7, 9);
    const third = numbers.slice(9, 11);
    
    switch (format) {
      case 'international':
        return `+90 ${areaCode} ${first} ${second} ${third}`;
      case 'national':
        return `0${areaCode} ${first} ${second} ${third}`;
      case 'local':
        return `${first} ${second} ${third}`;
      default:
        return `0${areaCode} ${first} ${second} ${third}`;
    }
  }
  
  // Diğer durumlar için olduğu gibi döndür
  return value;
};

/**
 * E-posta adresini formatla
 * @param {string} value - E-posta adresi
 * @param {boolean} lowercase - Küçük harfe çevir
 * @returns {string} Formatlanmış e-posta
 */
export const formatEmail = (value, lowercase = true) => {
  if (!value) return '-';
  
  const email = value.toString().trim();
  return lowercase ? email.toLowerCase() : email;
};

/**
 * URL formatla
 * @param {string} value - URL
 * @param {boolean} addProtocol - Protokol ekle
 * @returns {string} Formatlanmış URL
 */
export const formatUrl = (value, addProtocol = true) => {
  if (!value) return '-';
  
  let url = value.toString().trim();
  
  if (addProtocol && !url.startsWith('http://') && !url.startsWith('https://')) {
    url = `https://${url}`;
  }
  
  return url;
};

/**
 * Büyük sayıları kısaltarak formatla
 * @param {number} value - Formatlanacak sayı
 * @param {number} decimals - Ondalık basamak sayısı
 * @param {string} locale - Yerel ayar
 * @returns {string} Formatlanmış değer
 */
export const formatLargeNumber = (value, decimals = 1, locale = 'tr-TR') => {
  if (value === null || value === undefined || isNaN(value)) return '-';
  
  const absValue = Math.abs(value);
  const sign = value < 0 ? '-' : '';
  
  if (absValue >= 1e12) {
    return `${sign}${(absValue / 1e12).toFixed(decimals)}T`;
  } else if (absValue >= 1e9) {
    return `${sign}${(absValue / 1e9).toFixed(decimals)}M`;
  } else if (absValue >= 1e6) {
    return `${sign}${(absValue / 1e6).toFixed(decimals)}M`;
  } else if (absValue >= 1e3) {
    return `${sign}${(absValue / 1e3).toFixed(decimals)}B`;
  } else {
    return formatNumber(value, decimals, locale);
  }
};

/**
 * Dosya boyutunu formatla
 * @param {number} bytes - Byte cinsinden boyut
 * @param {number} decimals - Ondalık basamak sayısı
 * @returns {string} Formatlanmış boyut
 */
export const formatFileSize = (bytes, decimals = 2) => {
  if (bytes === 0) return '0 Bytes';
  if (!bytes || isNaN(bytes)) return '-';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(decimals))} ${sizes[i]}`;
};

/**
 * Metin kısaltma
 * @param {string} text - Kısaltılacak metin
 * @param {number} maxLength - Maksimum uzunluk
 * @param {string} ellipsis - Kısaltma işareti
 * @returns {string} Kısaltılmış metin
 */
export const truncateText = (text, maxLength = 50, ellipsis = '...') => {
  if (!text) return '';
  
  const str = text.toString();
  if (str.length <= maxLength) return str;
  
  return str.slice(0, maxLength - ellipsis.length) + ellipsis;
};

/**
 * Tarih format seçeneklerini getir
 * @param {string} format - Format türü
 * @returns {Object} Format seçenekleri
 */
const getDateFormatOptions = (format) => {
  const formats = {
    short: {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    },
    medium: {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
    },
    long: {
      day: '2-digit',
      month: 'long',
      year: 'numeric',
    },
    full: {
      weekday: 'long',
      day: '2-digit',
      month: 'long',
      year: 'numeric',
    },
    monthYear: {
      month: 'long',
      year: 'numeric',
    },
    dayMonth: {
      day: '2-digit',
      month: 'short',
    },
  };
  
  return formats[format] || formats.short;
};

/**
 * Hücre değerini otomatik formatla
 * @param {*} value - Formatlanacak değer
 * @param {Object} column - Sütun yapılandırması
 * @returns {string} Formatlanmış değer
 */
export const autoFormatCellValue = (value, column) => {
  if (value === null || value === undefined) return '-';
  
  switch (column.type) {
    case 'currency':
      return formatCurrency(value, column.currency, column.locale);
    case 'number':
      return formatNumber(value, column.decimals, column.locale);
    case 'percentage':
      return formatPercentage(value, column.decimals, column.locale);
    case 'date':
      return formatDate(value, column.dateFormat, column.locale);
    case 'time':
      return formatTime(value, column.includeSeconds, column.locale);
    case 'datetime':
      return formatDateTime(value, column.dateFormat, column.includeSeconds, column.locale);
    case 'boolean':
      return formatBoolean(value, column.labels);
    case 'phone':
      return formatPhone(value, column.phoneFormat);
    case 'email':
      return formatEmail(value, column.lowercase);
    case 'url':
      return formatUrl(value, column.addProtocol);
    case 'filesize':
      return formatFileSize(value, column.decimals);
    case 'text':
    default:
      if (column.truncate && column.maxLength) {
        return truncateText(value, column.maxLength, column.ellipsis);
      }
      return value.toString();
  }
};

/**
 * CSS sınıfı oluştur
 * @param {*} value - Değer
 * @param {Object} column - Sütun yapılandırması
 * @returns {string} CSS sınıfı
 */
export const getCellClassName = (value, column) => {
  const classes = ['table-cell'];
  
  // Tip bazlı sınıf
  classes.push(`cell-${column.type}`);
  
  // Hizalama
  if (column.align) {
    classes.push(`text-${column.align}`);
  }
  
  // Koşullu biçimlendirme
  if (column.conditionalFormatting) {
    column.conditionalFormatting.forEach(rule => {
      if (evaluateCondition(value, rule.condition)) {
        classes.push(rule.className);
      }
    });
  }
  
  // Değer bazlı sınıflar
  if (value === null || value === undefined || value === '') {
    classes.push('cell-empty');
  }
  
  if (column.type === 'number' || column.type === 'currency') {
    if (value < 0) classes.push('cell-negative');
    if (value > 0) classes.push('cell-positive');
    if (value === 0) classes.push('cell-zero');
  }
  
  return classes.join(' ');
};

/**
 * Koşul değerlendirme
 * @param {*} value - Değer
 * @param {Object} condition - Koşul
 * @returns {boolean} Koşul sonucu
 */
const evaluateCondition = (value, condition) => {
  if (!condition) return false;
  
  switch (condition.operator) {
    case 'equals':
      return value === condition.value;
    case 'notEquals':
      return value !== condition.value;
    case 'greaterThan':
      return parseFloat(value) > parseFloat(condition.value);
    case 'lessThan':
      return parseFloat(value) < parseFloat(condition.value);
    case 'contains':
      return value.toString().toLowerCase().includes(condition.value.toLowerCase());
    case 'startsWith':
      return value.toString().toLowerCase().startsWith(condition.value.toLowerCase());
    case 'endsWith':
      return value.toString().toLowerCase().endsWith(condition.value.toLowerCase());
    default:
      return false;
  }
};
