import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { useAppContext } from '../context/AppContext';
import { useAuth } from '../context/AuthContext';
import * as SecureStore from 'expo-secure-store';

// Güvenlik soruları listesi
const SECURITY_QUESTIONS = [
  'İlk evcil hayvanınızın adı neydi?',
  'Doğduğunuz şehrin adı nedir?',
  'Annenizin kızlık soyadı nedir?',
  'İlk öğretmeninizin adı neydi?',
  'En sevdiğiniz yemeğin adı nedir?',
  'İlk arabanızın markası neydi?',
  'İlk iş yerinizin adı neydi?',
  'En iyi arkadaşınızın adı nedir?',
  'Sevdiğiniz ilk filmin adı nedir?',
  'İlk gittiğiniz konser kimindi?',
];

/**
 * Güvenlik Sorusu ile Kurtarma Ekranı
 * PIN unutulduğunda güvenlik sorusu ile erişim sağlama
 *
 * @param {Object} props - Bileşen props'ları
 * @param {Object} props.navigation - Navigation nesnesi
 * @param {Object} props.route - Route nesnesi
 * @returns {JSX.Element} Güvenlik kurtarma ekranı
 */
function SecurityRecoveryScreen({ navigation, route }) {
  const insets = useSafeAreaInsets();
  const { theme } = useAppContext();
  const { setIsAuthenticated } = useAuth();
  
  // Route parametreleri
  const { mode = 'recover' } = route.params || {}; // 'setup', 'recover'
  
  const [step, setStep] = useState(mode === 'setup' ? 'setup' : 'recover');
  const [selectedQuestion, setSelectedQuestion] = useState('');
  const [answer, setAnswer] = useState('');
  const [newPin, setNewPin] = useState('');
  const [confirmNewPin, setConfirmNewPin] = useState('');
  const [attempts, setAttempts] = useState(0);
  const [isLocked, setIsLocked] = useState(false);
  const [lockTimeRemaining, setLockTimeRemaining] = useState(0);
  const [showQuestions, setShowQuestions] = useState(false);
  
  const MAX_ATTEMPTS = 3;
  const LOCK_TIME = 60; // saniye
  
  // Kilitleme zamanlayıcısı
  useEffect(() => {
    let interval;
    if (isLocked && lockTimeRemaining > 0) {
      interval = setInterval(() => {
        setLockTimeRemaining(prev => {
          if (prev <= 1) {
            setIsLocked(false);
            setAttempts(0);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isLocked, lockTimeRemaining]);
  
  // Kaydedilmiş güvenlik sorusunu yükle
  useEffect(() => {
    if (mode === 'recover') {
      loadSecurityQuestion();
    }
  }, [mode]);
  
  // Güvenlik sorusunu yükle
  const loadSecurityQuestion = async () => {
    try {
      const savedQuestion = await SecureStore.getItemAsync('securityQuestion');
      if (savedQuestion) {
        setSelectedQuestion(savedQuestion);
      } else {
        Alert.alert(
          'Güvenlik Sorusu Bulunamadı',
          'Henüz bir güvenlik sorusu ayarlanmamış. Lütfen PIN kodunuzu hatırlamaya çalışın.',
          [{ text: 'Tamam', onPress: () => navigation.goBack() }]
        );
      }
    } catch (error) {
      console.error('Güvenlik sorusu yükleme hatası:', error);
      Alert.alert('Hata', 'Güvenlik sorusu yüklenirken bir hata oluştu.');
    }
  };
  
  // Güvenlik sorusu seçme
  const selectSecurityQuestion = (question) => {
    setSelectedQuestion(question);
    setShowQuestions(false);
  };
  
  // Güvenlik sorusu ve cevabı kaydetme
  const saveSecurityQuestion = async () => {
    if (!selectedQuestion.trim()) {
      Alert.alert('Hata', 'Lütfen bir güvenlik sorusu seçin.');
      return;
    }
    
    if (!answer.trim()) {
      Alert.alert('Hata', 'Lütfen güvenlik sorunuzun cevabını girin.');
      return;
    }
    
    if (answer.trim().length < 3) {
      Alert.alert('Hata', 'Cevap en az 3 karakter olmalıdır.');
      return;
    }
    
    try {
      // Cevabı normalize et (küçük harf, Türkçe karakterler)
      const normalizedAnswer = answer.toLowerCase()
        .replace(/ğ/g, 'g')
        .replace(/ü/g, 'u')
        .replace(/ş/g, 's')
        .replace(/ı/g, 'i')
        .replace(/ö/g, 'o')
        .replace(/ç/g, 'c')
        .trim();
      
      await SecureStore.setItemAsync('securityQuestion', selectedQuestion);
      await SecureStore.setItemAsync('securityAnswer', normalizedAnswer);
      
      Alert.alert(
        'Başarılı',
        'Güvenlik sorunuz kaydedildi. PIN kodunuzu unuttuğunuzda bu soru ile erişim sağlayabilirsiniz.',
        [{ text: 'Tamam', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      console.error('Güvenlik sorusu kaydetme hatası:', error);
      Alert.alert('Hata', 'Güvenlik sorusu kaydedilirken bir hata oluştu.');
    }
  };
  
  // Güvenlik sorusu cevabını doğrula
  const verifySecurityAnswer = async () => {
    if (!answer.trim()) {
      Alert.alert('Hata', 'Lütfen cevabınızı girin.');
      return;
    }
    
    try {
      const savedAnswer = await SecureStore.getItemAsync('securityAnswer');
      
      if (!savedAnswer) {
        Alert.alert('Hata', 'Güvenlik sorusu cevabı bulunamadı.');
        return;
      }
      
      // Cevabı normalize et
      const normalizedAnswer = answer.toLowerCase()
        .replace(/ğ/g, 'g')
        .replace(/ü/g, 'u')
        .replace(/ş/g, 's')
        .replace(/ı/g, 'i')
        .replace(/ö/g, 'o')
        .replace(/ç/g, 'c')
        .trim();
      
      if (savedAnswer === normalizedAnswer) {
        setStep('reset-pin');
      } else {
        const newAttempts = attempts + 1;
        setAttempts(newAttempts);
        setAnswer('');
        
        if (newAttempts >= MAX_ATTEMPTS) {
          setIsLocked(true);
          setLockTimeRemaining(LOCK_TIME);
          Alert.alert(
            'Çok Fazla Hatalı Deneme',
            `${LOCK_TIME} saniye boyunca güvenlik sorusu engellenmiştir.`
          );
        } else {
          const remaining = MAX_ATTEMPTS - newAttempts;
          Alert.alert(
            'Hatalı Cevap',
            `Yanlış cevap. Kalan deneme hakkınız: ${remaining}`
          );
        }
      }
    } catch (error) {
      console.error('Güvenlik sorusu doğrulama hatası:', error);
      Alert.alert('Hata', 'Güvenlik sorusu doğrulanırken bir hata oluştu.');
    }
  };
  
  // Yeni PIN kaydetme
  const saveNewPin = async () => {
    if (!newPin.trim()) {
      Alert.alert('Hata', 'Lütfen yeni PIN kodunuzu girin.');
      return;
    }
    
    if (newPin.length !== 6) {
      Alert.alert('Hata', 'PIN kodu 6 haneli olmalıdır.');
      return;
    }
    
    if (newPin !== confirmNewPin) {
      Alert.alert('Hata', 'PIN kodları eşleşmiyor.');
      return;
    }
    
    try {
      await SecureStore.setItemAsync('userPin', newPin);
      
      Alert.alert(
        'Başarılı',
        'Yeni PIN kodunuz kaydedildi. Artık bu PIN ile uygulamaya giriş yapabilirsiniz.',
        [
          {
            text: 'Tamam',
            onPress: () => {
              setIsAuthenticated(true);
              navigation.replace('Home');
            }
          }
        ]
      );
    } catch (error) {
      console.error('PIN kaydetme hatası:', error);
      Alert.alert('Hata', 'PIN kodu kaydedilirken bir hata oluştu.');
    }
  };
  
  // İptal işlemi
  const handleCancel = () => {
    Alert.alert(
      'İptal',
      'Güvenlik kurtarma işlemini iptal etmek istediğinizden emin misiniz?',
      [
        { text: 'Hayır', style: 'cancel' },
        { text: 'Evet', onPress: () => navigation.goBack() }
      ]
    );
  };
  
  // Başlık metni
  const getTitle = () => {
    switch (step) {
      case 'setup':
        return 'Güvenlik Sorusu Ayarla';
      case 'recover':
        return 'Güvenlik Sorusu';
      case 'reset-pin':
        return 'Yeni PIN Belirle';
      default:
        return 'Güvenlik Kurtarma';
    }
  };
  
  // Açıklama metni
  const getDescription = () => {
    switch (step) {
      case 'setup':
        return 'PIN kodunuzu unuttuğunuzda erişim sağlamak için bir güvenlik sorusu ayarlayın';
      case 'recover':
        return 'PIN kodunuzu sıfırlamak için güvenlik sorunuzun cevabını verin';
      case 'reset-pin':
        return 'Yeni PIN kodunuzu belirleyin';
      default:
        return '';
    }
  };
  
  // Güvenlik sorusu seçimi render
  const renderQuestionSelection = () => (
    <View style={styles.questionList}>
      <Text style={[styles.questionListTitle, { color: theme.TEXT_PRIMARY }]}>
        Güvenlik Sorusu Seçin:
      </Text>
      {SECURITY_QUESTIONS.map((question, index) => (
        <TouchableOpacity
          key={index}
          style={[
            styles.questionItem,
            { backgroundColor: theme.SURFACE, borderColor: theme.BORDER }
          ]}
          onPress={() => selectSecurityQuestion(question)}
        >
          <Text style={[styles.questionText, { color: theme.TEXT_PRIMARY }]}>
            {question}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
  
  return (
    <KeyboardAvoidingView
      style={[styles.container, { paddingTop: insets.top, backgroundColor: theme.BACKGROUND }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView style={styles.scrollView} keyboardShouldPersistTaps="handled">
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={handleCancel}
          >
            <MaterialIcons name="close" size={24} color={theme.TEXT_PRIMARY} />
          </TouchableOpacity>
          
          <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
            {getTitle()}
          </Text>
          
          <View style={styles.headerSpacer} />
        </View>
        
        {/* Icon */}
        <View style={[styles.iconContainer, { backgroundColor: theme.WARNING + '20' }]}>
          <MaterialIcons name="help-outline" size={64} color={theme.WARNING} />
        </View>
        
        {/* Description */}
        <Text style={[styles.description, { color: theme.TEXT_SECONDARY }]}>
          {getDescription()}
        </Text>
        
        {/* Lock warning */}
        {isLocked && (
          <View style={[styles.lockWarning, { backgroundColor: theme.DANGER + '20' }]}>
            <MaterialIcons name="warning" size={24} color={theme.DANGER} />
            <Text style={[styles.lockWarningText, { color: theme.DANGER }]}>
              {lockTimeRemaining} saniye boyunca güvenlik sorusu engellenmiştir
            </Text>
          </View>
        )}
        
        {/* Attempts warning */}
        {attempts > 0 && attempts < MAX_ATTEMPTS && !isLocked && (
          <View style={[styles.attemptsWarning, { backgroundColor: theme.WARNING + '20' }]}>
            <Text style={[styles.attemptsWarningText, { color: theme.WARNING }]}>
              Kalan deneme hakkınız: {MAX_ATTEMPTS - attempts}
            </Text>
          </View>
        )}
        
        {/* Step: Setup */}
        {step === 'setup' && (
          <View style={styles.setupContainer}>
            {/* Question Selection */}
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: theme.TEXT_PRIMARY }]}>
                Güvenlik Sorusu
              </Text>
              <TouchableOpacity
                style={[
                  styles.questionSelector,
                  { backgroundColor: theme.SURFACE, borderColor: theme.BORDER }
                ]}
                onPress={() => setShowQuestions(true)}
              >
                <Text style={[
                  styles.questionSelectorText,
                  { color: selectedQuestion ? theme.TEXT_PRIMARY : theme.TEXT_SECONDARY }
                ]}>
                  {selectedQuestion || 'Güvenlik sorusu seçin'}
                </Text>
                <MaterialIcons name="arrow-drop-down" size={24} color={theme.TEXT_SECONDARY} />
              </TouchableOpacity>
            </View>
            
            {/* Answer Input */}
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: theme.TEXT_PRIMARY }]}>
                Cevap
              </Text>
              <TextInput
                style={[
                  styles.input,
                  { backgroundColor: theme.SURFACE, borderColor: theme.BORDER, color: theme.TEXT_PRIMARY }
                ]}
                value={answer}
                onChangeText={setAnswer}
                placeholder="Güvenlik sorunuzun cevabını girin"
                placeholderTextColor={theme.TEXT_SECONDARY}
                autoCapitalize="none"
                autoCorrect={false}
              />
              <Text style={[styles.inputHint, { color: theme.TEXT_SECONDARY }]}>
                Cevabınızı hatırlamanız önemlidir. Büyük/küçük harf duyarlı değildir.
              </Text>
            </View>
            
            {/* Save Button */}
            <TouchableOpacity
              style={[styles.primaryButton, { backgroundColor: theme.PRIMARY }]}
              onPress={saveSecurityQuestion}
            >
              <Text style={[styles.primaryButtonText, { color: theme.WHITE }]}>
                Güvenlik Sorusunu Kaydet
              </Text>
            </TouchableOpacity>
          </View>
        )}
        
        {/* Step: Recover */}
        {step === 'recover' && (
          <View style={styles.recoverContainer}>
            {/* Question Display */}
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: theme.TEXT_PRIMARY }]}>
                Güvenlik Sorusu:
              </Text>
              <View style={[styles.questionDisplay, { backgroundColor: theme.SURFACE }]}>
                <Text style={[styles.questionDisplayText, { color: theme.TEXT_PRIMARY }]}>
                  {selectedQuestion}
                </Text>
              </View>
            </View>
            
            {/* Answer Input */}
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: theme.TEXT_PRIMARY }]}>
                Cevap
              </Text>
              <TextInput
                style={[
                  styles.input,
                  { backgroundColor: theme.SURFACE, borderColor: theme.BORDER, color: theme.TEXT_PRIMARY }
                ]}
                value={answer}
                onChangeText={setAnswer}
                placeholder="Cevabınızı girin"
                placeholderTextColor={theme.TEXT_SECONDARY}
                autoCapitalize="none"
                autoCorrect={false}
                editable={!isLocked}
              />
            </View>
            
            {/* Verify Button */}
            <TouchableOpacity
              style={[
                styles.primaryButton,
                { backgroundColor: theme.PRIMARY },
                isLocked && { opacity: 0.5 }
              ]}
              onPress={verifySecurityAnswer}
              disabled={isLocked}
            >
              <Text style={[styles.primaryButtonText, { color: theme.WHITE }]}>
                Cevabı Doğrula
              </Text>
            </TouchableOpacity>
          </View>
        )}
        
        {/* Step: Reset PIN */}
        {step === 'reset-pin' && (
          <View style={styles.resetContainer}>
            {/* New PIN Input */}
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: theme.TEXT_PRIMARY }]}>
                Yeni PIN Kodu
              </Text>
              <TextInput
                style={[
                  styles.input,
                  { backgroundColor: theme.SURFACE, borderColor: theme.BORDER, color: theme.TEXT_PRIMARY }
                ]}
                value={newPin}
                onChangeText={setNewPin}
                placeholder="6 haneli PIN kodu"
                placeholderTextColor={theme.TEXT_SECONDARY}
                maxLength={6}
                keyboardType="numeric"
                secureTextEntry
              />
            </View>
            
            {/* Confirm PIN Input */}
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: theme.TEXT_PRIMARY }]}>
                PIN Kodunu Tekrar Girin
              </Text>
              <TextInput
                style={[
                  styles.input,
                  { backgroundColor: theme.SURFACE, borderColor: theme.BORDER, color: theme.TEXT_PRIMARY }
                ]}
                value={confirmNewPin}
                onChangeText={setConfirmNewPin}
                placeholder="PIN kodunu tekrar girin"
                placeholderTextColor={theme.TEXT_SECONDARY}
                maxLength={6}
                keyboardType="numeric"
                secureTextEntry
              />
            </View>
            
            {/* Save Button */}
            <TouchableOpacity
              style={[styles.primaryButton, { backgroundColor: theme.PRIMARY }]}
              onPress={saveNewPin}
            >
              <Text style={[styles.primaryButtonText, { color: theme.WHITE }]}>
                Yeni PIN Kodunu Kaydet
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
      
      {/* Question Selection Modal */}
      {showQuestions && (
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: theme.CARD }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: theme.TEXT_PRIMARY }]}>
                Güvenlik Sorusu Seçin
              </Text>
              <TouchableOpacity
                style={styles.modalCloseButton}
                onPress={() => setShowQuestions(false)}
              >
                <MaterialIcons name="close" size={24} color={theme.TEXT_PRIMARY} />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.modalScroll}>
              {renderQuestionSelection()}
            </ScrollView>
          </View>
        </View>
      )}
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 24,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 32,
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  headerSpacer: {
    width: 40,
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 24,
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 24,
  },
  lockWarning: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  lockWarningText: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '600',
  },
  attemptsWarning: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  attemptsWarningText: {
    textAlign: 'center',
    fontSize: 14,
    fontWeight: '600',
  },
  setupContainer: {
    marginBottom: 32,
  },
  recoverContainer: {
    marginBottom: 32,
  },
  resetContainer: {
    marginBottom: 32,
  },
  inputGroup: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
  },
  inputHint: {
    fontSize: 14,
    marginTop: 4,
    fontStyle: 'italic',
  },
  questionSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  questionSelectorText: {
    fontSize: 16,
    flex: 1,
  },
  questionDisplay: {
    padding: 16,
    borderRadius: 8,
  },
  questionDisplayText: {
    fontSize: 16,
    fontWeight: '500',
  },
  primaryButton: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16,
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    borderRadius: 16,
    paddingVertical: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  modalCloseButton: {
    padding: 4,
  },
  modalScroll: {
    paddingHorizontal: 20,
  },
  questionList: {
    marginBottom: 20,
  },
  questionListTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
  },
  questionItem: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 12,
  },
  questionText: {
    fontSize: 16,
  },
});

export default SecurityRecoveryScreen;
