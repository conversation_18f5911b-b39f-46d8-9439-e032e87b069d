import React, { useState } from 'react';
import { View, Text, TouchableOpacity, FlatList, Modal, TextInput, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useTheme } from '../../../../context/ThemeContext';

/**
 * Filtre yönetimi bileşeni - tablo filtrelerini oluşturma ve yönetme
 * @param {Object} props - Bileşen props'ları
 * @param {Array} props.filters - Mevcut filtreler
 * @param {Function} props.onFiltersChange - Filtreler değiştiğinde çağrılacak fonksiyon
 * @param {Array} props.columns - Filtrelenebilir sütunlar
 * @param {Boolean} props.visible - Modal görünürlüğü
 * @param {Function} props.onClose - Modal kapatma fonksiyonu
 */
const FilterManager = ({ 
  filters = [], 
  onFiltersChange, 
  columns = [], 
  visible = false, 
  onClose 
}) => {
  const { theme } = useTheme();
  const [showAddModal, setShowAddModal] = useState(false);
  const [newFilter, setNewFilter] = useState({
    column: '',
    operator: 'contains',
    value: '',
    type: 'text'
  });
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [datePickerMode, setDatePickerMode] = useState('date');

  // Tema güvenlik kontrolü
  if (!theme) {
    return null;
  }

  const styles = {
    container: {
      flex: 1,
      backgroundColor: theme.BACKGROUND || theme.colors?.background || '#fff',
      padding: 16,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 24,
      paddingBottom: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.BORDER || theme.colors?.border || '#e0e0e0',
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.TEXT_PRIMARY || theme.colors?.text || '#333',
    },
    closeButton: {
      padding: 8,
    },
    section: {
      marginBottom: 24,
    },
    sectionTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.TEXT_PRIMARY || theme.colors?.text || '#333',
      marginBottom: 12,
    },
    filterItem: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 12,
      backgroundColor: theme.SURFACE || theme.colors?.surface || '#f9f9f9',
      borderRadius: 8,
      marginBottom: 8,
      borderWidth: 1,
      borderColor: theme.BORDER || theme.colors?.border || '#e0e0e0',
    },
    filterInfo: {
      flex: 1,
      marginRight: 12,
    },
    filterColumn: {
      fontSize: 14,
      fontWeight: '500',
      color: theme.TEXT_PRIMARY || theme.colors?.text || '#333',
    },
    filterRule: {
      fontSize: 12,
      color: theme.TEXT_SECONDARY || theme.colors?.textSecondary || '#666',
      marginTop: 2,
    },
    addButton: {
      backgroundColor: theme.PRIMARY || theme.colors?.primary || '#007AFF',
      padding: 12,
      borderRadius: 8,
      alignItems: 'center',
      marginBottom: 16,
    },
    addButtonText: {
      color: theme.SURFACE || theme.colors?.surface || '#f9f9f9',
      fontSize: 14,
      fontWeight: '600',
    },
    removeButton: {
      backgroundColor: theme.DANGER || theme.colors?.error || '#dc3545',
      padding: 8,
      borderRadius: 6,
      alignItems: 'center',
    },
    removeButtonText: {
      color: theme.SURFACE || theme.colors?.surface || '#f9f9f9',
      fontSize: 12,
      fontWeight: '600',
    },
    modalContainer: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContent: {
      backgroundColor: theme.SURFACE || theme.colors?.surface || '#f9f9f9',
      padding: 24,
      borderRadius: 12,
      width: '90%',
      maxWidth: 400,
    },
    modalTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.TEXT_PRIMARY || theme.colors?.text || '#333',
      marginBottom: 16,
      textAlign: 'center',
    },
    inputGroup: {
      marginBottom: 16,
    },
    inputLabel: {
      fontSize: 14,
      fontWeight: '500',
      color: theme.TEXT_PRIMARY || theme.colors?.text || '#333',
      marginBottom: 8,
    },
    input: {
      borderWidth: 1,
      borderColor: theme.BORDER || theme.colors?.border || '#e0e0e0',
      borderRadius: 8,
      padding: 12,
      fontSize: 16,
      color: theme.TEXT_PRIMARY || theme.colors?.text || '#333',
    },
    picker: {
      borderWidth: 1,
      borderColor: theme.BORDER || theme.colors?.border || '#e0e0e0',
      borderRadius: 8,
      backgroundColor: theme.SURFACE || theme.colors?.surface || '#f9f9f9',
      padding: 12,
    },
    pickerText: {
      color: theme.TEXT_PRIMARY || theme.colors?.text || '#333',
      fontSize: 16,
    },
    modalActions: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: 16,
    },
    modalButton: {
      flex: 1,
      padding: 12,
      borderRadius: 8,
      alignItems: 'center',
      marginHorizontal: 4,
    },
    modalButtonCancel: {
      backgroundColor: theme.SURFACE_VARIANT || theme.colors?.surface || '#f9f9f9',
    },
    modalButtonConfirm: {
      backgroundColor: theme.PRIMARY || theme.colors?.primary || '#007AFF',
    },
    modalButtonText: {
      fontSize: 14,
      fontWeight: '600',
    },
    modalButtonTextCancel: {
      color: theme.TEXT_SECONDARY || theme.colors?.textSecondary || '#666',
    },
    modalButtonTextConfirm: {
      color: theme.SURFACE || theme.colors?.surface || '#f9f9f9',
    },
    operatorButton: {
      paddingHorizontal: 12,
      paddingVertical: 8,
      borderRadius: 6,
      borderWidth: 1,
      borderColor: theme.BORDER || theme.colors?.border || '#e0e0e0',
      marginRight: 8,
    },
    operatorButtonActive: {
      backgroundColor: theme.PRIMARY || theme.colors?.primary || '#007AFF',
      borderColor: theme.PRIMARY || theme.colors?.primary || '#007AFF',
    },
    operatorButtonText: {
      fontSize: 12,
      color: theme.TEXT_PRIMARY || theme.colors?.text || '#333',
    },
    operatorButtonTextActive: {
      color: theme.SURFACE || theme.colors?.surface || '#f9f9f9',
    },
  };

  // Filtre operatörleri
  const getOperators = (type) => {
    const commonOperators = [
      { id: 'equals', label: 'Eşittir', symbol: '=' },
      { id: 'not_equals', label: 'Eşit Değil', symbol: '≠' },
    ];

    const textOperators = [
      { id: 'contains', label: 'İçerir', symbol: '⊃' },
      { id: 'not_contains', label: 'İçermez', symbol: '⊅' },
      { id: 'starts_with', label: 'İle Başlar', symbol: '⏵' },
      { id: 'ends_with', label: 'İle Biter', symbol: '⏴' },
    ];

    const numberOperators = [
      { id: 'greater_than', label: 'Büyüktür', symbol: '>' },
      { id: 'less_than', label: 'Küçüktür', symbol: '<' },
      { id: 'greater_equal', label: 'Büyük Eşittir', symbol: '≥' },
      { id: 'less_equal', label: 'Küçük Eşittir', symbol: '≤' },
    ];

    switch (type) {
      case 'number':
      case 'currency':
        return [...commonOperators, ...numberOperators];
      case 'text':
        return [...commonOperators, ...textOperators];
      case 'date':
        return [...commonOperators, ...numberOperators];
      default:
        return commonOperators;
    }
  };

  /**
   * Filtre silme işlemi
   */
  const handleRemoveFilter = (filterId) => {
    Alert.alert(
      'Filtreyi Sil',
      'Bu filtreyi silmek istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        { 
          text: 'Sil', 
          style: 'destructive',
          onPress: () => {
            if (onFiltersChange) {
              const updatedFilters = filters.filter(filter => filter.id !== filterId);
              onFiltersChange(updatedFilters);
            }
          }
        }
      ]
    );
  };

  /**
   * Yeni filtre ekleme
   */
  const handleAddFilter = () => {
    if (!newFilter.column || !newFilter.value) {
      Alert.alert('Hata', 'Sütun ve değer alanları zorunludur.');
      return;
    }

    const filter = {
      id: Date.now().toString(),
      column: newFilter.column,
      operator: newFilter.operator,
      value: newFilter.value,
      type: newFilter.type,
      label: `${newFilter.column} ${getOperators(newFilter.type).find(op => op.id === newFilter.operator)?.label} ${newFilter.value}`
    };

    if (onFiltersChange) {
      onFiltersChange([...filters, filter]);
    }

    setNewFilter({
      column: '',
      operator: 'contains',
      value: '',
      type: 'text'
    });
    setShowAddModal(false);
  };

  /**
   * Filtre öğesi render
   */
  const renderFilterItem = ({ item }) => (
    <View style={styles.filterItem}>
      <View style={styles.filterInfo}>
        <Text style={styles.filterColumn}>{item.column}</Text>
        <Text style={styles.filterRule}>{item.label}</Text>
      </View>
      <TouchableOpacity
        style={styles.removeButton}
        onPress={() => handleRemoveFilter(item.id)}
      >
        <Ionicons name="close" size={20} color={theme.SURFACE || theme.colors?.surface || '#f9f9f9'} />
      </TouchableOpacity>
    </View>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Filtre Yönetimi</Text>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Ionicons name="close" size={24} color={theme.TEXT_PRIMARY || theme.colors?.text || '#333'} />
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Mevcut Filtreler</Text>
          <FlatList
            data={filters}
            renderItem={renderFilterItem}
            keyExtractor={item => item.id}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={
              <Text style={[styles.filterRule, { textAlign: 'center', padding: 20 }]}>
                Henüz filtre eklenmemiş
              </Text>
            }
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Yeni Filtre Ekle</Text>
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => setShowAddModal(true)}
          >
            <Text style={styles.addButtonText}>+ Filtre Ekle</Text>
          </TouchableOpacity>
        </View>

        {/* Filtre Ekleme Modal */}
        <Modal
          visible={showAddModal}
          transparent={true}
          animationType="fade"
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <Text style={styles.modalTitle}>Yeni Filtre</Text>
              
              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Sütun</Text>
                <TouchableOpacity style={styles.picker}>
                  <Text style={styles.pickerText}>
                    {newFilter.column || 'Sütun seçin'}
                  </Text>
                </TouchableOpacity>
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Operatör</Text>
                <TouchableOpacity style={styles.picker}>
                  <Text style={styles.pickerText}>
                    {getOperators(newFilter.type).find(op => op.id === newFilter.operator)?.label || 'Operatör seçin'}
                  </Text>
                </TouchableOpacity>
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Değer</Text>
                <TextInput
                  style={styles.input}
                  value={newFilter.value}
                  onChangeText={(text) => setNewFilter(prev => ({ ...prev, value: text }))}
                  placeholder="Filtre değerini girin"
                  placeholderTextColor={theme.TEXT_SECONDARY || theme.colors?.textSecondary || '#666'}
                />
              </View>

              <View style={styles.modalActions}>
                <TouchableOpacity
                  style={[styles.modalButton, styles.modalButtonCancel]}
                  onPress={() => setShowAddModal(false)}
                >
                  <Text style={[styles.modalButtonText, styles.modalButtonTextCancel]}>
                    İptal
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.modalButton, styles.modalButtonConfirm]}
                  onPress={handleAddFilter}
                >
                  <Text style={[styles.modalButtonText, styles.modalButtonTextConfirm]}>
                    Ekle
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>

        {/* Date Picker */}
        {showDatePicker && (
          <DateTimePicker
            value={new Date()}
            mode={datePickerMode}
            display="default"
            onChange={(event, selectedDate) => {
              setShowDatePicker(false);
              if (selectedDate) {
                setNewFilter(prev => ({ 
                  ...prev, 
                  value: selectedDate.toISOString().split('T')[0] 
                }));
              }
            }}
          />
        )}
      </View>
    </Modal>
  );
};

export default FilterManager;
