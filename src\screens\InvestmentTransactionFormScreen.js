import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useSQLiteContext } from 'expo-sqlite';
import { Colors } from '../constants/colors';
import DateTimePicker from '@react-native-community/datetimepicker';

/**
 * Yatırım işlemi ekleme/düzenleme ekranı
 * @param {Object} props Component props
 * @param {Object} props.navigation Navigation objesi
 * @param {Object} props.route Route objesi
 */
const InvestmentTransactionFormScreen = ({ navigation, route }) => {
  const { transaction, asset: initialAsset } = route.params || {};
  const db = useSQLiteContext();
  
  const [assets, setAssets] = useState([]);
  const [selectedAssetId, setSelectedAssetId] = useState(transaction?.asset_id || initialAsset?.id || null);
  const [type, setType] = useState(transaction?.type || 'buy');
  const [quantity, setQuantity] = useState(transaction?.quantity?.toString() || '');
  const [price, setPrice] = useState(transaction?.price?.toString() || '');
  const [date, setDate] = useState(transaction?.date ? new Date(transaction.date) : new Date());
  const [notes, setNotes] = useState(transaction?.notes || '');
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [loadingAssets, setLoadingAssets] = useState(true);

  // Varlıkları yükle
  useEffect(() => {
    const loadAssets = async () => {
      try {
        setLoadingAssets(true);
        
        const result = await db.getAllAsync(`
          SELECT * FROM investment_assets
          ORDER BY name
        `);
        
        setAssets(result);
        
        if (!selectedAssetId && result.length > 0) {
          setSelectedAssetId(result[0].id);
        }
      } catch (error) {
        console.error('Varlık yükleme hatası:', error);
        Alert.alert('Hata', 'Varlıklar yüklenirken bir hata oluştu.');
      } finally {
        setLoadingAssets(false);
      }
    };
    
    loadAssets();
  }, []);

  // İşlemi kaydet
  const saveTransaction = async () => {
    try {
      // Validasyon
      if (!selectedAssetId) {
        Alert.alert('Hata', 'Lütfen bir varlık seçin.');
        return;
      }
      
      if (!quantity || isNaN(parseFloat(quantity)) || parseFloat(quantity) <= 0) {
        Alert.alert('Hata', 'Lütfen geçerli bir miktar girin.');
        return;
      }
      
      if (!price || isNaN(parseFloat(price)) || parseFloat(price) <= 0) {
        Alert.alert('Hata', 'Lütfen geçerli bir fiyat girin.');
        return;
      }
      
      setIsLoading(true);
      
      const parsedQuantity = parseFloat(quantity);
      const parsedPrice = parseFloat(price);
      const formattedDate = date.toISOString().split('T')[0];
      
      if (transaction?.id) {
        // Mevcut işlemi güncelle
        await db.runAsync(`
          UPDATE investment_transactions 
          SET asset_id = ?, type = ?, quantity = ?, price = ?, date = ?, notes = ?
          WHERE id = ?
        `, [
          selectedAssetId, 
          type, 
          parsedQuantity, 
          parsedPrice, 
          formattedDate, 
          notes,
          transaction.id
        ]);
      } else {
        // Yeni işlem ekle
        await db.runAsync(`
          INSERT INTO investment_transactions 
          (asset_id, type, quantity, price, date, notes)
          VALUES (?, ?, ?, ?, ?, ?)
        `, [
          selectedAssetId, 
          type, 
          parsedQuantity, 
          parsedPrice, 
          formattedDate, 
          notes
        ]);
      }
      
      navigation.goBack();
    } catch (error) {
      console.error('İşlem kaydetme hatası:', error);
      Alert.alert('Hata', 'İşlem kaydedilirken bir hata oluştu.');
    } finally {
      setIsLoading(false);
    }
  };

  // Tarih değiştiğinde
  const handleDateChange = (_, selectedDate) => {
    setShowDatePicker(false);

    if (selectedDate) {
      setDate(selectedDate);
    }
  };

  // Tarih formatla
  const formatDate = (date) => {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}.${month}.${year}`;
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <MaterialIcons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.title}>
          {transaction?.id ? 'İşlem Düzenle' : 'Yeni İşlem Ekle'}
        </Text>
        <View style={{ width: 24 }} />
      </View>

      <KeyboardAvoidingView
        style={styles.container}
        behavior={'padding'}
        keyboardVerticalOffset={64}
      >
        <ScrollView style={styles.content}>
          {loadingAssets ? (
            <ActivityIndicator size="large" color={Colors.PRIMARY} style={styles.loader} />
          ) : (
            <>
              <View style={styles.formGroup}>
                <Text style={styles.label}>Varlık</Text>
                <ScrollView
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  style={styles.assetSelector}
                >
                  {assets.map((asset) => (
                    <TouchableOpacity
                      key={asset.id}
                      style={[
                        styles.assetItem,
                        selectedAssetId === asset.id && styles.selectedAssetItem
                      ]}
                      onPress={() => setSelectedAssetId(asset.id)}
                    >
                      <Text
                        style={[
                          styles.assetSymbol,
                          selectedAssetId === asset.id && styles.selectedAssetText
                        ]}
                      >
                        {asset.symbol}
                      </Text>
                      <Text
                        style={[
                          styles.assetName,
                          selectedAssetId === asset.id && styles.selectedAssetText
                        ]}
                      >
                        {asset.name}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>
              
              <View style={styles.formGroup}>
                <Text style={styles.label}>İşlem Tipi</Text>
                <View style={styles.typeSelector}>
                  <TouchableOpacity
                    style={[
                      styles.typeButton,
                      type === 'buy' && styles.activeTypeButton
                    ]}
                    onPress={() => setType('buy')}
                  >
                    <MaterialIcons
                      name="arrow-downward"
                      size={20}
                      color={type === 'buy' ? '#fff' : Colors.PRIMARY}
                    />
                    <Text
                      style={[
                        styles.typeButtonText,
                        type === 'buy' && styles.activeTypeButtonText
                      ]}
                    >
                      Alım
                    </Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={[
                      styles.typeButton,
                      type === 'sell' && styles.activeTypeButton
                    ]}
                    onPress={() => setType('sell')}
                  >
                    <MaterialIcons
                      name="arrow-upward"
                      size={20}
                      color={type === 'sell' ? '#fff' : Colors.PRIMARY}
                    />
                    <Text
                      style={[
                        styles.typeButtonText,
                        type === 'sell' && styles.activeTypeButtonText
                      ]}
                    >
                      Satım
                    </Text>
                  </TouchableOpacity>
                </View>
                
                <View style={styles.typeSelector}>
                  <TouchableOpacity
                    style={[
                      styles.typeButton,
                      type === 'dividend' && styles.activeTypeButton
                    ]}
                    onPress={() => setType('dividend')}
                  >
                    <MaterialIcons
                      name="attach-money"
                      size={20}
                      color={type === 'dividend' ? '#fff' : Colors.PRIMARY}
                    />
                    <Text
                      style={[
                        styles.typeButtonText,
                        type === 'dividend' && styles.activeTypeButtonText
                      ]}
                    >
                      Temettü
                    </Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={[
                      styles.typeButton,
                      type === 'interest' && styles.activeTypeButton
                    ]}
                    onPress={() => setType('interest')}
                  >
                    <MaterialIcons
                      name="trending-up"
                      size={20}
                      color={type === 'interest' ? '#fff' : Colors.PRIMARY}
                    />
                    <Text
                      style={[
                        styles.typeButtonText,
                        type === 'interest' && styles.activeTypeButtonText
                      ]}
                    >
                      Faiz
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
              
              <View style={styles.formGroup}>
                <Text style={styles.label}>Miktar</Text>
                <TextInput
                  style={styles.input}
                  value={quantity}
                  onChangeText={setQuantity}
                  keyboardType="numeric"
                  placeholder="0.00"
                />
              </View>
              
              <View style={styles.formGroup}>
                <Text style={styles.label}>Fiyat</Text>
                <View style={styles.inputWithIcon}>
                  <Text style={styles.inputIcon}>₺</Text>
                  <TextInput
                    style={styles.inputWithIconField}
                    value={price}
                    onChangeText={setPrice}
                    keyboardType="numeric"
                    placeholder="0.00"
                  />
                </View>
              </View>
              
              <View style={styles.formGroup}>
                <Text style={styles.label}>Tarih</Text>
                <TouchableOpacity
                  style={styles.dateSelector}
                  onPress={() => setShowDatePicker(true)}
                >
                  <Text style={styles.dateText}>{formatDate(date)}</Text>
                  <MaterialIcons name="calendar-today" size={20} color={Colors.PRIMARY} />
                </TouchableOpacity>
                
                {showDatePicker && (
                  <DateTimePicker
                    value={date}
                    mode="date"
                    display="default"
                    onChange={handleDateChange}
                  />
                )}
              </View>
              
              <View style={styles.formGroup}>
                <Text style={styles.label}>Notlar</Text>
                <TextInput
                  style={[styles.input, styles.textArea]}
                  value={notes}
                  onChangeText={setNotes}
                  placeholder="İşlem hakkında notlar..."
                  multiline
                  numberOfLines={4}
                  textAlignVertical="top"
                />
              </View>
            </>
          )}
        </ScrollView>
        
        <View style={styles.footer}>
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.cancelButtonText}>İptal</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.saveButton}
            onPress={saveTransaction}
            disabled={isLoading || loadingAssets}
          >
            {isLoading ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Text style={styles.saveButtonText}>Kaydet</Text>
            )}
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.PRIMARY,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loader: {
    marginTop: 32,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  assetSelector: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  assetItem: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    minWidth: 100,
  },
  selectedAssetItem: {
    backgroundColor: Colors.PRIMARY,
    borderColor: Colors.PRIMARY,
  },
  assetSymbol: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  assetName: {
    fontSize: 14,
    color: '#666',
  },
  selectedAssetText: {
    color: '#fff',
  },
  typeSelector: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  typeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    marginRight: 8,
    backgroundColor: '#fff',
  },
  activeTypeButton: {
    backgroundColor: Colors.PRIMARY,
    borderColor: Colors.PRIMARY,
  },
  typeButtonText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#333',
  },
  activeTypeButtonText: {
    color: '#fff',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  textArea: {
    minHeight: 100,
  },
  inputWithIcon: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  inputIcon: {
    paddingHorizontal: 12,
    fontSize: 16,
    color: '#666',
  },
  inputWithIconField: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
  },
  dateSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    backgroundColor: '#fff',
  },
  dateText: {
    fontSize: 16,
    color: '#333',
  },
  footer: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
    backgroundColor: '#fff',
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    marginRight: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#333',
  },
  saveButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
    borderRadius: 8,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
  },
});

export default InvestmentTransactionFormScreen;
