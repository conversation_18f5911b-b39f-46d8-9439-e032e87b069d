/**
 * Gelişmiş Export Servisi
 * PDF ve Excel export için gerçek fonksiyonalite
 */

import * as FileSystem from 'expo-file-system';
// import * as Sharing from 'expo-sharing';
// import * as Print from 'expo-print';

/**
 * PDF Export Servisi
 */
export class PDFExportService {
  /**
   * PDF oluştur ve paylaş
   */
  static async createAndSharePDF(data) {
    try {
      const htmlContent = this.generateHTMLContent(data);
      
      const { uri } = await Print.printToFileAsync({
        html: htmlContent,
        base64: false,
        width: 612,
        height: 792,
        margins: {
          left: 20,
          top: 20,
          right: 20,
          bottom: 20,
        },
      });
      
      // Paylaşım için hazırla
      const fileName = `${data.title.replace(/[^a-zA-Z0-9]/g, '_')}_${Date.now()}.pdf`;
      const newUri = FileSystem.documentDirectory + fileName;
      
      await FileSystem.moveAsync({
        from: uri,
        to: newUri,
      });
      
      // Paylaş
      await Sharing.shareAsync(newUri, {
        mimeType: 'application/pdf',
        dialogTitle: 'PDF Raporu Paylaş',
      });
      
      return true;
    } catch (error) {
      console.error('PDF oluşturma hatası:', error);
      return false;
    }
  }

  /**
   * HTML içeriği oluştur
   */
  static generateHTMLContent(data) {
    const { title, subtitle, summary, monthlyData, categoryBreakdown, transactions } = data;
    
    let html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>${title}</title>
        <style>
          body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
            line-height: 1.6;
          }
          .header { 
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #007AFF;
            padding-bottom: 20px;
          }
          .title { 
            font-size: 28px;
            font-weight: bold;
            color: #007AFF;
            margin: 0 0 10px 0;
          }
          .subtitle { 
            font-size: 16px;
            color: #666;
            margin: 0;
          }
          .summary { 
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
          }
          .summary-title { 
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
          }
          .summary-grid { 
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
          }
          .summary-item { 
            background: white;
            padding: 15px;
            border-radius: 6px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }
          .summary-label { 
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
            text-transform: uppercase;
          }
          .summary-value { 
            font-size: 18px;
            font-weight: bold;
            color: #333;
          }
          .income { color: #10B981; }
          .expense { color: #EF4444; }
          .net { color: #3B82F6; }
          .table { 
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }
          .table th, .table td { 
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
          }
          .table th { 
            background: #f8f9fa;
            font-weight: 600;
            color: #374151;
          }
          .table tr:hover { background: #f8f9fa; }
          .section { 
            margin: 30px 0;
            page-break-inside: avoid;
          }
          .section-title { 
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e5e7eb;
          }
          .footer { 
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            color: #666;
            font-size: 12px;
          }
          @media print {
            body { margin: 0; }
            .summary { break-inside: avoid; }
            .section { break-inside: avoid; }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1 class="title">${title}</h1>
          <p class="subtitle">${subtitle}</p>
        </div>
    `;
    
    // Özet bilgiler
    if (summary) {
      html += `
        <div class="summary">
          <div class="summary-title">Özet Bilgiler</div>
          <div class="summary-grid">
            ${summary.totalIncome !== undefined ? `
              <div class="summary-item">
                <div class="summary-label">Toplam Gelir</div>
                <div class="summary-value income">${this.formatCurrency(summary.totalIncome)}</div>
              </div>
            ` : ''}
            ${summary.totalExpense !== undefined ? `
              <div class="summary-item">
                <div class="summary-label">Toplam Gider</div>
                <div class="summary-value expense">${this.formatCurrency(summary.totalExpense)}</div>
              </div>
            ` : ''}
            ${summary.netAmount !== undefined ? `
              <div class="summary-item">
                <div class="summary-label">Net Tutar</div>
                <div class="summary-value net">${this.formatCurrency(summary.netAmount)}</div>
              </div>
            ` : ''}
            ${summary.transactionCount !== undefined ? `
              <div class="summary-item">
                <div class="summary-label">İşlem Sayısı</div>
                <div class="summary-value">${summary.transactionCount}</div>
              </div>
            ` : ''}
          </div>
        </div>
      `;
    }
    
    // Aylık veriler
    if (monthlyData && monthlyData.length > 0) {
      html += `
        <div class="section">
          <div class="section-title">Aylık Veriler</div>
          <table class="table">
            <thead>
              <tr>
                <th>Ay</th>
                <th>Gelir</th>
                <th>Gider</th>
                <th>Net</th>
              </tr>
            </thead>
            <tbody>
              ${monthlyData.map(item => `
                <tr>
                  <td>${item.month}</td>
                  <td class="income">${this.formatCurrency(item.income)}</td>
                  <td class="expense">${this.formatCurrency(item.expense)}</td>
                  <td class="net">${this.formatCurrency(item.net)}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
      `;
    }
    
    // Kategori dağılımı
    if (categoryBreakdown && categoryBreakdown.length > 0) {
      html += `
        <div class="section">
          <div class="section-title">Kategori Dağılımı</div>
          <table class="table">
            <thead>
              <tr>
                <th>Kategori</th>
                <th>Tutar</th>
                <th>İşlem Sayısı</th>
                <th>Yüzde</th>
              </tr>
            </thead>
            <tbody>
              ${categoryBreakdown.map(item => `
                <tr>
                  <td>${item.category}</td>
                  <td class="${item.amount > 0 ? 'income' : 'expense'}">${this.formatCurrency(item.amount)}</td>
                  <td>${item.count || '-'}</td>
                  <td>${item.percentage ? item.percentage.toFixed(1) + '%' : '-'}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
      `;
    }
    
    // İşlem listesi
    if (transactions && transactions.length > 0) {
      html += `
        <div class="section">
          <div class="section-title">İşlem Listesi</div>
          <table class="table">
            <thead>
              <tr>
                <th>Tarih</th>
                <th>Açıklama</th>
                <th>Kategori</th>
                <th>Tutar</th>
              </tr>
            </thead>
            <tbody>
              ${transactions.map(item => `
                <tr>
                  <td>${item.date}</td>
                  <td>${item.description}</td>
                  <td>${item.category}</td>
                  <td class="${item.amount > 0 ? 'income' : 'expense'}">${this.formatCurrency(item.amount)}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
      `;
    }
    
    html += `
        <div class="footer">
          <p>Rapor oluşturulma tarihi: ${new Date().toLocaleDateString('tr-TR')} ${new Date().toLocaleTimeString('tr-TR')}</p>
        </div>
      </body>
      </html>
    `;
    
    return html;
  }

  /**
   * Para birimi formatla
   */
  static formatCurrency(amount) {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 2
    }).format(amount);
  }
}

/**
 * Excel Export Servisi
 */
export class ExcelExportService {
  /**
   * CSV formatında Excel dosyası oluştur ve paylaş
   */
  static async createAndShareExcel(data) {
    try {
      const csvContent = this.generateCSVContent(data);
      
      const fileName = `${data.title.replace(/[^a-zA-Z0-9]/g, '_')}_${Date.now()}.csv`;
      const fileUri = FileSystem.documentDirectory + fileName;
      
      await FileSystem.writeAsStringAsync(fileUri, csvContent, {
        encoding: FileSystem.EncodingType.UTF8,
      });
      
      // Paylaş
      await Sharing.shareAsync(fileUri, {
        mimeType: 'text/csv',
        dialogTitle: 'Excel Raporu Paylaş',
      });
      
      return true;
    } catch (error) {
      console.error('Excel oluşturma hatası:', error);
      return false;
    }
  }

  /**
   * CSV içeriği oluştur
   */
  static generateCSVContent(data) {
    const { title, sheets } = data;
    
    let csvContent = `${title}\n`;
    csvContent += `Oluşturulma Tarihi: ${new Date().toLocaleDateString('tr-TR')}\n\n`;
    
    if (sheets && sheets.length > 0) {
      sheets.forEach((sheet, index) => {
        if (index > 0) csvContent += '\n';
        
        csvContent += `${sheet.name}\n`;
        
        if (sheet.data && sheet.data.length > 0) {
          // Header
          const headers = Object.keys(sheet.data[0]);
          csvContent += headers.join(',') + '\n';
          
          // Data rows
          sheet.data.forEach(row => {
            const values = headers.map(header => {
              const value = row[header];
              // CSV için özel karakterleri escape et
              if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
                return `"${value.replace(/"/g, '""')}"`;
              }
              return value;
            });
            csvContent += values.join(',') + '\n';
          });
        }
        
        csvContent += '\n';
      });
    }
    
    return csvContent;
  }
}

/**
 * Rapor Export Servisi
 */
export class ReportExportService {
  /**
   * PDF olarak dışa aktar
   */
  static async exportToPDF(data) {
    return await PDFExportService.createAndSharePDF(data);
  }

  /**
   * Excel olarak dışa aktar
   */
  static async exportToExcel(data) {
    return await ExcelExportService.createAndShareExcel(data);
  }
}
