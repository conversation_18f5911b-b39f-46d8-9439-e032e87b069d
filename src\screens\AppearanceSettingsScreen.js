import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  useWindowDimensions,
  Alert
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTheme } from '../context/ThemeContext';

/**
 * Tema ayarları ekranı
 * @param {Object} props - Component props
 * @param {Object} props.navigation - Navigation objesi
 * @returns {JSX.Element} AppearanceSettingsScreen bileşeni
 */
export default function AppearanceSettingsScreen({ navigation }) {
  const { width } = useWindowDimensions();
  const insets = useSafeAreaInsets();
  const { theme, isDarkMode, themeType, isSystemTheme, changeTheme, toggleUseSystemTheme } = useTheme();

  /**
   * Tema değiştirme işlevi
   * @param {string} newTheme - 'dark' veya 'light'
   */
  const handleChangeTheme = (newTheme) => {
    if (newTheme === themeType && !isSystemTheme) return;

    changeTheme(newTheme);
    Alert.alert(
      'Tema Değiştirildi',
      `Tema ${newTheme === 'dark' ? 'karanlık' : 'açık'} moda ayarlandı.`,
      [{ text: 'Tamam' }]
    );
  };

  /**
   * Sistem tema ayarını değiştir
   * @param {boolean} value - Sistem teması kullanılsın mı
   */
  const handleToggleSystemTheme = (value) => {
    toggleUseSystemTheme(value);
    Alert.alert(
      'Sistem Teması',
      value ? 'Sistem teması ayarı etkinleştirildi.' : 'Sistem teması ayarı kapatıldı.',
      [{ text: 'Tamam' }]
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.PRIMARY, paddingTop: insets.top + 10 }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
          activeOpacity={0.7}
        >
          <MaterialIcons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Tema Ayarları</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Sistem Teması Kullanımı */}
        <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <MaterialIcons name="settings" size={24} color={theme.PRIMARY} />
              <View style={styles.settingTextContainer}>
                <Text style={[styles.settingTitle, { color: theme.TEXT_PRIMARY }]}>
                  Sistem Temasını Kullan
                </Text>
                <Text style={[styles.settingDescription, { color: theme.TEXT_SECONDARY }]}>
                  Cihaz ayarlarınıza göre otomatik olarak açık veya karanlık tema kullanılır
                </Text>
              </View>
            </View>
            <Switch
              value={isSystemTheme}
              onValueChange={handleToggleSystemTheme}
              trackColor={{
                false: theme.BORDER,
                true: theme.PRIMARY + '40'
              }}
              thumbColor={isSystemTheme ? theme.PRIMARY : theme.TEXT_SECONDARY}
            />
          </View>
        </View>

        {/* Manuel Tema Seçimi */}
        {!isSystemTheme && (
          <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
            <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
              Tema Seçin
            </Text>

            <View style={styles.themeOptions}>
              {/* Açık Tema */}
              <TouchableOpacity
                style={[
                  styles.themeOption,
                  themeType === 'light' && [styles.selectedThemeOption, { borderColor: theme.PRIMARY }],
                  {
                    borderColor: themeType === 'light' ? theme.PRIMARY : theme.BORDER,
                    backgroundColor: theme.BACKGROUND,
                    width: (width - 60) / 2
                  }
                ]}
                onPress={() => handleChangeTheme('light')}
              >
                <View style={[styles.themePreview, styles.lightThemePreview]}>
                  <View style={[styles.themePreviewHeader, { backgroundColor: '#6c5ce7' }]} />
                  <View style={styles.themePreviewContent}>
                    <View style={[styles.themePreviewLine, { backgroundColor: '#333' }]} />
                    <View style={[styles.themePreviewLine, { backgroundColor: '#666' }]} />
                  </View>
                </View>
                <Text style={[styles.themeOptionText, { color: theme.TEXT_PRIMARY }]}>
                  Açık Tema
                </Text>
                {themeType === 'light' && (
                  <MaterialIcons name="check-circle" size={20} color={theme.PRIMARY} />
                )}
              </TouchableOpacity>

              {/* Karanlık Tema */}
              <TouchableOpacity
                style={[
                  styles.themeOption,
                  themeType === 'dark' && [styles.selectedThemeOption, { borderColor: theme.PRIMARY }],
                  {
                    borderColor: themeType === 'dark' ? theme.PRIMARY : theme.BORDER,
                    backgroundColor: theme.BACKGROUND,
                    width: (width - 60) / 2
                  }
                ]}
                onPress={() => handleChangeTheme('dark')}
              >
                <View style={[styles.themePreview, styles.darkThemePreview]}>
                  <View style={[styles.themePreviewHeader, { backgroundColor: '#6c5ce7' }]} />
                  <View style={[styles.themePreviewContent, { backgroundColor: '#2a2a2a' }]}>
                    <View style={[styles.themePreviewLine, { backgroundColor: '#ccc' }]} />
                    <View style={[styles.themePreviewLine, { backgroundColor: '#999' }]} />
                  </View>
                </View>
                <Text style={[styles.themeOptionText, { color: theme.TEXT_PRIMARY }]}>
                  Karanlık Tema
                </Text>
                {themeType === 'dark' && (
                  <MaterialIcons name="check-circle" size={20} color={theme.PRIMARY} />
                )}
              </TouchableOpacity>
            </View>

            <View style={styles.noteContainer}>
              <MaterialIcons name="info" size={18} color={theme.PRIMARY} />
              <Text style={[styles.noteText, { color: theme.TEXT_SECONDARY }]}>
                Karanlık tema, pil tasarrufu sağlar ve göz yorgunluğunu azaltabilir.
              </Text>
            </View>
          </View>
        )}

        {/* Tema Bilgisi */}
        <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            Tema Bilgileri
          </Text>
          <View style={styles.infoRow}>
            <Text style={[styles.infoLabel, { color: theme.TEXT_SECONDARY }]}>Mevcut Tema:</Text>
            <Text style={[styles.infoValue, { color: theme.TEXT_PRIMARY }]}>
              {isSystemTheme ? 'Sistem' : (themeType === 'dark' ? 'Karanlık' : 'Açık')}
            </Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={[styles.infoLabel, { color: theme.TEXT_SECONDARY }]}>Durum:</Text>
            <Text style={[styles.infoValue, { color: theme.TEXT_PRIMARY }]}>
              {isSystemTheme ? 'Otomatik' : 'Manuel'}
            </Text>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#fff',
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 20,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  section: {
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingTextContainer: {
    flex: 1,
    marginLeft: 12,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  themeOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  themeOption: {
    borderRadius: 12,
    borderWidth: 2,
    padding: 16,
    alignItems: 'center',
  },
  selectedThemeOption: {
    borderWidth: 2,
  },
  themePreview: {
    width: 60,
    height: 80,
    borderRadius: 8,
    marginBottom: 12,
    overflow: 'hidden',
  },
  lightThemePreview: {
    backgroundColor: '#fff',
  },
  darkThemePreview: {
    backgroundColor: '#1a1a1a',
  },
  themePreviewHeader: {
    height: 20,
    borderRadius: 4,
    margin: 4,
  },
  themePreviewContent: {
    flex: 1,
    padding: 4,
  },
  themePreviewLine: {
    height: 4,
    borderRadius: 2,
    marginVertical: 2,
  },
  themeOptionText: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  noteContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    backgroundColor: 'rgba(108, 92, 231, 0.1)',
  },
  noteText: {
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
    lineHeight: 20,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '600',
  },
});
