import React, { createContext, useState, useContext, useEffect } from 'react';
import { useSQLiteContext } from 'expo-sqlite';
import * as exchangeRateService from '../services/simpleExchangeRateService';

// Döviz kuru context'i oluştur
const ExchangeRateContext = createContext();

/**
 * Döviz Kuru Provider Bileşeni
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {React.ReactNode} props.children - Alt bileşenler
 * @returns {JSX.Element} ExchangeRateProvider bileşeni
 */
export const ExchangeRateProvider = ({ children }) => {
  const db = useSQLiteContext();
  const [rates, setRates] = useState({});
  const [baseCurrency, setBaseCurrency] = useState('TRY');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdate, setLastUpdate] = useState(null);
  
  // Döviz kurlarını yükle
  const loadExchangeRates = async (currency = baseCurrency) => {
    try {
      setLoading(true);
      setError(null);

      const today = new Date().toISOString().split('T')[0];

      // Önce API'den güncel kurları çekmeye çalış
      try {
        const apiData = await exchangeRateService.fetchExchangeRates(currency);
        if (apiData && apiData.rates) {
          // API'den gelen veriyi veritabanına kaydet
          await exchangeRateService.saveExchangeRates(db, apiData);
          setRates(apiData.rates);
          setBaseCurrency(currency);
          setLastUpdate(new Date());
          setLoading(false);
          return;
        }
      } catch (apiError) {
        console.warn('API\'den döviz kuru çekme hatası, veritabanından deneniyor:', apiError);
      }

      // API başarısız olursa veritabanından al
      const exchangeRates = await exchangeRateService.getExchangeRates(db, currency, today);

      setRates(exchangeRates);
      setBaseCurrency(currency);
      setLastUpdate(new Date());
      setLoading(false);
    } catch (err) {
      console.error('Döviz kuru yükleme hatası:', err);
      setError(err);
      setLoading(false);
    }
  };
  
  // Para birimi dönüştürme
  const convertCurrency = async (amount, fromCurrency, toCurrency, date = null) => {
    try {
      return await exchangeRateService.convertCurrency(db, amount, fromCurrency, toCurrency, date);
    } catch (err) {
      console.error('Döviz kuru çevirme hatası:', err);
      return amount; // Hata durumunda orijinal miktarı döndür
    }
  };
  
  // Para birimi formatla
  const formatCurrency = (amount, currency = baseCurrency) => {
    return exchangeRateService.formatCurrency(amount, currency);
  };
  
  // Desteklenen para birimleri
  const getSupportedCurrencies = () => {
    return exchangeRateService.getSupportedCurrencies();
  };
  
  // İlk yükleme
  useEffect(() => {
    loadExchangeRates();
  }, []);
  
  // Context değerleri
  const value = {
    rates,
    baseCurrency,
    loading,
    error,
    lastUpdate,
    loadExchangeRates,
    convertCurrency,
    formatCurrency,
    getSupportedCurrencies
  };
  
  return (
    <ExchangeRateContext.Provider value={value}>
      {children}
    </ExchangeRateContext.Provider>
  );
};

/**
 * Döviz Kuru Context Hook'u
 * 
 * @returns {Object} Döviz kuru context değerleri
 */
export const useExchangeRate = () => {
  const context = useContext(ExchangeRateContext);
  
  if (!context) {
    throw new Error('useExchangeRate must be used within an ExchangeRateProvider');
  }
  
  return context;
};
