import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Colors } from '../constants/colors';
import * as salaryService from '../services/salaryService';
import * as exchangeRateService from '../services/exchangeRateService';
import * as settingsService from '../services/settingsService';
import { formatCurrency, formatDate } from '../utils/formatters';

/**
 * Maaş Ödemesi Form Ekranı
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @param {Object} props.route - Route nesnesi
 * @returns {JSX.Element} Maaş Ödemesi Form Ekranı
 */
export default function SalaryPaymentForm({ navigation, route }) {
  const db = useSQLiteContext();
  const { salaryId, paymentId } = route.params || {};
  const isEditing = !!paymentId;

  // Durum değişkenleri
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [salary, setSalary] = useState(null);

  // Form değerleri
  const [amount, setAmount] = useState('');
  const [currency, setCurrency] = useState('TRY');
  const [paymentDate, setPaymentDate] = useState(new Date());
  const [isPaid, setIsPaid] = useState(false);
  const [notes, setNotes] = useState('');

  // DateTimePicker durumu
  const [showDatePicker, setShowDatePicker] = useState(false);

  // Verileri yükle
  const loadData = useCallback(async () => {
    try {
      setLoading(true);

      if (salaryId) {
        // Maaş bilgilerini getir
        const salaryDetails = await salaryService.getSalaryDetails(db, salaryId);
        setSalary(salaryDetails);

        // Varsayılan değerleri ayarla
        setAmount(salaryDetails.amount.toString());
        setCurrency(salaryDetails.currency);
      }

      if (paymentId) {
        // Ödeme bilgilerini getir
        const payment = await db.getFirstAsync(`
          SELECT * FROM salary_payments WHERE id = ?
        `, [paymentId]);

        if (payment) {
          setAmount(payment.amount.toString());
          setCurrency(payment.currency);
          setPaymentDate(new Date(payment.payment_date));
          setIsPaid(payment.is_paid === 1);
          setNotes(payment.notes || '');

          // Maaş bilgilerini getir
          const salaryDetails = await salaryService.getSalaryDetails(db, payment.salary_id);
          setSalary(salaryDetails);
        }
      }

      setLoading(false);
    } catch (error) {
      console.error('Veri yükleme hatası:', error);
      Alert.alert('Hata', 'Veriler yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  }, [db, salaryId, paymentId]);

  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [loadData])
  );

  // Tarih değişikliği
  const handleDateChange = (event, selectedDate) => {
    setShowDatePicker(false);
    if (selectedDate) {
      setPaymentDate(selectedDate);
    }
  };

  // Formu doğrula
  const validateForm = () => {
    if (!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
      Alert.alert('Hata', 'Lütfen geçerli bir miktar girin.');
      return false;
    }

    if (!paymentDate) {
      Alert.alert('Hata', 'Lütfen bir ödeme tarihi seçin.');
      return false;
    }

    return true;
  };

  // Döviz karşılıklarını göster
  const showCurrencyEquivalents = async () => {
    try {
      if (!amount || isNaN(parseFloat(amount))) {
        Alert.alert('Hata', 'Lütfen önce geçerli bir miktar girin.');
        return;
      }

      const amountValue = parseFloat(amount);
      const date = paymentDate.toISOString().split('T')[0];

      // Özel para birimini al
      let customCurrency = 'GBP';
      try {
        customCurrency = await settingsService.getCustomCurrency(db);
      } catch (error) {
        console.error('Özel para birimi getirme hatası:', error);
      }

      // Döviz karşılıklarını hesapla
      const equivalents = await exchangeRateService.calculateCurrencyEquivalents(
        db, amountValue, currency, date, customCurrency
      );

      // Sonuçları göster
      Alert.alert(
        'Döviz Karşılıkları',
        `${formatCurrency(amountValue, currency)} tutarının döviz karşılıkları:\n\n` +
        `USD: ${formatCurrency(equivalents.USD, 'USD')}\n` +
        `EUR: ${formatCurrency(equivalents.EUR, 'EUR')}\n` +
        `${equivalents.custom.currency}: ${formatCurrency(equivalents.custom.amount, equivalents.custom.currency)}`,
        [{ text: 'Tamam' }]
      );
    } catch (error) {
      console.error('Döviz karşılıkları hesaplama hatası:', error);
      Alert.alert('Hata', 'Döviz karşılıkları hesaplanırken bir hata oluştu.');
    }
  };

  // Ödemeyi kaydet
  const savePayment = async () => {
    if (!validateForm()) return;

    try {
      setSaving(true);

      // Önce döviz karşılıklarını hesapla
      const amountValue = parseFloat(amount);
      const date = paymentDate.toISOString().split('T')[0];

      // Döviz karşılıklarını hesapla
      const equivalents = await exchangeRateService.calculateCurrencyEquivalents(
        db, amountValue, currency, date
      );

      // Kullanıcıya döviz karşılıklarını göster ve onay al
      return new Promise((resolve) => {
        Alert.alert(
          'Döviz Karşılıkları',
          `${formatCurrency(amountValue, currency)} tutarının döviz karşılıkları:\n\n` +
          `USD: ${formatCurrency(equivalents.USD, 'USD')}\n` +
          `EUR: ${formatCurrency(equivalents.EUR, 'EUR')}\n` +
          `${equivalents.custom.currency}: ${formatCurrency(equivalents.custom.amount, equivalents.custom.currency)}\n\n` +
          'Bu döviz karşılıkları kaydedilecektir. Devam etmek istiyor musunuz?',
          [
            {
              text: 'İptal',
              style: 'cancel',
              onPress: () => {
                setSaving(false);
                resolve(false);
              }
            },
            {
              text: 'Kaydet',
              onPress: async () => {
                try {
                  const paymentData = {
                    salary_id: salary.id,
                    amount: amountValue,
                    currency,
                    payment_date: date,
                    is_paid: isPaid ? 1 : 0,
                    notes
                  };

                  if (isEditing) {
                    // Ödemeyi güncelle
                    await salaryService.updateSalaryPayment(db, paymentId, paymentData);
                    Alert.alert('Başarılı', 'Maaş ödemesi güncellendi.');
                  } else {
                    // Yeni ödeme oluştur
                    await salaryService.createSalaryPayment(db, paymentData);
                    Alert.alert('Başarılı', 'Maaş ödemesi oluşturuldu.');
                  }

                  setSaving(false);
                  navigation.goBack();
                  resolve(true);
                } catch (error) {
                  console.error('Ödeme kaydetme hatası:', error);
                  Alert.alert('Hata', 'Ödeme kaydedilirken bir hata oluştu.');
                  setSaving(false);
                  resolve(false);
                }
              }
            }
          ]
        );
      });
    } catch (error) {
      console.error('Ödeme kaydetme hatası:', error);
      Alert.alert('Hata', 'Ödeme kaydedilirken bir hata oluştu.');
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={styles.loadingText}>Yükleniyor...</Text>
      </View>
    );
  }

  if (!salary) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <MaterialIcons name="error-outline" size={64} color={Colors.DANGER} />
        <Text style={styles.errorText}>Maaş bulunamadı.</Text>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>Geri Dön</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {isEditing ? 'Ödemeyi Düzenle' : 'Yeni Ödeme'}
        </Text>
        <TouchableOpacity
          style={styles.saveButton}
          onPress={savePayment}
          disabled={saving}
        >
          {saving ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <MaterialIcons name="check" size={24} color="#fff" />
          )}
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.salaryInfo}>
          <Text style={styles.salaryName}>{salary.name}</Text>
          <Text style={styles.salaryAmount}>
            {formatCurrency(salary.amount, salary.currency)}
          </Text>
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>Miktar</Text>
          <View style={styles.amountContainer}>
            <TextInput
              style={styles.amountInput}
              value={amount}
              onChangeText={setAmount}
              keyboardType="numeric"
              placeholder="0.00"
            />
            <View style={styles.currencyContainer}>
              <Text style={styles.currencyText}>{currency}</Text>
            </View>
          </View>

          <TouchableOpacity
            style={styles.currencyButton}
            onPress={showCurrencyEquivalents}
          >
            <MaterialIcons name="currency-exchange" size={16} color={Colors.PRIMARY} />
            <Text style={styles.currencyButtonText}>Döviz Karşılıklarını Göster</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>Ödeme Tarihi</Text>
          <TouchableOpacity
            style={styles.dateSelector}
            onPress={() => setShowDatePicker(true)}
          >
            <MaterialIcons name="event" size={20} color={Colors.PRIMARY} />
            <Text style={styles.dateText}>{formatDate(paymentDate)}</Text>
          </TouchableOpacity>

          {showDatePicker && (
            <DateTimePicker
              value={paymentDate}
              mode="date"
              display="default"
              onChange={handleDateChange}
            />
          )}
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>Ödeme Durumu</Text>
          <TouchableOpacity
            style={styles.statusSelector}
            onPress={() => setIsPaid(!isPaid)}
          >
            <MaterialIcons
              name={isPaid ? "check-circle" : "radio-button-unchecked"}
              size={24}
              color={isPaid ? Colors.SUCCESS : Colors.GRAY_400}
            />
            <Text style={[
              styles.statusText,
              { color: isPaid ? Colors.SUCCESS : Colors.GRAY_600 }
            ]}>
              {isPaid ? 'Ödendi' : 'Ödenmedi'}
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>Notlar (Opsiyonel)</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={notes}
            onChangeText={setNotes}
            placeholder="Ödeme ile ilgili notlar..."
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: Colors.PRIMARY,
  },
  errorText: {
    marginTop: 10,
    fontSize: 16,
    color: Colors.DANGER,
    marginBottom: 20,
  },
  backButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    backgroundColor: Colors.PRIMARY,
    borderRadius: 8,
  },
  backButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 16,
    paddingHorizontal: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
  saveButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  salaryInfo: {
    backgroundColor: Colors.PRIMARY_LIGHT,
    borderRadius: 8,
    padding: 16,
    marginBottom: 20,
  },
  salaryName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.PRIMARY,
    marginBottom: 8,
  },
  salaryAmount: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.PRIMARY,
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: Colors.GRAY_300,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: Colors.TEXT_DARK,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  amountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  amountInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: Colors.GRAY_300,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: Colors.TEXT_DARK,
  },
  currencyContainer: {
    backgroundColor: Colors.GRAY_200,
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderTopRightRadius: 8,
    borderBottomRightRadius: 8,
    marginLeft: -1,
  },
  currencyText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
  },
  currencyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 8,
    padding: 8,
    borderRadius: 4,
    backgroundColor: Colors.PRIMARY_LIGHT,
  },
  currencyButtonText: {
    marginLeft: 8,
    fontSize: 14,
    color: Colors.PRIMARY,
    fontWeight: '500',
  },
  dateSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.GRAY_300,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  dateText: {
    fontSize: 16,
    color: Colors.TEXT_DARK,
    marginLeft: 8,
  },
  statusSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.GRAY_300,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  statusText: {
    fontSize: 16,
    marginLeft: 8,
  },
});
