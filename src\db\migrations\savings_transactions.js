/**
 * <PERSON><PERSON><PERSON> işlemleri tablosunu oluşturan migrasyon
 * 
 * @param {object} db - SQLite veritabanı nesnesi
 * @returns {Promise<void>}
 */
export const migrateSavingsTransactions = async (db) => {
  try {
    // Tablo var mı kontrol et
    const hasSavingsTransactionsTable = await db.getFirstAsync(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name='savings_transactions'
    `);
    
    if (!hasSavingsTransactionsTable) {
      // Birikim işlemleri tablosunu oluştur
      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS savings_transactions (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          goal_id INTEGER NOT NULL,
          amount DECIMAL(10,2) NOT NULL,
          type TEXT NOT NULL CHECK(type IN ('deposit', 'withdrawal')),
          date TEXT NOT NULL,
          notes TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (goal_id) REFERENCES savings_goals (id) ON DELETE CASCADE
        )
      `);
      
      // İndeks oluştur
      await db.execAsync(`
        CREATE INDEX IF NOT EXISTS idx_savings_transactions_goal_id
        ON savings_transactions (goal_id)
      `);
    } else {
      // Tabloda eksik sütunlar var mı kontrol et
      const columns = await db.getAllAsync(`PRAGMA table_info(savings_transactions)`);
      
      // Sütun adlarını bir diziye dönüştür
      const columnNames = columns.map(col => col.name);
      
      // Eksik sütunları kontrol et ve ekle
      if (!columnNames.includes('notes')) {
        await db.execAsync(`ALTER TABLE savings_transactions ADD COLUMN notes TEXT`);
      }
      
      if (!columnNames.includes('updated_at')) {
        await db.execAsync(`ALTER TABLE savings_transactions ADD COLUMN updated_at DATETIME DEFAULT CURRENT_TIMESTAMP`);
      }
    }
  } catch (error) {
    console.error('Birikim işlemleri tablosu migrasyon hatası:', error);
    throw error;
  }
};
