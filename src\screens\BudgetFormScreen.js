import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Colors } from '../constants/colors';
import * as budgetService from '../services/budgetService';
import * as categoryService from '../services/categoryService';
import * as exchangeRateService from '../services/exchangeRateService';
import { formatCurrency, getCurrencySymbol } from '../utils/formatters';
import { formatDate } from '../utils/dateFormatters';

/**
 * Bütçe Oluşturma/Düzenleme Ekranı
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @param {Object} props.route - Route nesnesi
 * @returns {JSX.Element} Bütçe Form Ekranı
 */
export default function BudgetFormScreen({ navigation, route }) {
  const db = useSQLiteContext();
  const { budget } = route?.params || {};
  const isEditing = !!budget;

  // Durum değişkenleri
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [categories, setCategories] = useState([]);
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [showNewCategoryModal, setShowNewCategoryModal] = useState(false);
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [supportedCurrencies, setSupportedCurrencies] = useState(['TRY', 'USD', 'EUR', 'GBP']);
  const [showCurrencyModal, setShowCurrencyModal] = useState(false);

  // Yeni kategori form verileri
  const [newCategoryData, setNewCategoryData] = useState({
    name: '',
    type: 'expense',
    icon: 'category',
    color: Colors.PRIMARY
  });

  // Form verileri
  const [formData, setFormData] = useState({
    name: '',
    period: 'monthly', // daily, weekly, monthly, yearly
    start_date: new Date().toISOString().split('T')[0],
    end_date: '',
    is_active: 1,
    notes: '',
    currency: 'TRY'
  });

  // Verileri yükle
  const loadData = useCallback(async () => {
    try {
      setLoading(true);

      // Kategorileri getir
      const expenseCategories = await db.getAllAsync(`
        SELECT * FROM categories
        WHERE type IN ('expense', 'both')
        ORDER BY name
      `);
      setCategories(expenseCategories);

      // Desteklenen para birimlerini getir
      try {
        const currencies = ['TRY', 'USD', 'EUR', 'GBP', 'JPY', 'CHF', 'CAD', 'AUD'];
        setSupportedCurrencies(currencies);
      } catch (error) {
        console.error('Para birimleri yüklenirken hata:', error);
        // Varsayılan para birimleri kullanılacak
      }

      // Düzenleme modunda ise mevcut bütçe verilerini getir
      if (isEditing) {
        const budgetDetails = await budgetService.getBudgetDetails(db, budget.id);

        // Form verilerini doldur
        setFormData({
          name: budgetDetails.name,
          period: budgetDetails.period,
          start_date: budgetDetails.start_date,
          end_date: budgetDetails.end_date || '',
          is_active: budgetDetails.is_active,
          notes: budgetDetails.notes || '',
          currency: budgetDetails.currency || 'TRY'
        });

        // Seçili kategorileri ayarla
        setSelectedCategories(budgetDetails.categories.map(cat => ({
          category_id: cat.category_id,
          category_name: cat.category_name,
          amount: cat.amount.toString(),
          icon: cat.icon,
          color: cat.color
        })));
      }

      setLoading(false);
    } catch (error) {
      console.error('Veri yükleme hatası:', error);
      Alert.alert('Hata', 'Veriler yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  }, [isEditing, budget, db]);

  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [loadData])
  );

  // Form alanını güncelle
  const updateFormField = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Tarih seçiciyi göster/gizle
  const toggleDatePicker = (pickerName) => {
    if (pickerName === 'start') {
      setShowStartDatePicker(!showStartDatePicker);
    } else {
      setShowEndDatePicker(!showEndDatePicker);
    }
  };

  // Tarih değişikliğini işle
  const handleDateChange = (_, selectedDate, pickerName) => {
    if (Platform.OS === 'android') {
      toggleDatePicker(pickerName);
    }

    if (selectedDate) {
      const formattedDate = selectedDate.toISOString().split('T')[0];

      if (pickerName === 'start') {
        updateFormField('start_date', formattedDate);
      } else {
        updateFormField('end_date', formattedDate);
      }
    }
  };

  // Kategori seç
  const selectCategory = (category) => {
    // Kategori zaten seçili mi kontrol et
    const isAlreadySelected = selectedCategories.some(
      cat => cat.category_id === category.id
    );

    if (!isAlreadySelected) {
      setSelectedCategories(prev => [
        ...prev,
        {
          category_id: category.id,
          category_name: category.name,
          amount: '',
          icon: category.icon,
          color: category.color
        }
      ]);
    }

    setShowCategoryModal(false);
  };

  // Yeni kategori oluştur
  const createNewCategory = async () => {
    try {
      if (!newCategoryData.name.trim()) {
        Alert.alert('Hata', 'Lütfen kategori adı girin.');
        return;
      }

      // Kategoriyi veritabanına ekle
      const result = await categoryService.addCategory(db, {
        name: newCategoryData.name,
        type: 'expense',
        icon: newCategoryData.icon || 'category',
        color: newCategoryData.color || Colors.PRIMARY
      });

      // Yeni kategoriyi kategoriler listesine ekle
      const newCategory = {
        id: result.lastInsertRowId,
        name: newCategoryData.name,
        type: 'expense',
        icon: newCategoryData.icon || 'category',
        color: newCategoryData.color || Colors.PRIMARY
      };

      setCategories(prev => [...prev, newCategory]);

      // Yeni kategoriyi seçili kategorilere ekle
      selectCategory(newCategory);

      // Form verilerini sıfırla
      setNewCategoryData({
        name: '',
        type: 'expense',
        icon: 'category',
        color: Colors.PRIMARY
      });

      // Modalı kapat
      setShowNewCategoryModal(false);

      Alert.alert('Başarılı', 'Yeni kategori oluşturuldu.');
    } catch (error) {
      console.error('Kategori oluşturma hatası:', error);
      Alert.alert('Hata', 'Kategori oluşturulurken bir hata oluştu.');
    }
  };

  // Kategori kaldır
  const removeCategory = (categoryId) => {
    setSelectedCategories(prev =>
      prev.filter(cat => cat.category_id !== categoryId)
    );
  };

  // Kategori miktarını güncelle
  const updateCategoryAmount = (categoryId, amount) => {
    setSelectedCategories(prev =>
      prev.map(cat =>
        cat.category_id === categoryId
          ? { ...cat, amount }
          : cat
      )
    );
  };

  // Para birimini değiştir
  const changeCurrency = (currency) => {
    setFormData(prev => ({ ...prev, currency }));
    setShowCurrencyModal(false);
  };

  // Formu doğrula
  const validateForm = () => {
    if (!formData.name.trim()) {
      Alert.alert('Hata', 'Lütfen bütçe adını girin.');
      return false;
    }

    if (!formData.start_date) {
      Alert.alert('Hata', 'Lütfen başlangıç tarihini seçin.');
      return false;
    }

    if (selectedCategories.length === 0) {
      Alert.alert('Hata', 'Lütfen en az bir kategori ekleyin.');
      return false;
    }

    // Tüm kategorilerin miktarlarının girildiğinden emin ol
    const invalidCategory = selectedCategories.find(cat => !cat.amount || isNaN(parseFloat(cat.amount)));
    if (invalidCategory) {
      Alert.alert('Hata', `Lütfen ${invalidCategory.category_name} kategorisi için geçerli bir miktar girin.`);
      return false;
    }

    return true;
  };

  // Bütçeyi kaydet
  const saveBudget = async () => {
    if (!validateForm()) return;

    try {
      setSaving(true);

      // Kategori miktarlarını sayıya dönüştür
      const categoriesWithNumericAmounts = selectedCategories.map(cat => ({
        ...cat,
        amount: parseFloat(cat.amount)
      }));

      // Bütçe verilerini hazırla
      const budgetData = {
        ...formData,
        // Eğer bitiş tarihi boşsa null olarak ayarla
        end_date: formData.end_date.trim() ? formData.end_date : null
      };

      if (isEditing) {
        // Mevcut bütçeyi güncelle
        await budgetService.updateBudget(
          db,
          budget.id,
          budgetData,
          categoriesWithNumericAmounts
        );
        Alert.alert('Başarılı', 'Bütçe başarıyla güncellendi.');
      } else {
        // Yeni bütçe oluştur
        await budgetService.createBudget(
          db,
          budgetData,
          categoriesWithNumericAmounts
        );
        Alert.alert('Başarılı', 'Bütçe başarıyla oluşturuldu.');
      }

      setSaving(false);
      navigation.goBack();
    } catch (error) {
      console.error('Bütçe kaydetme hatası:', error);
      Alert.alert('Hata', 'Bütçe kaydedilirken bir hata oluştu.');
      setSaving(false);
    }
  };

  // Periyot formatını görüntüle
  const formatPeriod = (period) => {
    switch (period) {
      case 'daily': return 'Günlük';
      case 'weekly': return 'Haftalık';
      case 'monthly': return 'Aylık';
      case 'yearly': return 'Yıllık';
      default: return period;
    }
  };

  // Toplam bütçe miktarını hesapla
  const calculateTotalBudget = () => {
    return selectedCategories.reduce((total, cat) => {
      const amount = parseFloat(cat.amount) || 0;
      return total + amount;
    }, 0);
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={styles.loadingText}>Yükleniyor...</Text>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {isEditing ? 'Bütçeyi Düzenle' : 'Yeni Bütçe Oluştur'}
        </Text>
        <TouchableOpacity
          style={styles.saveButton}
          onPress={saveBudget}
          disabled={saving}
        >
          {saving ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <MaterialIcons name="check" size={24} color="#fff" />
          )}
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>Bütçe Bilgileri</Text>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Bütçe Adı</Text>
            <TextInput
              style={styles.input}
              value={formData.name}
              onChangeText={(text) => updateFormField('name', text)}
              placeholder="Örn: Aylık Harcama Bütçesi"
              placeholderTextColor="#999"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Bütçe Dönemi</Text>
            <View style={styles.periodSelector}>
              {['daily', 'weekly', 'monthly', 'yearly'].map((period) => (
                <TouchableOpacity
                  key={period}
                  style={[
                    styles.periodOption,
                    formData.period === period && styles.periodOptionSelected
                  ]}
                  onPress={() => updateFormField('period', period)}
                >
                  <Text style={[
                    styles.periodOptionText,
                    formData.period === period && styles.periodOptionTextSelected
                  ]}>
                    {formatPeriod(period)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Başlangıç Tarihi</Text>
            <TouchableOpacity
              style={styles.dateInput}
              onPress={() => toggleDatePicker('start')}
            >
              <Text style={styles.dateText}>
                {formData.start_date ? formatDate(formData.start_date) : 'Tarih seçin'}
              </Text>
              <MaterialIcons name="calendar-today" size={20} color={Colors.GRAY_600} />
            </TouchableOpacity>

            {showStartDatePicker && (
              <DateTimePicker
                value={new Date(formData.start_date || new Date())}
                mode="date"
                display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                onChange={(event, date) => handleDateChange(event, date, 'start')}
              />
            )}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Bitiş Tarihi (Opsiyonel)</Text>
            <TouchableOpacity
              style={styles.dateInput}
              onPress={() => toggleDatePicker('end')}
            >
              <Text style={styles.dateText}>
                {formData.end_date ? formatDate(formData.end_date) : 'Tarih seçin'}
              </Text>
              <MaterialIcons name="calendar-today" size={20} color={Colors.GRAY_600} />
            </TouchableOpacity>

            {showEndDatePicker && (
              <DateTimePicker
                value={new Date(formData.end_date || new Date())}
                mode="date"
                display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                onChange={(event, date) => handleDateChange(event, date, 'end')}
              />
            )}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Para Birimi</Text>
            <TouchableOpacity
              style={styles.currencySelector}
              onPress={() => setShowCurrencyModal(true)}
            >
              <Text style={styles.currencyText}>
                {getCurrencySymbol(formData.currency)} {formData.currency}
              </Text>
              <MaterialIcons name="arrow-drop-down" size={24} color={Colors.GRAY_600} />
            </TouchableOpacity>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Notlar (Opsiyonel)</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={formData.notes}
              onChangeText={(text) => updateFormField('notes', text)}
              placeholder="Bütçe ile ilgili notlar..."
              placeholderTextColor="#999"
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>
        </View>

        <View style={styles.formSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Kategori Bütçeleri</Text>
            <View style={styles.categoryButtonsContainer}>
              <TouchableOpacity
                style={styles.addCategoryButton}
                onPress={() => setShowCategoryModal(true)}
              >
                <MaterialIcons name="add" size={20} color="#fff" />
                <Text style={styles.addCategoryButtonText}>Kategori Ekle</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.newCategoryButton}
                onPress={() => setShowNewCategoryModal(true)}
              >
                <MaterialIcons name="create-new-folder" size={20} color="#fff" />
              </TouchableOpacity>
            </View>
          </View>

          {selectedCategories.length === 0 ? (
            <View style={styles.emptyCategories}>
              <MaterialIcons name="category" size={48} color={Colors.GRAY_400} />
              <Text style={styles.emptyCategoriesText}>
                Henüz kategori eklenmedi
              </Text>
              <Text style={styles.emptyCategoriesSubtext}>
                Bütçenize kategori eklemek için "Kategori Ekle" butonuna tıklayın
              </Text>
            </View>
          ) : (
            <>
              {selectedCategories.map((category) => (
                <View key={category.category_id} style={styles.categoryItem}>
                  <View style={styles.categoryInfo}>
                    <View style={styles.categoryNameContainer}>
                      <MaterialIcons
                        name={category.icon || "category"}
                        size={20}
                        color={category.color || Colors.PRIMARY}
                      />
                      <Text style={styles.categoryName}>{category.category_name}</Text>
                    </View>
                    <TouchableOpacity
                      style={styles.removeCategoryButton}
                      onPress={() => removeCategory(category.category_id)}
                    >
                      <MaterialIcons name="close" size={20} color={Colors.DANGER} />
                    </TouchableOpacity>
                  </View>

                  <View style={styles.categoryAmountContainer}>
                    <Text style={styles.categoryAmountLabel}>Bütçe Miktarı:</Text>
                    <TextInput
                      style={styles.categoryAmountInput}
                      value={category.amount}
                      onChangeText={(text) => updateCategoryAmount(category.category_id, text)}
                      placeholder="0.00"
                      placeholderTextColor="#999"
                      keyboardType="numeric"
                    />
                    <Text style={styles.currencyText}>{formData.currency}</Text>
                  </View>
                </View>
              ))}

              <View style={styles.totalBudget}>
                <Text style={styles.totalBudgetLabel}>Toplam Bütçe:</Text>
                <Text style={styles.totalBudgetAmount}>
                  {formatCurrency(calculateTotalBudget(), formData.currency)}
                </Text>
              </View>
            </>
          )}
        </View>
      </ScrollView>

      {/* Kategori Seçim Modalı */}
      {showCategoryModal && (
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Kategori Seçin</Text>
              <TouchableOpacity
                style={styles.modalCloseButton}
                onPress={() => setShowCategoryModal(false)}
              >
                <MaterialIcons name="close" size={24} color={Colors.GRAY_600} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody}>
              {categories.map((category) => {
                // Zaten seçili kategorileri kontrol et
                const isSelected = selectedCategories.some(
                  cat => cat.category_id === category.id
                );

                return (
                  <TouchableOpacity
                    key={category.id}
                    style={[
                      styles.modalCategoryItem,
                      isSelected && styles.modalCategoryItemDisabled
                    ]}
                    onPress={() => !isSelected && selectCategory(category)}
                    disabled={isSelected}
                  >
                    <View style={styles.modalCategoryInfo}>
                      <MaterialIcons
                        name={category.icon || "category"}
                        size={20}
                        color={category.color || Colors.PRIMARY}
                      />
                      <Text style={[
                        styles.modalCategoryName,
                        isSelected && styles.modalCategoryNameDisabled
                      ]}>
                        {category.name}
                      </Text>
                    </View>

                    {isSelected && (
                      <MaterialIcons name="check" size={20} color={Colors.SUCCESS} />
                    )}
                  </TouchableOpacity>
                );
              })}
            </ScrollView>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={styles.createCategoryButton}
                onPress={() => {
                  setShowCategoryModal(false);
                  setShowNewCategoryModal(true);
                }}
              >
                <MaterialIcons name="add" size={20} color="#fff" />
                <Text style={styles.createCategoryButtonText}>Yeni Kategori Oluştur</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}

      {/* Yeni Kategori Ekleme Modalı */}
      {showNewCategoryModal && (
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Yeni Kategori Oluştur</Text>
              <TouchableOpacity
                style={styles.modalCloseButton}
                onPress={() => setShowNewCategoryModal(false)}
              >
                <MaterialIcons name="close" size={24} color={Colors.GRAY_600} />
              </TouchableOpacity>
            </View>

            <View style={styles.modalBody}>
              <View style={styles.formGroup}>
                <Text style={styles.label}>Kategori Adı</Text>
                <TextInput
                  style={styles.input}
                  value={newCategoryData.name}
                  onChangeText={(text) => setNewCategoryData(prev => ({ ...prev, name: text }))}
                  placeholder="Kategori adı girin"
                  placeholderTextColor="#999"
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.label}>Simge</Text>
                <View style={styles.iconSelector}>
                  {['category', 'shopping-cart', 'restaurant', 'home', 'directions-car', 'local-hospital', 'school', 'sports-basketball'].map(icon => (
                    <TouchableOpacity
                      key={icon}
                      style={[
                        styles.iconOption,
                        newCategoryData.icon === icon && styles.iconOptionSelected
                      ]}
                      onPress={() => setNewCategoryData(prev => ({ ...prev, icon }))}
                    >
                      <MaterialIcons
                        name={icon}
                        size={24}
                        color={newCategoryData.icon === icon ? '#fff' : Colors.GRAY_600}
                      />
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.label}>Renk</Text>
                <View style={styles.colorSelector}>
                  {[Colors.PRIMARY, Colors.SUCCESS, Colors.WARNING, Colors.DANGER, Colors.INFO, '#9C27B0', '#FF9800', '#795548'].map(color => (
                    <TouchableOpacity
                      key={color}
                      style={[
                        styles.colorOption,
                        { backgroundColor: color },
                        newCategoryData.color === color && styles.colorOptionSelected
                      ]}
                      onPress={() => setNewCategoryData(prev => ({ ...prev, color }))}
                    />
                  ))}
                </View>
              </View>
            </View>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setShowNewCategoryModal(false)}
              >
                <Text style={styles.cancelButtonText}>İptal</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.saveButton}
                onPress={createNewCategory}
              >
                <Text style={styles.saveButtonText}>Kaydet</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}

      {/* Para Birimi Seçim Modalı */}
      {showCurrencyModal && (
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Para Birimi Seçin</Text>
              <TouchableOpacity
                style={styles.modalCloseButton}
                onPress={() => setShowCurrencyModal(false)}
              >
                <MaterialIcons name="close" size={24} color={Colors.GRAY_600} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody}>
              {supportedCurrencies.map((currency) => (
                <TouchableOpacity
                  key={currency}
                  style={[
                    styles.modalCurrencyItem,
                    formData.currency === currency && styles.modalCurrencyItemSelected
                  ]}
                  onPress={() => changeCurrency(currency)}
                >
                  <Text style={styles.modalCurrencySymbol}>
                    {getCurrencySymbol(currency)}
                  </Text>
                  <Text style={styles.modalCurrencyCode}>
                    {currency}
                  </Text>
                  {formData.currency === currency && (
                    <MaterialIcons name="check" size={20} color={Colors.SUCCESS} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      )}
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa'
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center'
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.GRAY_600
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 16,
    paddingHorizontal: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2
  },
  backButton: {
    padding: 8,
    marginRight: 8
  },
  headerTitle: {
    flex: 1,
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff'
  },
  saveButton: {
    padding: 8,
    marginLeft: 8
  },
  content: {
    flex: 1,
    padding: 16
  },
  formSection: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.GRAY_800,
    marginBottom: 16
  },
  formGroup: {
    marginBottom: 16
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.GRAY_700,
    marginBottom: 8
  },
  input: {
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: Colors.GRAY_800
  },
  textArea: {
    minHeight: 100
  },
  periodSelector: {
    flexDirection: 'row',
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    overflow: 'hidden'
  },
  periodOption: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'center'
  },
  periodOptionSelected: {
    backgroundColor: Colors.PRIMARY
  },
  periodOptionText: {
    fontSize: 14,
    color: Colors.GRAY_700
  },
  periodOptionTextSelected: {
    color: '#fff',
    fontWeight: '500'
  },
  dateInput: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12
  },
  dateText: {
    fontSize: 16,
    color: Colors.GRAY_800
  },
  currencySelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12
  },
  currencyText: {
    fontSize: 16,
    color: Colors.GRAY_800
  },
  categoryButtonsContainer: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  addCategoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8
  },
  addCategoryButtonText: {
    color: '#fff',
    fontWeight: '500',
    marginLeft: 4
  },
  newCategoryButton: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.INFO,
    borderRadius: 8,
    padding: 8,
    marginLeft: 8
  },
  emptyCategories: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 32
  },
  emptyCategoriesText: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.GRAY_600,
    marginTop: 16
  },
  emptyCategoriesSubtext: {
    fontSize: 14,
    color: Colors.GRAY_500,
    textAlign: 'center',
    marginTop: 8,
    paddingHorizontal: 32
  },
  categoryItem: {
    backgroundColor: Colors.GRAY_50,
    borderRadius: 8,
    padding: 12,
    marginBottom: 12
  },
  categoryInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12
  },
  categoryNameContainer: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.GRAY_800,
    marginLeft: 8
  },
  removeCategoryButton: {
    padding: 4
  },
  categoryAmountContainer: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  categoryAmountLabel: {
    fontSize: 14,
    color: Colors.GRAY_600,
    marginRight: 8
  },
  categoryAmountInput: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
    color: Colors.GRAY_800
  },
  currencyText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.GRAY_700,
    marginLeft: 8
  },
  totalBudget: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: Colors.GRAY_200,
    paddingTop: 16,
    marginTop: 8
  },
  totalBudgetLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.GRAY_800
  },
  totalBudgetAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.PRIMARY
  },
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden'
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.GRAY_800
  },
  modalCloseButton: {
    padding: 4
  },
  modalBody: {
    padding: 16,
    maxHeight: 400
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.GRAY_200
  },
  createCategoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8
  },
  createCategoryButtonText: {
    color: '#fff',
    fontWeight: '500',
    marginLeft: 4
  },
  cancelButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: Colors.GRAY_200
  },
  cancelButtonText: {
    color: Colors.GRAY_700,
    fontWeight: '500'
  },
  saveButtonText: {
    color: '#fff',
    fontWeight: '500'
  },
  iconSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8
  },
  iconOption: {
    width: 48,
    height: 48,
    borderRadius: 8,
    backgroundColor: Colors.GRAY_100,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 4
  },
  iconOptionSelected: {
    backgroundColor: Colors.PRIMARY
  },
  colorSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8
  },
  colorOption: {
    width: 36,
    height: 36,
    borderRadius: 18,
    margin: 4
  },
  colorOptionSelected: {
    borderWidth: 2,
    borderColor: Colors.GRAY_800
  },
  modalCategoryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_100
  },
  modalCategoryItemDisabled: {
    opacity: 0.6
  },
  modalCategoryInfo: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  modalCategoryName: {
    fontSize: 16,
    color: Colors.GRAY_800,
    marginLeft: 8
  },
  modalCategoryNameDisabled: {
    color: Colors.GRAY_500
  },
  modalCurrencyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_100
  },
  modalCurrencyItemSelected: {
    backgroundColor: Colors.GRAY_50
  },
  modalCurrencySymbol: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.PRIMARY,
    width: 40
  },
  modalCurrencyCode: {
    flex: 1,
    fontSize: 16,
    color: Colors.GRAY_800
  }
});
