import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Switch,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  Modal
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import * as salaryService from '../services/salaryService';
import * as exchangeRateService from '../services/exchangeRateService';
import * as settingsService from '../services/settingsService';
import { formatCurrency } from '../utils/formatters';

/**
 * Maaş Ekleme/Düzenleme Ekranı
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @param {Object} props.route - Route nesnesi
 * @returns {JSX.Element} Maaş Form Ekranı
 */
export default function SalaryFormScreen({ navigation, route }) {
  const db = useSQLiteContext();
  const { salary } = route.params || {};
  const isEditing = !!salary;

  // Durum değişkenleri
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [categories, setCategories] = useState([]);
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [showCurrencyModal, setShowCurrencyModal] = useState(false);
  const [supportedCurrencies, setSupportedCurrencies] = useState([]);

  // Form verileri
  const [formData, setFormData] = useState({
    name: '',
    amount: '',
    currency: 'TRY',
    payment_day: '1',
    is_active: true,
    category_id: null,
    account_id: null,
    tax_rate: '0',
    notes: '',
    create_payments: true,
    update_future_payments: false,
    custom_currency: 'GBP'
  });

  // Verileri yükle
  const loadData = useCallback(async () => {
    try {
      setLoading(true);

      // Kategorileri getir
      const incomeCategories = await db.getAllAsync(`
        SELECT * FROM categories
        WHERE type IN ('income', 'both')
        ORDER BY name
      `);
      setCategories(incomeCategories);

      // Desteklenen para birimlerini getir
      const currencies = exchangeRateService.getSupportedCurrencies();
      setSupportedCurrencies(currencies);

      // Özel para birimini getir
      try {
        // Önce user_settings tablosunun varlığını kontrol et
        await db.execAsync(`
          CREATE TABLE IF NOT EXISTS user_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            key TEXT NOT NULL UNIQUE,
            value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
          )
        `);

        // Varsayılan özel para birimini ekle
        await db.execAsync(`
          INSERT OR IGNORE INTO user_settings (key, value)
          VALUES ('custom_currency', 'GBP')
        `);

        const userCustomCurrency = await settingsService.getCustomCurrency(db);

        // Form verilerine ekle
        setFormData(prev => ({
          ...prev,
          custom_currency: userCustomCurrency
        }));
      } catch (error) {
        console.error('Özel para birimi getirme hatası:', error);
        // Hata durumunda varsayılan değeri kullan
        setFormData(prev => ({
          ...prev,
          custom_currency: 'GBP'
        }));
      }

      // Düzenleme modunda ise mevcut maaş verilerini getir
      if (isEditing) {
        const salaryDetails = await salaryService.getSalaryDetails(db, salary.id);

        // Form verilerini doldur
        setFormData({
          name: salaryDetails.name,
          amount: salaryDetails.amount.toString(),
          currency: salaryDetails.currency || 'TRY',
          payment_day: salaryDetails.payment_day.toString(),
          is_active: salaryDetails.is_active === 1,
          category_id: salaryDetails.category_id,
          account_id: salaryDetails.account_id,
          tax_rate: salaryDetails.tax_rate ? salaryDetails.tax_rate.toString() : '0',
          notes: salaryDetails.notes || '',
          create_payments: false,
          update_future_payments: true,
          custom_currency: salaryDetails.custom_currency || userCustomCurrency || 'GBP'
        });
      }

      setLoading(false);
    } catch (error) {
      console.error('Veri yükleme hatası:', error);
      Alert.alert('Hata', 'Veriler yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  }, [isEditing, salary, db]);

  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [loadData])
  );

  // Form alanını güncelle
  const updateFormField = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Kategori seç
  const selectCategory = (category) => {
    updateFormField('category_id', category.id);
    setShowCategoryModal(false);
  };

  // Seçili kategoriyi bul
  const getSelectedCategory = () => {
    return categories.find(c => c.id === formData.category_id) || null;
  };

  // Formu doğrula
  const validateForm = () => {
    if (!formData.name.trim()) {
      Alert.alert('Hata', 'Lütfen maaş adını girin.');
      return false;
    }

    if (!formData.amount || isNaN(parseFloat(formData.amount)) || parseFloat(formData.amount) <= 0) {
      Alert.alert('Hata', 'Lütfen geçerli bir tutar girin.');
      return false;
    }

    if (!formData.payment_day || isNaN(parseInt(formData.payment_day)) ||
        parseInt(formData.payment_day) < 1 || parseInt(formData.payment_day) > 31) {
      Alert.alert('Hata', 'Lütfen 1-31 arasında geçerli bir ödeme günü girin.');
      return false;
    }

    return true;
  };



  // Maaşı kaydet
  const saveSalary = async () => {
    if (!validateForm()) return;

    try {
      setSaving(true);

      // Önce döviz karşılıklarını hesapla
      const amount = parseFloat(formData.amount);
      const currency = formData.currency;

      // Döviz karşılıklarını hesapla
      const equivalents = await exchangeRateService.calculateCurrencyEquivalents(
        db, amount, currency
      );

      // Kullanıcıya döviz karşılıklarını göster ve onay al
      return new Promise((resolve) => {
        Alert.alert(
          'Döviz Karşılıkları',
          `${formatCurrency(amount, currency)} tutarının döviz karşılıkları:\n\n` +
          `USD: ${formatCurrency(equivalents.USD, 'USD')}\n` +
          `EUR: ${formatCurrency(equivalents.EUR, 'EUR')}\n` +
          `${formData.custom_currency}: ${formatCurrency(equivalents.custom.amount, formData.custom_currency)}\n\n` +
          'Bu döviz karşılıkları kaydedilecektir. Devam etmek istiyor musunuz?',
          [
            {
              text: 'İptal',
              style: 'cancel',
              onPress: () => {
                setSaving(false);
                resolve(false);
              }
            },
            {
              text: 'Kaydet',
              onPress: async () => {
                try {
                  const salaryData = {
                    ...formData,
                    amount,
                    payment_day: parseInt(formData.payment_day),
                    is_active: formData.is_active ? 1 : 0,
                    tax_rate: parseFloat(formData.tax_rate || '0')
                  };

                  if (isEditing) {
                    // Mevcut maaşı güncelle
                    await salaryService.updateSalary(db, salary.id, salaryData);
                    Alert.alert('Başarılı', 'Maaş başarıyla güncellendi.');
                  } else {
                    // Yeni maaş oluştur
                    await salaryService.createSalary(db, salaryData);
                    Alert.alert('Başarılı', 'Maaş başarıyla oluşturuldu.');
                  }

                  setSaving(false);
                  navigation.goBack();
                  resolve(true);
                } catch (error) {
                  console.error('Maaş kaydetme hatası:', error);
                  Alert.alert('Hata', 'Maaş kaydedilirken bir hata oluştu.');
                  setSaving(false);
                  resolve(false);
                }
              }
            }
          ]
        );
      });
    } catch (error) {
      console.error('Maaş kaydetme hatası:', error);
      Alert.alert('Hata', 'Maaş kaydedilirken bir hata oluştu.');
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={styles.loadingText}>Yükleniyor...</Text>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {isEditing ? 'Maaşı Düzenle' : 'Yeni Maaş Ekle'}
        </Text>
        <TouchableOpacity
          style={styles.saveButton}
          onPress={saveSalary}
          disabled={saving}
        >
          {saving ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <MaterialIcons name="check" size={24} color="#fff" />
          )}
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>Maaş Bilgileri</Text>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Maaş Adı</Text>
            <TextInput
              style={styles.input}
              value={formData.name}
              onChangeText={(text) => updateFormField('name', text)}
              placeholder="Örn: Şirket Maaşı"
              placeholderTextColor="#999"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Tutar</Text>
            <View style={styles.amountContainer}>
              <TextInput
                style={styles.amountInput}
                value={formData.amount}
                onChangeText={(text) => updateFormField('amount', text)}
                placeholder="0.00"
                placeholderTextColor="#999"
                keyboardType="numeric"
              />
              <View style={styles.currencySelector}>
                <Text style={styles.currencyText}>{formData.currency}</Text>
              </View>
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Özel Para Birimi</Text>
            <TouchableOpacity
              style={styles.selectButton}
              onPress={() => setShowCurrencyModal(true)}
            >
              <Text style={styles.selectButtonText}>
                {formData.custom_currency || 'Para Birimi Seçin'}
              </Text>
              <MaterialIcons name="chevron-right" size={20} color={Colors.GRAY_500} />
            </TouchableOpacity>
            <Text style={styles.helperText}>
              USD ve EUR dışında takip etmek istediğiniz üçüncü para birimini seçin.
            </Text>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Ödeme Günü</Text>
            <TextInput
              style={styles.input}
              value={formData.payment_day}
              onChangeText={(text) => updateFormField('payment_day', text)}
              placeholder="Ayın kaçıncı günü? (1-31)"
              placeholderTextColor="#999"
              keyboardType="numeric"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Kategori</Text>
            <TouchableOpacity
              style={styles.selectButton}
              onPress={() => setShowCategoryModal(true)}
            >
              {getSelectedCategory() ? (
                <View style={styles.selectedItem}>
                  <MaterialIcons
                    name={getSelectedCategory().icon || "category"}
                    size={20}
                    color={getSelectedCategory().color || Colors.PRIMARY}
                  />
                  <Text style={styles.selectedItemText}>{getSelectedCategory().name}</Text>
                </View>
              ) : (
                <Text style={styles.selectButtonText}>Kategori Seçin</Text>
              )}
              <MaterialIcons name="chevron-right" size={20} color={Colors.GRAY_500} />
            </TouchableOpacity>
          </View>



          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>Vergi Oranı (%)</Text>
              <TouchableOpacity
                onPress={() => Alert.alert(
                  "Vergi Oranı Nedir?",
                  "Maaşınızdan kesilen gelir vergisi yüzdesidir. Örneğin %15, %20, %27, %35 gibi. Bu oran, brüt maaşınızdan kesilen vergi miktarını hesaplamak için kullanılır. Eğer net maaşınızı giriyorsanız, bu alanı 0 olarak bırakabilirsiniz."
                )}
              >
                <MaterialIcons name="info-outline" size={18} color={Colors.PRIMARY} />
              </TouchableOpacity>
            </View>
            <TextInput
              style={styles.input}
              value={formData.tax_rate}
              onChangeText={(text) => updateFormField('tax_rate', text)}
              placeholder="0"
              placeholderTextColor="#999"
              keyboardType="numeric"
            />
            <Text style={styles.helperText}>
              Maaşınızdan kesilen gelir vergisi yüzdesi. Net maaş giriyorsanız 0 bırakabilirsiniz.
            </Text>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Notlar (Opsiyonel)</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={formData.notes}
              onChangeText={(text) => updateFormField('notes', text)}
              placeholder="Maaş ile ilgili notlar..."
              placeholderTextColor="#999"
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>

          <View style={styles.switchContainer}>
            <Text style={styles.switchLabel}>Aktif</Text>
            <Switch
              value={formData.is_active}
              onValueChange={(value) => updateFormField('is_active', value)}
              trackColor={{ false: Colors.GRAY_300, true: Colors.PRIMARY_LIGHT }}
              thumbColor={formData.is_active ? Colors.PRIMARY : Colors.GRAY_500}
            />
          </View>

          {!isEditing && (
            <View style={styles.switchContainer}>
              <Text style={styles.switchLabel}>Gelecek Ödemeleri Oluştur</Text>
              <Switch
                value={formData.create_payments}
                onValueChange={(value) => updateFormField('create_payments', value)}
                trackColor={{ false: Colors.GRAY_300, true: Colors.PRIMARY_LIGHT }}
                thumbColor={formData.create_payments ? Colors.PRIMARY : Colors.GRAY_500}
              />
            </View>
          )}

          {isEditing && (
            <View style={styles.switchContainer}>
              <Text style={styles.switchLabel}>Gelecek Ödemeleri Güncelle</Text>
              <Switch
                value={formData.update_future_payments}
                onValueChange={(value) => updateFormField('update_future_payments', value)}
                trackColor={{ false: Colors.GRAY_300, true: Colors.PRIMARY_LIGHT }}
                thumbColor={formData.update_future_payments ? Colors.PRIMARY : Colors.GRAY_500}
              />
            </View>
          )}
        </View>
      </ScrollView>

      {/* Kategori Seçim Modalı */}
      {showCategoryModal && (
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Kategori Seçin</Text>
              <TouchableOpacity
                style={styles.modalCloseButton}
                onPress={() => setShowCategoryModal(false)}
              >
                <MaterialIcons name="close" size={24} color={Colors.GRAY_600} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody}>
              {categories.map((category) => (
                <TouchableOpacity
                  key={category.id}
                  style={styles.modalItem}
                  onPress={() => selectCategory(category)}
                >
                  <View style={styles.modalItemInfo}>
                    <MaterialIcons
                      name={category.icon || "category"}
                      size={20}
                      color={category.color || Colors.PRIMARY}
                    />
                    <Text style={styles.modalItemName}>{category.name}</Text>
                  </View>

                  {formData.category_id === category.id && (
                    <MaterialIcons name="check" size={20} color={Colors.SUCCESS} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      )}

      {/* Para Birimi Seçim Modalı */}
      {showCurrencyModal && (
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Özel Para Birimi Seçin</Text>
              <TouchableOpacity
                style={styles.modalCloseButton}
                onPress={() => setShowCurrencyModal(false)}
              >
                <MaterialIcons name="close" size={24} color={Colors.GRAY_600} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody}>
              {supportedCurrencies.map((currency) => (
                <TouchableOpacity
                  key={currency.code}
                  style={styles.modalItem}
                  onPress={() => {
                    updateFormField('custom_currency', currency.code);
                    setShowCurrencyModal(false);
                  }}
                >
                  <View style={styles.modalItemInfo}>
                    <Text style={styles.currencySymbol}>{currency.symbol}</Text>
                    <Text style={styles.modalItemName}>{currency.code} - {currency.name}</Text>
                  </View>

                  {formData.custom_currency === currency.code && (
                    <MaterialIcons name="check" size={20} color={Colors.SUCCESS} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      )}


    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa'
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center'
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.GRAY_600
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 16,
    paddingHorizontal: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2
  },
  backButton: {
    padding: 8,
    marginRight: 8
  },
  headerTitle: {
    flex: 1,
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff'
  },
  saveButton: {
    padding: 8,
    marginLeft: 8
  },
  content: {
    flex: 1,
    padding: 16
  },
  formSection: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.GRAY_800,
    marginBottom: 16
  },
  formGroup: {
    marginBottom: 16
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.GRAY_700
  },
  helperText: {
    fontSize: 12,
    color: Colors.GRAY_500,
    marginTop: 4,
    fontStyle: 'italic'
  },
  input: {
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: Colors.GRAY_800
  },
  textArea: {
    minHeight: 100
  },
  amountContainer: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  amountInput: {
    flex: 1,
    backgroundColor: Colors.GRAY_100,
    borderTopLeftRadius: 8,
    borderBottomLeftRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: Colors.GRAY_800
  },
  currencySelector: {
    backgroundColor: Colors.GRAY_200,
    borderTopRightRadius: 8,
    borderBottomRightRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    justifyContent: 'center'
  },
  currencyText: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.GRAY_700
  },
  selectButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12
  },
  selectButtonText: {
    fontSize: 16,
    color: Colors.GRAY_500
  },
  selectedItem: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  selectedItemText: {
    fontSize: 16,
    color: Colors.GRAY_800,
    marginLeft: 8
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16
  },
  switchLabel: {
    fontSize: 16,
    color: Colors.GRAY_700
  },
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden'
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.GRAY_800
  },
  modalCloseButton: {
    padding: 4
  },
  modalBody: {
    padding: 16,
    maxHeight: 400
  },
  modalItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_100
  },
  modalItemInfo: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  modalItemName: {
    fontSize: 16,
    color: Colors.GRAY_800,
    marginLeft: 8
  },
  currencySymbol: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.PRIMARY,
    width: 24,
    textAlign: 'center'
  }
});
