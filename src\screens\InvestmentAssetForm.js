import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Modal,
  Platform,
  KeyboardAvoidingView,
  Alert,
  ActivityIndicator
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { MaterialIcons } from '@expo/vector-icons';
import { Picker } from '@react-native-picker/picker';
import { Colors } from '../constants/colors';
import * as investmentService from '../services/investmentService';

// Varlık tipleri
const ASSET_TYPES = [
  { value: 'stock', label: 'His<PERSON> Senedi', icon: 'show-chart' },
  { value: 'crypto', label: 'Kripto Para', icon: 'currency-bitcoin' },
  { value: 'gold', label: 'Altın', icon: 'monetization-on' },
  { value: 'silver', label: 'Gümüş', icon: 'monetization-on' },
  { value: 'forex', label: '<PERSON>öviz', icon: 'currency-exchange' },
  { value: 'bond', label: 'Tahvil/Bono', icon: 'receipt-long' },
  { value: 'fund', label: 'Fon', icon: 'account-balance' },
  { value: 'other', label: 'Diğer', icon: 'category' }
];

// Renk seçenekleri
const COLOR_OPTIONS = [
  '#6c5ce7', '#00cec9', '#0984e3', '#e84393', '#00b894',
  '#fdcb6e', '#e17055', '#d63031', '#636e72', '#2d3436'
];

/**
 * Yatırım varlığı ekleme/düzenleme formu
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {boolean} props.visible - Formun görünür olup olmadığı
 * @param {Function} props.onClose - Form kapatıldığında çağrılacak fonksiyon
 * @param {Function} props.onSave - Form kaydedildiğinde çağrılacak fonksiyon
 * @param {Object} props.asset - Düzenlenecek varlık (varsa)
 * @returns {JSX.Element} Yatırım varlığı formu
 */
export default function InvestmentAssetForm({ visible, onClose, onSave, asset }) {
  const db = useSQLiteContext();

  // Durum değişkenleri
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [showIconPicker, setShowIconPicker] = useState(false);
  const [showColorPicker, setShowColorPicker] = useState(false);

  // Form değerleri
  const [formData, setFormData] = useState({
    name: '',
    symbol: '',
    type: 'stock',
    description: '',
    icon: 'show-chart',
    color: '#6c5ce7',
    is_active: 1
  });

  // Düzenleme modunda ise, varlık verilerini yükle
  useEffect(() => {
    if (asset) {
      setFormData({
        name: asset.name || '',
        symbol: asset.symbol || '',
        type: asset.type || 'stock',
        description: asset.description || '',
        icon: asset.icon || 'show-chart',
        color: asset.color || '#6c5ce7',
        is_active: asset.is_active !== undefined ? asset.is_active : 1
      });
    }
  }, [asset]);

  // Form değişikliklerini işle
  const handleChange = (name, value) => {
    setFormData(prev => ({ ...prev, [name]: value }));

    // Varlık tipi değiştiğinde, varsayılan ikonu ayarla
    if (name === 'type') {
      const assetType = ASSET_TYPES.find(type => type.value === value);
      if (assetType) {
        setFormData(prev => ({ ...prev, [name]: value, icon: assetType.icon }));
      }
    }
  };

  // Renk seç
  const handleColorSelect = (color) => {
    setFormData(prev => ({ ...prev, color }));
    setShowColorPicker(false);
  };

  // İkon seç
  const handleIconSelect = (icon) => {
    setFormData(prev => ({ ...prev, icon }));
    setShowIconPicker(false);
  };

  // Formu kaydet
  const handleSave = async () => {
    // Validasyon
    if (!formData.name.trim()) {
      Alert.alert('Hata', 'Lütfen bir varlık adı girin.');
      return;
    }

    if (!formData.symbol.trim()) {
      Alert.alert('Hata', 'Lütfen bir sembol girin.');
      return;
    }

    try {
      setSaving(true);

      const assetData = {
        name: formData.name.trim(),
        symbol: formData.symbol.trim().toUpperCase(),
        type: formData.type,
        description: formData.description.trim(),
        icon: formData.icon,
        color: formData.color,
        is_active: formData.is_active
      };

      if (asset && asset.id) {
        // Mevcut varlığı güncelle
        await investmentService.updateInvestmentAsset(asset.id, assetData);
      } else {
        // Yeni varlık ekle
        await investmentService.addInvestmentAsset(assetData);
      }

      setSaving(false);

      if (onSave) {
        onSave();
      }

      onClose();
    } catch (error) {
      console.error('Varlık kaydedilirken hata:', error);
      Alert.alert('Hata', 'Varlık kaydedilirken bir hata oluştu.');
      setSaving(false);
    }
  };

  // İkon seçici
  const renderIconPicker = () => {
    const icons = [
      'show-chart', 'trending-up', 'trending-down', 'currency-bitcoin', 'monetization-on',
      'account-balance', 'account-balance-wallet', 'currency-exchange', 'receipt-long',
      'attach-money', 'money-off', 'credit-card', 'savings', 'payments', 'category'
    ];

    return (
      <Modal
        visible={showIconPicker}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowIconPicker(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>İkon Seçin</Text>
              <TouchableOpacity onPress={() => setShowIconPicker(false)}>
                <MaterialIcons name="close" size={24} color="#999" />
              </TouchableOpacity>
            </View>
            <ScrollView style={styles.iconGrid}>
              <View style={styles.iconContainer}>
                {icons.map((icon) => (
                  <TouchableOpacity
                    key={icon}
                    style={[
                      styles.iconItem,
                      formData.icon === icon && styles.selectedIconItem
                    ]}
                    onPress={() => handleIconSelect(icon)}
                  >
                    <MaterialIcons name={icon} size={32} color={formData.color} />
                  </TouchableOpacity>
                ))}
              </View>
            </ScrollView>
          </View>
        </View>
      </Modal>
    );
  };

  // Renk seçici
  const renderColorPicker = () => {
    return (
      <Modal
        visible={showColorPicker}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowColorPicker(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Renk Seçin</Text>
              <TouchableOpacity onPress={() => setShowColorPicker(false)}>
                <MaterialIcons name="close" size={24} color="#999" />
              </TouchableOpacity>
            </View>
            <View style={styles.colorGrid}>
              {COLOR_OPTIONS.map((color) => (
                <TouchableOpacity
                  key={color}
                  style={[
                    styles.colorItem,
                    { backgroundColor: color },
                    formData.color === color && styles.selectedColorItem
                  ]}
                  onPress={() => handleColorSelect(color)}
                />
              ))}
            </View>
          </View>
        </View>
      </Modal>
    );
  };

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      >
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.title}>
              {asset?.id ? 'Varlığı Düzenle' : 'Yeni Yatırım Varlığı'}
            </Text>
            <TouchableOpacity onPress={onClose}>
              <MaterialIcons name="close" size={24} color="#999" />
            </TouchableOpacity>
          </View>

          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={Colors.PRIMARY} />
              <Text style={styles.loadingText}>Yükleniyor...</Text>
            </View>
          ) : (
            <ScrollView style={styles.form}>
              {/* Varlık Adı */}
              <View style={styles.formGroup}>
                <Text style={styles.label}>Varlık Adı</Text>
                <TextInput
                  style={styles.input}
                  value={formData.name}
                  onChangeText={(value) => handleChange('name', value)}
                  placeholder="Örn: Apple Inc."
                />
              </View>

              {/* Sembol */}
              <View style={styles.formGroup}>
                <Text style={styles.label}>Sembol</Text>
                <TextInput
                  style={styles.input}
                  value={formData.symbol}
                  onChangeText={(value) => handleChange('symbol', value)}
                  placeholder="Örn: AAPL"
                  autoCapitalize="characters"
                />
              </View>

              {/* Varlık Tipi */}
              <View style={styles.formGroup}>
                <Text style={styles.label}>Varlık Tipi</Text>
                <View style={styles.pickerContainer}>
                  <Picker
                    selectedValue={formData.type}
                    onValueChange={(value) => handleChange('type', value)}
                    style={styles.picker}
                  >
                    {ASSET_TYPES.map((type) => (
                      <Picker.Item key={type.value} label={type.label} value={type.value} />
                    ))}
                  </Picker>
                </View>
              </View>

              {/* Açıklama */}
              <View style={styles.formGroup}>
                <Text style={styles.label}>Açıklama (İsteğe Bağlı)</Text>
                <TextInput
                  style={[styles.input, styles.textArea]}
                  value={formData.description}
                  onChangeText={(value) => handleChange('description', value)}
                  placeholder="Varlık hakkında kısa bir açıklama"
                  multiline
                  numberOfLines={4}
                />
              </View>

              {/* İkon ve Renk */}
              <View style={styles.formGroup}>
                <Text style={styles.label}>Görünüm</Text>
                <View style={styles.appearanceContainer}>
                  <TouchableOpacity
                    style={styles.iconSelector}
                    onPress={() => setShowIconPicker(true)}
                  >
                    <MaterialIcons name={formData.icon} size={32} color={formData.color} />
                    <Text style={styles.iconSelectorText}>İkon Seç</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[styles.colorSelector, { backgroundColor: formData.color }]}
                    onPress={() => setShowColorPicker(true)}
                  >
                    <Text style={styles.colorSelectorText}>Renk Seç</Text>
                  </TouchableOpacity>
                </View>
              </View>

              {/* Aktif/Pasif */}
              {asset && asset.id && (
                <View style={styles.formGroup}>
                  <Text style={styles.label}>Durum</Text>
                  <View style={styles.statusContainer}>
                    <TouchableOpacity
                      style={[
                        styles.statusButton,
                        formData.is_active === 1 && styles.activeStatusButton
                      ]}
                      onPress={() => handleChange('is_active', 1)}
                    >
                      <Text style={[
                        styles.statusButtonText,
                        formData.is_active === 1 && styles.activeStatusButtonText
                      ]}>
                        Aktif
                      </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={[
                        styles.statusButton,
                        formData.is_active === 0 && styles.inactiveStatusButton
                      ]}
                      onPress={() => handleChange('is_active', 0)}
                    >
                      <Text style={[
                        styles.statusButtonText,
                        formData.is_active === 0 && styles.inactiveStatusButtonText
                      ]}>
                        Pasif
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              )}

              {/* Butonlar */}
              <View style={styles.buttonContainer}>
                <TouchableOpacity
                  style={[styles.button, styles.cancelButton]}
                  onPress={onClose}
                  disabled={saving}
                >
                  <Text style={styles.buttonText}>İptal</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.button, styles.saveButton]}
                  onPress={handleSave}
                  disabled={saving}
                >
                  {saving ? (
                    <ActivityIndicator size="small" color="#fff" />
                  ) : (
                    <Text style={[styles.buttonText, styles.saveButtonText]}>Kaydet</Text>
                  )}
                </TouchableOpacity>
              </View>
            </ScrollView>
          )}
        </View>
      </KeyboardAvoidingView>

      {renderIconPicker()}
      {renderColorPicker()}
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  content: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: '90%',
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  form: {
    flex: 1,
    paddingTop: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    color: '#333',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: '#333',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    overflow: 'hidden',
  },
  picker: {
    height: 50,
  },
  appearanceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  iconSelector: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    marginRight: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconSelectorText: {
    marginTop: 8,
    fontSize: 14,
    color: '#666',
  },
  colorSelector: {
    flex: 1,
    borderRadius: 8,
    padding: 12,
    marginLeft: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  colorSelectorText: {
    fontSize: 14,
    color: '#fff',
    fontWeight: 'bold',
  },
  statusContainer: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    overflow: 'hidden',
  },
  statusButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  activeStatusButton: {
    backgroundColor: 'rgba(46, 204, 113, 0.2)',
  },
  inactiveStatusButton: {
    backgroundColor: 'rgba(231, 76, 60, 0.2)',
  },
  statusButtonText: {
    fontSize: 16,
    color: '#666',
  },
  activeStatusButtonText: {
    color: '#2ecc71',
    fontWeight: 'bold',
  },
  inactiveStatusButtonText: {
    color: '#e74c3c',
    fontWeight: 'bold',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
    marginBottom: 24,
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#f5f5f5',
    marginRight: 8,
  },
  saveButton: {
    backgroundColor: Colors.PRIMARY,
    marginLeft: 8,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  saveButtonText: {
    color: '#fff',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 12,
    width: '80%',
    maxHeight: '70%',
    padding: 16,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  iconGrid: {
    maxHeight: 300,
  },
  iconContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  iconItem: {
    width: '25%',
    padding: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectedIconItem: {
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
  },
  colorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  colorItem: {
    width: 50,
    height: 50,
    margin: 8,
    borderRadius: 25,
  },
  selectedColorItem: {
    borderWidth: 3,
    borderColor: '#333',
  },
});
