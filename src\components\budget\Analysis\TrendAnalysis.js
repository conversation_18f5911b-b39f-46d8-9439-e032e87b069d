/**
 * Trend Analizi Komponenti
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md - Stage 4, Phase 1
 * 
 * Harcama trendleri ve gelecek tahminleri
 * Maksimum 250 satır - Tek sorum<PERSON>luk prensibi
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../../../context/ThemeContext';
import { formatTRY, formatUSD, formatEUR } from '../../../utils/formatters';

/**
 * Trend analizi komponenti
 * @param {Object} props - Component props
 * @param {Array} props.trendData - Trend verileri [{period, amount, change, prediction}]
 * @param {string} props.currency - Para birimi ('TRY', 'USD', 'EUR')
 * @param {string} props.trendType - Trend türü ('spending', 'saving', 'budget')
 * @param {Object} props.insights - <PERSON><PERSON><PERSON> {direction, strength, forecast}
 * @param {Object} props.theme - <PERSON><PERSON> ob<PERSON> (opsiyonel, context'ten alınır)
 */
const TrendAnalysis = ({ 
  trendData = [], 
  currency = 'TRY',
  trendType = 'spending',
  insights = {},
  theme: propTheme 
}) => {
  const { theme, isDarkMode } = useTheme();
  const currentTheme = propTheme || theme;

  /**
   * Para birimi formatı
   * @param {number} value - Formatlanacak değer
   * @returns {string} Formatlanmış değer
   */
  const formatCurrency = (value) => {
    switch (currency) {
      case 'USD':
        return formatUSD(value);
      case 'EUR':
        return formatEUR(value);
      default:
        return formatTRY(value);
    }
  };

  /**
   * Para birimi sembolü
   * @returns {string} Para birimi sembolü
   */
  const getCurrencySymbol = () => {
    switch (currency) {
      case 'USD':
        return '$';
      case 'EUR':
        return '€';
      default:
        return '₺';
    }
  };

  /**
   * Trend yönü rengi belirleme
   * @param {string} direction - Trend yönü ('up', 'down', 'stable')
   * @returns {string} Renk kodu
   */
  const getTrendColor = (direction) => {
    switch (direction) {
      case 'up':
        return trendType === 'saving' ? currentTheme.SUCCESS : currentTheme.WARNING;
      case 'down':
        return trendType === 'saving' ? currentTheme.WARNING : currentTheme.SUCCESS;
      default:
        return currentTheme.INFO;
    }
  };

  /**
   * Trend yönü ikonu belirleme
   * @param {string} direction - Trend yönü ('up', 'down', 'stable')
   * @returns {string} İkon adı
   */
  const getTrendIcon = (direction) => {
    switch (direction) {
      case 'up':
        return 'trending-up';
      case 'down':
        return 'trending-down';
      default:
        return 'trending-flat';
    }
  };

  /**
   * Trend gücü mesajı belirleme
   * @param {string} strength - Trend gücü ('strong', 'moderate', 'weak')
   * @returns {string} Güç mesajı
   */
  const getStrengthMessage = (strength) => {
    switch (strength) {
      case 'strong':
        return 'Güçlü Trend';
      case 'moderate':
        return 'Orta Trend';
      case 'weak':
        return 'Zayıf Trend';
      default:
        return 'Belirsiz Trend';
    }
  };

  /**
   * Trend türü başlığı
   * @returns {string} Trend başlığı
   */
  const getTrendTitle = () => {
    switch (trendType) {
      case 'saving':
        return 'Tasarruf Trendi';
      case 'budget':
        return 'Bütçe Trendi';
      default:
        return 'Harcama Trendi';
    }
  };

  /**
   * Değişim yüzdesi formatı
   * @param {number} change - Değişim yüzdesi
   * @returns {Object} Formatlanmış değişim {text, color}
   */
  const formatChange = (change) => {
    const isPositive = change > 0;
    const color = isPositive 
      ? (trendType === 'saving' ? currentTheme.SUCCESS : currentTheme.WARNING)
      : (trendType === 'saving' ? currentTheme.WARNING : currentTheme.SUCCESS);
    
    return {
      text: `${isPositive ? '+' : ''}${change.toFixed(1)}%`,
      color
    };
  };

  /**
   * Dönem formatı
   * @param {string} period - Dönem string
   * @returns {string} Formatlanmış dönem
   */
  const formatPeriod = (period) => {
    const [year, month] = period.split('-');
    const monthNames = [
      'Oca', 'Şub', 'Mar', 'Nis', 'May', 'Haz',
      'Tem', 'Ağu', 'Eyl', 'Eki', 'Kas', 'Ara'
    ];
    return `${monthNames[parseInt(month) - 1]} ${year}`;
  };

  if (trendData.length === 0) {
    return (
      <View style={[styles.container, { backgroundColor: currentTheme.SURFACE }]}>
        <View style={styles.emptyContainer}>
          <MaterialIcons name="trending-up" size={48} color={currentTheme.TEXT_SECONDARY} />
          <Text style={[styles.emptyTitle, { color: currentTheme.TEXT_PRIMARY }]}>
            Trend Verisi Yok
          </Text>
          <Text style={[styles.emptySubtitle, { color: currentTheme.TEXT_SECONDARY }]}>
            Trend analizi için yeterli veri bulunmuyor
          </Text>
        </View>
      </View>
    );
  }

  const currencySymbol = getCurrencySymbol();
  const trendColor = getTrendColor(insights.direction);
  const trendIcon = getTrendIcon(insights.direction);
  const strengthMessage = getStrengthMessage(insights.strength);

  // Son dönem ve önceki dönem karşılaştırması
  const latestPeriod = trendData[trendData.length - 1];
  const previousPeriod = trendData[trendData.length - 2];

  return (
    <View style={[styles.container, { backgroundColor: currentTheme.SURFACE }]}>
      {/* Header */}
      <View style={styles.header}>
        <MaterialIcons name="trending-up" size={20} color={currentTheme.PRIMARY} />
        <Text style={[styles.title, { color: currentTheme.TEXT_PRIMARY }]}>
          {getTrendTitle()}
        </Text>
      </View>

      {/* Ana trend göstergesi */}
      <View style={[styles.mainTrend, { backgroundColor: trendColor + '20' }]}>
        <View style={styles.trendHeader}>
          <MaterialIcons name={trendIcon} size={32} color={trendColor} />
          <View style={styles.trendInfo}>
            <Text style={[styles.trendDirection, { color: trendColor }]}>
              {insights.direction === 'up' ? 'Artış Trendi' : 
               insights.direction === 'down' ? 'Azalış Trendi' : 'Stabil Trend'}
            </Text>
            <Text style={[styles.trendStrength, { color: currentTheme.TEXT_SECONDARY }]}>
              {strengthMessage}
            </Text>
          </View>
        </View>

        {latestPeriod && (
          <View style={styles.currentValue}>
            <Text style={[styles.currentLabel, { color: currentTheme.TEXT_SECONDARY }]}>
              Son Dönem ({formatPeriod(latestPeriod.period)})
            </Text>
            <Text style={[styles.currentAmount, { color: currentTheme.TEXT_PRIMARY }]}>
              {currencySymbol}{latestPeriod.amount.toLocaleString('tr-TR')}
            </Text>
            
            {previousPeriod && (
              <View style={styles.changeInfo}>
                <Text style={[styles.changeLabel, { color: currentTheme.TEXT_SECONDARY }]}>
                  Önceki döneme göre:
                </Text>
                <Text style={[styles.changeValue, { color: formatChange(latestPeriod.change).color }]}>
                  {formatChange(latestPeriod.change).text}
                </Text>
              </View>
            )}
          </View>
        )}
      </View>

      {/* Trend geçmişi */}
      <View style={styles.historySection}>
        <Text style={[styles.historyTitle, { color: currentTheme.TEXT_PRIMARY }]}>
          Trend Geçmişi
        </Text>
        
        <ScrollView style={styles.historyList} showsVerticalScrollIndicator={false}>
          {trendData.slice(-6).reverse().map((item, index) => {
            const change = formatChange(item.change);
            
            return (
              <View key={item.period} style={styles.historyItem}>
                <View style={styles.historyLeft}>
                  <Text style={[styles.historyPeriod, { color: currentTheme.TEXT_PRIMARY }]}>
                    {formatPeriod(item.period)}
                  </Text>
                  <Text style={[styles.historyAmount, { color: currentTheme.TEXT_SECONDARY }]}>
                    {currencySymbol}{item.amount.toLocaleString('tr-TR')}
                  </Text>
                </View>

                <View style={styles.historyRight}>
                  {item.change !== 0 && (
                    <View style={styles.historyChange}>
                      <MaterialIcons 
                        name={item.change > 0 ? 'arrow-upward' : 'arrow-downward'} 
                        size={16} 
                        color={change.color} 
                      />
                      <Text style={[styles.historyChangeText, { color: change.color }]}>
                        {change.text}
                      </Text>
                    </View>
                  )}
                </View>
              </View>
            );
          })}
        </ScrollView>
      </View>

      {/* Gelecek tahmini */}
      {insights.forecast && (
        <View style={styles.forecastSection}>
          <Text style={[styles.forecastTitle, { color: currentTheme.TEXT_PRIMARY }]}>
            Gelecek Tahmini
          </Text>
          
          <View style={[styles.forecastCard, { backgroundColor: currentTheme.INFO + '20' }]}>
            <MaterialIcons name="visibility" size={20} color={currentTheme.INFO} />
            <View style={styles.forecastContent}>
              <Text style={[styles.forecastLabel, { color: currentTheme.TEXT_SECONDARY }]}>
                Gelecek ay tahmini:
              </Text>
              <Text style={[styles.forecastAmount, { color: currentTheme.TEXT_PRIMARY }]}>
                {currencySymbol}{insights.forecast.toLocaleString('tr-TR')}
              </Text>
              <Text style={[styles.forecastNote, { color: currentTheme.TEXT_SECONDARY }]}>
                Mevcut trend devam ederse
              </Text>
            </View>
          </View>
        </View>
      )}

      {/* Öneriler */}
      <View style={styles.recommendationsSection}>
        <Text style={[styles.recommendationsTitle, { color: currentTheme.TEXT_PRIMARY }]}>
          Öneriler
        </Text>
        
        <View style={styles.recommendationsList}>
          {insights.direction === 'up' && trendType === 'spending' && (
            <View style={[styles.recommendationItem, { backgroundColor: currentTheme.WARNING + '10' }]}>
              <MaterialIcons name="warning" size={16} color={currentTheme.WARNING} />
              <Text style={[styles.recommendationText, { color: currentTheme.TEXT_SECONDARY }]}>
                Harcamalarınız artış gösteriyor. Bütçenizi gözden geçirin.
              </Text>
            </View>
          )}

          {insights.direction === 'down' && trendType === 'spending' && (
            <View style={[styles.recommendationItem, { backgroundColor: currentTheme.SUCCESS + '10' }]}>
              <MaterialIcons name="check-circle" size={16} color={currentTheme.SUCCESS} />
              <Text style={[styles.recommendationText, { color: currentTheme.TEXT_SECONDARY }]}>
                Harika! Harcamalarınızı azaltmayı başardınız.
              </Text>
            </View>
          )}

          {insights.strength === 'strong' && (
            <View style={[styles.recommendationItem, { backgroundColor: currentTheme.INFO + '10' }]}>
              <MaterialIcons name="info" size={16} color={currentTheme.INFO} />
              <Text style={[styles.recommendationText, { color: currentTheme.TEXT_SECONDARY }]}>
                Güçlü trend tespit edildi. Bu eğilim devam edebilir.
              </Text>
            </View>
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '500',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: 'center',
  },
  mainTrend: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  trendHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 12,
  },
  trendInfo: {
    flex: 1,
  },
  trendDirection: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  trendStrength: {
    fontSize: 14,
  },
  currentValue: {
    alignItems: 'center',
  },
  currentLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  currentAmount: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  changeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  changeLabel: {
    fontSize: 12,
  },
  changeValue: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  historySection: {
    marginBottom: 16,
  },
  historyTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 12,
  },
  historyList: {
    maxHeight: 150,
  },
  historyItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  historyLeft: {
    flex: 1,
  },
  historyPeriod: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 2,
  },
  historyAmount: {
    fontSize: 12,
  },
  historyRight: {
    alignItems: 'flex-end',
  },
  historyChange: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  historyChangeText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  forecastSection: {
    marginBottom: 16,
  },
  forecastTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 12,
  },
  forecastCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 12,
  },
  forecastContent: {
    flex: 1,
  },
  forecastLabel: {
    fontSize: 12,
    marginBottom: 2,
  },
  forecastAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  forecastNote: {
    fontSize: 11,
    fontStyle: 'italic',
  },
  recommendationsSection: {
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
    paddingTop: 16,
  },
  recommendationsTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 12,
  },
  recommendationsList: {
    gap: 8,
  },
  recommendationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  recommendationText: {
    fontSize: 13,
    flex: 1,
    lineHeight: 18,
  },
});

export default TrendAnalysis;
