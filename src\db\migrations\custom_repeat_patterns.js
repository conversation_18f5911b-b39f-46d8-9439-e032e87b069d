/**
 * Özel tekrarlama desenleri için veritabanı güncellemesi
 *
 * @param {object} db - SQLite veritabanı nesnesi
 * @returns {Promise<void>}
 */
export const migrateCustomRepeatPatterns = async (db) => {
  try {
    console.log('Özel tekrarlama desenleri migrasyonu başlatılıyor...');

    // Notifications tablosunda yeni alanlar var mı kontrol et
    const hasCustomPatternFields = await db.getFirstAsync(`
      PRAGMA table_info(notifications)
    `).then(columns => {
      return columns.some(col => col.name === 'custom_pattern');
    }).catch(() => false);

    // Her bir sütunu ayrı ayrı kontrol et ve ekle
    const columns = await db.getAllAsync(`PRAGMA table_info(notifications)`);

    // custom_pattern sütunu
    if (!columns.some(col => col.name === 'custom_pattern')) {
      try {
        console.log('Notifications tablosuna custom_pattern alanı ekleniyor...');
        await db.execAsync(`ALTER TABLE notifications ADD COLUMN custom_pattern TEXT`);
        console.log('custom_pattern alanı başarıyla eklendi.');
      } catch (error) {
        console.warn('custom_pattern alanı eklenirken hata:', error.message);
      }
    } else {
      console.log('custom_pattern alanı zaten mevcut.');
    }

    // custom_pattern_type sütunu
    if (!columns.some(col => col.name === 'custom_pattern_type')) {
      try {
        console.log('Notifications tablosuna custom_pattern_type alanı ekleniyor...');
        await db.execAsync(`ALTER TABLE notifications ADD COLUMN custom_pattern_type TEXT`);
        console.log('custom_pattern_type alanı başarıyla eklendi.');
      } catch (error) {
        console.warn('custom_pattern_type alanı eklenirken hata:', error.message);
      }
    } else {
      console.log('custom_pattern_type alanı zaten mevcut.');
    }

    // custom_pattern_value sütunu
    if (!columns.some(col => col.name === 'custom_pattern_value')) {
      try {
        console.log('Notifications tablosuna custom_pattern_value alanı ekleniyor...');
        await db.execAsync(`ALTER TABLE notifications ADD COLUMN custom_pattern_value TEXT`);
        console.log('custom_pattern_value alanı başarıyla eklendi.');
      } catch (error) {
        console.warn('custom_pattern_value alanı eklenirken hata:', error.message);
      }
    } else {
      console.log('custom_pattern_value alanı zaten mevcut.');
    }

    // Özel tekrarlama desenleri tablosunu oluştur
    const hasRepeatPatternsTable = await db.getFirstAsync(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='reminder_repeat_patterns'
    `);

    if (!hasRepeatPatternsTable) {
      console.log('reminder_repeat_patterns tablosu oluşturuluyor...');

      // Özel tekrarlama desenleri tablosunu oluştur
      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS reminder_repeat_patterns (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          description TEXT,
          pattern_type TEXT NOT NULL,
          pattern_value TEXT NOT NULL,
          is_system INTEGER DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Varsayılan tekrarlama desenleri ekle
      await db.runAsync(`
        INSERT INTO reminder_repeat_patterns (name, description, pattern_type, pattern_value, is_system)
        VALUES ('Her ayın ilk günü', 'Her ayın 1. günü tekrarlar', 'monthly_day', '{"day": 1}', 1)
      `);

      await db.runAsync(`
        INSERT INTO reminder_repeat_patterns (name, description, pattern_type, pattern_value, is_system)
        VALUES ('Her ayın son günü', 'Her ayın son günü tekrarlar', 'monthly_last_day', '{}', 1)
      `);

      await db.runAsync(`
        INSERT INTO reminder_repeat_patterns (name, description, pattern_type, pattern_value, is_system)
        VALUES ('Her ayın son iş günü', 'Her ayın son iş günü tekrarlar', 'monthly_last_business_day', '{}', 1)
      `);

      await db.runAsync(`
        INSERT INTO reminder_repeat_patterns (name, description, pattern_type, pattern_value, is_system)
        VALUES ('Her ayın 15. günü', 'Her ayın 15. günü tekrarlar', 'monthly_day', '{"day": 15}', 1)
      `);

      await db.runAsync(`
        INSERT INTO reminder_repeat_patterns (name, description, pattern_type, pattern_value, is_system)
        VALUES ('Her ayın ilk Pazartesi günü', 'Her ayın ilk Pazartesi günü tekrarlar', 'monthly_weekday', '{"weekday": 1, "occurrence": 1}', 1)
      `);

      await db.runAsync(`
        INSERT INTO reminder_repeat_patterns (name, description, pattern_type, pattern_value, is_system)
        VALUES ('Her ayın son Cuma günü', 'Her ayın son Cuma günü tekrarlar', 'monthly_weekday', '{"weekday": 5, "occurrence": -1}', 1)
      `);

      await db.runAsync(`
        INSERT INTO reminder_repeat_patterns (name, description, pattern_type, pattern_value, is_system)
        VALUES ('Her 3 ayda bir', 'Her 3 ayda bir tekrarlar', 'interval_months', '{"months": 3}', 1)
      `);

      await db.runAsync(`
        INSERT INTO reminder_repeat_patterns (name, description, pattern_type, pattern_value, is_system)
        VALUES ('Her 2 haftada bir', 'Her 2 haftada bir tekrarlar', 'interval_weeks', '{"weeks": 2}', 1)
      `);

      await db.runAsync(`
        INSERT INTO reminder_repeat_patterns (name, description, pattern_type, pattern_value, is_system)
        VALUES ('Hafta içi her gün', 'Pazartesi-Cuma arası her gün tekrarlar', 'weekdays', '{"days": [1, 2, 3, 4, 5]}', 1)
      `);

      await db.runAsync(`
        INSERT INTO reminder_repeat_patterns (name, description, pattern_type, pattern_value, is_system)
        VALUES ('Hafta sonu her gün', 'Cumartesi ve Pazar günleri tekrarlar', 'weekdays', '{"days": [6, 0]}', 1)
      `);

      console.log('reminder_repeat_patterns tablosu başarıyla oluşturuldu.');
    } else {
      console.log('reminder_repeat_patterns tablosu zaten mevcut.');
    }

    console.log('Özel tekrarlama desenleri migrasyonu tamamlandı.');
  } catch (error) {
    console.error('Özel tekrarlama desenleri migrasyon hatası:', error);
    throw error;
  }
};
