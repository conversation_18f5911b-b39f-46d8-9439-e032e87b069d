module.exports = (api) => {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      // Make sure Reanimated plugin comes first
      'react-native-reanimated/plugin',

      [
        'module-resolver',
        {
          root: ['./src', './assets', './app'],
          extensions: ['.ios.js', '.android.js', '.js', '.ts', '.tsx', '.json'],
          alias: {
            '@screens': './src/screens',
            '@components': './src/components',
            '@navigation': './src/navigation',
            '@theme': './src/theme',
            '@db': './src/db',
            '@utils': './src/utils',
            '@hooks': './src/hooks',
            '@assets': './assets',
            '@constants': './src/constants',
          },
        },
      ],
    ],
  };
};
