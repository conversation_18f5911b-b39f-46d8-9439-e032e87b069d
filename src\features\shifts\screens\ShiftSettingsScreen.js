import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Switch,
  ActivityIndicator,
  Alert
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../../../constants/colors';
import { shiftStyles } from '../styles/shiftStyles';
import * as shiftService from '../services/shiftService';

/**
 * Vardiya Ayarları Ekranı
 * 
 * Bu ekran, vardiya takibi ile ilgili ayarları yönetmeyi sağlar.
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @returns {JSX.Element} Vardiya ayarları ekranı
 */
const ShiftSettingsScreen = ({ navigation }) => {
  const db = useSQLiteContext();
  const insets = useSafeAreaInsets();
  
  // Durum değişkenleri
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [settings, setSettings] = useState({
    hourly_rate: '100',
    overtime_rate: '150',
    currency: 'TRY',
    daily_work_hours: '8',
    weekly_work_hours: '40',
    work_days: '1,2,3,4,5',
    auto_create_shifts: false,
    notify_before_shift: false,
    notification_time: '60'
  });
  
  // Verileri yükle
  useEffect(() => {
    loadSettings();
  }, []);
  
  // Ayarları yükle
  const loadSettings = async () => {
    try {
      setLoading(true);
      const workSettings = await shiftService.getWorkSettings(db);
      
      if (workSettings) {
        setSettings({
          hourly_rate: workSettings.hourly_rate?.toString() || '100',
          overtime_rate: workSettings.overtime_rate?.toString() || '150',
          currency: workSettings.currency || 'TRY',
          daily_work_hours: workSettings.daily_work_hours?.toString() || '8',
          weekly_work_hours: workSettings.weekly_work_hours?.toString() || '40',
          work_days: workSettings.work_days || '1,2,3,4,5',
          auto_create_shifts: workSettings.auto_create_shifts === 1,
          notify_before_shift: workSettings.notify_before_shift === 1,
          notification_time: workSettings.notification_time?.toString() || '60'
        });
      }
      
      setLoading(false);
    } catch (error) {
      console.error('Ayarları yükleme hatası:', error);
      Alert.alert('Hata', 'Ayarlar yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  };
  
  // Ayarları kaydet
  const saveSettings = async () => {
    try {
      setSaving(true);
      
      const settingsData = {
        hourly_rate: parseFloat(settings.hourly_rate) || 100,
        overtime_rate: parseFloat(settings.overtime_rate) || 150,
        currency: settings.currency || 'TRY',
        daily_work_hours: parseFloat(settings.daily_work_hours) || 8,
        weekly_work_hours: parseFloat(settings.weekly_work_hours) || 40,
        work_days: settings.work_days || '1,2,3,4,5',
        auto_create_shifts: settings.auto_create_shifts ? 1 : 0,
        notify_before_shift: settings.notify_before_shift ? 1 : 0,
        notification_time: parseInt(settings.notification_time) || 60
      };
      
      await shiftService.saveWorkSettings(db, settingsData);
      
      Alert.alert('Başarılı', 'Ayarlar başarıyla kaydedildi.');
      setSaving(false);
    } catch (error) {
      console.error('Ayarları kaydetme hatası:', error);
      Alert.alert('Hata', 'Ayarlar kaydedilirken bir hata oluştu.');
      setSaving(false);
    }
  };
  
  // Çalışma günü seçimi değiştir
  const toggleWorkDay = (day) => {
    const workDays = settings.work_days.split(',').map(d => parseInt(d));
    
    let newWorkDays;
    if (workDays.includes(day)) {
      newWorkDays = workDays.filter(d => d !== day);
    } else {
      newWorkDays = [...workDays, day].sort();
    }
    
    setSettings({
      ...settings,
      work_days: newWorkDays.join(',')
    });
  };
  
  if (loading) {
    return (
      <View style={[shiftStyles.loadingContainer, { paddingTop: insets.top }]}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={shiftStyles.loadingText}>Ayarlar yükleniyor...</Text>
      </View>
    );
  }
  
  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      {/* Başlık */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Vardiya Ayarları</Text>
        
        <View style={{ width: 40 }} />
      </View>
      
      <ScrollView style={styles.content}>
        {/* Ücret Ayarları */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Ücret Ayarları</Text>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Saatlik Ücret</Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                value={settings.hourly_rate}
                onChangeText={(value) => setSettings({ ...settings, hourly_rate: value })}
                keyboardType="numeric"
                placeholder="100"
              />
              <Text style={styles.inputSuffix}>₺</Text>
            </View>
          </View>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Fazla Mesai Ücreti (%)</Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                value={settings.overtime_rate}
                onChangeText={(value) => setSettings({ ...settings, overtime_rate: value })}
                keyboardType="numeric"
                placeholder="150"
              />
              <Text style={styles.inputSuffix}>%</Text>
            </View>
            <Text style={styles.helperText}>
              Normal saatlik ücretin yüzde kaç fazlası olarak hesaplanacak
            </Text>
          </View>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Para Birimi</Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                value={settings.currency}
                onChangeText={(value) => setSettings({ ...settings, currency: value })}
                placeholder="TRY"
              />
            </View>
          </View>
        </View>
        
        {/* Çalışma Saatleri */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Çalışma Saatleri</Text>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Günlük Çalışma Saati</Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                value={settings.daily_work_hours}
                onChangeText={(value) => setSettings({ ...settings, daily_work_hours: value })}
                keyboardType="numeric"
                placeholder="8"
              />
              <Text style={styles.inputSuffix}>saat</Text>
            </View>
            <Text style={styles.helperText}>
              Bu süreyi aşan çalışmalar mesai olarak sayılır
            </Text>
          </View>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Haftalık Çalışma Saati</Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                value={settings.weekly_work_hours}
                onChangeText={(value) => setSettings({ ...settings, weekly_work_hours: value })}
                keyboardType="numeric"
                placeholder="40"
              />
              <Text style={styles.inputSuffix}>saat</Text>
            </View>
          </View>
        </View>
        
        {/* Çalışma Günleri */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Çalışma Günleri</Text>
          <Text style={styles.helperText}>
            Hangi günlerin normal çalışma günü olduğunu seçin. Diğer günler tatil günü olarak işaretlenecektir.
          </Text>
          
          <View style={styles.workDaysContainer}>
            {[
              { id: 1, name: 'Pazartesi' },
              { id: 2, name: 'Salı' },
              { id: 3, name: 'Çarşamba' },
              { id: 4, name: 'Perşembe' },
              { id: 5, name: 'Cuma' },
              { id: 6, name: 'Cumartesi' },
              { id: 7, name: 'Pazar' }
            ].map(day => {
              const isSelected = settings.work_days.split(',').includes(day.id.toString());
              
              return (
                <View key={day.id} style={styles.workDayItem}>
                  <Text style={styles.workDayText}>{day.name}</Text>
                  <Switch
                    value={isSelected}
                    onValueChange={() => toggleWorkDay(day.id)}
                    trackColor={{ false: '#ddd', true: Colors.PRIMARY }}
                    thumbColor="#fff"
                  />
                </View>
              );
            })}
          </View>
        </View>
        
        {/* Vardiya Planlaması */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Vardiya Planlaması</Text>
          
          <View style={styles.switchItem}>
            <View style={styles.switchInfo}>
              <MaterialIcons name="auto-awesome" size={24} color={settings.auto_create_shifts ? Colors.PRIMARY : '#666'} />
              <Text style={styles.switchLabel}>Otomatik Vardiya Oluştur</Text>
            </View>
            <Switch
              value={settings.auto_create_shifts}
              onValueChange={(value) => setSettings({ ...settings, auto_create_shifts: value })}
              trackColor={{ false: '#ddd', true: Colors.PRIMARY }}
              thumbColor="#fff"
            />
          </View>
          <Text style={styles.helperText}>
            Aktif planlamalara göre otomatik olarak vardiya oluşturulur
          </Text>
          
          <View style={styles.switchItem}>
            <View style={styles.switchInfo}>
              <MaterialIcons name="notifications" size={24} color={settings.notify_before_shift ? Colors.PRIMARY : '#666'} />
              <Text style={styles.switchLabel}>Vardiya Bildirimleri</Text>
            </View>
            <Switch
              value={settings.notify_before_shift}
              onValueChange={(value) => setSettings({ ...settings, notify_before_shift: value })}
              trackColor={{ false: '#ddd', true: Colors.PRIMARY }}
              thumbColor="#fff"
            />
          </View>
          
          {settings.notify_before_shift && (
            <View style={styles.formGroup}>
              <Text style={styles.label}>Bildirim Zamanı</Text>
              <View style={styles.inputContainer}>
                <TextInput
                  style={styles.input}
                  value={settings.notification_time}
                  onChangeText={(value) => setSettings({ ...settings, notification_time: value })}
                  keyboardType="numeric"
                  placeholder="60"
                />
                <Text style={styles.inputSuffix}>dakika önce</Text>
              </View>
            </View>
          )}
        </View>
        
        {/* Kaydet Butonu */}
        <TouchableOpacity
          style={styles.saveButton}
          onPress={saveSettings}
          disabled={saving}
        >
          {saving ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <>
              <MaterialIcons name="save" size={20} color="#fff" />
              <Text style={styles.saveButtonText}>Ayarları Kaydet</Text>
            </>
          )}
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...shiftStyles.container,
  },
  header: {
    ...shiftStyles.header,
  },
  headerTitle: {
    ...shiftStyles.headerTitle,
  },
  backButton: {
    ...shiftStyles.backButton,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    paddingBottom: 8,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 12,
    paddingHorizontal: 16,
  },
  input: {
    flex: 1,
    paddingVertical: 14,
    fontSize: 16,
    color: '#333',
  },
  inputSuffix: {
    fontSize: 16,
    color: '#666',
    marginLeft: 8,
  },
  helperText: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
    marginLeft: 4,
  },
  workDaysContainer: {
    marginTop: 8,
  },
  workDayItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    marginBottom: 4,
    backgroundColor: '#f9f9f9',
    borderRadius: 12,
  },
  workDayText: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
  },
  switchItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    marginBottom: 8,
    backgroundColor: '#f9f9f9',
    borderRadius: 12,
  },
  switchInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  switchLabel: {
    fontSize: 16,
    color: '#333',
    marginLeft: 8,
    fontWeight: '500',
  },
  saveButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
    borderRadius: 12,
    paddingVertical: 16,
    marginBottom: 24,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
    marginLeft: 8,
  },
});

export default ShiftSettingsScreen;
