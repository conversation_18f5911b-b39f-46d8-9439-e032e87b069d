import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Animated,
  Vibration,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { useAppContext } from '../context/AppContext';
import { useSecurity } from '../context/SecurityContext';

/**
 * PIN ve Biyometrik Güvenlik Ekranı
 * SecurityContext entegrasyonu ile güncellenmiş PIN doğrulama ekranı
 */
export default function PinSecurityScreen({ navigation, route }) {
  const insets = useSafeAreaInsets();
  const { theme } = useTheme();
  const { defaultCurrency } = useAppContext();
  const {
    isAuthenticated,
    pinEnabled,
    biometricEnabled,
    biometricAvailable,
    biometricType,
    securityLoading,
    verifyPin,
    verifyBiometric,
    setupPin,
    changePin,
    checkLockStatus,
  } = useSecurity();
  
  // Route parametreleri
  const { mode = 'verify' } = route.params || {}; // 'setup', 'verify', 'change'
  
  const [pin, setPin] = useState('');
  const [confirmPin, setConfirmPin] = useState('');
  const [step, setStep] = useState(mode === 'setup' ? 'setup' : 'verify');
  const [isLocked, setIsLocked] = useState(false);
  const [lockTimeRemaining, setLockTimeRemaining] = useState(0);
  const [showBiometric, setShowBiometric] = useState(false);
  
  // Animasyonlar
  const [shakeAnimation] = useState(new Animated.Value(0));

  // Başlangıç kontrolleri
  useEffect(() => {
    initializeScreen();
  }, []);

  // Biometrik otomatik başlatma
  useEffect(() => {
    if (step === 'verify' && biometricEnabled && biometricAvailable && !isLocked && pin.length === 0) {
      // İlk yüklendiğinde otomatik biometrik başlat
      const timer = setTimeout(() => {
        setShowBiometric(true);
        // Otomatik başlatma için sessiz versiyon kullan
        handleBiometricAuthSilent();
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [step, biometricEnabled, biometricAvailable, isLocked, pin.length]);
  
  // Kilitleme zamanlayıcısı
  useEffect(() => {
    let interval;
    if (isLocked && lockTimeRemaining > 0) {
      interval = setInterval(() => {
        setLockTimeRemaining(prev => {
          if (prev <= 1) {
            setIsLocked(false);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isLocked, lockTimeRemaining]);

  // Kimlik doğrulama başarılı olduğunda main ekrana yönlendir
  useEffect(() => {
    if (isAuthenticated && mode === 'verify') {
      navigation.replace('Main');
    }
  }, [isAuthenticated, mode, navigation]);

  /**
   * Ekranı başlat ve gerekli kontrolleri yap
   */
  const initializeScreen = async () => {
    try {
      // Kilitleme durumunu kontrol et
      const lockStatus = await checkLockStatus();
      if (lockStatus.locked) {
        setIsLocked(true);
        setLockTimeRemaining(Math.ceil(lockStatus.remainingTime / 1000));
      }
      
      // Biyometrik doğrulama durumunu ayarla
      if (mode === 'verify' && biometricEnabled && biometricAvailable && !lockStatus.locked) {
        setShowBiometric(true);
      } else {
        setShowBiometric(false);
      }
    } catch (error) {
      console.error('Ekran başlatma hatası:', error);
    }
  };

  /**
   * PIN rakam ekleme
   */
  const addDigit = (digit) => {
    if (isLocked) return;
    
    if (step === 'verify' || step === 'setup') {
      if (pin.length < 6) {
        setPin(pin + digit);
      }
    } else if (step === 'confirm') {
      if (confirmPin.length < 6) {
        setConfirmPin(confirmPin + digit);
      }
    }
  };

  /**
   * Son rakamı silme
   */
  const removeDigit = () => {
    if (isLocked) return;
    
    if (step === 'verify' || step === 'setup') {
      setPin(pin.slice(0, -1));
    } else if (step === 'confirm') {
      setConfirmPin(confirmPin.slice(0, -1));
    }
  };

  /**
   * PIN onaylama
   */
  const handlePinSubmit = async () => {
    if (isLocked) return;
    
    try {
      if (step === 'verify') {
        // PIN doğrulama
        if (pin.length !== 6) return;
        
        const result = await verifyPin(pin);
        if (result.success) {
          // Başarılı - main ekrana yönlendir
          navigation.replace('Main');
        } else {
          // Başarısız - hata göster
          playShakeAnimation();
          setPin('');
          
          if (result.locked) {
            setIsLocked(true);
            setLockTimeRemaining(Math.ceil(result.remainingTime / 1000));
            Alert.alert('Hesap Kilitlendi', `Çok fazla yanlış deneme. ${Math.ceil(result.remainingTime / 60)} dakika sonra tekrar deneyin.`);
          } else {
            Alert.alert('Yanlış PIN', result.error || 'Girdiğiniz PIN yanlış.');
          }
        }
      } else if (step === 'setup') {
        // PIN kurulumu
        if (pin.length !== 6) return;
        setStep('confirm');
      } else if (step === 'confirm') {
        // PIN onaylama
        if (confirmPin.length !== 6) return;
        
        if (pin !== confirmPin) {
          Alert.alert('PIN Uyuşmadı', 'Girdiğiniz PIN\'ler uyuşmuyor.');
          playShakeAnimation();
          setConfirmPin('');
          return;
        }
        
        const result = await setupPin(pin);
        if (result.success) {
          Alert.alert(
            'PIN Kuruldu',
            'PIN başarıyla kuruldu.',
            [
              {
                text: 'Tamam',
                onPress: () => navigation.replace('Main')
              }
            ]
          );
        } else {
          Alert.alert('Hata', result.error || 'PIN kurulurken bir hata oluştu.');
        }
      }
    } catch (error) {
      console.error('PIN işlem hatası:', error);
      Alert.alert('Hata', 'Bir hata oluştu. Lütfen tekrar deneyin.');
    }
  };

  /**
   * Biyometrik doğrulama - Otomatik başlatma için sessiz versiyon
   */
  const handleBiometricAuthSilent = async () => {
    try {
      const result = await verifyBiometric();
      if (result.success) {
        // Başarılı - main ekrana yönlendir
        navigation.replace('Main');
      } else {
        // Otomatik başlatmada hata mesajı gösterme, sadece log
        console.log('Otomatik biometrik doğrulama sonucu:', result.error);
      }
    } catch (error) {
      // Otomatik başlatmada hata mesajı gösterme, sadece log
      console.log('Otomatik biometrik doğrulama hatası:', error.message);
    }
  };

  /**
   * Biyometrik doğrulama - Manuel buton için
   */
  const handleBiometricAuth = async () => {
    try {
      const result = await verifyBiometric();
      if (result.success) {
        // Başarılı - main ekrana yönlendir
        navigation.replace('Main');
      } else {
        // Başarısız - hata göster ama sadece gerçek hata durumunda
        if (result.silent || 
            result.error === 'user_canceled' ||
            (result.error && (
              result.error.includes('cancel') || 
              result.error.includes('dismiss') ||
              result.error.includes('abort')
            ))) {
          // Sessiz hata - kullanıcı iptal etti, mesaj gösterme
          console.log('Biometrik doğrulama iptal edildi:', result.error);
        } else {
          // Gerçek hata - mesaj göster
          Alert.alert('Biyometrik Doğrulama Başarısız', result.error || 'Doğrulama başarısız.');
        }
      }
    } catch (error) {
      console.error('Biyometrik doğrulama hatası:', error);
      // Sadece gerçek hata durumunda mesaj göster
      const errorMessage = error.message || error.toString();
      if (!errorMessage.includes('cancel') && 
          !errorMessage.includes('dismiss') && 
          !errorMessage.includes('abort')) {
        Alert.alert('Hata', 'Biyometrik doğrulama sırasında bir hata oluştu.');
      }
    }
  };

  /**
   * Sallama animasyonu
   */
  const playShakeAnimation = () => {
    Vibration.vibrate(500);
    Animated.sequence([
      Animated.timing(shakeAnimation, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: -10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 0, duration: 50, useNativeDriver: true }),
    ]).start();
  };

  /**
   * Adım başlığını belirle
   */
  const getStepTitle = () => {
    switch (step) {
      case 'verify':
        return 'PIN Girişi';
      case 'setup':
        return 'PIN Oluşturun';
      case 'confirm':
        return 'PIN\'i Onaylayın';
      default:
        return 'Güvenlik';
    }
  };

  /**
   * Adım açıklamasını belirle
   */
  const getStepDescription = () => {
    switch (step) {
      case 'verify':
        return 'Devam etmek için PIN\'inizi girin';
      case 'setup':
        return '6 haneli güvenli bir PIN oluşturun';
      case 'confirm':
        return 'Oluşturduğunuz PIN\'i tekrar girin';
      default:
        return '';
    }
  };

  /**
   * PIN noktalarını render et
   */
  const renderPinDots = () => {
    const currentPin = step === 'confirm' ? confirmPin : pin;
    const maxLength = 6;
    
    return (
      <Animated.View 
        style={[
          styles.pinDotsRow,
          { transform: [{ translateX: shakeAnimation }] }
        ]}
      >
        {[...Array(maxLength)].map((_, index) => (
          <View
            key={index}
            style={[
              styles.pinDot,
              { 
                backgroundColor: index < currentPin.length ? theme.colors.primary : 'transparent',
                borderColor: index < currentPin.length ? theme.colors.primary : theme.colors.border
              }
            ]}
          />
        ))}
      </Animated.View>
    );
  };

  /**
   * Sayısal tuş takımı
   */
  const renderNumPad = () => {
    const numPadLayout = [
      [1, 2, 3],
      [4, 5, 6],
      [7, 8, 9],
      ['', 0, 'backspace']
    ];
    
    return (
      <View style={styles.numPad}>
        {numPadLayout.map((row, rowIndex) => (
          <View key={rowIndex} style={styles.numPadRow}>
            {row.map((item, itemIndex) => {
              if (item === '') {
                return (
                  <View key={itemIndex} style={styles.emptyButton} />
                );
              }
              
              if (item === 'backspace') {
                return (
                  <TouchableOpacity
                    key={itemIndex}
                    style={[
                      styles.numButton,
                      { backgroundColor: 'transparent' },
                      (isLocked || (step === 'confirm' ? confirmPin.length === 0 : pin.length === 0)) && { opacity: 0.3 }
                    ]}
                    onPress={removeDigit}
                    disabled={isLocked || (step === 'confirm' ? confirmPin.length === 0 : pin.length === 0)}
                  >
                    <MaterialIcons 
                      name="backspace" 
                      size={22} 
                      color={theme.colors.textSecondary} 
                    />
                  </TouchableOpacity>
                );
              }
              
              return (
                <TouchableOpacity
                  key={itemIndex}
                  style={[
                    styles.numButton,
                    { backgroundColor: 'transparent' },
                    isLocked && { opacity: 0.3 }
                  ]}
                  onPress={() => addDigit(item.toString())}
                  disabled={isLocked}
                >
                  <Text style={[styles.numButtonText, { color: theme.colors.text }]}>
                    {item}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </View>
        ))}
      </View>
    );
  };

  // Otomatik PIN submit (6 rakam tamamlandığında)
  useEffect(() => {
    const currentPin = step === 'confirm' ? confirmPin : pin;
    if (currentPin.length === 6 && !isLocked) {
      setTimeout(() => {
        handlePinSubmit();
      }, 100);
    }
  }, [pin, confirmPin, step, isLocked]);

  if (securityLoading) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.content}>
          <Text style={[styles.title, { color: theme.colors.text }]}>
            Yükleniyor...
          </Text>
        </View>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView 
      style={[styles.container, { backgroundColor: theme.colors.background, paddingTop: insets.top }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
    >
      {/* Header */}
      {step !== 'verify' && (
        <View style={styles.header}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={styles.backButton}
          >
            <MaterialIcons name="arrow-back" size={24} color={theme.colors.text} />
          </TouchableOpacity>
        </View>
      )}

      {/* Content */}
      <View style={styles.content}>
        {/* Minimal PIN Başlık */}
        {(!showBiometric || isLocked) && (
          <View style={styles.pinHeaderContainer}>
            <Text style={[styles.pinTitle, { color: theme.colors.text }]}>
              {getStepTitle()}
            </Text>
            <Text style={[styles.pinSubtitle, { color: theme.colors.textSecondary }]}>
              {getStepDescription()}
            </Text>
          </View>
        )}

        {/* Biometrik Başlık */}
        {showBiometric && step === 'verify' && !isLocked && (
          <View style={styles.biometricHeaderContainer}>
            <MaterialIcons name="security" size={48} color={theme.colors.primary} />
            <Text style={[styles.biometricTitle, { color: theme.colors.text }]}>
              Güvenli Giriş
            </Text>
            <Text style={[styles.biometricSubtitle, { color: theme.colors.textSecondary }]}>
              Biyometrik doğrulama ile giriş yapın
            </Text>
          </View>
        )}

        {/* Kilitleme uyarısı */}
        {isLocked && (
          <View style={[styles.lockWarning, { backgroundColor: theme.colors.danger + '15' }]}>
            <MaterialIcons name="lock" size={20} color={theme.colors.danger} />
            <Text style={[styles.lockText, { color: theme.colors.danger }]}>
              {Math.floor(lockTimeRemaining / 60)}:{(lockTimeRemaining % 60).toString().padStart(2, '0')} kaldı
            </Text>
          </View>
        )}

        {/* PIN Dots */}
        {(!showBiometric || isLocked) && (
          <View style={styles.pinDotsContainer}>
            {renderPinDots()}
          </View>
        )}

        {/* Biometrik Ana Buton */}
        {showBiometric && step === 'verify' && !isLocked && (
          <View style={styles.biometricMainContainer}>
            <TouchableOpacity
              style={[styles.biometricMainButton, { backgroundColor: theme.colors.primary }]}
              onPress={handleBiometricAuth}
            >
              <MaterialIcons name="security" size={28} color={theme.colors.white} />
              <Text style={[styles.biometricMainText, { color: theme.colors.white }]}>
                Biyometrik Giriş
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      {/* NumPad */}
      {(!showBiometric || isLocked) && renderNumPad()}

      {/* Alt Aksiyonlar - En alt kısım */}
      <View style={[styles.bottomActions, { paddingBottom: insets.bottom + 16 }]}>
        {/* Biometrik Aktif İken Alt Butonlar */}
        {showBiometric && step === 'verify' && !isLocked && (
          <TouchableOpacity
            style={[styles.bottomButton, { backgroundColor: theme.colors.surface }]}
            onPress={() => setShowBiometric(false)}
          >
            <MaterialIcons name="keyboard" size={18} color={theme.colors.textSecondary} />
            <Text style={[styles.bottomButtonText, { color: theme.colors.textSecondary }]}>
              PIN ile giriş
            </Text>
          </TouchableOpacity>
        )}

        {/* PIN Aktif İken Alt Butonlar */}
        {(!showBiometric || isLocked) && step === 'verify' && !isLocked && (
          <View style={styles.bottomButtonsContainer}>
            {/* Biometrik Buton */}
            {biometricEnabled && biometricAvailable && (
              <TouchableOpacity
                style={[styles.bottomButton, { backgroundColor: theme.colors.primary + '20' }]}
                onPress={() => {
                  setShowBiometric(true);
                  handleBiometricAuth();
                }}
              >
                <MaterialIcons name="security" size={18} color={theme.colors.primary} />
                <Text style={[styles.bottomButtonText, { color: theme.colors.primary }]}>
                  Biyometrik
                </Text>
              </TouchableOpacity>
            )}
            
            {/* PIN Unuttum Buton */}
            <TouchableOpacity
              style={[styles.bottomButton, { backgroundColor: theme.colors.surface }]}
              onPress={() => navigation.navigate('SecurityQuestions', { mode: 'recovery' })}
            >
              <MaterialIcons name="help-outline" size={18} color={theme.colors.textSecondary} />
              <Text style={[styles.bottomButtonText, { color: theme.colors.textSecondary }]}>
                PIN Unuttum
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  backButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 24,
  },
  pinHeaderContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  pinTitle: {
    fontSize: 22,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 8,
  },
  pinSubtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
  biometricHeaderContainer: {
    alignItems: 'center',
    marginBottom: 48,
  },
  biometricTitle: {
    fontSize: 24,
    fontWeight: '600',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  biometricSubtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
  lockWarning: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 24,
    gap: 6,
  },
  lockText: {
    fontSize: 14,
    fontWeight: '500',
  },
  pinDotsContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  biometricMainContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  biometricMainButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 16,
    gap: 10,
    minHeight: 56,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  biometricMainText: {
    fontSize: 18,
    fontWeight: '600',
  },
  bottomActions: {
    paddingHorizontal: 24,
    paddingTop: 8,
    paddingBottom: 16,
    minHeight: 60,
  },
  bottomButtonsContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  bottomButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    gap: 6,
    minHeight: 44,
  },
  bottomButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  pinDotsRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 16,
  },
  pinDot: {
    width: 14,
    height: 14,
    borderRadius: 7,
    borderWidth: 2,
  },
  biometricButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    gap: 12,
    marginBottom: 24,
    minHeight: 60,
  },
  biometricText: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  biometricContainer: {
    width: '100%',
    alignItems: 'center',
    gap: 16,
  },
  switchButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    gap: 8,
    minHeight: 44,
  },
  switchText: {
    fontSize: 14,
    fontWeight: '500',
  },
  pinActionsContainer: {
    width: '100%',
    gap: 12,
    marginTop: 24,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 8,
    minHeight: 44,
  },
  actionText: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
  recoveryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginTop: 16,
    gap: 8,
  },
  recoveryText: {
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
    flex: 1,
  },
  numPad: {
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  numPadRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 12,
    gap: 32,
  },
  numButton: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyButton: {
    width: 64,
    height: 64,
  },
  numButtonText: {
    fontSize: 24,
    fontWeight: '400',
  },
});
