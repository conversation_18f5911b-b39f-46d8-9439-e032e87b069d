/**
 * Vardiya tablosundaki hourly_rate sütununa varsayılan değer ekleyen migrasyon
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<void>}
 */
export const migrateWorkShiftsHourlyRate = async (db) => {
  try {
    // Migrasyon işlemini bir transaction içinde yap
    await db.execAsync('BEGIN TRANSACTION;');

    // work_shifts tablosunu kontrol et
    const workShiftsExists = await db.getFirstAsync(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='work_shifts'
    `);

    if (workShiftsExists) {
      // Sütunları kontrol et
      const columns = await db.getAllAsync(`PRAGMA table_info(work_shifts)`);
      const hourlyRateColumn = columns.find(col => col.name === 'hourly_rate');

      // hourly_rate sütunu varsa ve NOT NULL ise
      if (hourlyRateColumn && hourlyRateColumn.notnull === 1) {
        // Geçici tablo oluştur
        await db.execAsync(`
          CREATE TABLE work_shifts_temp (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date TEXT NOT NULL,
            start_time TEXT NOT NULL,
            end_time TEXT NOT NULL,
            break_duration INTEGER DEFAULT 0,
            is_overtime INTEGER DEFAULT 0,
            overtime_multiplier REAL DEFAULT 1.5,
            is_holiday INTEGER DEFAULT 0,
            holiday_multiplier REAL DEFAULT 2.0,
            hourly_rate REAL DEFAULT 100.0,
            earnings REAL,
            status TEXT DEFAULT 'active',
            notes TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP
          )
        `);

        // Önce sütunları kontrol et
        const hasUpdatedAt = columns.some(col => col.name === 'updated_at');

        // Verileri geçici tabloya kopyala
        if (hasUpdatedAt) {
          await db.execAsync(`
            INSERT INTO work_shifts_temp (
              id, date, start_time, end_time, break_duration, is_overtime,
              overtime_multiplier, is_holiday, holiday_multiplier,
              hourly_rate, earnings, status, notes, created_at, updated_at
            )
            SELECT
              id, date, start_time, end_time, break_duration, is_overtime,
              COALESCE(overtime_multiplier, 1.5), COALESCE(is_holiday, 0), COALESCE(holiday_multiplier, 2.0),
              COALESCE(hourly_rate, 100.0), earnings, status, notes, created_at, updated_at
            FROM work_shifts
          `);
        } else {
          await db.execAsync(`
            INSERT INTO work_shifts_temp (
              id, date, start_time, end_time, break_duration, is_overtime,
              overtime_multiplier, is_holiday, holiday_multiplier,
              hourly_rate, earnings, status, notes, created_at
            )
            SELECT
              id, date, start_time, end_time, break_duration, is_overtime,
              COALESCE(overtime_multiplier, 1.5), COALESCE(is_holiday, 0), COALESCE(holiday_multiplier, 2.0),
              COALESCE(hourly_rate, 100.0), earnings, status, notes, COALESCE(created_at, CURRENT_TIMESTAMP)
            FROM work_shifts
          `);
        }

        // Eski tabloyu sil ve geçici tabloyu yeniden adlandır
        await db.execAsync(`
          DROP TABLE work_shifts;
          ALTER TABLE work_shifts_temp RENAME TO work_shifts;
        `);
      }
    }

    // Transaction'ı tamamla
    await db.execAsync('COMMIT;');
  } catch (error) {
    // Hata durumunda transaction'ı geri al
    await db.execAsync('ROLLBACK;');
    console.error('Vardiya hourly_rate migrasyonu hatası:', error);
    throw error;
  }
};

/**
 * Vardiya tablosundaki hourly_rate sütununa varsayılan değer ekleyen migrasyon (Transaction olmadan)
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<void>}
 */
export const migrateWorkShiftsHourlyRateNoTransaction = async (db) => {
  try {
    // work_shifts tablosunu kontrol et
    const workShiftsExists = await db.getFirstAsync(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='work_shifts'
    `);

    if (workShiftsExists) {
      // Sütunları kontrol et
      const columns = await db.getAllAsync(`PRAGMA table_info(work_shifts)`);
      const hourlyRateColumn = columns.find(col => col.name === 'hourly_rate');

      // hourly_rate sütunu varsa ve NOT NULL ise
      if (hourlyRateColumn && hourlyRateColumn.notnull === 1) {
        // Geçici tablo oluştur
        await db.execAsync(`
          CREATE TABLE work_shifts_temp (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date TEXT NOT NULL,
            start_time TEXT NOT NULL,
            end_time TEXT NOT NULL,
            break_duration INTEGER DEFAULT 0,
            is_overtime INTEGER DEFAULT 0,
            overtime_multiplier REAL DEFAULT 1.5,
            is_holiday INTEGER DEFAULT 0,
            holiday_multiplier REAL DEFAULT 2.0,
            hourly_rate REAL DEFAULT 100.0,
            earnings REAL,
            status TEXT DEFAULT 'active',
            notes TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP
          )
        `);

        // Önce sütunları kontrol et
        const hasUpdatedAt = columns.some(col => col.name === 'updated_at');

        // Verileri geçici tabloya kopyala
        if (hasUpdatedAt) {
          await db.execAsync(`
            INSERT INTO work_shifts_temp (
              id, date, start_time, end_time, break_duration, is_overtime,
              overtime_multiplier, is_holiday, holiday_multiplier,
              hourly_rate, earnings, status, notes, created_at, updated_at
            )
            SELECT
              id, date, start_time, end_time, break_duration, is_overtime,
              COALESCE(overtime_multiplier, 1.5), COALESCE(is_holiday, 0), COALESCE(holiday_multiplier, 2.0),
              COALESCE(hourly_rate, 100.0), earnings, status, notes, created_at, updated_at
            FROM work_shifts
          `);
        } else {
          await db.execAsync(`
            INSERT INTO work_shifts_temp (
              id, date, start_time, end_time, break_duration, is_overtime,
              overtime_multiplier, is_holiday, holiday_multiplier,
              hourly_rate, earnings, status, notes, created_at
            )
            SELECT
              id, date, start_time, end_time, break_duration, is_overtime,
              COALESCE(overtime_multiplier, 1.5), COALESCE(is_holiday, 0), COALESCE(holiday_multiplier, 2.0),
              COALESCE(hourly_rate, 100.0), earnings, status, notes, COALESCE(created_at, CURRENT_TIMESTAMP)
            FROM work_shifts
          `);
        }

        // Eski tabloyu sil ve geçici tabloyu yeniden adlandır
        await db.execAsync(`
          DROP TABLE work_shifts;
          ALTER TABLE work_shifts_temp RENAME TO work_shifts;
        `);
      }
    }
  } catch (error) {
    console.error('Vardiya hourly_rate migrasyonu hatası:', error);
    throw error;
  }
};
