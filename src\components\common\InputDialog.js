import React, { useState } from 'react';
import { 
  Modal, 
  View, 
  TextInput, 
  Text, 
  TouchableOpacity, 
  StyleSheet, 
  ScrollView 
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';

// Uygun ikonlar listesi
const ICONS = [
  'category', 'shopping-cart', 'receipt', 'home', 'directions-car',
  'restaurant', 'local-cafe', 'sports', 'school', 'flight-takeoff',
  'medical-services', 'pets', 'local-grocery-store', 'local-movies',
  'local-bar', 'fitness-center', 'local-hotel', 'child-friendly',
  'account-balance', 'attach-money', 'trending-up', 'trending-down'
];

/**
 * Girdi Diyalog Bileşeni
 * @param {Object} props Bileşen özellikleri
 * @param {boolean} props.visible Modal görünürlüğü
 * @param {string} props.title Modal başlığı
 * @param {string} props.placeholder Input placeholder metni
 * @param {boolean} props.showIconPicker İkon seçici gösterilsin mi
 * @param {Function} props.onSubmit Gönder fonksiyonu
 * @param {Function} props.onCancel İptal fonksiyonu
 * @returns {JSX.Element} InputDialog bileşeni
 */
const InputDialog = ({
  visible,
  title,
  placeholder,
  showIconPicker = false,
  onSubmit,
  onCancel
}) => {
  const [text, setText] = useState('');
  const [selectedIcon, setSelectedIcon] = useState('category');

  /**
   * Diyalog gönderimi
   */
  const handleSubmit = () => {
    if (!text.trim()) return;

    onSubmit({ 
      name: text.trim(), 
      icon: selectedIcon
    });
    
    // Formu sıfırla
    setText('');
    setSelectedIcon('category');
  };

  /**
   * İptal ve sıfırlama
   */
  const handleCancel = () => {
    setText('');
    setSelectedIcon('category');
    onCancel();
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={handleCancel}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>{title}</Text>
            <TouchableOpacity onPress={handleCancel} style={styles.closeButton}>
              <MaterialIcons name="close" size={24} color="#6c757d" />
            </TouchableOpacity>
          </View>
          
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.input}
              value={text}
              onChangeText={setText}
              placeholder={placeholder}
              autoFocus
            />
          </View>
          
          {showIconPicker && (
            <ScrollView style={styles.iconContainer}>
              <Text style={styles.iconTitle}>İkon Seçimi</Text>
              <View style={styles.iconGrid}>
                {ICONS.map(icon => (
                  <TouchableOpacity
                    key={icon}
                    style={[
                      styles.iconItem,
                      selectedIcon === icon && styles.selectedIconItem
                    ]}
                    onPress={() => setSelectedIcon(icon)}
                  >
                    <MaterialIcons
                      name={icon}
                      size={24}
                      color={selectedIcon === icon ? '#fff' : '#6c757d'}
                    />
                  </TouchableOpacity>
                ))}
              </View>
            </ScrollView>
          )}
          
          <View style={styles.buttonContainer}>
            <TouchableOpacity 
              style={[styles.button, styles.cancelButton]}
              onPress={handleCancel}
            >
              <Text style={styles.cancelButtonText}>İptal</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.button, 
                styles.submitButton,
                !text.trim() && styles.disabledButton
              ]}
              onPress={handleSubmit}
              disabled={!text.trim()}
            >
              <Text style={styles.submitButtonText}>Ekle</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center'
  },
  modalContainer: {
    width: '85%',
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef'
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50'
  },
  closeButton: {
    padding: 4
  },
  inputContainer: {
    padding: 16
  },
  input: {
    backgroundColor: '#f8f9fa',
    borderWidth: 1,
    borderColor: '#ced4da',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#2c3e50'
  },
  iconContainer: {
    maxHeight: 200,
    padding: 16,
    paddingTop: 0
  },
  iconTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2c3e50',
    marginBottom: 12
  },
  iconGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start'
  },
  iconItem: {
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
    margin: 4,
    backgroundColor: '#f8f9fa',
    borderWidth: 1,
    borderColor: '#e9ecef'
  },
  selectedIconItem: {
    backgroundColor: '#3498db',
    borderColor: '#3498db'
  },
  buttonContainer: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#e9ecef'
  },
  button: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center'
  },
  cancelButton: {
    backgroundColor: '#f8f9fa',
    borderWidth: 1,
    borderColor: '#e9ecef',
    marginRight: 8
  },
  cancelButtonText: {
    color: '#6c757d',
    fontWeight: '500',
    fontSize: 14
  },
  submitButton: {
    backgroundColor: '#3498db',
    marginLeft: 8
  },
  submitButtonText: {
    color: '#fff',
    fontWeight: '500',
    fontSize: 14
  },
  disabledButton: {
    backgroundColor: '#a0d0f0'
  }
});

export default InputDialog;
