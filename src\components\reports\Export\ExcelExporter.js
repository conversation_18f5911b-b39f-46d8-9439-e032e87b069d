import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import { Platform } from 'react-native';

/**
 * Excel Exporter - Excel dosyaları oluşturur (CSV formatında)
 */
export class ExcelExporter {

  /**
   * Excel dosyası oluşturur (CSV formatında)
   * @param {Object} params - Excel parametreleri
   * @param {Object} params.data - Rapor verisi
   * @param {string} params.title - <PERSON><PERSON> ba<PERSON>
   * @param {string} params.type - Rapor türü
   * @param {Object} params.config - Excel yapılandırması
   * @returns {Promise<Object>} Excel oluşturma sonucu
   */
  static async exportToExcel({ data, title, type, config = {} }) {
    try {
      const csvContent = this.generateCSVContent(data, title, type);
      const fileName = `${title.replace(/[^a-zA-Z0-9]/g, '_')}_${new Date().getTime()}.csv`;
      const fileUri = FileSystem.documentDirectory + fileName;

      await FileSystem.writeAsStringAsync(fileUri, csvContent, {
        encoding: FileSystem.EncodingType.UTF8,
      });

      // Share the CSV file
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri, {
          mimeType: 'text/csv',
          dialogTitle: 'CSV Raporu Paylaş',
        });
      }

      return {
        success: true,
        message: 'CSV dosyası başarıyla oluşturuldu',
        filePath: fileUri
      };
    } catch (error) {
      console.error('CSV export error:', error);
      return {
        success: false,
        message: `CSV oluşturulurken hata oluştu: ${error.message}`,
        filePath: null
      };
    }
  }

  /**
   * CSV içeriği oluşturur
   */
  static generateCSVContent(data, title, type) {
    let csv = `"${title}"\n`;
    csv += `"Rapor Tarihi: ${new Date().toLocaleDateString('tr-TR')}"\n\n`;

    // Summary section
    if (data && data.summary) {
      csv += '"ÖZET"\n';
      csv += `"Toplam Gelir","${data.summary.totalIncome || 0}"\n`;
      csv += `"Toplam Gider","${data.summary.totalExpense || 0}"\n`;
      csv += `"Net Gelir","${data.summary.netIncome || 0}"\n\n`;
    }

    // Transactions section
    if (data && data.transactions && data.transactions.length > 0) {
      csv += '"İŞLEMLER"\n';
      csv += '"Tarih","Açıklama","Kategori","Tutar","Tür"\n';

      data.transactions.forEach(transaction => {
        const date = new Date(transaction.date).toLocaleDateString('tr-TR');
        const description = (transaction.description || '').replace(/"/g, '""');
        const category = (transaction.category_name || '').replace(/"/g, '""');
        const amount = transaction.amount || 0;
        const type = transaction.type === 'income' ? 'Gelir' : 'Gider';

        csv += `"${date}","${description}","${category}","${amount}","${type}"\n`;
      });
    }

    return csv;
  }

  /**
   * CSV formatında veri export eder
   */
  static async exportToCSV({ data, title, type, config = {} }) {
    return this.exportToExcel({ data, title, type, config });
  }
}
