import { Platform } from 'react-native';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';

/**
 * Excel Exporter - Excel dosyaları oluşturur
 * React Native'de gerçek Excel dosyası oluşturmak için CSV formatını kullanır
 * Gelecekte xlsx kütüphanesi entegrasyonu yapılabilir
 */
export class ExcelExporter {
  
  /**
   * Excel dosyası oluşturur (CSV formatında)
   * @param {Object} params - Excel parametreleri
   * @param {Object} params.data - Rapor verisi
   * @param {string} params.title - <PERSON><PERSON> ba<PERSON>lığı
   * @param {string} params.type - Rapor türü
   * @param {Object} params.config - Excel yapılandırması
   * @returns {Promise<Object>} Excel oluşturma sonucu
   */
  static async exportToExcel({ data, title, type, config = {} }) {
    try {
      // Excel yapılandırması
      const excelConfig = {
        format: config.format || 'csv',
        separator: config.separator || ',',
        encoding: config.encoding || 'utf8',
        includeHeaders: config.includeHeaders !== false,
        includeMetadata: config.includeMetadata !== false,
        ...config
      };

      // Excel içeriğini oluştur
      const csvContent = this.generateCSVContent({
        data,
        title,
        type,
        config: excelConfig
      });

      // Dosya adını oluştur
      const fileName = this.generateFileName(title, type, excelConfig.format);
      const fileUri = `${FileSystem.documentDirectory}${fileName}`;

      // Dosyayı yaz
      await FileSystem.writeAsStringAsync(fileUri, csvContent, {
        encoding: excelConfig.encoding
      });

      // Dosyayı paylaş
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri, {
          mimeType: excelConfig.format === 'csv' ? 'text/csv' : 'application/vnd.ms-excel',
          dialogTitle: 'Excel Dosyası Paylaş'
        });
      }

      return {
        success: true,
        uri: fileUri,
        fileName
      };

    } catch (error) {
      console.error('Excel Export Error:', error);
      return {
        success: false,
        error: error.message || 'Excel dosyası oluşturulurken bilinmeyen hata'
      };
    }
  }

  /**
   * CSV içeriğini oluşturur
   * @param {Object} params - CSV parametreleri
   * @returns {string} CSV içeriği
   */
  static generateCSVContent({ data, title, type, config }) {
    const separator = config.separator || ',';
    const includeMetadata = config.includeMetadata !== false;
    const includeHeaders = config.includeHeaders !== false;
    
    let csvContent = '';

    // Metadata ekle
    if (includeMetadata) {
      csvContent += this.generateMetadataSection(title, type, separator);
      csvContent += '\n\n';
    }

    // Rapor türüne göre içerik oluştur
    csvContent += this.generateTypeSpecificContent({ data, type, config, separator });

    return csvContent;
  }

  /**
   * Metadata bölümünü oluşturur
   * @param {string} title - Rapor başlığı
   * @param {string} type - Rapor türü
   * @param {string} separator - Ayırıcı karakter
   * @returns {string} Metadata CSV içeriği
   */
  static generateMetadataSection(title, type, separator) {
    const currentDate = new Date().toLocaleDateString('tr-TR');
    const currentTime = new Date().toLocaleTimeString('tr-TR');
    
    return [
      `Rapor Başlığı${separator}${title}`,
      `Rapor Türü${separator}${type}`,
      `Oluşturulma Tarihi${separator}${currentDate}`,
      `Oluşturulma Saati${separator}${currentTime}`,
      `Uygulama${separator}Maaş Takip Uygulaması`
    ].join('\n');
  }

  /**
   * Rapor türüne özel içerik oluşturur
   * @param {Object} params - İçerik parametreleri
   * @returns {string} Türe özel CSV içeriği
   */
  static generateTypeSpecificContent({ data, type, config, separator }) {
    switch (type) {
      case 'monthly-income-expense':
        return this.generateMonthlyIncomeExpenseCSV(data, separator, config);
      case 'basic-summary':
        return this.generateBasicSummaryCSV(data, separator, config);
      case 'category-distribution':
        return this.generateCategoryDistributionCSV(data, separator, config);
      case 'cash-flow':
        return this.generateCashFlowCSV(data, separator, config);
      case 'budget-vs-actual':
        return this.generateBudgetVsActualCSV(data, separator, config);
      case 'transaction-list':
        return this.generateTransactionListCSV(data, separator, config);
      default:
        return this.generateGenericCSV(data, separator, config);
    }
  }

  /**
   * Aylık gelir-gider CSV'si
   */
  static generateMonthlyIncomeExpenseCSV(data, separator, config) {
    const { summary, monthlyData } = data;
    let csvContent = '';

    // Özet bölümü
    csvContent += 'AYLIK GELİR-GİDER ÖZETİ\n';
    csvContent += `Toplam Gelir${separator}${summary.totalIncome || 0}\n`;
    csvContent += `Toplam Gider${separator}${summary.totalExpense || 0}\n`;
    csvContent += `Net Gelir${separator}${summary.netIncome || 0}\n`;
    csvContent += '\n';

    // Aylık detaylar
    if (monthlyData && monthlyData.length > 0) {
      csvContent += 'AYLIK DETAYLAR\n';
      csvContent += `Ay${separator}Gelir${separator}Gider${separator}Net\n`;
      
      monthlyData.forEach(item => {
        csvContent += [
          item.month,
          item.income || 0,
          item.expense || 0,
          item.net || 0
        ].join(separator) + '\n';
      });
    }

    return csvContent;
  }

  /**
   * Temel özet CSV'si
   */
  static generateBasicSummaryCSV(data, separator, config) {
    const { summary } = data;
    
    let csvContent = 'FİNANSAL ÖZET\n';
    csvContent += `Toplam Gelir${separator}${summary.totalIncome || 0}\n`;
    csvContent += `Toplam Gider${separator}${summary.totalExpense || 0}\n`;
    csvContent += `Ortalama Gelir${separator}${summary.averageIncome || 0}\n`;
    csvContent += `Ortalama Gider${separator}${summary.averageExpense || 0}\n`;
    csvContent += `Net Gelir${separator}${summary.netIncome || 0}\n`;

    return csvContent;
  }

  /**
   * Kategori dağılımı CSV'si
   */
  static generateCategoryDistributionCSV(data, separator, config) {
    const { categories } = data;
    
    if (!categories || categories.length === 0) {
      return 'Kategori verisi bulunamadı.\n';
    }

    let csvContent = 'KATEGORİ DAĞILIMI\n';
    csvContent += `Kategori${separator}Tutar${separator}Yüzde${separator}İşlem Sayısı${separator}Tip\n`;
    
    categories.forEach(category => {
      csvContent += [
        category.name,
        category.amount || 0,
        (category.percentage || 0).toFixed(1) + '%',
        category.count || 0,
        category.type === 'income' ? 'Gelir' : 'Gider'
      ].join(separator) + '\n';
    });

    return csvContent;
  }

  /**
   * Nakit akış CSV'si
   */
  static generateCashFlowCSV(data, separator, config) {
    const { cashFlowData } = data;
    
    if (!cashFlowData || cashFlowData.length === 0) {
      return 'Nakit akış verisi bulunamadı.\n';
    }

    let csvContent = 'NAKİT AKIŞ\n';
    csvContent += `Período${separator}Başlangıç${separator}Gelir${separator}Gider${separator}Bitiş\n`;
    
    cashFlowData.forEach(item => {
      csvContent += [
        item.period,
        item.startBalance || 0,
        item.income || 0,
        item.expense || 0,
        item.endBalance || 0
      ].join(separator) + '\n';
    });

    return csvContent;
  }

  /**
   * Bütçe vs gerçekleşen CSV'si
   */
  static generateBudgetVsActualCSV(data, separator, config) {
    const { budgetData } = data;
    
    if (!budgetData || budgetData.length === 0) {
      return 'Bütçe verisi bulunamadı.\n';
    }

    let csvContent = 'BÜTÇE VS GERÇEKLEŞEN\n';
    csvContent += `Kategori${separator}Bütçe${separator}Gerçekleşen${separator}Fark${separator}Yüzde\n`;
    
    budgetData.forEach(item => {
      csvContent += [
        item.category,
        item.budget || 0,
        item.actual || 0,
        item.difference || 0,
        (item.percentage || 0).toFixed(1) + '%'
      ].join(separator) + '\n';
    });

    return csvContent;
  }

  /**
   * İşlem listesi CSV'si
   */
  static generateTransactionListCSV(data, separator, config) {
    const { transactions } = data;
    
    if (!transactions || transactions.length === 0) {
      return 'İşlem verisi bulunamadı.\n';
    }

    let csvContent = 'İŞLEM LİSTESİ\n';
    csvContent += `Tarih${separator}Açıklama${separator}Kategori${separator}Tutar${separator}Tip\n`;
    
    transactions.forEach(transaction => {
      csvContent += [
        transaction.date,
        this.escapeCSVField(transaction.description, separator),
        transaction.category,
        transaction.amount || 0,
        transaction.type === 'income' ? 'Gelir' : 'Gider'
      ].join(separator) + '\n';
    });

    return csvContent;
  }

  /**
   * Genel CSV içeriği
   */
  static generateGenericCSV(data, separator, config) {
    let csvContent = 'RAPOR VERİSİ\n';
    
    if (typeof data === 'object' && data !== null) {
      // Nesne anahtarlarını CSV'ye dönüştür
      const keys = Object.keys(data);
      csvContent += keys.join(separator) + '\n';
      
      // Değerleri ekle
      const values = keys.map(key => {
        const value = data[key];
        return typeof value === 'object' ? JSON.stringify(value) : value;
      });
      csvContent += values.join(separator) + '\n';
    } else {
      csvContent += `Veri${separator}${data}\n`;
    }

    return csvContent;
  }

  /**
   * CSV alanını escape eder
   * @param {string} field - CSV alanı
   * @param {string} separator - Ayırıcı karakter
   * @returns {string} Escape edilmiş alan
   */
  static escapeCSVField(field, separator) {
    if (!field) return '';
    
    const fieldStr = String(field);
    
    // Ayırıcı karakter, çift tırnak veya yeni satır içeriyorsa tırnak içine al
    if (fieldStr.includes(separator) || fieldStr.includes('"') || fieldStr.includes('\n')) {
      return `"${fieldStr.replace(/"/g, '""')}"`;
    }
    
    return fieldStr;
  }

  /**
   * Dosya adı oluşturur
   * @param {string} title - Rapor başlığı
   * @param {string} type - Rapor türü
   * @param {string} format - Dosya formatı
   * @returns {string} Dosya adı
   */
  static generateFileName(title, type, format = 'csv') {
    const date = new Date();
    const dateStr = date.toISOString().split('T')[0];
    const timeStr = date.toTimeString().split(' ')[0].replace(/:/g, '-');
    
    const cleanTitle = title
      .replace(/[^a-zA-Z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .toLowerCase();
    
    return `${cleanTitle}-${dateStr}-${timeStr}.${format}`;
  }

  /**
   * Gelecekte XLSX desteği için placeholder
   * @param {Object} params - XLSX parametreleri
   * @returns {Promise<Object>} XLSX oluşturma sonucu
   */
  static async exportToXLSX(params) {
    // TODO: xlsx kütüphanesi entegrasyonu
    // import * as XLSX from 'xlsx';
    
    console.warn('XLSX export henüz desteklenmiyor. CSV formatı kullanılacak.');
    return this.exportToExcel({ ...params, config: { ...params.config, format: 'csv' } });
  }
}
