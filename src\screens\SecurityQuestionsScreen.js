import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  StatusBar,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { useAppContext } from '../context/AppContext';
import * as SecureStore from 'expo-secure-store';
import * as Crypto from 'expo-crypto';

// Güvenlik soruları listesi
const SECURITY_QUESTIONS = [
  'İlk evcil hayvanınızın adı neydi?',
  'Doğduğunuz şehrin adı nedir?',
  'Annenizin kızlık soyadı nedir?',
  'İlk öğretmeninizin adı neydi?',
  'En sevdiğiniz yemeğin adı nedir?',
  'İlk arabanızın markası neydi?',
  'İlk iş yerinizin adı neydi?',
  'En iyi arkadaşınızın adı nedir?',
  'Sevdiğiniz ilk filmin adı nedir?',
  'İlk gittiğiniz konser kimindi?',
  'İlk tatil yaptığınız yer neresi?',
  'Favori renginiz nedir?',
  'En sevdiğiniz kitabın adı nedir?',
  'İlk işinizin adı neydi?',
  'Çocukluk kahramanınız kimdi?',
];

/**
 * Güvenlik Soruları Ekranı
 * PIN unutulduğunda güvenlik soruları ile erişim sağlama
 * ve güvenlik soruları kurulumu
 */
export default function SecurityQuestionsScreen({ navigation, route }) {
  const insets = useSafeAreaInsets();
  const { theme } = useAppContext();
  
  // Route parametreleri
  const { mode = 'setup', fromSetup = false } = route.params || {}; // 'setup', 'recovery'
  
  const [step, setStep] = useState(mode === 'setup' ? 'setup' : 'recovery');
  const [selectedQuestions, setSelectedQuestions] = useState([]);
  const [answers, setAnswers] = useState(['']);
  const [customQuestions, setCustomQuestions] = useState(['']);
  const [recoveryAnswers, setRecoveryAnswers] = useState(['', '', '']);
  const [storedQuestions, setStoredQuestions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [questionCount, setQuestionCount] = useState(1); // Default 1 soru

  useEffect(() => {
    if (mode === 'recovery') {
      loadStoredQuestions();
    }
  }, [mode]);

  /**
   * Kayıtlı güvenlik sorularını yükle
   */
  const loadStoredQuestions = async () => {
    try {
      const storedData = await SecureStore.getItemAsync('securityQuestions');
      if (storedData) {
        const questions = JSON.parse(storedData);
        setStoredQuestions(questions);
      } else {
        Alert.alert(
          'Güvenlik Soruları Bulunamadı',
          'Hesabınızda kayıtlı güvenlik sorusu bulunamadı. Lütfen PIN\'inizi hatırlamaya çalışın.',
          [
            { 
              text: 'Tamam', 
              onPress: () => {
                if (navigation.canGoBack()) {
                  navigation.goBack();
                } else {
                  navigation.replace('Main');
                }
              }
            }
          ]
        );
      }
    } catch (error) {
      console.error('Güvenlik soruları yükleme hatası:', error);
      Alert.alert('Hata', 'Güvenlik soruları yüklenirken bir hata oluştu.');
    }
  };

  /**
   * Güvenlik sorusu seç
   */
  const selectQuestion = (questionIndex, position) => {
    const newSelectedQuestions = [...selectedQuestions];
    newSelectedQuestions[position] = questionIndex;
    setSelectedQuestions(newSelectedQuestions);
    
    // Cevap alanını temizle
    const newAnswers = [...answers];
    newAnswers[position] = '';
    setAnswers(newAnswers);
  };

  /**
   * Özel soru güncelle
   */
  const updateCustomQuestion = (question, position) => {
    const newCustomQuestions = [...customQuestions];
    newCustomQuestions[position] = question;
    setCustomQuestions(newCustomQuestions);
  };

  /**
   * Soru ekle
   */
  const addQuestion = () => {
    if (questionCount < 5) {
      setQuestionCount(questionCount + 1);
      setAnswers([...answers, '']);
      setSelectedQuestions([...selectedQuestions, -1]);
      setCustomQuestions([...customQuestions, '']);
    }
  };

  /**
   * Soru çıkar
   */
  const removeQuestion = () => {
    if (questionCount > 1) {
      setQuestionCount(questionCount - 1);
      setAnswers(answers.slice(0, -1));
      setSelectedQuestions(selectedQuestions.slice(0, -1));
      setCustomQuestions(customQuestions.slice(0, -1));
    }
  };

  /**
   * Cevap güncelle
   */
  const updateAnswer = (answer, position) => {
    const newAnswers = [...answers];
    while (newAnswers.length <= position) {
      newAnswers.push('');
    }
    newAnswers[position] = answer;
    setAnswers(newAnswers);
  };

  /**
   * Kurtarma cevabını güncelle
   */
  const updateRecoveryAnswer = (answer, position) => {
    const newRecoveryAnswers = [...recoveryAnswers];
    newRecoveryAnswers[position] = answer;
    setRecoveryAnswers(newRecoveryAnswers);
  };

  /**
   * Güvenlik sorularını kaydet
   */
  const saveSecurityQuestions = async () => {
    try {
      // Tüm alanların dolu olup olmadığını kontrol et
      const hasEmptyAnswers = answers.slice(0, questionCount).some(answer => answer.trim() === '');
      const hasEmptyQuestions = selectedQuestions.slice(0, questionCount).some((q, i) => {
        return q === -1 && customQuestions[i].trim() === '';
      });
      
      if (hasEmptyAnswers || hasEmptyQuestions) {
        Alert.alert('Eksik Bilgi', 'Lütfen tüm güvenlik sorularını ve cevaplarını girin.');
        return;
      }

      setLoading(true);

      // Cevapları şifrele
      const encryptedData = await Promise.all(
        selectedQuestions.slice(0, questionCount).map(async (questionIndex, index) => {
          const hashedAnswer = await Crypto.digestStringAsync(
            Crypto.CryptoDigestAlgorithm.SHA256,
            answers[index].toLowerCase().trim() + 'security-salt'
          );
          
          const questionText = questionIndex === -1 
            ? customQuestions[index] 
            : SECURITY_QUESTIONS[questionIndex];
            
          return {
            question: questionText,
            hashedAnswer
          };
        })
      );

      // Güvenli depolamaya kaydet
      await SecureStore.setItemAsync('securityQuestions', JSON.stringify(encryptedData));
      await SecureStore.setItemAsync('securityQuestionsEnabled', 'true');

      Alert.alert(
        'Güvenlik Soruları Kaydedildi',
        'Güvenlik sorularınız başarıyla kaydedildi. Bu soruları PIN\'inizi unuttuğunuzda kullanabilirsiniz.',
        [
          {
            text: 'Tamam',
            onPress: () => {
              if (fromSetup) {
                navigation.replace('Main');
              } else {
                if (navigation.canGoBack()) {
                  navigation.goBack();
                } else {
                  navigation.replace('Main');
                }
              }
            }
          }
        ]
      );

    } catch (error) {
      console.error('Güvenlik soruları kaydetme hatası:', error);
      Alert.alert('Hata', 'Güvenlik soruları kaydedilirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Güvenlik sorularını doğrula
   */
  const verifySecurityQuestions = async () => {
    try {
      if (recoveryAnswers.some(answer => answer.trim() === '')) {
        Alert.alert('Eksik Bilgi', 'Lütfen tüm soruları cevaplayın.');
        return;
      }

      setLoading(true);

      // Cevapları doğrula
      const isValid = await Promise.all(
        storedQuestions.map(async (questionData, index) => {
          const hashedAnswer = await Crypto.digestStringAsync(
            Crypto.CryptoDigestAlgorithm.SHA256,
            recoveryAnswers[index].toLowerCase().trim() + 'security-salt'
          );
          return hashedAnswer === questionData.hashedAnswer;
        })
      );

      if (isValid.every(valid => valid)) {
        Alert.alert(
          'Doğrulama Başarılı',
          'Güvenlik sorularınız doğrulandı. Yeni PIN oluşturabilirsiniz.',
          [
            {
              text: 'Yeni PIN Oluştur',
              onPress: () => navigation.replace('PinSecurity', { mode: 'setup' })
            }
          ]
        );
      } else {
        Alert.alert(
          'Doğrulama Başarısız',
          'Güvenlik sorularınızın cevapları yanlış. Lütfen tekrar deneyin.',
          [
            { text: 'Tekrar Dene', onPress: () => setRecoveryAnswers(['', '', '']) },
            { text: 'İptal', onPress: () => {
              if (navigation.canGoBack()) {
                navigation.goBack();
              } else {
                navigation.replace('Main');
              }
            } }
          ]
        );
      }

    } catch (error) {
      console.error('Güvenlik soruları doğrulama hatası:', error);
      Alert.alert('Hata', 'Güvenlik soruları doğrulanırken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Güvenlik soruları kurulum ekranı
   */
  const renderSetupScreen = () => (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <StatusBar 
        barStyle={theme.colors.statusBarStyle}
        backgroundColor={theme.colors.statusBarColor}
      />
      
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <MaterialIcons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
          Güvenlik Soruları
        </Text>
        <View style={{ width: 24 }} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.infoContainer}>
          <MaterialIcons name="help-outline" size={48} color={theme.colors.primary} />
          <Text style={[styles.infoTitle, { color: theme.colors.text }]}>
            Güvenlik Soruları Kurulumu
          </Text>
          <Text style={[styles.infoDescription, { color: theme.colors.textSecondary }]}>
            PIN'inizi unuttuğunuzda hesabınıza erişim sağlamak için güvenlik soruları belirleyin.
          </Text>
        </View>

        {/* Soru sayısı kontrol */}
        <View style={[styles.questionCountContainer, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.questionCountLabel, { color: theme.colors.text }]}>
            Güvenlik Sorusu Sayısı: {questionCount}
          </Text>
          <View style={styles.questionCountControls}>
            <TouchableOpacity
              style={[styles.countButton, { backgroundColor: theme.colors.primary, opacity: questionCount > 1 ? 1 : 0.5 }]}
              onPress={removeQuestion}
              disabled={questionCount <= 1}
            >
              <MaterialIcons name="remove" size={20} color={theme.colors.white} />
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.countButton, { backgroundColor: theme.colors.primary, opacity: questionCount < 5 ? 1 : 0.5 }]}
              onPress={addQuestion}
              disabled={questionCount >= 5}
            >
              <MaterialIcons name="add" size={20} color={theme.colors.white} />
            </TouchableOpacity>
          </View>
        </View>

        {Array.from({ length: questionCount }, (_, index) => (
          <View key={index} style={[styles.questionContainer, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.questionLabel, { color: theme.colors.text }]}>
              Güvenlik Sorusu {index + 1}
            </Text>
            
            <TouchableOpacity
              style={[styles.dropdown, { borderColor: theme.colors.border }]}
              onPress={() => {
                const options = SECURITY_QUESTIONS.map((question, qIndex) => ({
                  text: question,
                  onPress: () => selectQuestion(qIndex, index)
                })).concat([
                  { text: 'Özel Soru Gir', onPress: () => selectQuestion(-1, index) },
                  { text: 'İptal', style: 'cancel' }
                ]);
                
                Alert.alert('Güvenlik Sorusu Seç', 'Bir güvenlik sorusu seçin:', options);
              }}
            >
              <Text style={[styles.dropdownText, { color: theme.colors.text }]}>
                {selectedQuestions[index] !== undefined 
                  ? (selectedQuestions[index] === -1 
                      ? 'Özel Soru' 
                      : SECURITY_QUESTIONS[selectedQuestions[index]])
                  : 'Bir soru seçin...'
                }
              </Text>
              <MaterialIcons name="arrow-drop-down" size={24} color={theme.colors.textSecondary} />
            </TouchableOpacity>

            {selectedQuestions[index] === -1 && (
              <TextInput
                style={[styles.answerInput, { 
                  backgroundColor: theme.colors.background,
                  borderColor: theme.colors.border,
                  color: theme.colors.text
                }]}
                placeholder="Özel sorunuzu girin..."
                placeholderTextColor={theme.colors.textSecondary}
                value={customQuestions[index] || ''}
                onChangeText={(text) => updateCustomQuestion(text, index)}
                multiline
                autoCapitalize="sentences"
                autoCorrect={true}
              />
            )}

            {selectedQuestions[index] !== undefined && (
              <TextInput
                style={[styles.answerInput, { 
                  backgroundColor: theme.colors.background,
                  borderColor: theme.colors.border,
                  color: theme.colors.text
                }]}
                placeholder="Cevabınızı girin..."
                placeholderTextColor={theme.colors.textSecondary}
                value={answers[index] || ''}
                onChangeText={(text) => updateAnswer(text, index)}
                secureTextEntry={false}
                autoCapitalize="none"
                autoCorrect={false}
              />
            )}
          </View>
        ))}

        <TouchableOpacity
          style={[styles.saveButton, { 
            backgroundColor: theme.colors.primary,
            opacity: (selectedQuestions.slice(0, questionCount).every(q => q !== undefined) && 
                     answers.slice(0, questionCount).every(answer => answer.trim() !== '')) ? 1 : 0.5
          }]}
          onPress={saveSecurityQuestions}
          disabled={loading || 
                   selectedQuestions.slice(0, questionCount).some(q => q === undefined) ||
                   answers.slice(0, questionCount).some(answer => answer.trim() === '')}
        >
          <Text style={[styles.saveButtonText, { color: theme.colors.white }]}>
            {loading ? 'Kaydediliyor...' : 'Güvenlik Sorularını Kaydet'}
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </KeyboardAvoidingView>
  );

  /**
   * Güvenlik soruları kurtarma ekranı
   */
  const renderRecoveryScreen = () => (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <StatusBar 
        barStyle={theme.colors.statusBarStyle}
        backgroundColor={theme.colors.statusBarColor}
      />
      
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <MaterialIcons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
          Hesap Kurtarma
        </Text>
        <View style={{ width: 24 }} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.infoContainer}>
          <MaterialIcons name="lock-reset" size={48} color={theme.colors.warning} />
          <Text style={[styles.infoTitle, { color: theme.colors.text }]}>
            Güvenlik Soruları ile Kurtarma
          </Text>
          <Text style={[styles.infoDescription, { color: theme.colors.textSecondary }]}>
            PIN'inizi unuttuysanız, güvenlik sorularınızı cevaplayarak hesabınıza erişim sağlayabilirsiniz.
          </Text>
        </View>

        {storedQuestions.map((questionData, index) => (
          <View key={index} style={[styles.questionContainer, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.questionLabel, { color: theme.colors.text }]}>
              Soru {index + 1}
            </Text>
            
            <Text style={[styles.questionText, { color: theme.colors.text }]}>
              {questionData.question}
            </Text>

            <TextInput
              style={[styles.answerInput, { 
                backgroundColor: theme.colors.background,
                borderColor: theme.colors.border,
                color: theme.colors.text
              }]}
              placeholder="Cevabınızı girin..."
              placeholderTextColor={theme.colors.textSecondary}
              value={recoveryAnswers[index]}
              onChangeText={(text) => updateRecoveryAnswer(text, index)}
              secureTextEntry={false}
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>
        ))}

        <TouchableOpacity
          style={[styles.saveButton, { 
            backgroundColor: theme.colors.primary,
            opacity: recoveryAnswers.every(answer => answer.trim() !== '') ? 1 : 0.5
          }]}
          onPress={verifySecurityQuestions}
          disabled={loading || recoveryAnswers.some(answer => answer.trim() === '')}
        >
          <Text style={[styles.saveButtonText, { color: theme.colors.white }]}>
            {loading ? 'Doğrulanıyor...' : 'Güvenlik Sorularını Doğrula'}
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </KeyboardAvoidingView>
  );

  return step === 'setup' ? renderSetupScreen() : renderRecoveryScreen();
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  infoContainer: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  infoTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  infoDescription: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'center',
    marginBottom: 32,
  },
  questionContainer: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  questionCountContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  questionCountLabel: {
    fontSize: 16,
    fontWeight: '600',
  },
  questionCountControls: {
    flexDirection: 'row',
    gap: 8,
  },
  countButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  questionLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  questionText: {
    fontSize: 16,
    marginBottom: 12,
    lineHeight: 24,
  },
  dropdown: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 12,
  },
  dropdownText: {
    fontSize: 16,
    flex: 1,
  },
  answerInput: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderRadius: 8,
    fontSize: 16,
  },
  saveButton: {
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginVertical: 24,
  },
  saveButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
});
