import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
} from 'react-native';
import { useTheme } from '../../../../context/ThemeContext';

/**
 * Veri Kaynağı Seçici - Rapor için veri kaynakları
 * Farklı veri türlerini birleştirme ve yapılandırma
 */
const DataSourceSelector = ({
  dataSources = [],
  onDataSourcesChange,
}) => {
  const { theme } = useTheme();
  const [selectedSources, setSelectedSources] = useState([]);
  const [dateRange, setDateRange] = useState({
    start: '',
    end: '',
    period: 'thisMonth',
  });

  /**
   * Veri kaynağı seçimini değiştir
   */
  const handleSourceToggle = (sourceId) => {
    setSelectedSources(prev => {
      const newSources = prev.includes(sourceId)
        ? prev.filter(id => id !== sourceId)
        : [...prev, sourceId];
      
      onDataSourcesChange?.(newSources);
      return newSources;
    });
  };

  /**
   * Tarih aralığı seçenekleri
   */
  const getDateRangeOptions = () => [
    { id: 'thisMonth', name: 'Bu Ay', icon: '📅' },
    { id: 'lastMonth', name: 'Geçen Ay', icon: '📅' },
    { id: 'thisYear', name: 'Bu Yıl', icon: '📅' },
    { id: 'lastYear', name: 'Geçen Yıl', icon: '📅' },
    { id: 'custom', name: 'Özel Tarih', icon: '🗓️' },
  ];

  return (
    <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            📊 Veri Kaynakları
          </Text>
          <Text style={[styles.sectionSubtitle, { color: theme.TEXT_SECONDARY }]}>
            Raporda kullanılacak veri kaynaklarını seçin
          </Text>
        </View>

        {/* Data Sources */}
        <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
          {dataSources.map((source) => (
            <View
              key={source.id}
              style={[
                styles.sourceCard,
                { backgroundColor: theme.BACKGROUND },
                selectedSources.includes(source.id) && { borderColor: theme.PRIMARY, borderWidth: 2 },
              ]}
            >
              <View style={styles.sourceHeader}>
                <View style={styles.sourceInfo}>
                  <Text style={styles.sourceIcon}>{source.icon}</Text>
                  <View style={styles.sourceDetails}>
                    <Text style={[styles.sourceName, { color: theme.TEXT_PRIMARY }]}>
                      {source.name}
                    </Text>
                    <Text style={[styles.sourceCount, { color: theme.TEXT_SECONDARY }]}>
                      {source.count} kayıt
                    </Text>
                  </View>
                </View>
                
                <Switch
                  value={selectedSources.includes(source.id)}
                  onValueChange={() => handleSourceToggle(source.id)}
                  trackColor={{ false: theme.BORDER, true: theme.PRIMARY }}
                  thumbColor={selectedSources.includes(source.id) ? theme.SURFACE : theme.TEXT_SECONDARY}
                />
              </View>
              
              {/* Source Fields */}
              {selectedSources.includes(source.id) && (
                <View style={styles.sourceFields}>
                  <Text style={[styles.fieldsTitle, { color: theme.TEXT_PRIMARY }]}>
                    Mevcut Alanlar:
                  </Text>
                  <View style={styles.fieldsContainer}>
                    {source.fields?.map((field, index) => (
                      <View
                        key={index}
                        style={[styles.fieldTag, { backgroundColor: theme.PRIMARY }]}
                      >
                        <Text style={[styles.fieldText, { color: theme.SURFACE }]}>
                          {field.label} ({field.type})
                        </Text>
                      </View>
                    ))}
                  </View>
                </View>
              )}
            </View>
          ))}
        </View>

        {/* Date Range */}
        <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            📅 Tarih Aralığı
          </Text>
          <Text style={[styles.sectionSubtitle, { color: theme.TEXT_SECONDARY }]}>
            Rapor için tarih aralığını belirleyin
          </Text>
          
          <View style={styles.dateRangeContainer}>
            {getDateRangeOptions().map((option) => (
              <TouchableOpacity
                key={option.id}
                style={[
                  styles.dateOption,
                  { backgroundColor: theme.BACKGROUND },
                  dateRange.period === option.id && { borderColor: theme.PRIMARY, borderWidth: 2 },
                ]}
                onPress={() => setDateRange(prev => ({ ...prev, period: option.id }))}
              >
                <Text style={styles.dateIcon}>{option.icon}</Text>
                <Text style={[styles.dateName, { color: theme.TEXT_PRIMARY }]}>
                  {option.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Summary */}
        <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            📋 Özet
          </Text>
          
          <View style={styles.summaryContainer}>
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>
                Seçili Veri Kaynakları:
              </Text>
              <Text style={[styles.summaryValue, { color: theme.TEXT_PRIMARY }]}>
                {selectedSources.length} kaynak
              </Text>
            </View>
            
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>
                Tarih Aralığı:
              </Text>
              <Text style={[styles.summaryValue, { color: theme.TEXT_PRIMARY }]}>
                {getDateRangeOptions().find(opt => opt.id === dateRange.period)?.name}
              </Text>
            </View>
            
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>
                Toplam Kayıt (Tahmini):
              </Text>
              <Text style={[styles.summaryValue, { color: theme.TEXT_PRIMARY }]}>
                {dataSources
                  .filter(source => selectedSources.includes(source.id))
                  .reduce((total, source) => total + source.count, 0)
                } kayıt
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  section: {
    margin: 16,
    padding: 16,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: 14,
    opacity: 0.8,
  },
  sourceCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  sourceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  sourceInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  sourceIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  sourceDetails: {
    flex: 1,
  },
  sourceName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  sourceCount: {
    fontSize: 12,
  },
  sourceFields: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  fieldsTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  fieldsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
  },
  fieldTag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  fieldText: {
    fontSize: 10,
    fontWeight: '500',
  },
  dateRangeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 12,
  },
  dateOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'transparent',
    gap: 6,
  },
  dateIcon: {
    fontSize: 16,
  },
  dateName: {
    fontSize: 14,
    fontWeight: '500',
  },
  summaryContainer: {
    gap: 8,
  },
  summaryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 14,
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: '600',
  },
});

export default DataSourceSelector;
