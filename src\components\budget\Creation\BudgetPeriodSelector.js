/**
 * <PERSON>ütçe Dönem Seçici Komponenti
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md - Stage 2, Phase 1
 * 
 * Dönem seçimi (aylık/haftalık/özel)
 * Maksimum 200 satır - Tek sorumluluk prensibi
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../../../context/ThemeContext';

/**
 * Bütçe dönem seçici komponenti
 * @param {Object} props - Component props
 * @param {string} props.selectedPeriod - Seçili dönem türü ('monthly', 'weekly', 'custom')
 * @param {Function} props.onPeriodSelect - Dönem seçim callback fonksiyonu
 * @param {Object} props.theme - <PERSON><PERSON> obje<PERSON> (opsiyonel, context'ten alınır)
 */
const BudgetPeriodSelector = ({ 
  selectedPeriod, 
  onPeriodSelect, 
  theme: propTheme 
}) => {
  const { theme } = useTheme();
  const currentTheme = propTheme || theme;

  // Dönem türleri tanımları
  const periodTypes = [
    {
      id: 'monthly',
      title: 'Aylık Bütçe',
      description: 'Her ay için ayrı bütçe planı',
      icon: 'calendar-month',
      duration: '30 gün',
      examples: ['Ocak 2024', 'Şubat 2024', 'Mart 2024'],
      recommended: true
    },
    {
      id: 'weekly',
      title: 'Haftalık Bütçe',
      description: 'Haftalık bütçe kontrolü',
      icon: 'calendar-week',
      duration: '7 gün',
      examples: ['1-7 Ocak', '8-14 Ocak', '15-21 Ocak'],
      recommended: false
    },
    {
      id: 'custom',
      title: 'Özel Dönem',
      description: 'Kendi tarih aralığınızı belirleyin',
      icon: 'date-range',
      duration: 'Esnek',
      examples: ['Tatil dönemi', 'Proje süresi', 'Özel etkinlik'],
      recommended: false
    }
  ];

  /**
   * Dönem seçim işleyicisi
   * @param {string} periodId - Seçilen dönem türü ID'si
   */
  const handlePeriodSelect = (periodId) => {
    if (onPeriodSelect) {
      onPeriodSelect(periodId);
    }
  };

  /**
   * Seçili durumu kontrol eder
   * @param {string} periodId - Kontrol edilecek dönem ID'si
   * @returns {boolean} Seçili olup olmadığı
   */
  const isSelected = (periodId) => selectedPeriod === periodId;

  return (
    <View style={[styles.container, { backgroundColor: currentTheme.BACKGROUND }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: currentTheme.TEXT_PRIMARY }]}>
          Bütçe Dönemini Seçin
        </Text>
        <Text style={[styles.subtitle, { color: currentTheme.TEXT_SECONDARY }]}>
          Bütçenizin geçerli olacağı süreyi belirleyin
        </Text>
      </View>

      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {periodTypes.map((period) => (
          <TouchableOpacity
            key={period.id}
            style={[
              styles.periodCard,
              {
                backgroundColor: currentTheme.SURFACE,
                borderColor: isSelected(period.id) 
                  ? currentTheme.PRIMARY 
                  : currentTheme.BORDER,
                borderWidth: isSelected(period.id) ? 2 : 1,
              }
            ]}
            onPress={() => handlePeriodSelect(period.id)}
            activeOpacity={0.7}
          >
            {/* Önerilen rozeti */}
            {period.recommended && (
              <View style={[styles.recommendedBadge, { backgroundColor: currentTheme.SUCCESS }]}>
                <Text style={[styles.recommendedText, { color: currentTheme.WHITE }]}>
                  Önerilen
                </Text>
              </View>
            )}

            {/* Header */}
            <View style={styles.cardHeader}>
              <View style={[styles.iconContainer, { backgroundColor: currentTheme.PRIMARY + '20' }]}>
                <MaterialIcons 
                  name={period.icon} 
                  size={28} 
                  color={currentTheme.PRIMARY} 
                />
              </View>
              
              <View style={styles.durationBadge}>
                <Text style={[styles.durationText, { color: currentTheme.TEXT_SECONDARY }]}>
                  {period.duration}
                </Text>
              </View>

              <MaterialIcons
                name={isSelected(period.id) ? 'radio-button-checked' : 'radio-button-unchecked'}
                size={24}
                color={isSelected(period.id) ? currentTheme.PRIMARY : currentTheme.TEXT_SECONDARY}
              />
            </View>

            {/* İçerik */}
            <View style={styles.cardContent}>
              <Text style={[styles.periodTitle, { color: currentTheme.TEXT_PRIMARY }]}>
                {period.title}
              </Text>
              <Text style={[styles.periodDescription, { color: currentTheme.TEXT_SECONDARY }]}>
                {period.description}
              </Text>

              {/* Örnekler */}
              <View style={styles.examplesList}>
                <Text style={[styles.examplesTitle, { color: currentTheme.TEXT_SECONDARY }]}>
                  Örnekler:
                </Text>
                {period.examples.map((example, index) => (
                  <View key={index} style={styles.exampleItem}>
                    <MaterialIcons 
                      name="fiber-manual-record" 
                      size={6} 
                      color={currentTheme.TEXT_SECONDARY} 
                    />
                    <Text style={[styles.exampleText, { color: currentTheme.TEXT_SECONDARY }]}>
                      {example}
                    </Text>
                  </View>
                ))}
              </View>
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
  },
  header: {
    paddingVertical: 16,
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 16,
  },
  periodCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    position: 'relative',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  recommendedBadge: {
    position: 'absolute',
    top: -8,
    right: 16,
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    zIndex: 1,
  },
  recommendedText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  durationBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  durationText: {
    fontSize: 12,
    fontWeight: '500',
  },
  cardContent: {
    flex: 1,
  },
  periodTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  periodDescription: {
    fontSize: 14,
    marginBottom: 12,
    lineHeight: 20,
  },
  examplesList: {
    gap: 4,
  },
  examplesTitle: {
    fontSize: 13,
    fontWeight: '500',
    marginBottom: 4,
  },
  exampleItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingLeft: 8,
  },
  exampleText: {
    fontSize: 12,
    flex: 1,
  },
});

export default BudgetPeriodSelector;
