/**
 * Vardiya Servisi
 *
 * <PERSON><PERSON> servis, vardiya takibi ile ilgili veritabanı işlemlerini yönetir.
 * Vardiya ekleme, g<PERSON><PERSON><PERSON><PERSON>, silme ve sorgulama işlemleri bu servis üzerinden yapılır.
 */

/**
 * Vardiya türlerini getirir
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<Array>} Vardiya türleri
 */
export const getShiftTypes = async (db) => {
  try {
    return await db.getAllAsync(`
      SELECT * FROM work_shift_types
      ORDER BY name ASC
    `);
  } catch (error) {
    console.error('Vardiya türleri getirme hatası:', error);
    throw error;
  }
};

/**
 * Vardiya türü ekler
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} shiftType - Eklenecek vardiya türü
 * @returns {Promise<number>} Eklenen vardiya türünün ID'si
 */
export const addShiftType = async (db, shiftType) => {
  try {
    const result = await db.runAsync(`
      INSERT INTO work_shift_types (
        name, color, start_time, end_time, break_duration, hourly_rate
      )
      VALUES (?, ?, ?, ?, ?, ?)
    `, [
      shiftType.name,
      shiftType.color,
      shiftType.start_time || null,
      shiftType.end_time || null,
      shiftType.break_duration || 0,
      shiftType.hourly_rate || null
    ]);

    return result.lastInsertRowId;
  } catch (error) {
    console.error('Vardiya türü ekleme hatası:', error);
    throw error;
  }
};

/**
 * Vardiya türünü günceller
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} id - Vardiya türü ID'si
 * @param {Object} shiftType - Güncellenecek vardiya türü verileri
 * @returns {Promise<number>} Etkilenen satır sayısı
 */
export const updateShiftType = async (db, id, shiftType) => {
  try {
    const result = await db.runAsync(`
      UPDATE work_shift_types
      SET name = ?,
          color = ?,
          start_time = ?,
          end_time = ?,
          break_duration = ?,
          hourly_rate = ?
      WHERE id = ?
    `, [
      shiftType.name,
      shiftType.color,
      shiftType.start_time || null,
      shiftType.end_time || null,
      shiftType.break_duration || 0,
      shiftType.hourly_rate || null,
      id
    ]);

    return result.changes;
  } catch (error) {
    console.error('Vardiya türü güncelleme hatası:', error);
    throw error;
  }
};

/**
 * Vardiya türünü siler
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} id - Vardiya türü ID'si
 * @returns {Promise<number>} Etkilenen satır sayısı
 */
export const deleteShiftType = async (db, id) => {
  try {
    const result = await db.runAsync(`
      DELETE FROM work_shift_types
      WHERE id = ?
    `, [id]);

    return result.changes;
  } catch (error) {
    console.error('Vardiya türü silme hatası:', error);
    throw error;
  }
};

/**
 * Vardiya planlaması ekler
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} schedule - Eklenecek vardiya planlaması
 * @returns {Promise<number>} Eklenen planlamanın ID'si
 */
export const addShiftSchedule = async (db, schedule) => {
  try {
    const result = await db.runAsync(`
      INSERT INTO work_shift_schedules (
        name, shift_type_id, days_of_week, start_date, end_date, is_active
      )
      VALUES (?, ?, ?, ?, ?, ?)
    `, [
      schedule.name,
      schedule.shift_type_id,
      schedule.days_of_week,
      schedule.start_date,
      schedule.end_date || null,
      schedule.is_active ? 1 : 0
    ]);

    return result.lastInsertRowId;
  } catch (error) {
    console.error('Vardiya planlaması ekleme hatası:', error);
    throw error;
  }
};

/**
 * Vardiya planlamasını günceller
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} id - Planlama ID'si
 * @param {Object} schedule - Güncellenecek planlama verileri
 * @returns {Promise<number>} Etkilenen satır sayısı
 */
export const updateShiftSchedule = async (db, id, schedule) => {
  try {
    const result = await db.runAsync(`
      UPDATE work_shift_schedules
      SET name = ?,
          shift_type_id = ?,
          days_of_week = ?,
          start_date = ?,
          end_date = ?,
          is_active = ?
      WHERE id = ?
    `, [
      schedule.name,
      schedule.shift_type_id,
      schedule.days_of_week,
      schedule.start_date,
      schedule.end_date || null,
      schedule.is_active ? 1 : 0,
      id
    ]);

    return result.changes;
  } catch (error) {
    console.error('Vardiya planlaması güncelleme hatası:', error);
    throw error;
  }
};

/**
 * Vardiya planlamasını siler
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} id - Planlama ID'si
 * @returns {Promise<number>} Etkilenen satır sayısı
 */
export const deleteShiftSchedule = async (db, id) => {
  try {
    const result = await db.runAsync(`
      DELETE FROM work_shift_schedules
      WHERE id = ?
    `, [id]);

    return result.changes;
  } catch (error) {
    console.error('Vardiya planlaması silme hatası:', error);
    throw error;
  }
};

/**
 * Vardiya planlamalarını getirir
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {boolean} activeOnly - Sadece aktif planlamaları getir
 * @returns {Promise<Array>} Vardiya planlamaları
 */
export const getShiftSchedules = async (db, activeOnly = false) => {
  try {
    let query = `
      SELECT s.*, t.name as shift_type_name, t.color as shift_type_color
      FROM work_shift_schedules s
      LEFT JOIN work_shift_types t ON s.shift_type_id = t.id
    `;

    if (activeOnly) {
      query += ' WHERE s.is_active = 1';
    }

    query += ' ORDER BY s.name ASC';

    return await db.getAllAsync(query);
  } catch (error) {
    console.error('Vardiya planlamaları getirme hatası:', error);
    throw error;
  }
};

// workService.js dosyasından alınan vardiya ile ilgili fonksiyonlar
export {
  addWorkShift as addShift,
  updateWorkShift as updateShift,
  deleteWorkShift as deleteShift,
  getWorkShift as getShift,
  getWorkShifts as getShifts,
  getWorkShiftsByDate as getShiftsByDate,
  getWorkShiftsByDateRange as getShiftsByDateRange,
  getActiveWorkShifts as getActiveShifts,
  getWorkShiftsByMonth as getShiftsByMonth,
  getWorkSettings,
  saveWorkSettings,
  updateWorkSettings,
  calculateWorkHours,
  getWorkShiftsByStatus,
  addWorkPayment,
  createShiftsFromSchedules,
  createShiftsFromSpecificSchedules,
  addWorkShiftNotification,
  updateWorkShiftStatus
} from '../../../services/workService';
