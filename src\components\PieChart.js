import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Colors } from '../constants/colors';

/**
 * Pasta grafik bileşeni
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {Array} props.data - Grafik verileri
 * @returns {JSX.Element} Pasta grafik bileşeni
 */
export default function PieChart({ data }) {
  if (!data || data.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>Veri bulunamadı</Text>
      </View>
    );
  }

  const total = data.reduce((sum, item) => sum + item.value, 0);

  return (
    <View style={styles.container}>
      <View style={styles.chartContainer}>
        <View style={styles.pieContainer}>
          {data.map((item, index) => {
            const percentage = (item.value / total) * 100;
            return (
              <View
                key={item.id || index}
                style={[
                  styles.pieSlice,
                  {
                    backgroundColor: item.color || Colors.PRIMARY,
                    width: `${percentage}%`,
                  }
                ]}
              />
            );
          })}
        </View>
      </View>
      
      <View style={styles.legendContainer}>
        {data.map((item, index) => {
          const percentage = ((item.value / total) * 100).toFixed(1);
          return (
            <View key={item.id || index} style={styles.legendItem}>
              <View style={[styles.legendColor, { backgroundColor: item.color || Colors.PRIMARY }]} />
              <View style={styles.legendText}>
                <Text style={styles.legendLabel}>{item.name}</Text>
                <Text style={styles.legendValue}>
                  ₺{item.value.toLocaleString('tr-TR')} ({percentage}%)
                </Text>
              </View>
            </View>
          );
        })}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  emptyContainer: {
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color:'#333333',
  },
  chartContainer: {
    marginBottom: 16,
  },
  pieContainer: {
    flexDirection: 'row',
    height: 40,
    borderRadius: 20,
    overflow: 'hidden',
  },
  pieSlice: {
    height: '100%',
  },
  legendContainer: {
    marginTop: 8,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  legendColor: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 8,
  },
  legendText: {
    flex: 1,
  },
  legendLabel: {
    fontSize: 14,
    color: '#333333',
  },
  legendValue: {
    fontSize: 12,
    color:'#333333',
  },
});
