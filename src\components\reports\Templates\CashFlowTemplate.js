import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  ActivityIndicator 
} from 'react-native';
import { useTheme } from '../../../context/ThemeContext';
import { useDataIntegration } from '../../../context/DataIntegrationContext';
import { ExportManager } from '../Export';

/**
 * Nakit Akış Şablonu
 * Nakit giriş-çıkış takip raporu
 */
const CashFlowTemplate = ({ 
  templateConfig, 
  customParams = {}, 
  onExport, 
  onSave 
}) => {
  const { theme } = useTheme();
  const { reportDataService, isLoading: contextLoading } = useDataIntegration();

  const [loading, setLoading] = useState(true);
  const [reportData, setReportData] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadReportData();
  }, [reportDataService]);

  /**
   * <PERSON><PERSON> verileri<PERSON> y<PERSON>
   */
  const loadReportData = async () => {
    if (!reportDataService) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const params = {
        dateRange: customParams.dateRange || 'current_month',
        includeProjections: customParams.includeProjections || false,
        ...customParams
      };
      
      const data = await reportDataService.getCashFlowData(params);
      setReportData(data);
    } catch (error) {
      setError('Nakit akış verileri yüklenemedi: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Loading state
  if (loading || contextLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.BACKGROUND }]}>
        <ActivityIndicator size="large" color={theme.PRIMARY} />
        <Text style={[styles.loadingText, { color: theme.TEXT_PRIMARY }]}>
          Nakit akış verileri yükleniyor...
        </Text>
      </View>
    );
  }

  // Error state
  if (error) {
    return (
      <View style={[styles.errorContainer, { backgroundColor: theme.BACKGROUND }]}>
        <Text style={[styles.errorText, { color: theme.ERROR }]}>
          {error}
        </Text>
        <TouchableOpacity 
          style={[styles.retryButton, { backgroundColor: theme.PRIMARY }]}
          onPress={loadReportData}
        >
          <Text style={[styles.retryText, { color: theme.SURFACE }]}>
            Tekrar Dene
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Use real data or fallback to empty state
  const cashFlowData = reportData || { 
    openingBalance: 0, 
    closingBalance: 0, 
    netCashFlow: 0, 
    cashInflows: [], 
    cashOutflows: [] 
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      <View style={[styles.header, { backgroundColor: theme.SURFACE }]}>
        <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
          💰 Nakit Akış Raporu
        </Text>
        <Text style={[styles.subtitle, { color: theme.TEXT_SECONDARY }]}>
          Nakit giriş-çıkış takip analizi
        </Text>
      </View>

      {/* Nakit Akış Özeti */}
      <View style={styles.summaryContainer}>
        <View style={[styles.summaryCard, { backgroundColor: theme.INFO }]}>
          <Text style={[styles.summaryLabel, { color: theme.SURFACE }]}>
            Açılış Bakiyesi
          </Text>
          <Text style={[styles.summaryValue, { color: theme.SURFACE }]}>
            ₺{cashFlowData.openingBalance.toLocaleString('tr-TR')}
          </Text>
        </View>

        <View style={[styles.summaryCard, { backgroundColor: theme.SUCCESS }]}>
          <Text style={[styles.summaryLabel, { color: theme.SURFACE }]}>
            Kapanış Bakiyesi
          </Text>
          <Text style={[styles.summaryValue, { color: theme.SURFACE }]}>
            ₺{cashFlowData.closingBalance.toLocaleString('tr-TR')}
          </Text>
        </View>

        <View style={[styles.summaryCard, { backgroundColor: theme.PRIMARY }]}>
          <Text style={[styles.summaryLabel, { color: theme.SURFACE }]}>
            Net Nakit Akış
          </Text>
          <Text style={[styles.summaryValue, { color: theme.SURFACE }]}>
            ₺{cashFlowData.netCashFlow.toLocaleString('tr-TR')}
          </Text>
        </View>
      </View>

      {/* Nakit Girişleri */}
      <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
        <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
          📈 Nakit Girişleri
        </Text>
        {cashFlowData.cashInflows.length === 0 ? (
          <View style={styles.emptyState}>
            <Text style={[styles.emptyText, { color: theme.TEXT_SECONDARY }]}>
              Seçilen dönem için nakit girişi bulunamadı.
            </Text>
          </View>
        ) : (
          cashFlowData.cashInflows.map((item, index) => (
          <View key={index} style={styles.flowRow}>
            <View style={styles.flowInfo}>
              <Text style={[styles.flowDate, { color: theme.TEXT_SECONDARY }]}>
                {formatDate(item.date)}
              </Text>
              <Text style={[styles.flowDescription, { color: theme.TEXT_PRIMARY }]}>
                {item.description}
              </Text>
            </View>
            <Text style={[styles.flowAmount, { color: theme.SUCCESS }]}>
              +₺{item.amount.toLocaleString('tr-TR')}
            </Text>
          </View>
          ))
        )}
      </View>

      {/* Nakit Çıkışları */}
      <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
        <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
          📉 Nakit Çıkışları
        </Text>
        {cashFlowData.cashOutflows.length === 0 ? (
          <View style={styles.emptyState}>
            <Text style={[styles.emptyText, { color: theme.TEXT_SECONDARY }]}>
              Seçilen dönem için nakit çıkışı bulunamadı.
            </Text>
          </View>
        ) : (
          cashFlowData.cashOutflows.map((item, index) => (
          <View key={index} style={styles.flowRow}>
            <View style={styles.flowInfo}>
              <Text style={[styles.flowDate, { color: theme.TEXT_SECONDARY }]}>
                {formatDate(item.date)}
              </Text>
              <Text style={[styles.flowDescription, { color: theme.TEXT_PRIMARY }]}>
                {item.description}
              </Text>
            </View>
            <Text style={[styles.flowAmount, { color: theme.ERROR }]}>
              -₺{item.amount.toLocaleString('tr-TR')}
            </Text>
          </View>
          ))
        )}
      </View>

      {/* Nakit Akış Analizi */}
      <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
        <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
          📊 Nakit Akış Analizi
        </Text>
        
        <View style={styles.analysisRow}>
          <Text style={[styles.analysisLabel, { color: theme.TEXT_SECONDARY }]}>
            Toplam Giriş:
          </Text>
          <Text style={[styles.analysisValue, { color: theme.SUCCESS }]}>
            ₺{mockData.cashInflows.reduce((sum, item) => sum + item.amount, 0).toLocaleString('tr-TR')}
          </Text>
        </View>
        
        <View style={styles.analysisRow}>
          <Text style={[styles.analysisLabel, { color: theme.TEXT_SECONDARY }]}>
            Toplam Çıkış:
          </Text>
          <Text style={[styles.analysisValue, { color: theme.ERROR }]}>
            ₺{mockData.cashOutflows.reduce((sum, item) => sum + item.amount, 0).toLocaleString('tr-TR')}
          </Text>
        </View>
        
        <View style={styles.analysisRow}>
          <Text style={[styles.analysisLabel, { color: theme.TEXT_SECONDARY }]}>
            Net Değişim:
          </Text>
          <Text style={[styles.analysisValue, { color: mockData.netCashFlow >= 0 ? theme.SUCCESS : theme.ERROR }]}>
            ₺{mockData.netCashFlow.toLocaleString('tr-TR')}
          </Text>
        </View>
      </View>

      {/* Aksiyon Butonları */}
      <View style={styles.actionContainer}>
        <ExportManager 
          reportData={cashFlowData}
          reportTitle="Nakit Akış Raporu"
          reportType="cash_flow"
          buttonStyle={styles.exportButton}
          buttonTextStyle={styles.exportButtonText}
          theme={theme}
        />
        
        <TouchableOpacity 
          style={[styles.actionButton, { backgroundColor: theme.SUCCESS }]}
          onPress={() => onSave && onSave()}
        >
          <Text style={[styles.actionButtonText, { color: theme.SURFACE }]}>
            💾 Kaydet
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
  },
  summaryContainer: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
  },
  summaryCard: {
    flex: 1,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 4,
    textAlign: 'center',
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  section: {
    margin: 16,
    padding: 16,
    borderRadius: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  flowRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  flowInfo: {
    flex: 1,
  },
  flowDate: {
    fontSize: 12,
    marginBottom: 2,
  },
  flowDescription: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  flowAmount: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  analysisRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 4,
  },
  analysisLabel: {
    fontSize: 14,
  },
  analysisValue: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  actionContainer: {
    flexDirection: 'column',
    padding: 16,
    gap: 12,
  },
  exportButton: {
    marginBottom: 8,
  },
  exportButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  actionButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  // Loading and error states
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryText: {
    fontSize: 14,
    fontWeight: '600',
  },
  emptyState: {
    padding: 20,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
  },
});

export default CashFlowTemplate;
