import React, { memo, useState, useCallback } from 'react';
import { Image, View, ActivityIndicator, StyleSheet } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';

/**
 * Optimized Image Component
 * Lazy loading ve caching ile optimize edilmiş resim bileşeni
 */
const OptimizedImage = memo(({
  source,
  style,
  resizeMode = 'cover',
  placeholder = true,
  fallbackIcon = 'image',
  onLoad,
  onError,
  ...props
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  const handleLoad = useCallback((event) => {
    setLoading(false);
    if (onLoad) {
      onLoad(event);
    }
  }, [onLoad]);

  const handleError = useCallback((event) => {
    setLoading(false);
    setError(true);
    if (onError) {
      onError(event);
    }
  }, [onError]);

  const handleLoadStart = useCallback(() => {
    setLoading(true);
    setError(false);
  }, []);

  if (error) {
    return (
      <View style={[styles.container, style, styles.errorContainer]}>
        <MaterialIcons 
          name={fallbackIcon} 
          size={24} 
          color="#ccc" 
        />
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      <Image
        {...props}
        source={source}
        style={[styles.image, style]}
        resizeMode={resizeMode}
        onLoad={handleLoad}
        onError={handleError}
        onLoadStart={handleLoadStart}
        // Performance optimizations
        fadeDuration={200}
        progressiveRenderingEnabled={true}
        // Memory optimizations
        blurRadius={loading ? 1 : 0}
      />
      
      {loading && placeholder && (
        <View style={[styles.placeholder, style]}>
          <ActivityIndicator size="small" color="#ccc" />
        </View>
      )}
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  placeholder: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  errorContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
});

OptimizedImage.displayName = 'OptimizedImage';

export default OptimizedImage;
