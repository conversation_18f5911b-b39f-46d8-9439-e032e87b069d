/**
 * Bütçe Componentleri Test Dosyası
 * Stage 2 implementasyonu test ve doğrulama
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Bütçe Componentleri Test Başlatılıyor...\n');

// Test edilecek componentler
const components = [
  // Phase 1 - Temel Seçim Componentleri
  'src/components/budget/Creation/BudgetTypeSelector.js',
  'src/components/budget/Creation/BudgetTypeCard.js',
  'src/components/budget/Creation/BudgetPeriodSelector.js',
  'src/components/budget/Creation/CustomDatePicker.js',
  'src/components/budget/Creation/BudgetLimitInput.js',
  'src/components/budget/Creation/CurrencySelector.js',
  
  // Phase 2 - Kategori Yönetimi
  'src/components/budget/Creation/CategorySelector.js',
  'src/components/budget/Creation/CategoryBudgetInput.js',
  'src/components/budget/Creation/CategoryList.js',
  
  // Phase 3 - Akıllı Öneriler
  'src/components/budget/Creation/SmartSuggestions.js',
  'src/components/budget/Creation/HistoricalAnalysis.js',
  'src/components/budget/Creation/BudgetRecommendations.js',
  
  // Phase 4 - Wizard ve Navigation
  'src/components/budget/Creation/WizardHeader.js',
  'src/components/budget/Creation/WizardNavigation.js',
  'src/screens/budget/BudgetCreateScreen.js',

  // Stage 3 Components - Real-time Tracking
  'src/components/budget/Tracking/BudgetStatusCard.js',
  'src/components/budget/Tracking/RemainingAmountDisplay.js',
  'src/components/budget/Tracking/SpendingVelocityIndicator.js',
  'src/components/budget/Tracking/BudgetProgressBar.js',
  'src/components/budget/Tracking/CategoryProgressList.js',
  'src/components/budget/Tracking/DailySpendingChart.js',
  'src/components/budget/Tracking/BudgetAlertCard.js',
  'src/components/budget/Tracking/OverspendingWarning.js',
  'src/components/budget/Tracking/LowBudgetNotification.js',
  'src/screens/budget/BudgetDetailScreen.js'
];

// Test 1: Dosya varlığı kontrolü
console.log('📁 Dosya Varlığı Testi:');
let allFilesExist = true;

components.forEach(file => {
  const filePath = path.join(__dirname, file);
  const exists = fs.existsSync(filePath);
  console.log(`  ${exists ? '✅' : '❌'} ${file}`);
  if (!exists) allFilesExist = false;
});

console.log(`\n📊 Dosya Varlığı Sonucu: ${allFilesExist ? '✅ Tüm dosyalar mevcut' : '❌ Eksik dosyalar var'}\n`);

// Test 2: Import/Export kontrolü
console.log('🔍 Import/Export Kontrolü:');

const importTests = [
  {
    file: 'src/components/budget/Creation/index.js',
    expectedExports: [
      'BudgetTypeSelector',
      'BudgetPeriodSelector', 
      'BudgetLimitInput',
      'CategorySelector',
      'SmartSuggestions',
      'WizardHeader'
    ]
  }
];

let allImportsValid = true;

importTests.forEach(test => {
  try {
    const filePath = path.join(__dirname, test.file);
    const content = fs.readFileSync(filePath, 'utf8');
    
    console.log(`  📄 ${test.file}:`);
    
    test.expectedExports.forEach(exportName => {
      const found = content.includes(`export { default as ${exportName} }`);
      console.log(`    ${found ? '✅' : '❌'} ${exportName}`);
      if (!found) allImportsValid = false;
    });
    
  } catch (error) {
    console.log(`    ❌ Dosya okuma hatası: ${error.message}`);
    allImportsValid = false;
  }
});

console.log(`\n📊 Import/Export Sonucu: ${allImportsValid ? '✅ Tüm export\'lar geçerli' : '❌ Export hataları var'}\n`);

// Test 3: Dependency kontrolü
console.log('📦 Dependency Kontrolü:');

const dependencies = [
  '@react-native-community/datetimepicker',
  'expo-sqlite',
  '@expo/vector-icons'
];

let allDepsValid = true;

try {
  const packageJsonPath = path.join(__dirname, 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  const allDependencies = {
    ...packageJson.dependencies,
    ...packageJson.devDependencies
  };

  dependencies.forEach(dep => {
    const found = allDependencies[dep];
    console.log(`  ${found ? '✅' : '❌'} ${dep} ${found ? `(${found})` : ''}`);
    if (!found) allDepsValid = false;
  });

} catch (error) {
  console.log(`  ❌ package.json okuma hatası: ${error.message}`);
  allDepsValid = false;
}

console.log(`\n📊 Dependency Sonucu: ${allDepsValid ? '✅ Tüm dependency\'ler mevcut' : '❌ Eksik dependency\'ler var'}\n`);

// Test 4: Navigation entegrasyonu
console.log('🧭 Navigation Entegrasyonu:');

try {
  const appJsPath = path.join(__dirname, 'App.js');
  const appJsContent = fs.readFileSync(appJsPath, 'utf8');
  
  const navigationChecks = [
    { name: 'BudgetCreateScreen import', check: 'BudgetCreateScreen' },
    { name: 'BudgetCreate route', check: 'name="BudgetCreate"' },
    { name: 'BudgetsScreenEnhanced import', check: 'BudgetsScreenEnhanced' }
  ];
  
  let allNavigationValid = true;
  
  navigationChecks.forEach(check => {
    const found = appJsContent.includes(check.check);
    console.log(`  ${found ? '✅' : '❌'} ${check.name}`);
    if (!found) allNavigationValid = false;
  });
  
  console.log(`\n📊 Navigation Sonucu: ${allNavigationValid ? '✅ Navigation entegrasyonu tamamlandı' : '❌ Navigation hataları var'}\n`);
  
} catch (error) {
  console.log(`  ❌ App.js kontrolü hatası: ${error.message}\n`);
}

// Test 5: Component yapısı kontrolü
console.log('🏗️ Component Yapısı Kontrolü:');

const structureTests = [
  {
    file: 'src/screens/budget/BudgetCreateScreen.js',
    requiredElements: ['useState', 'useRef', 'Animated', 'steps', 'renderCurrentStep']
  },
  {
    file: 'src/components/budget/Creation/BudgetTypeSelector.js',
    requiredElements: ['budgetTypes', 'handleTypeSelect', 'MaterialIcons']
  }
];

let allStructureValid = true;

structureTests.forEach(test => {
  try {
    const filePath = path.join(__dirname, test.file);
    const content = fs.readFileSync(filePath, 'utf8');
    
    console.log(`  📄 ${test.file}:`);
    
    test.requiredElements.forEach(element => {
      const found = content.includes(element);
      console.log(`    ${found ? '✅' : '❌'} ${element}`);
      if (!found) allStructureValid = false;
    });
    
  } catch (error) {
    console.log(`    ❌ Dosya okuma hatası: ${error.message}`);
    allStructureValid = false;
  }
});

console.log(`\n📊 Component Yapısı Sonucu: ${allStructureValid ? '✅ Tüm yapılar geçerli' : '❌ Yapı hataları var'}\n`);

// Genel sonuç
console.log('🎯 GENEL TEST SONUCU:');
console.log('='.repeat(50));

const overallResult = allFilesExist && allImportsValid && allDepsValid && allStructureValid;

if (overallResult) {
  console.log('✅ TÜM TESTLER BAŞARILI!');
  console.log('🎉 Stage 2 - Bütçe Oluşturma ve Yönetimi implementasyonu tamamlandı.');
  console.log('📋 Hazır olan özellikler:');
  console.log('   • 15+ React Native component');
  console.log('   • Wizard-based budget creation flow');
  console.log('   • Multi-currency support (TRY/USD/EUR)');
  console.log('   • Smart suggestions ve historical analysis');
  console.log('   • Category management integration');
  console.log('   • Theme system compatibility');
  console.log('   • Navigation integration');
  console.log('   • Turkish language interface');
  console.log('\n🚀 Sonraki adımlar:');
  console.log('   1. Expo Go\'da uygulamayı test edin');
  console.log('   2. Budget creation wizard\'ını deneyin');
  console.log('   3. Stage 3 implementasyonuna geçin');
} else {
  console.log('❌ BAZI TESTLER BAŞARISIZ!');
  console.log('🔧 Lütfen yukarıdaki hataları kontrol edin ve düzeltin.');
}

console.log('='.repeat(50));

process.exit(overallResult ? 0 : 1);
