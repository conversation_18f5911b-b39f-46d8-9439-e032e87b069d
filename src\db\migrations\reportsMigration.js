/**
 * Reports Database Migration
 * Rapor sistemi için gerekli tüm tabloları oluşturur
 */

/**
 * Rapor sistemini için database tablolarını oluşturur
 * @param {Object} db - SQLite database instance
 */
export const createReportsSchema = async (db) => {
  try {
    // <PERSON>or Ş<PERSON>lonlar<PERSON>
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS report_templates (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        category TEXT NOT NULL DEFAULT 'general',
        config TEXT NOT NULL,              -- JSON yapılandırma
        is_system INTEGER DEFAULT 0,       -- Sistem şablonu mu
        is_interactive INTEGER DEFAULT 0,  -- İnteraktif mi
        thumbnail TEXT,                    -- <PERSON><PERSON><PERSON> önizleme resmi
        tags TEXT,                         -- <PERSON><PERSON><PERSON><PERSON> (JSON array)
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Kay<PERSON>ilen Raporlar
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS saved_reports (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        template_id INTEGER,
        name TEXT NOT NULL,
        description TEXT,
        config TEXT NOT NULL,              -- JSON yapılandırma
        data_sources TEXT,                 -- Veri kaynakları (JSON array)
        filters TEXT,                      -- Filtreler (JSON)
        layout TEXT,                       -- Layout yapılandırması (JSON)
        is_favorite INTEGER DEFAULT 0,     -- Favori mi
        view_count INTEGER DEFAULT 0,      -- Görüntülenme sayısı
        last_viewed DATETIME,              -- Son görüntülenme
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (template_id) REFERENCES report_templates (id)
      );
    `);

    // Rapor Zamanlamaları
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS report_schedules (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        report_id INTEGER,
        schedule_type TEXT NOT NULL,       -- daily, weekly, monthly, custom
        schedule_config TEXT,              -- JSON yapılandırma
        export_format TEXT,                -- PDF, Excel, CSV, etc.
        recipients TEXT,                   -- E-posta alıcıları (JSON array)
        last_run DATETIME,                 -- Son çalıştırma
        next_run DATETIME,                 -- Sonraki çalıştırma
        is_active INTEGER DEFAULT 1,       -- Aktif mi
        failure_count INTEGER DEFAULT 0,   -- Hata sayısı
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (report_id) REFERENCES saved_reports (id)
      );
    `);

    // Rapor Paylaşımları
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS report_shares (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        report_id INTEGER,
        share_type TEXT NOT NULL,          -- link, email, qr_code
        share_token TEXT UNIQUE,           -- Paylaşım tokeni
        expires_at DATETIME,               -- Geçerlilik süresi
        password TEXT,                     -- Şifre (opsiyonel)
        view_count INTEGER DEFAULT 0,      -- Görüntülenme sayısı
        max_views INTEGER,                 -- Maksimum görüntülenme
        is_active INTEGER DEFAULT 1,       -- Aktif mi
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (report_id) REFERENCES saved_reports (id)
      );
    `);

    // Rapor Etiketleri
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS report_tags (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        color TEXT,                        -- Etiket rengi
        description TEXT,
        usage_count INTEGER DEFAULT 0,     -- Kullanım sayısı
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Rapor-Etiket İlişkileri
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS report_tag_relations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        report_id INTEGER,
        tag_id INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (report_id) REFERENCES saved_reports (id),
        FOREIGN KEY (tag_id) REFERENCES report_tags (id),
        UNIQUE(report_id, tag_id)
      );
    `);

    // Rapor Kullanım İstatistikleri
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS report_usage_stats (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        report_id INTEGER,
        action_type TEXT NOT NULL,         -- view, export, share, edit
        action_details TEXT,               -- JSON detaylar
        duration INTEGER,                  -- İşlem süresi (ms)
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (report_id) REFERENCES saved_reports (id)
      );
    `);

    // Rapor Verileri Cache (performans için)
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS report_data_cache (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        report_id INTEGER,
        cache_key TEXT NOT NULL,           -- Unique cache key
        cache_data TEXT NOT NULL,          -- JSON cached data
        expires_at DATETIME,               -- Cache geçerlilik süresi
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (report_id) REFERENCES saved_reports (id),
        UNIQUE(report_id, cache_key)
      );
    `);

    // Indeksler (Performance için)
    await db.execAsync(`
      CREATE INDEX IF NOT EXISTS idx_report_templates_category ON report_templates(category);
    `);
    await db.execAsync(`
      CREATE INDEX IF NOT EXISTS idx_saved_reports_template ON saved_reports(template_id);
    `);
    await db.execAsync(`
      CREATE INDEX IF NOT EXISTS idx_saved_reports_favorite ON saved_reports(is_favorite);
    `);
    await db.execAsync(`
      CREATE INDEX IF NOT EXISTS idx_saved_reports_last_viewed ON saved_reports(last_viewed);
    `);
    await db.execAsync(`
      CREATE INDEX IF NOT EXISTS idx_report_schedules_next_run ON report_schedules(next_run);
    `);
    await db.execAsync(`
      CREATE INDEX IF NOT EXISTS idx_report_shares_token ON report_shares(share_token);
    `);
    await db.execAsync(`
      CREATE INDEX IF NOT EXISTS idx_report_usage_stats_action ON report_usage_stats(action_type);
    `);
    await db.execAsync(`
      CREATE INDEX IF NOT EXISTS idx_report_data_cache_key ON report_data_cache(cache_key);
    `);
    await db.execAsync(`
      CREATE INDEX IF NOT EXISTS idx_report_data_cache_expires ON report_data_cache(expires_at);
    `);

    console.log('📊 Rapor tablolarayı başarıyla oluşturuldu');
  } catch (error) {
    console.error('❌ Rapor tabloları oluşturulurken hata:', error);
    throw error;
  }
};

/**
 * Başlangıç rapor şablonlarını ekler
 * @param {Object} db - SQLite database instance
 */
export const seedReportTemplates = async (db) => {
  try {
    // Sistem şablonlarını ekle
    const systemTemplates = [
      {
        name: 'Aylık Gelir-Gider Raporu',
        description: 'Aylık gelir ve gider dağılımının detaylı analizi',
        category: 'financial',
        config: JSON.stringify({
          type: 'monthly_income_expense',
          charts: ['pie', 'bar'],
          tables: ['summary', 'detailed']
        }),
        is_system: 1,
        is_interactive: 1,
        tags: JSON.stringify(['aylık', 'gelir', 'gider', 'temel'])
      },
      {
        name: 'Kategori Dağılım Analizi',
        description: 'Harcama kategorilerinin detaylı dağılım raporu',
        category: 'category',
        config: JSON.stringify({
          type: 'category_distribution',
          charts: ['pie', 'treemap'],
          tables: ['category_breakdown']
        }),
        is_system: 1,
        is_interactive: 1,
        tags: JSON.stringify(['kategori', 'dağılım', 'analiz'])
      },
      {
        name: 'Nakit Akış Raporu',
        description: 'Aylık nakit akış trendleri ve projeksiyonlar',
        category: 'cashflow',
        config: JSON.stringify({
          type: 'cash_flow',
          charts: ['line', 'waterfall'],
          tables: ['monthly_flow']
        }),
        is_system: 1,
        is_interactive: 1,
        tags: JSON.stringify(['nakit', 'akış', 'trend'])
      },
      {
        name: 'Bütçe vs Gerçekleşen',
        description: 'Planlanan bütçe ile gerçekleşen harcamaların karşılaştırması',
        category: 'budget',
        config: JSON.stringify({
          type: 'budget_vs_actual',
          charts: ['bar', 'gauge'],
          tables: ['comparison']
        }),
        is_system: 1,
        is_interactive: 1,
        tags: JSON.stringify(['bütçe', 'karşılaştırma', 'planlama'])
      },
      {
        name: 'Mesai Gelir Analizi',
        description: 'Mesai çalışmalarından elde edilen gelir analizi',
        category: 'income',
        config: JSON.stringify({
          type: 'overtime_income',
          charts: ['line', 'bar'],
          tables: ['overtime_details']
        }),
        is_system: 1,
        is_interactive: 1,
        tags: JSON.stringify(['mesai', 'gelir', 'analiz'])
      },
      {
        name: 'Düzenli Gelir Takibi',
        description: 'Maaş ve düzenli gelirlerin takip raporu',
        category: 'income',
        config: JSON.stringify({
          type: 'regular_income_tracking',
          charts: ['line'],
          tables: ['income_summary']
        }),
        is_system: 1,
        is_interactive: 1,
        tags: JSON.stringify(['maaş', 'düzenli', 'gelir'])
      },
      {
        name: 'Temel Özet Raporu',
        description: 'Genel finansal durumun özet raporu',
        category: 'summary',
        config: JSON.stringify({
          type: 'basic_summary',
          charts: ['pie', 'bar'],
          tables: ['summary']
        }),
        is_system: 1,
        is_interactive: 1,
        tags: JSON.stringify(['özet', 'temel', 'genel'])
      }
    ];

    // Her şablon için INSERT OR REPLACE
    for (const template of systemTemplates) {
      await db.runAsync(
        `INSERT OR REPLACE INTO report_templates 
         (name, description, category, config, is_system, is_interactive, tags) 
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [
          template.name,
          template.description,
          template.category,
          template.config,
          template.is_system,
          template.is_interactive,
          template.tags
        ]
      );
    }

    console.log('📊 Sistem rapor şablonları başarıyla eklendi');
  } catch (error) {
    console.error('❌ Sistem rapor şablonları eklenirken hata:', error);
    throw error;
  }
};

/**
 * Başlangıç etiketlerini ekler
 * @param {Object} db - SQLite database instance
 */
export const seedReportTags = async (db) => {
  try {
    const tags = [
      { name: 'Finansal', color: '#2196F3', description: 'Finansal raporlar' },
      { name: 'Kategori', color: '#4CAF50', description: 'Kategori bazlı analizler' },
      { name: 'Gelir', color: '#FF9800', description: 'Gelir analiz raporları' },
      { name: 'Gider', color: '#F44336', description: 'Gider analiz raporları' },
      { name: 'Bütçe', color: '#9C27B0', description: 'Bütçe planlama raporları' },
      { name: 'Trend', color: '#607D8B', description: 'Trend analiz raporları' },
      { name: 'Özet', color: '#795548', description: 'Özet raporlar' },
      { name: 'Detay', color: '#009688', description: 'Detaylı analiz raporları' }
    ];

    for (const tag of tags) {
      await db.runAsync(
        `INSERT OR REPLACE INTO report_tags (name, color, description) VALUES (?, ?, ?)`,
        [tag.name, tag.color, tag.description]
      );
    }

    console.log('🏷️ Başlangıç etiketleri başarıyla eklendi');
  } catch (error) {
    console.error('❌ Başlangıç etiketleri eklenirken hata:', error);
    throw error;
  }
};
