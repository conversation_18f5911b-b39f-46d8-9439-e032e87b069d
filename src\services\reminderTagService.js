/**
 * Hatırlatıcı etiketleri için servis fonksiyonları
 */

/**
 * Tüm etiketleri getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<Array>} Etiketler listesi
 */
export const getAllTags = async (db) => {
  try {
    const tags = await db.getAllAsync(`
      SELECT * FROM reminder_tags
      ORDER BY name ASC
    `);

    return tags;
  } catch (error) {
    console.error('Etiketleri getirme hatası:', error);
    throw error;
  }
};

/**
 * Belirli bir etiketi getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} tagId - Etiket ID
 * @returns {Promise<Object|null>} Etiket
 */
export const getTagById = async (db, tagId) => {
  try {
    const tag = await db.getFirstAsync(`
      SELECT * FROM reminder_tags
      WHERE id = ?
    `, [tagId]);

    return tag;
  } catch (error) {
    console.error('Etiket getirme hatası:', error);
    throw error;
  }
};

/**
 * Yeni bir etiket ekler
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} tag - Etiket verileri
 * @param {string} tag.name - Etiket adı
 * @param {string} tag.color - Etiket rengi
 * @returns {Promise<number>} Eklenen etiketin ID'si
 */
export const addTag = async (db, tag) => {
  try {
    const result = await db.runAsync(`
      INSERT INTO reminder_tags (name, color)
      VALUES (?, ?)
    `, [
      tag.name,
      tag.color || '#3498db'
    ]);

    return result.lastInsertRowId;
  } catch (error) {
    console.error('Etiket ekleme hatası:', error);
    throw error;
  }
};

/**
 * Bir etiketi günceller
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} tagId - Etiket ID
 * @param {Object} tag - Güncellenecek etiket verileri
 * @returns {Promise<boolean>} Başarılı ise true
 */
export const updateTag = async (db, tagId, tag) => {
  try {
    await db.runAsync(`
      UPDATE reminder_tags
      SET name = ?, color = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [
      tag.name,
      tag.color || '#3498db',
      tagId
    ]);

    return true;
  } catch (error) {
    console.error('Etiket güncelleme hatası:', error);
    throw error;
  }
};

/**
 * Bir etiketi siler
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} tagId - Etiket ID
 * @returns {Promise<boolean>} Başarılı ise true
 */
export const deleteTag = async (db, tagId) => {
  try {
    // Önce etiket ilişkilerini sil
    await db.runAsync(`
      DELETE FROM reminder_tag_relations
      WHERE tag_id = ?
    `, [tagId]);

    // Sonra etiketi sil
    await db.runAsync(`
      DELETE FROM reminder_tags
      WHERE id = ?
    `, [tagId]);

    return true;
  } catch (error) {
    console.error('Etiket silme hatası:', error);
    throw error;
  }
};

/**
 * Bir hatırlatıcının etiketlerini getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} reminderId - Hatırlatıcı ID
 * @returns {Promise<Array>} Etiketler listesi
 */
export const getTagsByReminderId = async (db, reminderId) => {
  try {
    const tags = await db.getAllAsync(`
      SELECT t.*
      FROM reminder_tags t
      JOIN reminder_tag_relations r ON t.id = r.tag_id
      WHERE r.reminder_id = ?
      ORDER BY t.name ASC
    `, [reminderId]);

    return tags;
  } catch (error) {
    console.error('Hatırlatıcı etiketlerini getirme hatası:', error);
    throw error;
  }
};

/**
 * Bir etikete sahip tüm hatırlatıcıları getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} tagId - Etiket ID
 * @param {Object} options - Sorgu seçenekleri
 * @returns {Promise<Array>} Hatırlatıcılar listesi
 */
export const getRemindersByTagId = async (db, tagId, options = {}) => {
  try {
    const {
      status,
      sortBy = 'scheduled_at',
      sortOrder = 'asc',
      limit = 100,
      offset = 0
    } = options;

    // Sorgu parametrelerini hazırla
    const queryParams = [tagId];
    let whereClause = "r.tag_id = ? AND n.related_type = 'user_reminder'";

    if (status) {
      whereClause += ' AND n.status = ?';
      queryParams.push(status);
    }

    // Sorguyu oluştur
    const query = `
      SELECT n.*, c.name as category_name, c.color as category_color, g.name as group_name, g.color as group_color
      FROM notifications n
      JOIN reminder_tag_relations r ON n.id = r.reminder_id
      LEFT JOIN categories c ON n.category_id = c.id
      LEFT JOIN reminder_groups g ON n.group_id = g.id
      WHERE ${whereClause}
      ORDER BY n.${sortBy} ${sortOrder}
      LIMIT ? OFFSET ?
    `;

    queryParams.push(limit, offset);

    // Sorguyu çalıştır
    const reminders = await db.getAllAsync(query, queryParams);

    // Sonuçları işle
    return reminders.map(reminder => {
      // JSON alanlarını parse et
      const repeatDays = reminder.repeat_days ? JSON.parse(reminder.repeat_days) : null;
      const repeatMonths = reminder.repeat_months ? JSON.parse(reminder.repeat_months) : null;
      const data = reminder.data ? JSON.parse(reminder.data) : {};

      return {
        ...reminder,
        repeat_days: repeatDays,
        repeat_months: repeatMonths,
        data
      };
    });
  } catch (error) {
    console.error('Etiket hatırlatıcılarını getirme hatası:', error);
    throw error;
  }
};

/**
 * Bir hatırlatıcıya etiket ekler
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} reminderId - Hatırlatıcı ID
 * @param {number} tagId - Etiket ID
 * @returns {Promise<boolean>} Başarılı ise true
 */
export const addTagToReminder = async (db, reminderId, tagId) => {
  try {
    // Önce ilişkinin var olup olmadığını kontrol et
    const existingRelation = await db.getFirstAsync(`
      SELECT * FROM reminder_tag_relations
      WHERE reminder_id = ? AND tag_id = ?
    `, [reminderId, tagId]);

    // İlişki yoksa ekle
    if (!existingRelation) {
      await db.runAsync(`
        INSERT INTO reminder_tag_relations (reminder_id, tag_id)
        VALUES (?, ?)
      `, [reminderId, tagId]);
    }

    return true;
  } catch (error) {
    console.error('Hatırlatıcıya etiket ekleme hatası:', error);
    throw error;
  }
};

/**
 * Bir hatırlatıcıdan etiketi kaldırır
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} reminderId - Hatırlatıcı ID
 * @param {number} tagId - Etiket ID
 * @returns {Promise<boolean>} Başarılı ise true
 */
export const removeTagFromReminder = async (db, reminderId, tagId) => {
  try {
    await db.runAsync(`
      DELETE FROM reminder_tag_relations
      WHERE reminder_id = ? AND tag_id = ?
    `, [reminderId, tagId]);

    return true;
  } catch (error) {
    console.error('Hatırlatıcıdan etiket kaldırma hatası:', error);
    throw error;
  }
};

/**
 * Bir hatırlatıcının tüm etiketlerini günceller
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} reminderId - Hatırlatıcı ID
 * @param {Array<number>} tagIds - Etiket ID'leri listesi
 * @returns {Promise<boolean>} Başarılı ise true
 */
export const updateReminderTags = async (db, reminderId, tagIds) => {
  try {
    // Önce tüm etiketleri kaldır
    await db.runAsync(`
      DELETE FROM reminder_tag_relations
      WHERE reminder_id = ?
    `, [reminderId]);

    // Yeni etiketleri ekle
    if (tagIds && tagIds.length > 0) {
      for (const tagId of tagIds) {
        await db.runAsync(`
          INSERT INTO reminder_tag_relations (reminder_id, tag_id)
          VALUES (?, ?)
        `, [reminderId, tagId]);
      }
    }

    return true;
  } catch (error) {
    console.error('Hatırlatıcı etiketlerini güncelleme hatası:', error);
    throw error;
  }
};
