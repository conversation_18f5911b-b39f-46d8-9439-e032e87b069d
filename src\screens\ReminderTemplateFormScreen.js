import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
  Switch
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import * as reminderTemplateService from '../services/reminderTemplateService';
import * as reminderTagService from '../services/reminderTagService';

/**
 * Hatırlatıcı Şablonu Form Ekranı
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @param {Object} props.route - Route nesnesi
 * @returns {JSX.Element} Hatırlatıcı Şablonu Form Ekranı
 */
const ReminderTemplateFormScreen = ({ navigation, route }) => {
  const db = useSQLiteContext();
  const templateId = route.params?.templateId;
  const isEditing = !!templateId;
  
  // Form durumu
  const [name, setName] = useState('');
  const [title, setTitle] = useState('');
  const [message, setMessage] = useState('');
  const [repeatType, setRepeatType] = useState('once');
  const [repeatInterval, setRepeatInterval] = useState(1);
  const [repeatDays, setRepeatDays] = useState([]);
  const [priority, setPriority] = useState('normal');
  const [categoryId, setCategoryId] = useState(null);
  const [groupId, setGroupId] = useState(null);
  const [icon, setIcon] = useState('notifications');
  const [color, setColor] = useState('#3498db');
  const [selectedTags, setSelectedTags] = useState([]);
  
  // UI durumu
  const [loading, setLoading] = useState(isEditing);
  const [saving, setSaving] = useState(false);
  const [categories, setCategories] = useState([]);
  const [groups, setGroups] = useState([]);
  const [tags, setTags] = useState([]);
  const [showIconPicker, setShowIconPicker] = useState(false);
  const [showColorPicker, setShowColorPicker] = useState(false);
  
  // Renk seçenekleri
  const colorOptions = [
    '#3498db', // Mavi
    '#2ecc71', // Yeşil
    '#e74c3c', // Kırmızı
    '#f39c12', // Turuncu
    '#9b59b6', // Mor
    '#1abc9c', // Turkuaz
    '#34495e', // Lacivert
    '#e67e22', // Turuncu-Kahve
    '#95a5a6', // Gri
    '#16a085', // Koyu Turkuaz
  ];
  
  // İkon seçenekleri
  const iconOptions = [
    'notifications',
    'event',
    'alarm',
    'receipt',
    'payment',
    'shopping-cart',
    'local-hospital',
    'directions-car',
    'flight',
    'hotel',
    'restaurant',
    'school',
    'work',
    'home',
    'people',
    'person',
    'cake',
    'favorite',
    'star',
    'bookmark',
    'attach-money',
    'account-balance',
    'credit-card',
    'local-atm',
    'local-grocery-store',
    'local-mall',
    'local-pharmacy',
    'local-laundry-service',
    'local-gas-station',
    'local-bar',
    'local-cafe',
    'local-dining',
    'local-movies',
    'local-library',
    'local-post-office',
    'local-see',
    'local-shipping',
    'local-taxi',
    'local-activity',
    'local-florist',
    'local-parking',
    'local-printshop',
    'local-offer',
    'local-phone',
  ];
  
  // Verileri yükle
  const loadData = useCallback(async () => {
    if (!isEditing) return;
    
    try {
      setLoading(true);
      
      // Şablon detaylarını getir
      const templateDetails = await reminderTemplateService.getTemplateById(db, templateId);
      
      if (templateDetails) {
        setName(templateDetails.name);
        setTitle(templateDetails.title);
        setMessage(templateDetails.message || '');
        setRepeatType(templateDetails.repeat_type || 'once');
        setRepeatInterval(templateDetails.repeat_interval || 1);
        setRepeatDays(templateDetails.repeat_days || []);
        setPriority(templateDetails.priority || 'normal');
        setCategoryId(templateDetails.category_id);
        setGroupId(templateDetails.group_id);
        setIcon(templateDetails.icon || 'notifications');
        setColor(templateDetails.color || '#3498db');
        
        // Etiketleri yükle
        if (templateDetails.tags && templateDetails.tags.length > 0) {
          setSelectedTags(templateDetails.tags.map(tag => tag.id));
        }
      }
      
      setLoading(false);
    } catch (error) {
      console.error('Şablon detayları yükleme hatası:', error);
      Alert.alert('Hata', 'Şablon detayları yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  }, [db, templateId, isEditing]);
  
  // Kategorileri yükle
  const loadCategories = useCallback(async () => {
    try {
      const result = await db.getAllAsync(`
        SELECT * FROM categories
        ORDER BY name ASC
      `);
      
      setCategories(result);
    } catch (error) {
      console.error('Kategorileri yükleme hatası:', error);
    }
  }, [db]);
  
  // Grupları yükle
  const loadGroups = useCallback(async () => {
    try {
      const result = await db.getAllAsync(`
        SELECT * FROM reminder_groups
        ORDER BY name ASC
      `);
      
      setGroups(result);
    } catch (error) {
      console.error('Grupları yükleme hatası:', error);
    }
  }, [db]);
  
  // Etiketleri yükle
  const loadTags = useCallback(async () => {
    try {
      const result = await reminderTagService.getAllTags(db);
      setTags(result);
    } catch (error) {
      console.error('Etiketleri yükleme hatası:', error);
    }
  }, [db]);
  
  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadData();
      loadCategories();
      loadGroups();
      loadTags();
    }, [loadData, loadCategories, loadGroups, loadTags])
  );
  
  // Etiket seçimini güncelle
  const toggleTagSelection = (tagId) => {
    setSelectedTags(prevTags => {
      if (prevTags.includes(tagId)) {
        return prevTags.filter(id => id !== tagId);
      } else {
        return [...prevTags, tagId];
      }
    });
  };
  
  // Şablonu kaydet
  const saveTemplate = async () => {
    try {
      // Form doğrulama
      if (!name.trim()) {
        Alert.alert('Hata', 'Lütfen bir şablon adı girin.');
        return;
      }
      
      if (!title.trim()) {
        Alert.alert('Hata', 'Lütfen bir başlık girin.');
        return;
      }
      
      setSaving(true);
      
      // Şablon verilerini hazırla
      const templateData = {
        name,
        title,
        message,
        repeat_type: repeatType,
        repeat_interval: repeatInterval,
        repeat_days: repeatDays,
        priority,
        category_id: categoryId,
        group_id: groupId,
        icon,
        color
      };
      
      if (isEditing) {
        // Şablonu güncelle
        await reminderTemplateService.updateTemplate(db, templateId, templateData, selectedTags);
        Alert.alert('Başarılı', 'Şablon güncellendi.');
      } else {
        // Yeni şablon ekle
        await reminderTemplateService.addTemplate(db, templateData, selectedTags);
        Alert.alert('Başarılı', 'Şablon eklendi.');
      }
      
      setSaving(false);
      navigation.goBack();
    } catch (error) {
      console.error('Şablon kaydetme hatası:', error);
      Alert.alert('Hata', 'Şablon kaydedilirken bir hata oluştu.');
      setSaving(false);
    }
  };
  
  // Yükleniyor durumu
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={styles.loadingText}>Yükleniyor...</Text>
      </View>
    );
  }
  
  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>
          {isEditing ? 'Şablonu Düzenle' : 'Yeni Şablon'}
        </Text>
      </View>
      
      {/* Şablon Adı */}
      <View style={styles.formGroup}>
        <Text style={styles.label}>Şablon Adı</Text>
        <TextInput
          style={styles.input}
          value={name}
          onChangeText={setName}
          placeholder="Şablon adını girin"
          placeholderTextColor={Colors.GRAY_500}
        />
      </View>
      
      {/* Hatırlatıcı Başlığı */}
      <View style={styles.formGroup}>
        <Text style={styles.label}>Hatırlatıcı Başlığı</Text>
        <TextInput
          style={styles.input}
          value={title}
          onChangeText={setTitle}
          placeholder="Hatırlatıcı başlığını girin"
          placeholderTextColor={Colors.GRAY_500}
        />
      </View>
      
      {/* Hatırlatıcı Mesajı */}
      <View style={styles.formGroup}>
        <Text style={styles.label}>Hatırlatıcı Mesajı</Text>
        <TextInput
          style={[styles.input, styles.textArea]}
          value={message}
          onChangeText={setMessage}
          placeholder="Hatırlatıcı mesajını girin"
          placeholderTextColor={Colors.GRAY_500}
          multiline
          numberOfLines={4}
          textAlignVertical="top"
        />
      </View>
      
      {/* Tekrarlama Tipi */}
      <View style={styles.formGroup}>
        <Text style={styles.label}>Tekrarlama Tipi</Text>
        <View style={styles.optionsContainer}>
          <TouchableOpacity
            style={[
              styles.optionButton,
              repeatType === 'once' && styles.optionButtonActive
            ]}
            onPress={() => setRepeatType('once')}
          >
            <MaterialIcons
              name="event"
              size={20}
              color={repeatType === 'once' ? '#fff' : Colors.TEXT_DARK}
            />
            <Text style={[
              styles.optionText,
              repeatType === 'once' && styles.optionTextActive
            ]}>
              Bir Kez
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.optionButton,
              repeatType === 'daily' && styles.optionButtonActive
            ]}
            onPress={() => setRepeatType('daily')}
          >
            <MaterialIcons
              name="today"
              size={20}
              color={repeatType === 'daily' ? '#fff' : Colors.TEXT_DARK}
            />
            <Text style={[
              styles.optionText,
              repeatType === 'daily' && styles.optionTextActive
            ]}>
              Günlük
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.optionButton,
              repeatType === 'weekly' && styles.optionButtonActive
            ]}
            onPress={() => setRepeatType('weekly')}
          >
            <MaterialIcons
              name="view-week"
              size={20}
              color={repeatType === 'weekly' ? '#fff' : Colors.TEXT_DARK}
            />
            <Text style={[
              styles.optionText,
              repeatType === 'weekly' && styles.optionTextActive
            ]}>
              Haftalık
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.optionButton,
              repeatType === 'monthly' && styles.optionButtonActive
            ]}
            onPress={() => setRepeatType('monthly')}
          >
            <MaterialIcons
              name="date-range"
              size={20}
              color={repeatType === 'monthly' ? '#fff' : Colors.TEXT_DARK}
            />
            <Text style={[
              styles.optionText,
              repeatType === 'monthly' && styles.optionTextActive
            ]}>
              Aylık
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.optionButton,
              repeatType === 'yearly' && styles.optionButtonActive
            ]}
            onPress={() => setRepeatType('yearly')}
          >
            <MaterialIcons
              name="event-available"
              size={20}
              color={repeatType === 'yearly' ? '#fff' : Colors.TEXT_DARK}
            />
            <Text style={[
              styles.optionText,
              repeatType === 'yearly' && styles.optionTextActive
            ]}>
              Yıllık
            </Text>
          </TouchableOpacity>
        </View>
      </View>
      
      {/* Öncelik */}
      <View style={styles.formGroup}>
        <Text style={styles.label}>Öncelik</Text>
        <View style={styles.priorityContainer}>
          <TouchableOpacity
            style={[
              styles.priorityButton,
              priority === 'low' && styles.priorityButtonActive,
              { borderColor: Colors.SUCCESS }
            ]}
            onPress={() => setPriority('low')}
          >
            <View style={[styles.priorityDot, { backgroundColor: Colors.SUCCESS }]} />
            <Text style={styles.priorityText}>Düşük</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.priorityButton,
              priority === 'normal' && styles.priorityButtonActive,
              { borderColor: Colors.WARNING }
            ]}
            onPress={() => setPriority('normal')}
          >
            <View style={[styles.priorityDot, { backgroundColor: Colors.WARNING }]} />
            <Text style={styles.priorityText}>Normal</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.priorityButton,
              priority === 'high' && styles.priorityButtonActive,
              { borderColor: Colors.DANGER }
            ]}
            onPress={() => setPriority('high')}
          >
            <View style={[styles.priorityDot, { backgroundColor: Colors.DANGER }]} />
            <Text style={styles.priorityText}>Yüksek</Text>
          </TouchableOpacity>
        </View>
      </View>
      
      {/* Kategori */}
      <View style={styles.formGroup}>
        <Text style={styles.label}>Kategori</Text>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.categoriesContainer}
        >
          {categories.map(category => (
            <TouchableOpacity
              key={category.id}
              style={[
                styles.categoryButton,
                categoryId === category.id && styles.categoryButtonActive,
                {
                  backgroundColor: categoryId === category.id ? category.color : 'transparent',
                  borderColor: category.color,
                  borderWidth: categoryId !== category.id ? 1 : 0
                }
              ]}
              onPress={() => setCategoryId(category.id)}
            >
              <Text style={[
                styles.categoryText,
                categoryId === category.id && styles.categoryTextActive
              ]}>
                {category.name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
      
      {/* Grup */}
      <View style={styles.formGroup}>
        <Text style={styles.label}>Grup</Text>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.categoriesContainer}
        >
          {groups.map(group => (
            <TouchableOpacity
              key={group.id}
              style={[
                styles.categoryButton,
                groupId === group.id && styles.categoryButtonActive,
                {
                  backgroundColor: groupId === group.id ? group.color : 'transparent',
                  borderColor: group.color,
                  borderWidth: groupId !== group.id ? 1 : 0
                }
              ]}
              onPress={() => setGroupId(group.id)}
            >
              <View style={styles.groupButtonContent}>
                <MaterialIcons
                  name={group.icon || 'folder'}
                  size={16}
                  color={groupId === group.id ? '#fff' : group.color}
                  style={styles.groupIcon}
                />
                <Text style={[
                  styles.categoryText,
                  groupId === group.id && styles.categoryTextActive
                ]}>
                  {group.name}
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
      
      {/* Etiketler */}
      <View style={styles.formGroup}>
        <Text style={styles.label}>Etiketler</Text>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.categoriesContainer}
        >
          {tags.map(tag => (
            <TouchableOpacity
              key={tag.id}
              style={[
                styles.tagButton,
                selectedTags.includes(tag.id) && styles.tagButtonActive,
                {
                  backgroundColor: selectedTags.includes(tag.id) ? tag.color : 'transparent',
                  borderColor: tag.color,
                  borderWidth: !selectedTags.includes(tag.id) ? 1 : 0
                }
              ]}
              onPress={() => toggleTagSelection(tag.id)}
            >
              <Text style={[
                styles.tagText,
                selectedTags.includes(tag.id) && styles.tagTextActive
              ]}>
                {tag.name}
              </Text>
            </TouchableOpacity>
          ))}
          <TouchableOpacity
            style={[
              styles.categoryButton,
              styles.newCategoryButton
            ]}
            onPress={() => navigation.navigate('ReminderTags')}
          >
            <MaterialIcons name="label" size={16} color={Colors.PRIMARY} />
            <Text style={[
              styles.categoryText,
              { color: Colors.PRIMARY }
            ]}>
              Etiketleri Yönet
            </Text>
          </TouchableOpacity>
        </ScrollView>
      </View>
      
      {/* İkon */}
      <View style={styles.formGroup}>
        <Text style={styles.label}>İkon</Text>
        <TouchableOpacity
          style={styles.iconPickerButton}
          onPress={() => setShowIconPicker(!showIconPicker)}
        >
          <MaterialIcons name={icon} size={24} color={color} />
          <Text style={styles.iconPickerText}>{icon}</Text>
          <MaterialIcons
            name={showIconPicker ? 'keyboard-arrow-up' : 'keyboard-arrow-down'}
            size={24}
            color={Colors.TEXT_DARK}
          />
        </TouchableOpacity>
        
        {showIconPicker && (
          <View style={styles.iconPickerContainer}>
            {iconOptions.map(iconName => (
              <TouchableOpacity
                key={iconName}
                style={[
                  styles.iconOption,
                  icon === iconName && styles.iconOptionSelected
                ]}
                onPress={() => {
                  setIcon(iconName);
                  setShowIconPicker(false);
                }}
              >
                <MaterialIcons name={iconName} size={24} color={color} />
              </TouchableOpacity>
            ))}
          </View>
        )}
      </View>
      
      {/* Renk */}
      <View style={styles.formGroup}>
        <Text style={styles.label}>Renk</Text>
        <View style={styles.colorPickerContainer}>
          {colorOptions.map(colorOption => (
            <TouchableOpacity
              key={colorOption}
              style={[
                styles.colorOption,
                { backgroundColor: colorOption },
                color === colorOption && styles.colorOptionSelected
              ]}
              onPress={() => setColor(colorOption)}
            />
          ))}
        </View>
      </View>
      
      {/* Kaydet Butonu */}
      <TouchableOpacity
        style={styles.saveButton}
        onPress={saveTemplate}
        disabled={saving}
      >
        {saving ? (
          <ActivityIndicator size="small" color="#fff" />
        ) : (
          <>
            <MaterialIcons name="save" size={20} color="#fff" />
            <Text style={styles.saveButtonText}>
              {isEditing ? 'Şablonu Güncelle' : 'Şablonu Kaydet'}
            </Text>
          </>
        )}
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 32,
  },
  header: {
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: Colors.PRIMARY,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    marginBottom: 8,
  },
  input: {
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: Colors.TEXT_DARK,
  },
  textArea: {
    minHeight: 100,
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.GRAY_300,
    marginRight: 8,
    marginBottom: 8,
  },
  optionButtonActive: {
    backgroundColor: Colors.PRIMARY,
    borderColor: Colors.PRIMARY,
  },
  optionText: {
    fontSize: 14,
    color: Colors.TEXT_DARK,
    marginLeft: 4,
  },
  optionTextActive: {
    color: '#fff',
  },
  priorityContainer: {
    flexDirection: 'row',
  },
  priorityButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginRight: 8,
  },
  priorityButtonActive: {
    backgroundColor: Colors.GRAY_200,
  },
  priorityDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  priorityText: {
    fontSize: 14,
    color: Colors.TEXT_DARK,
  },
  categoriesContainer: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  categoryButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginRight: 8,
  },
  categoryButtonActive: {
    backgroundColor: Colors.PRIMARY,
  },
  categoryText: {
    fontSize: 14,
    color: Colors.TEXT_DARK,
  },
  categoryTextActive: {
    color: '#fff',
  },
  groupButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  groupIcon: {
    marginRight: 4,
  },
  newCategoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.PRIMARY,
    borderStyle: 'dashed',
  },
  tagButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    marginRight: 8,
    borderWidth: 1,
    borderColor: Colors.PRIMARY,
  },
  tagButtonActive: {
    backgroundColor: Colors.PRIMARY,
  },
  tagText: {
    fontSize: 14,
    color: Colors.TEXT_DARK,
  },
  tagTextActive: {
    color: '#fff',
  },
  iconPickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    padding: 12,
  },
  iconPickerText: {
    flex: 1,
    fontSize: 16,
    color: Colors.TEXT_DARK,
    marginLeft: 8,
  },
  iconPickerContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    padding: 8,
    marginTop: 8,
  },
  iconOption: {
    width: 48,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
    margin: 4,
  },
  iconOptionSelected: {
    backgroundColor: Colors.GRAY_200,
  },
  colorPickerContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  colorOption: {
    width: 32,
    height: 32,
    borderRadius: 16,
    margin: 4,
  },
  colorOptionSelected: {
    borderWidth: 2,
    borderColor: Colors.TEXT_DARK,
  },
  saveButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
    borderRadius: 8,
    padding: 16,
    marginTop: 16,
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
});

export default ReminderTemplateFormScreen;
