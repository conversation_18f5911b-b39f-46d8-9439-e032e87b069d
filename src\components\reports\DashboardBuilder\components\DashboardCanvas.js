import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Dimensions,
  PanResponder,
} from 'react-native';
import { useTheme } from '../../../../context/ThemeContext';
import DashboardWidget from './DashboardWidget';
import GridOverlay from './GridOverlay';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

/**
 * Dashboard Canvas - Widget yerleştirme ve düzenleme alanı
 */
const DashboardCanvas = ({
  widgets = [],
  selectedWidget = null,
  onSelectWidget,
  onMoveWidget,
  onResizeWidget,
  onRemoveWidget,
  onUpdateWidget,
  draggedWidget = null,
  onDragStart,
  onDragEnd,
  isPreviewMode = false,
}) => {
  const { theme } = useTheme();
  const [showGrid, setShowGrid] = useState(true);
  const [snapToGrid, setSnapToGrid] = useState(true);
  const panResponder = useRef(null);

  // Theme kontrolü
  if (!theme) {
    return null;
  }

  /**
   * Güvenli tema değeri alma
   * @param {string} property - Tema özelliği
   * @param {string} fallback - Varsayılan değer
   * @returns {string} Güvenli tema değeri
   */
  const getSafeThemeValue = (property, fallback) => {
    return theme[property] || theme.colors?.[property.toLowerCase()] || fallback;
  };

  /**
   * Grid snap hesaplama
   */
  const snapToGridValue = (value, gridSize = 20) => {
    return snapToGrid ? Math.round(value / gridSize) * gridSize : value;
  };

  /**
   * Widget seçimi
   */
  const handleWidgetSelect = (widget) => {
    if (!isPreviewMode) {
      onSelectWidget(widget);
    }
  };

  /**
   * Widget konumu güncelleme
   */
  const handleWidgetMove = (widgetId, newPosition) => {
    const snappedPosition = {
      x: snapToGridValue(newPosition.x),
      y: snapToGridValue(newPosition.y),
    };
    onMoveWidget(widgetId, snappedPosition);
  };

  /**
   * Widget boyutu güncelleme
   */
  const handleWidgetResize = (widgetId, newSize) => {
    const snappedSize = {
      width: snapToGridValue(newSize.width),
      height: snapToGridValue(newSize.height),
    };
    onResizeWidget(widgetId, snappedSize);
  };

  /**
   * Canvas üzerinde boş alan tıklama
   */
  const handleCanvasPress = () => {
    if (selectedWidget && !isPreviewMode) {
      onSelectWidget(null);
    }
  };

  /**
   * Widget render
   */
  const renderWidget = (widget) => {
    const isSelected = selectedWidget?.id === widget.id;
    const isDragged = draggedWidget?.id === widget.id;

    return (
      <DashboardWidget
        key={widget.id}
        widget={widget}
        isSelected={isSelected}
        isDragged={isDragged}
        isPreviewMode={isPreviewMode}
        onSelect={() => handleWidgetSelect(widget)}
        onMove={(newPosition) => handleWidgetMove(widget.id, newPosition)}
        onResize={(newSize) => handleWidgetResize(widget.id, newSize)}
        onRemove={() => onRemoveWidget(widget.id)}
        onUpdate={(updatedWidget) => onUpdateWidget(widget.id, updatedWidget)}
        onDragStart={() => onDragStart(widget)}
        onDragEnd={onDragEnd}
        snapToGrid={snapToGrid}
        theme={theme}
      />
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: getSafeThemeValue('BACKGROUND', '#fff') }]}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
        bounces={false}
        onTouchStart={handleCanvasPress}
      >
        {/* Grid Overlay */}
        {showGrid && !isPreviewMode && (
          <GridOverlay
            width={screenWidth * 2}
            height={screenHeight * 2}
            gridSize={20}
            color={getSafeThemeValue('BORDER', '#e0e0e0')}
            opacity={0.3}
          />
        )}

        {/* Canvas Area */}
        <View style={[styles.canvas, { backgroundColor: getSafeThemeValue('BACKGROUND', '#fff') }]}>
          {/* Render all widgets */}
          {widgets
            .filter(widget => widget.isVisible)
            .sort((a, b) => (a.zIndex || 0) - (b.zIndex || 0))
            .map(renderWidget)}

          {/* Drop Zone Indicators */}
          {draggedWidget && !isPreviewMode && (
            <View style={[styles.dropZone, { borderColor: getSafeThemeValue('PRIMARY', '#007AFF') }]}>
              {/* Drop zone visual indicator */}
            </View>
          )}
        </View>
      </ScrollView>

      {/* Canvas Controls */}
      {!isPreviewMode && (
        <View style={[styles.canvasControls, { backgroundColor: getSafeThemeValue('SURFACE', '#f9f9f9') }]}>
          <View style={styles.controlGroup}>
            <View style={styles.controlRow}>
              <View style={styles.controlItem}>
                <View style={[styles.controlIndicator, { backgroundColor: showGrid ? getSafeThemeValue('SUCCESS', '#28a745') : getSafeThemeValue('DISABLED', '#ccc') }]} />
                <Text style={[styles.controlLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666') }]}>
                  Grid
                </Text>
              </View>
              <View style={styles.controlItem}>
                <View style={[styles.controlIndicator, { backgroundColor: snapToGrid ? getSafeThemeValue('SUCCESS', '#28a745') : getSafeThemeValue('DISABLED', '#ccc') }]} />
                <Text style={[styles.controlLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666') }]}>
                  Snap
                </Text>
              </View>
            </View>
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    minWidth: screenWidth * 2,
    minHeight: screenHeight * 2,
  },
  canvas: {
    flex: 1,
    position: 'relative',
    minWidth: screenWidth * 2,
    minHeight: screenHeight * 2,
  },
  dropZone: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderWidth: 2,
    borderStyle: 'dashed',
    borderRadius: 8,
    opacity: 0.5,
  },
  canvasControls: {
    position: 'absolute',
    top: 12,
    right: 12,
    padding: 8,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  controlGroup: {
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  controlRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  controlItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  controlIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  controlLabel: {
    fontSize: 10,
    fontWeight: '600',
  },
});

export default DashboardCanvas;
