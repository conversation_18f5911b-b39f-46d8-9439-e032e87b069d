/**
 * Rapor Veri Servisi
 * Raporlar için gerekli verilerin hazırlanması ve işlenmesi
 * Real Data Integration ile güncellenmiş versiyon
 */

import { createRealDataService } from './reports/realDataService';

/**
 * Rapor veri servisi
 * Mock data yerine Real Data Integration kullanır
 */
class ReportDataService {
  constructor(db) {
    this.db = db;
    this.realDataService = createRealDataService(db);
  }

  /**
   * Aylık gelir-gider verilerini çeker
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Monthly income/expense data
   */
  async getMonthlyIncomeExpenseData(params) {
    try {
      // Real data integration kullan
      return await this.realDataService.getMonthlyIncomeExpense(params);
    } catch (error) {
      // Hata durumunda mock data döndür
      return this.getMockMonthlyIncomeExpenseData();
    }
  }

  /**
   * <PERSON><PERSON><PERSON>lım verilerini <PERSON>ek<PERSON>
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Category distribution data
   */
  async getCategoryDistributionData(params) {
    try {
      return await this.realDataService.getCategoryDistribution(params);
    } catch (error) {
      return this.getMockCategoryDistributionData(params);
    }
  }

  /**
   * Günlük trend verilerini çeker
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Daily trends data
   */
  async getDailyTrendsData(params) {
    try {
      return await this.realDataService.getDailyTrends(params);
    } catch (error) {
      return this.getMockDailyTrendsData(params);
    }
  }

  /**
   * En yüksek işlemleri çeker
   * @param {Object} params - Query parameters
   * @returns {Promise<Array>} Top transactions
   */
  async getTopTransactionsData(params) {
    try {
      return await this.realDataService.getTopTransactions(params);
    } catch (error) {
      return this.getMockTopTransactionsData(params);
    }
  }

  /**
   * Finansal hedef verilerini çeker
   * @param {Object} params - Query parameters
   * @returns {Promise<Array>} Goals data
   */
  async getGoalsProgressData(params) {
    try {
      return await this.realDataService.getGoalsProgress(params);
    } catch (error) {
      return this.getMockGoalsProgressData(params);
    }
  }

  /**
   * Aylık karşılaştırma verilerini çeker
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Monthly comparison data
   */
  async getMonthlyComparisonData(params) {
    try {
      return await this.realDataService.getMonthlyComparison(params);
    } catch (error) {
      return this.getMockMonthlyComparisonData(params);
    }
  }

  /**
   * Rapor özet verilerini çeker
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Summary data
   */
  async getReportSummaryData(params) {
    try {
      return await this.realDataService.getReportSummary(params);
    } catch (error) {
      return this.getMockReportSummaryData(params);
    }
  }

  /**
   * Mock aylık gelir-gider verisi
   */
  getMockMonthlyIncomeExpenseData() {
    return {
      period: 'Ocak 2024',
      income: {
        total: 45000,
        categories: [
          { name: 'Maaş', amount: 35000, percentage: 77.8 },
          { name: 'Freelance', amount: 8000, percentage: 17.8 },
          { name: 'Diğer', amount: 2000, percentage: 4.4 }
        ]
      },
      expenses: {
        total: 32000,
        categories: [
          { name: 'Kira', amount: 15000, percentage: 46.9 },
          { name: 'Gıda', amount: 5000, percentage: 15.6 },
          { name: 'Ulaşım', amount: 3000, percentage: 9.4 },
          { name: 'Faturalar', amount: 4000, percentage: 12.5 },
          { name: 'Diğer', amount: 5000, percentage: 15.6 }
        ]
      },
      netIncome: 13000,
      previousMonth: {
        income: 42000,
        expenses: 30000,
        netIncome: 12000
      },
      comparison: {
        incomeChange: 7.1,
        expenseChange: 6.7,
        netIncomeChange: 8.3
      }
    };
  }

  /**
   * Kategori dağılım verilerini çeker
   */
  async getCategoryDistributionData(params) {
    try {
      return this.getMockCategoryDistributionData();
    } catch (error) {
      throw error;
    }
  }

  /**
   * Mock kategori dağılım verisi
   */
  getMockCategoryDistributionData() {
    return {
      period: 'Ocak 2024',
      categories: [
        { 
          name: 'Gıda & İçecek', 
          amount: 5000, 
          percentage: 35.5, 
          transactionCount: 45,
          color: '#FF6B6B'
        },
        { 
          name: 'Ulaşım', 
          amount: 3000, 
          percentage: 21.3, 
          transactionCount: 28,
          color: '#4ECDC4'
        },
        { 
          name: 'Kira', 
          amount: 2500, 
          percentage: 17.7, 
          transactionCount: 1,
          color: '#45B7D1'
        },
        { 
          name: 'Faturalar', 
          amount: 2000, 
          percentage: 14.2, 
          transactionCount: 8,
          color: '#FFA07A'
        },
        { 
          name: 'Eğlence', 
          amount: 1100, 
          percentage: 7.8, 
          transactionCount: 15,
          color: '#98D8C8'
        }
      ],
      totalAmount: 14600,
      totalTransactions: 97
    };
  }

  /**
   * Nakit akış verilerini çeker
   */
  async getCashFlowData(params) {
    try {
      return await this.realDataService.getCashFlowData(params);
    } catch (error) {
      return this.getMockCashFlowData(params);
    }
  }

  /**
   * Mock nakit akış verisi
   */
  getMockCashFlowData() {
    return {
      period: 'Son 30 Gün',
      dailyData: [
        { date: '2024-01-01', income: 0, expense: 150, net: -150, cumulative: -150 },
        { date: '2024-01-02', income: 0, expense: 200, net: -200, cumulative: -350 },
        { date: '2024-01-03', income: 5000, expense: 300, net: 4700, cumulative: 4350 },
        { date: '2024-01-04', income: 0, expense: 180, net: -180, cumulative: 4170 },
        { date: '2024-01-05', income: 0, expense: 250, net: -250, cumulative: 3920 },
      ],
      summary: {
        totalIncome: 45000,
        totalExpense: 32000,
        netFlow: 13000,
        averageDailyIncome: 1500,
        averageDailyExpense: 1067
      }
    };
  }

  /**
   * Bütçe vs gerçek verilerini çeker
   */
  async getBudgetVsActualData(params) {
    try {
      return await this.realDataService.getBudgetVsActualData(params);
    } catch (error) {
      return this.getMockBudgetVsActualData(params);
    }
  }

  /**
   * Mock bütçe vs gerçek verisi
   */
  getMockBudgetVsActualData() {
    return {
      period: 'Ocak 2024',
      categories: [
        { 
          name: 'Gıda & İçecek', 
          budgeted: 4000, 
          actual: 5000, 
          variance: -1000,
          variancePercentage: -25.0,
          status: 'over'
        },
        { 
          name: 'Ulaşım', 
          budgeted: 3500, 
          actual: 3000, 
          variance: 500,
          variancePercentage: 14.3,
          status: 'under'
        },
        { 
          name: 'Eğlence', 
          budgeted: 1500, 
          actual: 1100, 
          variance: 400,
          variancePercentage: 26.7,
          status: 'under'
        }
      ],
      totals: {
        totalBudget: 35000,
        totalActual: 32000,
        totalVariance: 3000,
        variancePercentage: 8.6
      }
    };
  }

  /**
   * İşlem listesi verilerini çeker
   */
  async getTransactionListData(params) {
    try {
      return await this.realDataService.getTransactionListData(params);
    } catch (error) {
      return this.getMockTransactionListData(params);
    }
  }

  /**
   * Mock işlem listesi verisi
   */
  getMockTransactionListData() {
    return {
      period: 'Son 30 Gün',
      transactions: [
        { 
          date: '2024-01-01', 
          description: 'Market Alışverişi', 
          category: 'Gıda & İçecek', 
          amount: 150, 
          type: 'expense' 
        },
        { 
          date: '2024-01-02', 
          description: 'Araç Yakıt', 
          category: 'Ulaşım', 
          amount: 200, 
          type: 'expense' 
        },
        { 
          date: '2024-01-03', 
          description: 'Maaş', 
          category: 'Gelir', 
          amount: 5000, 
          type: 'income' 
        },
        { 
          date: '2024-01-04', 
          description: 'Kira Ödemesi', 
          category: 'Kira', 
          amount: 180, 
          type: 'expense' 
        },
        { 
          date: '2024-01-05', 
          description: 'Freelance Proje Geliri', 
          category: 'Gelir', 
          amount: 250, 
          type: 'income' 
        },
      ],
      totalIncome: 5250,
      totalExpense: 1030,
      netIncome: 4220
    };
  }

  /**
   * Temel özet verilerini çeker
   */
  async getBasicSummaryData(params) {
    try {
      return await this.realDataService.getBasicSummaryData(params);
    } catch (error) {
      return this.getMockBasicSummaryData(params);
    }
  }

  /**
   * Mock temel özet verisi
   */
  getMockBasicSummaryData() {
    return {
      period: 'Ocak 2024',
      summary: {
        totalIncome: 45000,
        totalExpense: 32000,
        netIncome: 13000,
        savingsRate: 28.9,
        transactionCount: 156,
        averageTransactionAmount: 205
      },
      topCategories: {
        expense: [
          { name: 'Gıda & İçecek', amount: 5000 },
          { name: 'Ulaşım', amount: 3000 },
          { name: 'Kira', amount: 2500 }
        ],
        income: [
          { name: 'Maaş', amount: 35000 },
          { name: 'Freelance', amount: 8000 },
          { name: 'Diğer', amount: 2000 }
        ]
      }
    };
  }
}

export default ReportDataService;
