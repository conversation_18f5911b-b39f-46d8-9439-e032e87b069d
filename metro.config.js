const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

// Get the default Metro configuration for Expo projects
const defaultConfig = getDefaultConfig(__dirname);

// Customize the resolver
defaultConfig.resolver = {
  ...defaultConfig.resolver,

  // Ensure node_modules are properly resolved
  extraNodeModules: new Proxy({}, {
    get: (target, name) => {
      return path.join(__dirname, `node_modules/${name}`);
    },
  }),

  // Configure asset resolutions
  assetExts: [...defaultConfig.resolver.assetExts],

  // Configure source extensions for proper resolution
  sourceExts: [...defaultConfig.resolver.sourceExts, 'jsx', 'js', 'ts', 'tsx', 'json'],

  // Fix date-fns module resolution warnings
  resolverMainFields: ['react-native', 'browser', 'main'],
  platforms: ['ios', 'android', 'native', 'web'],
};

// Export the modified configuration
module.exports = defaultConfig;
