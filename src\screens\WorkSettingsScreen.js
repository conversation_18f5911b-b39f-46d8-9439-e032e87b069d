import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Switch,
  Alert,
  ActivityIndicator
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import * as workService from '../services/workService';

/**
 * Vardiya Ayarları Ekranı
 *
 * Bu ekran, vardiya takibi ile ilgili tüm ayarları yönetmeyi sağlar:
 * - Ücret ayarları (saatlik ücret, fazla mesai ücreti, para birimi)
 * - Çalışma saatleri (haftalık ve günlük çalışma saatleri)
 * - Çalışma günleri (hangi günlerin çalışma günü olduğu)
 * - Vardiya planlaması (otomatik vardiya oluşturma, bildirimler)
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @returns {JSX.Element} Vardiya ayarları ekranı
 */
export default function WorkSettingsScreen({ navigation }) {
  const db = useSQLiteContext();
  const insets = useSafeAreaInsets();

  // Durum değişkenleri
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [settings, setSettings] = useState({
    hourly_rate: '100',
    overtime_rate: '150',
    weekly_work_hours: '45',
    daily_work_hours: '9',
    currency: 'TRY',
    work_days: '1,2,3,4,5',
    auto_create_shifts: true,
    auto_create_days_ahead: '7',
    shift_notification_enabled: true,
    shift_notification_minutes: '60'
  });

  // Çalışma günleri
  const [workDays, setWorkDays] = useState({
    1: true, // Pazartesi
    2: true, // Salı
    3: true, // Çarşamba
    4: true, // Perşembe
    5: true, // Cuma
    6: false, // Cumartesi
    7: false  // Pazar
  });

  // Ayarları yükle
  useEffect(() => {
    const loadSettings = async () => {
      try {
        setLoading(true);

        const workSettings = await workService.getWorkSettings(db);

        if (workSettings) {
          setSettings({
            hourly_rate: workSettings.hourly_rate ? workSettings.hourly_rate.toString() : '100',
            overtime_rate: workSettings.overtime_rate ? workSettings.overtime_rate.toString() : '150',
            weekly_work_hours: workSettings.weekly_work_hours ? workSettings.weekly_work_hours.toString() : '45',
            daily_work_hours: workSettings.daily_work_hours ? workSettings.daily_work_hours.toString() : '9',
            currency: workSettings.currency || 'TRY',
            work_days: workSettings.work_days || '1,2,3,4,5',
            auto_create_shifts: workSettings.auto_create_shifts !== 0,
            auto_create_days_ahead: workSettings.auto_create_days_ahead ? workSettings.auto_create_days_ahead.toString() : '7',
            shift_notification_enabled: workSettings.shift_notification_enabled !== 0,
            shift_notification_minutes: workSettings.shift_notification_minutes ? workSettings.shift_notification_minutes.toString() : '60'
          });

          // Çalışma günlerini ayarla
          const days = workSettings.work_days ? workSettings.work_days.split(',').map(d => parseInt(d)) : [1, 2, 3, 4, 5];
          const newWorkDays = { 1: false, 2: false, 3: false, 4: false, 5: false, 6: false, 7: false };

          days.forEach(day => {
            newWorkDays[day] = true;
          });

          setWorkDays(newWorkDays);
        }

        setLoading(false);
      } catch (error) {
        console.error('Vardiya ayarları yükleme hatası:', error);
        Alert.alert('Hata', 'Vardiya ayarları yüklenirken bir hata oluştu.');
        setLoading(false);
      }
    };

    loadSettings();
  }, [db]);

  // Ayarları kaydet
  const saveSettings = async () => {
    try {
      setSaving(true);

      // Çalışma günlerini string'e dönüştür
      const workDaysString = Object.entries(workDays)
        .filter(([_, isSelected]) => isSelected)
        .map(([day]) => day)
        .join(',');

      const updatedSettings = {
        hourly_rate: parseFloat(settings.hourly_rate),
        overtime_rate: parseFloat(settings.overtime_rate),
        weekly_work_hours: parseInt(settings.weekly_work_hours),
        daily_work_hours: parseInt(settings.daily_work_hours),
        currency: settings.currency,
        work_days: workDaysString,
        auto_create_shifts: settings.auto_create_shifts ? 1 : 0,
        auto_create_days_ahead: parseInt(settings.auto_create_days_ahead),
        shift_notification_enabled: settings.shift_notification_enabled ? 1 : 0,
        shift_notification_minutes: parseInt(settings.shift_notification_minutes)
      };

      await workService.updateWorkSettings(db, updatedSettings);

      setSaving(false);
      Alert.alert('Başarılı', 'Vardiya ayarları başarıyla kaydedildi.');
    } catch (error) {
      console.error('Vardiya ayarları kaydetme hatası:', error);
      Alert.alert('Hata', 'Vardiya ayarları kaydedilirken bir hata oluştu.');
      setSaving(false);
    }
  };

  // Çalışma günü değişikliğini işle
  const handleWorkDayChange = (day, value) => {
    setWorkDays(prev => ({ ...prev, [day]: value }));
  };

  // Form değişikliklerini işle
  const handleChange = (name, value) => {
    setSettings(prev => ({ ...prev, [name]: value }));
  };

  // Gün adını formatla
  const formatDayName = (day) => {
    switch (parseInt(day)) {
      case 1: return 'Pazartesi';
      case 2: return 'Salı';
      case 3: return 'Çarşamba';
      case 4: return 'Perşembe';
      case 5: return 'Cuma';
      case 6: return 'Cumartesi';
      case 7: return 'Pazar';
      default: return '';
    }
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent, { paddingTop: insets.top }]}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={styles.loadingText}>Vardiya ayarları yükleniyor...</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Vardiya Ayarları</Text>
        <View style={{ width: 40 }} />
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Ücret Ayarları</Text>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Saatlik Ücret</Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                value={settings.hourly_rate}
                onChangeText={(value) => handleChange('hourly_rate', value)}
                keyboardType="decimal-pad"
                placeholder="Saatlik ücret"
              />
              <Text style={styles.currencyText}>{settings.currency}</Text>
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Fazla Mesai Ücreti</Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                value={settings.overtime_rate}
                onChangeText={(value) => handleChange('overtime_rate', value)}
                keyboardType="decimal-pad"
                placeholder="Fazla mesai ücreti"
              />
              <Text style={styles.currencyText}>{settings.currency}</Text>
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Para Birimi</Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                value={settings.currency}
                onChangeText={(value) => handleChange('currency', value)}
                placeholder="Para birimi"
              />
            </View>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Çalışma Saatleri</Text>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Haftalık Çalışma Saati</Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                value={settings.weekly_work_hours}
                onChangeText={(value) => handleChange('weekly_work_hours', value)}
                keyboardType="number-pad"
                placeholder="Haftalık çalışma saati"
              />
              <Text style={styles.currencyText}>saat</Text>
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Günlük Çalışma Saati</Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                value={settings.daily_work_hours}
                onChangeText={(value) => handleChange('daily_work_hours', value)}
                keyboardType="number-pad"
                placeholder="Günlük çalışma saati"
              />
              <Text style={styles.currencyText}>saat</Text>
            </View>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Çalışma Günleri</Text>

          {Object.entries(workDays).map(([day, isSelected]) => (
            <View key={day} style={styles.workDayItem}>
              <Text style={styles.workDayText}>{formatDayName(day)}</Text>
              <Switch
                value={isSelected}
                onValueChange={(value) => handleWorkDayChange(day, value)}
                trackColor={{ false: '#d1d1d1', true: Colors.PRIMARY }}
                thumbColor="#fff"
              />
            </View>
          ))}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Vardiya Planlaması</Text>

          <View style={styles.switchItem}>
            <View style={styles.switchInfo}>
              <MaterialIcons name="schedule" size={24} color={settings.auto_create_shifts ? Colors.PRIMARY : '#666'} />
              <Text style={styles.switchLabel}>Otomatik Vardiya Oluştur</Text>
            </View>
            <Switch
              value={settings.auto_create_shifts}
              onValueChange={(value) => handleChange('auto_create_shifts', value)}
              trackColor={{ false: '#d1d1d1', true: Colors.PRIMARY }}
              thumbColor="#fff"
            />
          </View>

          {settings.auto_create_shifts && (
            <View style={styles.formGroup}>
              <Text style={styles.label}>Kaç Gün İlerisi İçin Oluştur</Text>
              <View style={styles.inputContainer}>
                <TextInput
                  style={styles.input}
                  value={settings.auto_create_days_ahead}
                  onChangeText={(value) => handleChange('auto_create_days_ahead', value)}
                  keyboardType="number-pad"
                  placeholder="Gün sayısı"
                />
                <Text style={styles.currencyText}>gün</Text>
              </View>
              <Text style={styles.helperText}>
                Vardiya planlamasına göre bu kadar gün ilerisine kadar vardiyalar otomatik oluşturulur.
              </Text>
            </View>
          )}

          <View style={styles.switchItem}>
            <View style={styles.switchInfo}>
              <MaterialIcons name="notifications" size={24} color={settings.shift_notification_enabled ? Colors.PRIMARY : '#666'} />
              <Text style={styles.switchLabel}>Vardiya Bildirimleri</Text>
            </View>
            <Switch
              value={settings.shift_notification_enabled}
              onValueChange={(value) => handleChange('shift_notification_enabled', value)}
              trackColor={{ false: '#d1d1d1', true: Colors.PRIMARY }}
              thumbColor="#fff"
            />
          </View>

          {settings.shift_notification_enabled && (
            <View style={styles.formGroup}>
              <Text style={styles.label}>Bildirim Zamanı</Text>
              <View style={styles.inputContainer}>
                <TextInput
                  style={styles.input}
                  value={settings.shift_notification_minutes}
                  onChangeText={(value) => handleChange('shift_notification_minutes', value)}
                  keyboardType="number-pad"
                  placeholder="Dakika"
                />
                <Text style={styles.currencyText}>dakika önce</Text>
              </View>
              <Text style={styles.helperText}>
                Vardiya başlamadan bu kadar dakika önce bildirim gönderilir.
              </Text>
            </View>
          )}
        </View>

        <TouchableOpacity
          style={styles.saveButton}
          onPress={saveSettings}
          disabled={saving}
        >
          {saving ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <>
              <MaterialIcons name="save" size={20} color="#fff" />
              <Text style={styles.saveButtonText}>Ayarları Kaydet</Text>
            </>
          )}
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: Colors.PRIMARY,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    color: '#333',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 12,
    paddingHorizontal: 16,
  },
  input: {
    flex: 1,
    paddingVertical: 14,
    fontSize: 16,
    color: '#333',
  },
  currencyText: {
    fontSize: 16,
    color: '#666',
    marginLeft: 8,
  },
  workDayItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    marginBottom: 4,
    backgroundColor: '#f9f9f9',
    borderRadius: 12,
  },
  workDayText: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
  },
  switchItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    marginBottom: 8,
    backgroundColor: '#f9f9f9',
    borderRadius: 12,
  },
  switchInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  switchLabel: {
    fontSize: 16,
    color: '#333',
    marginLeft: 8,
    fontWeight: '500',
  },
  helperText: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  saveButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
    borderRadius: 12,
    paddingVertical: 16,
    marginBottom: 24,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
    marginLeft: 8,
  },
});
