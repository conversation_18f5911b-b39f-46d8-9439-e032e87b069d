import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

/**
 * Table Widget - Tablo <PERSON>imi için widget
 */
const TableWidget = ({ widget, isPreviewMode, onUpdate, theme }) => {
  /**
   * G<PERSON><PERSON>li tema değeri alma
   * @param {string} property - <PERSON><PERSON>
   * @param {string} fallback - <PERSON><PERSON><PERSON><PERSON><PERSON>
   * @returns {string} <PERSON><PERSON><PERSON>li tema değeri
   */
  const getSafeThemeValue = (property, fallback) => {
    return theme[property] || theme.colors?.[property.toLowerCase()] || fallback;
  };

  return (
    <View style={[styles.container, { backgroundColor: getSafeThemeValue('BACKGROUND', '#fff') }]}>
      <View style={[styles.tableArea, { borderColor: getSafeThemeValue('BORDER', '#e0e0e0') }]}>
        <Text style={[styles.tableIcon, { color: getSafeThemeValue('SUCCESS', '#28a745') }]}>
          📋
        </Text>
        <Text style={[styles.tableLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666') }]}>
          Data Table
        </Text>
        <Text style={[styles.tableSubLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666') }]}>
          {widget.config?.columns?.length || 4} columns
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 4,
  },
  tableArea: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderStyle: 'dashed',
    borderRadius: 4,
  },
  tableIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  tableLabel: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 4,
  },
  tableSubLabel: {
    fontSize: 10,
  },
});

export default TableWidget;
