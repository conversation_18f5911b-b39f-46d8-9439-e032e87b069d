import { executeSql, getDatabase } from '../utils/dbUtils';

/**
 * @typedef {object} NotificationSettings
 * @property {boolean} enabled - Bildirim etkin mi?
 * @property {number} days_before - Ka<PERSON> gün önce bildirilecek?
 * @property {string} time - <PERSON><PERSON><PERSON><PERSON> (örn: '09:00')
 */

/**
 * @typedef {object} RegularIncome
 * @property {number} [id] - Gelir ID'si
 * @property {string} title - <PERSON><PERSON><PERSON> b<PERSON>
 * @property {number} amount - G<PERSON><PERSON> miktarı
 * @property {string} currency_code - Para birimi kodu (örn: 'TRY', 'USD')
 * @property {number} [payment_day] - Öde<PERSON> günü (ayın günü veya haftanın günü)
 * @property {'daily' | 'weekly' | 'monthly' | 'yearly' | 'custom'} recurrence_type - Tekrarlama türü
 * @property {number} [recurrence_interval] - Tekrarlama aralığı (recurrence_type 'custom' ise veya haftalık/ayl<PERSON><PERSON> katl<PERSON> i<PERSON>)
 * @property {string} next_payment_date - <PERSON><PERSON> sonraki ödeme ta<PERSON> (YYYY-MM-DD)
 * @property {string | NotificationSettings} [notification_settings] - Bildirim ayarları (JSON string veya obje)
 * @property {'active' | 'paused' | 'ended'} [status] - Durum ('active', 'paused', 'ended')
 * @property {string} [notes] - Notlar
 * @property {string} [created_at] - Oluşturulma tarihi
 * @property {string} [updated_at] - Güncellenme tarihi
 */

/**
 * Yeni bir düzenli gelir ekler.
 * @param {object} params
 * @param {string} params.title - Gelir başlığı
 * @param {number} params.amount - Gelir miktarı
 * @param {string} params.currency_code - Para birimi kodu
 * @param {number} [params.payment_day] - Ödeme günü
 * @param {'daily' | 'weekly' | 'monthly' | 'yearly' | 'custom'} params.recurrence_type - Tekrarlama türü
 * @param {number} [params.recurrence_interval] - Tekrarlama aralığı
 * @param {string} params.next_payment_date - Bir sonraki ödeme tarihi (YYYY-MM-DD)
 * @param {NotificationSettings} [params.notification_settings] - Bildirim ayarları
 * @param {'active' | 'paused' | 'ended'} [params.status='active'] - Durum
 * @param {string} [params.notes] - Notlar
 * @returns {Promise<number>} Eklenen gelirin ID'si
 */
export const addRegularIncome = async ({
  title,
  amount,
  currency_code,
  payment_day,
  recurrence_type,
  recurrence_interval,
  next_payment_date,
  notification_settings,
  status = 'active',
  notes,
}) => {
  const db = await getDatabase();
  const notificationSettingsString = notification_settings
    ? JSON.stringify(notification_settings)
    : null;
  const sql = `
    INSERT INTO regular_incomes (
      title, amount, currency_code, payment_day, recurrence_type,
      recurrence_interval, next_payment_date, notification_settings, status, notes
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
  `;
  const params = [
    title,
    amount,
    currency_code,
    payment_day,
    recurrence_type,
    recurrence_interval,
    next_payment_date,
    notificationSettingsString,
    status,
    notes,
  ];
  try {
    const result = await executeSql(db, sql, params);
    return result.insertId;
  } catch (error) {
    console.error('Error adding regular income:', error);
    throw error;
  }
};

/**
 * Tüm düzenli gelirleri getirir.
 * @returns {Promise<RegularIncome[]>} Düzenli gelirlerin listesi
 */
export const getAllRegularIncomes = async () => {
  const db = await getDatabase();
  const sql = 'SELECT * FROM regular_incomes ORDER BY next_payment_date ASC;';
  try {
    const results = await executeSql(db, sql);
    return results.rows._array.map(row => ({
      ...row,
      notification_settings: row.notification_settings
        ? JSON.parse(row.notification_settings)
        : null,
    }));
  } catch (error) {
    console.error('Error fetching all regular incomes:', error);
    throw error;
  }
};

/**
 * Belirli bir ID'ye sahip düzenli geliri getirir.
 * @param {object} params
 * @param {number} params.id - Getirilecek gelirin ID'si
 * @returns {Promise<RegularIncome | null>} Düzenli gelir objesi veya bulunamazsa null
 */
export const getRegularIncomeById = async ({ id }) => {
  const db = await getDatabase();
  const sql = 'SELECT * FROM regular_incomes WHERE id = ?;';
  try {
    const result = await executeSql(db, sql, [id]);
    if (result.rows && result.rows.length > 0) {
      const row = result.rows.item(0);
      return {
        ...row,
        notification_settings: row.notification_settings
          ? JSON.parse(row.notification_settings)
          : null,
      };
    }
    return null;
  } catch (error) {
    console.error(`Error fetching regular income with id ${id}:`, error);
    throw error;
  }
};

/**
 * Mevcut bir düzenli geliri günceller.
 * @param {object} params
 * @param {number} params.id - Güncellenecek gelirin ID'si
 * @param {string} [params.title] - Yeni gelir başlığı
 * @param {number} [params.amount] - Yeni gelir miktarı
 * @param {string} [params.currency_code] - Yeni para birimi kodu
 * @param {number} [params.payment_day] - Yeni ödeme günü
 * @param {'daily' | 'weekly' | 'monthly' | 'yearly' | 'custom'} [params.recurrence_type] - Yeni tekrarlama türü
 * @param {number} [params.recurrence_interval] - Yeni tekrarlama aralığı
 * @param {string} [params.next_payment_date] - Yeni bir sonraki ödeme tarihi
 * @param {NotificationSettings} [params.notification_settings] - Yeni bildirim ayarları
 * @param {'active' | 'paused' | 'ended'} [params.status] - Yeni durum
 * @param {string} [params.notes] - Yeni notlar
 * @returns {Promise<number>} Etkilenen satır sayısı
 */
export const updateRegularIncome = async ({
  id,
  title,
  amount,
  currency_code,
  payment_day,
  recurrence_type,
  recurrence_interval,
  next_payment_date,
  notification_settings,
  status,
  notes,
}) => {
  const db = await getDatabase();
  
  const existingIncome = await getRegularIncomeById({ id });
  if (!existingIncome) {
    throw new Error(`Regular income with id ${id} not found.`);
  }

  const paramsToUpdate = [];
  const sqlSetClauses = [];

  if (title !== undefined) {
    sqlSetClauses.push('title = ?');
    paramsToUpdate.push(title);
  }
  if (amount !== undefined) {
    sqlSetClauses.push('amount = ?');
    paramsToUpdate.push(amount);
  }
  if (currency_code !== undefined) {
    sqlSetClauses.push('currency_code = ?');
    paramsToUpdate.push(currency_code);
  }
  if (payment_day !== undefined) {
    sqlSetClauses.push('payment_day = ?');
    paramsToUpdate.push(payment_day);
  }
  if (recurrence_type !== undefined) {
    sqlSetClauses.push('recurrence_type = ?');
    paramsToUpdate.push(recurrence_type);
  }
  if (recurrence_interval !== undefined) {
    sqlSetClauses.push('recurrence_interval = ?');
    paramsToUpdate.push(recurrence_interval);
  }
  if (next_payment_date !== undefined) {
    sqlSetClauses.push('next_payment_date = ?');
    paramsToUpdate.push(next_payment_date);
  }
  if (notification_settings !== undefined) {
    sqlSetClauses.push('notification_settings = ?');
    paramsToUpdate.push(notification_settings ? JSON.stringify(notification_settings) : null);
  }
  if (status !== undefined) {
    sqlSetClauses.push('status = ?');
    paramsToUpdate.push(status);
  }
  if (notes !== undefined) {
    sqlSetClauses.push('notes = ?');
    paramsToUpdate.push(notes);
  }

  if (sqlSetClauses.length === 0) {
    return 0; // Güncellenecek bir şey yok
  }

  const sql = `
    UPDATE regular_incomes
    SET ${sqlSetClauses.join(', ')}, updated_at = CURRENT_TIMESTAMP
    WHERE id = ?;
  `;
  paramsToUpdate.push(id);

  try {
    const result = await executeSql(db, sql, paramsToUpdate);
    return result.rowsAffected;
  } catch (error) {
    console.error(`Error updating regular income with id ${id}:`, error);
    throw error;
  }
};

/**
 * Bir düzenli geliri siler.
 * @param {object} params
 * @param {number} params.id - Silinecek gelirin ID'si
 * @returns {Promise<number>} Etkilenen satır sayısı
 */
export const deleteRegularIncome = async ({ id }) => {
  const db = await getDatabase();
  const sql = 'DELETE FROM regular_incomes WHERE id = ?;';
  try {
    const result = await executeSql(db, sql, [id]);
    return result.rowsAffected;
  } catch (error) {
    console.error(`Error deleting regular income with id ${id}:`, error);
    throw error;
  }
};

// TODO:
// - Exchange rate kaydı için fonksiyon (ödeme alındığında)
// - Önceki ödemelerle karşılaştırma için yardımcı fonksiyonlar
// - Sonraki ödeme tarihini hesaplama mantığı (Bu serviste veya ayrı bir utility'de olabilir)
