/**
 * <PERSON><PERSON><PERSON>leme Listesi Komponenti
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md - Stage 3, Phase 2
 * 
 * <PERSON><PERSON><PERSON> bazlı bütçe ilerleme listesi
 * Maksimum 250 satır - Tek sorumluluk prensibi
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../../../context/ThemeContext';
import BudgetProgressBar from './BudgetProgressBar';

/**
 * Kategori ilerleme listesi komponenti
 * @param {Object} props - Component props
 * @param {Array} props.categories - Kate<PERSON>i listesi
 * @param {Object} props.categorySpending - <PERSON><PERSON>i harcamal<PERSON>ı {categoryId: amount}
 * @param {Object} props.categoryLimits - Kategori limitleri {categoryId: limit}
 * @param {string} props.currency - Para birimi ('TRY', 'USD', 'EUR')
 * @param {Function} props.onCategoryPress - Kategori tıklama callback
 * @param {boolean} props.showEmptyCategories - Boş kategoriler gösterilsin mi
 * @param {Object} props.theme - Tema objesi (opsiyonel, context'ten alınır)
 */
const CategoryProgressList = ({ 
  categories = [], 
  categorySpending = {}, 
  categoryLimits = {},
  currency = 'TRY',
  onCategoryPress,
  showEmptyCategories = false,
  theme: propTheme 
}) => {
  const { theme, isDarkMode } = useTheme();
  const currentTheme = propTheme || theme;

  /**
   * Para birimi sembolü
   * @returns {string} Para birimi sembolü
   */
  const getCurrencySymbol = () => {
    switch (currency) {
      case 'USD':
        return '$';
      case 'EUR':
        return '€';
      default:
        return '₺';
    }
  };

  /**
   * Kategori verilerini hazırla
   * @returns {Array} Hazırlanmış kategori listesi
   */
  const prepareCategories = () => {
    return categories
      .map(category => {
        const spent = categorySpending[category.id] || 0;
        const limit = categoryLimits[category.id] || 0;
        const percentage = limit > 0 ? (spent / limit) * 100 : 0;
        
        return {
          ...category,
          spent,
          limit,
          percentage,
          remaining: Math.max(limit - spent, 0),
          isOverBudget: spent > limit
        };
      })
      .filter(category => showEmptyCategories || category.limit > 0)
      .sort((a, b) => b.percentage - a.percentage); // En yüksek yüzde önce
  };

  /**
   * Kategori durum rengi belirleme
   * @param {number} percentage - Harcama yüzdesi
   * @returns {string} Durum rengi
   */
  const getCategoryStatusColor = (percentage) => {
    if (percentage >= 100) return currentTheme.ERROR;
    if (percentage >= 90) return currentTheme.WARNING;
    if (percentage >= 75) return currentTheme.INFO;
    return currentTheme.SUCCESS;
  };

  /**
   * Kategori listesi render fonksiyonu
   * @param {Object} item - Kategori objesi
   * @returns {JSX.Element} Kategori list item
   */
  const renderCategoryItem = ({ item }) => {
    const statusColor = getCategoryStatusColor(item.percentage);
    const currencySymbol = getCurrencySymbol();

    return (
      <TouchableOpacity
        style={[styles.categoryItem, { backgroundColor: currentTheme.SURFACE }]}
        onPress={() => onCategoryPress && onCategoryPress(item)}
        activeOpacity={0.7}
      >
        {/* Kategori header */}
        <View style={styles.categoryHeader}>
          <View style={styles.categoryInfo}>
            <View style={[styles.categoryIcon, { backgroundColor: currentTheme.PRIMARY + '20' }]}>
              <MaterialIcons 
                name={item.icon || 'category'} 
                size={20} 
                color={currentTheme.PRIMARY} 
              />
            </View>
            <View style={styles.categoryDetails}>
              <Text style={[styles.categoryName, { color: currentTheme.TEXT_PRIMARY }]}>
                {item.name}
              </Text>
              <Text style={[styles.categoryAmount, { color: currentTheme.TEXT_SECONDARY }]}>
                {currencySymbol}{item.spent.toLocaleString('tr-TR')} / {currencySymbol}{item.limit.toLocaleString('tr-TR')}
              </Text>
            </View>
          </View>

          <View style={styles.categoryStatus}>
            <Text style={[styles.percentageText, { color: statusColor }]}>
              %{item.percentage.toFixed(1)}
            </Text>
            {item.isOverBudget && (
              <MaterialIcons name="warning" size={16} color={currentTheme.ERROR} />
            )}
          </View>
        </View>

        {/* İlerleme çubuğu */}
        <View style={styles.progressContainer}>
          <View style={[styles.progressTrack, { backgroundColor: currentTheme.BORDER }]}>
            <View 
              style={[
                styles.progressFill,
                {
                  backgroundColor: statusColor,
                  width: `${Math.min(item.percentage, 100)}%`
                }
              ]} 
            />
            
            {/* Aşım göstergesi */}
            {item.isOverBudget && (
              <View 
                style={[
                  styles.overBudgetIndicator,
                  {
                    backgroundColor: currentTheme.ERROR + '40',
                    width: `${Math.min(item.percentage - 100, 50)}%`,
                    left: '100%'
                  }
                ]} 
              />
            )}
          </View>
        </View>

        {/* Kalan miktar */}
        <View style={styles.remainingInfo}>
          <Text style={[styles.remainingLabel, { color: currentTheme.TEXT_SECONDARY }]}>
            {item.isOverBudget ? 'Aşım:' : 'Kalan:'}
          </Text>
          <Text style={[
            styles.remainingAmount, 
            { color: item.isOverBudget ? currentTheme.ERROR : currentTheme.SUCCESS }
          ]}>
            {currencySymbol}{(item.isOverBudget ? item.spent - item.limit : item.remaining).toLocaleString('tr-TR')}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  /**
   * Boş liste komponenti
   */
  const EmptyListComponent = () => (
    <View style={styles.emptyContainer}>
      <MaterialIcons name="category" size={48} color={currentTheme.TEXT_SECONDARY} />
      <Text style={[styles.emptyTitle, { color: currentTheme.TEXT_PRIMARY }]}>
        Kategori bulunamadı
      </Text>
      <Text style={[styles.emptySubtitle, { color: currentTheme.TEXT_SECONDARY }]}>
        Henüz kategori bazlı bütçe bulunmuyor
      </Text>
    </View>
  );

  /**
   * Liste header komponenti
   */
  const ListHeaderComponent = () => {
    const preparedCategories = prepareCategories();
    const totalSpent = preparedCategories.reduce((sum, cat) => sum + cat.spent, 0);
    const totalLimit = preparedCategories.reduce((sum, cat) => sum + cat.limit, 0);
    const overBudgetCount = preparedCategories.filter(cat => cat.isOverBudget).length;
    const currencySymbol = getCurrencySymbol();

    return (
      <View style={styles.headerContainer}>
        <Text style={[styles.headerTitle, { color: currentTheme.TEXT_PRIMARY }]}>
          Kategori Bütçeleri
        </Text>
        
        <View style={styles.summaryCards}>
          <View style={[styles.summaryCard, { backgroundColor: currentTheme.PRIMARY + '20' }]}>
            <Text style={[styles.summaryLabel, { color: currentTheme.TEXT_SECONDARY }]}>
              Toplam Harcama
            </Text>
            <Text style={[styles.summaryValue, { color: currentTheme.PRIMARY }]}>
              {currencySymbol}{totalSpent.toLocaleString('tr-TR')}
            </Text>
          </View>

          {overBudgetCount > 0 && (
            <View style={[styles.summaryCard, { backgroundColor: currentTheme.ERROR + '20' }]}>
              <Text style={[styles.summaryLabel, { color: currentTheme.TEXT_SECONDARY }]}>
                Aşan Kategori
              </Text>
              <Text style={[styles.summaryValue, { color: currentTheme.ERROR }]}>
                {overBudgetCount} kategori
              </Text>
            </View>
          )}
        </View>
      </View>
    );
  };

  const preparedCategories = prepareCategories();

  return (
    <View style={[styles.container, { backgroundColor: currentTheme.BACKGROUND }]}>
      <FlatList
        data={preparedCategories}
        renderItem={renderCategoryItem}
        keyExtractor={(item) => item.id.toString()}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContent}
        ListHeaderComponent={ListHeaderComponent}
        ListEmptyComponent={EmptyListComponent}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  listContent: {
    paddingBottom: 16,
  },
  headerContainer: {
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  summaryCards: {
    flexDirection: 'row',
    gap: 12,
  },
  summaryCard: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  categoryItem: {
    marginHorizontal: 16,
    marginVertical: 6,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  categoryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 12,
  },
  categoryIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoryDetails: {
    flex: 1,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  categoryAmount: {
    fontSize: 13,
  },
  categoryStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  percentageText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  progressContainer: {
    marginBottom: 8,
  },
  progressTrack: {
    height: 8,
    borderRadius: 4,
    overflow: 'hidden',
    position: 'relative',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  overBudgetIndicator: {
    position: 'absolute',
    top: 0,
    height: '100%',
    borderRadius: 4,
  },
  remainingInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  remainingLabel: {
    fontSize: 12,
  },
  remainingAmount: {
    fontSize: 14,
    fontWeight: '500',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default CategoryProgressList;
