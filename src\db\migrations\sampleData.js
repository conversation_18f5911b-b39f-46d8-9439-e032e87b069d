/**
 * Örnek veri ekleme scripti
 */

/**
 * Örnek işlem verileri ekler
 * @param {Object} db - Database instance
 */
export const insertSampleTransactions = async (db) => {
  try {
    console.log('🎯 Örnek işlem verileri ekleniyor...');
    
    const sampleTransactions = [
      {
        date: '2024-07-01',
        category: 'Gıda',
        description: 'Market alışverişi',
        amount: 150.50,
        type: 'expense'
      },
      {
        date: '2024-07-02',
        category: 'Ulaşım',
        description: 'Otobüs kartı',
        amount: 50.00,
        type: 'expense'
      },
      {
        date: '2024-07-03',
        category: 'Maaş',
        description: 'Aylık maaş',
        amount: 15000.00,
        type: 'income'
      },
      {
        date: '2024-07-04',
        category: 'Eğlence',
        description: 'Sinema',
        amount: 45.00,
        type: 'expense'
      },
      {
        date: '2024-07-05',
        category: 'Sağlık',
        description: '<PERSON>czane',
        amount: 25.75,
        type: 'expense'
      }
    ];

    for (const transaction of sampleTransactions) {
      await db.runAsync(
        'INSERT INTO transactions (date, category, description, amount, type) VALUES (?, ?, ?, ?, ?)',
        [transaction.date, transaction.category, transaction.description, transaction.amount, transaction.type]
      );
    }

    console.log('✅ Örnek işlem verileri başarıyla eklendi');
  } catch (error) {
    console.error('❌ Örnek işlem verileri eklenirken hata:', error);
  }
};

/**
 * Örnek maaş verileri ekler
 * @param {Object} db - Database instance
 */
export const insertSampleSalaries = async (db) => {
  try {
    console.log('🎯 Örnek maaş verileri ekleniyor...');
    
    const sampleSalaries = [
      {
        payment_date: '2024-06-30',
        work_days: 22,
        gross_salary: 18000.00,
        net_salary: 15000.00,
        tax: 2500.00,
        insurance: 500.00
      },
      {
        payment_date: '2024-05-31',
        work_days: 21,
        gross_salary: 17500.00,
        net_salary: 14500.00,
        tax: 2400.00,
        insurance: 600.00
      }
    ];

    for (const salary of sampleSalaries) {
      await db.runAsync(
        'INSERT INTO salaries (payment_date, work_days, gross_salary, net_salary, tax, insurance) VALUES (?, ?, ?, ?, ?, ?)',
        [salary.payment_date, salary.work_days, salary.gross_salary, salary.net_salary, salary.tax, salary.insurance]
      );
    }

    console.log('✅ Örnek maaş verileri başarıyla eklendi');
  } catch (error) {
    console.error('❌ Örnek maaş verileri eklenirken hata:', error);
  }
};

/**
 * Örnek mesai verileri ekler
 * @param {Object} db - Database instance
 */
export const insertSampleOvertimes = async (db) => {
  try {
    console.log('🎯 Örnek mesai verileri ekleniyor...');
    
    const sampleOvertimes = [
      {
        shift_date: '2024-07-01',
        shift_type: 'gece',
        hours_worked: 8,
        hourly_rate: 75.00,
        total_payment: 600.00
      },
      {
        shift_date: '2024-07-02',
        shift_type: 'gündüz',
        hours_worked: 4,
        hourly_rate: 60.00,
        total_payment: 240.00
      }
    ];

    for (const overtime of sampleOvertimes) {
      await db.runAsync(
        'INSERT INTO overtime_shifts (shift_date, shift_type, hours_worked, hourly_rate, total_payment) VALUES (?, ?, ?, ?, ?)',
        [overtime.shift_date, overtime.shift_type, overtime.hours_worked, overtime.hourly_rate, overtime.total_payment]
      );
    }

    console.log('✅ Örnek mesai verileri başarıyla eklendi');
  } catch (error) {
    console.error('❌ Örnek mesai verileri eklenirken hata:', error);
  }
};

/**
 * Tüm örnek verileri ekler
 * @param {Object} db - Database instance
 */
export const insertAllSampleData = async (db) => {
  try {
    console.log('🎯 Tüm örnek veriler ekleniyor...');
    
    await insertSampleTransactions(db);
    await insertSampleSalaries(db);
    await insertSampleOvertimes(db);
    
    console.log('🎉 Tüm örnek veriler başarıyla eklendi!');
  } catch (error) {
    console.error('❌ Örnek veriler eklenirken hata:', error);
  }
};
