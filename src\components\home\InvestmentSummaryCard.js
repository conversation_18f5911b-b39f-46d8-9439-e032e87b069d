import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { PieChart } from 'react-native-chart-kit';
import { Dimensions } from 'react-native';
import { formatCurrency } from '../../utils/formatters';
import { COLORS, LAYOUT, TYPOGRAPHY } from '../../constants/themes';

/**
 * Yatırım özet kartı bileşeni
 * 
 * @param {Object} props Bileşen özellikleri
 * @param {Array} props.data Yatırım verileri
 * @param {number} props.totalValue Toplam değer
 * @param {Function} props.onViewAll Tümünü görüntüleme fonksiyonu
 * @param {boolean} props.loading Yükleniyor durumu
 * @returns {JSX.Element} InvestmentSummaryCard bileşeni
 */
const InvestmentSummaryCard = ({
  data = [],
  totalValue = 0,
  onViewAll,
  loading = false
}) => {
  if (loading) {
    return (
      <View style={[styles.container, styles.loadingContainer]}>
        <View style={styles.loadingHeader} />
        <View style={styles.loadingChart} />
        <View style={styles.loadingLegend} />
      </View>
    );
  }

  if (!data || data.length === 0) {
    return (
      <View style={[styles.container, styles.emptyContainer]}>
        <MaterialIcons name="show-chart" size={48} color={COLORS.neutral[300]} />
        <Text style={styles.emptyText}>Henüz yatırım verisi bulunmuyor</Text>
        
        {onViewAll && (
          <TouchableOpacity 
            style={styles.addButton}
            onPress={onViewAll}
          >
            <Text style={styles.addButtonText}>Yatırım Ekle</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  }

  const chartConfig = {
    color: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
    strokeWidth: 2,
  };

  const chartWidth = Dimensions.get('window').width - 64;

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Yatırım Dağılımı</Text>
        {onViewAll && (
          <TouchableOpacity onPress={onViewAll}>
            <Text style={styles.viewAllText}>Tümünü Gör</Text>
          </TouchableOpacity>
        )}
      </View>

      <View style={styles.chartContainer}>
        <PieChart
          data={data}
          width={chartWidth}
          height={160}
          chartConfig={chartConfig}
          accessor="value"
          backgroundColor="transparent"
          paddingLeft="15"
          absolute
        />
      </View>

      <View style={styles.legendContainer}>
        {data.slice(0, 3).map((item) => (
          <View key={item.id || item.name} style={styles.legendItem}>
            <View 
              style={[
                styles.legendColorBox, 
                { backgroundColor: item.color }
              ]} 
            />
            <View style={styles.legendTextContainer}>
              <Text style={styles.legendName} numberOfLines={1}>{item.name}</Text>
              <Text style={styles.legendValue}>{formatCurrency(item.value)}</Text>
            </View>
            <Text style={styles.legendPercentage}>{item.percentage || 0}%</Text>
          </View>
        ))}
        
        {data.length > 3 && (
          <Text style={styles.moreText}>
            +{data.length - 3} daha fazla
          </Text>
        )}
      </View>
      
      <View style={styles.totalContainer}>
        <Text style={styles.totalLabel}>Toplam Portföy Değeri</Text>
        <Text style={styles.totalValue}>{formatCurrency(totalValue)}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: #FFFFFF,
    borderRadius: LAYOUT.radius.lg,
    padding: LAYOUT.spacing.lg,
    marginHorizontal: LAYOUT.spacing.md,
    marginVertical: LAYOUT.spacing.sm,
    ...LAYOUT.shadow.sm
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: LAYOUT.spacing.md
  },
  title: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: TYPOGRAPHY.fontWeight.semiBold,
    color: COLORS.neutral[800]
  },
  viewAllText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    fontWeight: TYPOGRAPHY.fontWeight.medium,
    color: Colors.PRIMARY.main
  },
  chartContainer: {
    alignItems: 'center',
    height: 160,
    marginBottom: LAYOUT.spacing.md
  },
  legendContainer: {
    marginTop: LAYOUT.spacing.md
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: LAYOUT.spacing.sm
  },
  legendColorBox: {
    width: 12,
    height: 12,
    borderRadius: LAYOUT.radius.sm,
    marginRight: LAYOUT.spacing.sm
  },
  legendTextContainer: {
    flex: 1,
    marginRight: LAYOUT.spacing.sm
  },
  legendName: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: #374151
  },
  legendValue: {
    fontSize: TYPOGRAPHY.fontSize.xs,
    color: #6B7280,
    marginTop: 2
  },
  legendPercentage: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    fontWeight: TYPOGRAPHY.fontWeight.semiBold,
    color: #374151
  },
  moreText: {
    fontSize: TYPOGRAPHY.fontSize.xs,
    color: Colors.PRIMARY.main,
    fontWeight: TYPOGRAPHY.fontWeight.medium,
    textAlign: 'center',
    marginTop: LAYOUT.spacing.sm
  },
  totalContainer: {
    marginTop: LAYOUT.spacing.md,
    paddingTop: LAYOUT.spacing.md,
    borderTopWidth: 1,
    borderTopColor: COLORS.neutral[100],
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  totalLabel: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: #4B5563
  },
  totalValue: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: TYPOGRAPHY.fontWeight.semiBold,
    color: COLORS.neutral[800]
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: LAYOUT.spacing.xl,
    minHeight: 200
  },
  emptyText: {
    fontSize: TYPOGRAPHY.fontSize.md,
    color: #6B7280,
    marginTop: LAYOUT.spacing.md,
    marginBottom: LAYOUT.spacing.lg
  },
  addButton: {
    backgroundColor: Colors.PRIMARY.main,
    paddingVertical: LAYOUT.spacing.sm,
    paddingHorizontal: LAYOUT.spacing.lg,
    borderRadius: LAYOUT.radius.md
  },
  addButtonText: {
    color: #FFFFFF,
    fontSize: TYPOGRAPHY.fontSize.sm,
    fontWeight: TYPOGRAPHY.fontWeight.semiBold
  },
  loadingContainer: {
    minHeight: 300
  },
  loadingHeader: {
    width: '100%',
    height: 24,
    backgroundColor: COLORS.neutral[200],
    borderRadius: LAYOUT.radius.md,
    marginBottom: LAYOUT.spacing.lg
  },
  loadingChart: {
    width: '100%',
    height: 160,
    backgroundColor: COLORS.neutral[100],
    borderRadius: LAYOUT.radius.md,
    marginBottom: LAYOUT.spacing.lg
  },
  loadingLegend: {
    width: '100%',
    height: 80,
    backgroundColor: COLORS.neutral[200],
    borderRadius: LAYOUT.radius.md
  }
});

export default InvestmentSummaryCard;
