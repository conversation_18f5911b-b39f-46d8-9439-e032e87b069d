/**
 * Database Test Utility
 * Database bağlantısını ve temel işlemleri test eder
 */

/**
 * Database bağlantısını test eder
 * @param {Object} db - SQLite database instance
 */
export const testDatabaseConnection = async (db) => {
  try {
    console.log('🔍 Database bağlantısı test ediliyor...');

    // Basit bir SQL sorgusu ile bağlantıyı test et
    const result = await db.getFirstAsync('SELECT 1 as test');
    console.log('✅ Database bağlantısı çalışıyor:', result);

    // Tabloları listele
    const tables = await db.getAllAsync(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name NOT LIKE 'sqlite_%'
      ORDER BY name
    `);
    console.log('📋 Mevcut tablolar:', tables.map(t => t.name));

    // Categories tablosunu kontrol et
    const categoriesInfo = await db.getAllAsync("PRAGMA table_info(categories)");
    console.log('📊 Categories tablo yapısı:', categoriesInfo.map(c => c.name));

    // Transactions tablosunu kontrol et
    const transactionsInfo = await db.getAllAsync("PRAGMA table_info(transactions)");
    console.log('💳 Transactions tablo yapısı:', transactionsInfo.map(c => c.name));

    // Budget tablolarını kontrol et
    const budgetTables = tables.filter(t => t.name.includes('budget'));
    console.log('💰 Budget tabloları:', budgetTables.map(t => t.name));

    return {
      success: true,
      tables: tables.map(t => t.name),
      categoriesColumns: categoriesInfo.map(c => c.name),
      transactionsColumns: transactionsInfo.map(c => c.name)
    };
  } catch (error) {
    console.error('❌ Database test hatası:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Temel category verilerini test eder
 */
export const testCategoryData = async (db) => {
  try {
    console.log('🔍 Category verileri test ediliyor...');

    const categories = await db.getAllAsync('SELECT * FROM categories LIMIT 5');
    console.log('📝 İlk 5 kategori:', categories);

    return {
      success: true,
      categories
    };
  } catch (error) {
    console.error('❌ Category test hatası:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Budget tablolarını test eder
 */
export const testBudgetTables = async (db) => {
  try {
    console.log('🔍 Budget tabloları test ediliyor...');

    // Budget enhanced tablosu kontrol
    const budgets = await db.getAllAsync('SELECT * FROM budgets_enhanced LIMIT 3');
    console.log('💰 Budget enhanced verileri:', budgets);

    return {
      success: true,
      budgets
    };
  } catch (error) {
    console.error('❌ Budget test hatası:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Tam database sistem testi
 */
export const runFullDatabaseTest = async (db) => {
  console.log('🚀 Tam database sistem testi başlatılıyor...');

  const connectionTest = await testDatabaseConnection(db);
  const categoryTest = await testCategoryData(db);
  const budgetTest = await testBudgetTables(db);

  return {
    connection: connectionTest,
    categories: categoryTest,
    budgets: budgetTest,
    overall: connectionTest.success && categoryTest.success && budgetTest.success
  };
};
