import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

/**
 * Filter Widget - Filtre kontrolleri için widget
 */
const FilterWidget = ({ widget, isPreviewMode, onUpdate, theme }) => {
  /**
   * G<PERSON><PERSON>li tema değeri alma
   * @param {string} property - <PERSON><PERSON>
   * @param {string} fallback - <PERSON><PERSON><PERSON><PERSON><PERSON>
   * @returns {string} G<PERSON><PERSON>li tema değeri
   */
  const getSafeThemeValue = (property, fallback) => {
    return theme[property] || theme.colors?.[property.toLowerCase()] || fallback;
  };

  return (
    <View style={[styles.container, { backgroundColor: getSafeThemeValue('BACKGROUND', '#fff') }]}>
      <View style={[styles.filterArea, { borderColor: getSafeThemeValue('BORDER', '#e0e0e0') }]}>
        <Text style={[styles.filterIcon, { color: getSafeThemeValue('ACCENT', '#6f42c1') }]}>
          🔍
        </Text>
        <Text style={[styles.filterLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666') }]}>
          Filter Control
        </Text>
        <Text style={[styles.filterSubLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666') }]}>
          {widget.config?.filterType || 'Date Range'}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 4,
  },
  filterArea: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderStyle: 'dashed',
    borderRadius: 4,
  },
  filterIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  filterLabel: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 4,
  },
  filterSubLabel: {
    fontSize: 10,
  },
});

export default FilterWidget;
