import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';

/**
 * Son işlemler kartı bileşeni
 *
 * @param {Object} props - Bileşen props'ları
 * @param {Array} props.transactions - İşlemler listesi
 * @param {Function} props.onViewAll - Tümünü görüntüle butonuna tıklama olayı
 * @param {Function} props.onItemPress - İşlem öğesine tıklama olayı
 * @returns {JSX.Element} RecentTransactionsCard bileşeni
 */
const RecentTransactionsCard = ({ transactions = [], onViewAll, onItemPress }) => {
  // Para birimini formatla
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 2
    }).format(amount);
  };

  // Tarihi formatla
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return format(date, 'd MMMM yyyy', { locale: tr });
  };

  // Eğer işlem yoksa boş bir kart göster
  if (!transactions || transactions.length === 0) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Son İşlemler</Text>
          <TouchableOpacity onPress={onViewAll}>
            <Text style={styles.seeAll}>Tümünü Gör</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.emptyContainer}>
          <MaterialIcons name="receipt-long" size={48} color="#ddd" />
          <Text style={styles.emptyText}>Henüz işlem bulunmuyor</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Son İşlemler</Text>
        <TouchableOpacity onPress={onViewAll}>
          <Text style={styles.seeAll}>Tümünü Gör</Text>
        </TouchableOpacity>
      </View>

      <FlatList
        data={transactions.slice(0, 5)}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={styles.transactionItem}
            onPress={() => onItemPress(item.id)}
          >
            <MaterialIcons
              name={item.is_income ? 'add-circle' : 'remove-circle'}
              size={24}
              color={item.is_income ? '#2ecc71' : '#e74c3c'}
            />

            <View style={styles.transactionDetails}>
              <View style={styles.transactionHeader}>
                {item.category_name && (
                  <View style={[
                    styles.categoryContainer,
                    { backgroundColor: item.category_color ? `${item.category_color}20` : '#f8f9fa' }
                  ]}>
                    <MaterialIcons
                      name={item.category_icon || 'category'}
                      size={16}
                      color={item.category_color || '#6c757d'}
                    />
                    <Text style={styles.categoryText}>
                      {item.category_name}
                    </Text>
                  </View>
                )}
              </View>

              {item.description && (
                <Text style={styles.description} numberOfLines={1}>
                  {item.description}
                </Text>
              )}

              <Text style={styles.date}>{formatDate(item.transaction_date)}</Text>
            </View>

            <View style={styles.rightContent}>
              <Text style={[
                styles.amount,
                { color: item.is_income ? '#2ecc71' : '#e74c3c' }
              ]}>
                {item.is_income ? '+' : '-'}{formatCurrency(item.amount)}
              </Text>
            </View>
          </TouchableOpacity>
        )}
        keyExtractor={(item) => item.id.toString()}
        scrollEnabled={false}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    margin: 16,
    marginTop: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  seeAll: {
    color: '#3498db',
    fontSize: 14,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 32,
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
    color: '#999',
  },
  transactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },
  transactionDetails: {
    flex: 1,
    marginLeft: 12,
  },
  transactionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  categoryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  categoryText: {
    fontSize: 12,
    color: '#495057',
    marginLeft: 4,
  },
  description: {
    fontSize: 14,
    color: '#212529',
    marginBottom: 4,
  },
  date: {
    fontSize: 12,
    color: '#868e96',
  },
  rightContent: {
    alignItems: 'flex-end',
  },
  amount: {
    fontSize: 16,
    fontWeight: '500',
  },
  separator: {
    height: 1,
    backgroundColor: '#f0f0f0',
  },
});

export default RecentTransactionsCard;
