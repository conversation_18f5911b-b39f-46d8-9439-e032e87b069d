import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { useTheme } from '../../../../context/ThemeContext';

/**
 * Tablo Builder Header Bileşeni
 * Progress ve kapama butonunu içerir
 * MAX 50 satır - Modularization Rule
 */
const TableBuilderHeader = ({ 
  title = 'Tablo Oluşturucu', 
  currentStep = 1, 
  totalSteps = 4, 
  onClose
}) => {
  const { theme } = useTheme();
  
  // Tema güvenlik kontrolü
  if (!theme) {
    return null;
  }

  const progressPercentage = (currentStep / totalSteps) * 100;

  return (
    <View style={[styles.container, { backgroundColor: theme.SURFACE || theme.colors?.surface || '#f9f9f9' }]}>
      <View style={styles.titleContainer}>
        <Text style={[styles.title, { color: theme.TEXT_PRIMARY || theme.colors?.text || '#333' }]}>
          {title}
        </Text>
        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <Text style={[styles.closeText, { color: theme.TEXT_SECONDARY || theme.colors?.textSecondary || '#666' }]}>
            ✕
          </Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.progressContainer}>
        <Text style={[styles.stepText, { color: theme.TEXT_SECONDARY || theme.colors?.textSecondary || '#666' }]}>
          Adım {currentStep} / {totalSteps}
        </Text>
        <View style={[styles.progressBar, { backgroundColor: theme.BORDER || theme.colors?.border || '#e0e0e0' }]}>
          <View 
            style={[
              styles.progressFill, 
              { 
                backgroundColor: theme.PRIMARY || theme.colors?.primary || '#007AFF',
                width: `${progressPercentage}%`
              }
            ]} 
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 4,
  },
  closeText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  progressContainer: {
    gap: 8,
  },
  stepText: {
    fontSize: 14,
  },
  progressBar: {
    height: 4,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
});

export default TableBuilderHeader;
