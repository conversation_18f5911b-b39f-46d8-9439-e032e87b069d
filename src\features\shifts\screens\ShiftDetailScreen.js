import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../../../constants/colors';
import { shiftStyles } from '../styles/shiftStyles';
import { formatDate, formatTime, calculateDuration, formatCurrency } from '../utils/shiftUtils';
import * as shiftService from '../services/shiftService';
import ShiftFormModal from '../components/ShiftFormModal';

/**
 * Vardiya Detay Ekranı
 * 
 * Bu ekran, bir vardiyaya ait detaylı bilgileri gösterir.
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @param {Object} props.route - Route nesnesi
 * @returns {JSX.Element} Vardiya detay ekranı
 */
const ShiftDetailScreen = ({ navigation, route }) => {
  const { shiftId } = route.params;
  const db = useSQLiteContext();
  const insets = useSafeAreaInsets();
  
  // Durum değişkenleri
  const [loading, setLoading] = useState(true);
  const [shift, setShift] = useState(null);
  const [shiftType, setShiftType] = useState(null);
  const [settings, setSettings] = useState(null);
  
  // Modal durumları
  const [showEditModal, setShowEditModal] = useState(false);
  
  // Verileri yükle
  useEffect(() => {
    loadData();
  }, [shiftId]);
  
  // Verileri yükle
  const loadData = async () => {
    try {
      setLoading(true);
      
      // Vardiyayı yükle
      const shiftData = await shiftService.getShift(db, shiftId);
      setShift(shiftData);
      
      // Vardiya türünü yükle
      if (shiftData.shift_type_id) {
        const types = await shiftService.getShiftTypes(db);
        const type = types.find(t => t.id === shiftData.shift_type_id);
        setShiftType(type);
      }
      
      // Ayarları yükle
      const workSettings = await shiftService.getWorkSettings(db);
      setSettings(workSettings);
      
      setLoading(false);
    } catch (error) {
      console.error('Veri yükleme hatası:', error);
      Alert.alert('Hata', 'Veriler yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  };
  
  // Vardiya güncelle
  const handleUpdateShift = async (id, shiftData) => {
    try {
      await shiftService.updateShift(db, id, shiftData);
      Alert.alert('Başarılı', 'Vardiya başarıyla güncellendi.');
      loadData();
    } catch (error) {
      console.error('Vardiya güncelleme hatası:', error);
      Alert.alert('Hata', 'Vardiya güncellenirken bir hata oluştu.');
    }
  };
  
  // Vardiya sil
  const handleDeleteShift = () => {
    Alert.alert(
      'Vardiya Sil',
      'Bu vardiyayı silmek istediğinize emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await shiftService.deleteShift(db, shiftId);
              Alert.alert('Başarılı', 'Vardiya başarıyla silindi.');
              navigation.goBack();
            } catch (error) {
              console.error('Vardiya silme hatası:', error);
              Alert.alert('Hata', 'Vardiya silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };
  
  // Vardiya durumunu güncelle
  const handleUpdateStatus = async (status) => {
    try {
      await shiftService.updateShift(db, shiftId, { status });
      Alert.alert('Başarılı', 'Vardiya durumu başarıyla güncellendi.');
      loadData();
    } catch (error) {
      console.error('Vardiya durumu güncelleme hatası:', error);
      Alert.alert('Hata', 'Vardiya durumu güncellenirken bir hata oluştu.');
    }
  };
  
  if (loading) {
    return (
      <View style={[shiftStyles.loadingContainer, { paddingTop: insets.top }]}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={shiftStyles.loadingText}>Vardiya yükleniyor...</Text>
      </View>
    );
  }
  
  if (!shift) {
    return (
      <View style={[shiftStyles.emptyState, { paddingTop: insets.top }]}>
        <MaterialIcons name="error-outline" size={48} color={Colors.GRAY_400} />
        <Text style={shiftStyles.emptyStateText}>Vardiya bulunamadı</Text>
        <TouchableOpacity
          style={[shiftStyles.button, shiftStyles.primaryButton, { marginTop: 16 }]}
          onPress={() => navigation.goBack()}
        >
          <Text style={shiftStyles.primaryButtonText}>Geri Dön</Text>
        </TouchableOpacity>
      </View>
    );
  }
  
  // Vardiya süresi hesaplama
  const duration = calculateDuration(
    new Date(`${shift.date}T${shift.start_time}`),
    new Date(`${shift.date}T${shift.end_time}`),
    shift.break_duration
  );
  
  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      {/* Başlık */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Vardiya Detayı</Text>
        
        <View style={styles.headerButtons}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setShowEditModal(true)}
          >
            <MaterialIcons name="edit" size={24} color="#fff" />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.headerButton}
            onPress={handleDeleteShift}
          >
            <MaterialIcons name="delete" size={24} color="#fff" />
          </TouchableOpacity>
        </View>
      </View>
      
      <ScrollView style={styles.content}>
        {/* Durum Kartı */}
        <View style={styles.statusCard}>
          <View style={styles.statusHeader}>
            <Text style={styles.statusTitle}>Durum</Text>
            <View style={[
              styles.statusBadge,
              { backgroundColor: getStatusColor(shift.status) }
            ]}>
              <Text style={styles.statusText}>
                {getStatusText(shift.status)}
              </Text>
            </View>
          </View>
          
          <View style={styles.statusActions}>
            {shift.status !== 'active' && (
              <TouchableOpacity
                style={[styles.statusButton, { backgroundColor: '#4caf50' }]}
                onPress={() => handleUpdateStatus('active')}
              >
                <MaterialIcons name="play-arrow" size={16} color="#fff" />
                <Text style={styles.statusButtonText}>Başlat</Text>
              </TouchableOpacity>
            )}
            
            {shift.status === 'active' && (
              <TouchableOpacity
                style={[styles.statusButton, { backgroundColor: '#2196f3' }]}
                onPress={() => handleUpdateStatus('completed')}
              >
                <MaterialIcons name="check" size={16} color="#fff" />
                <Text style={styles.statusButtonText}>Tamamla</Text>
              </TouchableOpacity>
            )}
            
            {shift.status !== 'cancelled' && (
              <TouchableOpacity
                style={[styles.statusButton, { backgroundColor: '#f44336' }]}
                onPress={() => handleUpdateStatus('cancelled')}
              >
                <MaterialIcons name="close" size={16} color="#fff" />
                <Text style={styles.statusButtonText}>İptal Et</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
        
        {/* Vardiya Bilgileri */}
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Vardiya Bilgileri</Text>
          
          <View style={styles.infoRow}>
            <View style={styles.infoItem}>
              <MaterialIcons name="event" size={20} color="#666" />
              <Text style={styles.infoLabel}>Tarih</Text>
              <Text style={styles.infoValue}>{formatDate(shift.date)}</Text>
            </View>
          </View>
          
          <View style={styles.infoRow}>
            <View style={styles.infoItem}>
              <MaterialIcons name="access-time" size={20} color="#666" />
              <Text style={styles.infoLabel}>Başlangıç</Text>
              <Text style={styles.infoValue}>{formatTime(new Date(`${shift.date}T${shift.start_time}`))}</Text>
            </View>
            
            <View style={styles.infoItem}>
              <MaterialIcons name="access-time" size={20} color="#666" />
              <Text style={styles.infoLabel}>Bitiş</Text>
              <Text style={styles.infoValue}>{formatTime(new Date(`${shift.date}T${shift.end_time}`))}</Text>
            </View>
          </View>
          
          <View style={styles.infoRow}>
            <View style={styles.infoItem}>
              <MaterialIcons name="timelapse" size={20} color="#666" />
              <Text style={styles.infoLabel}>Süre</Text>
              <Text style={styles.infoValue}>{duration.toFixed(2)} saat</Text>
            </View>
            
            <View style={styles.infoItem}>
              <MaterialIcons name="free-breakfast" size={20} color="#666" />
              <Text style={styles.infoLabel}>Mola</Text>
              <Text style={styles.infoValue}>{shift.break_duration} dakika</Text>
            </View>
          </View>
          
          {shiftType && (
            <View style={styles.shiftTypeContainer}>
              <View style={[styles.colorDot, { backgroundColor: shiftType.color }]} />
              <Text style={styles.shiftTypeName}>{shiftType.name}</Text>
            </View>
          )}
          
          <View style={styles.tagsContainer}>
            {shift.is_overtime === 1 && (
              <View style={[styles.tag, { backgroundColor: Colors.WARNING }]}>
                <MaterialIcons name="alarm-on" size={12} color="#fff" />
                <Text style={styles.tagText}>Mesai</Text>
              </View>
            )}
            
            {shift.is_holiday === 1 && (
              <View style={[styles.tag, { backgroundColor: '#e74c3c' }]}>
                <MaterialIcons name="event" size={12} color="#fff" />
                <Text style={styles.tagText}>Tatil Günü</Text>
              </View>
            )}
          </View>
        </View>
        
        {/* Ücret Bilgileri */}
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Ücret Bilgileri</Text>
          
          <View style={styles.infoRow}>
            <View style={styles.infoItem}>
              <MaterialIcons name="attach-money" size={20} color="#666" />
              <Text style={styles.infoLabel}>Saatlik Ücret</Text>
              <Text style={styles.infoValue}>{formatCurrency(shift.hourly_rate)}</Text>
            </View>
          </View>
          
          <View style={styles.earningsContainer}>
            <Text style={styles.earningsLabel}>Toplam Kazanç</Text>
            <Text style={styles.earningsValue}>{formatCurrency(shift.earnings)}</Text>
          </View>
        </View>
        
        {/* Notlar */}
        {shift.notes && (
          <View style={styles.card}>
            <Text style={styles.cardTitle}>Notlar</Text>
            <Text style={styles.notesText}>{shift.notes}</Text>
          </View>
        )}
      </ScrollView>
      
      {/* Vardiya Düzenleme Modalı */}
      <ShiftFormModal
        visible={showEditModal}
        onClose={() => setShowEditModal(false)}
        onSave={handleUpdateShift}
        shift={shift}
        shiftTypes={shiftType ? [shiftType] : []}
        settings={settings}
      />
    </View>
  );
};

// Durum metni
const getStatusText = (status) => {
  switch (status) {
    case 'active':
      return 'Aktif';
    case 'completed':
      return 'Tamamlandı';
    case 'planned':
      return 'Planlandı';
    case 'cancelled':
      return 'İptal Edildi';
    default:
      return status;
  }
};

// Durum rengi
const getStatusColor = (status) => {
  switch (status) {
    case 'active':
      return '#4caf50'; // Yeşil
    case 'completed':
      return '#2196f3'; // Mavi
    case 'planned':
      return '#ff9800'; // Turuncu
    case 'cancelled':
      return '#f44336'; // Kırmızı
    default:
      return '#9e9e9e'; // Gri
  }
};

const styles = StyleSheet.create({
  container: {
    ...shiftStyles.container,
  },
  header: {
    ...shiftStyles.header,
  },
  headerTitle: {
    ...shiftStyles.headerTitle,
  },
  backButton: {
    ...shiftStyles.backButton,
  },
  headerButtons: {
    flexDirection: 'row',
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  statusCard: {
    ...shiftStyles.card,
    marginBottom: 16,
  },
  statusHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  statusTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#fff',
  },
  statusActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statusButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginHorizontal: 4,
  },
  statusButtonText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#fff',
    marginLeft: 4,
  },
  card: {
    ...shiftStyles.card,
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    paddingBottom: 8,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  infoItem: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  infoLabel: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 4,
  },
  shiftTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    marginBottom: 16,
    padding: 8,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
  },
  colorDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 8,
  },
  shiftTypeName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  tag: {
    ...shiftStyles.tag,
  },
  tagText: {
    ...shiftStyles.tagText,
  },
  earningsContainer: {
    backgroundColor: '#f9f9f9',
    borderRadius: 12,
    padding: 16,
    marginTop: 8,
    alignItems: 'center',
  },
  earningsLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  earningsValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.INCOME,
  },
  notesText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
  },
});

export default ShiftDetailScreen;
