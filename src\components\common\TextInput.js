import React from 'react';
import { View, TextInput as RNTextInput, Text, StyleSheet } from 'react-native';

/**
 * Özelleştirilmiş TextInput bileşeni
 * @param {Object} props Component props
 * @param {string} [props.label] Input etiketi
 * @param {string} [props.error] Hata mesajı
 * @param {boolean} [props.disabled] Devre dışı durumu
 * @param {Object} [props.containerStyle] Ka<PERSON><PERSON>ıcı stil
 * @param {Object} [props.inputStyle] Input stil
 * @param {Object} [props.labelStyle] Etiket stil
 * @param {string} [props.placeholder] Placeholder metni
 * @param {Function} props.onChangeText Değer değişim fonksiyonu
 * @param {string} props.value Input değeri
 */
const TextInput = ({
    label,
    error,
    disabled = false,
    containerStyle,
    inputStyle,
    labelStyle,
    placeholder = '',
    onChangeText,
    value,
    ...props
}) => {
    return (
        <View style={[styles.container, containerStyle]}>
            {label && (
                <Text style={[styles.label, labelStyle, error && styles.errorLabel]}>
                    {label}
                </Text>
            )}
            
            <RNTextInput
                style={[
                    styles.input,
                    inputStyle,
                    error && styles.inputError,
                    disabled && styles.inputDisabled
                ]}
                placeholderTextColor="#95a5a6"
                placeholder={placeholder}
                onChangeText={onChangeText}
                value={value}
                editable={!disabled}
                {...props}
            />
            
            {error && typeof error === 'string' && (
                <Text style={styles.errorText}>{error}</Text>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        marginBottom: 12,
    },
    label: {
        fontSize: 14,
        marginBottom: 6,
        color: '#2c3e50',
        fontWeight: '500',
    },
    input: {
        height: 48,
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        paddingHorizontal: 16,
        fontSize: 16,
        color: '#2c3e50',
        backgroundColor: '#fff',
    },
    inputError: {
        borderColor: '#e74c3c',
    },
    inputDisabled: {
        backgroundColor: '#f5f5f5',
        borderColor: '#ddd',
    },
    errorLabel: {
        color: '#e74c3c',
    },
    errorText: {
        color: '#e74c3c',
        fontSize: 12,
        marginTop: 4,
    }
});

export default TextInput;