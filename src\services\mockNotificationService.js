/**
 * Mock Bildirim Servisi
 * Expo Go'da bildirim desteği olmadığı için kullanılan mock servis
 */

// Tüm fonksiyonlar için boş mock uygulamaları
export const getPermissionsAsync = async () => ({ status: 'granted' });
export const requestPermissionsAsync = async () => ({ status: 'granted' });
export const setNotificationHandler = async () => {};
export const scheduleNotificationAsync = async () => 'mock-notification-id';
export const cancelScheduledNotificationAsync = async () => {};
export const cancelAllScheduledNotificationsAsync = async () => {};
export const getAllScheduledNotificationsAsync = async () => [];
export const dismissAllNotificationsAsync = async () => {};
export const getBadgeCountAsync = async () => 0;
export const setBadgeCountAsync = async () => {};
export const addNotificationReceivedListener = () => ({ remove: () => {} });
export const addNotificationResponseReceivedListener = () => ({ remove: () => {} });

// Android için sabitler
export const AndroidNotificationPriority = {
  MIN: 0,
  LOW: 1,
  DEFAULT: 2,
  HIGH: 3,
  MAX: 4
};

// Bildirim kanalları için mock
export const AndroidImportance = {
  MIN: 0,
  LOW: 1,
  DEFAULT: 2,
  HIGH: 3,
  MAX: 4
};

// Bildirim kategorileri için mock
export const setNotificationCategoryAsync = async () => {};
export const getNotificationCategoriesAsync = async () => [];

// Bildirim içeriği için mock
export const getDevicePushTokenAsync = async () => ({ data: 'mock-token', type: 'mock' });
export const getExpoPushTokenAsync = async () => 'mock-expo-token';

// Bildirim durumu için mock
export const getLastNotificationResponseAsync = async () => null;
export const getPresentedNotificationsAsync = async () => [];

// Bildirim kanalları için mock
export const setNotificationChannelAsync = async () => {};
export const getNotificationChannelsAsync = async () => [];
export const deleteNotificationChannelAsync = async () => {};

// Bildirim grupları için mock
export const createNotificationGroupAsync = async () => {};
export const deleteNotificationGroupAsync = async () => {};

// Bildirim içeriği için mock
export const getNotificationContentAsync = async () => ({
  title: 'Mock Bildirim',
  body: 'Bu bir mock bildirimdir',
  data: {}
});

// Bildirim tetikleyicileri için mock
export const getNotificationTriggerAsync = async () => null;

// Bildirim izinleri için mock
export const getPermissionStatusAsync = async () => 'granted';

// Bildirim ayarları için mock
export const getNotificationSettingsAsync = async () => ({
  allowsAlert: true,
  allowsBadge: true,
  allowsSound: true,
  allowsAnnouncements: true,
  allowsCriticalAlerts: true,
  allowsPreviews: true,
  allowsDisplayInNotificationCenter: true,
  allowsDisplayOnLockScreen: true,
  lockScreenSetting: 'always',
  showPreviewsSetting: 'always',
  criticalAlertSetting: 'disabled',
  alertStyle: 'banner',
  scheduledDeliveryAuthorization: 'authorized',
  timeSensitiveAuthorization: 'authorized',
  providesAppNotificationSettings: false
});
