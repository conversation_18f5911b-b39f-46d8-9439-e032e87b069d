/**
 * Para birimi formatı için biçimlendirme
 * @param {number} amount Biçimlendirilecek tutar
 * @param {string} currency Para birimi (varsayılan: TRY)
 * @param {number} decimals Ondalık basamak sayısı
 * @param {boolean} addCurrency Para birimi sembolü eklensin mi
 * @returns {string} Biçimlendirilmiş para değeri
 */
export const formatCurrency = (amount, currency = 'TRY', decimals = 2, addCurrency = true) => {
  if (amount === null || amount === undefined || isNaN(amount)) {
    return addCurrency ? '₺0,00' : '0,00';
  }

  // Turkish locale için ayarlar
  if (addCurrency) {
    const formatter = new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    });

    return formatter.format(parseFloat(amount));
  } else {
    const formatter = new Intl.NumberFormat('tr-TR', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    });

    return formatter.format(parseFloat(amount));
  }
};

/**
 * Büyük sayılar için kısaltılmış format (1K, 1M, 1B)
 * @param {number} number Kısaltılacak sayı
 * @returns {string} Kısaltılmış sayı
 */
export const formatCompactNumber = (number) => {
  if (number === null || number === undefined || isNaN(number)) {
    return '0';
  }

  // Turkish locale için ayarlar
  const formatter = new Intl.NumberFormat('tr-TR', {
    notation: 'compact',
    compactDisplay: 'short'
  });

  return formatter.format(number);
};

/**
 * String veya number olarak gelen tutarı number'a çevirir
 * @param {string|number} amount - Normalleştirilecek tutar
 * @returns {number} Normalleştirilmiş tutar
 */
export const normalizeAmount = (amount) => {
  if (typeof amount === 'number') return amount;

  // Türkçe formatındaki sayıyı işlenebilir hale getir
  const normalized = amount
    .replace(/\./g, '') // Binlik ayracını kaldır
    .replace(/,/g, '.'); // Ondalık ayracını noktaya çevir

  const parsed = parseFloat(normalized);

  if (isNaN(parsed)) return 0;

  return parsed;
};

/**
 * Para birimini Türk Lirası formatına çevirir
 * @param {number|string} amount - Formatlanacak miktar
 * @returns {string} TL formatında tutar
 */
export const formatTRY = (amount) => formatCurrency(amount, 'TRY');

/**
 * Para birimini Dolar formatına çevirir
 * @param {number|string} amount - Formatlanacak miktar
 * @returns {string} USD formatında tutar
 */
export const formatUSD = (amount) => formatCurrency(amount, 'USD');

/**
 * Para birimini Euro formatına çevirir
 * @param {number|string} amount - Formatlanacak miktar
 * @returns {string} EUR formatında tutar
 */
export const formatEUR = (amount) => formatCurrency(amount, 'EUR');

/**
 * Groups expenses by category and calculates totals
 * @param {Array} expenses Array of expense objects
 * @returns {Object} Grouped expenses with totals
 */
export const groupExpensesByCategory = (expenses) => {
  return expenses.reduce((acc, expense) => {
    const { category, amount } = expense;
    acc[category] = (acc[category] || 0) + Number(amount);
    return acc;
  }, {});
};

/**
 * Tarihi Türkçe formatlar
 * @param {string|Date} date - Formatlanacak tarih
 * @returns {string} Formatlanmış tarih
 */
export const formatDate = (date) => {
  if (!date) return '';

  const dateObj = new Date(date);
  return dateObj.toLocaleDateString('tr-TR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

/**
 * Maaş frekansını Türkçe metne çevirir
 * @param {string} frequency Maaş frekansı
 * @returns {string} Türkçe metin
 */
export const formatFrequency = (frequency) => {
  const frequencies = {
    daily: 'Günlük',
    weekly: 'Haftalık',
    monthly: 'Aylık'
  };

  return frequencies[frequency] || frequency;
};

/**
 * Tutarı başka bir para birimine çevirir ve formatlar
 * @param {number} amount - Çevrilecek tutar
 * @param {string} fromCurrency - Kaynak para birimi
 * @param {string} toCurrency - Hedef para birimi
 * @param {number} rate - Dönüşüm oranı
 * @returns {string} Formatlanmış tutar
 */
export const formatExchangeAmount = (amount, fromCurrency, toCurrency, rate) => {
  if (!amount || !rate) return '';

  const convertedAmount = amount * rate;
  return formatCurrency(convertedAmount, toCurrency);
};

/**
 * Para birimi sembolünü döndürür
 * @param {string} currency - Para birimi kodu
 * @returns {string} Para birimi sembolü
 */
export const getCurrencySymbol = (currency) => {
  const symbols = {
    TRY: '₺',
    USD: '$',
    EUR: '€',
    GBP: '£',
    JPY: '¥',
    CHF: 'CHF',
    CAD: 'C$',
    AUD: 'A$',
    CNY: '¥',
    RUB: '₽',
    KRW: '₩',
    INR: '₹',
    BRL: 'R$',
    MXN: 'Mex$',
    SAR: 'SR',
    AED: 'AED'
  };

  return symbols[currency] || currency;
};