import { format, addDays, subDays, differenceInMinutes, parseISO } from 'date-fns';
import { tr } from 'date-fns/locale';

/**
 * Tarihi formatlar
 * @param {Date|string} date - Formatlanacak tarih
 * @param {string} formatStr - Format string
 * @returns {string} Formatlanmış tarih
 */
export const formatDate = (date, formatStr = 'dd MMMM yyyy') => {
  if (!date) return '';
  
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  return format(dateObj, formatStr, { locale: tr });
};

/**
 * Saati formatlar
 * @param {Date|string} date - Formatlanacak tarih
 * @returns {string} Formatlanmış saat (HH:mm)
 */
export const formatTime = (date) => {
  if (!date) return '';
  
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  return format(dateObj, 'HH:mm', { locale: tr });
};

/**
 * Tarihi ve saati formatlar
 * @param {Date|string} date - Formatlanacak tarih
 * @returns {string} Formatlanmış tarih ve saat
 */
export const formatDateTime = (date) => {
  if (!date) return '';
  
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  return format(dateObj, 'dd MMM yyyy HH:mm', { locale: tr });
};

/**
 * Bir sonraki günü döndürür
 * @param {Date} date - Baz alınacak tarih
 * @returns {Date} Bir sonraki gün
 */
export const getNextDay = (date) => {
  return addDays(date, 1);
};

/**
 * Bir önceki günü döndürür
 * @param {Date} date - Baz alınacak tarih
 * @returns {Date} Bir önceki gün
 */
export const getPreviousDay = (date) => {
  return subDays(date, 1);
};

/**
 * İki tarih arasındaki süreyi saat cinsinden hesaplar
 * @param {Date|string} startDate - Başlangıç tarihi
 * @param {Date|string} endDate - Bitiş tarihi
 * @param {number} breakDuration - Mola süresi (dakika)
 * @returns {number} Saat cinsinden süre
 */
export const calculateDuration = (startDate, endDate, breakDuration = 0) => {
  if (!startDate || !endDate) return 0;
  
  const start = typeof startDate === 'string' ? parseISO(startDate) : startDate;
  const end = typeof endDate === 'string' ? parseISO(endDate) : endDate;
  
  // Dakika cinsinden süre
  const durationInMinutes = differenceInMinutes(end, start) - breakDuration;
  
  // Saat cinsinden süre (2 ondalık basamak)
  return Math.max(0, parseFloat((durationInMinutes / 60).toFixed(2)));
};

/**
 * Kazancı hesaplar
 * @param {number} hours - Çalışılan saat
 * @param {number} hourlyRate - Saatlik ücret
 * @param {number} multiplier - Çarpan (mesai, tatil günü vs. için)
 * @returns {number} Toplam kazanç
 */
export const calculateEarnings = (hours, hourlyRate, multiplier = 1) => {
  return parseFloat((hours * hourlyRate * multiplier).toFixed(2));
};

/**
 * Para birimini formatlar
 * @param {number} amount - Miktar
 * @param {string} currency - Para birimi
 * @returns {string} Formatlanmış para
 */
export const formatCurrency = (amount, currency = 'TRY') => {
  return new Intl.NumberFormat('tr-TR', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};

/**
 * Vardiya durumunu insan tarafından okunabilir formata çevirir
 * @param {string} status - Vardiya durumu
 * @returns {string} İnsan tarafından okunabilir durum
 */
export const getShiftStatusText = (status) => {
  switch (status) {
    case 'active':
      return 'Aktif';
    case 'completed':
      return 'Tamamlandı';
    case 'planned':
      return 'Planlandı';
    case 'cancelled':
      return 'İptal Edildi';
    default:
      return status;
  }
};

/**
 * Vardiya durumuna göre renk döndürür
 * @param {string} status - Vardiya durumu
 * @returns {string} Renk kodu
 */
export const getShiftStatusColor = (status) => {
  switch (status) {
    case 'active':
      return '#4caf50'; // Yeşil
    case 'completed':
      return '#2196f3'; // Mavi
    case 'planned':
      return '#ff9800'; // Turuncu
    case 'cancelled':
      return '#f44336'; // Kırmızı
    default:
      return '#9e9e9e'; // Gri
  }
};
