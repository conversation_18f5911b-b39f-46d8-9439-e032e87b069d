/**
 * Rapor dışa aktarma servisi
 * PDF, CSV, Excel formatlarında rapor dışa aktarma
 */

import * as FileSystem from 'expo-file-system';
// import * as Sharing from 'expo-sharing';
// import * as Print from 'expo-print';

/**
 * Export servisi sınıfı
 */
export class ExportService {
  
  /**
   * PDF formatında rapor dışa aktarma
   */
  static async exportToPDF(reportData, options = {}) {
    try {
      const {
        title = 'Finansal Rapor',
        subtitle = 'Rapor Özeti',
        includeCharts = false,
        pageOrientation = 'portrait'
      } = options;

      // HTML içeriği oluştur
      const htmlContent = this.generateHTMLContent(reportData, { 
        title, 
        subtitle, 
        includeCharts 
      });

      // PDF oluştur
      const { uri } = await Print.printToFileAsync({
        html: htmlContent,
        base64: false,
        orientation: pageOrientation
      });

      // Dosyayı paylaş
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(uri, {
          mimeType: 'application/pdf',
          dialogTitle: 'Raporu Paylaş'
        });
      }

      return { success: true, uri };
    } catch (error) {
      console.error('PDF export hatası:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * CSV formatında rapor dışa aktarma
   */
  static async exportToCSV(reportData, options = {}) {
    try {
      const {
        filename = 'finansal_rapor.csv',
        delimiter = ',',
        includeHeaders = true
      } = options;

      // CSV içeriği oluştur
      const csvContent = this.generateCSVContent(reportData, { 
        delimiter, 
        includeHeaders 
      });

      // Dosya yolu oluştur
      const fileUri = FileSystem.documentDirectory + filename;

      // CSV dosyasını yaz
      await FileSystem.writeAsStringAsync(fileUri, csvContent, {
        encoding: FileSystem.EncodingType.UTF8
      });

      // Dosyayı paylaş
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri, {
          mimeType: 'text/csv',
          dialogTitle: 'CSV Raporu Paylaş'
        });
      }

      return { success: true, uri: fileUri };
    } catch (error) {
      console.error('CSV export hatası:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * JSON formatında rapor dışa aktarma
   */
  static async exportToJSON(reportData, options = {}) {
    try {
      const {
        filename = 'finansal_rapor.json',
        pretty = true
      } = options;

      // JSON içeriği oluştur
      const jsonContent = pretty ? 
        JSON.stringify(reportData, null, 2) : 
        JSON.stringify(reportData);

      // Dosya yolu oluştur
      const fileUri = FileSystem.documentDirectory + filename;

      // JSON dosyasını yaz
      await FileSystem.writeAsStringAsync(fileUri, jsonContent, {
        encoding: FileSystem.EncodingType.UTF8
      });

      // Dosyayı paylaş
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri, {
          mimeType: 'application/json',
          dialogTitle: 'JSON Raporu Paylaş'
        });
      }

      return { success: true, uri: fileUri };
    } catch (error) {
      console.error('JSON export hatası:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * HTML içeriği oluşturma (PDF için)
   */
  static generateHTMLContent(reportData, options) {
    const { title, subtitle, includeCharts } = options;
    
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <title>${title}</title>
          <style>
            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 800px;
              margin: 0 auto;
              padding: 20px;
            }
            .header {
              text-align: center;
              border-bottom: 2px solid #4CAF50;
              padding-bottom: 20px;
              margin-bottom: 30px;
            }
            .header h1 {
              color: #2E7D32;
              margin: 0;
              font-size: 28px;
            }
            .header p {
              color: #666;
              margin: 10px 0 0 0;
              font-size: 16px;
            }
            .summary {
              background: #f5f5f5;
              padding: 20px;
              border-radius: 8px;
              margin-bottom: 30px;
            }
            .summary h2 {
              margin-top: 0;
              color: #1976D2;
            }
            .summary-grid {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
              gap: 15px;
              margin-top: 15px;
            }
            .summary-item {
              background: white;
              padding: 15px;
              border-radius: 6px;
              border-left: 4px solid #4CAF50;
            }
            .summary-item .label {
              font-size: 14px;
              color: #666;
              margin-bottom: 5px;
            }
            .summary-item .value {
              font-size: 18px;
              font-weight: bold;
              color: #333;
            }
            .table-container {
              margin: 30px 0;
            }
            table {
              width: 100%;
              border-collapse: collapse;
              background: white;
              border-radius: 8px;
              overflow: hidden;
              box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            th {
              background: #4CAF50;
              color: white;
              padding: 12px;
              text-align: left;
              font-weight: 600;
            }
            td {
              padding: 12px;
              border-bottom: 1px solid #eee;
            }
            tr:hover {
              background: #f9f9f9;
            }
            .amount {
              text-align: right;
              font-weight: 600;
            }
            .positive {
              color: #4CAF50;
            }
            .negative {
              color: #f44336;
            }
            .footer {
              margin-top: 40px;
              padding-top: 20px;
              border-top: 1px solid #ddd;
              text-align: center;
              color: #666;
              font-size: 14px;
            }
            @media print {
              body { margin: 0; }
              .no-print { display: none; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>${title}</h1>
            <p>${subtitle}</p>
            <p>Oluşturulma Tarihi: ${new Date().toLocaleDateString('tr-TR', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}</p>
          </div>

          ${this.generateSummaryHTML(reportData)}
          ${this.generateDataTablesHTML(reportData)}

          <div class="footer">
            <p>Bu rapor Maaş Takip Uygulaması tarafından otomatik olarak oluşturulmuştur.</p>
          </div>
        </body>
      </html>
    `;
  }

  /**
   * Özet bilgiler HTML'i oluştur
   */
  static generateSummaryHTML(reportData) {
    const summary = reportData.summary || {};
    
    return `
      <div class="summary">
        <h2>📊 Rapor Özeti</h2>
        <div class="summary-grid">
          ${summary.totalIncome ? `
            <div class="summary-item">
              <div class="label">Toplam Gelir</div>
              <div class="value positive">₺${summary.totalIncome.toLocaleString('tr-TR')}</div>
            </div>
          ` : ''}
          ${summary.totalExpenses ? `
            <div class="summary-item">
              <div class="label">Toplam Gider</div>
              <div class="value negative">₺${summary.totalExpenses.toLocaleString('tr-TR')}</div>
            </div>
          ` : ''}
          ${summary.netProfit !== undefined ? `
            <div class="summary-item">
              <div class="label">Net Kar/Zarar</div>
              <div class="value ${summary.netProfit >= 0 ? 'positive' : 'negative'}">
                ₺${summary.netProfit.toLocaleString('tr-TR')}
              </div>
            </div>
          ` : ''}
          ${summary.savingsRate !== undefined ? `
            <div class="summary-item">
              <div class="label">Tasarruf Oranı</div>
              <div class="value">%${summary.savingsRate.toFixed(1)}</div>
            </div>
          ` : ''}
        </div>
      </div>
    `;
  }

  /**
   * Veri tabloları HTML'i oluştur
   */
  static generateDataTablesHTML(reportData) {
    let html = '';

    // Aylık veriler tablosu
    if (reportData.monthlyData && reportData.monthlyData.length > 0) {
      html += `
        <div class="table-container">
          <h3>📅 Aylık Veriler</h3>
          <table>
            <thead>
              <tr>
                <th>Ay</th>
                <th>Gelir</th>
                <th>Gider</th>
                <th>Net</th>
              </tr>
            </thead>
            <tbody>
              ${reportData.monthlyData.map(item => `
                <tr>
                  <td>${item.month}</td>
                  <td class="amount positive">₺${item.income.toLocaleString('tr-TR')}</td>
                  <td class="amount negative">₺${item.expense.toLocaleString('tr-TR')}</td>
                  <td class="amount ${item.net >= 0 ? 'positive' : 'negative'}">
                    ₺${item.net.toLocaleString('tr-TR')}
                  </td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
      `;
    }

    // Kategori dağılımı
    if (reportData.categories && reportData.categories.length > 0) {
      html += `
        <div class="table-container">
          <h3>🏷️ Kategori Dağılımı</h3>
          <table>
            <thead>
              <tr>
                <th>Kategori</th>
                <th>Tutar</th>
                <th>Yüzde</th>
              </tr>
            </thead>
            <tbody>
              ${reportData.categories.map(item => `
                <tr>
                  <td>${item.name || item.category}</td>
                  <td class="amount">₺${item.amount.toLocaleString('tr-TR')}</td>
                  <td class="amount">${item.percentage ? item.percentage.toFixed(1) : 0}%</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
      `;
    }

    return html;
  }

  /**
   * CSV içeriği oluşturma
   */
  static generateCSVContent(reportData, options) {
    const { delimiter, includeHeaders } = options;
    let csvContent = '';

    // Başlık ekle
    if (includeHeaders) {
      csvContent += `"Finansal Rapor - ${new Date().toLocaleDateString('tr-TR')}"${delimiter}${delimiter}${delimiter}\n`;
      csvContent += `${delimiter}${delimiter}${delimiter}\n`;
    }

    // Özet bilgiler
    if (reportData.summary) {
      const summary = reportData.summary;
      csvContent += `"Özet Bilgiler"${delimiter}${delimiter}${delimiter}\n`;
      if (summary.totalIncome) csvContent += `"Toplam Gelir"${delimiter}"₺${summary.totalIncome.toLocaleString('tr-TR')}"${delimiter}${delimiter}\n`;
      if (summary.totalExpenses) csvContent += `"Toplam Gider"${delimiter}"₺${summary.totalExpenses.toLocaleString('tr-TR')}"${delimiter}${delimiter}\n`;
      if (summary.netProfit !== undefined) csvContent += `"Net Kar/Zarar"${delimiter}"₺${summary.netProfit.toLocaleString('tr-TR')}"${delimiter}${delimiter}\n`;
      csvContent += `${delimiter}${delimiter}${delimiter}\n`;
    }

    // Aylık veriler
    if (reportData.monthlyData && reportData.monthlyData.length > 0) {
      csvContent += `"Aylık Veriler"${delimiter}${delimiter}${delimiter}\n`;
      csvContent += `"Ay"${delimiter}"Gelir"${delimiter}"Gider"${delimiter}"Net"\n`;
      reportData.monthlyData.forEach(item => {
        csvContent += `"${item.month}"${delimiter}"₺${item.income.toLocaleString('tr-TR')}"${delimiter}"₺${item.expense.toLocaleString('tr-TR')}"${delimiter}"₺${item.net.toLocaleString('tr-TR')}"\n`;
      });
      csvContent += `${delimiter}${delimiter}${delimiter}\n`;
    }

    // Kategori dağılımı
    if (reportData.categories && reportData.categories.length > 0) {
      csvContent += `"Kategori Dağılımı"${delimiter}${delimiter}${delimiter}\n`;
      csvContent += `"Kategori"${delimiter}"Tutar"${delimiter}"Yüzde"${delimiter}\n`;
      reportData.categories.forEach(item => {
        csvContent += `"${item.name || item.category}"${delimiter}"₺${item.amount.toLocaleString('tr-TR')}"${delimiter}"${item.percentage ? item.percentage.toFixed(1) : 0}%"${delimiter}\n`;
      });
    }

    return csvContent;
  }

  /**
   * Dosya izinlerini kontrol et
   */
  static async checkPermissions() {
    try {
      const { status } = await MediaLibrary.requestPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('İzin kontrolü hatası:', error);
      return false;
    }
  }

  /**
   * Desteklenen formatları listele
   */
  static getSupportedFormats() {
    return [
      { id: 'pdf', name: 'PDF', extension: '.pdf', mimeType: 'application/pdf' },
      { id: 'csv', name: 'CSV', extension: '.csv', mimeType: 'text/csv' },
      { id: 'json', name: 'JSON', extension: '.json', mimeType: 'application/json' }
    ];
  }
}

export default ExportService;
