export const TRANSACTION_THEME = {
  income: {
    primary: '#2ecc71',
    // ... mevcut theme
  },
  expense: {
    primary: '#e74c3c',
    // ... mevcut theme
  }
};

export const FREQUENCY_OPTIONS = [
  // ... mevcut FREQUENCY_OPTIONS array'i
];

export const ICON_CATEGORIES = [
  // ... mevcut ICON_CATEGORIES array'i
];

/**
 * İşlem sabitleri
 */

// İşlem tipleri
export const TRANSACTION_TYPES = {
  INCOME: 'income', 
  EXPENSE: 'expense'
};

// G<PERSON>r sıklık türleri
export const FREQUENCY_TYPES = {
  ONCE: 'once',
  WEEKLY: 'weekly',
  MONTHLY: 'monthly',
  YEARLY: 'yearly',
};

// Sıklık tiplerinin Türkçe karşılıkları
export const FREQUENCY_LABELS = {
  [FREQUENCY_TYPES.ONCE]: 'Te<PERSON>',
  [FREQUENCY_TYPES.WEEKLY]: 'Haftalık',
  [FREQUENCY_TYPES.MONTHLY]: 'Aylık',
  [FREQUENCY_TYPES.YEARLY]: 'Yıllık',
};

// Varsayılan kategori iconları
export const DEFAULT_CATEGORY_ICONS = {
  [TRANSACTION_TYPES.INCOME]: 'account-balance-wallet',
  [TRANSACTION_TYPES.EXPENSE]: 'category',
};

// Varsayılan harcama kategorileri - kullanıcı oluşturmadığında
export const DEFAULT_EXPENSE_CATEGORIES = [
  { id: 'groceries', name: 'Market', icon: 'shopping-basket', type: TRANSACTION_TYPES.EXPENSE },
  { id: 'dining', name: 'Yemek', icon: 'restaurant', type: TRANSACTION_TYPES.EXPENSE },
  { id: 'transportation', name: 'Ulaşım', icon: 'directions-car', type: TRANSACTION_TYPES.EXPENSE },
  { id: 'utilities', name: 'Faturalar', icon: 'receipt', type: TRANSACTION_TYPES.EXPENSE },
  { id: 'housing', name: 'Konut', icon: 'home', type: TRANSACTION_TYPES.EXPENSE },
  { id: 'entertainment', name: 'Eğlence', icon: 'movie', type: TRANSACTION_TYPES.EXPENSE },
  { id: 'health', name: 'Sağlık', icon: 'favorite', type: TRANSACTION_TYPES.EXPENSE },
  { id: 'education', name: 'Eğitim', icon: 'school', type: TRANSACTION_TYPES.EXPENSE },
  { id: 'shopping', name: 'Alışveriş', icon: 'shopping-cart', type: TRANSACTION_TYPES.EXPENSE },
  { id: 'personal', name: 'Kişisel', icon: 'person', type: TRANSACTION_TYPES.EXPENSE },
  { id: 'travel', name: 'Seyahat', icon: 'flight', type: TRANSACTION_TYPES.EXPENSE },
  { id: 'other', name: 'Diğer', icon: 'more-horiz', type: TRANSACTION_TYPES.EXPENSE },
];

// Varsayılan gelir kategorileri
export const DEFAULT_INCOME_CATEGORIES = [
  { id: 'salary', name: 'Maaş', icon: 'account-balance', type: TRANSACTION_TYPES.INCOME },
  { id: 'investment', name: 'Yatırım', icon: 'trending-up', type: TRANSACTION_TYPES.INCOME },
  { id: 'gift', name: 'Hediye', icon: 'card-giftcard', type: TRANSACTION_TYPES.INCOME },
  { id: 'bonus', name: 'Prim', icon: 'star', type: TRANSACTION_TYPES.INCOME },
  { id: 'other_income', name: 'Diğer', icon: 'attach-money', type: TRANSACTION_TYPES.INCOME },
];
