import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  RefreshControl
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import { format, parseISO } from 'date-fns';
import { tr } from 'date-fns/locale';
import * as budgetService from '../services/budgetService';
import { Colors } from '../constants/colors';

/**
 * Bütçe Listesi Ekranı
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @returns {JSX.Element} Bütçe Listesi Ekranı
 */
const BudgetListScreen = ({ navigation }) => {
  const db = useSQLiteContext();
  
  // Durum
  const [budgets, setBudgets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeOnly, setActiveOnly] = useState(true);
  
  // Verileri yükle
  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      const budgetData = await budgetService.getBudgets(db, { activeOnly });
      setBudgets(budgetData);
      setLoading(false);
    } catch (error) {
      console.error('Bütçeleri yükleme hatası:', error);
      Alert.alert('Hata', 'Bütçeler yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  }, [db, activeOnly]);
  
  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [loadData])
  );
  
  // Yenileme işlemi
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };
  
  // Bütçe detaylarına git
  const navigateToBudgetDetails = (budget) => {
    navigation.navigate('BudgetDetails', { budgetId: budget.id });
  };
  
  // Bütçe düzenleme ekranına git
  const navigateToBudgetForm = (budget) => {
    navigation.navigate('BudgetForm', { budgetId: budget.id });
  };
  
  // Bütçe silme
  const deleteBudget = (budget) => {
    Alert.alert(
      'Bütçeyi Sil',
      `"${budget.name}" bütçesini silmek istediğinize emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await budgetService.deleteBudget(db, budget.id);
              Alert.alert('Başarılı', 'Bütçe silindi.');
              loadData();
            } catch (error) {
              console.error('Bütçe silme hatası:', error);
              Alert.alert('Hata', 'Bütçe silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };
  
  // Bütçe öğesi
  const renderBudgetItem = ({ item }) => {
    // İlerleme yüzdesi
    const progress = item.totalBudget > 0 
      ? Math.min((item.totalSpent / item.totalBudget) * 100, 100) 
      : 0;
    
    // Kalan tutar
    const remaining = item.totalBudget - item.totalSpent;
    
    // Tarih aralığı
    const startDate = format(parseISO(item.start_date), 'dd MMM yyyy', { locale: tr });
    const endDate = item.end_date ? format(parseISO(item.end_date), 'dd MMM yyyy', { locale: tr }) : 'Süresiz';
    
    return (
      <TouchableOpacity
        style={styles.budgetItem}
        onPress={() => navigateToBudgetDetails(item)}
      >
        <View style={styles.budgetHeader}>
          <Text style={styles.budgetName}>{item.name}</Text>
          <View style={styles.budgetActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigateToBudgetForm(item)}
            >
              <MaterialIcons name="edit" size={20} color={Colors.PRIMARY} />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => deleteBudget(item)}
            >
              <MaterialIcons name="delete" size={20} color={Colors.DANGER} />
            </TouchableOpacity>
          </View>
        </View>
        
        <View style={styles.budgetInfo}>
          <Text style={styles.budgetPeriod}>
            {item.period === 'monthly' ? 'Aylık' : 
             item.period === 'weekly' ? 'Haftalık' : 
             item.period === 'yearly' ? 'Yıllık' : 'Özel'}
          </Text>
          <Text style={styles.budgetDate}>{startDate} - {endDate}</Text>
        </View>
        
        <View style={styles.budgetProgress}>
          <View style={styles.progressBarContainer}>
            <View 
              style={[
                styles.progressBar, 
                { width: `${progress}%` },
                progress > 80 ? styles.progressBarDanger : 
                progress > 60 ? styles.progressBarWarning : 
                styles.progressBarSuccess
              ]} 
            />
          </View>
          <Text style={styles.progressText}>{progress.toFixed(0)}%</Text>
        </View>
        
        <View style={styles.budgetAmounts}>
          <View style={styles.amountItem}>
            <Text style={styles.amountLabel}>Toplam Bütçe</Text>
            <Text style={styles.amountValue}>
              {item.totalBudget.toLocaleString('tr-TR')} ₺
            </Text>
          </View>
          <View style={styles.amountItem}>
            <Text style={styles.amountLabel}>Harcanan</Text>
            <Text style={styles.amountValue}>
              {item.totalSpent.toLocaleString('tr-TR')} ₺
            </Text>
          </View>
          <View style={styles.amountItem}>
            <Text style={styles.amountLabel}>Kalan</Text>
            <Text style={[
              styles.amountValue,
              remaining < 0 ? styles.negativeAmount : null
            ]}>
              {remaining.toLocaleString('tr-TR')} ₺
            </Text>
          </View>
        </View>
        
        {item.category_count > 0 && (
          <Text style={styles.categoryCount}>
            {item.category_count} kategori
          </Text>
        )}
      </TouchableOpacity>
    );
  };
  
  // Boş durum
  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <MaterialIcons name="account-balance-wallet" size={64} color={Colors.GRAY_300} />
      <Text style={styles.emptyTitle}>Henüz Bütçe Yok</Text>
      <Text style={styles.emptyText}>
        Gelir ve giderlerinizi takip etmek için bütçe oluşturun
      </Text>
      <TouchableOpacity
        style={styles.emptyButton}
        onPress={() => navigation.navigate('BudgetForm')}
      >
        <MaterialIcons name="add" size={20} color="#fff" />
        <Text style={styles.emptyButtonText}>Bütçe Oluştur</Text>
      </TouchableOpacity>
    </View>
  );
  
  // Yükleniyor durumu
  if (loading && !refreshing) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={styles.loadingText}>Yükleniyor...</Text>
      </View>
    );
  }
  
  return (
    <View style={styles.container}>
      {/* Başlık */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Bütçelerim</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={[
              styles.filterButton,
              activeOnly ? styles.filterButtonActive : null
            ]}
            onPress={() => setActiveOnly(!activeOnly)}
          >
            <MaterialIcons 
              name={activeOnly ? "visibility" : "visibility-off"} 
              size={20} 
              color={activeOnly ? Colors.PRIMARY : Colors.GRAY_500} 
            />
            <Text style={[
              styles.filterButtonText,
              activeOnly ? styles.filterButtonTextActive : null
            ]}>
              {activeOnly ? 'Aktif' : 'Tümü'}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => navigation.navigate('BudgetForm')}
          >
            <MaterialIcons name="add" size={24} color="#fff" />
          </TouchableOpacity>
        </View>
      </View>
      
      {/* Bütçe Listesi */}
      <FlatList
        data={budgets}
        renderItem={renderBudgetItem}
        keyExtractor={item => item.id.toString()}
        contentContainerStyle={styles.listContent}
        ListEmptyComponent={renderEmptyState}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[Colors.PRIMARY]}
          />
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    backgroundColor: Colors.GRAY_100,
  },
  filterButtonActive: {
    backgroundColor: Colors.PRIMARY_LIGHT,
  },
  filterButtonText: {
    fontSize: 14,
    marginLeft: 4,
    color: Colors.GRAY_500,
  },
  filterButtonTextActive: {
    color: Colors.PRIMARY,
    fontWeight: 'bold',
  },
  addButton: {
    backgroundColor: Colors.PRIMARY,
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: Colors.PRIMARY,
  },
  listContent: {
    padding: 16,
    flexGrow: 1,
  },
  budgetItem: {
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
  },
  budgetHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  budgetName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    flex: 1,
  },
  budgetActions: {
    flexDirection: 'row',
  },
  actionButton: {
    padding: 8,
    marginLeft: 4,
  },
  budgetInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  budgetPeriod: {
    fontSize: 14,
    color: Colors.PRIMARY,
    fontWeight: '500',
  },
  budgetDate: {
    fontSize: 14,
    color: Colors.TEXT_LIGHT,
  },
  budgetProgress: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  progressBarContainer: {
    flex: 1,
    height: 8,
    backgroundColor: Colors.GRAY_200,
    borderRadius: 4,
    marginRight: 8,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    borderRadius: 4,
  },
  progressBarSuccess: {
    backgroundColor: Colors.SUCCESS,
  },
  progressBarWarning: {
    backgroundColor: Colors.WARNING,
  },
  progressBarDanger: {
    backgroundColor: Colors.DANGER,
  },
  progressText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    width: 40,
    textAlign: 'right',
  },
  budgetAmounts: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  amountItem: {
    flex: 1,
  },
  amountLabel: {
    fontSize: 12,
    color: Colors.TEXT_LIGHT,
    marginBottom: 2,
  },
  amountValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
  },
  negativeAmount: {
    color: Colors.DANGER,
  },
  categoryCount: {
    fontSize: 12,
    color: Colors.TEXT_LIGHT,
    textAlign: 'right',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    marginTop: 16,
  },
  emptyText: {
    fontSize: 14,
    color: Colors.TEXT_LIGHT,
    textAlign: 'center',
    marginTop: 8,
    marginHorizontal: 32,
  },
  emptyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 24,
  },
  emptyButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 8,
  },
});

export default BudgetListScreen;
