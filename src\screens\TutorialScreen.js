import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ScrollView, 
  Dimensions,
  Animated,
  PanResponder,
  Platform,
  StatusBar,
  KeyboardAvoidingView
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { useAppContext } from '../context/AppContext';
import AsyncStorage from '@react-native-async-storage/async-storage';

const { width } = Dimensions.get('window');

// Tutorial adımları
const TUTORIAL_STEPS = [
  {
    id: 1,
    title: 'Hoş Geldiniz!',
    description: 'Bu uygulama ile kişisel finanslarınızı kolayca yönetebilirsiniz.',
    icon: 'account-balance-wallet',
    content: '<PERSON><PERSON><PERSON><PERSON><PERSON>, gider<PERSON><PERSON><PERSON> ve bütçenizi takip edebilir, finansal durumunuzu her zaman kontrol altında tutabilirsiniz.',
    color: '#6c5ce7' // Will be replaced with theme colors dynamically
  },
  {
    id: 2,
    title: '<PERSON>şlem Ekleme',
    description: 'Hızlı ve kolay şekilde gelir ve gider ekleyebilirsiniz.',
    icon: 'add-circle',
    content: 'Ana sayfadan + butonuna tıklayarak yeni işlem ekleyin. Kategori seçin, tutar girin ve notlarınızı ekleyin.',
    color: '#00cec9' // Will be replaced with theme colors dynamically
  },
  {
    id: 3,
    title: 'Kategoriler',
    description: 'İşlemlerinizi düzenli tutmak için kategoriler kullanın.',
    icon: 'category',
    content: 'Özel kategoriler oluşturun veya hazır kategorileri kullanın. Her kategori için farklı renkler ve ikonlar seçebilirsiniz.',
    color: '#ff7675' // Will be replaced with theme colors dynamically
  },
  {
    id: 4,
    title: 'Bütçe Takibi',
    description: 'Bütçenizi belirleyin ve harcamalarınızı kontrol edin.',
    icon: 'pie-chart',
    content: 'Aylık, haftalık veya yıllık bütçe hedefleri koyun. Harcamalarınızı gerçek zamanlı olarak takip edin.',
    color: '#fdcb6e' // Will be replaced with theme colors dynamically
  },
  {
    id: 5,
    title: 'Raporlar',
    description: 'Detaylı raporlar ile finansal durumunuzu analiz edin.',
    icon: 'assessment',
    content: 'Grafikler ve tablolar ile gelir-gider dağılımınızı görün. Aylık, haftalık karşılaştırmalar yapın.',
    color: '#a29bfe' // Will be replaced with theme colors dynamically
  },
  {
    id: 6,
    title: 'Tema Seçimi',
    description: 'Uygulamayı kişiselleştirin, açık veya koyu tema seçin.',
    icon: 'palette',
    content: 'Ayarlar bölümünden tema tercihlerinizi değiştirin. Sistem temasını takip edebilir veya manuel seçim yapabilirsiniz.',
    color: '#fd79a8' // Will be replaced with theme colors dynamically
  },
  {
    id: 7,
    title: 'Hazırsınız!',
    description: 'Artık uygulamayı kullanmaya başlayabilirsiniz.',
    icon: 'check-circle',
    content: 'Tüm özellikleri keşfettiniz! Finansal hedeflerinize ulaşmak için uygulamayı kullanmaya başlayın.',
    color: '#55efc4' // Will be replaced with theme colors dynamically
  }
];

/**
 * Uygulama tanıtım ekranı
 * İlk kullanım için adım adım kılavuz
 *
 * @param {Object} props - Bileşen props'ları
 * @param {Object} props.navigation - Navigation nesnesi
 * @returns {JSX.Element} Tutorial ekranı
 */
function TutorialScreen({ navigation }) {
  const insets = useSafeAreaInsets();
  const { theme } = useTheme();
  const { defaultCurrency } = useAppContext();
  
  const [currentStep, setCurrentStep] = useState(0);
  const [scrollX] = useState(new Animated.Value(0));
  const [isCompleted, setIsCompleted] = useState(false);
  
  // Sayfa değişim animasyonu
  const scrollToPage = (index) => {
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({
        x: index * width,
        animated: true
      });
    }
  };
  
  const scrollViewRef = React.useRef(null);
  
  // Sonraki adım
  const nextStep = () => {
    if (currentStep < TUTORIAL_STEPS.length - 1) {
      const newStep = currentStep + 1;
      setCurrentStep(newStep);
      scrollToPage(newStep);
    } else {
      completeTutorial();
    }
  };
  
  // Önceki adım
  const previousStep = () => {
    if (currentStep > 0) {
      const newStep = currentStep - 1;
      setCurrentStep(newStep);
      scrollToPage(newStep);
    }
  };
  
  // Adım atlama
  const skipTutorial = async () => {
    await AsyncStorage.setItem('tutorialCompleted', 'true');
    navigation.replace('PinSetupFlow');
  };
  
  // Tutorial tamamlama
  const completeTutorial = async () => {
    setIsCompleted(true);
    await AsyncStorage.setItem('tutorialCompleted', 'true');
    
    // Kısa animasyon sonrası PIN kurulum ekranına yönlendir
    setTimeout(() => {
      navigation.replace('PinSetupFlow');
    }, 1000);
  };
  
  // Sayfa değişimini handle et
  const handleScroll = (event) => {
    const offsetX = event.nativeEvent.contentOffset.x;
    const newStep = Math.round(offsetX / width);
    
    if (newStep !== currentStep && newStep >= 0 && newStep < TUTORIAL_STEPS.length) {
      setCurrentStep(newStep);
    }
  };
  
  // Progress indicator
  const renderProgressIndicator = () => (
    <View style={styles.progressContainer}>
      {TUTORIAL_STEPS.map((_, index) => (
        <View
          key={index}
          style={[
            styles.progressDot,
            { backgroundColor: theme.colors.border },
            index === currentStep && { 
              backgroundColor: theme.colors.primary,
              transform: [{ scale: 1.2 }]
            }
          ]}
        />
      ))}
    </View>
  );
  
  // Tutorial adımı render et
  const renderTutorialStep = (step, index) => {
    // Map step colors to theme colors
    const stepColors = [
      theme.colors.primary,
      theme.colors.success,
      theme.colors.error,
      theme.colors.warning,
      theme.colors.info,
      theme.colors.secondary,
      theme.colors.primary
    ];
    const stepColor = stepColors[index] || theme.colors.primary;
    
    return (
      <View key={step.id} style={[styles.stepContainer, { width }]}>
        <View style={styles.stepContent}>
          {/* İkon */}
          <View style={[styles.iconContainer, { backgroundColor: stepColor + '20' }]}>
            <MaterialIcons 
              name={step.icon} 
              size={80} 
              color={stepColor} 
            />
          </View>
          
          {/* Başlık */}
          <Text style={[styles.stepTitle, { color: theme.colors.text }]}>
            {step.title}
          </Text>
          
          {/* Açıklama */}
          <Text style={[styles.stepDescription, { color: theme.colors.textSecondary }]}>
            {step.description}
          </Text>
          
          {/* Detaylı içerik */}
          <Text style={[styles.stepContent, { color: theme.colors.text }]}>
            {step.content}
          </Text>
        </View>
      </View>
    );
  };
  
  // Tamamlama ekranı
  if (isCompleted) {
    return (
      <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
        <View style={styles.completedContainer}>
          <View style={[styles.completedIconContainer, { backgroundColor: theme.SUCCESS + '20' }]}>
            <MaterialIcons 
              name="check-circle" 
              size={120} 
              color={theme.SUCCESS} 
            />
          </View>
          
          <Text style={[styles.completedTitle, { color: theme.TEXT_PRIMARY }]}>
            Tebrikler!
          </Text>
          
          <Text style={[styles.completedDescription, { color: theme.TEXT_SECONDARY }]}>
            Artık uygulamayı kullanmaya hazırsınız.
          </Text>
          
          <View style={styles.loadingContainer}>
            <Text style={[styles.loadingText, { color: theme.TEXT_SECONDARY }]}>
              Ana sayfaya yönlendiriliyorsunuz...
            </Text>
          </View>
        </View>
      </View>
    );
  }
  
  return (
    <KeyboardAvoidingView 
      style={[styles.container, { paddingTop: insets.top, backgroundColor: theme.BACKGROUND }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <StatusBar 
        barStyle={theme.STATUS_BAR_STYLE}
        backgroundColor={theme.STATUS_BAR_COLOR}
        translucent={false}
      />
      
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.SURFACE }]}>
        <TouchableOpacity 
          style={styles.skipButton}
          onPress={skipTutorial}
        >
          <Text style={[styles.skipButtonText, { color: theme.TEXT_SECONDARY }]}>
            Geç
          </Text>
        </TouchableOpacity>
        
        <Text style={[styles.headerTitle, { color: theme.TEXT_PRIMARY }]}>
          Uygulama Kılavuzu
        </Text>
        
        <View style={styles.headerSpacer} />
      </View>
      
      {/* Progress */}
      {renderProgressIndicator()}
      
      {/* Content */}
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
      >
        {TUTORIAL_STEPS.map((step, index) => renderTutorialStep(step, index))}
      </ScrollView>
      
      {/* Navigation */}
      <View style={[styles.navigationContainer, { backgroundColor: theme.SURFACE }]}>
        <TouchableOpacity 
          style={[
            styles.navButton,
            { backgroundColor: theme.SURFACE, borderColor: theme.BORDER },
            currentStep === 0 && { opacity: 0.5 }
          ]}
          onPress={previousStep}
          disabled={currentStep === 0}
        >
          <MaterialIcons 
            name="arrow-back" 
            size={24} 
            color={currentStep === 0 ? theme.TEXT_DISABLED : theme.TEXT_PRIMARY} 
          />
          <Text style={[
            styles.navButtonText,
            { color: currentStep === 0 ? theme.TEXT_DISABLED : theme.TEXT_PRIMARY }
          ]}>
            Önceki
          </Text>
        </TouchableOpacity>
        
        <View style={styles.stepIndicator}>
          <Text style={[styles.stepIndicatorText, { color: theme.TEXT_SECONDARY }]}>
            {currentStep + 1} / {TUTORIAL_STEPS.length}
          </Text>
        </View>
        
        <TouchableOpacity 
          style={[styles.navButton, { backgroundColor: theme.PRIMARY }]}
          onPress={nextStep}
        >
          <Text style={[styles.navButtonText, { color: theme.WHITE }]}>
            {currentStep === TUTORIAL_STEPS.length - 1 ? 'Tamamla' : 'Sonraki'}
          </Text>
          <MaterialIcons 
            name={currentStep === TUTORIAL_STEPS.length - 1 ? 'check' : 'arrow-forward'} 
            size={24} 
            color={theme.WHITE} 
          />
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  skipButton: {
    padding: 8,
  },
  skipButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  headerSpacer: {
    width: 50,
  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
    paddingHorizontal: 16,
  },
  progressDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  stepContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  stepContent: {
    alignItems: 'center',
    maxWidth: 320,
  },
  iconContainer: {
    width: 160,
    height: 160,
    borderRadius: 80,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 32,
  },
  stepTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
  },
  stepDescription: {
    fontSize: 18,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 26,
  },
  stepContentText: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
  navigationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  navButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    minWidth: 100,
    justifyContent: 'center',
  },
  navButtonText: {
    fontSize: 16,
    fontWeight: '600',
    marginHorizontal: 8,
  },
  stepIndicator: {
    alignItems: 'center',
  },
  stepIndicatorText: {
    fontSize: 14,
    fontWeight: '500',
  },
  completedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  completedIconContainer: {
    width: 200,
    height: 200,
    borderRadius: 100,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 32,
  },
  completedTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
  },
  completedDescription: {
    fontSize: 18,
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 26,
  },
  loadingContainer: {
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    textAlign: 'center',
  },
});

export default TutorialScreen;
