import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Switch,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { format } from 'date-fns';
import { Colors } from '../constants/colors';
import * as notificationDbService from '../services/notificationDbService';
import * as notificationService from '../services/NotificationService';

/**
 * Bildirim Ayarları Ekranı
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @returns {JSX.Element} Bildirim Ayarları Ekranı
 */
export default function NotificationSettingsScreen({ navigation }) {
  const db = useSQLiteContext();

  // Durum değişkenleri
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [settings, setSettings] = useState({});
  const [showStartTimePicker, setShowStartTimePicker] = useState(false);
  const [showEndTimePicker, setShowEndTimePicker] = useState(false);
  const [hasPermission, setHasPermission] = useState(false);

  // Verileri yükle
  const loadData = useCallback(async () => {
    try {
      setLoading(true);

      // Bildirim izinlerini kontrol et
      const permissionStatus = await notificationService.checkNotificationPermissions();
      setHasPermission(permissionStatus);

      // Bildirim ayarlarını getir
      const notificationTypes = ['transaction', 'budget', 'savings', 'reminder', 'system'];
      const settingsData = {};

      for (const type of notificationTypes) {
        const typeSetting = await notificationDbService.getNotificationSettings(db, type);

        if (typeSetting) {
          settingsData[type] = {
            ...typeSetting,
            is_enabled: typeSetting.is_enabled === 1,
            sound_enabled: typeSetting.sound_enabled === 1,
            vibration_enabled: typeSetting.vibration_enabled === 1,
            quiet_hours_enabled: typeSetting.quiet_hours_enabled === 1,
          };
        } else {
          // Varsayılan ayarlar
          settingsData[type] = {
            type,
            is_enabled: true,
            sound_enabled: true,
            vibration_enabled: true,
            quiet_hours_enabled: false,
            quiet_hours_start: '22:00',
            quiet_hours_end: '08:00',
          };
        }
      }

      setSettings(settingsData);
      setLoading(false);
    } catch (error) {
      console.error('Bildirim ayarları yükleme hatası:', error);
      Alert.alert('Hata', 'Bildirim ayarları yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  }, [db]);

  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [loadData])
  );

  // Ayarları güncelle
  const updateSettings = async () => {
    try {
      setSaving(true);

      // Her bildirim tipi için ayarları güncelle
      for (const type in settings) {
        await notificationDbService.updateNotificationSettings(db, type, settings[type]);
      }

      setSaving(false);
      Alert.alert('Başarılı', 'Bildirim ayarları güncellendi.');

      // Bekleyen bildirimleri kontrol et
      await notificationDbService.checkPendingNotifications(db);
    } catch (error) {
      console.error('Bildirim ayarları güncelleme hatası:', error);
      Alert.alert('Hata', 'Bildirim ayarları güncellenirken bir hata oluştu.');
      setSaving(false);
    }
  };

  // Bildirim izni iste
  const requestPermission = async () => {
    try {
      const permissionStatus = await notificationService.checkNotificationPermissions();
      setHasPermission(permissionStatus);

      if (!permissionStatus) {
        Alert.alert(
          'Bildirim İzni',
          'Bildirim izni verilmedi. Lütfen cihaz ayarlarından bildirimlere izin verin.'
        );
      }
    } catch (error) {
      console.error('Bildirim izni isteme hatası:', error);
      Alert.alert('Hata', 'Bildirim izni istenirken bir hata oluştu.');
    }
  };

  // Ayar değerini değiştir
  const handleSettingChange = (type, key, value) => {
    setSettings(prev => ({
      ...prev,
      [type]: {
        ...prev[type],
        [key]: value
      }
    }));
  };

  // Sessiz saat başlangıcı değiştiğinde
  const handleStartTimeChange = (event, selectedTime) => {
    setShowStartTimePicker(false);

    if (selectedTime) {
      const formattedTime = format(selectedTime, 'HH:mm');

      // Tüm bildirim tipleri için sessiz saat başlangıcını güncelle
      const updatedSettings = { ...settings };

      for (const type in updatedSettings) {
        updatedSettings[type].quiet_hours_start = formattedTime;
      }

      setSettings(updatedSettings);
    }
  };

  // Sessiz saat bitişi değiştiğinde
  const handleEndTimeChange = (event, selectedTime) => {
    setShowEndTimePicker(false);

    if (selectedTime) {
      const formattedTime = format(selectedTime, 'HH:mm');

      // Tüm bildirim tipleri için sessiz saat bitişini güncelle
      const updatedSettings = { ...settings };

      for (const type in updatedSettings) {
        updatedSettings[type].quiet_hours_end = formattedTime;
      }

      setSettings(updatedSettings);
    }
  };

  // Bildirim tipi adını getir
  const getNotificationTypeName = (type) => {
    switch (type) {
      case 'transaction':
        return 'İşlem Bildirimleri';
      case 'budget':
        return 'Bütçe Bildirimleri';
      case 'savings':
        return 'Birikim Bildirimleri';
      case 'reminder':
        return 'Hatırlatma Bildirimleri';
      case 'system':
        return 'Sistem Bildirimleri';
      default:
        return type;
    }
  };

  // Bildirim tipi simgesini getir
  const getNotificationTypeIcon = (type) => {
    switch (type) {
      case 'transaction':
        return 'receipt';
      case 'budget':
        return 'account-balance-wallet';
      case 'savings':
        return 'savings';
      case 'reminder':
        return 'notifications';
      case 'system':
        return 'info';
      default:
        return 'notifications';
    }
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={styles.loadingText}>Bildirim ayarları yükleniyor...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Bildirim Ayarları</Text>
        <TouchableOpacity
          style={styles.saveButton}
          onPress={updateSettings}
          disabled={saving}
        >
          {saving ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <MaterialIcons name="check" size={24} color="#fff" />
          )}
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        {/* Bildirim İzni */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <MaterialIcons name="notifications" size={20} color={Colors.PRIMARY} />
            <Text style={styles.sectionTitle}>Bildirim İzni</Text>
          </View>

          <View style={styles.permissionContainer}>
            <View style={styles.permissionInfo}>
              <Text style={styles.permissionText}>
                Bildirim izni: {hasPermission ? 'Verildi' : 'Verilmedi'}
              </Text>
              <Text style={styles.permissionDescription}>
                Bildirim almak için izin vermeniz gerekiyor.
              </Text>
            </View>

            <TouchableOpacity
              style={[
                styles.permissionButton,
                hasPermission ? styles.permissionGranted : styles.permissionDenied
              ]}
              onPress={requestPermission}
            >
              <MaterialIcons
                name={hasPermission ? 'check' : 'error'}
                size={20}
                color="#fff"
              />
            </TouchableOpacity>
          </View>
        </View>

        {/* Bildirim Tipleri */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <MaterialIcons name="category" size={20} color={Colors.PRIMARY} />
            <Text style={styles.sectionTitle}>Bildirim Tipleri</Text>
          </View>

          {Object.keys(settings).map((type) => (
            <View key={type} style={styles.notificationTypeItem}>
              <View style={styles.notificationTypeInfo}>
                <MaterialIcons
                  name={getNotificationTypeIcon(type)}
                  size={20}
                  color={Colors.PRIMARY}
                />
                <Text style={styles.notificationTypeName}>
                  {getNotificationTypeName(type)}
                </Text>
              </View>

              <Switch
                value={settings[type].is_enabled}
                onValueChange={(value) => handleSettingChange(type, 'is_enabled', value)}
                trackColor={{ false: Colors.GRAY_300, true: Colors.PRIMARY_LIGHT }}
                thumbColor={settings[type].is_enabled ? Colors.PRIMARY : Colors.GRAY_500}
              />
            </View>
          ))}
        </View>

        {/* Sessiz Saatler */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <MaterialIcons name="access-time" size={20} color={Colors.PRIMARY} />
            <Text style={styles.sectionTitle}>Sessiz Saatler</Text>
          </View>

          <View style={styles.quietHoursContainer}>
            <View style={styles.quietHoursInfo}>
              <Text style={styles.quietHoursText}>Sessiz saatler</Text>
              <Text style={styles.quietHoursDescription}>
                Belirlenen saatler arasında bildirim sesi çalmaz.
              </Text>
            </View>

            <Switch
              value={settings.system.quiet_hours_enabled}
              onValueChange={(value) => {
                // Tüm bildirim tipleri için sessiz saatleri güncelle
                const updatedSettings = { ...settings };

                for (const type in updatedSettings) {
                  updatedSettings[type].quiet_hours_enabled = value;
                }

                setSettings(updatedSettings);
              }}
              trackColor={{ false: Colors.GRAY_300, true: Colors.PRIMARY_LIGHT }}
              thumbColor={settings.system.quiet_hours_enabled ? Colors.PRIMARY : Colors.GRAY_500}
            />
          </View>

          {settings.system.quiet_hours_enabled && (
            <View style={styles.timePickerContainer}>
              <TouchableOpacity
                style={styles.timePicker}
                onPress={() => setShowStartTimePicker(true)}
              >
                <Text style={styles.timePickerLabel}>Başlangıç</Text>
                <Text style={styles.timePickerValue}>
                  {settings.system.quiet_hours_start || '22:00'}
                </Text>
              </TouchableOpacity>

              <Text style={styles.timePickerSeparator}>-</Text>

              <TouchableOpacity
                style={styles.timePicker}
                onPress={() => setShowEndTimePicker(true)}
              >
                <Text style={styles.timePickerLabel}>Bitiş</Text>
                <Text style={styles.timePickerValue}>
                  {settings.system.quiet_hours_end || '08:00'}
                </Text>
              </TouchableOpacity>
            </View>
          )}

          {showStartTimePicker && (
            <DateTimePicker
              value={parseTime(settings.system.quiet_hours_start || '22:00')}
              mode="time"
              is24Hour={true}
              display="default"
              onChange={handleStartTimeChange}
            />
          )}

          {showEndTimePicker && (
            <DateTimePicker
              value={parseTime(settings.system.quiet_hours_end || '08:00')}
              mode="time"
              is24Hour={true}
              display="default"
              onChange={handleEndTimeChange}
            />
          )}
        </View>

        {/* Ses ve Titreşim */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <MaterialIcons name="volume-up" size={20} color={Colors.PRIMARY} />
            <Text style={styles.sectionTitle}>Ses ve Titreşim</Text>
          </View>

          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Bildirim Sesi</Text>
            <Switch
              value={settings.system.sound_enabled}
              onValueChange={(value) => {
                // Tüm bildirim tipleri için ses ayarını güncelle
                const updatedSettings = { ...settings };

                for (const type in updatedSettings) {
                  updatedSettings[type].sound_enabled = value;
                }

                setSettings(updatedSettings);
              }}
              trackColor={{ false: Colors.GRAY_300, true: Colors.PRIMARY_LIGHT }}
              thumbColor={settings.system.sound_enabled ? Colors.PRIMARY : Colors.GRAY_500}
            />
          </View>

          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Titreşim</Text>
            <Switch
              value={settings.system.vibration_enabled}
              onValueChange={(value) => {
                // Tüm bildirim tipleri için titreşim ayarını güncelle
                const updatedSettings = { ...settings };

                for (const type in updatedSettings) {
                  updatedSettings[type].vibration_enabled = value;
                }

                setSettings(updatedSettings);
              }}
              trackColor={{ false: Colors.GRAY_300, true: Colors.PRIMARY_LIGHT }}
              thumbColor={settings.system.vibration_enabled ? Colors.PRIMARY : Colors.GRAY_500}
            />
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

/**
 * Saat formatını Date nesnesine dönüştürür
 *
 * @param {string} timeString - Saat formatı (HH:MM)
 * @returns {Date} Date nesnesi
 */
const parseTime = (timeString) => {
  const [hours, minutes] = timeString.split(':').map(Number);
  const date = new Date();
  date.setHours(hours);
  date.setMinutes(minutes);
  return date;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.GRAY_600,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 16,
    paddingHorizontal: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  headerTitle: {
    flex: 1,
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  saveButton: {
    padding: 8,
    marginLeft: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.GRAY_800,
    marginLeft: 8,
  },
  permissionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  permissionInfo: {
    flex: 1,
  },
  permissionText: {
    fontSize: 16,
    color: Colors.GRAY_800,
    marginBottom: 4,
  },
  permissionDescription: {
    fontSize: 12,
    color: Colors.GRAY_600,
  },
  permissionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  permissionGranted: {
    backgroundColor: Colors.SUCCESS,
  },
  permissionDenied: {
    backgroundColor: Colors.DANGER,
  },
  notificationTypeItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200,
  },
  notificationTypeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  notificationTypeName: {
    fontSize: 16,
    color: Colors.GRAY_800,
    marginLeft: 8,
  },
  quietHoursContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  quietHoursInfo: {
    flex: 1,
  },
  quietHoursText: {
    fontSize: 16,
    color: Colors.GRAY_800,
    marginBottom: 4,
  },
  quietHoursDescription: {
    fontSize: 12,
    color: Colors.GRAY_600,
  },
  timePickerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  timePicker: {
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    width: 120,
  },
  timePickerLabel: {
    fontSize: 12,
    color: Colors.GRAY_600,
    marginBottom: 4,
  },
  timePickerValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.PRIMARY,
  },
  timePickerSeparator: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.GRAY_600,
    marginHorizontal: 16,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200,
  },
  settingLabel: {
    fontSize: 16,
    color: Colors.GRAY_800,
  },
});
