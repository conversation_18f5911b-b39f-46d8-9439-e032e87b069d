/**
 * H<PERSON><PERSON><PERSON>ı Gider Modal Bileşeni
 * ModernBudgetScreen için özel tasarlanmış modal
 */

import React, { useState, useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// Simple Picker replacement for now
const SimplePicker = ({ selectedValue, onValueChange, style, children, enabled = true }) => (
  <View style={[{ borderWidth: 1, borderColor: '#ddd', borderRadius: 8, backgroundColor: enabled ? 'white' : '#f5f5f5' }, style]}>
    {children}
  </View>
);

const PickerItem = ({ label, value }) => null; // Placeholder
SimplePicker.Item = PickerItem;

/**
 * Hızlı Gider Modal Bileşeni
 * @param {Object} props - Bileşen özellikleri
 * @param {boolean} props.visible - Modal görünürlüğü
 * @param {Function} props.onClose - Modal kapatma fonksiyonu
 * @param {Function} props.onSave - Gider kaydetme fonksiyonu
 * @param {Array} props.budgets - Mevcut bütçeler
 * @param {Array} props.categories - Kategoriler
 * @param {Object} props.theme - Tema bilgileri
 * @param {string} props.defaultCurrency - Varsayılan para birimi
 */
const QuickExpenseModal = ({
  visible,
  onClose,
  onSave,
  budgets = [],
  categories = [],
  theme,
  defaultCurrency = 'TRY',
}) => {
  const [amount, setAmount] = useState('');
  const [selectedBudget, setSelectedBudget] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [description, setDescription] = useState('');
  const [loading, setLoading] = useState(false);

  // Modal açıldığında formu sıfırla
  useEffect(() => {
    if (visible) {
      setAmount('');
      setSelectedBudget('');
      setSelectedCategory('');
      setDescription('');
    }
  }, [visible]);

  /**
   * Formu doğrula
   */
  const validateForm = () => {
    if (!amount || parseFloat(amount) <= 0) {
      Alert.alert('Hata', 'Geçerli bir tutar giriniz.');
      return false;
    }
    if (!selectedBudget) {
      Alert.alert('Hata', 'Bir bütçe seçiniz.');
      return false;
    }
    if (!selectedCategory) {
      Alert.alert('Hata', 'Bir kategori seçiniz.');
      return false;
    }
    return true;
  };

  /**
   * Gideri kaydet
   */
  const handleSave = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);
      
      const expenseData = {
        amount: parseFloat(amount),
        budgetId: selectedBudget,
        categoryId: selectedCategory,
        description: description.trim() || 'Hızlı gider',
        currency: defaultCurrency,
        date: new Date().toISOString(),
      };

      await onSave(expenseData);
      onClose();
      
      Alert.alert(
        'Başarılı',
        'Gider başarıyla kaydedildi.',
        [{ text: 'Tamam' }]
      );
    } catch (error) {
      console.error('Hızlı gider kaydetme hatası:', error);
      Alert.alert(
        'Hata',
        'Gider kaydedilirken bir hata oluştu.',
        [{ text: 'Tamam' }]
      );
    } finally {
      setLoading(false);
    }
  };

  // Seçilen bütçeye ait kategorileri filtrele
  const getFilteredCategories = () => {
    const selectedBudgetData = budgets.find(b => b.id === selectedBudget);
    if (!selectedBudgetData?.category_ids) return categories;
    
    const budgetCategoryIds = selectedBudgetData.category_ids.split(',').map(id => parseInt(id));
    return categories.filter(cat => budgetCategoryIds.includes(cat.id));
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <KeyboardAvoidingView
        style={[styles.container, { backgroundColor: theme.background }]}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Header */}
        <View style={[styles.header, { backgroundColor: theme.primary }]}>
          <TouchableOpacity onPress={onClose} style={styles.headerButton}>
            <Ionicons name="close" size={24} color={theme.background} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: theme.background }]}>
            Hızlı Gider Ekle
          </Text>
          <TouchableOpacity
            onPress={handleSave}
            style={[styles.headerButton, { opacity: loading ? 0.5 : 1 }]}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator size="small" color={theme.background} />
            ) : (
              <Ionicons name="checkmark" size={24} color={theme.background} />
            )}
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Tutar */}
          <View style={styles.section}>
            <Text style={[styles.label, { color: theme.text }]}>Tutar</Text>
            <View style={[styles.amountContainer, { borderColor: theme.border }]}>
              <TextInput
                style={[styles.amountInput, { color: theme.text }]}
                value={amount}
                onChangeText={setAmount}
                placeholder="0.00"
                placeholderTextColor={theme.textSecondary}
                keyboardType="numeric"
                returnKeyType="next"
              />
              <Text style={[styles.currency, { color: theme.textSecondary }]}>
                {defaultCurrency}
              </Text>
            </View>
          </View>

          {/* Bütçe Seçimi */}
          <View style={styles.section}>
            <Text style={[styles.label, { color: theme.text }]}>Bütçe</Text>
            <View style={[styles.pickerContainer, { borderColor: theme.border }]}>
              <Text style={[styles.pickerPlaceholder, { color: selectedBudget ? theme.text : theme.textSecondary }]}>
                {selectedBudget ? budgets.find(b => b.id === selectedBudget)?.name || 'Bütçe seçiniz' : 'Bütçe seçiniz'}
              </Text>
            </View>
          </View>

          {/* Kategori Seçimi */}
          <View style={styles.section}>
            <Text style={[styles.label, { color: theme.text }]}>Kategori</Text>
            <View style={[styles.pickerContainer, { 
              borderColor: theme.border,
              backgroundColor: !selectedBudget ? '#f5f5f5' : theme.card 
            }]}>
              <Text style={[styles.pickerPlaceholder, { 
                color: selectedCategory ? theme.text : theme.textSecondary 
              }]}>
                {selectedCategory ? 
                  getFilteredCategories().find(c => c.id === selectedCategory)?.name || 'Kategori seçiniz' :
                  'Kategori seçiniz'
                }
              </Text>
            </View>
            {!selectedBudget && (
              <Text style={[styles.helperText, { color: theme.textSecondary }]}>
                Önce bir bütçe seçiniz
              </Text>
            )}
          </View>

          {/* Açıklama */}
          <View style={styles.section}>
            <Text style={[styles.label, { color: theme.text }]}>
              Açıklama (Opsiyonel)
            </Text>
            <TextInput
              style={[
                styles.descriptionInput,
                {
                  borderColor: theme.border,
                  color: theme.text,
                  backgroundColor: theme.card,
                },
              ]}
              value={description}
              onChangeText={setDescription}
              placeholder="Gider açıklaması..."
              placeholderTextColor={theme.textSecondary}
              multiline
              numberOfLines={3}
              returnKeyType="done"
            />
          </View>

          {/* Bilgi Notu */}
          <View style={[styles.infoBox, { backgroundColor: theme.card }]}>
            <Ionicons name="information-circle" size={20} color={theme.primary} />
            <Text style={[styles.infoText, { color: theme.textSecondary }]}>
              Bu gider seçilen bütçeden düşülecek ve işlem geçmişinize eklenecektir.
            </Text>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: 44, // Safe area için
  },
  headerButton: {
    padding: 8,
    width: 40,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  amountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  amountInput: {
    flex: 1,
    fontSize: 24,
    fontWeight: 'bold',
  },
  currency: {
    fontSize: 18,
    fontWeight: '500',
    marginLeft: 8,
  },
  pickerContainer: {
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
    minHeight: 50,
    justifyContent: 'center',
  },
  pickerPlaceholder: {
    fontSize: 16,
  },
  helperText: {
    fontSize: 14,
    marginTop: 4,
    fontStyle: 'italic',
  },
  descriptionInput: {
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    textAlignVertical: 'top',
    minHeight: 80,
  },
  infoBox: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 16,
    borderRadius: 12,
    marginTop: 16,
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
    marginLeft: 8,
  },
});

export default QuickExpenseModal;
