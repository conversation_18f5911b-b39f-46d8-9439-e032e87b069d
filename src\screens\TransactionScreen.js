import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Text,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Alert
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import { MaterialIcons } from '@expo/vector-icons';
import { useTransactionService } from '../db/dbService';
import { Colors } from '../constants/colors';
import { useAppContext } from '../context/AppContext';
import { useExchangeRates } from '../contexts/ExchangeRateContext';

// Bileşenler
import TransactionItem from '../components/transactions/TransactionItem';
import TransactionFilter from '../components/transactions/TransactionFilter';
import LoadingIndicator from '../components/common/LoadingIndicator';
import EmptyState from '../components/common/EmptyState';

/**
 * İşlem yönetim ekranı
 * @param {Object} props - Bileşen props'ları
 * @param {Object} props.navigation - Navigation nesnesi
 * @param {Object} props.route - Route nesnesi
 * @returns {JSX.Element} İşlem yönetim ekranı
 */
const TransactionScreen = ({ navigation, route }) => {
  const db = useSQLiteContext();
  const transactionService = useTransactionService();
  const { rates } = useExchangeRates();
  const { theme } = useAppContext();

  // URL parametrelerini al
  const params = route?.params || {};
  const initialType = params.type || 'expense';

  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [transactions, setTransactions] = useState([]);
  const [selectedType, setSelectedType] = useState(initialType); // 'expense' or 'income'
  const [stats, setStats] = useState({
    totalAmount: 0,
    count: 0
  });
  const [filters, setFilters] = useState({
    startDate: null,
    endDate: null,
    categoryId: null,
    minAmount: null,
    maxAmount: null
  });
  const [showFilters, setShowFilters] = useState(false);

  // İşlemleri yükle
  const loadTransactions = useCallback(async () => {
    try {
      setIsLoading(true);

      // İşlemleri getir
      const data = await transactionService.getTransactions({
        isIncome: selectedType === 'income'
      });

      // İşlemleri ayarla
      setTransactions(data);

      // İstatistikleri hesapla
      if (data.length > 0) {
        const total = data.reduce((sum, tx) => sum + tx.amount, 0);
        setStats({
          totalAmount: total,
          count: data.length
        });
      } else {
        setStats({
          totalAmount: 0,
          count: 0
        });
      }
    } catch (error) {
      console.error('İşlemler yüklenirken hata:', error);
      Alert.alert('Hata', 'İşlemler yüklenirken bir hata oluştu.');
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  }, [transactionService, selectedType]);

  // Yenileme işlemi
  const handleRefresh = () => {
    setRefreshing(true);
    loadTransactions();
  };

  // İşlem tipi değiştiğinde işlemleri yeniden yükle
  useEffect(() => {
    loadTransactions();
  }, [loadTransactions, selectedType]);

  // İşlem silme
  const handleDeleteTransaction = useCallback(async (id) => {
    if (!id) return;

    Alert.alert(
      'İşlemi Sil',
      'Bu işlemi silmek istediğinize emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              setIsLoading(true);
              await transactionService.deleteTransaction(id);
              loadTransactions();
            } catch (error) {
              console.error('İşlem silinirken hata:', error);
              Alert.alert('Hata', 'İşlem silinirken bir hata oluştu.');
            } finally {
              setIsLoading(false);
            }
          }
        }
      ]
    );
  }, [transactionService, loadTransactions]);

  // Yeni işlem ekleme
  const handleAddTransaction = useCallback(() => {
    navigation.navigate('transaction/edit', {
      type: selectedType
    });
  }, [navigation, selectedType]);

  // İşlem düzenleme
  const handleEditTransaction = useCallback((transaction) => {
    if (!transaction?.id) return;

    navigation.navigate('transaction/edit', {
      id: transaction.id,
      type: selectedType
    });
  }, [navigation, selectedType]);

  // İşlem öğesine tıklandığında
  const handleTransactionPress = useCallback((transaction) => {
    if (!transaction?.id) return;

    // İşlem detaylarını göster
    navigation.navigate('TransactionDetail', {
      transactionId: transaction.id
    });
  }, [navigation]);

  // Para birimini formatla
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 2
    }).format(amount);
  };

  // İşlem öğesi
  const renderTransactionItem = ({ item }) => (
    <TransactionItem
      transaction={item}
      onPress={() => handleTransactionPress(item)}
      onDelete={() => handleDeleteTransaction(item.id)}
    />
  );

  if (isLoading) {
    return <LoadingIndicator />;
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Başlık */}
      <View style={[styles.header, { backgroundColor: theme.colors.surface, borderBottomColor: theme.colors.border }]}>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>İşlemler</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => navigation.navigate('transaction/edit', { type: selectedType })}
        >
          <MaterialIcons name="add" size={24} color={theme.colors.white} />
        </TouchableOpacity>
      </View>

      {/* İşlem Tipi Seçici */}
      <View style={[styles.typeSelector, { backgroundColor: theme.colors.surface, borderBottomColor: theme.colors.border }]}>
        <TouchableOpacity
          style={[
            styles.typeOption,
            { backgroundColor: theme.colors.backgroundSecondary },
            selectedType === 'expense' && [styles.activeTypeOption, { backgroundColor: theme.colors.error }]
          ]}
          onPress={() => setSelectedType('expense')}
        >
          <MaterialIcons
            name="remove-circle"
            size={20}
            color={selectedType === 'expense' ? theme.colors.white : theme.colors.textSecondary}
          />
          <Text
            style={[
              styles.typeText,
              { color: theme.colors.textSecondary },
              selectedType === 'expense' && [styles.activeTypeText, { color: theme.colors.white }]
            ]}
          >
            Giderler
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.typeOption,
            { backgroundColor: theme.colors.backgroundSecondary },
            selectedType === 'income' && [styles.activeTypeOption, { backgroundColor: theme.colors.success }]
          ]}
          onPress={() => setSelectedType('income')}
        >
          <MaterialIcons
            name="add-circle"
            size={20}
            color={selectedType === 'income' ? theme.colors.white : theme.colors.textSecondary}
          />
          <Text
            style={[
              styles.typeText,
              { color: theme.colors.textSecondary },
              selectedType === 'income' && [styles.activeTypeText, { color: theme.colors.white }]
            ]}
          >
            Gelirler
          </Text>
        </TouchableOpacity>
      </View>

      {/* Toplam Özeti */}
      <View style={[styles.summaryCard, { backgroundColor: theme.colors.surface, borderBottomColor: theme.colors.border }]}>
        <Text style={[styles.summaryTitle, { color: theme.colors.textSecondary }]}>
          Toplam {selectedType === 'income' ? 'Gelir' : 'Gider'}
        </Text>
        <Text style={[
          styles.summaryAmount,
          { color: selectedType === 'income' ? theme.colors.success : theme.colors.error }
        ]}>
          {formatCurrency(stats.totalAmount)}
        </Text>
        <Text style={[styles.summaryCount, { color: theme.colors.textSecondary }]}>
          {stats.count} işlem
        </Text>
      </View>

      {/* İşlem Listesi */}
      {transactions.length > 0 ? (
        <FlatList
          data={transactions}
          renderItem={renderTransactionItem}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.listContent}
          onRefresh={handleRefresh}
          refreshing={refreshing}
        />
      ) : (
        <EmptyState
          icon={selectedType === 'income' ? 'account-balance-wallet' : 'receipt-long'}
          message={`Henüz ${selectedType === 'income' ? 'gelir' : 'gider'} kaydı bulunmuyor`}
          actionText={`Yeni ${selectedType === 'income' ? 'Gelir' : 'Gider'} Ekle`}
          onAction={() => navigation.navigate('transaction/edit', { type: selectedType })}
        />
      )}

      {/* Yeni İşlem Butonu */}
      <TouchableOpacity
        style={[
          styles.floatingButton,
          { backgroundColor: selectedType === 'income' ? theme.colors.success : theme.colors.error }
        ]}
        onPress={() => navigation.navigate('transaction/edit', { type: selectedType })}
      >
        <MaterialIcons name="add" size={24} color={theme.colors.white} />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
  },
  typeSelector: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  typeOption: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  activeTypeOption: {
    // Background color set dynamically
  },
  typeText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
  activeTypeText: {
    fontWeight: '600',
  },
  summaryCard: {
    padding: 16,
    borderBottomWidth: 1,
    alignItems: 'center',
  },
  summaryTitle: {
    fontSize: 14,
    marginBottom: 4,
  },
  summaryAmount: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  summaryCount: {
    fontSize: 12,
  },
  listContent: {
    padding: 16,
    paddingBottom: 80,
  },
  floatingButton: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
});

export default TransactionScreen;
