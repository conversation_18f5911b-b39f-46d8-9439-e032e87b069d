import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import CustomButton from './CustomButton';
import { LAYOUT, ICON_SELECTOR_THEME } from '../../constants/themeConstants';

/**
 * İkon seçici bileşeni
 * @param {Object} props Component props
 * @param {string[]} props.icons - Gösterilecek ikonların listesi
 * @param {string} props.selectedIcon - Seçili ikonun adı
 * @param {Function} props.onSelect - İkon seçildiğinde çağrılacak fonksiyon
 * @param {boolean} props.isDarkMode - Karanlık mod durumu
 */
const IconSelector = ({ 
  icons = [], 
  selectedIcon, 
  onSelect,
  isDarkMode = false 
}) => {
  const theme = isDarkMode ? ICON_SELECTOR_THEME.dark : ICON_SELECTOR_THEME.light;

  return (
    <ScrollView 
      horizontal 
      showsHorizontalScrollIndicator={false}
      style={styles.container}
    >
      <View style={styles.iconGrid}>
        {icons.map((icon) => (
          <CustomButton
            key={icon}
            style={[
              styles.iconButton,
              {
                backgroundColor: selectedIcon === icon 
                  ? theme.selected.background
                  : theme.unselected.background,
                borderColor: selectedIcon === icon
                  ? theme.selected.border
                  : theme.unselected.border
              },
              selectedIcon === icon && styles.selectedIcon
            ]}
            onPress={() => onSelect(icon)}
          >
            <MaterialIcons
              name={icon}
              size={24}
              color={selectedIcon === icon ? theme.selected.text : theme.unselected.text}
            />
          </CustomButton>
        ))}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flexGrow: 0
  },
  iconGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: LAYOUT.spacing.sm,
    paddingVertical: LAYOUT.spacing.sm
  },
  iconButton: {
    width: 48,
    height: 48,
    borderRadius: LAYOUT.radius.md,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1
  },
  selectedIcon: {
    transform: [{ scale: 1.1 }]
  }
});

export default React.memo(IconSelector);
