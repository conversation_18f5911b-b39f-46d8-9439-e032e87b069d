import React from 'react';
import { View, Text, FlatList, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import TransactionItem from './TransactionItem';
import { Colors } from '../../constants/colors';

/**
 * İşlem listesi bileşeni
 * @param {Object} props Component props
 * @param {Array} props.transactions İşlem listesi
 * @param {boolean} props.loading Yükleniyor mu?
 * @param {Function} props.onRefresh Yenileme işlevi
 * @param {boolean} props.refreshing Yenileniyor mu?
 * @param {Function} props.onDeleteTransaction İşlem silme işlevi
 * @param {Function} props.onEditTransaction İşlem düzenleme işlevi
 * @param {Function} props.onViewTransaction İşlem görüntüleme işlevi
 * @param {string} props.emptyMessage Boş liste mesajı
 */
const TransactionList = ({
  transactions,
  loading,
  onRefresh,
  refreshing,
  onDeleteTransaction,
  onEditTransaction,
  onViewTransaction,
  emptyMessage = 'Henüz işlem bulunmuyor'
}) => {
  // Boş liste görünümü
  const renderEmptyList = () => {
    if (loading) {
      return (
        <View style={styles.emptyContainer}>
          <ActivityIndicator size="large" color={Colors.PRIMARY} />
          <Text style={styles.emptyText}>İşlemler yükleniyor...</Text>
        </View>
      );
    }

    return (
      <View style={styles.emptyContainer}>
        <MaterialIcons name="receipt-long" size={64} color={Colors.GRAY} />
        <Text style={styles.emptyText}>{emptyMessage}</Text>
      </View>
    );
  };

  // Liste başlığı
  const renderHeader = () => {
    if (!transactions || transactions.length === 0) {
      return null;
    }

    return (
      <View style={styles.headerContainer}>
        <Text style={styles.headerText}>İşlemler</Text>
        <Text style={styles.headerCount}>{transactions.length} işlem</Text>
      </View>
    );
  };

  // Liste öğesi
  const renderItem = ({ item }) => (
    <TransactionItem
      transaction={item}
      onDelete={() => onDeleteTransaction(item.id)}
      onEdit={() => onEditTransaction(item)}
      onPress={() => onViewTransaction(item)}
    />
  );

  return (
    <FlatList
      data={transactions}
      renderItem={renderItem}
      keyExtractor={(item) => item.id.toString()}
      contentContainerStyle={styles.listContainer}
      ListEmptyComponent={renderEmptyList}
      ListHeaderComponent={renderHeader}
      refreshing={refreshing}
      onRefresh={onRefresh}
    />
  );
};

const styles = StyleSheet.create({
  listContainer: {
    flexGrow: 1,
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 48,
  },
  emptyText: {
    fontSize: 16,
    color: Colors.GRAY_DARK,
    marginTop: 16,
    textAlign: 'center',
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingVertical: 8,
  },
  headerText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.BLACK,
  },
  headerCount: {
    fontSize: 14,
    color: Colors.GRAY_DARK,
  },
});

export default TransactionList;
