import tokens from '../tokens';

/**
 * Theme Utilities
 * Tema ile ilgili yardımcı fonksiyonlar
 */

/**
 * Renk manipülasyon fonksiyonları
 */

/**
 * Hex rengi RGB'ye çevirir
 * @param {string} hex - Hex renk kodu
 * @returns {Object} RGB değerleri
 */
export const hexToRgb = (hex) => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
};

/**
 * RGB'yi hex'e çevirir
 * @param {number} r - Red değeri
 * @param {number} g - Green değeri
 * @param {number} b - Blue değeri
 * @returns {string} Hex renk kodu
 */
export const rgbToHex = (r, g, b) => {
  return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
};

/**
 * Rengi alpha ile birleştirir
 * @param {string} color - Renk kodu
 * @param {number} alpha - Alpha değeri (0-1)
 * @returns {string} RGBA renk kodu
 */
export const withAlpha = (color, alpha) => {
  const rgb = hexToRgb(color);
  if (!rgb) return color;
  
  return `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${alpha})`;
};

/**
 * Rengi açar (lighter)
 * @param {string} color - Renk kodu
 * @param {number} amount - Açma miktarı (0-1)
 * @returns {string} Açılmış renk
 */
export const lighten = (color, amount) => {
  const rgb = hexToRgb(color);
  if (!rgb) return color;

  const r = Math.min(255, Math.floor(rgb.r + (255 - rgb.r) * amount));
  const g = Math.min(255, Math.floor(rgb.g + (255 - rgb.g) * amount));
  const b = Math.min(255, Math.floor(rgb.b + (255 - rgb.b) * amount));

  return rgbToHex(r, g, b);
};

/**
 * Rengi koyulaştırır (darker)
 * @param {string} color - Renk kodu
 * @param {number} amount - Koyulaştırma miktarı (0-1)
 * @returns {string} Koyulaştırılmış renk
 */
export const darken = (color, amount) => {
  const rgb = hexToRgb(color);
  if (!rgb) return color;

  const r = Math.max(0, Math.floor(rgb.r * (1 - amount)));
  const g = Math.max(0, Math.floor(rgb.g * (1 - amount)));
  const b = Math.max(0, Math.floor(rgb.b * (1 - amount)));

  return rgbToHex(r, g, b);
};

/**
 * Tema yardımcı fonksiyonları
 */

/**
 * Tema rengini alır
 * @param {Object} theme - Tema objesi
 * @param {string} path - Renk yolu (örn: 'primary', 'text.primary')
 * @param {string} fallback - Varsayılan renk
 * @returns {string} Renk değeri
 */
export const getThemeColor = (theme, path, fallback = '#000000') => {
  if (!theme || !path) return fallback;

  const keys = path.split('.');
  let color = theme.colors;

  for (const key of keys) {
    color = color?.[key];
    if (color === undefined) break;
  }

  return color || fallback;
};

/**
 * Kontrast rengini hesaplar
 * @param {string} backgroundColor - Arka plan rengi
 * @param {string} lightColor - Açık renk seçeneği
 * @param {string} darkColor - Koyu renk seçeneği
 * @returns {string} Kontrast rengi
 */
export const getContrastColor = (backgroundColor, lightColor = '#ffffff', darkColor = '#000000') => {
  const rgb = hexToRgb(backgroundColor);
  if (!rgb) return darkColor;

  // Luminance hesaplama
  const luminance = (0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b) / 255;
  
  return luminance > 0.5 ? darkColor : lightColor;
};

/**
 * Tema varyantları oluşturur
 * @param {string} baseColor - Ana renk
 * @returns {Object} Renk varyantları
 */
export const createColorVariants = (baseColor) => {
  return {
    50: lighten(baseColor, 0.9),
    100: lighten(baseColor, 0.8),
    200: lighten(baseColor, 0.6),
    300: lighten(baseColor, 0.4),
    400: lighten(baseColor, 0.2),
    500: baseColor,
    600: darken(baseColor, 0.1),
    700: darken(baseColor, 0.2),
    800: darken(baseColor, 0.3),
    900: darken(baseColor, 0.4),
  };
};

/**
 * Tema oluşturucu
 * @param {Object} config - Tema konfigürasyonu
 * @returns {Object} Tema objesi
 */
export const createTheme = (config = {}) => {
  const {
    primary = tokens.colors.primary[500],
    secondary = tokens.colors.gray[600],
    success = tokens.colors.success[500],
    danger = tokens.colors.danger[500],
    warning = tokens.colors.warning[500],
    info = tokens.colors.info[500],
    background = '#ffffff',
    surface = '#ffffff',
    isDark = false,
  } = config;

  const primaryVariants = createColorVariants(primary);
  const textColors = isDark ? {
    primary: tokens.colors.gray[100],
    secondary: tokens.colors.gray[300],
    tertiary: tokens.colors.gray[400],
    inverse: tokens.colors.gray[900],
  } : {
    primary: tokens.colors.gray[900],
    secondary: tokens.colors.gray[600],
    tertiary: tokens.colors.gray[500],
    inverse: '#ffffff',
  };

  return {
    colors: {
      primary: primaryVariants[500],
      primaryLight: primaryVariants[400],
      primaryDark: primaryVariants[600],
      secondary,
      success,
      danger,
      warning,
      info,
      background,
      surface,
      card: surface,
      text: textColors,
      border: isDark ? tokens.colors.gray[700] : tokens.colors.gray[200],
      divider: isDark ? tokens.colors.gray[800] : tokens.colors.gray[100],
      income: success,
      expense: danger,
    },
    variants: primaryVariants,
    isDark,
  };
};

/**
 * Tema geçiş animasyonları
 */
export const themeTransition = {
  duration: tokens.duration.normal,
  easing: tokens.easing.easeInOut,
};

/**
 * Accessibility yardımcıları
 */

/**
 * WCAG kontrast oranını hesaplar
 * @param {string} color1 - İlk renk
 * @param {string} color2 - İkinci renk
 * @returns {number} Kontrast oranı
 */
export const getContrastRatio = (color1, color2) => {
  const rgb1 = hexToRgb(color1);
  const rgb2 = hexToRgb(color2);
  
  if (!rgb1 || !rgb2) return 1;

  const getLuminance = (rgb) => {
    const { r, g, b } = rgb;
    const [rs, gs, bs] = [r, g, b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
  };

  const lum1 = getLuminance(rgb1);
  const lum2 = getLuminance(rgb2);
  
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);
  
  return (brightest + 0.05) / (darkest + 0.05);
};

/**
 * WCAG AA uyumluluğunu kontrol eder
 * @param {string} color1 - İlk renk
 * @param {string} color2 - İkinci renk
 * @param {string} level - 'AA' veya 'AAA'
 * @returns {boolean} Uyumlu mu
 */
export const isAccessible = (color1, color2, level = 'AA') => {
  const ratio = getContrastRatio(color1, color2);
  const threshold = level === 'AAA' ? 7 : 4.5;
  return ratio >= threshold;
};

export default {
  hexToRgb,
  rgbToHex,
  withAlpha,
  lighten,
  darken,
  getThemeColor,
  getContrastColor,
  createColorVariants,
  createTheme,
  themeTransition,
  getContrastRatio,
  isAccessible,
};
