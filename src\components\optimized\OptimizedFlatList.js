import React, { memo, useCallback, useMemo } from 'react';
import { FlatList } from 'react-native';
import { useOptimizedList, usePerformanceMonitor } from '../../hooks/usePerformance';

/**
 * Optimized FlatList Component
 * Performance optimizasyonları ile geliştirilmiş FlatList
 */
const OptimizedFlatList = memo(({
  data,
  renderItem,
  keyExtractor,
  itemHeight = 80,
  windowSize = 10,
  maxToRenderPerBatch = 5,
  initialNumToRender = 10,
  removeClippedSubviews = true,
  onEndReachedThreshold = 0.5,
  onEndReached,
  ListHeaderComponent,
  ListFooterComponent,
  ListEmptyComponent,
  refreshControl,
  contentContainerStyle,
  style,
  ...props
}) => {
  // Performance monitoring
  usePerformanceMonitor('OptimizedFlatList');

  // Optimized list props
  const optimizedProps = useOptimizedList(data, {
    itemHeight,
    windowSize,
    maxToRenderPerBatch,
    initialNumToRender,
    removeClippedSubviews,
  });

  // Memoized render item
  const memoizedRenderItem = useCallback((itemData) => {
    return renderItem(itemData);
  }, [renderItem]);

  // Memoized key extractor
  const memoizedKeyExtractor = useCallback((item, index) => {
    if (keyExtractor) {
      return keyExtractor(item, index);
    }
    return item.id?.toString() || index.toString();
  }, [keyExtractor]);

  // Memoized get item layout
  const getItemLayout = useMemo(() => {
    if (!itemHeight) return undefined;
    
    return (data, index) => ({
      length: itemHeight,
      offset: itemHeight * index,
      index,
    });
  }, [itemHeight]);

  // Memoized on end reached
  const memoizedOnEndReached = useCallback((info) => {
    if (onEndReached) {
      onEndReached(info);
    }
  }, [onEndReached]);

  return (
    <FlatList
      {...optimizedProps}
      {...props}
      data={data}
      renderItem={memoizedRenderItem}
      keyExtractor={memoizedKeyExtractor}
      getItemLayout={getItemLayout}
      onEndReached={memoizedOnEndReached}
      onEndReachedThreshold={onEndReachedThreshold}
      ListHeaderComponent={ListHeaderComponent}
      ListFooterComponent={ListFooterComponent}
      ListEmptyComponent={ListEmptyComponent}
      refreshControl={refreshControl}
      contentContainerStyle={contentContainerStyle}
      style={style}
      // Performance optimizations
      removeClippedSubviews={removeClippedSubviews}
      maxToRenderPerBatch={maxToRenderPerBatch}
      updateCellsBatchingPeriod={50}
      windowSize={windowSize}
      initialNumToRender={initialNumToRender}
      legacyImplementation={false}
      disableVirtualization={false}
      // Memory optimizations
      maintainVisibleContentPosition={{
        minIndexForVisible: 0,
        autoscrollToTopThreshold: 10,
      }}
    />
  );
});

OptimizedFlatList.displayName = 'OptimizedFlatList';

export default OptimizedFlatList;
