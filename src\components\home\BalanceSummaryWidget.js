import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';
import { useAppContext } from '../../context/AppContext';

/**
 * Bakiye özeti widget'ı - <PERSON><PERSON><PERSON>, gider ve bakiye bilgilerini görüntüler
 * Modern kartlar halinde tasarlanmış bakiye özetini gösterir
 *
 * @param {Object} props - Bileşen props'ları
 * @param {Object} props.balanceSummary - Bakiye özet bilgileri
 * @param {number} props.balanceSummary.income - Toplam gelir
 * @param {number} props.balanceSummary.expense - Toplam gider
 * @param {number} props.balanceSummary.balance - Net bakiye
 * @param {Function} props.onPress - Widget'a tıklandığında çalışacak fonksiyon
 * @param {Function} props.onIncomePress - Gelir kartına tıklandığında çalışacak fonksiyon
 * @param {Function} props.onExpensePress - Gider kartına tıklandığında çalışacak fonksiyon
 * @param {Function} props.onBalancePress - Bakiye kartına tıklandığında çalışacak fonksiyon
 * @returns {JSX.Element} BalanceSummaryWidget bileşeni
 */
const BalanceSummaryWidget = ({
  balanceSummary,
  onPress,
  onIncomePress,
  onExpensePress,
  onBalancePress,
}) => {
  const { theme } = useTheme();
  const { defaultCurrency } = useAppContext();

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: defaultCurrency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getBalanceColor = (balance) => {
    if (balance > 0) return theme.SUCCESS;
    if (balance < 0) return theme.DANGER;
    return theme.TEXT_SECONDARY;
  };

  const getBalanceIcon = (balance) => {
    if (balance > 0) return 'trending-up';
    if (balance < 0) return 'trending-down';
    return 'trending-flat';
  };

  return (
    <TouchableOpacity
      style={[styles.container, { backgroundColor: theme.CARD }]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <View style={[styles.header, { borderBottomColor: theme.BORDER }]}>
        <View style={styles.headerLeft}>
          <MaterialIcons name="account-balance-wallet" size={24} color={theme.PRIMARY} />
          <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
            Bakiye Özeti
          </Text>
        </View>
        <MaterialIcons name="chevron-right" size={20} color={theme.TEXT_SECONDARY} />
      </View>

      <View style={styles.content}>
        {/* Gelir Kartı */}
        <TouchableOpacity
          style={[styles.summaryCard, styles.incomeCard]}
          onPress={onIncomePress}
          activeOpacity={0.7}
        >
          <View style={[styles.cardContent, { backgroundColor: theme.SUCCESS + '10' }]}>
            <View style={[styles.cardIconContainer, { backgroundColor: theme.SUCCESS + '20' }]}>
              <MaterialIcons name="add-circle" size={20} color={theme.SUCCESS} />
            </View>
            <View style={styles.cardInfo}>
              <Text style={[styles.cardLabel, { color: theme.TEXT_SECONDARY }]}>
                Gelir
              </Text>
              <Text style={[styles.cardAmount, { color: theme.SUCCESS }]}>
                {formatCurrency(balanceSummary.income)}
              </Text>
            </View>
          </View>
        </TouchableOpacity>

        {/* Gider Kartı */}
        <TouchableOpacity
          style={[styles.summaryCard, styles.expenseCard]}
          onPress={onExpensePress}
          activeOpacity={0.7}
        >
          <View style={[styles.cardContent, { backgroundColor: theme.DANGER + '10' }]}>
            <View style={[styles.cardIconContainer, { backgroundColor: theme.DANGER + '20' }]}>
              <MaterialIcons name="remove-circle" size={20} color={theme.DANGER} />
            </View>
            <View style={styles.cardInfo}>
              <Text style={[styles.cardLabel, { color: theme.TEXT_SECONDARY }]}>
                Gider
              </Text>
              <Text style={[styles.cardAmount, { color: theme.DANGER }]}>
                {formatCurrency(balanceSummary.expense)}
              </Text>
            </View>
          </View>
        </TouchableOpacity>

        {/* Bakiye Kartı */}
        <TouchableOpacity
          style={[styles.summaryCard, styles.balanceCard]}
          onPress={onBalancePress}
          activeOpacity={0.7}
        >
          <View style={[styles.cardContent, { backgroundColor: getBalanceColor(balanceSummary.balance) + '10' }]}>
            <View style={[styles.cardIconContainer, { backgroundColor: getBalanceColor(balanceSummary.balance) + '20' }]}>
              <MaterialIcons 
                name={getBalanceIcon(balanceSummary.balance)} 
                size={20} 
                color={getBalanceColor(balanceSummary.balance)} 
              />
            </View>
            <View style={styles.cardInfo}>
              <Text style={[styles.cardLabel, { color: theme.TEXT_SECONDARY }]}>
                Net Bakiye
              </Text>
              <Text style={[styles.cardAmount, { color: getBalanceColor(balanceSummary.balance) }]}>
                {formatCurrency(balanceSummary.balance)}
              </Text>
            </View>
          </View>
        </TouchableOpacity>
      </View>

      {/* Alt Bilgi */}
      <View style={styles.footer}>
        <Text style={[styles.footerText, { color: theme.TEXT_SECONDARY }]}>
          Tüm hesaplardan toplam özet
        </Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  content: {
    padding: 16,
  },
  summaryCard: {
    borderRadius: 12,
    marginBottom: 12,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.05)',
  },
  cardContent: {
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  cardIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  cardInfo: {
    flex: 1,
  },
  cardLabel: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
  },
  cardAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  incomeCard: {
    marginBottom: 8,
  },
  expenseCard: {
    marginBottom: 8,
  },
  balanceCard: {
    marginBottom: 0,
  },
  footer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 12,
    fontStyle: 'italic',
  },
});

export default BalanceSummaryWidget;
