/**
 * Transactions tablosuna currency sütununu ekleyen migrasyon
 * 
 * @param {object} db - SQLite veritabanı nesnesi
 * @returns {Promise<void>}
 */
export const addCurrencyToTransactions = async (db) => {
  try {
    console.log('Transactions tablosuna currency sütunu ekleme migrasyonu başlatılıyor...');
    
    // Transactions tablosunda currency sütunu var mı kontrol et
    const columns = await db.getAllAsync(`PRAGMA table_info(transactions)`);
    
    // Sütun adlarını bir diziye dönüştür
    const columnNames = columns.map(col => col.name);
    
    // Currency sütunu yoksa ekle
    if (!columnNames.includes('currency')) {
      console.log('currency sütunu ekleniyor...');
      await db.execAsync(`ALTER TABLE transactions ADD COLUMN currency TEXT DEFAULT 'TRY'`);
      console.log('currency sütunu başarıyla eklendi.');
    } else {
      console.log('currency sütunu zaten mevcut.');
    }
    
    console.log('Transactions tablosuna currency sütunu ekleme migrasyonu tamamlandı.');
  } catch (error) {
    console.error('Transactions tablosuna currency sütunu ekleme migrasyon hatası:', error);
    throw error;
  }
};
