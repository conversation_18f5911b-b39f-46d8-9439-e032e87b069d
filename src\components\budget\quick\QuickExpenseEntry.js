import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Modal,
  FlatList,
  ActivityIndicator
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useSQLiteContext } from 'expo-sqlite';

/**
 * Quick Expense Entry Component
 * Hızlı harcama girişi ve budget impact calculation
 */
export default function QuickExpenseEntry({ 
  theme,
  onExpenseAdded,
  style 
}) {
  const db = useSQLiteContext();
  
  // State yönetimi
  const [visible, setVisible] = useState(false);
  const [amount, setAmount] = useState('');
  const [description, setDescription] = useState('');
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [categories, setCategories] = useState([]);
  const [budgetImpact, setBudgetImpact] = useState(null);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  /**
   * <PERSON>gorileri yükle
   */
  const loadCategories = async () => {
    try {
      const categoryResults = await db.getAllAsync(`
        SELECT * FROM categories 
        WHERE type = 'expense' 
        ORDER BY name ASC
      `);
      setCategories(categoryResults);
    } catch (error) {
      console.error('Kategoriler yüklenirken hata:', error);
    }
  };

  /**
   * Budget impact hesapla
   */
  const calculateBudgetImpact = async (categoryId, expenseAmount) => {
    if (!categoryId || !expenseAmount) {
      setBudgetImpact(null);
      return;
    }

    try {
      setLoading(true);

      // Aktif bütçeyi bul
      const activeBudget = await db.getFirstAsync(`
        SELECT b.*, bc.limit_amount
        FROM budgets b
        JOIN budget_categories bc ON b.id = bc.budget_id
        WHERE bc.category_id = ?
          AND b.start_date <= date('now')
          AND b.end_date >= date('now')
        ORDER BY b.created_at DESC
        LIMIT 1
      `, [categoryId]);

      if (!activeBudget) {
        setBudgetImpact({
          status: 'no_budget',
          message: 'Bu kategori için aktif bütçe bulunamadı'
        });
        return;
      }

      // Bu kategorideki mevcut harcamaları getir
      const currentSpending = await db.getFirstAsync(`
        SELECT COALESCE(SUM(amount), 0) as total
        FROM transactions
        WHERE category_id = ?
          AND type = 'expense'
          AND date >= ?
          AND date <= ?
      `, [categoryId, activeBudget.start_date, activeBudget.end_date]);

      const currentTotal = currentSpending?.total || 0;
      const newTotal = currentTotal + parseFloat(expenseAmount);
      const budgetLimit = activeBudget.limit_amount;
      const utilizationRate = (newTotal / budgetLimit) * 100;

      let status = 'safe';
      let message = `Bütçe kullanımı: %${utilizationRate.toFixed(1)}`;

      if (utilizationRate > 100) {
        status = 'over_budget';
        message = `⚠️ Bütçe aşımı! %${(utilizationRate - 100).toFixed(1)} fazla`;
      } else if (utilizationRate > 90) {
        status = 'warning';
        message = `⚠️ Bütçe sınırına yaklaşıyorsunuz (%${utilizationRate.toFixed(1)})`;
      } else if (utilizationRate > 75) {
        status = 'caution';
        message = `⚡ Bütçenizin %${utilizationRate.toFixed(1)}'ini kullanacaksınız`;
      }

      setBudgetImpact({
        status,
        message,
        currentSpending: currentTotal,
        newTotal,
        budgetLimit,
        utilizationRate,
        remaining: budgetLimit - newTotal
      });

    } catch (error) {
      console.error('Budget impact hesaplanırken hata:', error);
      setBudgetImpact({
        status: 'error',
        message: 'Bütçe etkisi hesaplanamadı'
      });
    } finally {
      setLoading(false);
    }
  };

  /**
   * Harcama kaydet
   */
  const saveExpense = async () => {
    if (!amount || !selectedCategory) {
      Alert.alert('Hata', 'Lütfen miktar ve kategori seçin.');
      return;
    }

    try {
      setSaving(true);

      // Transaction kaydet
      await db.runAsync(`
        INSERT INTO transactions (
          amount, 
          description, 
          category_id, 
          type, 
          date,
          created_at
        ) VALUES (?, ?, ?, 'expense', date('now'), datetime('now'))
      `, [
        parseFloat(amount),
        description || 'Hızlı harcama',
        selectedCategory.id
      ]);

      // Başarılı mesaj
      Alert.alert(
        'Başarılı',
        `${amount} TL harcama kaydedildi.`,
        [{ text: 'Tamam', onPress: () => {
          setVisible(false);
          resetForm();
          onExpenseAdded && onExpenseAdded();
        }}]
      );

    } catch (error) {
      console.error('Harcama kaydedilirken hata:', error);
      Alert.alert('Hata', 'Harcama kaydedilemedi.');
    } finally {
      setSaving(false);
    }
  };

  /**
   * Formu sıfırla
   */
  const resetForm = () => {
    setAmount('');
    setDescription('');
    setSelectedCategory(null);
    setBudgetImpact(null);
  };

  /**
   * Modal açıldığında kategorileri yükle
   */
  useEffect(() => {
    if (visible) {
      loadCategories();
    }
  }, [visible]);

  /**
   * Amount veya kategori değiştiğinde budget impact hesapla
   */
  useEffect(() => {
    if (selectedCategory && amount) {
      calculateBudgetImpact(selectedCategory.id, amount);
    }
  }, [selectedCategory, amount]);

  /**
   * Budget impact rengi
   */
  const getBudgetImpactColor = () => {
    if (!budgetImpact) return theme.TEXT_SECONDARY;
    
    switch (budgetImpact.status) {
      case 'safe': return theme.SUCCESS;
      case 'caution': return theme.WARNING;
      case 'warning': return theme.WARNING;
      case 'over_budget': return theme.DANGER;
      default: return theme.TEXT_SECONDARY;
    }
  };

  return (
    <>
      {/* Quick Entry Button */}
      <TouchableOpacity
        style={[
          styles.quickButton,
          { backgroundColor: theme.PRIMARY },
          style
        ]}
        onPress={() => setVisible(true)}
      >
        <MaterialIcons name="add" size={24} color={theme.WHITE} />
        <Text style={[styles.quickButtonText, { color: theme.WHITE }]}>
          Hızlı Harcama
        </Text>
      </TouchableOpacity>

      {/* Quick Entry Modal */}
      <Modal
        visible={visible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: theme.SURFACE }]}>
            {/* Header */}
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: theme.TEXT_PRIMARY }]}>
                Hızlı Harcama Girişi
              </Text>
              <TouchableOpacity
                onPress={() => setVisible(false)}
                style={styles.closeButton}
              >
                <MaterialIcons name="close" size={24} color={theme.TEXT_SECONDARY} />
              </TouchableOpacity>
            </View>

            {/* Amount Input */}
            <View style={styles.inputContainer}>
              <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
                Miktar (TL)
              </Text>
              <TextInput
                style={[
                  styles.amountInput,
                  { 
                    backgroundColor: theme.BACKGROUND,
                    color: theme.TEXT_PRIMARY,
                    borderColor: theme.BORDER
                  }
                ]}
                value={amount}
                onChangeText={setAmount}
                placeholder="0.00"
                placeholderTextColor={theme.TEXT_SECONDARY}
                keyboardType="numeric"
                autoFocus
              />
            </View>

            {/* Category Selection */}
            <View style={styles.inputContainer}>
              <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
                Kategori
              </Text>
              <FlatList
                data={categories}
                horizontal
                showsHorizontalScrollIndicator={false}
                keyExtractor={(item) => item.id.toString()}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={[
                      styles.categoryItem,
                      {
                        backgroundColor: selectedCategory?.id === item.id 
                          ? theme.PRIMARY + '20' 
                          : theme.BACKGROUND,
                        borderColor: selectedCategory?.id === item.id 
                          ? theme.PRIMARY 
                          : theme.BORDER
                      }
                    ]}
                    onPress={() => setSelectedCategory(item)}
                  >
                    <MaterialIcons 
                      name={item.icon || 'category'} 
                      size={20} 
                      color={selectedCategory?.id === item.id ? theme.PRIMARY : theme.TEXT_SECONDARY} 
                    />
                    <Text style={[
                      styles.categoryText,
                      { 
                        color: selectedCategory?.id === item.id 
                          ? theme.PRIMARY 
                          : theme.TEXT_SECONDARY 
                      }
                    ]}>
                      {item.name}
                    </Text>
                  </TouchableOpacity>
                )}
                style={styles.categoryList}
              />
            </View>

            {/* Description Input */}
            <View style={styles.inputContainer}>
              <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
                Açıklama (Opsiyonel)
              </Text>
              <TextInput
                style={[
                  styles.descriptionInput,
                  { 
                    backgroundColor: theme.BACKGROUND,
                    color: theme.TEXT_PRIMARY,
                    borderColor: theme.BORDER
                  }
                ]}
                value={description}
                onChangeText={setDescription}
                placeholder="Harcama açıklaması..."
                placeholderTextColor={theme.TEXT_SECONDARY}
                multiline
              />
            </View>

            {/* Budget Impact */}
            {budgetImpact && (
              <View style={[
                styles.budgetImpact,
                { backgroundColor: getBudgetImpactColor() + '20' }
              ]}>
                {loading ? (
                  <ActivityIndicator size="small" color={theme.PRIMARY} />
                ) : (
                  <Text style={[
                    styles.budgetImpactText,
                    { color: getBudgetImpactColor() }
                  ]}>
                    {budgetImpact.message}
                  </Text>
                )}
              </View>
            )}

            {/* Action Buttons */}
            <View style={styles.actionButtons}>
              <TouchableOpacity
                style={[
                  styles.cancelButton,
                  { backgroundColor: theme.TEXT_SECONDARY + '20' }
                ]}
                onPress={() => setVisible(false)}
              >
                <Text style={[styles.cancelButtonText, { color: theme.TEXT_SECONDARY }]}>
                  İptal
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.saveButton,
                  { 
                    backgroundColor: theme.PRIMARY,
                    opacity: (!amount || !selectedCategory || saving) ? 0.5 : 1
                  }
                ]}
                onPress={saveExpense}
                disabled={!amount || !selectedCategory || saving}
              >
                {saving ? (
                  <ActivityIndicator size="small" color={theme.WHITE} />
                ) : (
                  <Text style={[styles.saveButtonText, { color: theme.WHITE }]}>
                    Kaydet
                  </Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  quickButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  quickButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  closeButton: {
    padding: 4,
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  amountInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  categoryList: {
    maxHeight: 60,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    marginRight: 8,
    gap: 6,
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '500',
  },
  descriptionInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    minHeight: 60,
    textAlignVertical: 'top',
  },
  budgetImpact: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
    alignItems: 'center',
  },
  budgetImpactText: {
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  saveButton: {
    flex: 2,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});
