/**
 * Gelişmiş Bütçe Yönetimi Migration
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md Stage 1 implementasyonu
 */

import { 
  createBudgetTables, 
  insertSystemBudgetTemplates,
  ALL_BUDGET_TABLES,
  BUDGET_INDEXES 
} from '../../database/budgetSchema.js';

/**
 * Gelişmiş bütçe yönetimi tablolarını oluşturur
 * @param {Object} db - SQLite database instance
 */
export const migrateBudgetManagementEnhanced = async (db) => {
  try {
    console.log('🔄 Gelişmiş bütçe yönetimi migration başlatılıyor...');

    // Check if enhanced budget tables already exist
    const hasBudgetsEnhanced = await db.getFirstAsync(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name='budgets_enhanced'
    `);

    if (hasBudgetsEnhanced) {
      console.log('✅ <PERSON>eliş<PERSON>ş bütçe tabloları zaten mevcut');
      return;
    }

    // Create enhanced budget tables
    await createBudgetTables(db);

    // Insert system budget templates
    await insertSystemBudgetTemplates(db);

    // Migrate existing budget data if any
    await migrateExistingBudgetData(db);

    console.log('✅ Gelişmiş bütçe yönetimi migration tamamlandı');

  } catch (error) {
    console.error('❌ Gelişmiş bütçe yönetimi migration hatası:', error);
    throw error;
  }
};

/**
 * Mevcut bütçe verilerini yeni yapıya migrate eder
 * @param {Object} db - SQLite database instance
 */
const migrateExistingBudgetData = async (db) => {
  try {
    console.log('📦 Mevcut bütçe verileri migrate ediliyor...');

    // Check if old budgets table exists
    const hasOldBudgets = await db.getFirstAsync(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='budgets'
    `);

    if (!hasOldBudgets) {
      console.log('ℹ️ Migrate edilecek eski bütçe verisi bulunamadı');
      return;
    }

    // Get existing budgets
    const existingBudgets = await db.getAllAsync(`
      SELECT * FROM budgets WHERE is_active = 1
    `);

    if (existingBudgets.length === 0) {
      console.log('ℹ️ Aktif bütçe verisi bulunamadı');
      return;
    }

    console.log(`📊 ${existingBudgets.length} adet bütçe migrate ediliyor...`);

    // Migrate each budget
    for (const oldBudget of existingBudgets) {
      await migrateSingleBudget(db, oldBudget);
    }

    console.log('✅ Mevcut bütçe verileri başarıyla migrate edildi');

  } catch (error) {
    console.error('❌ Bütçe verisi migration hatası:', error);
    throw error;
  }
};

/**
 * Tek bir bütçeyi yeni yapıya migrate eder
 * @param {Object} db - SQLite database instance
 * @param {Object} oldBudget - Eski bütçe verisi
 */
const migrateSingleBudget = async (db, oldBudget) => {
  try {
    await db.withTransactionAsync(async () => {
      // Create enhanced budget entry
      const result = await db.runAsync(`
        INSERT INTO budgets_enhanced 
        (name, description, type, period_type, start_date, end_date, total_limit, currency, status)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        oldBudget.name || 'Migrate Edilen Bütçe',
        oldBudget.notes || 'Eski sistemden migrate edildi',
        'category_based', // Default type
        oldBudget.period || 'monthly',
        oldBudget.start_date,
        oldBudget.end_date,
        oldBudget.amount || 0,
        oldBudget.currency || 'TRY',
        oldBudget.is_active ? 'active' : 'completed'
      ]);

      const newBudgetId = result.lastInsertRowId;

      // Migrate budget categories if they exist
      const existingBudgetCategories = await db.getAllAsync(`
        SELECT * FROM budget_categories WHERE budget_id = ?
      `, [oldBudget.id]);

      for (const category of existingBudgetCategories) {
        await db.runAsync(`
          INSERT INTO budget_categories_enhanced 
          (budget_id, category_id, limit_amount, spent_amount, currency)
          VALUES (?, ?, ?, ?, ?)
        `, [
          newBudgetId,
          category.category_id,
          category.amount,
          0, // Will be calculated later
          oldBudget.currency || 'TRY'
        ]);
      }

      // Create default alert settings
      await db.runAsync(`
        INSERT INTO budget_alert_settings 
        (budget_id, threshold_75, threshold_90, threshold_100, daily_limit_exceeded, category_limit_exceeded)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [newBudgetId, 1, 1, 1, 1, 1]);

      console.log(`✅ Bütçe migrate edildi: ${oldBudget.name} (ID: ${newBudgetId})`);
    });

  } catch (error) {
    console.error(`❌ Bütçe migration hatası (${oldBudget.name}):`, error);
    throw error;
  }
};

/**
 * Enhanced budget tables'ın varlığını kontrol eder
 * @param {Object} db - SQLite database instance
 * @returns {boolean} Tables exist or not
 */
export const checkEnhancedBudgetTables = async (db) => {
  try {
    const tables = [
      'budgets_enhanced',
      'budget_categories_enhanced', 
      'budget_history',
      'budget_alert_settings',
      'budget_templates'
    ];

    for (const tableName of tables) {
      const tableExists = await db.getFirstAsync(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name=?
      `, [tableName]);

      if (!tableExists) {
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error('Enhanced budget tables kontrol hatası:', error);
    return false;
  }
};

/**
 * Budget management system'in hazır olup olmadığını kontrol eder
 * @param {Object} db - SQLite database instance
 * @returns {boolean} System ready or not
 */
export const isBudgetManagementReady = async (db) => {
  try {
    // Check tables
    const tablesExist = await checkEnhancedBudgetTables(db);
    if (!tablesExist) {
      return false;
    }

    // Check system templates
    const templatesCount = await db.getFirstAsync(`
      SELECT COUNT(*) as count FROM budget_templates WHERE is_system = 1
    `);

    return templatesCount?.count >= 3; // At least 3 system templates
  } catch (error) {
    console.error('Budget management readiness kontrol hatası:', error);
    return false;
  }
};

/**
 * Force recreate all budget tables (for development/testing)
 * @param {Object} db - SQLite database instance
 */
export const recreateBudgetTables = async (db) => {
  try {
    console.log('🔄 Bütçe tabloları yeniden oluşturuluyor...');

    // Drop existing tables
    const tablesToDrop = [
      'budget_alert_settings',
      'budget_history', 
      'budget_categories_enhanced',
      'budgets_enhanced',
      'budget_templates'
    ];

    for (const tableName of tablesToDrop) {
      await db.execAsync(`DROP TABLE IF EXISTS ${tableName}`);
    }

    // Recreate tables
    await createBudgetTables(db);
    await insertSystemBudgetTemplates(db);

    console.log('✅ Bütçe tabloları başarıyla yeniden oluşturuldu');
  } catch (error) {
    console.error('❌ Bütçe tabloları yeniden oluşturma hatası:', error);
    throw error;
  }
};
