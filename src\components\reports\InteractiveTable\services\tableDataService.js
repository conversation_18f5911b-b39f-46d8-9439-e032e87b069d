/**
 * Tablo Veri Servisi
 * Veri kaynaklarından veri çekme, filtreleme, birleştirme
 * MAX 250 satır - Modularization Rule
 */

class TableDataService {
  /**
   * Tablo verisi çekme
   */
  async fetchTableData({ dataSources, filters = [], limit = 1000, offset = 0 }) {
    try {
      const allData = [];
      
      // Her veri kaynağından veri çek
      for (const source of dataSources) {
        const sourceData = await this.fetchFromSource(source, filters);
        allData.push(...sourceData);
      }
      
      // Veriyi birleştir ve filtrele
      const processedData = this.processData(allData, filters);
      
      // Sayfalama uygula
      const paginatedData = this.paginateData(processedData, limit, offset);
      
      return {
        rows: paginatedData,
        total: processedData.length,
        limit,
        offset,
      };
    } catch (error) {
      throw new Error('Veri çekilirken bir hata oluştu');
    }
  }

  /**
   * <PERSON><PERSON>li bir veri kaynağından veri çekme
   */
  async fetchFromSource(source, filters) {
    switch (source) {
      case 'transactions':
        return this.fetchTransactions(filters);
      case 'salary':
        return this.fetchSalaryData(filters);
      case 'overtime':
        return this.fetchOvertimeData(filters);
      case 'budgets':
        return this.fetchBudgetData(filters);
      default:
        return [];
    }
  }

  /**
   * İşlem verilerini çek
   */
  async fetchTransactions(filters) {
    // Mock veri - gerçek implementasyonda SQLite'dan çekilecek
    return [
      {
        id: 1,
        date: '2024-01-15',
        category: 'Gıda',
        description: 'Market alışverişi',
        amount: -250.50,
        type: 'expense',
        source: 'transactions',
      },
      {
        id: 2,
        date: '2024-01-20',
        category: 'Maaş',
        description: 'Aylık maaş',
        amount: 15000,
        type: 'income',
        source: 'transactions',
      },
      {
        id: 3,
        date: '2024-01-25',
        category: 'Ulaşım',
        description: 'Yakıt',
        amount: -300,
        type: 'expense',
        source: 'transactions',
      },
    ];
  }

  /**
   * Maaş verilerini çek
   */
  async fetchSalaryData(filters) {
    // Mock veri
    return [
      {
        id: 101,
        date: '2024-01-01',
        category: 'Maaş',
        description: 'Ocak maaşı',
        amount: 15000,
        type: 'salary',
        source: 'salary',
      },
    ];
  }

  /**
   * Mesai verilerini çek
   */
  async fetchOvertimeData(filters) {
    // Mock veri
    return [
      {
        id: 201,
        date: '2024-01-10',
        category: 'Mesai',
        description: 'Hafta sonu mesaisi',
        amount: 500,
        type: 'overtime',
        source: 'overtime',
      },
    ];
  }

  /**
   * Bütçe verilerini çek
   */
  async fetchBudgetData(filters) {
    // Mock veri
    return [
      {
        id: 301,
        date: '2024-01-01',
        category: 'Bütçe',
        description: 'Aylık bütçe planı',
        amount: 12000,
        type: 'budget',
        source: 'budgets',
      },
    ];
  }

  /**
   * Veriyi işle ve filtrele
   */
  processData(data, filters) {
    let processedData = [...data];

    // Tarih filtreleri uygula
    const dateFilter = filters.find(f => f.type === 'date');
    if (dateFilter) {
      processedData = processedData.filter(item => {
        const itemDate = new Date(item.date);
        const startDate = new Date(dateFilter.startDate);
        const endDate = new Date(dateFilter.endDate);
        return itemDate >= startDate && itemDate <= endDate;
      });
    }

    // Kategori filtreleri uygula
    const categoryFilter = filters.find(f => f.type === 'category');
    if (categoryFilter && categoryFilter.values.length > 0) {
      processedData = processedData.filter(item => 
        categoryFilter.values.includes(item.category)
      );
    }

    // Tutar filtreleri uygula
    const amountFilter = filters.find(f => f.type === 'amount');
    if (amountFilter) {
      processedData = processedData.filter(item => {
        const amount = Math.abs(item.amount);
        return amount >= amountFilter.min && amount <= amountFilter.max;
      });
    }

    // Tarihe göre sırala (en yeni önce)
    processedData.sort((a, b) => new Date(b.date) - new Date(a.date));

    return processedData;
  }

  /**
   * Sayfalama uygula
   */
  paginateData(data, limit, offset) {
    return data.slice(offset, offset + limit);
  }

  /**
   * Tablo yapılandırmasını kaydet
   */
  async saveTableConfig(config) {
    try {
      // TODO: SQLite'a kaydet
      const savedConfig = {
        ...config,
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
      };
      return savedConfig;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Kaydedilen tablo yapılandırmalarını getir
   */
  async getSavedTableConfigs() {
    try {
      // TODO: SQLite'dan çek
      return [];
    } catch (error) {
      return [];
    }
  }
}

// Singleton instance
export const tableDataService = new TableDataService();
