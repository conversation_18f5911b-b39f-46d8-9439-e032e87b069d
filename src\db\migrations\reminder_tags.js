/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> etiketleri tablosu için migrasyon
 *
 * @param {object} db - SQLite veritabanı nesnesi
 * @returns {Promise<void>}
 */
export const migrateReminderTags = async (db) => {
  try {
    console.log('Hatırlatıcı etiketleri tablosu migrasyonu başlatılıyor...');

    // Etiketler tablosunu kontrol et
    const hasTagsTable = await db.getFirstAsync(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='reminder_tags'
    `);

    if (!hasTagsTable) {
      console.log('reminder_tags tablosu oluşturuluyor...');

      // Etiketler tablosunu oluştur
      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS reminder_tags (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          color TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Etiket-hatırlatıcı ilişki tablosunu oluştur
      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS reminder_tag_relations (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          reminder_id INTEGER NOT NULL,
          tag_id INTEGER NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (reminder_id) REFERENCES notifications(id) ON DELETE CASCADE,
          FOREIGN KEY (tag_id) REFERENCES reminder_tags(id) ON DELETE CASCADE,
          UNIQUE(reminder_id, tag_id)
        )
      `);

      // Varsayılan etiketleri ekle
      await db.runAsync(`
        INSERT INTO reminder_tags (name, color)
        VALUES ('Önemli', '#e74c3c')
      `);

      await db.runAsync(`
        INSERT INTO reminder_tags (name, color)
        VALUES ('Acil', '#e67e22')
      `);

      await db.runAsync(`
        INSERT INTO reminder_tags (name, color)
        VALUES ('Takip', '#3498db')
      `);

      await db.runAsync(`
        INSERT INTO reminder_tags (name, color)
        VALUES ('Kişisel', '#2ecc71')
      `);

      await db.runAsync(`
        INSERT INTO reminder_tags (name, color)
        VALUES ('İş', '#9b59b6')
      `);

      console.log('reminder_tags tablosu başarıyla oluşturuldu.');
    } else {
      console.log('reminder_tags tablosu zaten mevcut.');
    }

    console.log('Hatırlatıcı etiketleri migrasyonu tamamlandı.');
  } catch (error) {
    console.error('Hatırlatıcı etiketleri migrasyon hatası:', error);
    throw error;
  }
};
