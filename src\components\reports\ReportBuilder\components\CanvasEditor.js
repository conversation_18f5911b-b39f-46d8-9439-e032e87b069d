import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Alert,
} from 'react-native';
import { useTheme } from '../../../../context/ThemeContext';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

/**
 * Canvas Editörü - Sürükle-bırak ile rapor tasarlama
 * Widget yerleştirme, boyutlandırma ve düzenleme
 */
const CanvasEditor = ({
  elements = [],
  selectedElement = null,
  onAddElement,
  onRemoveElement,
  onUpdateElement,
  onMoveElement,
  onResizeElement,
  onUndo,
  onRedo,
  onClear,
  canUndo = false,
  canRedo = false,
}) => {
  const { theme } = useTheme();
  const [draggedElement, setDraggedElement] = useState(null);
  const [showWidgetPalette, setShowWidgetPalette] = useState(false);
  const panRef = useRef(null);

  // Tema güvenlik kontrolü
  if (!theme) {
    return null;
  }

  /**
   * Güvenli tema değeri alma fonksiyonu
   * @param {string} property - Tema özelliği
   * @param {string} fallback - Varsayılan değer
   * @returns {string} Güvenli tema değeri
   */
  const getSafeThemeValue = (property, fallback) => {
    return theme[property] || theme.colors?.[property.toLowerCase()] || fallback;
  };

  /**
   * Widget türleri
   */
  const getWidgetTypes = () => [
    { id: 'chart', name: 'Grafik', icon: '📊', color: getSafeThemeValue('PRIMARY', '#007AFF') },
    { id: 'table', name: 'Tablo', icon: '📋', color: getSafeThemeValue('SUCCESS', '#28a745') },
    { id: 'text', name: 'Metin', icon: '📝', color: getSafeThemeValue('WARNING', '#ffc107') },
    { id: 'kpi', name: 'KPI', icon: '🎯', color: getSafeThemeValue('INFO', '#17a2b8') },
    { id: 'image', name: 'Resim', icon: '🖼️', color: getSafeThemeValue('SECONDARY', '#6c757d') },
    { id: 'filter', name: 'Filtre', icon: '🔍', color: getSafeThemeValue('ACCENT', '#6f42c1') },
  ];

  /**
   * Yeni widget ekleme
   */
  const handleAddWidget = (widgetType) => {
    const newElement = {
      id: Date.now().toString(),
      type: widgetType.id,
      name: widgetType.name,
      x: 50,
      y: 50,
      width: 200,
      height: 150,
      data: {},
      style: {
        backgroundColor: getSafeThemeValue('SURFACE', '#f9f9f9'),
        borderColor: widgetType.color,
        borderWidth: 2,
        borderRadius: 8,
      },
    };
    
    onAddElement(newElement);
    setShowWidgetPalette(false);
  };

  /**
   * Element silme
   */
  const handleDeleteElement = (elementId) => {
    Alert.alert(
      'Widget Sil',
      'Bu widget\'ı silmek istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        { text: 'Sil', onPress: () => onRemoveElement(elementId), style: 'destructive' },
      ]
    );
  };

  /**
   * Element seçme
   */
  const handleSelectElement = (element) => {
    onUpdateElement(element.id, { ...element, selected: true });
    
    // Diğer elementlerin seçimini kaldır
    elements.forEach(el => {
      if (el.id !== element.id && el.selected) {
        onUpdateElement(el.id, { ...el, selected: false });
      }
    });
  };

  /**
   * Sürükleme başlangıcı
   */
  const handleGestureBegin = (element) => {
    setDraggedElement(element);
    handleSelectElement(element);
  };

  /**
   * Sürükleme işlemi
   */
  const handleGestureMove = (event, element) => {
    if (!draggedElement) return;
    
    const { translationX, translationY } = event.nativeEvent;
    const newX = Math.max(0, Math.min(screenWidth - element.width, element.x + translationX));
    const newY = Math.max(0, Math.min(screenHeight - element.height, element.y + translationY));
    
    onMoveElement(element.id, newX, newY);
  };

  /**
   * Sürükleme bitişi
   */
  const handleGestureEnd = () => {
    setDraggedElement(null);
  };

  /**
   * Widget render
   */
  const renderWidget = (element) => {
    const widgetType = getWidgetTypes().find(w => w.id === element.type);
    if (!widgetType) return null;

    return (
      <TouchableOpacity
        key={element.id}
        style={[
          styles.widget,
          {
            left: element.x,
            top: element.y,
            width: element.width,
            height: element.height,
            backgroundColor: element.style?.backgroundColor || getSafeThemeValue('SURFACE', '#f9f9f9'),
            borderColor: element.selected ? getSafeThemeValue('PRIMARY', '#007AFF') : (element.style?.borderColor || getSafeThemeValue('BORDER', '#e0e0e0')),
            borderWidth: element.selected ? 3 : (element.style?.borderWidth || 1),
            borderRadius: element.style?.borderRadius || 8,
          },
        ]}
        onPress={() => handleSelectElement(element)}
        activeOpacity={0.8}
      >
        {/* Widget Header */}
        <View style={[styles.widgetHeader, { backgroundColor: widgetType.color }]}>
          <Text style={[styles.widgetIcon, { color: getSafeThemeValue('SURFACE', '#f9f9f9') }]}>
            {widgetType.icon}
          </Text>
          <Text style={[styles.widgetTitle, { color: getSafeThemeValue('SURFACE', '#f9f9f9') }]}>
            {element.name}
          </Text>
          <TouchableOpacity
            style={styles.widgetDeleteButton}
            onPress={() => handleDeleteElement(element.id)}
          >
            <Text style={[styles.widgetDeleteText, { color: getSafeThemeValue('SURFACE', '#f9f9f9') }]}>
              ×
            </Text>
          </TouchableOpacity>
        </View>
        
        {/* Widget Content */}
        <View style={styles.widgetContent}>
          <Text style={[styles.widgetPlaceholder, { color: getSafeThemeValue('TEXT_SECONDARY', '#666') }]}>
            {element.type === 'chart' && '📊 Grafik Alanı'}
            {element.type === 'table' && '📋 Tablo Alanı'}
            {element.type === 'text' && '📝 Metin Alanı'}
            {element.type === 'kpi' && '🎯 KPI Alanı'}
            {element.type === 'image' && '🖼️ Resim Alanı'}
            {element.type === 'filter' && '🔍 Filtre Alanı'}
          </Text>
        </View>
        
        {/* Resize Handle */}
        {element.selected && (
          <View style={[styles.resizeHandle, { backgroundColor: getSafeThemeValue('PRIMARY', '#007AFF') }]} />
        )}
      </TouchableOpacity>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: getSafeThemeValue('BACKGROUND', '#fff') }]}>
      {/* Toolbar */}
      <View style={[styles.toolbar, { backgroundColor: getSafeThemeValue('SURFACE', '#f9f9f9') }]}>
        <TouchableOpacity
          style={[styles.toolbarButton, { backgroundColor: getSafeThemeValue('PRIMARY', '#007AFF') }]}
          onPress={() => setShowWidgetPalette(!showWidgetPalette)}
        >
          <Text style={[styles.toolbarButtonText, { color: getSafeThemeValue('SURFACE', '#f9f9f9') }]}>
            ➕ Widget Ekle
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.toolbarButton, { backgroundColor: canUndo ? getSafeThemeValue('SUCCESS', '#28a745') : getSafeThemeValue('DISABLED', '#ccc') }]}
          onPress={onUndo}
          disabled={!canUndo}
        >
          <Text style={[styles.toolbarButtonText, { color: getSafeThemeValue('SURFACE', '#f9f9f9') }]}>
            ↶ Geri Al
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.toolbarButton, { backgroundColor: canRedo ? getSafeThemeValue('SUCCESS', '#28a745') : getSafeThemeValue('DISABLED', '#ccc') }]}
          onPress={onRedo}
          disabled={!canRedo}
        >
          <Text style={[styles.toolbarButtonText, { color: getSafeThemeValue('SURFACE', '#f9f9f9') }]}>
            ↷ Yinele
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.toolbarButton, { backgroundColor: getSafeThemeValue('ERROR', '#dc3545') }]}
          onPress={onClear}
        >
          <Text style={[styles.toolbarButtonText, { color: getSafeThemeValue('SURFACE', '#f9f9f9') }]}>
            🗑️ Temizle
          </Text>
        </TouchableOpacity>
      </View>

      {/* Widget Palette */}
      {showWidgetPalette && (
        <View style={[styles.widgetPalette, { backgroundColor: getSafeThemeValue('SURFACE', '#f9f9f9') }]}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {getWidgetTypes().map((widgetType) => (
              <TouchableOpacity
                key={widgetType.id}
                style={[styles.paletteItem, { backgroundColor: widgetType.color }]}
                onPress={() => handleAddWidget(widgetType)}
              >
                <Text style={[styles.paletteIcon, { color: getSafeThemeValue('SURFACE', '#f9f9f9') }]}>
                  {widgetType.icon}
                </Text>
                <Text style={[styles.paletteName, { color: getSafeThemeValue('SURFACE', '#f9f9f9') }]}>
                  {widgetType.name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}

      {/* Canvas */}
      <View style={[styles.canvas, { backgroundColor: getSafeThemeValue('BACKGROUND', '#fff') }]}>
        <ScrollView
          style={styles.canvasScroll}
          contentContainerStyle={styles.canvasContent}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
        >
          {/* Grid Background */}
          <View style={[styles.gridBackground, { backgroundColor: getSafeThemeValue('BACKGROUND', '#fff') }]}>
            {/* Render all widgets */}
            {elements.map(renderWidget)}
          </View>
        </ScrollView>
      </View>

      {/* Status Bar */}
      <View style={[styles.statusBar, { backgroundColor: getSafeThemeValue('SURFACE', '#f9f9f9') }]}>
        <Text style={[styles.statusText, { color: getSafeThemeValue('TEXT_SECONDARY', '#666') }]}>
          {elements.length} widget • {selectedElement ? `Seçili: ${selectedElement.name}` : 'Hiçbiri seçili değil'}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  toolbar: {
    flexDirection: 'row',
    padding: 8,
    gap: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  toolbarButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  toolbarButtonText: {
    fontSize: 12,
    fontWeight: '600',
  },
  widgetPalette: {
    padding: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  paletteItem: {
    alignItems: 'center',
    padding: 8,
    marginRight: 8,
    borderRadius: 8,
    minWidth: 60,
  },
  paletteIcon: {
    fontSize: 20,
    marginBottom: 4,
  },
  paletteName: {
    fontSize: 10,
    fontWeight: '600',
  },
  canvas: {
    flex: 1,
  },
  canvasScroll: {
    flex: 1,
  },
  canvasContent: {
    minHeight: screenHeight,
    minWidth: screenWidth,
  },
  gridBackground: {
    flex: 1,
    position: 'relative',
  },
  widget: {
    position: 'absolute',
    minWidth: 100,
    minHeight: 80,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  widgetHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },
  widgetIcon: {
    fontSize: 14,
    marginRight: 4,
  },
  widgetTitle: {
    fontSize: 12,
    fontWeight: '600',
    flex: 1,
  },
  widgetDeleteButton: {
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  widgetDeleteText: {
    fontSize: 16,
    fontWeight: '700',
  },
  widgetContent: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 8,
  },
  widgetPlaceholder: {
    fontSize: 12,
    textAlign: 'center',
  },
  resizeHandle: {
    position: 'absolute',
    right: -3,
    bottom: -3,
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  statusBar: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  statusText: {
    fontSize: 11,
  },
});

export default CanvasEditor;
