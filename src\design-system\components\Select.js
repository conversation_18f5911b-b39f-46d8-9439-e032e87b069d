import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  FlatList,
  StyleSheet,
  Dimensions,
  TextInput
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import tokens from '../tokens';

const { height: screenHeight } = Dimensions.get('window');

/**
 * Modern Select Component
 * Design system'e uygun, tutarlı select bileşeni
 */
const Select = ({
  label,
  placeholder = 'Seçiniz...',
  value,
  onValueChange,
  options = [],
  error,
  helperText,
  disabled = false,
  required = false,
  variant = 'outlined',
  size = 'md',
  leftIcon,
  searchable = false,
  multiple = false,
  style,
  ...props
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchText, setSearchText] = useState('');

  // Get selected option(s)
  const getSelectedOption = useCallback(() => {
    if (multiple) {
      return options.filter(option => value?.includes(option.value));
    }
    return options.find(option => option.value === value);
  }, [options, value, multiple]);

  // Get display text
  const getDisplayText = useCallback(() => {
    const selected = getSelectedOption();
    if (multiple) {
      if (!selected || selected.length === 0) return placeholder;
      if (selected.length === 1) return selected[0].label;
      return `${selected.length} öğe seçildi`;
    }
    return selected ? selected.label : placeholder;
  }, [getSelectedOption, multiple, placeholder]);

  // Filter options based on search
  const filteredOptions = searchable && searchText
    ? options.filter(option => 
        option.label.toLowerCase().includes(searchText.toLowerCase())
      )
    : options;

  // Handle option select
  const handleOptionSelect = useCallback((option) => {
    if (multiple) {
      const currentValues = value || [];
      const newValues = currentValues.includes(option.value)
        ? currentValues.filter(v => v !== option.value)
        : [...currentValues, option.value];
      onValueChange(newValues);
    } else {
      onValueChange(option.value);
      setIsOpen(false);
    }
  }, [multiple, value, onValueChange]);

  // Get variant styles
  const getVariantStyles = () => {
    const baseStyle = {
      borderRadius: tokens.borderRadius.lg,
      paddingHorizontal: tokens.spacing[4],
    };

    switch (variant) {
      case 'outlined':
        return {
          ...baseStyle,
          borderWidth: 1,
          borderColor: error 
            ? tokens.colors.danger[500] 
            : isOpen 
            ? tokens.colors.primary[500] 
            : tokens.colors.gray[300],
          backgroundColor: disabled ? tokens.colors.gray[50] : '#ffffff',
        };
      case 'filled':
        return {
          ...baseStyle,
          borderWidth: 0,
          backgroundColor: disabled 
            ? tokens.colors.gray[100] 
            : tokens.colors.gray[50],
          borderBottomWidth: 2,
          borderBottomColor: error 
            ? tokens.colors.danger[500] 
            : isOpen 
            ? tokens.colors.primary[500] 
            : tokens.colors.gray[300],
        };
      default:
        return baseStyle;
    }
  };

  // Get size styles
  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return {
          paddingVertical: tokens.spacing[2],
          minHeight: 40,
        };
      case 'md':
        return {
          paddingVertical: tokens.spacing[3],
          minHeight: 48,
        };
      case 'lg':
        return {
          paddingVertical: tokens.spacing[4],
          minHeight: 56,
        };
      default:
        return {
          paddingVertical: tokens.spacing[3],
          minHeight: 48,
        };
    }
  };

  const variantStyles = getVariantStyles();
  const sizeStyles = getSizeStyles();

  // Render option item
  const renderOption = ({ item }) => {
    const isSelected = multiple 
      ? value?.includes(item.value)
      : value === item.value;

    return (
      <TouchableOpacity
        style={[
          styles.option,
          isSelected && styles.selectedOption
        ]}
        onPress={() => handleOptionSelect(item)}
      >
        <Text style={[
          styles.optionText,
          isSelected && styles.selectedOptionText
        ]}>
          {item.label}
        </Text>
        {isSelected && (
          <MaterialIcons
            name="check"
            size={20}
            color={tokens.colors.primary[500]}
          />
        )}
      </TouchableOpacity>
    );
  };

  return (
    <View style={[styles.container, style]}>
      {/* Label */}
      {label && (
        <Text style={[
          styles.label,
          error && styles.errorLabel
        ]}>
          {label}{required && ' *'}
        </Text>
      )}

      {/* Select Button */}
      <TouchableOpacity
        style={[styles.selectButton, variantStyles, sizeStyles]}
        onPress={() => !disabled && setIsOpen(true)}
        disabled={disabled}
        {...props}
      >
        {/* Left Icon */}
        {leftIcon && (
          <MaterialIcons
            name={leftIcon}
            size={20}
            color={tokens.colors.gray[500]}
            style={styles.leftIcon}
          />
        )}

        {/* Display Text */}
        <Text style={[
          styles.displayText,
          !getSelectedOption() && styles.placeholderText,
          disabled && styles.disabledText,
          leftIcon && styles.textWithLeftIcon,
        ]}>
          {getDisplayText()}
        </Text>

        {/* Dropdown Icon */}
        <MaterialIcons
          name={isOpen ? 'keyboard-arrow-up' : 'keyboard-arrow-down'}
          size={24}
          color={tokens.colors.gray[500]}
          style={styles.dropdownIcon}
        />
      </TouchableOpacity>

      {/* Helper Text / Error */}
      {(helperText || error) && (
        <Text style={[
          styles.helperText,
          error && styles.errorText
        ]}>
          {error || helperText}
        </Text>
      )}

      {/* Options Modal */}
      <Modal
        visible={isOpen}
        transparent
        animationType="fade"
        onRequestClose={() => setIsOpen(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setIsOpen(false)}
        >
          <View style={styles.modalContent}>
            {/* Search Input */}
            {searchable && (
              <View style={styles.searchContainer}>
                <MaterialIcons
                  name="search"
                  size={20}
                  color={tokens.colors.gray[500]}
                  style={styles.searchIcon}
                />
                <TextInput
                  style={styles.searchInput}
                  placeholder="Ara..."
                  value={searchText}
                  onChangeText={setSearchText}
                  placeholderTextColor={tokens.colors.gray[400]}
                />
              </View>
            )}

            {/* Options List */}
            <FlatList
              data={filteredOptions}
              renderItem={renderOption}
              keyExtractor={item => item.value.toString()}
              style={styles.optionsList}
              showsVerticalScrollIndicator={false}
              maxHeight={screenHeight * 0.4}
            />

            {/* Close Button */}
            {multiple && (
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setIsOpen(false)}
              >
                <Text style={styles.closeButtonText}>Tamam</Text>
              </TouchableOpacity>
            )}
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: tokens.spacing[4],
  },
  label: {
    fontSize: tokens.typography.fontSize.sm,
    fontWeight: tokens.typography.fontWeight.medium,
    color: tokens.colors.gray[700],
    marginBottom: tokens.spacing[1],
    fontFamily: tokens.typography.fontFamily.medium,
  },
  errorLabel: {
    color: tokens.colors.danger[500],
  },
  selectButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  leftIcon: {
    marginRight: tokens.spacing[2],
  },
  displayText: {
    flex: 1,
    fontSize: tokens.typography.fontSize.base,
    color: tokens.colors.gray[900],
    fontFamily: tokens.typography.fontFamily.regular,
  },
  placeholderText: {
    color: tokens.colors.gray[400],
  },
  disabledText: {
    color: tokens.colors.gray[500],
  },
  textWithLeftIcon: {
    marginLeft: 0,
  },
  dropdownIcon: {
    marginLeft: tokens.spacing[2],
  },
  helperText: {
    marginTop: tokens.spacing[1],
    fontSize: tokens.typography.fontSize.xs,
    color: tokens.colors.gray[500],
    fontFamily: tokens.typography.fontFamily.regular,
  },
  errorText: {
    color: tokens.colors.danger[500],
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: tokens.spacing[4],
  },
  modalContent: {
    backgroundColor: '#ffffff',
    borderRadius: tokens.borderRadius.xl,
    width: '100%',
    maxHeight: screenHeight * 0.6,
    ...tokens.shadows.lg,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: tokens.spacing[4],
    borderBottomWidth: 1,
    borderBottomColor: tokens.colors.gray[200],
  },
  searchIcon: {
    marginRight: tokens.spacing[2],
  },
  searchInput: {
    flex: 1,
    fontSize: tokens.typography.fontSize.base,
    color: tokens.colors.gray[900],
    fontFamily: tokens.typography.fontFamily.regular,
  },
  optionsList: {
    maxHeight: screenHeight * 0.4,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: tokens.spacing[4],
    borderBottomWidth: 1,
    borderBottomColor: tokens.colors.gray[100],
  },
  selectedOption: {
    backgroundColor: tokens.colors.primary[50],
  },
  optionText: {
    flex: 1,
    fontSize: tokens.typography.fontSize.base,
    color: tokens.colors.gray[900],
    fontFamily: tokens.typography.fontFamily.regular,
  },
  selectedOptionText: {
    color: tokens.colors.primary[500],
    fontWeight: tokens.typography.fontWeight.medium,
  },
  closeButton: {
    padding: tokens.spacing[4],
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: tokens.colors.gray[200],
  },
  closeButtonText: {
    fontSize: tokens.typography.fontSize.base,
    color: tokens.colors.primary[500],
    fontWeight: tokens.typography.fontWeight.medium,
    fontFamily: tokens.typography.fontFamily.medium,
  },
});

export default Select;
