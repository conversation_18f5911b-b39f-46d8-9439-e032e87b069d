import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../../constants/colors';

/**
 * Düzenli gelir form hataları bileşeni
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {string} props.error - Hata mesajı
 * @param {Function} props.setError - Hata mesajı değiştirme fonksiyonu
 * @returns {JSX.Element} Hata gösterimi bileşeni
 */
const RegularIncomeErrorDisplay = React.memo(({ error, setError }) => {
  if (!error) return null;
  
  return (
    <View style={styles.errorContainer}>
      <View style={styles.errorIconContainer}>
        <MaterialIcons name="error" size={24} color={Colors.DANGER} />
        <Text style={styles.errorTitle}>Lütfen Hataları Düzeltin</Text>
      </View>
      <View style={styles.errorTextContainer}>
        <Text style={styles.errorText}>{error}</Text>
      </View>
      <TouchableOpacity 
        style={styles.errorCloseButton}
        onPress={() => setError('')}
        accessibilityLabel="Hata mesajını kapat"
        accessibilityHint="Hata mesajını kapatmak için dokunun"
      >
        <MaterialIcons name="close" size={20} color={Colors.GRAY_600} />
      </TouchableOpacity>
    </View>
  );
});

/**
 * Enhanced modern styles for RegularIncomeErrorDisplay component
 * Following Material Design 3.0 principles with improved error presentation
 */
const styles = StyleSheet.create({
  errorContainer: {
    backgroundColor: Colors.DANGER + '12',
    borderWidth: 2,
    borderColor: Colors.DANGER + '35',
    borderRadius: 16,
    padding: 20,
    marginBottom: 24,
    position: 'relative',
    shadowColor: Colors.DANGER,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
    borderLeftWidth: 6,
    borderLeftColor: Colors.DANGER,
  },
  errorIconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  errorTitle: {
    fontSize: 17,
    fontWeight: '800',
    color: Colors.DANGER,
    marginLeft: 12,
    letterSpacing: 0.3,
  },
  errorTextContainer: {
    backgroundColor: Colors.WHITE + 'AA',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  errorText: {
    fontSize: 15,
    color: Colors.GRAY_800,
    lineHeight: 24,
    fontWeight: '500',
  },
  errorCloseButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.WHITE,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
});

export default RegularIncomeErrorDisplay;
