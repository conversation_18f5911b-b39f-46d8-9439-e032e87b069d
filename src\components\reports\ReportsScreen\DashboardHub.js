import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Dimensions,
} from 'react-native';

const { width: screenWidth } = Dimensions.get('window');

/**
 * Dashboard Hub Component
 * Central hub showing statistics, quick access cards, and live previews
 * Follows REPORTS_SCREEN_REDESIGN_PLAN.md specifications
 */
const DashboardHub = ({ 
  reportStats,
  recentReports,
  favoriteReports,
  onReportPress,
  onStatsPress,
  theme,
  isLoading = false
}) => {
  if (isLoading) {
    return (
      <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: theme.TEXT_SECONDARY }]}>
            Dashboard yükleniyor...
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      {/* Statistics Cards */}
      <View style={styles.statsSection}>
        <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
          📊 <PERSON><PERSON>tistikler
        </Text>
        
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          style={styles.statsScrollView}
        >
          <TouchableOpacity 
            style={[styles.statCard, { backgroundColor: theme.PRIMARY }]}
            onPress={() => onStatsPress?.('total')}
          >
            <Text style={[styles.statNumber, { color: theme.SURFACE }]}>
              {reportStats?.totalReports || 0}
            </Text>
            <Text style={[styles.statLabel, { color: theme.SURFACE }]}>
              Toplam Rapor
            </Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={[styles.statCard, { backgroundColor: theme.SUCCESS }]}
            onPress={() => onStatsPress?.('exports')}
          >
            <Text style={[styles.statNumber, { color: theme.SURFACE }]}>
              {reportStats?.totalExports || 0}
            </Text>
            <Text style={[styles.statLabel, { color: theme.SURFACE }]}>
              Dışa Aktarma
            </Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={[styles.statCard, { backgroundColor: theme.WARNING }]}
            onPress={() => onStatsPress?.('favorites')}
          >
            <Text style={[styles.statNumber, { color: theme.SURFACE }]}>
              {reportStats?.favoritesCount || 0}
            </Text>
            <Text style={[styles.statLabel, { color: theme.SURFACE }]}>
              Favoriler
            </Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={[styles.statCard, { backgroundColor: theme.INFO }]}
            onPress={() => onStatsPress?.('scheduled')}
          >
            <Text style={[styles.statNumber, { color: theme.SURFACE }]}>
              {reportStats?.scheduledCount || 0}
            </Text>
            <Text style={[styles.statLabel, { color: theme.SURFACE }]}>
              Zamanlanmış
            </Text>
          </TouchableOpacity>
        </ScrollView>
      </View>

      {/* Recent Reports */}
      {recentReports && recentReports.length > 0 && (
        <View style={styles.recentSection}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            🕒 Son Kullanılan Raporlar
          </Text>
          
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            style={styles.reportsScrollView}
          >
            {recentReports.map((report, index) => (
              <TouchableOpacity
                key={index}
                style={[styles.reportCard, { backgroundColor: theme.SURFACE }]}
                onPress={() => onReportPress?.(report)}
              >
                <Text style={[styles.reportIcon, { color: theme.PRIMARY }]}>
                  {report.icon || '📊'}
                </Text>
                <Text style={[styles.reportTitle, { color: theme.TEXT_PRIMARY }]}>
                  {report.name || 'Rapor'}
                </Text>
                <Text style={[styles.reportDate, { color: theme.TEXT_SECONDARY }]}>
                  {report.lastViewed || 'Bilinmiyor'}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}

      {/* Favorite Reports */}
      {favoriteReports && favoriteReports.length > 0 && (
        <View style={styles.favoritesSection}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            ⭐ Favori Raporlar
          </Text>
          
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            style={styles.reportsScrollView}
          >
            {favoriteReports.map((report, index) => (
              <TouchableOpacity
                key={index}
                style={[styles.reportCard, { backgroundColor: theme.SURFACE }]}
                onPress={() => onReportPress?.(report)}
              >
                <Text style={[styles.reportIcon, { color: theme.WARNING }]}>
                  ⭐
                </Text>
                <Text style={[styles.reportTitle, { color: theme.TEXT_PRIMARY }]}>
                  {report.name || 'Favori Rapor'}
                </Text>
                <Text style={[styles.reportDate, { color: theme.TEXT_SECONDARY }]}>
                  {report.category || 'Kategori'}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  loadingContainer: {
    padding: 40,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  statsSection: {
    marginBottom: 24,
  },
  statsScrollView: {
    marginHorizontal: -4,
  },
  statCard: {
    width: 120,
    height: 80,
    borderRadius: 12,
    padding: 12,
    marginHorizontal: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  recentSection: {
    marginBottom: 24,
  },
  favoritesSection: {
    marginBottom: 24,
  },
  reportsScrollView: {
    marginHorizontal: -4,
  },
  reportCard: {
    width: 140,
    height: 100,
    borderRadius: 12,
    padding: 12,
    marginHorizontal: 4,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  reportIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  reportTitle: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 4,
  },
  reportDate: {
    fontSize: 12,
    textAlign: 'center',
  },
});

export default DashboardHub;
