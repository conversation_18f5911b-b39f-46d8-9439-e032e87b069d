/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> grupları tablosu için migrasyon
 *
 * @param {object} db - SQLite veritabanı nesnesi
 * @returns {Promise<void>}
 */
export const migrateReminderGroups = async (db) => {
  try {
    console.log('Hat<PERSON>rlatıcı grupları tablosu migrasyonu başlatılıyor...');

    // Hatırlatıcı grupları tablosunu kontrol et
    const hasReminderGroupsTable = await db.getFirstAsync(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='reminder_groups'
    `);

    if (!hasReminderGroupsTable) {
      console.log('reminder_groups tablosu oluşturuluyor...');

      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS reminder_groups (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          description TEXT,
          color TEXT,
          icon TEXT,
          is_default INTEGER DEFAULT 0,
          is_active INTEGER DEFAULT 1,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Varsayılan grupları ekle
      await db.runAsync(`
        INSERT INTO reminder_groups (name, description, color, icon, is_default)
        VALUES ('Genel', 'Genel hatırlatıcılar', '#3498db', 'notifications', 1)
      `);

      await db.runAsync(`
        INSERT INTO reminder_groups (name, description, color, icon, is_default)
        VALUES ('Kişisel', 'Kişisel hatırlatıcılar', '#2ecc71', 'person', 0)
      `);

      await db.runAsync(`
        INSERT INTO reminder_groups (name, description, color, icon, is_default)
        VALUES ('İş', 'İş ile ilgili hatırlatıcılar', '#e74c3c', 'work', 0)
      `);

      await db.runAsync(`
        INSERT INTO reminder_groups (name, description, color, icon, is_default)
        VALUES ('Finans', 'Finansal hatırlatıcılar', '#f39c12', 'account-balance-wallet', 0)
      `);

      console.log('reminder_groups tablosu başarıyla oluşturuldu.');
    } else {
      console.log('reminder_groups tablosu zaten mevcut.');
    }

    // notifications tablosuna group_id sütununu ekle
    const notificationsColumns = await db.getAllAsync(`PRAGMA table_info(notifications)`);
    const notificationsColumnNames = notificationsColumns.map(col => col.name);

    if (!notificationsColumnNames.includes('group_id')) {
      console.log('notifications tablosuna group_id sütunu ekleniyor...');
      await db.execAsync(`ALTER TABLE notifications ADD COLUMN group_id INTEGER REFERENCES reminder_groups(id)`);
    }

    console.log('Hatırlatıcı grupları migrasyonu tamamlandı.');
  } catch (error) {
    console.error('Hatırlatıcı grupları migrasyon hatası:', error);
    throw error;
  }
};
