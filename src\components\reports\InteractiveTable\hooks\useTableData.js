import { useState, useEffect, useCallback } from 'react';
import { tableDataService } from '../services/tableDataService';

/**
 * Tablo verisi yönetimi hook'u
 * Veri yükleme, filtreleme, cache yönetimi
 * MAX 100 satır - Modularization Rule
 */
export const useTableData = (dataSources = [], filters = []) => {
  const [tableData, setTableData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [totalRows, setTotalRows] = useState(0);

  /**
   * Veri yükleme fonksiyonu
   */
  const loadData = useCallback(async () => {
    if (dataSources.length === 0) {
      setTableData([]);
      setTotalRows(0);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const data = await tableDataService.fetchTableData({
        dataSources,
        filters,
        limit: 1000, // İlk implementasyon için limit
      });
      
      setTableData(data.rows || []);
      setTotalRows(data.total || 0);
    } catch (err) {
      setError('Veri yüklenirken bir hata oluştu.');
      setTableData([]);
      setTotalRows(0);
    } finally {
      setLoading(false);
    }
  }, [dataSources, filters]);

  /**
   * Veri yenileme
   */
  const refreshData = useCallback(() => {
    loadData();
  }, [loadData]);

  /**
   * Sayfa değiştirme (gelecek için)
   */
  const changePage = useCallback((page) => {
    // TODO: Implement pagination
  }, []);

  /**
   * Sıralama (gelecek için)
   */
  const sortData = useCallback((column, direction) => {
    // TODO: Implement sorting
  }, []);

  // İlk yükleme
  useEffect(() => {
    loadData();
  }, [loadData]);

  return {
    tableData,
    loading,
    error,
    totalRows,
    loadData,
    refreshData,
    changePage,
    sortData,
  };
};
