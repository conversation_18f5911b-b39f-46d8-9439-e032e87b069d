/**
 * Export System Integration Test
 * Quick verification that all export modules are properly integrated
 */

import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import ExportManager from './ExportManager';

// Mock data for testing
const mockReportData = {
  title: 'Test Raporu',
  summary: {
    totalIncome: 5000,
    totalExpense: 3000,
    netAmount: 2000,
  },
  transactions: [
    {
      id: 1,
      date: '2024-01-01',
      description: 'Test İşlem 1',
      amount: 1000,
      category: 'Test Kategori',
      type: 'income'
    },
    {
      id: 2,
      date: '2024-01-02',
      description: 'Test İşlem 2',
      amount: 500,
      category: 'Test Kategori',
      type: 'expense'
    }
  ]
};

const ExportSystemTest = () => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Export System Integration Test</Text>
      
      <ExportManager 
        reportData={mockReportData}
        reportTitle="Test Raporu"
        reportType="test"
        theme={{
          PRIMARY: '#007AFF',
          SURFACE: '#FFFFFF',
          TEXT_PRIMARY: '#000000',
          TEXT_SECONDARY: '#666666',
          ERROR: '#FF3B30',
          SUCCESS: '#34C759',
          BACKGROUND: '#F2F2F7'
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#F2F2F7',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
});

export default ExportSystemTest;
