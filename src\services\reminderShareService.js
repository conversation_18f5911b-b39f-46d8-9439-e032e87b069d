import { Share } from 'react-native';
import { format, parseISO } from 'date-fns';
import { tr } from 'date-fns/locale';

/**
 * Hatırlatıcı paylaşımı için servis fonksiyonları
 */

/**
 * Hatırlatıcıyı metin olarak paylaş
 *
 * @param {Object} reminder - Hatırlatıcı verisi
 * @returns {Promise<void>}
 */
export const shareReminderAsText = async (reminder) => {
  try {
    // Tarih ve saat bilgilerini ayır
    const scheduledDate = parseISO(reminder.scheduled_at);
    const formattedDate = format(scheduledDate, 'dd MMMM yyyy', { locale: tr });
    const formattedTime = format(scheduledDate, 'HH:mm');

    // Tekrarlama bilgisi
    let repeatInfo = 'Bir kez';
    if (reminder.repeat_type === 'daily') {
      repeatInfo = `Her ${reminder.repeat_interval} günde bir`;
    } else if (reminder.repeat_type === 'weekly') {
      repeatInfo = `Her ${reminder.repeat_interval} haftada bir`;

      if (reminder.repeat_days && reminder.repeat_days.length > 0) {
        const dayNames = ['Pazartesi', 'Salı', 'Çarşamba', 'Perşembe', 'Cuma', 'Cumartesi', 'Pazar'];
        const selectedDays = reminder.repeat_days.map(day => dayNames[day - 1]).join(', ');
        repeatInfo += ` (${selectedDays})`;
      }
    } else if (reminder.repeat_type === 'monthly') {
      repeatInfo = `Her ${reminder.repeat_interval} ayda bir`;
    } else if (reminder.repeat_type === 'yearly') {
      repeatInfo = `Her ${reminder.repeat_interval} yılda bir`;
    }

    // Öncelik bilgisi
    let priorityText = 'Normal';
    if (reminder.priority === 'low') {
      priorityText = 'Düşük';
    } else if (reminder.priority === 'high') {
      priorityText = 'Yüksek';
    }

    // Paylaşım metni
    const message = `📅 Hatırlatıcı: ${reminder.title}\n\n` +
      `${reminder.message ? `📝 ${reminder.message}\n\n` : ''}` +
      `📆 Tarih: ${formattedDate}\n` +
      `⏰ Saat: ${formattedTime}\n` +
      `🔄 Tekrarlama: ${repeatInfo}\n` +
      `⚠️ Öncelik: ${priorityText}\n` +
      `${reminder.category_name ? `🏷️ Kategori: ${reminder.category_name}\n` : ''}` +
      `${reminder.group_name ? `📁 Grup: ${reminder.group_name}\n` : ''}`;

    return message;
  } catch (error) {
    console.error('Hatırlatıcı metin paylaşımı hatası:', error);
    throw error;
  }
};

/**
 * Hatırlatıcıyı HTML olarak oluştur
 *
 * @param {Object} reminder - Hatırlatıcı verisi
 * @returns {string} HTML içeriği
 */
export const createReminderHtml = (reminder) => {
  try {
    // Tarih ve saat bilgilerini ayır
    const scheduledDate = parseISO(reminder.scheduled_at);
    const formattedDate = format(scheduledDate, 'dd MMMM yyyy', { locale: tr });
    const formattedTime = format(scheduledDate, 'HH:mm');

    // Tekrarlama bilgisi
    let repeatInfo = 'Bir kez';
    if (reminder.repeat_type === 'daily') {
      repeatInfo = `Her ${reminder.repeat_interval} günde bir`;
    } else if (reminder.repeat_type === 'weekly') {
      repeatInfo = `Her ${reminder.repeat_interval} haftada bir`;

      if (reminder.repeat_days && reminder.repeat_days.length > 0) {
        const dayNames = ['Pazartesi', 'Salı', 'Çarşamba', 'Perşembe', 'Cuma', 'Cumartesi', 'Pazar'];
        const selectedDays = reminder.repeat_days.map(day => dayNames[day - 1]).join(', ');
        repeatInfo += ` (${selectedDays})`;
      }
    } else if (reminder.repeat_type === 'monthly') {
      repeatInfo = `Her ${reminder.repeat_interval} ayda bir`;
    } else if (reminder.repeat_type === 'yearly') {
      repeatInfo = `Her ${reminder.repeat_interval} yılda bir`;
    }

    // Öncelik bilgisi ve rengi
    let priorityText = 'Normal';
    let priorityColor = '#f39c12';
    if (reminder.priority === 'low') {
      priorityText = 'Düşük';
      priorityColor = '#2ecc71';
    } else if (reminder.priority === 'high') {
      priorityText = 'Yüksek';
      priorityColor = '#e74c3c';
    }

    // Kategori rengi
    const categoryColor = reminder.category_color || '#3498db';

    // HTML içeriği
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Hatırlatıcı: ${reminder.title}</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
          }
          .reminder-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }
          .reminder-header {
            background-color: #3498db;
            color: white;
            padding: 15px;
            font-size: 20px;
            font-weight: bold;
          }
          .reminder-content {
            padding: 20px;
          }
          .reminder-message {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            font-style: italic;
          }
          .reminder-info {
            display: flex;
            flex-wrap: wrap;
          }
          .info-item {
            display: flex;
            align-items: center;
            margin-right: 20px;
            margin-bottom: 10px;
          }
          .info-label {
            font-weight: bold;
            margin-right: 5px;
          }
          .priority-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            color: white;
            font-weight: bold;
            background-color: ${priorityColor};
          }
          .category-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            color: white;
            font-weight: bold;
            background-color: ${categoryColor};
          }
          .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 12px;
            color: #777;
          }
        </style>
      </head>
      <body>
        <div class="reminder-card">
          <div class="reminder-header" style="background-color: ${reminder.group_color || '#3498db'}">
            ${reminder.title}
          </div>
          <div class="reminder-content">
            ${reminder.message ? `
              <div class="reminder-message">
                ${reminder.message.replace(/\\n/g, '<br>')}
              </div>
            ` : ''}

            <div class="reminder-info">
              <div class="info-item">
                <span class="info-label">📆 Tarih:</span>
                <span>${formattedDate}</span>
              </div>

              <div class="info-item">
                <span class="info-label">⏰ Saat:</span>
                <span>${formattedTime}</span>
              </div>

              <div class="info-item">
                <span class="info-label">🔄 Tekrarlama:</span>
                <span>${repeatInfo}</span>
              </div>

              <div class="info-item">
                <span class="info-label">⚠️ Öncelik:</span>
                <span class="priority-badge">${priorityText}</span>
              </div>

              ${reminder.category_name ? `
                <div class="info-item">
                  <span class="info-label">🏷️ Kategori:</span>
                  <span class="category-badge">${reminder.category_name}</span>
                </div>
              ` : ''}

              ${reminder.group_name ? `
                <div class="info-item">
                  <span class="info-label">📁 Grup:</span>
                  <span>${reminder.group_name}</span>
                </div>
              ` : ''}
            </div>
          </div>
        </div>

        <div class="footer">
          Bu hatırlatıcı, Finansal Takip uygulaması ile oluşturulmuştur.<br>
          Oluşturulma Tarihi: ${format(new Date(), 'dd MMMM yyyy HH:mm', { locale: tr })}
        </div>
      </body>
      </html>
    `;

    return html;
  } catch (error) {
    console.error('Hatırlatıcı HTML oluşturma hatası:', error);
    throw error;
  }
};

/**
 * Hatırlatıcıyı paylaş
 *
 * @param {Object} reminder - Hatırlatıcı verisi
 * @returns {Promise<void>}
 */
export const shareReminder = async (reminder) => {
  try {
    // Metin içeriğini oluştur
    const message = await shareReminderAsText(reminder);

    // Paylaş
    await Share.share({
      message,
      title: `Hatırlatıcı: ${reminder.title}`
    });
  } catch (error) {
    console.error('Hatırlatıcı paylaşımı hatası:', error);
    throw error;
  }
};
