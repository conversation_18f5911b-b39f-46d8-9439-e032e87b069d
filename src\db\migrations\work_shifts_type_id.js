/**
 * Vardiya tablosuna shift_type_id sütunu ekleyen migrasyon
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<void>}
 */
export const migrateWorkShiftsTypeId = async (db) => {
  try {
    // Migrasyon işlemini bir transaction içinde yap
    await db.execAsync('BEGIN TRANSACTION;');

    // work_shifts tablosunu kontrol et
    const workShiftsExists = await db.getFirstAsync(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name='work_shifts'
    `);

    if (workShiftsExists) {
      // Sütunları kontrol et
      const columns = await db.getAllAsync(`PRAGMA table_info(work_shifts)`);
      const columnNames = columns.map(col => col.name);
      
      // shift_type_id sütunu ekle
      if (!columnNames.includes('shift_type_id')) {
        // Varsayılan vardiya türünü bul
        let defaultShiftTypeId = 1;
        try {
          const defaultShiftType = await db.getFirstAsync(`
            SELECT id FROM work_shift_types ORDER BY id LIMIT 1
          `);
          if (defaultShiftType) {
            defaultShiftTypeId = defaultShiftType.id;
          }
        } catch (error) {
          console.warn('Varsayılan vardiya türü bulunamadı:', error.message);
        }
        
        // Geçici tablo oluştur
        await db.execAsync(`
          CREATE TABLE work_shifts_temp (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date TEXT NOT NULL,
            start_time TEXT NOT NULL,
            end_time TEXT NOT NULL,
            break_duration INTEGER DEFAULT 0,
            is_overtime INTEGER DEFAULT 0,
            overtime_multiplier REAL DEFAULT 1.5,
            is_holiday INTEGER DEFAULT 0,
            holiday_multiplier REAL DEFAULT 2.0,
            hourly_rate REAL DEFAULT 100.0,
            shift_type_id INTEGER DEFAULT ${defaultShiftTypeId},
            earnings REAL,
            status TEXT DEFAULT 'active',
            notes TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (shift_type_id) REFERENCES work_shift_types (id) ON DELETE SET NULL
          )
        `);
        
        // Verileri geçici tabloya kopyala
        const hasUpdatedAt = columnNames.includes('updated_at');
        
        if (hasUpdatedAt) {
          await db.execAsync(`
            INSERT INTO work_shifts_temp (
              id, date, start_time, end_time, break_duration, is_overtime, 
              overtime_multiplier, is_holiday, holiday_multiplier, 
              hourly_rate, earnings, status, notes, created_at, updated_at
            )
            SELECT 
              id, date, start_time, end_time, break_duration, is_overtime, 
              COALESCE(overtime_multiplier, 1.5), COALESCE(is_holiday, 0), COALESCE(holiday_multiplier, 2.0), 
              COALESCE(hourly_rate, 100.0), earnings, status, notes, created_at, updated_at
            FROM work_shifts
          `);
        } else {
          await db.execAsync(`
            INSERT INTO work_shifts_temp (
              id, date, start_time, end_time, break_duration, is_overtime, 
              overtime_multiplier, is_holiday, holiday_multiplier, 
              hourly_rate, earnings, status, notes, created_at
            )
            SELECT 
              id, date, start_time, end_time, break_duration, is_overtime, 
              COALESCE(overtime_multiplier, 1.5), COALESCE(is_holiday, 0), COALESCE(holiday_multiplier, 2.0), 
              COALESCE(hourly_rate, 100.0), earnings, status, notes, COALESCE(created_at, CURRENT_TIMESTAMP)
            FROM work_shifts
          `);
        }
        
        // Eski tabloyu sil ve geçici tabloyu yeniden adlandır
        await db.execAsync(`
          DROP TABLE work_shifts;
          ALTER TABLE work_shifts_temp RENAME TO work_shifts;
        `);
      }
    }

    // Transaction'ı tamamla
    await db.execAsync('COMMIT;');
  } catch (error) {
    // Hata durumunda transaction'ı geri al
    await db.execAsync('ROLLBACK;');
    console.error('Vardiya shift_type_id migrasyonu hatası:', error);
    throw error;
  }
};

/**
 * Vardiya tablosuna shift_type_id sütunu ekleyen migrasyon (Transaction olmadan)
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<void>}
 */
export const migrateWorkShiftsTypeIdNoTransaction = async (db) => {
  try {
    // work_shifts tablosunu kontrol et
    const workShiftsExists = await db.getFirstAsync(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='work_shifts'
    `);

    if (workShiftsExists) {
      // Sütunları kontrol et
      const columns = await db.getAllAsync(`PRAGMA table_info(work_shifts)`);
      const columnNames = columns.map(col => col.name);

      // shift_type_id sütunu ekle
      if (!columnNames.includes('shift_type_id')) {
        await db.execAsync(`ALTER TABLE work_shifts ADD COLUMN shift_type_id INTEGER`);
      }

      // Eğer tablo boşsa veya shift_type_id sütunu yeni eklendiyse, tabloyu yeniden oluştur
      const hasData = await db.getFirstAsync(`SELECT COUNT(*) as count FROM work_shifts`);

      if (hasData.count === 0 || !columnNames.includes('shift_type_id')) {
        console.log('work_shifts tablosu yeniden yapılandırılıyor...');

        // Geçici tablo oluştur
        await db.execAsync(`
          CREATE TABLE work_shifts_temp (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date TEXT NOT NULL,
            start_time TEXT NOT NULL,
            end_time TEXT NOT NULL,
            break_duration INTEGER DEFAULT 0,
            is_overtime INTEGER DEFAULT 0,
            overtime_multiplier REAL DEFAULT 1.5,
            is_holiday INTEGER DEFAULT 0,
            holiday_multiplier REAL DEFAULT 2.0,
            hourly_rate REAL DEFAULT 100.0,
            earnings REAL DEFAULT 0,
            status TEXT DEFAULT 'planned',
            notes TEXT,
            shift_type_id INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (shift_type_id) REFERENCES work_shift_types(id) ON DELETE SET NULL
          )
        `);

        // Eğer veri varsa, mevcut verileri kopyala
        if (hasData.count > 0) {
          await db.execAsync(`
            INSERT INTO work_shifts_temp (
              id, date, start_time, end_time, break_duration, is_overtime,
              overtime_multiplier, is_holiday, holiday_multiplier,
              hourly_rate, earnings, status, notes, created_at
            )
            SELECT
              id, date, start_time, end_time, break_duration, is_overtime,
              COALESCE(overtime_multiplier, 1.5), COALESCE(is_holiday, 0), COALESCE(holiday_multiplier, 2.0),
              COALESCE(hourly_rate, 100.0), earnings, status, notes, COALESCE(created_at, CURRENT_TIMESTAMP)
            FROM work_shifts
          `);
        }

        // Eski tabloyu sil ve geçici tabloyu yeniden adlandır
        await db.execAsync(`
          DROP TABLE work_shifts;
          ALTER TABLE work_shifts_temp RENAME TO work_shifts;
        `);
      }
    }
  } catch (error) {
    console.error('Vardiya shift_type_id migrasyonu hatası:', error);
    throw error;
  }
};
