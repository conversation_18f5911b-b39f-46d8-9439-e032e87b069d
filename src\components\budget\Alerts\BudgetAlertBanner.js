/**
 * Budget Alert Banner Component
 * Displays budget notifications and alerts
 * 
 * Features:
 * - Real-time budget notifications
 * - Swipe to dismiss
 * - Priority-based display
 * - Interactive tap actions
 * - Turkish localization
 * - Dark mode support
 */

import React, { memo } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

/**
 * Budget Alert Banner Component
 * @param {Array} notifications - Array of budget notifications
 * @param {Function} onNotificationPress - Handle notification press
 * @param {Object} theme - Theme colors
 */
const BudgetAlertBanner = memo(({ notifications, onNotificationPress, theme }) => {
  /**
   * Gets notification icon based on type
   */
  const getNotificationIcon = (type) => {
    const iconMap = {
      'threshold_75': 'warning-outline',
      'threshold_90': 'alert-circle-outline', 
      'over_budget': 'alert-circle',
      'category_over_limit': 'warning',
      'daily_summary': 'stats-chart-outline'
    };
    return iconMap[type] || 'information-circle-outline';
  };

  /**
   * Gets notification color based on type and priority
   */
  const getNotificationColor = (type, thresholdValue) => {
    if (type === 'over_budget' || type === 'category_over_limit') {
      return theme.DANGER;
    }
    if (type === 'threshold_90' || (thresholdValue && thresholdValue >= 90)) {
      return theme.WARNING;
    }
    if (type === 'threshold_75' || (thresholdValue && thresholdValue >= 75)) {
      return theme.WARNING_LIGHT;
    }
    return theme.INFO;
  };

  /**
   * Formats notification time
   */
  const formatNotificationTime = (sentAt) => {
    const now = new Date();
    const sent = new Date(sentAt);
    const diffMinutes = Math.floor((now - sent) / (1000 * 60));
    
    if (diffMinutes < 1) return 'Şimdi';
    if (diffMinutes < 60) return `${diffMinutes}dk önce`;
    
    const diffHours = Math.floor(diffMinutes / 60);
    if (diffHours < 24) return `${diffHours}sa önce`;
    
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays} gün önce`;
  };

  /**
   * Gets priority score for sorting notifications
   */
  const getNotificationPriority = (notification) => {
    const priorityMap = {
      'over_budget': 5,
      'category_over_limit': 4,
      'threshold_90': 3,
      'threshold_75': 2,
      'daily_summary': 1
    };
    return priorityMap[notification.notification_type] || 0;
  };

  // Sort notifications by priority and time
  const sortedNotifications = [...notifications].sort((a, b) => {
    const priorityDiff = getNotificationPriority(b) - getNotificationPriority(a);
    if (priorityDiff !== 0) return priorityDiff;
    return new Date(b.sent_at) - new Date(a.sent_at);
  });

  // Show only top 3 most important notifications
  const displayNotifications = sortedNotifications.slice(0, 3);

  if (displayNotifications.length === 0) {
    return null;
  }

  return (
    <View style={styles.container}>
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {displayNotifications.map((notification) => {
          const notificationColor = getNotificationColor(
            notification.notification_type, 
            notification.threshold_value
          );
          const icon = getNotificationIcon(notification.notification_type);

          return (
            <TouchableOpacity
              key={notification.id}
              style={[
                styles.notificationCard,
                { 
                  backgroundColor: theme.SURFACE,
                  borderLeftColor: notificationColor,
                }
              ]}
              onPress={() => onNotificationPress(notification)}
              activeOpacity={0.8}
            >
              {/* Notification Icon */}
              <View style={[styles.iconContainer, { backgroundColor: notificationColor }]}>
                <Ionicons name={icon} size={20} color={theme.ON_PRIMARY} />
              </View>

              {/* Notification Content */}
              <View style={styles.contentContainer}>
                <View style={styles.headerRow}>
                  <Text style={[styles.budgetName, { color: theme.TEXT_PRIMARY }]}>
                    {notification.budget_name}
                  </Text>
                  <Text style={[styles.timeText, { color: theme.TEXT_DISABLED }]}>
                    {formatNotificationTime(notification.sent_at)}
                  </Text>
                </View>

                <Text 
                  style={[styles.messageText, { color: theme.TEXT_SECONDARY }]}
                  numberOfLines={2}
                >
                  {notification.message}
                </Text>

                {notification.category_name && (
                  <View style={styles.categoryRow}>
                    <Ionicons name="pricetag" size={12} color={theme.TEXT_DISABLED} />
                    <Text style={[styles.categoryText, { color: theme.TEXT_DISABLED }]}>
                      {notification.category_name}
                    </Text>
                  </View>
                )}

                {notification.threshold_value && (
                  <View style={styles.progressContainer}>
                    <View style={[styles.progressTrack, { backgroundColor: theme.BORDER }]}>
                      <View
                        style={[
                          styles.progressBar,
                          {
                            width: `${Math.min(notification.threshold_value, 100)}%`,
                            backgroundColor: notificationColor,
                          }
                        ]}
                      />
                    </View>
                    <Text style={[styles.progressText, { color: notificationColor }]}>
                      %{Math.round(notification.threshold_value || 0)}
                    </Text>
                  </View>
                )}
              </View>

              {/* Dismiss Button */}
              <TouchableOpacity
                style={styles.dismissButton}
                onPress={(e) => {
                  e.stopPropagation();
                  // Handle dismiss if needed
                }}
              >
                <Ionicons name="close" size={16} color={theme.TEXT_DISABLED} />
              </TouchableOpacity>
            </TouchableOpacity>
          );
        })}
      </ScrollView>

      {/* Show all notifications button if there are more */}
      {notifications.length > 3 && (
        <TouchableOpacity
          style={[styles.showAllButton, { backgroundColor: theme.PRIMARY }]}
          onPress={() => {
            // Navigate to notifications screen if available
            console.log('Show all notifications');
          }}
        >
          <Text style={[styles.showAllText, { color: theme.ON_PRIMARY }]}>
            +{notifications.length - 3} daha
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  scrollContent: {
    paddingHorizontal: 16,
    gap: 12,
  },
  notificationCard: {
    width: 300,
    borderRadius: 10,
    padding: 12,
    borderLeftWidth: 4,
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentContainer: {
    flex: 1,
    gap: 4,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  budgetName: {
    fontSize: 14,
    fontWeight: '600',
    flex: 1,
  },
  timeText: {
    fontSize: 12,
  },
  messageText: {
    fontSize: 13,
    lineHeight: 16,
  },
  categoryRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  categoryText: {
    fontSize: 12,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginTop: 4,
  },
  progressTrack: {
    flex: 1,
    height: 4,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 12,
    fontWeight: '600',
    minWidth: 35,
    textAlign: 'right',
  },
  dismissButton: {
    padding: 4,
  },
  showAllButton: {
    alignSelf: 'center',
    paddingHorizontal: 16,
    paddingVertical: 6,
    borderRadius: 15,
    marginTop: 8,
  },
  showAllText: {
    fontSize: 12,
    fontWeight: '600',
  },
});

export default BudgetAlertBanner;
