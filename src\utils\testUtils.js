/**
 * Test tarihi yardımcı fonksiyonları
 */

import { getSafeDateRange, formatDateSafely, isValidDate } from '../utils/dateUtils';

export const testDateUtils = () => {
  console.log('=== Tarih Yardımcı Fonksiyon Testleri ===');
  
  try {
    // Test 1: getSafeDateRange
    console.log('Test 1: getSafeDateRange');
    const lastMonth = getSafeDateRange('last30days');
    console.log('Son 30 gün:', lastMonth);
    
    const last3Months = getSafeDateRange('last3months');
    console.log('Son 3 ay:', last3Months);
    
    // Test 2: formatDateSafely
    console.log('\nTest 2: formatDateSafely');
    const today = new Date();
    console.log('Bugün (YYYY-MM-DD):', formatDateSafely(today));
    console.log('Bugün (DD/MM/YYYY):', formatDateSafely(today, 'DD/MM/YYYY'));
    console.log('Bugün (readable):', formatDateSafely(today, 'readable'));
    
    // Test 3: isValidDate
    console.log('\nTest 3: isValidDate');
    console.log('Geçerli tarih:', isValidDate(new Date()));
    console.log('Geçersiz tarih:', isValidDate('invalid-date'));
    console.log('Null tarih:', isValidDate(null));
    
    console.log('\n=== Testler Tamamlandı ===');
    return true;
  } catch (error) {
    console.error('Test hatası:', error);
    return false;
  }
};

// Kategorik dağılım testi için mock veri
export const generateMockCategoryData = () => {
  return [
    { category: 'Yemek', amount: -1250.50, count: 25 },
    { category: 'Ulaşım', amount: -430.75, count: 12 },
    { category: 'Alışveriş', amount: -890.25, count: 8 },
    { category: 'Maaş', amount: 5000.00, count: 1 },
    { category: 'Freelance', amount: 1200.00, count: 3 },
    { category: 'Faturalar', amount: -675.80, count: 6 },
    { category: 'Eğlence', amount: -320.60, count: 4 },
    { category: 'Sağlık', amount: -180.40, count: 2 }
  ];
};

// Aylık gelir-gider testi için mock veri
export const generateMockMonthlyData = () => {
  const months = ['Ocak', 'Şubat', 'Mart', 'Nisan', 'Mayıs', 'Haziran'];
  
  return months.map((month, index) => ({
    month,
    income: 5000 + (Math.random() * 1000),
    expense: 3000 + (Math.random() * 1500),
    net: 0 // Hesaplanacak
  })).map(item => ({
    ...item,
    net: item.income - item.expense
  }));
};

// Test verilerini çalıştır
if (__DEV__) {
  // Development modunda testleri çalıştır
  setTimeout(() => {
    testDateUtils();
  }, 1000);
}
