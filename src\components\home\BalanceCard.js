import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../../constants/colors';

/**
 * Bakiye kartı bileşeni
 *
 * @param {Object} props - Bileşen props'ları
 * @param {number} props.balance - Toplam bakiye
 * @param {number} props.income - Toplam gelir
 * @param {number} props.expense - Toplam gider
 * @param {number} props.usdRate - Dolar kuru
 * @param {number} props.eurRate - Euro kuru
 * @returns {JSX.Element} BalanceCard bileşeni
 */
const BalanceCard = ({ balance = 0, income = 0, expense = 0, usdRate = 0, eurRate = 0 }) => {
  const [showDetails, setShowDetails] = useState(false);

  // Para birimini formatla
  const formatCurrency = (amount, currency = 'TRY') => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(amount);
  };

  // Döviz değerlerini hesapla
  const usdValue = usdRate ? balance / usdRate : 0;
  const eurValue = eurRate ? balance / eurRate : 0;

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Toplam Bakiye</Text>
        <TouchableOpacity
          style={styles.detailsButton}
          onPress={() => setShowDetails(!showDetails)}
        >
          <MaterialIcons
            name={showDetails ? "expand-less" : "expand-more"}
            size={24}
            color="#fff"
          />
        </TouchableOpacity>
      </View>

      <Text style={styles.balanceAmount}>{formatCurrency(balance)}</Text>

      {/* Döviz değerleri */}
      {showDetails && (
        <View style={styles.exchangeRates}>
          <Text style={styles.exchangeRateText}>
            {formatCurrency(usdValue, 'USD')}
          </Text>
          <Text style={styles.exchangeRateText}>
            {formatCurrency(eurValue, 'EUR')}
          </Text>
        </View>
      )}

      {/* Gelir ve gider özeti */}
      <View style={styles.summaryContainer}>
        <View style={styles.summaryItem}>
          <View style={styles.summaryIconContainer}>
            <MaterialIcons name="arrow-upward" size={16} color="#2ecc71" />
          </View>
          <View>
            <Text style={styles.summaryLabel}>Gelir</Text>
            <Text style={styles.summaryAmount}>{formatCurrency(income)}</Text>
          </View>
        </View>

        <View style={styles.divider} />

        <View style={styles.summaryItem}>
          <View style={[styles.summaryIconContainer, { backgroundColor: 'rgba(231, 76, 60, 0.2)' }]}>
            <MaterialIcons name="arrow-downward" size={16} color="#e74c3c" />
          </View>
          <View>
            <Text style={styles.summaryLabel}>Gider</Text>
            <Text style={styles.summaryAmount}>{formatCurrency(expense)}</Text>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.PRIMARY,
    borderRadius: 16,
    padding: 20,
    margin: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  title: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    opacity: 0.9,
  },
  detailsButton: {
    padding: 4,
  },
  balanceAmount: {
    color: '#fff',
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  exchangeRates: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginBottom: 16,
  },
  exchangeRateText: {
    color: '#fff',
    fontSize: 14,
    opacity: 0.9,
    marginRight: 16,
  },
  summaryContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    marginTop: 8,
  },
  summaryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  summaryIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(46, 204, 113, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  summaryLabel: {
    color: '#fff',
    fontSize: 14,
    opacity: 0.9,
  },
  summaryAmount: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  divider: {
    width: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    marginHorizontal: 16,
  },
});

export default BalanceCard;
