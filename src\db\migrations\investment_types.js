/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> yatırım türleri
 */
export const DEFAULT_INVESTMENT_TYPES = [
  { name: '<PERSON><PERSON>', icon: 'trending-up', isDefault: true },
  { name: '<PERSON><PERSON><PERSON>', icon: 'monetization-on', isDefault: true },
  { name: '<PERSON><PERSON><PERSON><PERSON>', icon: 'attach-money', isDefault: true },
  { name: 'Krip<PERSON> Para', icon: 'currency-bitcoin', isDefault: true },
  { name: '<PERSON>hvil/Bono', icon: 'description', isDefault: true },
  { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', icon: 'home', isDefault: true },
  { name: '<PERSON><PERSON>', icon: 'account-balance', isDefault: true },
];

/**
 * Yatırım türleri tablosunu oluşturur
 * @param {Object} db - SQLite veritabanı bağlantısı
 */
export const createInvestmentTypesTable = async (db) => {
  await db.execAsync(`
    CREATE TABLE IF NOT EXISTS investment_types (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL UNIQUE,
      icon TEXT,
      is_default BOOLEAN DEFAULT 0,
      created_at DATETIME DEFAULT (datetime('now', 'localtime')),
      updated_at DATETIME DEFAULT (datetime('now', 'localtime'))
    );
  `);

  // Varsayılan türleri ekle
  for (const type of DEFAULT_INVESTMENT_TYPES) {
    await db.runAsync(`
      INSERT OR IGNORE INTO investment_types (name, icon, is_default)
      VALUES (?, ?, ?)
    `, [type.name, type.icon, type.isDefault ? 1 : 0]);
  }
};
