import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Animated,
  Vibration,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { useAppContext } from '../context/AppContext';
import * as LocalAuthentication from 'expo-local-authentication';
import * as SecureStore from 'expo-secure-store';
import * as Crypto from 'expo-crypto';

/**
 * PIN Kurulum Akış Ekranı
 * Yeni kullanıcılar için PIN kurulum sürecini yönetir
 */
export default function PinSetupFlow({ navigation, route }) {
  const insets = useSafeAreaInsets();
  const { theme } = useTheme();
  const { defaultCurrency } = useAppContext();
  
  // Kurulum adımları - 4 adım: PIN oluştur, PIN onayla, Biometrik seç, Güvenlik soruları
  const [currentStep, setCurrentStep] = useState(1);
  const [pin, setPin] = useState('');
  const [confirmPin, setConfirmPin] = useState('');
  const [biometricAvailable, setBiometricAvailable] = useState(false);
  const [biometricType, setBiometricType] = useState(null);
  const [enableBiometric, setEnableBiometric] = useState(false);
  const [setupSecurityQuestions, setSetupSecurityQuestions] = useState(false);
  
  // Animasyonlar
  const [shakeAnimation] = useState(new Animated.Value(0));
  const [fadeAnimation] = useState(new Animated.Value(1));

  useEffect(() => {
    checkBiometricSupport();
  }, []);

  /**
   * Biyometrik destek kontrolü
   */
  const checkBiometricSupport = async () => {
    try {
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      const supportedTypes = await LocalAuthentication.supportedAuthenticationTypesAsync();
      
      console.log('Biometric Check:', { hasHardware, isEnrolled, supportedTypes });
      
      const isAvailable = hasHardware && isEnrolled && supportedTypes.length > 0;
      setBiometricAvailable(isAvailable);
      
      if (!isAvailable) {
        setBiometricType(null);
        console.log('Biometric not available');
        return;
      }
      
      // Öncelik sırası: Parmak izi > Yüz tanıma > Iris > Diğer
      // Bu sıralama ile daha yaygın olan parmak izi önceliklendirilir
      if (supportedTypes.includes(LocalAuthentication.AuthenticationType.FINGERPRINT)) {
        setBiometricType('fingerprint');
        console.log('Fingerprint available');
      } else if (supportedTypes.includes(LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION)) {
        setBiometricType('face');
        console.log('Face recognition available');
      } else if (supportedTypes.includes(LocalAuthentication.AuthenticationType.IRIS)) {
        setBiometricType('iris');
        console.log('Iris available');
      } else {
        setBiometricType('biometric');
        console.log('Generic biometric available');
      }
    } catch (error) {
      console.error('Biyometrik kontrol hatası:', error);
      setBiometricAvailable(false);
      setBiometricType(null);
    }
  };

  /**
   * PIN doğrulama
   */
  const validatePin = (pinValue) => {
    if (pinValue.length !== 6) {
      return { valid: false, message: 'PIN 6 haneli olmalıdır.' };
    }
    if (!/^\d{6}$/.test(pinValue)) {
      return { valid: false, message: 'PIN sadece rakamlardan oluşmalıdır.' };
    }
    // Basit PIN kontrolü (123456, 000000 gibi)
    if (pinValue === '123456' || pinValue === '000000' || /^(.)\1{5}$/.test(pinValue)) {
      return { valid: false, message: 'Lütfen daha güvenli bir PIN seçin.' };
    }
    return { valid: true };
  };

  /**
   * PIN rakam ekleme
   */
  const addDigit = (digit) => {
    if (currentStep === 1) {
      if (pin.length < 6) {
        setPin(pin + digit);
      }
    } else if (currentStep === 2) {
      if (confirmPin.length < 6) {
        setConfirmPin(confirmPin + digit);
      }
    }
  };

  /**
   * Son rakamı silme
   */
  const removeDigit = () => {
    if (currentStep === 1) {
      setPin(pin.slice(0, -1));
    } else if (currentStep === 2) {
      setConfirmPin(confirmPin.slice(0, -1));
    }
  };

  /**
   * PIN onaylama
   */
  const confirmStep = async () => {
    if (currentStep === 1) {
      // İlk PIN girişi
      const validation = validatePin(pin);
      if (!validation.valid) {
        Alert.alert('Geçersiz PIN', validation.message);
        playShakeAnimation();
        setPin('');
        return;
      }
      setCurrentStep(2);
    } else if (currentStep === 2) {
      // PIN onaylama
      if (pin !== confirmPin) {
        Alert.alert('PIN Uyuşmadı', 'Girdiğiniz PIN\'ler uyuşmuyor. Lütfen tekrar deneyin.');
        playShakeAnimation();
        setConfirmPin('');
        return;
      }
      setCurrentStep(3);
    } else if (currentStep === 3) {
      // Biyometrik ayar tamamlandı, güvenlik sorularına geç
      setCurrentStep(4);
    } else if (currentStep === 4) {
      // Güvenlik soruları seçimi
      await setupSecurity();
    }
  };

  /**
   * Güvenlik ayarlarını kaydet
   */
  const setupSecurity = async () => {
    try {
      // PIN'i hashle ve kaydet
      const pinHash = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        pin + 'finansal-takip-salt' // Salt ekleme
      );
      
      await SecureStore.setItemAsync('pinHash', pinHash);
      await SecureStore.setItemAsync('pinEnabled', 'true');
      
      if (enableBiometric && biometricAvailable) {
        await SecureStore.setItemAsync('biometricEnabled', 'true');
      }
      
      // Güvenlik soruları kurulumu seçilmişse
      if (setupSecurityQuestions) {
        navigation.replace('SecurityQuestions', { mode: 'setup', fromSetup: true });
      } else {
        // Kurulum tamamlandı
        Alert.alert(
          'Kurulum Tamamlandı',
          'PIN ve güvenlik ayarlarınız başarıyla kuruldu.',
          [
            {
              text: 'Tamam',
              onPress: () => navigation.replace('Main')
            }
          ]
        );
      }
    } catch (error) {
      console.error('Güvenlik kurulum hatası:', error);
      Alert.alert('Hata', 'Güvenlik ayarları kaydedilirken bir hata oluştu.');
    }
  };

  /**
   * Sallama animasyonu
   */
  const playShakeAnimation = () => {
    Vibration.vibrate(500);
    Animated.sequence([
      Animated.timing(shakeAnimation, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: -10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 0, duration: 50, useNativeDriver: true }),
    ]).start();
  };

  /**
   * Kurulum adımını atla
   */
  const skipSetup = () => {
    Alert.alert(
      'Kurulumu Atla',
      'PIN kurulumu yapmadan devam etmek istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        { 
          text: 'Atla', 
          style: 'destructive',
          onPress: () => navigation.replace('Main')
        }
      ]
    );
  };

  /**
   * Adım içeriğini render et
   */
  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return renderPinSetup();
      case 2:
        return renderPinConfirm();
      case 3:
        return renderBiometricSetup();
      case 4:
        return renderSecurityQuestionsSetup();
      default:
        return null;
    }
  };

  /**
   * PIN kurulum adımı
   */
  const renderPinSetup = () => (
    <View style={styles.stepContainer}>
      <MaterialIcons name="lock" size={48} color={theme.colors.primary} />
      <Text style={[styles.stepTitle, { color: theme.colors.text }]}>
        PIN Oluştur
      </Text>
      <Text style={[styles.stepDescription, { color: theme.colors.textSecondary }]}>
        6 haneli güvenlik PIN'i oluşturun
      </Text>
      
      <Animated.View 
        style={[
          styles.pinDisplay,
          { transform: [{ translateX: shakeAnimation }] }
        ]}
      >
        {[...Array(6)].map((_, index) => (
          <View
            key={index}
            style={[
              styles.pinDot,
              { 
                backgroundColor: index < pin.length ? theme.colors.primary : 'transparent',
                borderColor: index < pin.length ? theme.colors.primary : theme.colors.textSecondary + '40'
              }
            ]}
          />
        ))}
      </Animated.View>
    </View>
  );

  /**
   * PIN onaylama adımı
   */
  const renderPinConfirm = () => (
    <View style={styles.stepContainer}>
      <MaterialIcons name="check-circle" size={48} color={theme.colors.success} />
      <Text style={[styles.stepTitle, { color: theme.colors.text }]}>
        PIN Onayla
      </Text>
      <Text style={[styles.stepDescription, { color: theme.colors.textSecondary }]}>
        PIN'inizi tekrar girin
      </Text>
      
      <Animated.View 
        style={[
          styles.pinDisplay,
          { transform: [{ translateX: shakeAnimation }] }
        ]}
      >
        {[...Array(6)].map((_, index) => (
          <View
            key={index}
            style={[
              styles.pinDot,
              { 
                backgroundColor: index < confirmPin.length ? theme.colors.success : 'transparent',
                borderColor: index < confirmPin.length ? theme.colors.success : theme.colors.textSecondary + '40'
              }
            ]}
          />
        ))}
      </Animated.View>
    </View>
  );

  /**
   * Biyometrik kurulum adımı
   */
  const renderBiometricSetup = () => {
    const getBiometricIcon = () => {
      switch (biometricType) {
        case 'face':
          return 'face';
        case 'fingerprint':
          return 'fingerprint';
        case 'iris':
          return 'visibility';
        default:
          return 'security';
      }
    };
    
    const getBiometricText = () => {
      return 'Biyometrik Güvenlik';
    };
    
    const getBiometricDescription = () => {
      if (!biometricAvailable) {
        return 'Cihazınızda biyometrik güvenlik desteklenmiyor.';
      }
      
      return 'Hızlı ve güvenli giriş için biyometrik güvenlik.';
    };
    
    return (
      <View style={styles.stepContainer}>
        <MaterialIcons 
          name={getBiometricIcon()} 
          size={48} 
          color={biometricAvailable ? theme.colors.info : theme.colors.textSecondary} 
        />
        <Text style={[styles.stepTitle, { color: theme.colors.text }]}>
          {getBiometricText()}
        </Text>
        <Text style={[styles.stepDescription, { color: theme.colors.textSecondary }]}>
          {getBiometricDescription()}
        </Text>
        
        {biometricAvailable && (
          <TouchableOpacity
            style={[
              styles.biometricOption,
              { 
                backgroundColor: enableBiometric ? theme.colors.primary + '15' : 'transparent',
                borderColor: enableBiometric ? theme.colors.primary : theme.colors.textSecondary + '30'
              }
            ]}
            onPress={() => setEnableBiometric(!enableBiometric)}
          >
            <MaterialIcons 
              name={enableBiometric ? 'check-circle' : 'radio-button-unchecked'} 
              size={24} 
              color={enableBiometric ? theme.colors.primary : theme.colors.textSecondary} 
            />
            <Text style={[styles.biometricText, { color: theme.colors.text }]}>
              Biyometrik Güvenlik Etkinleştir
            </Text>
          </TouchableOpacity>
        )}
        
        {!biometricAvailable && (
          <View style={[styles.biometricOption, { 
            backgroundColor: 'transparent',
            borderColor: theme.colors.textSecondary + '30',
            opacity: 0.6
          }]}>
            <MaterialIcons 
              name="info" 
              size={24} 
              color={theme.colors.textSecondary} 
            />
            <Text style={[styles.biometricText, { color: theme.colors.textSecondary }]}>
              Biyometrik Güvenlik Kullanılamıyor
            </Text>
          </View>
        )}
      </View>
    );
  };

  /**
   * Güvenlik soruları kurulum adımı
   */
  const renderSecurityQuestionsSetup = () => (
    <View style={styles.stepContainer}>
      <MaterialIcons name="help" size={48} color={theme.colors.warning} />
      <Text style={[styles.stepTitle, { color: theme.colors.text }]}>
        Güvenlik Soruları
      </Text>
      <Text style={[styles.stepDescription, { color: theme.colors.textSecondary }]}>
        PIN'inizi unuttuğunuzda erişim için güvenlik soruları kurabilirsiniz.
      </Text>
      
      <TouchableOpacity
        style={[
          styles.biometricOption,
          { 
            backgroundColor: setupSecurityQuestions ? theme.colors.primary + '15' : 'transparent',
            borderColor: setupSecurityQuestions ? theme.colors.primary : theme.colors.textSecondary + '30'
          }
        ]}
        onPress={() => setSetupSecurityQuestions(!setupSecurityQuestions)}
      >
        <MaterialIcons 
          name={setupSecurityQuestions ? 'check-circle' : 'radio-button-unchecked'} 
          size={24} 
          color={setupSecurityQuestions ? theme.colors.primary : theme.colors.textSecondary} 
        />
        <Text style={[styles.biometricText, { color: theme.colors.text }]}>
          Güvenlik Soruları Kurulumu
        </Text>
      </TouchableOpacity>
      
      <View style={[styles.biometricOption, { 
        backgroundColor: 'transparent',
        borderColor: theme.colors.textSecondary + '30',
        opacity: 0.8
      }]}
      >
        <MaterialIcons 
          name="info" 
          size={24} 
          color={theme.colors.textSecondary} 
        />
        <Text style={[styles.biometricText, { color: theme.colors.textSecondary }]}>
          {setupSecurityQuestions 
            ? 'PIN kurulumu sonrası güvenlik soruları kurulumuna yönlendirileceksiniz.'
            : 'Güvenlik soruları kurulumunu daha sonra ayarlardan yapabilirsiniz.'
          }
        </Text>
      </View>
    </View>
  );

  /**
   * Sayısal tuş takımı - Minimal ve şeffaf tasarım
   */
  const renderNumPad = () => {
    const currentInput = currentStep === 1 ? pin : confirmPin;
    const maxLength = 6;
    
    if (currentStep === 3 || currentStep === 4) return null; // Biyometrik ve güvenlik soruları adımında tuş takımı gösterme
    
    const numPadLayout = [
      [1, 2, 3],
      [4, 5, 6],
      [7, 8, 9],
      ['', 0, 'backspace']
    ];
    
    return (
      <View style={styles.numPad}>
        {numPadLayout.map((row, rowIndex) => (
          <View key={rowIndex} style={styles.numPadRow}>
            {row.map((item, itemIndex) => {
              if (item === '') {
                return (
                  <View key={itemIndex} style={styles.numButton} />
                );
              }
              
              if (item === 'backspace') {
                return (
                  <TouchableOpacity
                    key={itemIndex}
                    style={[
                      styles.numButton,
                      currentInput.length === 0 && { opacity: 0.3 }
                    ]}
                    onPress={removeDigit}
                    disabled={currentInput.length === 0}
                  >
                    <MaterialIcons 
                      name="backspace" 
                      size={24} 
                      color={currentInput.length === 0 ? theme.colors.textSecondary : theme.colors.text} 
                    />
                  </TouchableOpacity>
                );
              }
              
              return (
                <TouchableOpacity
                  key={itemIndex}
                  style={[
                    styles.numButton,
                    currentInput.length >= maxLength && { opacity: 0.3 }
                  ]}
                  onPress={() => addDigit(item.toString())}
                  disabled={currentInput.length >= maxLength}
                >
                  <Text style={[styles.numButtonText, { color: theme.colors.text }]}>
                    {item}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </View>
        ))}
      </View>
    );
  };

  return (
    <KeyboardAvoidingView 
      style={[styles.container, { backgroundColor: theme.colors.background, paddingTop: insets.top }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
    >
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={skipSetup}>
          <Text style={[styles.skipText, { color: theme.colors.textSecondary }]}>
            Atla
          </Text>
        </TouchableOpacity>
        
        <View style={styles.progressContainer}>
          {[1, 2, 3, 4].map((step) => (
            <View
              key={step}
              style={[
                styles.progressDot,
                { 
                  backgroundColor: step <= currentStep ? theme.colors.primary : theme.colors.border
                }
              ]}
            />
          ))}
        </View>
        
        <View style={{ width: 40 }} />
      </View>

      {/* Content */}
      <View style={styles.content}>
        {renderStep()}
      </View>

      {/* NumPad */}
      {renderNumPad()}

      {/* Action Button */}
      <View style={styles.actionContainer}>
        <TouchableOpacity
          style={[
            styles.actionButton,
            { 
              backgroundColor: theme.colors.primary,
              opacity: (currentStep < 3 && (currentStep === 1 ? pin.length === 6 : confirmPin.length === 6)) || currentStep >= 3 ? 1 : 0.5
            }
          ]}
          onPress={confirmStep}
          disabled={(currentStep < 3 && (currentStep === 1 ? pin.length !== 6 : confirmPin.length !== 6)) && currentStep < 3}
        >
          <Text style={[styles.actionButtonText, { color: theme.colors.white }]}>
            {currentStep === 4 ? 'Kurulumu Tamamla' : 'Devam'}
          </Text>
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  skipText: {
    fontSize: 16,
    fontWeight: '500',
  },
  progressContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  progressDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 32,
  },
  stepContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  stepTitle: {
    fontSize: 22,
    fontWeight: '600',
    textAlign: 'center',
    marginTop: 24,
    marginBottom: 8,
  },
  stepDescription: {
    fontSize: 15,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 32,
  },
  pinDisplay: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 12,
    paddingVertical: 16,
    marginTop: 8,
  },
  pinDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    borderWidth: 1.5,
  },
  biometricOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    borderWidth: 1.5,
    gap: 12,
    marginTop: 24,
  },
  biometricText: {
    fontSize: 16,
    fontWeight: '500',
    flex: 1,
  },
  numPad: {
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  numPadRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 12,
  },
  numButton: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 16,
  },
  numButtonText: {
    fontSize: 24,
    fontWeight: '400',
  },
  actionContainer: {
    paddingHorizontal: 32,
    paddingBottom: 32,
  },
  actionButton: {
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});
