/**
 * <PERSON><PERSON><PERSON>z <PERSON> Komponenti
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md - Stage 4, Phase 1
 * 
 * <PERSON><PERSON>i bazlı harcama dağılımı ve analizi (Pasta grafik)
 * Maksimum 300 satır - Tek sorumluluk prensibi
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../../../context/ThemeContext';
import { formatTRY, formatUSD, formatEUR } from '../../../utils/formatters';

const { width } = Dimensions.get('window');

/**
 * Kategori analiz grafiği komponenti
 * @param {Object} props - Component props
 * @param {Array} props.categoryData - Kategori verileri [{id, name, amount, budget, icon, color}]
 * @param {string} props.currency - Para birim<PERSON> ('TRY', 'USD', 'EUR')
 * @param {boolean} props.showBudgetComparison - Bütçe karşılaştırması gösterilsin mi
 * @param {Function} props.onCategoryPress - Kategori tıklama callback
 * @param {Object} props.theme - Tema objesi (opsiyonel, context'ten alınır)
 */
const CategoryAnalysisChart = ({ 
  categoryData = [], 
  currency = 'TRY',
  showBudgetComparison = true,
  onCategoryPress,
  theme: propTheme 
}) => {
  const { theme, isDarkMode } = useTheme();
  const currentTheme = propTheme || theme;

  // State yönetimi
  const [selectedCategory, setSelectedCategory] = useState(null);

  /**
   * Para birimi formatı
   * @param {number} value - Formatlanacak değer
   * @returns {string} Formatlanmış değer
   */
  const formatCurrency = (value) => {
    switch (currency) {
      case 'USD':
        return formatUSD(value);
      case 'EUR':
        return formatEUR(value);
      default:
        return formatTRY(value);
    }
  };

  /**
   * Para birimi sembolü
   * @returns {string} Para birimi sembolü
   */
  const getCurrencySymbol = () => {
    switch (currency) {
      case 'USD':
        return '$';
      case 'EUR':
        return '€';
      default:
        return '₺';
    }
  };

  /**
   * Pasta grafik verilerini hazırla
   * @returns {Object} Hazırlanmış pasta grafik verileri
   */
  const preparePieData = () => {
    if (categoryData.length === 0) {
      return {
        total: 0,
        slices: []
      };
    }

    const total = categoryData.reduce((sum, cat) => sum + cat.amount, 0);
    let currentAngle = 0;

    const slices = categoryData.map((category, index) => {
      const percentage = total > 0 ? (category.amount / total) * 100 : 0;
      const angle = (category.amount / total) * 360;
      
      const slice = {
        ...category,
        percentage,
        startAngle: currentAngle,
        endAngle: currentAngle + angle,
        color: category.color || getDefaultColor(index)
      };

      currentAngle += angle;
      return slice;
    });

    return {
      total,
      slices: slices.sort((a, b) => b.amount - a.amount)
    };
  };

  /**
   * Varsayılan renk paleti
   * @param {number} index - Kategori indeksi
   * @returns {string} Renk kodu
   */
  const getDefaultColor = (index) => {
    const colors = [
      currentTheme.PRIMARY,
      currentTheme.SUCCESS,
      currentTheme.WARNING,
      currentTheme.INFO,
      currentTheme.ERROR,
      '#9C27B0',
      '#FF5722',
      '#607D8B'
    ];
    return colors[index % colors.length];
  };

  /**
   * Kategori performans durumu
   * @param {Object} category - Kategori objesi
   * @returns {Object} Performans durumu {status, color, icon}
   */
  const getCategoryStatus = (category) => {
    if (!showBudgetComparison || !category.budget) {
      return {
        status: 'normal',
        color: currentTheme.TEXT_SECONDARY,
        icon: 'info'
      };
    }

    const percentage = (category.amount / category.budget) * 100;

    if (percentage > 100) {
      return {
        status: 'over',
        color: currentTheme.ERROR,
        icon: 'error'
      };
    } else if (percentage > 90) {
      return {
        status: 'warning',
        color: currentTheme.WARNING,
        icon: 'warning'
      };
    } else if (percentage > 75) {
      return {
        status: 'caution',
        color: currentTheme.INFO,
        icon: 'info'
      };
    } else {
      return {
        status: 'good',
        color: currentTheme.SUCCESS,
        icon: 'check-circle'
      };
    }
  };

  /**
   * Kategori tıklama işleyicisi
   * @param {Object} category - Kategori objesi
   */
  const handleCategoryPress = (category) => {
    setSelectedCategory(selectedCategory?.id === category.id ? null : category);
    if (onCategoryPress) {
      onCategoryPress(category);
    }
  };

  if (categoryData.length === 0) {
    return (
      <View style={[styles.container, { backgroundColor: currentTheme.SURFACE }]}>
        <View style={styles.emptyContainer}>
          <MaterialIcons name="pie-chart" size={48} color={currentTheme.TEXT_SECONDARY} />
          <Text style={[styles.emptyTitle, { color: currentTheme.TEXT_PRIMARY }]}>
            Kategori Verisi Yok
          </Text>
          <Text style={[styles.emptySubtitle, { color: currentTheme.TEXT_SECONDARY }]}>
            Henüz kategori bazlı harcama verisi bulunmuyor
          </Text>
        </View>
      </View>
    );
  }

  const pieData = preparePieData();
  const currencySymbol = getCurrencySymbol();

  return (
    <View style={[styles.container, { backgroundColor: currentTheme.SURFACE }]}>
      {/* Header */}
      <View style={styles.header}>
        <MaterialIcons name="pie-chart" size={20} color={currentTheme.PRIMARY} />
        <Text style={[styles.title, { color: currentTheme.TEXT_PRIMARY }]}>
          Kategori Analizi
        </Text>
      </View>

      {/* Pasta grafik (Basit görsel temsil) */}
      <View style={styles.chartSection}>
        <View style={styles.pieChartContainer}>
          <View style={[styles.pieChart, { backgroundColor: currentTheme.BORDER }]}>
            {/* Basit pasta grafik temsili - gerçek SVG yerine */}
            <View style={styles.pieCenter}>
              <Text style={[styles.totalLabel, { color: currentTheme.TEXT_SECONDARY }]}>
                Toplam
              </Text>
              <Text style={[styles.totalAmount, { color: currentTheme.TEXT_PRIMARY }]}>
                {currencySymbol}{pieData.total.toLocaleString('tr-TR')}
              </Text>
            </View>
          </View>
        </View>

        {/* Kategori legend */}
        <ScrollView style={styles.legendContainer} showsVerticalScrollIndicator={false}>
          {pieData.slices.map((slice, index) => {
            const status = getCategoryStatus(slice);
            const isSelected = selectedCategory?.id === slice.id;

            return (
              <TouchableOpacity
                key={slice.id}
                style={[
                  styles.legendItem,
                  {
                    backgroundColor: isSelected 
                      ? currentTheme.PRIMARY + '10' 
                      : 'transparent',
                    borderColor: isSelected 
                      ? currentTheme.PRIMARY 
                      : 'transparent',
                  }
                ]}
                onPress={() => handleCategoryPress(slice)}
                activeOpacity={0.7}
              >
                <View style={styles.legendLeft}>
                  <View style={[styles.colorDot, { backgroundColor: slice.color }]} />
                  <MaterialIcons 
                    name={slice.icon || 'category'} 
                    size={20} 
                    color={currentTheme.TEXT_PRIMARY} 
                  />
                  <Text style={[styles.categoryName, { color: currentTheme.TEXT_PRIMARY }]}>
                    {slice.name}
                  </Text>
                </View>

                <View style={styles.legendRight}>
                  <View style={styles.amountInfo}>
                    <Text style={[styles.categoryAmount, { color: currentTheme.TEXT_PRIMARY }]}>
                      {currencySymbol}{slice.amount.toLocaleString('tr-TR')}
                    </Text>
                    <Text style={[styles.categoryPercentage, { color: currentTheme.TEXT_SECONDARY }]}>
                      %{slice.percentage.toFixed(1)}
                    </Text>
                  </View>

                  {showBudgetComparison && slice.budget && (
                    <MaterialIcons 
                      name={status.icon} 
                      size={16} 
                      color={status.color} 
                    />
                  )}
                </View>
              </TouchableOpacity>
            );
          })}
        </ScrollView>
      </View>

      {/* Seçili kategori detayı */}
      {selectedCategory && (
        <View style={[styles.detailSection, { backgroundColor: currentTheme.BACKGROUND }]}>
          <Text style={[styles.detailTitle, { color: currentTheme.TEXT_PRIMARY }]}>
            {selectedCategory.name} Detayı
          </Text>
          
          <View style={styles.detailStats}>
            <View style={styles.detailItem}>
              <Text style={[styles.detailLabel, { color: currentTheme.TEXT_SECONDARY }]}>
                Harcama
              </Text>
              <Text style={[styles.detailValue, { color: currentTheme.TEXT_PRIMARY }]}>
                {formatCurrency(selectedCategory.amount)}
              </Text>
            </View>

            {showBudgetComparison && selectedCategory.budget && (
              <>
                <View style={styles.detailItem}>
                  <Text style={[styles.detailLabel, { color: currentTheme.TEXT_SECONDARY }]}>
                    Bütçe
                  </Text>
                  <Text style={[styles.detailValue, { color: currentTheme.TEXT_PRIMARY }]}>
                    {formatCurrency(selectedCategory.budget)}
                  </Text>
                </View>

                <View style={styles.detailItem}>
                  <Text style={[styles.detailLabel, { color: currentTheme.TEXT_SECONDARY }]}>
                    Kalan
                  </Text>
                  <Text style={[
                    styles.detailValue, 
                    { 
                      color: selectedCategory.budget - selectedCategory.amount >= 0 
                        ? currentTheme.SUCCESS 
                        : currentTheme.ERROR 
                    }
                  ]}>
                    {formatCurrency(Math.abs(selectedCategory.budget - selectedCategory.amount))}
                    {selectedCategory.amount > selectedCategory.budget && ' (aşım)'}
                  </Text>
                </View>
              </>
            )}

            <View style={styles.detailItem}>
              <Text style={[styles.detailLabel, { color: currentTheme.TEXT_SECONDARY }]}>
                Oran
              </Text>
              <Text style={[styles.detailValue, { color: currentTheme.PRIMARY }]}>
                %{selectedCategory.percentage.toFixed(1)}
              </Text>
            </View>
          </View>
        </View>
      )}

      {/* Özet istatistikler */}
      <View style={styles.summarySection}>
        <Text style={[styles.summaryTitle, { color: currentTheme.TEXT_PRIMARY }]}>
          Özet
        </Text>
        
        <View style={styles.summaryStats}>
          <View style={styles.summaryItem}>
            <Text style={[styles.summaryLabel, { color: currentTheme.TEXT_SECONDARY }]}>
              En Yüksek
            </Text>
            <Text style={[styles.summaryValue, { color: currentTheme.PRIMARY }]}>
              {pieData.slices.length > 0 ? pieData.slices[0].name : '-'}
            </Text>
          </View>

          <View style={styles.summaryItem}>
            <Text style={[styles.summaryLabel, { color: currentTheme.TEXT_SECONDARY }]}>
              Kategori Sayısı
            </Text>
            <Text style={[styles.summaryValue, { color: currentTheme.TEXT_PRIMARY }]}>
              {pieData.slices.length}
            </Text>
          </View>

          {showBudgetComparison && (
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryLabel, { color: currentTheme.TEXT_SECONDARY }]}>
                Aşan Kategori
              </Text>
              <Text style={[styles.summaryValue, { color: currentTheme.ERROR }]}>
                {pieData.slices.filter(s => s.budget && s.amount > s.budget).length}
              </Text>
            </View>
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '500',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: 'center',
  },
  chartSection: {
    marginBottom: 16,
  },
  pieChartContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  pieChart: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  pieCenter: {
    alignItems: 'center',
  },
  totalLabel: {
    fontSize: 12,
    marginBottom: 2,
  },
  totalAmount: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  legendContainer: {
    maxHeight: 200,
  },
  legendItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 4,
  },
  legendLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 8,
  },
  colorDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  categoryName: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
  legendRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  amountInfo: {
    alignItems: 'flex-end',
  },
  categoryAmount: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  categoryPercentage: {
    fontSize: 12,
  },
  detailSection: {
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  detailTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  detailStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  detailItem: {
    alignItems: 'center',
  },
  detailLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  summarySection: {
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
    paddingTop: 16,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 12,
    textAlign: 'center',
  },
  summaryStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: 'bold',
  },
});

export default CategoryAnalysisChart;
