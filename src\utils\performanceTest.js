/**
 * Performance Test Utilities
 * Uygulama performansını test etmek için araçlar
 */

import { performanceMonitor, queryOptimizer, memoryManager } from './performanceUtils';

/**
 * Performance Test Suite
 */
export class PerformanceTestSuite {
  constructor() {
    this.results = [];
  }

  /**
   * Database performance test
   * @param {SQLite.SQLiteDatabase} db - Database instance
   * @returns {Promise<Object>} Test results
   */
  async testDatabasePerformance(db) {
    const results = {
      name: 'Database Performance',
      tests: [],
    };

    // Test 1: Simple query performance
    performanceMonitor.start('simple_query');
    await db.getAllAsync('SELECT COUNT(*) FROM transactions');
    const simpleQueryTime = performanceMonitor.end('simple_query');
    results.tests.push({
      name: 'Simple Query',
      duration: simpleQueryTime,
      status: simpleQueryTime < 50 ? 'PASS' : 'FAIL',
    });

    // Test 2: Complex join query performance
    performanceMonitor.start('complex_query');
    await db.getAllAsync(`
      SELECT t.*, c.name as category_name 
      FROM transactions t 
      LEFT JOIN categories c ON t.category_id = c.id 
      ORDER BY t.date DESC 
      LIMIT 100
    `);
    const complexQueryTime = performanceMonitor.end('complex_query');
    results.tests.push({
      name: 'Complex Query',
      duration: complexQueryTime,
      status: complexQueryTime < 100 ? 'PASS' : 'FAIL',
    });

    // Test 3: Batch insert performance
    performanceMonitor.start('batch_insert');
    await db.withTransactionAsync(async () => {
      for (let i = 0; i < 100; i++) {
        await db.runAsync(
          'INSERT INTO transactions (type, amount, description, date, category_id) VALUES (?, ?, ?, ?, ?)',
          'expense', Math.random() * 1000, `Test transaction ${i}`, new Date().toISOString(), 1
        );
      }
    });
    const batchInsertTime = performanceMonitor.end('batch_insert');
    results.tests.push({
      name: 'Batch Insert (100 records)',
      duration: batchInsertTime,
      status: batchInsertTime < 500 ? 'PASS' : 'FAIL',
    });

    // Test 4: Index effectiveness
    performanceMonitor.start('indexed_query');
    await db.getAllAsync('SELECT * FROM transactions WHERE date >= ? ORDER BY date DESC', [
      new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
    ]);
    const indexedQueryTime = performanceMonitor.end('indexed_query');
    results.tests.push({
      name: 'Indexed Query',
      duration: indexedQueryTime,
      status: indexedQueryTime < 100 ? 'PASS' : 'FAIL',
    });

    return results;
  }

  /**
   * Memory performance test
   * @returns {Promise<Object>} Test results
   */
  async testMemoryPerformance() {
    const results = {
      name: 'Memory Performance',
      tests: [],
    };

    // Test 1: Memory allocation
    performanceMonitor.start('memory_allocation');
    const largeArray = new Array(10000).fill(0).map((_, i) => ({
      id: i,
      data: `Test data ${i}`,
      timestamp: Date.now(),
    }));
    const allocationTime = performanceMonitor.end('memory_allocation');
    results.tests.push({
      name: 'Memory Allocation (10k objects)',
      duration: allocationTime,
      status: allocationTime < 100 ? 'PASS' : 'FAIL',
    });

    // Test 2: Memory cleanup
    performanceMonitor.start('memory_cleanup');
    largeArray.length = 0;
    memoryManager.forceGC();
    const cleanupTime = performanceMonitor.end('memory_cleanup');
    results.tests.push({
      name: 'Memory Cleanup',
      duration: cleanupTime,
      status: cleanupTime < 50 ? 'PASS' : 'FAIL',
    });

    return results;
  }

  /**
   * Component render performance test
   * @param {Function} renderFunction - Component render function
   * @returns {Promise<Object>} Test results
   */
  async testRenderPerformance(renderFunction) {
    const results = {
      name: 'Render Performance',
      tests: [],
    };

    // Test 1: Initial render
    performanceMonitor.start('initial_render');
    await renderFunction();
    const initialRenderTime = performanceMonitor.end('initial_render');
    results.tests.push({
      name: 'Initial Render',
      duration: initialRenderTime,
      status: initialRenderTime < 16 ? 'PASS' : 'WARN', // 60fps = 16ms per frame
    });

    // Test 2: Re-render performance
    performanceMonitor.start('re_render');
    for (let i = 0; i < 10; i++) {
      await renderFunction();
    }
    const reRenderTime = performanceMonitor.end('re_render') / 10;
    results.tests.push({
      name: 'Average Re-render',
      duration: reRenderTime,
      status: reRenderTime < 16 ? 'PASS' : 'WARN',
    });

    return results;
  }

  /**
   * Navigation performance test
   * @param {Object} navigation - Navigation object
   * @returns {Promise<Object>} Test results
   */
  async testNavigationPerformance(navigation) {
    const results = {
      name: 'Navigation Performance',
      tests: [],
    };

    // Test 1: Screen transition
    performanceMonitor.start('screen_transition');
    navigation.navigate('TestScreen');
    // Simulate transition time
    await new Promise(resolve => setTimeout(resolve, 100));
    const transitionTime = performanceMonitor.end('screen_transition');
    results.tests.push({
      name: 'Screen Transition',
      duration: transitionTime,
      status: transitionTime < 300 ? 'PASS' : 'FAIL',
    });

    return results;
  }

  /**
   * Run all performance tests
   * @param {Object} testContext - Test context with db, navigation, etc.
   * @returns {Promise<Object>} Complete test results
   */
  async runAllTests(testContext = {}) {
    const allResults = {
      timestamp: new Date().toISOString(),
      results: [],
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
        warnings: 0,
      },
    };

    try {
      // Database tests
      if (testContext.db) {
        const dbResults = await this.testDatabasePerformance(testContext.db);
        allResults.results.push(dbResults);
      }

      // Memory tests
      const memoryResults = await this.testMemoryPerformance();
      allResults.results.push(memoryResults);

      // Component render tests
      if (testContext.renderFunction) {
        const renderResults = await this.testRenderPerformance(testContext.renderFunction);
        allResults.results.push(renderResults);
      }

      // Navigation tests
      if (testContext.navigation) {
        const navResults = await this.testNavigationPerformance(testContext.navigation);
        allResults.results.push(navResults);
      }

      // Calculate summary
      allResults.results.forEach(result => {
        result.tests.forEach(test => {
          allResults.summary.total++;
          switch (test.status) {
            case 'PASS':
              allResults.summary.passed++;
              break;
            case 'FAIL':
              allResults.summary.failed++;
              break;
            case 'WARN':
              allResults.summary.warnings++;
              break;
          }
        });
      });

    } catch (error) {
      console.error('Performance test error:', error);
      allResults.error = error.message;
    }

    return allResults;
  }

  /**
   * Generate performance report
   * @param {Object} results - Test results
   * @returns {string} Formatted report
   */
  generateReport(results) {
    let report = `
# Performance Test Report
Generated: ${results.timestamp}

## Summary
- Total Tests: ${results.summary.total}
- Passed: ${results.summary.passed}
- Failed: ${results.summary.failed}
- Warnings: ${results.summary.warnings}

## Detailed Results
`;

    results.results.forEach(category => {
      report += `\n### ${category.name}\n`;
      category.tests.forEach(test => {
        const status = test.status === 'PASS' ? '✅' : test.status === 'WARN' ? '⚠️' : '❌';
        report += `${status} ${test.name}: ${test.duration}ms\n`;
      });
    });

    if (results.error) {
      report += `\n## Errors\n${results.error}\n`;
    }

    return report;
  }
}

// Global performance test instance
export const performanceTest = new PerformanceTestSuite();

export default {
  PerformanceTestSuite,
  performanceTest,
};
