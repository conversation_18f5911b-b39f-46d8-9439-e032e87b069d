/**
 * Bütçe hedefleri için servis fonksiyonları
 */
import { format, parseISO } from 'date-fns';
import * as exchangeRateService from './exchangeRateService';

/**
 * Tüm bütçe hedeflerini getirir
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {boolean} activeOnly - Sad<PERSON>e tamamlanmamış hedefleri getir
 * @returns {Promise<Array>} Bütçe hedefleri listesi
 */
export const getAllBudgetGoals = async (db, activeOnly = false) => {
  try {
    let query = `
      SELECT bg.*, c.name as category_name, c.color as category_color, c.icon as category_icon
      FROM budget_goals bg
      LEFT JOIN categories c ON bg.category_id = c.id
    `;
    
    if (activeOnly) {
      query += ` WHERE bg.is_completed = 0`;
    }
    
    query += ` ORDER BY bg.target_date ASC`;
    
    const goals = await db.getAllAsync(query);
    
    // Her hedef için ilerleme hesapla
    return goals.map(goal => {
      const progress = goal.target_amount > 0 
        ? Math.min((goal.current_amount / goal.target_amount) * 100, 100) 
        : 0;
      
      return {
        ...goal,
        progress
      };
    });
  } catch (error) {
    console.error('Bütçe hedeflerini getirme hatası:', error);
    throw error;
  }
};

/**
 * Belirli bir bütçe hedefini getirir
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} goalId - Hedef ID
 * @returns {Promise<Object|null>} Bütçe hedefi
 */
export const getBudgetGoalById = async (db, goalId) => {
  try {
    const goal = await db.getFirstAsync(`
      SELECT bg.*, c.name as category_name, c.color as category_color, c.icon as category_icon
      FROM budget_goals bg
      LEFT JOIN categories c ON bg.category_id = c.id
      WHERE bg.id = ?
    `, [goalId]);
    
    if (!goal) return null;
    
    // İlerleme hesapla
    const progress = goal.target_amount > 0 
      ? Math.min((goal.current_amount / goal.target_amount) * 100, 100) 
      : 0;
    
    // Kalan tutar hesapla
    const remaining = goal.target_amount - goal.current_amount;
    
    return {
      ...goal,
      progress,
      remaining
    };
  } catch (error) {
    console.error('Bütçe hedefi getirme hatası:', error);
    throw error;
  }
};

/**
 * Yeni bir bütçe hedefi ekler
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} goal - Hedef verileri
 * @param {string} goal.name - Hedef adı
 * @param {string} goal.description - Hedef açıklaması
 * @param {number} goal.target_amount - Hedef tutar
 * @param {number} goal.current_amount - Mevcut tutar
 * @param {string} goal.start_date - Başlangıç tarihi
 * @param {string} goal.target_date - Hedef tarihi
 * @param {number} goal.category_id - Kategori ID
 * @param {string} goal.currency - Para birimi
 * @returns {Promise<number>} Eklenen hedefin ID'si
 */
export const addBudgetGoal = async (db, goal) => {
  try {
    const result = await db.runAsync(`
      INSERT INTO budget_goals (
        name, description, target_amount, current_amount,
        start_date, target_date, category_id, currency
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      goal.name,
      goal.description || '',
      goal.target_amount,
      goal.current_amount || 0,
      goal.start_date,
      goal.target_date,
      goal.category_id || null,
      goal.currency || 'TRY'
    ]);
    
    return result.lastInsertRowId;
  } catch (error) {
    console.error('Bütçe hedefi ekleme hatası:', error);
    throw error;
  }
};

/**
 * Bir bütçe hedefini günceller
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} goalId - Hedef ID
 * @param {Object} goal - Güncellenecek hedef verileri
 * @returns {Promise<boolean>} Başarılı ise true
 */
export const updateBudgetGoal = async (db, goalId, goal) => {
  try {
    await db.runAsync(`
      UPDATE budget_goals
      SET name = ?, description = ?, target_amount = ?, current_amount = ?,
          start_date = ?, target_date = ?, category_id = ?, currency = ?,
          is_completed = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [
      goal.name,
      goal.description || '',
      goal.target_amount,
      goal.current_amount !== undefined ? goal.current_amount : 0,
      goal.start_date,
      goal.target_date,
      goal.category_id || null,
      goal.currency || 'TRY',
      goal.is_completed !== undefined ? goal.is_completed : 0,
      goalId
    ]);
    
    return true;
  } catch (error) {
    console.error('Bütçe hedefi güncelleme hatası:', error);
    throw error;
  }
};

/**
 * Bir bütçe hedefini siler
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} goalId - Hedef ID
 * @returns {Promise<boolean>} Başarılı ise true
 */
export const deleteBudgetGoal = async (db, goalId) => {
  try {
    await db.runAsync(`
      DELETE FROM budget_goals
      WHERE id = ?
    `, [goalId]);
    
    return true;
  } catch (error) {
    console.error('Bütçe hedefi silme hatası:', error);
    throw error;
  }
};

/**
 * Bir bütçe hedefine para ekler
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} goalId - Hedef ID
 * @param {number} amount - Eklenecek tutar
 * @param {string} currency - Para birimi
 * @returns {Promise<Object>} Güncellenmiş hedef
 */
export const addFundsToBudgetGoal = async (db, goalId, amount, currency = 'TRY') => {
  try {
    // Hedefi getir
    const goal = await getBudgetGoalById(db, goalId);
    
    if (!goal) {
      throw new Error('Hedef bulunamadı');
    }
    
    // Para birimi dönüşümü
    let convertedAmount = amount;
    if (currency !== goal.currency) {
      convertedAmount = await exchangeRateService.convertCurrency(
        db, amount, currency, goal.currency, new Date()
      );
    }
    
    // Yeni mevcut tutarı hesapla
    const newCurrentAmount = goal.current_amount + convertedAmount;
    
    // Hedef tamamlandı mı kontrol et
    const isCompleted = newCurrentAmount >= goal.target_amount ? 1 : 0;
    
    // Hedefi güncelle
    await db.runAsync(`
      UPDATE budget_goals
      SET current_amount = ?, is_completed = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [newCurrentAmount, isCompleted, goalId]);
    
    // Güncellenmiş hedefi getir
    return await getBudgetGoalById(db, goalId);
  } catch (error) {
    console.error('Bütçe hedefine para ekleme hatası:', error);
    throw error;
  }
};

/**
 * Bir bütçe hedefinden para çeker
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} goalId - Hedef ID
 * @param {number} amount - Çekilecek tutar
 * @param {string} currency - Para birimi
 * @returns {Promise<Object>} Güncellenmiş hedef
 */
export const withdrawFundsFromBudgetGoal = async (db, goalId, amount, currency = 'TRY') => {
  try {
    // Hedefi getir
    const goal = await getBudgetGoalById(db, goalId);
    
    if (!goal) {
      throw new Error('Hedef bulunamadı');
    }
    
    // Para birimi dönüşümü
    let convertedAmount = amount;
    if (currency !== goal.currency) {
      convertedAmount = await exchangeRateService.convertCurrency(
        db, amount, currency, goal.currency, new Date()
      );
    }
    
    // Yeni mevcut tutarı hesapla
    const newCurrentAmount = Math.max(goal.current_amount - convertedAmount, 0);
    
    // Hedef tamamlandı mı kontrol et
    const isCompleted = goal.is_completed;
    
    // Hedefi güncelle
    await db.runAsync(`
      UPDATE budget_goals
      SET current_amount = ?, is_completed = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [newCurrentAmount, isCompleted, goalId]);
    
    // Güncellenmiş hedefi getir
    return await getBudgetGoalById(db, goalId);
  } catch (error) {
    console.error('Bütçe hedefinden para çekme hatası:', error);
    throw error;
  }
};

/**
 * Bir bütçe hedefini tamamlandı olarak işaretler
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} goalId - Hedef ID
 * @returns {Promise<Object>} Güncellenmiş hedef
 */
export const markBudgetGoalAsCompleted = async (db, goalId) => {
  try {
    await db.runAsync(`
      UPDATE budget_goals
      SET is_completed = 1, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [goalId]);
    
    return await getBudgetGoalById(db, goalId);
  } catch (error) {
    console.error('Bütçe hedefini tamamlandı olarak işaretleme hatası:', error);
    throw error;
  }
};

/**
 * Bir bütçe hedefini tamamlanmadı olarak işaretler
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} goalId - Hedef ID
 * @returns {Promise<Object>} Güncellenmiş hedef
 */
export const markBudgetGoalAsNotCompleted = async (db, goalId) => {
  try {
    await db.runAsync(`
      UPDATE budget_goals
      SET is_completed = 0, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [goalId]);
    
    return await getBudgetGoalById(db, goalId);
  } catch (error) {
    console.error('Bütçe hedefini tamamlanmadı olarak işaretleme hatası:', error);
    throw error;
  }
};
