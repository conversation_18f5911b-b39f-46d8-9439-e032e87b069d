/**
 * Template Service
 * Manages report templates and template-related operations
 * Follows REPORTS_SCREEN_REDESIGN_PLAN.md specifications
 */

/**
 * Get all available templates
 */
export const getAllTemplates = async () => {
  try {
    // Mock data for now - will be replaced with actual database queries
    return [
      {
        id: 'monthly_income_expense',
        name: '<PERSON><PERSON><PERSON><PERSON>r-Gider Raporu',
        description: 'Detaylı aylık finansal durum analizi',
        icon: '📊',
        category: 'Finansal',
        isSystem: true,
        isInteractive: true,
        thumbnail: null,
        tags: ['gelir', 'gider', 'aylık', 'finansal'],
        config: {
          dataSource: ['transactions', 'salary_payments'],
          defaultFilters: {
            period: 'current_month',
          },
          columns: ['date', 'category', 'description', 'amount', 'type'],
          charts: ['line', 'pie'],
        },
      },
      {
        id: 'category_distribution',
        name: '<PERSON><PERSON><PERSON>',
        description: '<PERSON><PERSON>ma kategorilerinin detaylı analizi',
        icon: '📈',
        category: 'Analiz',
        isSystem: true,
        isInteractive: true,
        thumbnail: null,
        tags: ['kategori', 'dağılım', 'analiz'],
        config: {
          dataSource: ['transactions'],
          defaultFilters: {
            type: 'expense',
            period: 'current_month',
          },
          groupBy: 'category',
          charts: ['pie', 'bar'],
        },
      },
      {
        id: 'cash_flow',
        name: 'Nakit Akış Raporu',
        description: 'Aylık nakit giriş ve çıkışlarının analizi',
        icon: '💸',
        category: 'Nakit Akış',
        isSystem: true,
        isInteractive: true,
        thumbnail: null,
        tags: ['nakit', 'akış', 'gelir', 'gider'],
        config: {
          dataSource: ['transactions', 'salary_payments'],
          defaultFilters: {
            period: 'current_month',
          },
          groupBy: 'date',
          charts: ['waterfall', 'line'],
        },
      },
      {
        id: 'budget_vs_actual',
        name: 'Bütçe vs Gerçekleşen',
        description: 'Planlanan bütçe ile gerçekleşen harcamaların karşılaştırması',
        icon: '🎯',
        category: 'Bütçe',
        isSystem: true,
        isInteractive: true,
        thumbnail: null,
        tags: ['bütçe', 'karşılaştırma', 'hedef'],
        config: {
          dataSource: ['budgets', 'transactions'],
          defaultFilters: {
            period: 'current_month',
          },
          comparison: true,
          charts: ['bar', 'gauge'],
        },
      },
      {
        id: 'shift_income',
        name: 'Mesai Gelir Analizi',
        description: 'Mesai saatleri ve gelir analizi',
        icon: '⏰',
        category: 'Mesai',
        isSystem: true,
        isInteractive: true,
        thumbnail: null,
        tags: ['mesai', 'gelir', 'saat'],
        config: {
          dataSource: ['work_shifts', 'overtime'],
          defaultFilters: {
            period: 'current_month',
          },
          groupBy: 'date',
          charts: ['line', 'bar'],
        },
      },
      {
        id: 'regular_income_tracking',
        name: 'Düzenli Gelir Takibi',
        description: 'Maaş ve düzenli gelirlerin takibi',
        icon: '💰',
        category: 'Gelir',
        isSystem: true,
        isInteractive: true,
        thumbnail: null,
        tags: ['maaş', 'düzenli', 'gelir'],
        config: {
          dataSource: ['salary_payments', 'regular_incomes'],
          defaultFilters: {
            period: 'current_year',
          },
          groupBy: 'month',
          charts: ['line', 'bar'],
        },
      },
      {
        id: 'summary_overview',
        name: 'Temel Özet Raporu',
        description: 'Genel finansal durumun özeti',
        icon: '📋',
        category: 'Özet',
        isSystem: true,
        isInteractive: true,
        thumbnail: null,
        tags: ['özet', 'genel', 'durum'],
        config: {
          dataSource: ['transactions', 'salary_payments', 'budgets'],
          defaultFilters: {
            period: 'current_month',
          },
          summary: true,
          charts: ['kpi', 'pie'],
        },
      },
    ];
  } catch (error) {
    console.error('Error getting templates:', error);
    return [];
  }
};

/**
 * Get template by ID
 */
export const getTemplateById = async (templateId) => {
  try {
    const templates = await getAllTemplates();
    return templates.find(template => template.id === templateId) || null;
  } catch (error) {
    console.error('Error getting template by ID:', error);
    return null;
  }
};

/**
 * Get templates by category
 */
export const getTemplatesByCategory = async (category) => {
  try {
    const templates = await getAllTemplates();
    if (category === 'all') {
      return templates;
    }
    return templates.filter(template => template.category === category);
  } catch (error) {
    console.error('Error getting templates by category:', error);
    return [];
  }
};

/**
 * Get quick templates (most used/recommended)
 */
export const getQuickTemplates = async () => {
  try {
    const allTemplates = await getAllTemplates();
    // Return first 4 templates as quick templates
    return allTemplates.slice(0, 4);
  } catch (error) {
    console.error('Error getting quick templates:', error);
    return [];
  }
};

/**
 * Search templates
 */
export const searchTemplates = async (query) => {
  try {
    const templates = await getAllTemplates();
    const lowercaseQuery = query.toLowerCase();
    
    return templates.filter(template => 
      template.name.toLowerCase().includes(lowercaseQuery) ||
      template.description.toLowerCase().includes(lowercaseQuery) ||
      template.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
    );
  } catch (error) {
    console.error('Error searching templates:', error);
    return [];
  }
};

/**
 * Get template categories
 */
export const getTemplateCategories = async () => {
  try {
    const templates = await getAllTemplates();
    
    // Count templates by category
    const categoryCounts = templates.reduce((acc, template) => {
      const category = template.category || 'Diğer';
      acc[category] = (acc[category] || 0) + 1;
      return acc;
    }, {});

    // Create category objects
    const categories = Object.entries(categoryCounts).map(([name, count]) => ({
      id: name.toLowerCase().replace(/\s+/g, '-'),
      name,
      count,
      icon: getCategoryIcon(name),
    }));

    return categories;
  } catch (error) {
    console.error('Error getting template categories:', error);
    return [];
  }
};

/**
 * Get category icon
 */
const getCategoryIcon = (categoryName) => {
  const iconMap = {
    'Finansal': '💰',
    'Analiz': '📈',
    'Bütçe': '🎯',
    'Nakit Akış': '💸',
    'Mesai': '⏰',
    'Gelir': '💵',
    'Özet': '📋',
    'Diğer': '📊',
  };
  
  return iconMap[categoryName] || '📊';
};

/**
 * Create custom template
 */
export const createCustomTemplate = async (templateData) => {
  try {
    const newTemplate = {
      id: `custom_${Date.now()}`,
      ...templateData,
      isSystem: false,
      isInteractive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    console.log('Custom template created:', newTemplate);
    return newTemplate;
  } catch (error) {
    console.error('Error creating custom template:', error);
    throw error;
  }
};

/**
 * Update template
 */
export const updateTemplate = async (templateId, updateData) => {
  try {
    const updatedTemplate = {
      id: templateId,
      ...updateData,
      updatedAt: new Date().toISOString(),
    };
    
    console.log('Template updated:', updatedTemplate);
    return updatedTemplate;
  } catch (error) {
    console.error('Error updating template:', error);
    throw error;
  }
};

/**
 * Delete template
 */
export const deleteTemplate = async (templateId) => {
  try {
    // Only allow deletion of custom templates
    const template = await getTemplateById(templateId);
    if (template && template.isSystem) {
      throw new Error('System templates cannot be deleted');
    }
    
    console.log('Template deleted:', templateId);
    return true;
  } catch (error) {
    console.error('Error deleting template:', error);
    throw error;
  }
};

export const templateService = {
  getAllTemplates,
  getTemplateById,
  getTemplatesByCategory,
  getQuickTemplates,
  searchTemplates,
  getTemplateCategories,
  createCustomTemplate,
  updateTemplate,
  deleteTemplate,
};
