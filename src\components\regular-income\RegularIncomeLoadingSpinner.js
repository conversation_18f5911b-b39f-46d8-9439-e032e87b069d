import React from 'react';
import { View, ActivityIndicator, Text, StyleSheet } from 'react-native';
import { Colors } from '../../constants/colors';

/**
 * Enhanced loading spinner with modern Material Design 3.0 styling
 * 
 * @param {Object} props - Bile<PERSON>en ö<PERSON>leri 
 * @param {string} props.text - Gösterilecek metin
 * @param {string} props.color - İndikatör rengi
 * @returns {JSX.Element} Yükleniyor göstergesi
 */
const RegularIncomeLoadingSpinner = React.memo(({ text = 'Yükleniyor...', color = Colors.PRIMARY }) => (
  <View style={styles.container}>
    <ActivityIndicator size="large" color={color} />
    {text && <Text style={[styles.text, { color }]}>{text}</Text>}
  </View>
));

const styles = StyleSheet.create({
  container: {
    alignItems: 'center', 
    padding: 24,
    backgroundColor: '#fff',
    borderRadius: 16,
    margin: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 4,
  },
  text: {
    marginTop: 12, 
    fontSize: 16, 
    fontWeight: '500',
    letterSpacing: 0.25,
  }
});

export default RegularIncomeLoadingSpinner;
