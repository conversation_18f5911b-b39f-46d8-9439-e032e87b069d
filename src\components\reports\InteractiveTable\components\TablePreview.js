import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../../../context/ThemeContext';
import { formatCurrency } from '../../../../utils/reportUtils';

/**
 * Tablo önizleme bileşeni - yapılandırılmış tablonun önizlemesini gösterir
 */
const TablePreview = ({ 
  data = [], 
  columns = [], 
  filters = [], 
  sorting = { column: null, direction: 'asc' },
  onSort,
  pageSize = 10,
  currentPage = 1,
  onPageChange,
  loading = false,
  isVisible = true,
  onToggleVisibility
}) => {
  const { theme } = useTheme();
  const [filteredData, setFilteredData] = useState([]);
  const [sortedData, setSortedData] = useState([]);
  const [paginatedData, setPaginatedData] = useState([]);
  const [totalPages, setTotalPages] = useState(0);

  // Tema güvenlik kontrolü
  if (!theme) {
    return null;
  }

  const styles = {
    container: {
      flex: 1,
      backgroundColor: theme.BACKGROUND || theme.colors?.background || '#fff',
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: 16,
      backgroundColor: theme.SURFACE || theme.colors?.surface || '#f9f9f9',
      borderBottomWidth: 1,
      borderBottomColor: theme.BORDER || theme.colors?.border || '#e0e0e0',
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.TEXT_PRIMARY || theme.colors?.text || '#333',
    },
    headerSubtitle: {
      fontSize: 12,
      color: theme.TEXT_SECONDARY || theme.colors?.textSecondary || '#666',
    },
    toolbar: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 12,
      backgroundColor: theme.SURFACE || theme.colors?.surface || '#f9f9f9',
    },
    toolbarButton: {
      backgroundColor: theme.SURFACE_VARIANT || theme.colors?.surface || '#f9f9f9',
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      marginRight: 8,
      borderBottomWidth: 1,
      borderBottomColor: theme.BORDER || theme.colors?.border || '#e0e0e0',
    },
    toolbarButtonText: {
      fontSize: 12,
      color: theme.TEXT_PRIMARY || theme.colors?.text || '#333',
    },
    tableContainer: {
      flex: 1,
    },
    tableHeaderContainer: {
      backgroundColor: theme.PRIMARY || theme.colors?.primary || '#007AFF',
    },
    tableHeader: {
      flexDirection: 'row',
      backgroundColor: theme.PRIMARY || theme.colors?.primary || '#007AFF',
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: theme.BORDER || theme.colors?.border || '#e0e0e0',
    },
    tableHeaderCell: {
      flex: 1,
      paddingHorizontal: 8,
      minWidth: 100,
    },
    tableHeaderCellOdd: {
      backgroundColor: theme.BACKGROUND || theme.colors?.background || '#fff',
    },
    tableHeaderCellEven: {
      backgroundColor: theme.SURFACE || theme.colors?.surface || '#f9f9f9',
    },
    tableHeaderText: {
      fontSize: 12,
      fontWeight: '600',
      color: theme.SURFACE || theme.colors?.surface || '#f9f9f9',
      textAlign: 'center',
    },
    sortableHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    tableRow: {
      flexDirection: 'row',
      paddingVertical: 8,
      borderBottomWidth: 1,
      borderBottomColor: theme.BORDER || theme.colors?.border || '#e0e0e0',
    },
    tableRowOdd: {
      backgroundColor: theme.BACKGROUND || theme.colors?.background || '#fff',
    },
    tableRowEven: {
      backgroundColor: theme.SURFACE || theme.colors?.surface || '#f9f9f9',
    },
    tableCell: {
      flex: 1,
      paddingHorizontal: 8,
      minWidth: 100,
      justifyContent: 'center',
    },
    tableCellText: {
      fontSize: 12,
      color: theme.TEXT_PRIMARY || theme.colors?.text || '#333',
      textAlign: 'center',
    },
    tableCellNumber: {
      textAlign: 'right',
    },
    tableCellCurrency: {
      textAlign: 'right',
      fontWeight: '500',
    },
    emptyState: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      padding: 40,
    },
    emptyStateText: {
      fontSize: 16,
      color: theme.TEXT_SECONDARY || theme.colors?.textSecondary || '#666',
      textAlign: 'center',
      marginTop: 16,
    },
    loadingState: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      padding: 40,
    },
    loadingText: {
      fontSize: 16,
      color: theme.TEXT_SECONDARY || theme.colors?.textSecondary || '#666',
      textAlign: 'center',
      marginTop: 16,
    },
    pagination: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 16,
      backgroundColor: theme.SURFACE || theme.colors?.surface || '#f9f9f9',
      borderTopWidth: 1,
      borderTopColor: theme.BORDER || theme.colors?.border || '#e0e0e0',
    },
    paginationInfo: {
      fontSize: 12,
      color: theme.TEXT_SECONDARY || theme.colors?.textSecondary || '#666',
    },
    paginationButtons: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    paginationButton: {
      padding: 8,
      marginHorizontal: 4,
      backgroundColor: theme.SURFACE_VARIANT || theme.colors?.surface || '#f9f9f9',
      borderRadius: 4,
    },
    paginationButtonDisabled: {
      opacity: 0.5,
    },
    paginationButtonText: {
      fontSize: 12,
      color: theme.TEXT_PRIMARY || theme.colors?.text || '#333',
      fontWeight: '500',
    },
    statsContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 12,
      backgroundColor: theme.SURFACE_VARIANT || theme.colors?.surface || '#f9f9f9',
      borderTopWidth: 1,
      borderTopColor: theme.BORDER || theme.colors?.border || '#e0e0e0',
    },
    statsText: {
      fontSize: 12,
      color: theme.TEXT_PRIMARY || theme.colors?.text || '#333',
      marginRight: 16,
    },
    statsSubtext: {
      fontSize: 10,
      color: theme.TEXT_SECONDARY || theme.colors?.textSecondary || '#666',
    },
  };

  useEffect(() => {
    applyFilters();
  }, [data, filters]);

  useEffect(() => {
    applySorting();
  }, [filteredData, sorting]);

  useEffect(() => {
    applyPagination();
  }, [sortedData, currentPage, pageSize]);

  /**
   * Filtre uygulama
   */
  const applyFilters = () => {
    let filtered = [...data];
    
    filters.forEach(filter => {
      filtered = filtered.filter(item => {
        const value = item[filter.column];
        const filterValue = filter.value;
        
        switch (filter.operator) {
          case 'equals':
            return value == filterValue;
          case 'not_equals':
            return value != filterValue;
          case 'contains':
            return String(value).toLowerCase().includes(String(filterValue).toLowerCase());
          case 'not_contains':
            return !String(value).toLowerCase().includes(String(filterValue).toLowerCase());
          case 'starts_with':
            return String(value).toLowerCase().startsWith(String(filterValue).toLowerCase());
          case 'ends_with':
            return String(value).toLowerCase().endsWith(String(filterValue).toLowerCase());
          case 'greater_than':
            return Number(value) > Number(filterValue);
          case 'less_than':
            return Number(value) < Number(filterValue);
          case 'greater_equal':
            return Number(value) >= Number(filterValue);
          case 'less_equal':
            return Number(value) <= Number(filterValue);
          default:
            return true;
        }
      });
    });
    
    setFilteredData(filtered);
  };

  /**
   * Sıralama uygulama
   */
  const applySorting = () => {
    if (!sorting.column) {
      setSortedData(filteredData);
      return;
    }

    const sorted = [...filteredData].sort((a, b) => {
      const aValue = a[sorting.column];
      const bValue = b[sorting.column];
      
      let comparison = 0;
      if (aValue > bValue) comparison = 1;
      if (aValue < bValue) comparison = -1;
      
      return sorting.direction === 'desc' ? comparison * -1 : comparison;
    });
    
    setSortedData(sorted);
  };

  /**
   * Sayfalama uygulama
   */
  const applyPagination = () => {
    const total = Math.ceil(sortedData.length / pageSize);
    setTotalPages(total);
    
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginated = sortedData.slice(startIndex, endIndex);
    
    setPaginatedData(paginated);
  };

  /**
   * Sıralama değiştirme
   */
  const handleSort = (column) => {
    if (!onSort) return;
    
    let direction = 'asc';
    if (sorting.column === column && sorting.direction === 'asc') {
      direction = 'desc';
    }
    
    onSort({ column, direction });
  };

  /**
   * Hücre değeri formatı
   */
  const formatCellValue = (value, column) => {
    if (value === null || value === undefined) return '-';
    
    switch (column.type) {
      case 'currency':
        return formatCurrency ? formatCurrency(value) : `₺${Number(value).toFixed(2)}`;
      case 'number':
        return Number(value).toLocaleString('tr-TR');
      case 'date':
        return new Date(value).toLocaleDateString('tr-TR');
      case 'percentage':
        return `%${Number(value).toFixed(1)}`;
      default:
        return String(value);
    }
  };

  /**
   * Tablo başlığı render
   */
  const renderTableHeader = () => (
    <View style={styles.tableHeaderContainer}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <View style={styles.tableHeader}>
          {columns.filter(col => col.visible !== false).map((column, index) => (
            <TouchableOpacity
              key={column.id}
              style={[
                styles.tableHeaderCell,
                index % 2 === 0 ? styles.tableHeaderCellEven : styles.tableHeaderCellOdd
              ]}
              onPress={() => handleSort(column.id)}
            >
              <View style={styles.sortableHeader}>
                <Text style={styles.tableHeaderText}>{column.name}</Text>
                {sorting.column === column.id && (
                  <Ionicons
                    name={sorting.direction === 'asc' ? 'arrow-up' : 'arrow-down'}
                    size={12}
                    color={theme.SURFACE || theme.colors?.surface || '#f9f9f9'}
                    style={{ marginLeft: 4 }}
                  />
                )}
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </View>
  );

  /**
   * Tablo satırı render
   */
  const renderTableRow = (item, index) => (
    <View
      key={index}
      style={[
        styles.tableRow,
        index % 2 === 0 ? styles.tableRowEven : styles.tableRowOdd
      ]}
    >
      {columns.filter(col => col.visible !== false).map((column) => (
        <View key={column.id} style={styles.tableCell}>
          <Text
            style={[
              styles.tableCellText,
              column.type === 'number' && styles.tableCellNumber,
              column.type === 'currency' && styles.tableCellCurrency,
            ]}
          >
            {formatCellValue(item[column.id], column)}
          </Text>
        </View>
      ))}
    </View>
  );

  if (loading) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <View>
            <Text style={styles.headerTitle}>📊 Tablo Önizlemesi</Text>
            <Text style={styles.headerSubtitle}>Veriler yükleniyor...</Text>
          </View>
          {onToggleVisibility && (
            <TouchableOpacity onPress={onToggleVisibility}>
              <Text style={styles.headerSubtitle}>{isVisible ? '▲' : '▼'}</Text>
            </TouchableOpacity>
          )}
        </View>
        <View style={styles.loadingState}>
          <Ionicons name="hourglass" size={48} color={theme.TEXT_SECONDARY || theme.colors?.textSecondary || '#666'} />
          <Text style={styles.loadingText}>Tablo verileri yükleniyor...</Text>
        </View>
      </View>
    );
  }

  if (!isVisible) {
    return (
      <View style={styles.header}>
        <View>
          <Text style={styles.headerTitle}>📊 Tablo Önizlemesi</Text>
          <Text style={styles.headerSubtitle}>{sortedData.length} kayıt</Text>
        </View>
        {onToggleVisibility && (
          <TouchableOpacity onPress={onToggleVisibility}>
            <Text style={styles.headerSubtitle}>▼</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View>
          <Text style={styles.headerTitle}>📊 Tablo Önizlemesi</Text>
          <Text style={styles.headerSubtitle}>{sortedData.length} kayıt</Text>
        </View>
        {onToggleVisibility && (
          <TouchableOpacity onPress={onToggleVisibility}>
            <Text style={styles.headerSubtitle}>▲</Text>
          </TouchableOpacity>
        )}
      </View>
      
      {paginatedData.length > 0 ? (
        <>
          <View style={styles.tableContainer}>
            {renderTableHeader()}
            <ScrollView style={{ flex: 1 }}>
              {paginatedData.map((item, index) => renderTableRow(item, index))}
            </ScrollView>
          </View>
          
          {totalPages > 1 && (
            <View style={styles.pagination}>
              <Text style={styles.paginationInfo}>
                Sayfa {currentPage} / {totalPages} ({sortedData.length} kayıt)
              </Text>
              <View style={styles.paginationButtons}>
                <TouchableOpacity
                  style={[
                    styles.paginationButton,
                    currentPage === 1 && styles.paginationButtonDisabled
                  ]}
                  onPress={() => onPageChange && onPageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  <Ionicons name="chevron-back" size={16} color={theme.TEXT_PRIMARY || theme.colors?.text || '#333'} />
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.paginationButton,
                    currentPage === totalPages && styles.paginationButtonDisabled
                  ]}
                  onPress={() => onPageChange && onPageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  <Ionicons name="chevron-forward" size={16} color={theme.TEXT_PRIMARY || theme.colors?.text || '#333'} />
                </TouchableOpacity>
              </View>
            </View>
          )}
        </>
      ) : (
        <View style={styles.emptyState}>
          <Ionicons name="document-outline" size={48} color={theme.TEXT_SECONDARY || theme.colors?.textSecondary || '#666'} />
          <Text style={styles.emptyStateText}>
            {filters.length > 0 
              ? 'Filtre kriterlerinize uygun veri bulunamadı'
              : 'Gösterilecek veri yok'
            }
          </Text>
        </View>
      )}
    </View>
  );
};

export default TablePreview;
