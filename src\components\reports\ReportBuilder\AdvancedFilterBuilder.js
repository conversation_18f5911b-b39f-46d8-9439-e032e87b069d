/**
 * Advanced Filter Builder - Gelişmiş Filtreleme Bileşeni
 * Raporlar için dinamik ve detaylı filtreleme seçenekleri
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Modal,
  Switch,
  Alert,
  Dimensions,
} from 'react-native';
import { useTheme } from '../../../context/ThemeContext';
import DateTimePicker from '@react-native-community/datetimepicker';

const { width: screenWidth } = Dimensions.get('window');

/**
 * Gelişmiş Filtre Builder Bileşeni
 * @param {Object} props - Bileşen props'ları
 * @param {boolean} props.visible - Modal görünürlüğü
 * @param {Object} props.currentFilters - Mevcut filtreler
 * @param {Function} props.onFiltersApply - Filtreleri uygula callback
 * @param {Function} props.onClose - Modal kapatma callback
 * @param {Array} props.availableCategories - Kullanılabilir kategoriler
 * @param {Object} props.dataRange - Veri aralığı bilgisi
 */
const AdvancedFilterBuilder = ({
  visible,
  currentFilters = {},
  onFiltersApply,
  onClose,
  availableCategories = [],
  dataRange = {}
}) => {
  const { theme } = useTheme();

  // Filter state
  const [filters, setFilters] = useState({
    dateRange: {
      startDate: null,
      endDate: null,
      preset: 'thisMonth' // thisMonth, lastMonth, thisYear, lastYear, custom
    },
    categories: [],
    amountRange: {
      min: null,
      max: null,
      preset: 'all' // all, low, medium, high, custom
    },
    transactionTypes: [], // income, expense, transfer
    sources: [], // salary, overtime, transaction, other
    smartFilters: {
      highExpenses: false,
      lowExpenses: false,
      recurringTransactions: false,
      anomalyDetection: false
    }
  });

  const [showDatePicker, setShowDatePicker] = useState(false);
  const [datePickerMode, setDatePickerMode] = useState('start'); // start, end
  const [tempDate, setTempDate] = useState(new Date());

  useEffect(() => {
    if (visible) {
      // Modal açıldığında mevcut filtreleri yükle
      setFilters(prev => ({
        ...prev,
        ...currentFilters
      }));
    }
  }, [visible, currentFilters]);

  /**
   * Tarih preset'ini uygular
   */
  const applyDatePreset = (preset) => {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth();
    
    let startDate, endDate;

    switch (preset) {
      case 'thisMonth':
        startDate = new Date(year, month, 1);
        endDate = new Date(year, month + 1, 0);
        break;
      case 'lastMonth':
        startDate = new Date(year, month - 1, 1);
        endDate = new Date(year, month, 0);
        break;
      case 'thisYear':
        startDate = new Date(year, 0, 1);
        endDate = new Date(year, 11, 31);
        break;
      case 'lastYear':
        startDate = new Date(year - 1, 0, 1);
        endDate = new Date(year - 1, 11, 31);
        break;
      case 'last30Days':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        endDate = now;
        break;
      case 'last90Days':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        endDate = now;
        break;
      default:
        return;
    }

    setFilters(prev => ({
      ...prev,
      dateRange: {
        startDate,
        endDate,
        preset
      }
    }));
  };

  /**
   * Tutar preset'ini uygular
   */
  const applyAmountPreset = (preset) => {
    const { minAmount = 0, maxAmount = 10000 } = dataRange;
    
    let min, max;

    switch (preset) {
      case 'low':
        min = minAmount;
        max = maxAmount * 0.3;
        break;
      case 'medium':
        min = maxAmount * 0.3;
        max = maxAmount * 0.7;
        break;
      case 'high':
        min = maxAmount * 0.7;
        max = maxAmount;
        break;
      case 'all':
        min = null;
        max = null;
        break;
      default:
        return;
    }

    setFilters(prev => ({
      ...prev,
      amountRange: {
        min,
        max,
        preset
      }
    }));
  };

  /**
   * Kategori seçimini toggle eder
   */
  const toggleCategory = (category) => {
    setFilters(prev => ({
      ...prev,
      categories: prev.categories.includes(category)
        ? prev.categories.filter(c => c !== category)
        : [...prev.categories, category]
    }));
  };

  /**
   * İşlem türü seçimini toggle eder
   */
  const toggleTransactionType = (type) => {
    setFilters(prev => ({
      ...prev,
      transactionTypes: prev.transactionTypes.includes(type)
        ? prev.transactionTypes.filter(t => t !== type)
        : [...prev.transactionTypes, type]
    }));
  };

  /**
   * Veri kaynağı seçimini toggle eder
   */
  const toggleSource = (source) => {
    setFilters(prev => ({
      ...prev,
      sources: prev.sources.includes(source)
        ? prev.sources.filter(s => s !== source)
        : [...prev.sources, source]
    }));
  };

  /**
   * Akıllı filtreyi toggle eder
   */
  const toggleSmartFilter = (filter) => {
    setFilters(prev => ({
      ...prev,
      smartFilters: {
        ...prev.smartFilters,
        [filter]: !prev.smartFilters[filter]
      }
    }));
  };

  /**
   * Filtreleri sıfırlar
   */
  const resetFilters = () => {
    setFilters({
      dateRange: {
        startDate: null,
        endDate: null,
        preset: 'thisMonth'
      },
      categories: [],
      amountRange: {
        min: null,
        max: null,
        preset: 'all'
      },
      transactionTypes: [],
      sources: [],
      smartFilters: {
        highExpenses: false,
        lowExpenses: false,
        recurringTransactions: false,
        anomalyDetection: false
      }
    });
  };

  /**
   * Filtreleri uygular
   */
  const applyFilters = () => {
    onFiltersApply(filters);
    onClose();
  };

  /**
   * Tarih seçiciyi açar
   */
  const openDatePicker = (mode) => {
    setDatePickerMode(mode);
    setTempDate(mode === 'start' ? filters.dateRange.startDate || new Date() : filters.dateRange.endDate || new Date());
    setShowDatePicker(true);
  };

  /**
   * Tarih seçiciyi kapatır
   */
  const onDateChange = (event, selectedDate) => {
    setShowDatePicker(false);
    if (selectedDate) {
      setFilters(prev => ({
        ...prev,
        dateRange: {
          ...prev.dateRange,
          [datePickerMode === 'start' ? 'startDate' : 'endDate']: selectedDate,
          preset: 'custom'
        }
      }));
    }
  };

  /**
   * Tarih formatlama
   */
  const formatDate = (date) => {
    if (!date) return 'Seçiniz';
    return date.toLocaleDateString('tr-TR');
  };

  // Hızlı filtre preset'leri
  const quickDatePresets = [
    { id: 'thisMonth', name: 'Bu Ay', icon: '📅' },
    { id: 'lastMonth', name: 'Geçen Ay', icon: '📅' },
    { id: 'thisYear', name: 'Bu Yıl', icon: '📅' },
    { id: 'lastYear', name: 'Geçen Yıl', icon: '📅' },
    { id: 'last30Days', name: 'Son 30 Gün', icon: '📅' },
    { id: 'last90Days', name: 'Son 90 Gün', icon: '📅' },
  ];

  const transactionTypeOptions = [
    { id: 'income', name: 'Gelir', icon: '💰' },
    { id: 'expense', name: 'Gider', icon: '💸' },
    { id: 'transfer', name: 'Transfer', icon: '🔄' },
  ];

  const sourceOptions = [
    { id: 'salary', name: 'Maaş', icon: '💳' },
    { id: 'overtime', name: 'Mesai', icon: '⏰' },
    { id: 'transaction', name: 'İşlem', icon: '💱' },
    { id: 'other', name: 'Diğer', icon: '📋' },
  ];

  const smartFilterOptions = [
    { id: 'highExpenses', name: 'Yüksek Harcamalar', description: 'Ortalamanın üstü' },
    { id: 'lowExpenses', name: 'Düşük Harcamalar', description: 'Ortalamanın altı' },
    { id: 'recurringTransactions', name: 'Tekrar Eden İşlemler', description: 'Düzenli ödemeler' },
    { id: 'anomalyDetection', name: 'Anomali Tespiti', description: 'Olağandışı işlemler' },
  ];

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
        {/* Header */}
        <View style={[styles.header, { backgroundColor: theme.SURFACE }]}>
          <TouchableOpacity onPress={onClose}>
            <Text style={[styles.closeButton, { color: theme.TEXT_PRIMARY }]}>
              ← Kapat
            </Text>
          </TouchableOpacity>
          <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
            🔍 Gelişmiş Filtreleme
          </Text>
          <TouchableOpacity onPress={resetFilters}>
            <Text style={[styles.resetButton, { color: theme.PRIMARY }]}>
              Sıfırla
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Hızlı Tarih Filtreleri */}
          <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
            <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
              🎯 Hızlı Tarih Filtreleri
            </Text>
            <View style={styles.quickFilters}>
              {quickDatePresets.map(preset => (
                <TouchableOpacity
                  key={preset.id}
                  style={[
                    styles.quickFilterButton,
                    { backgroundColor: theme.BACKGROUND },
                    filters.dateRange.preset === preset.id && { 
                      backgroundColor: theme.PRIMARY,
                      borderColor: theme.PRIMARY 
                    }
                  ]}
                  onPress={() => applyDatePreset(preset.id)}
                >
                  <Text style={styles.quickFilterIcon}>{preset.icon}</Text>
                  <Text style={[
                    styles.quickFilterText,
                    { color: theme.TEXT_PRIMARY },
                    filters.dateRange.preset === preset.id && { color: theme.SURFACE }
                  ]}>
                    {preset.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Özel Tarih Aralığı */}
          <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
            <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
              📅 Özel Tarih Aralığı
            </Text>
            <View style={styles.dateRangeContainer}>
              <TouchableOpacity
                style={[styles.dateButton, { backgroundColor: theme.BACKGROUND }]}
                onPress={() => openDatePicker('start')}
              >
                <Text style={[styles.dateButtonLabel, { color: theme.TEXT_SECONDARY }]}>
                  Başlangıç
                </Text>
                <Text style={[styles.dateButtonValue, { color: theme.TEXT_PRIMARY }]}>
                  {formatDate(filters.dateRange.startDate)}
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.dateButton, { backgroundColor: theme.BACKGROUND }]}
                onPress={() => openDatePicker('end')}
              >
                <Text style={[styles.dateButtonLabel, { color: theme.TEXT_SECONDARY }]}>
                  Bitiş
                </Text>
                <Text style={[styles.dateButtonValue, { color: theme.TEXT_PRIMARY }]}>
                  {formatDate(filters.dateRange.endDate)}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Kategori Filtreleri */}
          <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
            <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
              🏷️ Kategori Filtreleri
            </Text>
            <View style={styles.categoryContainer}>
              {availableCategories.map(category => (
                <TouchableOpacity
                  key={category.id}
                  style={[
                    styles.categoryButton,
                    { backgroundColor: theme.BACKGROUND },
                    filters.categories.includes(category.id) && { 
                      backgroundColor: theme.PRIMARY,
                      borderColor: theme.PRIMARY 
                    }
                  ]}
                  onPress={() => toggleCategory(category.id)}
                >
                  <Text style={[
                    styles.categoryButtonText,
                    { color: theme.TEXT_PRIMARY },
                    filters.categories.includes(category.id) && { color: theme.SURFACE }
                  ]}>
                    {category.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Tutar Filtreleri */}
          <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
            <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
              💰 Tutar Filtreleri
            </Text>
            <View style={styles.amountPresets}>
              {['all', 'low', 'medium', 'high'].map(preset => (
                <TouchableOpacity
                  key={preset}
                  style={[
                    styles.amountPresetButton,
                    { backgroundColor: theme.BACKGROUND },
                    filters.amountRange.preset === preset && { 
                      backgroundColor: theme.PRIMARY,
                      borderColor: theme.PRIMARY 
                    }
                  ]}
                  onPress={() => applyAmountPreset(preset)}
                >
                  <Text style={[
                    styles.amountPresetText,
                    { color: theme.TEXT_PRIMARY },
                    filters.amountRange.preset === preset && { color: theme.SURFACE }
                  ]}>
                    {preset === 'all' ? 'Hepsi' : 
                     preset === 'low' ? 'Düşük' : 
                     preset === 'medium' ? 'Orta' : 'Yüksek'}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
            
            <View style={styles.amountInputContainer}>
              <View style={styles.amountInputGroup}>
                <Text style={[styles.amountInputLabel, { color: theme.TEXT_SECONDARY }]}>
                  Minimum
                </Text>
                <TextInput
                  style={[styles.amountInput, { backgroundColor: theme.BACKGROUND, color: theme.TEXT_PRIMARY }]}
                  placeholder="0"
                  placeholderTextColor={theme.TEXT_SECONDARY}
                  keyboardType="numeric"
                  value={filters.amountRange.min?.toString() || ''}
                  onChangeText={(text) => setFilters(prev => ({
                    ...prev,
                    amountRange: {
                      ...prev.amountRange,
                      min: text ? parseFloat(text) : null,
                      preset: 'custom'
                    }
                  }))}
                />
              </View>
              
              <View style={styles.amountInputGroup}>
                <Text style={[styles.amountInputLabel, { color: theme.TEXT_SECONDARY }]}>
                  Maksimum
                </Text>
                <TextInput
                  style={[styles.amountInput, { backgroundColor: theme.BACKGROUND, color: theme.TEXT_PRIMARY }]}
                  placeholder="∞"
                  placeholderTextColor={theme.TEXT_SECONDARY}
                  keyboardType="numeric"
                  value={filters.amountRange.max?.toString() || ''}
                  onChangeText={(text) => setFilters(prev => ({
                    ...prev,
                    amountRange: {
                      ...prev.amountRange,
                      max: text ? parseFloat(text) : null,
                      preset: 'custom'
                    }
                  }))}
                />
              </View>
            </View>
          </View>

          {/* İşlem Türü Filtreleri */}
          <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
            <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
              📊 İşlem Türü Filtreleri
            </Text>
            <View style={styles.typeContainer}>
              {transactionTypeOptions.map(type => (
                <TouchableOpacity
                  key={type.id}
                  style={[
                    styles.typeButton,
                    { backgroundColor: theme.BACKGROUND },
                    filters.transactionTypes.includes(type.id) && { 
                      backgroundColor: theme.PRIMARY,
                      borderColor: theme.PRIMARY 
                    }
                  ]}
                  onPress={() => toggleTransactionType(type.id)}
                >
                  <Text style={styles.typeButtonIcon}>{type.icon}</Text>
                  <Text style={[
                    styles.typeButtonText,
                    { color: theme.TEXT_PRIMARY },
                    filters.transactionTypes.includes(type.id) && { color: theme.SURFACE }
                  ]}>
                    {type.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Kaynak Filtreleri */}
          <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
            <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
              🔍 Kaynak Filtreleri
            </Text>
            <View style={styles.sourceContainer}>
              {sourceOptions.map(source => (
                <TouchableOpacity
                  key={source.id}
                  style={[
                    styles.sourceButton,
                    { backgroundColor: theme.BACKGROUND },
                    filters.sources.includes(source.id) && { 
                      backgroundColor: theme.PRIMARY,
                      borderColor: theme.PRIMARY 
                    }
                  ]}
                  onPress={() => toggleSource(source.id)}
                >
                  <Text style={styles.sourceButtonIcon}>{source.icon}</Text>
                  <Text style={[
                    styles.sourceButtonText,
                    { color: theme.TEXT_PRIMARY },
                    filters.sources.includes(source.id) && { color: theme.SURFACE }
                  ]}>
                    {source.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Akıllı Filtreler */}
          <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
            <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
              🤖 Akıllı Filtreler
            </Text>
            <View style={styles.smartFiltersContainer}>
              {smartFilterOptions.map(filter => (
                <View key={filter.id} style={styles.smartFilterItem}>
                  <View style={styles.smartFilterInfo}>
                    <Text style={[styles.smartFilterName, { color: theme.TEXT_PRIMARY }]}>
                      {filter.name}
                    </Text>
                    <Text style={[styles.smartFilterDescription, { color: theme.TEXT_SECONDARY }]}>
                      {filter.description}
                    </Text>
                  </View>
                  <Switch
                    value={filters.smartFilters[filter.id]}
                    onValueChange={() => toggleSmartFilter(filter.id)}
                    trackColor={{ false: theme.BACKGROUND, true: theme.PRIMARY }}
                    thumbColor={filters.smartFilters[filter.id] ? theme.SURFACE : theme.TEXT_SECONDARY}
                  />
                </View>
              ))}
            </View>
          </View>
        </ScrollView>

        {/* Footer */}
        <View style={[styles.footer, { backgroundColor: theme.SURFACE }]}>
          <TouchableOpacity
            style={[styles.applyButton, { backgroundColor: theme.PRIMARY }]}
            onPress={applyFilters}
          >
            <Text style={[styles.applyButtonText, { color: theme.SURFACE }]}>
              ✅ Filtreleri Uygula
            </Text>
          </TouchableOpacity>
        </View>

        {/* Date Picker Modal */}
        {showDatePicker && (
          <DateTimePicker
            value={tempDate}
            mode="date"
            display="default"
            onChange={onDateChange}
          />
        )}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  closeButton: {
    fontSize: 16,
    fontWeight: '500',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  resetButton: {
    fontSize: 16,
    fontWeight: '500',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 16,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  quickFilters: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  quickFilterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
  },
  quickFilterIcon: {
    fontSize: 16,
    marginRight: 6,
  },
  quickFilterText: {
    fontSize: 14,
    fontWeight: '500',
  },
  dateRangeContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  dateButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
  },
  dateButtonLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  dateButtonValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  categoryContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  categoryButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
  },
  categoryButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  amountPresets: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 12,
  },
  amountPresetButton: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
  },
  amountPresetText: {
    fontSize: 14,
    fontWeight: '500',
  },
  amountInputContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  amountInputGroup: {
    flex: 1,
  },
  amountInputLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  amountInput: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
    fontSize: 16,
  },
  typeContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  typeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
  },
  typeButtonIcon: {
    fontSize: 16,
    marginRight: 6,
  },
  typeButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  sourceContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  sourceButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
  },
  sourceButtonIcon: {
    fontSize: 16,
    marginRight: 6,
  },
  sourceButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  smartFiltersContainer: {
    gap: 12,
  },
  smartFilterItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  smartFilterInfo: {
    flex: 1,
  },
  smartFilterName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  smartFilterDescription: {
    fontSize: 12,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  applyButton: {
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  applyButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default AdvancedFilterBuilder;
