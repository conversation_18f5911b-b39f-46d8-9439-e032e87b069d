/**
 * Hesap tabloları için migrasyon
 *
 * @param {object} db - SQLite veritabanı nesnesi
 * @returns {Promise<void>}
 */
export const migrateAccounts = async (db) => {
  try {
    console.log('Hesap tabloları migrasyonu başlatılıyor...');

    // Eski tabloyu temizle
    await db.execAsync(`DROP TABLE IF EXISTS accounts`);

    // accounts tablosunu oluştur
    const hasAccountsTable = await db.getFirstAsync(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='accounts'
    `);

    if (!hasAccountsTable) {
      console.log('accounts tablosu oluşturuluyor...');

      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS accounts (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          type TEXT NOT NULL DEFAULT 'bank', -- bank, cash, credit_card, investment, other
          currency TEXT NOT NULL DEFAULT 'TRY',
          balance DECIMAL(10,2) DEFAULT 0,
          is_active INTEGER DEFAULT 1,
          icon TEXT,
          color TEXT,
          notes TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Varsayılan hesapları ekle
      await db.runAsync(`
        INSERT INTO accounts (name, type, currency, balance, icon, color)
        VALUES ('Nakit', 'cash', 'TRY', 0, 'payments', '#4CAF50')
      `);

      await db.runAsync(`
        INSERT INTO accounts (name, type, currency, balance, icon, color)
        VALUES ('Banka', 'bank', 'TRY', 0, 'account-balance', '#2196F3')
      `);

      console.log('accounts tablosu başarıyla oluşturuldu.');
    } else {
      console.log('accounts tablosu zaten mevcut.');

      // Tabloda eksik sütunlar var mı kontrol et
      const columns = await db.getAllAsync(`PRAGMA table_info(accounts)`);

      // Sütun adlarını bir diziye dönüştür
      const columnNames = columns.map(col => col.name);

      // Eksik sütunları kontrol et ve ekle
      if (!columnNames.includes('currency')) {
        console.log('currency sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE accounts ADD COLUMN currency TEXT NOT NULL DEFAULT 'TRY'`);
      }

      if (!columnNames.includes('is_active')) {
        console.log('is_active sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE accounts ADD COLUMN is_active INTEGER DEFAULT 1`);
      }

      if (!columnNames.includes('icon')) {
        console.log('icon sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE accounts ADD COLUMN icon TEXT`);
      }

      if (!columnNames.includes('color')) {
        console.log('color sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE accounts ADD COLUMN color TEXT`);
      }

      if (!columnNames.includes('notes')) {
        console.log('notes sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE accounts ADD COLUMN notes TEXT`);
      }

      if (!columnNames.includes('updated_at')) {
        console.log('updated_at sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE accounts ADD COLUMN updated_at DATETIME DEFAULT CURRENT_TIMESTAMP`);
      }
    }

    console.log('Hesap tabloları migrasyonu tamamlandı.');
  } catch (error) {
    console.error('Hesap tabloları migrasyon hatası:', error);
    throw error;
  }
};
