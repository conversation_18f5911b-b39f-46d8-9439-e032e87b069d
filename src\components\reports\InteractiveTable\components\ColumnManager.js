import React, { useState } from 'react';
import { View, Text, TouchableOpacity, FlatList, Modal, TextInput, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../../../context/ThemeContext';

/**
 * <PERSON><PERSON>tun yönetimi bile<PERSON> - sütun ekle<PERSON>, çıkarma, yeniden adland<PERSON>
 * @param {Object} props - Bileşen props'ları
 * @param {Array} props.columns - Mevcut sütunlar
 * @param {Function} props.onColumnsChange - Sütunlar değiştiğinde çağrılacak fonksiyon
 * @param {Array} props.availableColumns - Kullanılabilir sütunlar
 * @param {Boolean} props.visible - Modal görünürlüğü
 * @param {Function} props.onClose - Modal kapatma fonksiyonu
 */
const ColumnManager = ({ 
  columns = [], 
  onColumnsChange, 
  availableColumns = [], 
  visible = false, 
  onClose 
}) => {
  const { theme } = useTheme();
  const [editingColumn, setEditingColumn] = useState(null);
  const [newColumnName, setNewColumnName] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);

  // Tema güvenlik kontrolü
  if (!theme) {
    return null;
  }

  const styles = {
    container: {
      flex: 1,
      backgroundColor: theme.BACKGROUND || theme.colors?.background || '#fff',
      padding: 16,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 24,
      paddingBottom: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.BORDER || theme.colors?.border || '#e0e0e0',
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.TEXT_PRIMARY || theme.colors?.text || '#333',
    },
    closeButton: {
      padding: 8,
    },
    section: {
      marginBottom: 24,
    },
    sectionTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.TEXT_PRIMARY || theme.colors?.text || '#333',
      marginBottom: 12,
    },
    columnItem: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 12,
      backgroundColor: theme.SURFACE || theme.colors?.surface || '#f9f9f9',
      borderRadius: 8,
      marginBottom: 8,
      borderWidth: 1,
      borderColor: theme.BORDER || theme.colors?.border || '#e0e0e0',
    },
    columnInfo: {
      flex: 1,
      marginRight: 12,
    },
    columnName: {
      fontSize: 14,
      fontWeight: '500',
      color: theme.TEXT_PRIMARY || theme.colors?.text || '#333',
    },
    columnType: {
      fontSize: 12,
      color: theme.TEXT_SECONDARY || theme.colors?.textSecondary || '#666',
      marginTop: 2,
    },
    columnActions: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    actionButton: {
      padding: 8,
      marginLeft: 4,
    },
    addButton: {
      backgroundColor: theme.PRIMARY || theme.colors?.primary || '#007AFF',
      padding: 12,
      borderRadius: 8,
      alignItems: 'center',
      marginBottom: 16,
    },
    addButtonText: {
      color: theme.SURFACE || theme.colors?.surface || '#f9f9f9',
      fontSize: 14,
      fontWeight: '600',
    },
    modalContainer: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContent: {
      backgroundColor: theme.SURFACE || theme.colors?.surface || '#f9f9f9',
      padding: 24,
      borderRadius: 12,
      width: '90%',
      maxWidth: 400,
    },
    modalTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.TEXT_PRIMARY || theme.colors?.text || '#333',
      marginBottom: 16,
      textAlign: 'center',
    },
    input: {
      borderWidth: 1,
      borderColor: theme.BORDER || theme.colors?.border || '#e0e0e0',
      borderRadius: 8,
      padding: 12,
      fontSize: 16,
      color: theme.TEXT_PRIMARY || theme.colors?.text || '#333',
      marginBottom: 16,
    },
    modalActions: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    modalButton: {
      flex: 1,
      padding: 12,
      borderRadius: 8,
      alignItems: 'center',
      marginHorizontal: 4,
    },
    modalButtonCancel: {
      backgroundColor: theme.SURFACE_VARIANT || theme.colors?.surface || '#f9f9f9',
    },
    modalButtonConfirm: {
      backgroundColor: theme.PRIMARY || theme.colors?.primary || '#007AFF',
    },
    modalButtonText: {
      fontSize: 14,
      fontWeight: '600',
    },
    modalButtonTextCancel: {
      color: theme.TEXT_SECONDARY || theme.colors?.textSecondary || '#666',
    },
    modalButtonTextConfirm: {
      color: theme.SURFACE || theme.colors?.surface || '#f9f9f9',
    },
  };

  /**
   * Sütun silme işlemi
   * @param {string} columnId - Silinecek sütun ID'si
   */
  const handleRemoveColumn = (columnId) => {
    Alert.alert(
      'Sütunu Sil',
      'Bu sütunu silmek istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        { 
          text: 'Sil', 
          style: 'destructive',
          onPress: () => {
            const updatedColumns = columns.filter(col => col.id !== columnId);
            onColumnsChange(updatedColumns);
          }
        }
      ]
    );
  };

  /**
   * Sütun düzenleme başlatma
   * @param {Object} column - Düzenlenecek sütun
   */
  const handleEditColumn = (column) => {
    setEditingColumn(column);
    setNewColumnName(column.name);
  };

  /**
   * Sütun düzenleme kaydetme
   */
  const handleSaveEdit = () => {
    if (!newColumnName.trim()) {
      Alert.alert('Hata', 'Sütun adı boş olamaz');
      return;
    }

    const updatedColumns = columns.map(col => 
      col.id === editingColumn.id 
        ? { ...col, name: newColumnName.trim() }
        : col
    );
    
    onColumnsChange(updatedColumns);
    setEditingColumn(null);
    setNewColumnName('');
  };

  /**
   * Yeni sütun ekleme
   * @param {Object} column - Eklenecek sütun
   */
  const handleAddColumn = (column) => {
    const newColumn = {
      ...column,
      id: `col_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      visible: true,
      sortable: true,
      filterable: true,
    };
    
    const updatedColumns = [...columns, newColumn];
    onColumnsChange(updatedColumns);
    setShowAddModal(false);
  };

  /**
   * Sütun görünürlüğünü değiştirme
   * @param {string} columnId - Sütun ID'si
   */
  const toggleColumnVisibility = (columnId) => {
    const updatedColumns = columns.map(col => 
      col.id === columnId 
        ? { ...col, visible: !col.visible }
        : col
    );
    
    onColumnsChange(updatedColumns);
  };

  /**
   * Sütun öğesi render fonksiyonu
   * @param {Object} param0 - Render parametreleri
   * @returns {JSX.Element} Sütun öğesi
   */
  const renderColumnItem = ({ item }) => (
    <View style={styles.columnItem}>
      <View style={styles.columnInfo}>
        <Text style={styles.columnName}>{item.name}</Text>
        <Text style={styles.columnType}>{item.type || 'Metin'}</Text>
      </View>
      <View style={styles.columnActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => toggleColumnVisibility(item.id)}
        >
          <Ionicons
            name={item.visible ? 'eye' : 'eye-off'}
            size={20}
            color={item.visible ? (theme.PRIMARY || theme.colors?.primary || '#007AFF') : (theme.TEXT_SECONDARY || theme.colors?.textSecondary || '#666')}
          />
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleEditColumn(item)}
        >
          <Ionicons
            name="pencil"
            size={20}
            color={theme.TEXT_SECONDARY || theme.colors?.textSecondary || '#666'}
          />
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleRemoveColumn(item.id)}
        >
          <Ionicons
            name="trash"
            size={20}
            color={theme.DANGER || theme.colors?.error || '#dc3545'}
          />
        </TouchableOpacity>
      </View>
    </View>
  );

  /**
   * Kullanılabilir sütun öğesi render fonksiyonu
   * @param {Object} param0 - Render parametreleri
   * @returns {JSX.Element} Kullanılabilir sütun öğesi
   */
  const renderAvailableColumnItem = ({ item }) => (
    <TouchableOpacity
      style={[styles.columnItem, { borderColor: theme.PRIMARY || theme.colors?.primary || '#007AFF' }]}
      onPress={() => handleAddColumn(item)}
    >
      <View style={styles.columnInfo}>
        <Text style={styles.columnName}>{item.name}</Text>
        <Text style={styles.columnType}>{item.type || 'Metin'}</Text>
      </View>
      <Ionicons
        name="add"
        size={20}
        color={theme.PRIMARY || theme.colors?.primary || '#007AFF'}
      />
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Sütun Yönetimi</Text>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Ionicons name="close" size={24} color={theme.TEXT_PRIMARY || theme.colors?.text || '#333'} />
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Mevcut Sütunlar</Text>
          <FlatList
            data={columns}
            renderItem={renderColumnItem}
            keyExtractor={item => item.id}
            showsVerticalScrollIndicator={false}
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Yeni Sütun Ekle</Text>
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => setShowAddModal(true)}
          >
            <Text style={styles.addButtonText}>+ Sütun Ekle</Text>
          </TouchableOpacity>
        </View>

        {/* Düzenleme Modal */}
        <Modal
          visible={editingColumn !== null}
          transparent={true}
          animationType="fade"
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <Text style={styles.modalTitle}>Sütun Düzenle</Text>
              <TextInput
                style={styles.input}
                value={newColumnName}
                onChangeText={setNewColumnName}
                placeholder="Sütun adı"
                placeholderTextColor={theme.colors.textSecondary}
              />
              <View style={styles.modalActions}>
                <TouchableOpacity
                  style={[styles.modalButton, styles.modalButtonCancel]}
                  onPress={() => {
                    setEditingColumn(null);
                    setNewColumnName('');
                  }}
                >
                  <Text style={[styles.modalButtonText, styles.modalButtonTextCancel]}>İptal</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.modalButton, styles.modalButtonConfirm]}
                  onPress={handleSaveEdit}
                >
                  <Text style={[styles.modalButtonText, styles.modalButtonTextConfirm]}>Kaydet</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>

        {/* Yeni Sütun Ekleme Modal */}
        <Modal
          visible={showAddModal}
          transparent={true}
          animationType="fade"
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <Text style={styles.modalTitle}>Sütun Ekle</Text>
              <FlatList
                data={availableColumns.filter(col => 
                  !columns.some(existingCol => existingCol.id === col.id)
                )}
                renderItem={renderAvailableColumnItem}
                keyExtractor={item => item.id}
                showsVerticalScrollIndicator={false}
              />
              <TouchableOpacity
                style={[styles.modalButton, styles.modalButtonCancel]}
                onPress={() => setShowAddModal(false)}
              >
                <Text style={[styles.modalButtonText, styles.modalButtonTextCancel]}>İptal</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>
      </View>
    </Modal>
  );
};

export default ColumnManager;
