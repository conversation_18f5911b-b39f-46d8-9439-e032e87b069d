/**
 * <PERSON><PERSON><PERSON> hedefleri tablosunu oluşturan migrasyon
 *
 * @param {object} db - SQLite veritabanı nesnesi
 * @returns {Promise<void>}
 */
export const migrateSavingsGoals = async (db) => {
  try {
    console.log('<PERSON><PERSON><PERSON> hedefleri tablosu migrasyonu başlatılıyor...');

    // Tablo var mı kontrol et
    const hasSavingsGoalsTable = await db.getFirstAsync(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='savings_goals'
    `);

    if (!hasSavingsGoalsTable) {
      console.log('<PERSON><PERSON><PERSON> hedefleri tablosu oluşturuluyor...');

      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS savings_goals (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          target_amount DECIMAL(10,2) NOT NULL,
          current_amount DECIMAL(10,2) DEFAULT 0,
          target_date DATE NOT NULL,
          currency TEXT DEFAULT 'TRY',
          notes TEXT,
          is_completed INTEGER DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      console.log('Birikim hedefleri tablosu başarıyla oluşturuldu.');
    } else {
      console.log('Birikim hedefleri tablosu zaten mevcut.');

      // Tabloda eksik sütunlar var mı kontrol et
      const columns = await db.getAllAsync(`PRAGMA table_info(savings_goals)`);

      // Sütun adlarını bir diziye dönüştür
      const columnNames = columns.map(col => col.name);

      // Eksik sütunları kontrol et ve ekle
      if (!columnNames.includes('currency')) {
        console.log('currency sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE savings_goals ADD COLUMN currency TEXT DEFAULT 'TRY'`);
      }

      if (!columnNames.includes('notes')) {
        console.log('notes sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE savings_goals ADD COLUMN notes TEXT`);
      }

      if (!columnNames.includes('updated_at')) {
        console.log('updated_at sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE savings_goals ADD COLUMN updated_at DATETIME DEFAULT CURRENT_TIMESTAMP`);
      }

      if (!columnNames.includes('is_completed')) {
        console.log('is_completed sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE savings_goals ADD COLUMN is_completed INTEGER DEFAULT 0`);
      }
    }

    console.log('Birikim hedefleri tablosu migrasyonu tamamlandı.');
  } catch (error) {
    console.error('Birikim hedefleri tablosu migrasyon hatası:', error);
    throw error;
  }
};
