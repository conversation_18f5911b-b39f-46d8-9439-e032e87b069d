/**
 * Budget Notification Service
 * Integrates with existing NotificationService for budget alerts
 * Provides real-time budget threshold notifications
 * 
 * Features:
 * - Threshold-based alerts (75%, 90%, 100%+)
 * - Category limit exceeded notifications
 * - Daily/Weekly budget summaries
 * - Turkish localized messages
 * - Integration with existing notification system
 */

import * as NotificationService from '../NotificationService';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';

/**
 * Checks budget thresholds and sends notifications if needed
 * @param {SQLiteDatabase} db - Database instance
 * @param {number} budgetId - Budget ID to check
 * @returns {Promise<Array>} Array of sent notifications
 */
export const checkBudgetThresholds = async (db, budgetId) => {
  try {
    console.log(`🔔 Checking budget thresholds for budget ${budgetId}`);
    
    // Get budget with current spending data
    const budget = await db.getFirstAsync(`
      SELECT 
        b.*,
        COALESCE(SUM(bc.spent_amount), 0) as total_spent,
        COALESCE(SUM(bc.limit_amount), b.total_limit) as total_limit
      FROM budgets b
      LEFT JOIN budget_categories bc ON b.id = bc.budget_id
      WHERE b.id = ? AND b.status = 'active'
      GROUP BY b.id
    `, [budgetId]);
    
    if (!budget) {
      console.log('❌ Budget not found or not active');
      return [];
    }
    
    // Get alert settings
    const alertSettings = await db.getFirstAsync(`
      SELECT * FROM budget_alert_settings WHERE budget_id = ?
    `, [budgetId]);
    
    if (!alertSettings) {
      console.log('❌ No alert settings found');
      return [];
    }
    
    const spendingPercentage = budget.total_limit > 0 
      ? (budget.total_spent / budget.total_limit) * 100 
      : 0;
    
    const sentNotifications = [];
    
    // Check 100%+ threshold (over budget)
    if (spendingPercentage >= 100 && alertSettings.threshold_100) {
      const notification = await sendOverBudgetNotification(db, budget, spendingPercentage);
      if (notification) sentNotifications.push(notification);
    }
    // Check 90% threshold
    else if (spendingPercentage >= 90 && alertSettings.threshold_90) {
      const notification = await send90PercentNotification(db, budget, spendingPercentage);
      if (notification) sentNotifications.push(notification);
    }
    // Check 75% threshold
    else if (spendingPercentage >= 75 && alertSettings.threshold_75) {
      const notification = await send75PercentNotification(db, budget, spendingPercentage);
      if (notification) sentNotifications.push(notification);
    }
    
    console.log(`✅ Sent ${sentNotifications.length} budget notifications`);
    return sentNotifications;
    
  } catch (error) {
    console.error('❌ Budget threshold check failed:', error);
    return [];
  }
};

/**
 * Checks category-specific budget limits
 * @param {SQLiteDatabase} db - Database instance
 * @param {number} budgetId - Budget ID
 * @returns {Promise<Array>} Array of category notifications
 */
export const checkCategoryLimits = async (db, budgetId) => {
  try {
    console.log(`🔔 Checking category limits for budget ${budgetId}`);
    
    // Get categories that are over their limits
    const overLimitCategories = await db.getAllAsync(`
      SELECT 
        bc.*,
        c.name as category_name,
        c.icon as category_icon,
        b.name as budget_name,
        ROUND((bc.spent_amount / bc.limit_amount) * 100, 2) as usage_percentage
      FROM budget_categories bc
      INNER JOIN categories c ON bc.category_id = c.id
      INNER JOIN budgets b ON bc.budget_id = b.id
      WHERE bc.budget_id = ? 
        AND bc.spent_amount > bc.limit_amount
        AND b.status = 'active'
    `, [budgetId]);
    
    // Get alert settings
    const alertSettings = await db.getFirstAsync(`
      SELECT category_limit_exceeded FROM budget_alert_settings WHERE budget_id = ?
    `, [budgetId]);
    
    if (!alertSettings?.category_limit_exceeded) {
      return [];
    }
    
    const sentNotifications = [];
    
    for (const category of overLimitCategories) {
      // Check if we already sent a notification for this category recently
      const recentNotification = await db.getFirstAsync(`
        SELECT id FROM budget_notifications 
        WHERE budget_id = ? 
          AND category_id = ? 
          AND notification_type = 'category_over_limit'
          AND sent_at > datetime('now', '-1 hour')
      `, [budgetId, category.category_id]);
      
      if (!recentNotification) {
        const notification = await sendCategoryOverLimitNotification(db, category);
        if (notification) sentNotifications.push(notification);
      }
    }
    
    console.log(`✅ Sent ${sentNotifications.length} category limit notifications`);
    return sentNotifications;
    
  } catch (error) {
    console.error('❌ Category limit check failed:', error);
    return [];
  }
};

/**
 * Sends daily budget summary notification
 * @param {SQLiteDatabase} db - Database instance
 * @param {number} budgetId - Budget ID
 * @returns {Promise<Object|null>} Notification result
 */
export const sendDailyBudgetSummary = async (db, budgetId) => {
  try {
    console.log(`📊 Sending daily budget summary for ${budgetId}`);
    
    // Get budget summary data
    const summary = await db.getFirstAsync(`
      SELECT 
        b.name as budget_name,
        b.currency,
        COALESCE(SUM(bc.spent_amount), 0) as total_spent,
        COALESCE(SUM(bc.limit_amount), b.total_limit) as total_limit,
        COUNT(CASE WHEN bc.spent_amount > bc.limit_amount THEN 1 END) as over_limit_categories
      FROM budgets b
      LEFT JOIN budget_categories bc ON b.id = bc.budget_id
      WHERE b.id = ? AND b.status = 'active'
      GROUP BY b.id
    `, [budgetId]);
    
    if (!summary) return null;
    
    const spendingPercentage = summary.total_limit > 0 
      ? Math.round((summary.total_spent / summary.total_limit) * 100) 
      : 0;
    
    const remaining = summary.total_limit - summary.total_spent;
    
    // Create notification content
    const title = `📊 ${summary.budget_name} - Günlük Özet`;
    let body = `Bugün: ${formatCurrency(summary.total_spent, summary.currency)} harcandı\n`;
    body += `Kalan: ${formatCurrency(remaining, summary.currency)} (%${100 - spendingPercentage})\n`;
    
    if (summary.over_limit_categories > 0) {
      body += `⚠️ ${summary.over_limit_categories} kategori limitini aştı`;
    }
    
    // Send notification
    const notificationId = await NotificationService.sendNotification({
      title,
      body,
      data: {
        type: 'budget_daily_summary',
        budgetId: budgetId,
        spendingPercentage
      }
    });
    
    // Log to database
    await logNotification(db, budgetId, null, 'daily_summary', body, spendingPercentage, summary.total_spent);
    
    return { id: notificationId, type: 'daily_summary' };
    
  } catch (error) {
    console.error('❌ Daily budget summary failed:', error);
    return null;
  }
};

/**
 * Sends 75% threshold notification
 * @param {SQLiteDatabase} db - Database instance
 * @param {Object} budget - Budget data
 * @param {number} percentage - Spending percentage
 * @returns {Promise<Object|null>} Notification result
 */
const send75PercentNotification = async (db, budget, percentage) => {
  try {
    // Check if already sent recently
    const recentNotification = await db.getFirstAsync(`
      SELECT id FROM budget_notifications 
      WHERE budget_id = ? 
        AND notification_type = 'threshold_75'
        AND sent_at > datetime('now', '-24 hours')
    `, [budget.id]);
    
    if (recentNotification) return null;
    
    const title = `⚠️ ${budget.name} - Dikkat!`;
    const body = `Bütçenizin %${Math.round(percentage)}'ini kullandınız. Harcamalarınızı kontrol etmeyi unutmayın!`;
    
    const notificationId = await NotificationService.sendNotification({
      title,
      body,
      data: {
        type: 'budget_threshold_75',
        budgetId: budget.id,
        percentage
      }
    });
    
    await logNotification(db, budget.id, null, 'threshold_75', body, percentage, budget.total_spent);
    
    return { id: notificationId, type: 'threshold_75' };
  } catch (error) {
    console.error('❌ 75% threshold notification failed:', error);
    return null;
  }
};

/**
 * Sends 90% threshold notification
 * @param {SQLiteDatabase} db - Database instance
 * @param {Object} budget - Budget data
 * @param {number} percentage - Spending percentage
 * @returns {Promise<Object|null>} Notification result
 */
const send90PercentNotification = async (db, budget, percentage) => {
  try {
    const recentNotification = await db.getFirstAsync(`
      SELECT id FROM budget_notifications 
      WHERE budget_id = ? 
        AND notification_type = 'threshold_90'
        AND sent_at > datetime('now', '-12 hours')
    `, [budget.id]);
    
    if (recentNotification) return null;
    
    const title = `🚨 ${budget.name} - Son Uyarı!`;
    const body = `Bütçenizin %${Math.round(percentage)}'ini kullandınız. Limit aşımına çok yakınsınız!`;
    
    const notificationId = await NotificationService.sendNotification({
      title,
      body,
      data: {
        type: 'budget_threshold_90',
        budgetId: budget.id,
        percentage
      }
    });
    
    await logNotification(db, budget.id, null, 'threshold_90', body, percentage, budget.total_spent);
    
    return { id: notificationId, type: 'threshold_90' };
  } catch (error) {
    console.error('❌ 90% threshold notification failed:', error);
    return null;
  }
};

/**
 * Sends over budget notification
 * @param {SQLiteDatabase} db - Database instance
 * @param {Object} budget - Budget data
 * @param {number} percentage - Spending percentage
 * @returns {Promise<Object|null>} Notification result
 */
const sendOverBudgetNotification = async (db, budget, percentage) => {
  try {
    const title = `🔴 ${budget.name} - Limit Aşıldı!`;
    const overAmount = budget.total_spent - budget.total_limit;
    const body = `Bütçenizi ${formatCurrency(overAmount, budget.currency)} aştınız! (%${Math.round(percentage - 100)} fazla)`;
    
    const notificationId = await NotificationService.sendNotification({
      title,
      body,
      data: {
        type: 'budget_over_limit',
        budgetId: budget.id,
        percentage,
        overAmount
      }
    });
    
    await logNotification(db, budget.id, null, 'over_budget', body, percentage, budget.total_spent);
    
    return { id: notificationId, type: 'over_budget' };
  } catch (error) {
    console.error('❌ Over budget notification failed:', error);
    return null;
  }
};

/**
 * Sends category over limit notification
 * @param {SQLiteDatabase} db - Database instance
 * @param {Object} category - Category data
 * @returns {Promise<Object|null>} Notification result
 */
const sendCategoryOverLimitNotification = async (db, category) => {
  try {
    const title = `🔴 ${category.category_name} - Limit Aşıldı!`;
    const overAmount = category.spent_amount - category.limit_amount;
    const body = `${category.category_name} kategorisi limitini ${formatCurrency(overAmount, category.currency)} aştı!`;
    
    const notificationId = await NotificationService.sendNotification({
      title,
      body,
      data: {
        type: 'category_over_limit',
        budgetId: category.budget_id,
        categoryId: category.category_id,
        overAmount
      }
    });
    
    await logNotification(
      db, 
      category.budget_id, 
      category.category_id, 
      'category_over_limit', 
      body, 
      category.usage_percentage, 
      category.spent_amount
    );
    
    return { id: notificationId, type: 'category_over_limit' };
  } catch (error) {
    console.error('❌ Category over limit notification failed:', error);
    return null;
  }
};

/**
 * Logs notification to database
 * @param {SQLiteDatabase} db - Database instance
 * @param {number} budgetId - Budget ID
 * @param {number} categoryId - Category ID (optional)
 * @param {string} type - Notification type
 * @param {string} message - Notification message
 * @param {number} thresholdValue - Threshold percentage
 * @param {number} amount - Current amount
 * @returns {Promise<void>}
 */
const logNotification = async (db, budgetId, categoryId, type, message, thresholdValue, amount) => {
  try {
    await db.runAsync(`
      INSERT INTO budget_notifications 
      (budget_id, category_id, notification_type, message, threshold_value, amount)
      VALUES (?, ?, ?, ?, ?, ?)
    `, [budgetId, categoryId, type, message, thresholdValue, amount]);
  } catch (error) {
    console.error('❌ Notification logging failed:', error);
  }
};

/**
 * Gets unread budget notifications
 * @param {SQLiteDatabase} db - Database instance
 * @param {number} budgetId - Budget ID (optional)
 * @returns {Promise<Array>} Unread notifications
 */
export const getUnreadBudgetNotifications = async (db, budgetId = null) => {
  try {
    let query = `
      SELECT 
        bn.*,
        b.name as budget_name,
        c.name as category_name,
        c.icon as category_icon
      FROM budget_notifications bn
      INNER JOIN budgets b ON bn.budget_id = b.id
      LEFT JOIN categories c ON bn.category_id = c.id
      WHERE bn.read_at IS NULL
    `;
    
    const params = [];
    
    if (budgetId) {
      query += ' AND bn.budget_id = ?';
      params.push(budgetId);
    }
    
    query += ' ORDER BY bn.sent_at DESC LIMIT 50';
    
    const notifications = await db.getAllAsync(query, params);
    return notifications;
  } catch (error) {
    console.error('❌ Unread notifications retrieval failed:', error);
    return [];
  }
};

/**
 * Marks budget notifications as read
 * @param {SQLiteDatabase} db - Database instance
 * @param {Array} notificationIds - Array of notification IDs
 * @returns {Promise<boolean>} Success status
 */
export const markNotificationsAsRead = async (db, notificationIds) => {
  try {
    if (!notificationIds || notificationIds.length === 0) return false;
    
    const placeholders = notificationIds.map(() => '?').join(',');
    await db.runAsync(`
      UPDATE budget_notifications 
      SET read_at = CURRENT_TIMESTAMP 
      WHERE id IN (${placeholders})
    `, notificationIds);
    
    console.log(`✅ Marked ${notificationIds.length} notifications as read`);
    return true;
  } catch (error) {
    console.error('❌ Mark notifications as read failed:', error);
    return false;
  }
};

/**
 * Formats currency amount for display
 * @param {number} amount - Amount to format
 * @param {string} currency - Currency code
 * @returns {string} Formatted amount
 */
const formatCurrency = (amount, currency = 'TRY') => {
  const symbols = { TRY: '₺', USD: '$', EUR: '€' };
  const symbol = symbols[currency] || currency;
  
  return `${Math.abs(amount).toLocaleString('tr-TR', { 
    minimumFractionDigits: 2, 
    maximumFractionDigits: 2 
  })} ${symbol}`;
};
