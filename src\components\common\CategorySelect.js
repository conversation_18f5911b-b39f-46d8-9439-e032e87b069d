import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, TextInput, ScrollView } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { EXPENSE_CATEGORIES } from '../../constants/categories';

/**
 * <PERSON><PERSON>i seçim bi<PERSON>
 * @param {Object} props Component props
 * @param {Array} props.selected - Seç<PERSON> kategoriler
 * @param {Function} props.onSelect - Kategori seçim handler
 * @param {boolean} [props.multiple=false] - Çoklu seçim
 * @param {string} [props.placeholder='Kategori Seç'] - Placeholder text
 * @param {boolean} [props.showSearch=true] - <PERSON><PERSON>
 * @param {Array} [props.categories=EXPENSE_CATEGORIES] - Özel kategori listesi
 * @returns {JSX.Element} CategorySelect component
 */
export const CategorySelect = ({
    selected = [],
    onSelect,
    multiple = false,
    placeholder = 'Kate<PERSON><PERSON>',
    showSearch = true,
    categories = EXPENSE_CATEGORIES
}) => {
    const [searchText, setSearchText] = useState('');
    const [isExpanded, setIsExpanded] = useState(false);

    // Kategori filtreleme
    const filteredCategories = categories.filter(category =>
        category.label.toLowerCase().includes(searchText.toLowerCase())
    );

    // Kategori seçim handler
    const handleSelect = (category) => {
        if (multiple) {
            const isSelected = selected.includes(category.value);
            const newSelected = isSelected
                ? selected.filter(item => item !== category.value)
                : [...selected, category.value];
            onSelect(newSelected);
        } else {
            onSelect(category.value);
            setIsExpanded(false);
        }
    };

    // Seçili kategori isimlerini getir
    const getSelectedLabels = () => {
        if (!selected || (Array.isArray(selected) && selected.length === 0)) {
            return placeholder;
        }

        const selectedCategories = categories.filter(cat => 
            Array.isArray(selected) 
                ? selected.includes(cat.value)
                : selected === cat.value
        );

        return selectedCategories.map(cat => cat.label).join(', ');
    };

    return (
        <View style={styles.container}>
            <TouchableOpacity
                style={styles.header}
                onPress={() => setIsExpanded(!isExpanded)}
            >
                <View style={styles.headerContent}>
                    <Text style={styles.headerText} numberOfLines={1}>
                        {getSelectedLabels()}
                    </Text>
                    <MaterialIcons
                        name={isExpanded ? "expand-less" : "expand-more"}
                        size={24}
                        color="#666"
                    />
                </View>
            </TouchableOpacity>

            {isExpanded && (
                <View style={styles.dropdownContainer}>
                    {showSearch && (
                        <View style={styles.searchContainer}>
                            <MaterialIcons name="search" size={20} color="#666" />
                            <TextInput
                                style={styles.searchInput}
                                value={searchText}
                                onChangeText={setSearchText}
                                placeholder="Kategori ara..."
                                placeholderTextColor="#999"
                            />
                        </View>
                    )}

                    <ScrollView style={styles.categoryList}>
                        {filteredCategories.map((category) => (
                            <TouchableOpacity
                                key={category.value}
                                style={[
                                    styles.categoryItem,
                                    (Array.isArray(selected) 
                                        ? selected.includes(category.value)
                                        : selected === category.value) && styles.selectedItem
                                ]}
                                onPress={() => handleSelect(category)}
                            >
                                <Text style={[
                                    styles.categoryText,
                                    (Array.isArray(selected)
                                        ? selected.includes(category.value)
                                        : selected === category.value) && styles.selectedText
                                ]}>
                                    {category.label}
                                </Text>
                                {(Array.isArray(selected)
                                    ? selected.includes(category.value)
                                    : selected === category.value) && (
                                    <MaterialIcons name="check" size={20} color="#fff" />
                                )}
                            </TouchableOpacity>
                        ))}
                    </ScrollView>
                </View>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        backgroundColor: '#fff',
        borderRadius: 8,
        marginBottom: 16,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.2,
        shadowRadius: 2,
    },
    header: {
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    headerContent: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    headerText: {
        fontSize: 16,
        color: '#333',
        flex: 1,
        marginRight: 8,
    },
    dropdownContainer: {
        maxHeight: 300,
    },
    searchContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 8,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    searchInput: {
        flex: 1,
        marginLeft: 8,
        fontSize: 16,
        color: '#333',
    },
    categoryList: {
        maxHeight: 250,
    },
    categoryItem: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    selectedItem: {
        backgroundColor: '#3498db',
    },
    categoryText: {
        fontSize: 16,
        color: '#333',
    },
    selectedText: {
        color: '#fff',
    }
});
