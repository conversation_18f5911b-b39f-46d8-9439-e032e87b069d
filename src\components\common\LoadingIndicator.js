import React from 'react';
import { View, ActivityIndicator, StyleSheet } from 'react-native';
import { Colors } from '../../constants/colors';

/**
 * Yükleme göstergesi bileşeni
 * Renk hatalarına karşı korumalı
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {string} [props.color] - Gösterge rengi
 * @param {string} [props.size='large'] - Gösterge boyutu ('small' or 'large')
 * @param {Object} [props.style] - Ek stil özellikleri
 * @returns {JSX.Element} LoadingIndicator bileşeni
 */
const LoadingIndicator = ({
  color,
  size = 'large',
  style
}) => {
  // Varsayılan renk
  const safeColor = color || Colors.PRIMARY;

  // Beyaz renk
  const safeWhite = '#FFFFFF';

  return (
    <View style={[
      styles.container,
      { backgroundColor: safeWhite },
      style
    ]}>
      <ActivityIndicator size={size} color={safeColor} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'
  }
});

export default LoadingIndicator;
