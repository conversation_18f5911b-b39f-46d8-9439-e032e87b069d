import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Dimensions } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '../../context/ThemeContext';
import { useAppContext } from '../../context/AppContext';

const { width } = Dimensions.get('window');

/**
 * Özelleştirilebilir widget - Kullanıcının seçtiği içerikleri gösterir
 * Dashboard tarzı mini grafikler ve hızlı erişim butonları içerir
 *
 * @param {Object} props - Bileşen props'ları
 * @param {Object} props.quickStats - Hızlı istatistikler
 * @param {number} props.quickStats.thisMonth - Bu ay tutarı
 * @param {number} props.quickStats.lastWeek - Bu hafta tutarı
 * @param {string} props.quickStats.trend - Trend durumu ('up', 'down', 'neutral')
 * @param {Object} props.balanceData - Bakiye bilgileri
 * @param {Array} props.quickActions - Hızlı erişim butonları
 * @param {Function} props.onPress - Widget'a tıklandığında çalışacak fonksiyon
 * @param {Function} props.onQuickActionPress - Hızlı buton tıklandığında çalışacak fonksiyon
 * @param {Function} props.onCustomizePress - Özelleştir butonuna tıklandığında çalışacak fonksiyon
 * @returns {JSX.Element} CustomizableWidget bileşeni
 */
const CustomizableWidget = ({
  quickStats = { thisMonth: 0, lastWeek: 0, trend: 'neutral' },
  balanceData = { income: 0, expense: 0, balance: 0 },
  quickActions = [],
  onPress,
  onQuickActionPress,
  onCustomizePress,
}) => {
  const { theme } = useTheme();
  const { defaultCurrency } = useAppContext();
  const [selectedView, setSelectedView] = useState('stats'); // 'stats', 'actions', 'balance'

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: defaultCurrency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getTrendColor = (trend) => {
    switch (trend) {
      case 'up':
        return theme.DANGER; // Harcama artıyorsa kırmızı
      case 'down':
        return theme.SUCCESS; // Harcama azalıyorsa yeşil
      default:
        return theme.TEXT_SECONDARY;
    }
  };

  const getTrendIcon = (trend) => {
    switch (trend) {
      case 'up':
        return 'trending-up';
      case 'down':
        return 'trending-down';
      default:
        return 'trending-flat';
    }
  };

  const defaultQuickActions = [
    { id: 'add-income', title: 'Gelir', icon: 'add-circle', color: theme.SUCCESS },
    { id: 'add-expense', title: 'Gider', icon: 'remove-circle', color: theme.DANGER },
    { id: 'view-stats', title: 'Grafik', icon: 'bar-chart', color: theme.PRIMARY },
    { id: 'categories', title: 'Kategori', icon: 'category', color: theme.WARNING },
  ];

  const currentQuickActions = quickActions.length > 0 ? quickActions : defaultQuickActions;

  const renderStatsView = () => (
    <View style={styles.statsView}>
      <View style={styles.statsGrid}>
        <View style={[styles.statCard, { backgroundColor: theme.PRIMARY + '15' }]}>
          <MaterialIcons name="calendar-today" size={24} color={theme.PRIMARY} />
          <Text style={[styles.statLabel, { color: theme.TEXT_SECONDARY }]}>
            Bu Ay
          </Text>
          <Text style={[styles.statValue, { color: theme.PRIMARY }]}>
            {formatCurrency(quickStats.thisMonth)}
          </Text>
        </View>
        <View style={[styles.statCard, { backgroundColor: theme.WARNING + '15' }]}>
          <MaterialIcons name="date-range" size={24} color={theme.WARNING} />
          <Text style={[styles.statLabel, { color: theme.TEXT_SECONDARY }]}>
            Bu Hafta
          </Text>
          <Text style={[styles.statValue, { color: theme.WARNING }]}>
            {formatCurrency(quickStats.lastWeek)}
          </Text>
        </View>
      </View>
      <View style={[styles.trendCard, { backgroundColor: getTrendColor(quickStats.trend) + '15' }]}>
        <MaterialIcons
          name={getTrendIcon(quickStats.trend)}
          size={20}
          color={getTrendColor(quickStats.trend)}
        />
        <Text style={[styles.trendText, { color: getTrendColor(quickStats.trend) }]}>
          {quickStats.trend === 'up' ? 'Harcamalar artıyor' : 
           quickStats.trend === 'down' ? 'Harcamalar azalıyor' : 'Harcamalar sabit'}
        </Text>
      </View>
    </View>
  );

  const renderActionsView = () => (
    <View style={styles.actionsView}>
      <View style={styles.actionsGrid}>
        {currentQuickActions.slice(0, 4).map((action) => (
          <TouchableOpacity
            key={action.id}
            style={[styles.actionButton, { backgroundColor: action.color + '15' }]}
            onPress={() => onQuickActionPress?.(action.id.toUpperCase().replace('-', '_'))}
            activeOpacity={0.7}
          >
            <MaterialIcons name={action.icon} size={20} color={action.color} />
            <Text style={[styles.actionText, { color: theme.TEXT_PRIMARY }]}>
              {action.title}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderMiniChart = () => (
    <View style={styles.chartView}>
      <View style={styles.miniBalanceContainer}>
        <View style={styles.miniBalanceItem}>
          <MaterialIcons name="add-circle" size={24} color={theme.SUCCESS} />
          <Text style={[styles.miniBalanceLabel, { color: theme.TEXT_SECONDARY }]}>
            Gelir
          </Text>
          <Text style={[styles.miniBalanceValue, { color: theme.SUCCESS }]}>
            {formatCurrency(balanceData.income)}
          </Text>
        </View>
        <View style={styles.miniBalanceItem}>
          <MaterialIcons name="remove-circle" size={24} color={theme.DANGER} />
          <Text style={[styles.miniBalanceLabel, { color: theme.TEXT_SECONDARY }]}>
            Gider
          </Text>
          <Text style={[styles.miniBalanceValue, { color: theme.DANGER }]}>
            {formatCurrency(balanceData.expense)}
          </Text>
        </View>
        <View style={styles.miniBalanceItem}>
          <MaterialIcons name="account-balance-wallet" size={24} color={theme.PRIMARY} />
          <Text style={[styles.miniBalanceLabel, { color: theme.TEXT_SECONDARY }]}>
            Bakiye
          </Text>
          <Text style={[styles.miniBalanceValue, { color: theme.PRIMARY }]}>
            {formatCurrency(balanceData.balance)}
          </Text>
        </View>
      </View>
    </View>
  );

  const renderViewTabs = () => (
    <View style={[styles.tabContainer, { backgroundColor: theme.BACKGROUND }]}>
      <TouchableOpacity
        style={[
          styles.tab,
          selectedView === 'stats' && { backgroundColor: theme.PRIMARY + '15' }
        ]}
        onPress={() => setSelectedView('stats')}
        activeOpacity={0.7}
      >
        <MaterialIcons
          name="bar-chart"
          size={18}
          color={selectedView === 'stats' ? theme.PRIMARY : theme.TEXT_SECONDARY}
        />
        <Text style={[
          styles.tabText,
          { color: selectedView === 'stats' ? theme.PRIMARY : theme.TEXT_SECONDARY }
        ]}>
          İstatistik
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[
          styles.tab,
          selectedView === 'actions' && { backgroundColor: theme.PRIMARY + '15' }
        ]}
        onPress={() => setSelectedView('actions')}
        activeOpacity={0.7}
      >
        <MaterialIcons
          name="dashboard"
          size={18}
          color={selectedView === 'actions' ? theme.PRIMARY : theme.TEXT_SECONDARY}
        />
        <Text style={[
          styles.tabText,
          { color: selectedView === 'actions' ? theme.PRIMARY : theme.TEXT_SECONDARY }
        ]}>
          Hızlı Erişim
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[
          styles.tab,
          selectedView === 'mini-chart' && { backgroundColor: theme.PRIMARY + '15' }
        ]}
        onPress={() => setSelectedView('mini-chart')}
        activeOpacity={0.7}
      >
        <MaterialIcons
          name="account-balance-wallet"
          size={18}
          color={selectedView === 'mini-chart' ? theme.PRIMARY : theme.TEXT_SECONDARY}
        />
        <Text style={[
          styles.tabText,
          { color: selectedView === 'mini-chart' ? theme.PRIMARY : theme.TEXT_SECONDARY }
        ]}>
          Bakiye
        </Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <TouchableOpacity
      style={[styles.container, { backgroundColor: theme.CARD }]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <View style={[styles.header, { borderBottomColor: theme.BORDER }]}>
        <View style={styles.headerLeft}>
          <MaterialIcons name="dashboard" size={24} color={theme.PRIMARY} />
          <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
            Özel Dashboard
          </Text>
        </View>
        <TouchableOpacity
          style={styles.customizeButton}
          onPress={onCustomizePress}
          activeOpacity={0.7}
        >
          <MaterialIcons name="settings" size={20} color={theme.TEXT_SECONDARY} />
        </TouchableOpacity>
      </View>

      {renderViewTabs()}

      <View style={styles.content}>
        {selectedView === 'stats' && renderStatsView()}
        {selectedView === 'actions' && renderActionsView()}
        {selectedView === 'mini-chart' && renderMiniChart()}
      </View>

      <View style={styles.footer}>
        <Text style={[styles.footerText, { color: theme.TEXT_SECONDARY }]}>
          Özelleştirilebilir widget
        </Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  customizeButton: {
    padding: 4,
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: 8,
    paddingVertical: 8,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 4,
    borderRadius: 8,
    marginHorizontal: 2,
  },
  tabText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  content: {
    minHeight: 120,
  },
  // Stats View
  statsView: {
    padding: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  statCard: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 4,
  },
  statLabel: {
    fontSize: 12,
    marginTop: 4,
    marginBottom: 2,
  },
  statValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  trendCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 8,
    borderRadius: 8,
  },
  trendText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  // Actions View
  actionsView: {
    padding: 16,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionButton: {
    width: '48%',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  actionText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 8,
    flex: 1,
  },
  // Chart View
  chartView: {
    padding: 16,
  },
  miniBalanceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  miniBalanceItem: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 12,
  },
  miniBalanceLabel: {
    fontSize: 10,
    marginTop: 4,
    marginBottom: 2,
  },
  miniBalanceValue: {
    fontSize: 12,
    fontWeight: '600',
  },
  footer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 12,
    fontStyle: 'italic',
  },
});

export default CustomizableWidget;
