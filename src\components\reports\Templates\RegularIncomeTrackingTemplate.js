import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useTheme } from '../../../context/ThemeContext';
import { useDataIntegration } from '../../../context/DataIntegrationContext';
import { formatCurrency } from '../../../utils/formatters';
import { ExportManager } from '../Export';

/**
 * <PERSON><PERSON>zenli Gelir Takip Şablonu
 * Ma<PERSON>ş ve düzenli gelir kaynaklarının takip raporu - Real Data Integration ile güncellenmiş
 */
const RegularIncomeTrackingTemplate = ({ 
  templateConfig, 
  customParams, 
  onExport, 
  onSave 
}) => {
  const { theme } = useTheme();
  const { getRegularIncomeTracking, loading, error } = useDataIntegration();
  const [reportData, setReportData] = useState(null);

  /**
   * Get safe theme value
   * @param {string} property - Theme property
   * @param {string} fallback - Fallback value
   * @returns {string} Safe theme value
   */
  const getSafeThemeValue = (property, fallback) => {
    return theme?.[property] || theme?.colors?.[property.toLowerCase()] || fallback;
  };

  // Load report data on component mount
  useEffect(() => {
    loadReportData();
  }, [customParams]);

  /**
   * Load regular income tracking data
   */
  const loadReportData = async () => {
    try {
      const data = await getRegularIncomeTracking(customParams);
      setReportData(data);
    } catch (err) {
      // Error is handled by DataIntegrationContext
      setReportData(null);
    }
  };

  // Loading state
  if (loading && !reportData) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: getSafeThemeValue('BACKGROUND', '#FFFFFF') }]}>
        <ActivityIndicator size="large" color={getSafeThemeValue('PRIMARY', '#007AFF')} />
        <Text style={[styles.loadingText, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
          Düzenli gelir verileri yükleniyor...
        </Text>
      </View>
    );
  }

  // Error state
  if (error && !reportData) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: getSafeThemeValue('BACKGROUND', '#FFFFFF') }]}>
        <Text style={[styles.errorText, { color: getSafeThemeValue('ERROR', '#FF3B30') }]}>
          ⚠️ Veri Yüklenemedi
        </Text>
        <Text style={[styles.errorDetail, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
          {error}
        </Text>
        <TouchableOpacity
          style={[styles.retryButton, { backgroundColor: getSafeThemeValue('PRIMARY', '#007AFF') }]}
          onPress={loadReportData}
        >
          <Text style={[styles.retryButtonText, { color: getSafeThemeValue('WHITE', '#FFFFFF') }]}>
            Tekrar Dene
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Empty state
  if (!reportData || (!reportData.transactions?.length && !reportData.workPayments?.length)) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: getSafeThemeValue('BACKGROUND', '#FFFFFF') }]}>
        <Text style={[styles.emptyTitle, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
          💼 Düzenli Gelir Bulunamadı
        </Text>
        <Text style={[styles.emptySubtitle, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
          Seçilen dönemde düzenli gelir kaydı bulunamadı.
        </Text>
        <Text style={[styles.emptyHint, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
          Maaş ve düzenli gelir kayıtları ekleyerek bu raporu görüntüleyebilirsiniz.
        </Text>
      </View>
    );
  }

  const renderSummaryCard = () => (
    <View style={[styles.summaryCard, { backgroundColor: getSafeThemeValue('CARD_BACKGROUND', '#F8F9FA') }]}>
      <Text style={[styles.cardTitle, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
        📊 Düzenli Gelir Özeti
      </Text>
      <View style={styles.summaryGrid}>
        <View style={styles.summaryItem}>
          <Text style={[styles.summaryLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
            Toplam Gelir
          </Text>
          <Text style={[styles.summaryValue, { color: getSafeThemeValue('SUCCESS', '#28A745') }]}>
            {formatCurrency(reportData.summary.totalIncome)}
          </Text>
        </View>
        <View style={styles.summaryItem}>
          <Text style={[styles.summaryLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
            Aylık Ortalama
          </Text>
          <Text style={[styles.summaryValue, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
            {formatCurrency(reportData.summary.monthlyAverage)}
          </Text>
        </View>
        <View style={styles.summaryItem}>
          <Text style={[styles.summaryLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
            Beklenen Gelir
          </Text>
          <Text style={[styles.summaryValue, { color: getSafeThemeValue('INFO', '#17A2B8') }]}>
            {formatCurrency(reportData.summary.expectedIncome)}
          </Text>
        </View>
        <View style={styles.summaryItem}>
          <Text style={[styles.summaryLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
            Fark
          </Text>
          <Text style={[
            styles.summaryValue, 
            { color: reportData.summary.varianceFromExpected >= 0 ? 
              getSafeThemeValue('SUCCESS', '#28A745') : 
              getSafeThemeValue('ERROR', '#FF3B30') 
            }
          ]}>
            {formatCurrency(reportData.summary.varianceFromExpected)}
          </Text>
        </View>
      </View>
    </View>
  );

  const renderSettingsCard = () => (
    <View style={[styles.summaryCard, { backgroundColor: getSafeThemeValue('CARD_BACKGROUND', '#F8F9FA') }]}>
      <Text style={[styles.cardTitle, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
        ⚙️ Çalışma Ayarları
      </Text>
      <View style={styles.settingsGrid}>
        <View style={styles.settingItem}>
          <Text style={[styles.settingLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
            Saatlik Ücret
          </Text>
          <Text style={[styles.settingValue, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
            {formatCurrency(reportData.settings.hourlyRate)}/saat
          </Text>
        </View>
        <View style={styles.settingItem}>
          <Text style={[styles.settingLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
            Haftalık Saat
          </Text>
          <Text style={[styles.settingValue, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
            {reportData.settings.weeklyWorkHours} saat
          </Text>
        </View>
        <View style={styles.settingItem}>
          <Text style={[styles.settingLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
            Günlük Saat
          </Text>
          <Text style={[styles.settingValue, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
            {reportData.settings.dailyWorkHours} saat
          </Text>
        </View>
        <View style={styles.settingItem}>
          <Text style={[styles.settingLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
            Para Birimi
          </Text>
          <Text style={[styles.settingValue, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
            {reportData.settings.currency}
          </Text>
        </View>
      </View>
    </View>
  );

  const renderTransactionsList = () => (
    <View style={[styles.summaryCard, { backgroundColor: getSafeThemeValue('CARD_BACKGROUND', '#F8F9FA') }]}>
      <Text style={[styles.cardTitle, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
        📋 Düzenli Gelir Kayıtları
      </Text>
      {reportData.transactions.map((transaction, index) => (
        <View key={index} style={[styles.transactionItem, { borderBottomColor: getSafeThemeValue('BORDER', '#E5E5E5') }]}>
          <View style={styles.transactionInfo}>
            <Text style={[styles.transactionDate, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
              {new Date(transaction.date).toLocaleDateString('tr-TR')}
            </Text>
            <Text style={[styles.transactionDesc, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
              {transaction.description}
            </Text>
            <Text style={[styles.transactionCategory, { color: transaction.color }]}>
              {transaction.category}
            </Text>
          </View>
          <Text style={[styles.transactionAmount, { color: getSafeThemeValue('SUCCESS', '#28A745') }]}>
            {formatCurrency(transaction.amount)}
          </Text>
        </View>
      ))}
    </View>
  );

  const renderWorkPaymentsList = () => (
    <View style={[styles.summaryCard, { backgroundColor: getSafeThemeValue('CARD_BACKGROUND', '#F8F9FA') }]}>
      <Text style={[styles.cardTitle, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
        💰 Çalışma Ödemeleri
      </Text>
      {reportData.workPayments.map((payment, index) => (
        <View key={index} style={[styles.paymentItem, { borderBottomColor: getSafeThemeValue('BORDER', '#E5E5E5') }]}>
          <View style={styles.paymentInfo}>
            <Text style={[styles.paymentPeriod, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
              {new Date(payment.periodStart).toLocaleDateString('tr-TR')} - {new Date(payment.periodEnd).toLocaleDateString('tr-TR')}
            </Text>
            <Text style={[styles.paymentHours, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
              {payment.hours} saat
            </Text>
            <Text style={[styles.paymentStatus, { 
              color: payment.isPaid ? getSafeThemeValue('SUCCESS', '#28A745') : getSafeThemeValue('WARNING', '#FFC107') 
            }]}>
              {payment.isPaid ? '✅ Ödendi' : '⏳ Bekliyor'}
            </Text>
          </View>
          <View style={styles.paymentAmount}>
            <Text style={[styles.paymentAmountText, { color: getSafeThemeValue('SUCCESS', '#28A745') }]}>
              {formatCurrency(payment.amount)}
            </Text>
            {payment.paymentDate && (
              <Text style={[styles.paymentDate, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
                {new Date(payment.paymentDate).toLocaleDateString('tr-TR')}
              </Text>
            )}
          </View>
        </View>
      ))}
    </View>
  );

  return (
    <ScrollView style={[styles.container, { backgroundColor: getSafeThemeValue('BACKGROUND', '#FFFFFF') }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
          💼 Düzenli Gelir Takip Raporu
        </Text>
        <Text style={[styles.subtitle, { color: getSafeThemeValue('TEXT_SECONDARY', '#666666') }]}>
          {reportData.period}
        </Text>
      </View>

      {renderSummaryCard()}
      {renderSettingsCard()}
      
      {reportData.transactions?.length > 0 && renderTransactionsList()}
      {reportData.workPayments?.length > 0 && renderWorkPaymentsList()}

      {(onExport || onSave) && (
        <View style={styles.actions}>
          <ExportManager 
            reportData={reportData}
            reportTitle="Düzenli Gelir Takip Raporu"
            reportType="regular_income_tracking"
            buttonStyle={styles.exportButton}
            buttonTextStyle={styles.exportButtonText}
            theme={theme}
          />
          
          {onSave && (
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: getSafeThemeValue('SECONDARY', '#6C757D') }]}
              onPress={() => onSave(reportData)}
            >
              <Text style={[styles.actionButtonText, { color: getSafeThemeValue('WHITE', '#FFFFFF') }]}>
                � Kaydet
              </Text>
            </TouchableOpacity>
          )}
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  header: {
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    textAlign: 'center',
  },
  errorText: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  errorDetail: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 8,
  },
  emptyHint: {
    fontSize: 14,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  summaryCard: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  summaryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  summaryItem: {
    width: '48%',
    marginBottom: 16,
  },
  summaryLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  settingsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  settingItem: {
    width: '48%',
    marginBottom: 12,
  },
  settingLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  settingValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  transactionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionDate: {
    fontSize: 12,
    marginBottom: 2,
  },
  transactionDesc: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  transactionCategory: {
    fontSize: 12,
    fontWeight: '500',
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  paymentItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  paymentInfo: {
    flex: 1,
  },
  paymentPeriod: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  paymentHours: {
    fontSize: 12,
    marginBottom: 4,
  },
  paymentStatus: {
    fontSize: 12,
    fontWeight: '500',
  },
  paymentAmount: {
    alignItems: 'flex-end',
  },
  paymentAmountText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  paymentDate: {
    fontSize: 12,
    marginTop: 2,
  },
  actions: {
    flexDirection: 'column',
    justifyContent: 'space-around',
    padding: 20,
    gap: 12,
  },
  exportButton: {
    marginBottom: 8,
  },
  exportButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  actionButton: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    minWidth: 120,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

export default RegularIncomeTrackingTemplate;
