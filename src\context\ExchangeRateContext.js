import React, { createContext, useState, useContext, useEffect, useCallback } from 'react';
import { useSQLiteContext } from 'expo-sqlite';
import * as exchangeRateService from '../services/simpleExchangeRateService';

// Context oluştur
const ExchangeRateContext = createContext();

/**
 * Döviz kuru context sağlayıcısı
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {React.ReactNode} props.children - Alt bileşenler
 * @returns {JSX.Element} ExchangeRateProvider bileşeni
 */
export const ExchangeRateProvider = ({ children }) => {
  const db = useSQLiteContext();

  // Durum değişkenleri
  const [rates, setRates] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdate, setLastUpdate] = useState(null);
  const [baseCurrencies, setBaseCurrencies] = useState(['TRY', 'USD', 'EUR']);

  // Döviz kurlarını yükle
  const loadExchangeRates = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const allRates = {};

      // Her bir baz para birimi için kurları al
      for (const currency of baseCurrencies) {
        const ratesData = await exchangeRateService.getExchangeRates(db, currency);
        allRates[currency] = ratesData;
      }

      setRates(allRates);

      // Son güncelleme tarihini al
      const lastUpdateDate = await exchangeRateService.getLastUpdateDate(db, 'TRY');
      setLastUpdate(lastUpdateDate);

      setLoading(false);
    } catch (err) {
      console.error('Döviz kurları yükleme hatası:', err);
      setError('Döviz kurları yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  }, [db, baseCurrencies]);

  // Döviz kurlarını güncelle
  const updateExchangeRates = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Döviz kurlarını güncelle
      await exchangeRateService.updateExchangeRates(db, baseCurrencies);

      // Güncel kurları yükle
      await loadExchangeRates();

      return true;
    } catch (err) {
      console.error('Döviz kurları güncelleme hatası:', err);
      setError('Döviz kurları güncellenirken bir hata oluştu.');
      setLoading(false);
      return false;
    }
  }, [db, baseCurrencies, loadExchangeRates]);

  // Para birimi dönüşümü yap
  const convertCurrency = useCallback(async (amount, fromCurrency, toCurrency, date = null) => {
    if (!amount || isNaN(parseFloat(amount))) {
      return 0;
    }

    try {
      const result = await exchangeRateService.convertCurrency(
        db,
        parseFloat(amount),
        fromCurrency,
        toCurrency,
        date
      );

      return result;
    } catch (err) {
      console.error('Para birimi dönüştürme hatası:', err);
      return amount; // Hata durumunda orijinal miktarı döndür
    }
  }, [db]);

  // Belirli bir para birimi için döviz kurunu al
  const getExchangeRate = useCallback((fromCurrency, toCurrency) => {
    if (!rates || !rates[fromCurrency]) {
      return null;
    }

    return rates[fromCurrency][toCurrency] || null;
  }, [rates]);

  // Desteklenen para birimlerini al
  const getSupportedCurrencies = useCallback(() => {
    return [
      { code: 'TRY', name: 'Türk Lirası', symbol: '₺' },
      { code: 'USD', name: 'Amerikan Doları', symbol: '$' },
      { code: 'EUR', name: 'Euro', symbol: '€' },
      { code: 'GBP', name: 'İngiliz Sterlini', symbol: '£' },
      { code: 'JPY', name: 'Japon Yeni', symbol: '¥' },
      { code: 'CHF', name: 'İsviçre Frangı', symbol: 'CHF' },
      { code: 'CAD', name: 'Kanada Doları', symbol: 'C$' },
      { code: 'AUD', name: 'Avustralya Doları', symbol: 'A$' },
      { code: 'CNY', name: 'Çin Yuanı', symbol: '¥' },
      { code: 'RUB', name: 'Rus Rublesi', symbol: '₽' }
    ];
  }, []);

  // İlk yükleme
  useEffect(() => {
    loadExchangeRates();
  }, [loadExchangeRates]);

  // Context değerleri
  const value = {
    rates,
    loading,
    error,
    lastUpdate,
    baseCurrencies,
    loadExchangeRates,
    updateExchangeRates,
    convertCurrency,
    getExchangeRate,
    getSupportedCurrencies,
    setBaseCurrencies
  };

  return (
    <ExchangeRateContext.Provider value={value}>
      {children}
    </ExchangeRateContext.Provider>
  );
};

/**
 * Döviz kuru context hook'u
 *
 * @returns {Object} Döviz kuru context değerleri
 */
export const useExchangeRate = () => {
  const context = useContext(ExchangeRateContext);

  if (!context) {
    throw new Error('useExchangeRate must be used within an ExchangeRateProvider');
  }

  return context;
};

export default ExchangeRateContext;
