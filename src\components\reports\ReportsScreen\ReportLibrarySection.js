import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  TextInput,
} from 'react-native';

/**
 * Report Library Section Component
 * Displays categorized reports with search and filter capabilities
 * Follows REPORTS_SCREEN_REDESIGN_PLAN.md specifications
 */
const ReportLibrarySection = ({ 
  reports = [],
  categories = [],
  selectedCategory = 'all',
  onCategorySelect,
  onReportPress,
  onReportEdit,
  onReportDelete,
  onReportFavorite,
  theme 
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearch, setShowSearch] = useState(false);

  // Filter reports based on category and search
  const filteredReports = reports.filter(report => {
    const matchesCategory = selectedCategory === 'all' || report.category === selectedCategory;
    const matchesSearch = !searchQuery || 
      report.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      report.description?.toLowerCase().includes(searchQuery.toLowerCase());
    
    return matchesCategory && matchesSearch;
  });

  // Group reports by category
  const groupedReports = filteredReports.reduce((acc, report) => {
    const category = report.category || 'Diğer';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(report);
    return acc;
  }, {});

  return (
    <View style={[styles.container, { backgroundColor: theme.SURFACE }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
          📚 Rapor Kütüphanesi
        </Text>
        
        <TouchableOpacity 
          style={[styles.searchButton, { backgroundColor: theme.BACKGROUND }]}
          onPress={() => setShowSearch(!showSearch)}
        >
          <Text style={[styles.searchButtonText, { color: theme.TEXT_PRIMARY }]}>
            🔍
          </Text>
        </TouchableOpacity>
      </View>

      {/* Search Bar */}
      {showSearch && (
        <View style={[styles.searchContainer, { backgroundColor: theme.BACKGROUND }]}>
          <TextInput
            style={[styles.searchInput, { color: theme.TEXT_PRIMARY }]}
            placeholder="Rapor ara..."
            placeholderTextColor={theme.TEXT_SECONDARY}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
      )}

      {/* Category Filters */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.categoriesScrollView}
      >
        <TouchableOpacity
          style={[
            styles.categoryChip,
            { backgroundColor: selectedCategory === 'all' ? theme.PRIMARY : theme.BACKGROUND }
          ]}
          onPress={() => onCategorySelect?.('all')}
        >
          <Text style={[
            styles.categoryChipText,
            { color: selectedCategory === 'all' ? theme.SURFACE : theme.TEXT_PRIMARY }
          ]}>
            Hepsi ({reports.length})
          </Text>
        </TouchableOpacity>

        {categories.map((category) => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryChip,
              { backgroundColor: selectedCategory === category.id ? theme.PRIMARY : theme.BACKGROUND }
            ]}
            onPress={() => onCategorySelect?.(category.id)}
          >
            <Text style={[
              styles.categoryChipText,
              { color: selectedCategory === category.id ? theme.SURFACE : theme.TEXT_PRIMARY }
            ]}>
              {category.icon} {category.name} ({category.count || 0})
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Reports List */}
      <ScrollView style={styles.reportsContainer} showsVerticalScrollIndicator={false}>
        {Object.keys(groupedReports).length === 0 ? (
          <View style={styles.emptyContainer}>
            <Text style={[styles.emptyText, { color: theme.TEXT_SECONDARY }]}>
              {searchQuery ? 'Arama kriterlerine uygun rapor bulunamadı' : 'Henüz rapor bulunmuyor'}
            </Text>
          </View>
        ) : (
          Object.entries(groupedReports).map(([categoryName, categoryReports]) => (
            <View key={categoryName} style={styles.categorySection}>
              <Text style={[styles.categoryTitle, { color: theme.TEXT_PRIMARY }]}>
                {categoryName} ({categoryReports.length})
              </Text>
              
              {categoryReports.map((report) => (
                <TouchableOpacity
                  key={report.id}
                  style={[styles.reportItem, { backgroundColor: theme.BACKGROUND }]}
                  onPress={() => onReportPress?.(report)}
                >
                  <View style={styles.reportContent}>
                    <View style={styles.reportHeader}>
                      <Text style={[styles.reportIcon, { color: theme.PRIMARY }]}>
                        {report.icon || '📊'}
                      </Text>
                      <View style={styles.reportInfo}>
                        <Text style={[styles.reportName, { color: theme.TEXT_PRIMARY }]}>
                          {report.name}
                        </Text>
                        <Text style={[styles.reportDescription, { color: theme.TEXT_SECONDARY }]}>
                          {report.description || 'Açıklama yok'}
                        </Text>
                      </View>
                      <TouchableOpacity
                        style={styles.favoriteButton}
                        onPress={() => onReportFavorite?.(report)}
                      >
                        <Text style={[styles.favoriteIcon, { color: report.isFavorite ? theme.WARNING : theme.TEXT_SECONDARY }]}>
                          {report.isFavorite ? '⭐' : '☆'}
                        </Text>
                      </TouchableOpacity>
                    </View>
                    
                    <View style={styles.reportMeta}>
                      <Text style={[styles.reportDate, { color: theme.TEXT_SECONDARY }]}>
                        Son görüntüleme: {report.lastViewed || 'Hiç'}
                      </Text>
                      <Text style={[styles.reportViews, { color: theme.TEXT_SECONDARY }]}>
                        {report.viewCount || 0} görüntüleme
                      </Text>
                    </View>
                  </View>

                  <View style={styles.reportActions}>
                    <TouchableOpacity
                      style={[styles.actionButton, { backgroundColor: theme.SUCCESS }]}
                      onPress={() => onReportPress?.(report)}
                    >
                      <Text style={[styles.actionButtonText, { color: theme.SURFACE }]}>
                        👁
                      </Text>
                    </TouchableOpacity>
                    
                    <TouchableOpacity
                      style={[styles.actionButton, { backgroundColor: theme.WARNING }]}
                      onPress={() => onReportEdit?.(report)}
                    >
                      <Text style={[styles.actionButtonText, { color: theme.SURFACE }]}>
                        ✏️
                      </Text>
                    </TouchableOpacity>
                    
                    <TouchableOpacity
                      style={[styles.actionButton, { backgroundColor: theme.ERROR }]}
                      onPress={() => onReportDelete?.(report)}
                    >
                      <Text style={[styles.actionButtonText, { color: theme.SURFACE }]}>
                        🗑️
                      </Text>
                    </TouchableOpacity>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          ))
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    paddingBottom: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  searchButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchButtonText: {
    fontSize: 16,
  },
  searchContainer: {
    marginHorizontal: 16,
    marginBottom: 12,
    borderRadius: 8,
    paddingHorizontal: 12,
  },
  searchInput: {
    height: 40,
    fontSize: 16,
  },
  categoriesScrollView: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  categoryChip: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
  },
  categoryChipText: {
    fontSize: 12,
    fontWeight: '600',
  },
  reportsContainer: {
    maxHeight: 400,
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  emptyContainer: {
    padding: 40,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
  },
  categorySection: {
    marginBottom: 16,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  reportItem: {
    flexDirection: 'row',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  reportContent: {
    flex: 1,
  },
  reportHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  reportIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  reportInfo: {
    flex: 1,
  },
  reportName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  reportDescription: {
    fontSize: 14,
  },
  favoriteButton: {
    padding: 4,
  },
  favoriteIcon: {
    fontSize: 16,
  },
  reportMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 32,
  },
  reportDate: {
    fontSize: 12,
  },
  reportViews: {
    fontSize: 12,
  },
  reportActions: {
    flexDirection: 'column',
    justifyContent: 'center',
    marginLeft: 8,
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 4,
  },
  actionButtonText: {
    fontSize: 12,
  },
});

export default ReportLibrarySection;
