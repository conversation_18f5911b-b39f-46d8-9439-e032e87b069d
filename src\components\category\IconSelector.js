import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Modal,
  TextInput
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../../constants/colors';
import { useTheme } from '../../context/ThemeContext';

// Kategori ikonları
const CATEGORY_ICONS = [
  // Gider kategorileri için ikonlar
  'shopping-cart', 'restaurant', 'fastfood', 'local-cafe', 'local-bar',
  'directions-car', 'local-gas-station', 'commute', 'directions-bus', 'train',
  'home', 'weekend', 'tv', 'smartphone', 'laptop', 'headset',
  'fitness-center', 'sports-basketball', 'sports-football', 'sports-soccer',
  'movie', 'theaters', 'music-note', 'videogame-asset', 'casino',
  'school', 'book', 'menu-book', 'auto-stories', 'library-books',
  'medical-services', 'local-hospital', 'healing', 'medication',
  'checkroom', 'dry-cleaning', 'iron', 'style',
  'pets', 'child-care', 'family-restroom', 'elderly',
  'credit-card', 'account-balance', 'account-balance-wallet', 'payments',
  'receipt', 'receipt-long', 'description', 'attach-money',
  'flight', 'hotel', 'beach-access', 'luggage',
  'local-grocery-store', 'local-mall', 'local-florist', 'local-laundry-service',
  'local-offer', 'redeem', 'card-giftcard', 'celebration',
  'cake', 'liquor', 'local-dining', 'bakery-dining',
  'wifi', 'phone', 'phone-android', 'phone-iphone',
  'build', 'handyman', 'construction', 'plumbing',
  'cleaning-services', 'wash', 'bathtub', 'shower',
  'sports', 'fitness-center', 'golf-course', 'pool',

  // Gelir kategorileri için ikonlar
  'work', 'business-center', 'work-outline', 'badge',
  'attach-money', 'money', 'account-balance', 'savings',
  'card-giftcard', 'redeem', 'loyalty', 'local-activity',
  'trending-up', 'show-chart', 'pie-chart', 'insert-chart',
  'real-estate-agent', 'apartment', 'house', 'villa',
  'payments', 'credit-score', 'account-balance-wallet', 'receipt-long',
  'sell', 'store', 'storefront', 'shopping-bag',
  'handshake', 'support-agent', 'groups', 'diversity-3',
];

/**
 * İkon seçici bileşeni
 *
 * @param {Object} props - Bileşen props'ları
 * @param {boolean} props.visible - Modal görünürlüğü
 * @param {string} props.selectedIcon - Seçili ikon
 * @param {Function} props.onSelectIcon - İkon seçildiğinde çağrılacak fonksiyon
 * @param {Function} props.onClose - Modal kapatıldığında çağrılacak fonksiyon
 * @param {string} props.color - İkon rengi
 * @returns {JSX.Element} İkon seçici bileşeni
 */
const IconSelector = ({ visible, selectedIcon, onSelectIcon, onClose, color = Colors.PRIMARY }) => {
  const { theme } = useTheme();
  const [searchText, setSearchText] = useState('');

  // Arama filtresine göre ikonları filtrele
  const filteredIcons = searchText
    ? CATEGORY_ICONS.filter(icon => icon.includes(searchText.toLowerCase()))
    : CATEGORY_ICONS;

  // İkon öğesi
  const renderIconItem = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.iconItem,
        { backgroundColor: theme.SURFACE_VARIANT },
        selectedIcon === item && styles.selectedIconItem
      ]}
      onPress={() => onSelectIcon(item)}
    >
      <MaterialIcons
        name={item}
        size={28}
        color={selectedIcon === item ? '#fff' : color}
      />
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={visible}
      animationType="fade"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContainer, { backgroundColor: theme.SURFACE }]}>
          {/* Başlık */}
          <View style={[styles.header, { borderBottomColor: theme.BORDER }]}>
            <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>İkon Seç</Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={onClose}
            >
              <MaterialIcons name="close" size={24} color={theme.TEXT_PRIMARY} />
            </TouchableOpacity>
          </View>

          {/* Arama */}
          <View style={[styles.searchContainer, { backgroundColor: theme.SURFACE_VARIANT, borderBottomColor: theme.BORDER }]}>
            <MaterialIcons name="search" size={20} color={theme.TEXT_SECONDARY} />
            <TextInput
              style={[styles.searchInput, { color: theme.TEXT_PRIMARY }]}
              value={searchText}
              onChangeText={setSearchText}
              placeholder="İkon ara..."
              placeholderTextColor={theme.TEXT_SECONDARY}
            />
            {searchText ? (
              <TouchableOpacity
                style={styles.clearButton}
                onPress={() => setSearchText('')}
              >
                <MaterialIcons name="clear" size={20} color={theme.TEXT_SECONDARY} />
              </TouchableOpacity>
            ) : null}
          </View>

          {/* İkon Listesi */}
          <FlatList
            data={filteredIcons}
            renderItem={renderIconItem}
            keyExtractor={(item) => item}
            numColumns={4}
            contentContainerStyle={styles.iconList}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <MaterialIcons name="search-off" size={48} color={theme.TEXT_SECONDARY} />
                <Text style={[styles.emptyText, { color: theme.TEXT_SECONDARY }]}>Sonuç bulunamadı</Text>
              </View>
            }
          />

          {/* Alt Butonlar */}
          <View style={[styles.footer, { borderTopColor: theme.BORDER }]}>
            <TouchableOpacity
              style={[styles.cancelButton, { backgroundColor: theme.SURFACE_VARIANT }]}
              onPress={onClose}
            >
              <Text style={[styles.cancelButtonText, { color: theme.TEXT_PRIMARY }]}>Kapat</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 16,
  },
  modalContainer: {
    width: '90%',
    maxHeight: '80%',
    borderRadius: 12,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  closeButton: {
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderBottomWidth: 1,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    marginLeft: 8,
  },
  clearButton: {
    padding: 4,
  },
  iconList: {
    padding: 12,
  },
  iconItem: {
    width: '23%',
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
    margin: '1%',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  selectedIconItem: {
    backgroundColor: Colors.PRIMARY,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
    marginTop: 24,
  },
  emptyText: {
    fontSize: 16,
    marginTop: 12,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 12,
    borderTopWidth: 1,
  },
  cancelButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 4,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
});

export default IconSelector;
