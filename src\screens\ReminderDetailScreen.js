import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Share,
  Modal
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import { format, parseISO, differenceInDays } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Colors } from '../constants/colors';
import * as reminderService from '../services/reminderService';
import * as reminderShareService from '../services/reminderShareService';
import * as reminderTagService from '../services/reminderTagService';
import * as expenseReminderService from '../services/expenseReminderService';
import * as exchangeRateService from '../services/exchangeRateService';

/**
 * Hatırlatıcı Detay Ekranı
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @param {Object} props.route - Route nesnesi
 * @returns {JSX.Element} Hatırlatıcı Detay Ekranı
 */
export default function ReminderDetailScreen({ navigation, route }) {
  const db = useSQLiteContext();
  const { reminderId } = route?.params || {};

  // Durum
  const [reminder, setReminder] = useState(null);
  const [loading, setLoading] = useState(true);
  const [category, setCategory] = useState(null);
  const [tags, setTags] = useState([]);

  // Verileri yükle
  const loadData = useCallback(async () => {
    try {
      setLoading(true);

      // Hatırlatıcı detaylarını getir
      const reminderDetails = await reminderService.getReminderById(db, reminderId);

      // Harcama hatırlatıcısı mı kontrol et
      if (reminderDetails?.data?.isExpenseReminder) {
        // Harcama hatırlatıcısı detaylarını getir
        const expenseReminderDetails = await expenseReminderService.getExpenseReminderById(db, reminderId);
        if (expenseReminderDetails) {
          // Hatırlatıcı nesnesini güncelle
          reminderDetails.expense_type = expenseReminderDetails.expense_type;
          reminderDetails.amount = expenseReminderDetails.amount;
          reminderDetails.currency = expenseReminderDetails.currency;
          reminderDetails.due_date = expenseReminderDetails.due_date;
          reminderDetails.remind_days_before = expenseReminderDetails.remind_days_before;
        }
      }

      setReminder(reminderDetails);

      // Kategori bilgilerini getir
      if (reminderDetails?.category_id) {
        const categoryData = await db.getFirstAsync(`
          SELECT * FROM categories WHERE id = ?
        `, [reminderDetails.category_id]);

        setCategory(categoryData);
      }

      // Grup bilgilerini getir
      if (reminderDetails?.group_id) {
        const groupData = await db.getFirstAsync(`
          SELECT * FROM reminder_groups WHERE id = ?
        `, [reminderDetails.group_id]);

        // Hatırlatıcı nesnesine grup bilgilerini ekle
        if (groupData) {
          reminderDetails.group_name = groupData.name;
          reminderDetails.group_color = groupData.color;
          reminderDetails.group_icon = groupData.icon;
        }
      }

      // Etiketleri getir
      const tagData = await reminderTagService.getTagsByReminderId(db, reminderId);
      setTags(tagData);

      setLoading(false);
    } catch (error) {
      console.error('Hatırlatıcı detayları yükleme hatası:', error);
      Alert.alert('Hata', 'Hatırlatıcı detayları yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  }, [reminderId, db]);

  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [loadData])
  );

  // Hatırlatıcıyı düzenle
  const editReminder = () => {
    // Harcama hatırlatıcısı mı kontrol et
    if (reminder?.data?.isExpenseReminder) {
      navigation.navigate('ExpenseReminderForm', { reminderId });
    } else {
      navigation.navigate('ReminderForm', { reminderId });
    }
  };

  // Hatırlatıcıyı sil
  const deleteReminder = () => {
    Alert.alert(
      'Hatırlatıcıyı Sil',
      'Bu hatırlatıcıyı silmek istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await reminderService.deleteReminder(db, reminderId);
              Alert.alert('Başarılı', 'Hatırlatıcı silindi.');
              navigation.goBack();
            } catch (error) {
              console.error('Hatırlatıcı silme hatası:', error);
              Alert.alert('Hata', 'Hatırlatıcı silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };

  // Hatırlatıcıyı etkinleştir/devre dışı bırak
  const toggleEnabled = async () => {
    try {
      const newState = reminder.is_enabled === 0 || reminder.is_enabled === false;
      await reminderService.toggleReminderEnabled(db, reminderId, newState);

      // Durumu güncelle
      setReminder({
        ...reminder,
        is_enabled: newState ? 1 : 0
      });

      Alert.alert(
        'Başarılı',
        newState ? 'Hatırlatıcı etkinleştirildi.' : 'Hatırlatıcı devre dışı bırakıldı.'
      );
    } catch (error) {
      console.error('Hatırlatıcı etkinleştirme hatası:', error);
      Alert.alert('Hata', 'Hatırlatıcı durumu değiştirilirken bir hata oluştu.');
    }
  };

  // Hatırlatıcıyı paylaş
  const shareReminder = async () => {
    if (!reminder) return;

    try {
      await reminderShareService.shareReminder(reminder);
    } catch (error) {
      console.error('Hatırlatıcı paylaşma hatası:', error);
      Alert.alert('Hata', 'Hatırlatıcı paylaşılırken bir hata oluştu.');
    }
  };

  // Yükleniyor durumu
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={styles.loadingText}>Yükleniyor...</Text>
      </View>
    );
  }

  // Hatırlatıcı bulunamadı
  if (!reminder) {
    return (
      <View style={styles.notFoundContainer}>
        <MaterialIcons name="error-outline" size={64} color={Colors.GRAY_500} />
        <Text style={styles.notFoundText}>Hatırlatıcı bulunamadı.</Text>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>Geri Dön</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Tarih ve saat bilgilerini ayır
  const scheduledDate = parseISO(reminder.scheduled_at);
  const formattedDate = format(scheduledDate, 'dd MMMM yyyy', { locale: tr });
  const formattedTime = format(scheduledDate, 'HH:mm');

  // Kalan gün hesapla
  const today = new Date();
  const daysRemaining = differenceInDays(scheduledDate, today);

  // Tekrarlama bilgisi
  let repeatInfo = 'Bir kez';
  if (reminder.repeat_type === 'daily') {
    repeatInfo = `Her ${reminder.repeat_interval} günde bir`;
  } else if (reminder.repeat_type === 'weekly') {
    repeatInfo = `Her ${reminder.repeat_interval} haftada bir`;

    if (reminder.repeat_days && reminder.repeat_days.length > 0) {
      const dayNames = ['Pazartesi', 'Salı', 'Çarşamba', 'Perşembe', 'Cuma', 'Cumartesi', 'Pazar'];
      const selectedDays = reminder.repeat_days.map(day => dayNames[day - 1]).join(', ');
      repeatInfo += ` (${selectedDays})`;
    }
  } else if (reminder.repeat_type === 'monthly') {
    repeatInfo = `Her ${reminder.repeat_interval} ayda bir`;
  } else if (reminder.repeat_type === 'yearly') {
    repeatInfo = `Her ${reminder.repeat_interval} yılda bir`;
  }

  // Bitiş tarihi
  let endDateInfo = '';
  if (reminder.repeat_end_date) {
    const endDate = parseISO(reminder.repeat_end_date);
    endDateInfo = format(endDate, 'dd MMMM yyyy', { locale: tr });
  }

  // Öncelik rengi ve metni
  let priorityColor = Colors.WARNING;
  let priorityText = 'Normal';

  if (reminder.priority === 'low') {
    priorityColor = Colors.SUCCESS;
    priorityText = 'Düşük';
  } else if (reminder.priority === 'high') {
    priorityColor = Colors.DANGER;
    priorityText = 'Yüksek';
  }

  return (
    <View style={styles.container}>
      {/* Başlık */}
      <View style={styles.header}>
        <Text style={styles.title}>{reminder.title}</Text>

        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={shareReminder}
          >
            <MaterialIcons name="share" size={24} color={Colors.PRIMARY} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.headerButton}
            onPress={editReminder}
          >
            <MaterialIcons name="edit" size={24} color={Colors.PRIMARY} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.headerButton}
            onPress={deleteReminder}
          >
            <MaterialIcons name="delete" size={24} color={Colors.DANGER} />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView style={styles.content}>
        {/* Durum */}
        <View style={styles.statusContainer}>
          <View style={[styles.statusBadge, { backgroundColor: priorityColor }]}>
            <MaterialIcons
              name={
                reminder.priority === 'low' ? 'arrow-downward' :
                reminder.priority === 'high' ? 'arrow-upward' : 'remove'
              }
              size={16}
              color="#fff"
            />
            <Text style={styles.statusText}>{priorityText}</Text>
          </View>

          <View style={[
            styles.statusBadge,
            { backgroundColor: reminder.is_enabled ? Colors.SUCCESS : Colors.GRAY_500 }
          ]}>
            <MaterialIcons
              name={reminder.is_enabled ? 'notifications-active' : 'notifications-off'}
              size={16}
              color="#fff"
            />
            <Text style={styles.statusText}>
              {reminder.is_enabled ? 'Etkin' : 'Devre Dışı'}
            </Text>
          </View>

          <View style={[
            styles.statusBadge,
            {
              backgroundColor:
                reminder.status === 'pending' ? Colors.WARNING :
                reminder.status === 'sent' ? Colors.INFO :
                reminder.status === 'read' ? Colors.SUCCESS :
                Colors.GRAY_500
            }
          ]}>
            <MaterialIcons
              name={
                reminder.status === 'pending' ? 'schedule' :
                reminder.status === 'sent' ? 'notifications' :
                reminder.status === 'read' ? 'done-all' : 'cancel'
              }
              size={16}
              color="#fff"
            />
            <Text style={styles.statusText}>
              {
                reminder.status === 'pending' ? 'Bekliyor' :
                reminder.status === 'sent' ? 'Gönderildi' :
                reminder.status === 'read' ? 'Okundu' : 'İptal Edildi'
              }
            </Text>
          </View>
        </View>

        {/* Mesaj */}
        {reminder.message && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Mesaj</Text>
            <Text style={styles.message}>{reminder.message}</Text>
          </View>
        )}

        {/* Harcama Bilgileri */}
        {reminder.data?.isExpenseReminder && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Harcama Bilgileri</Text>

            {/* Harcama Türü */}
            <View style={styles.infoItem}>
              <MaterialIcons
                name={
                  reminder.expense_type === 'bill' ? 'receipt' :
                  reminder.expense_type === 'subscription' ? 'subscriptions' :
                  reminder.expense_type === 'regular' ? 'repeat' : 'attach-money'
                }
                size={24}
                color={
                  reminder.expense_type === 'bill' ? '#e74c3c' :
                  reminder.expense_type === 'subscription' ? '#3498db' :
                  reminder.expense_type === 'regular' ? '#2ecc71' : '#f39c12'
                }
              />
              <Text style={styles.infoText}>
                {
                  reminder.expense_type === 'bill' ? 'Fatura' :
                  reminder.expense_type === 'subscription' ? 'Abonelik' :
                  reminder.expense_type === 'regular' ? 'Düzenli Ödeme' : 'Diğer Harcama'
                }
              </Text>
            </View>

            {/* Miktar */}
            <View style={styles.infoItem}>
              <MaterialIcons name="attach-money" size={24} color={Colors.PRIMARY} />
              <Text style={styles.infoText}>
                {new Intl.NumberFormat('tr-TR', {
                  style: 'currency',
                  currency: reminder.currency || 'TRY'
                }).format(reminder.amount)}
              </Text>
            </View>

            {/* Son Ödeme Tarihi */}
            {reminder.due_date && (
              <View style={styles.infoItem}>
                <MaterialIcons name="event" size={24} color={Colors.PRIMARY} />
                <Text style={styles.infoText}>
                  Son Ödeme: {format(parseISO(reminder.due_date), 'dd MMMM yyyy', { locale: tr })}
                </Text>
              </View>
            )}

            {/* Hatırlatma Zamanı */}
            {reminder.remind_days_before !== undefined && (
              <View style={styles.infoItem}>
                <MaterialIcons name="notifications" size={24} color={Colors.PRIMARY} />
                <Text style={styles.infoText}>
                  {reminder.remind_days_before === 0 ? 'Aynı gün hatırlatılacak' :
                   reminder.remind_days_before === 1 ? '1 gün önce hatırlatılacak' :
                   `${reminder.remind_days_before} gün önce hatırlatılacak`}
                </Text>
              </View>
            )}
          </View>
        )}

        {/* Tarih ve Saat */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Tarih ve Saat</Text>
          <View style={styles.dateTimeContainer}>
            <View style={styles.dateTimeItem}>
              <MaterialIcons name="event" size={24} color={Colors.PRIMARY} />
              <Text style={styles.dateTimeText}>{formattedDate}</Text>
            </View>
            <View style={styles.dateTimeItem}>
              <MaterialIcons name="access-time" size={24} color={Colors.PRIMARY} />
              <Text style={styles.dateTimeText}>{formattedTime}</Text>
            </View>
          </View>

          {daysRemaining >= 0 ? (
            <View style={[
              styles.remainingDaysContainer,
              daysRemaining === 0 ? styles.todayContainer :
              daysRemaining <= 1 ? styles.urgentContainer :
              daysRemaining <= 3 ? styles.soonContainer :
              styles.normalContainer
            ]}>
              <MaterialIcons
                name={
                  daysRemaining === 0 ? 'today' :
                  daysRemaining <= 1 ? 'priority-high' :
                  'event-available'
                }
                size={20}
                color="#fff"
              />
              <Text style={styles.remainingDaysText}>
                {daysRemaining === 0 ? 'Bugün' :
                 daysRemaining === 1 ? 'Yarın' :
                 `${daysRemaining} gün sonra`}
              </Text>
            </View>
          ) : (
            <View style={styles.pastContainer}>
              <MaterialIcons name="event-busy" size={20} color="#fff" />
              <Text style={styles.remainingDaysText}>
                {Math.abs(daysRemaining) === 1 ? 'Dün' :
                 `${Math.abs(daysRemaining)} gün önce`}
              </Text>
            </View>
          )}
        </View>

        {/* Tekrarlama */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Tekrarlama</Text>
          <View style={styles.infoItem}>
            <MaterialIcons
              name={
                reminder.repeat_type === 'once' ? 'event' :
                reminder.repeat_type === 'daily' ? 'today' :
                reminder.repeat_type === 'weekly' ? 'view-week' :
                reminder.repeat_type === 'monthly' ? 'date-range' :
                'event-repeat'
              }
              size={24}
              color={Colors.PRIMARY}
            />
            <Text style={styles.infoText}>{repeatInfo}</Text>
          </View>

          {reminder.repeat_end_date && (
            <View style={styles.infoItem}>
              <MaterialIcons name="event-busy" size={24} color={Colors.PRIMARY} />
              <Text style={styles.infoText}>Bitiş: {endDateInfo}</Text>
            </View>
          )}
        </View>

        {/* Kategori */}
        {category && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Kategori</Text>
            <View style={styles.categoryContainer}>
              <View
                style={[
                  styles.categoryBadge,
                  { backgroundColor: category.color || Colors.PRIMARY }
                ]}
              >
                <Text style={styles.categoryText}>{category.name}</Text>
              </View>
            </View>
          </View>
        )}

        {/* Grup */}
        {reminder.group_name && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Grup</Text>
            <View style={styles.categoryContainer}>
              <View
                style={[
                  styles.groupBadge,
                  { backgroundColor: reminder.group_color || Colors.PRIMARY }
                ]}
              >
                <MaterialIcons
                  name={reminder.group_icon || 'folder'}
                  size={16}
                  color="#fff"
                  style={styles.groupIcon}
                />
                <Text style={styles.categoryText}>{reminder.group_name}</Text>
              </View>
            </View>
          </View>
        )}

        {/* Etiketler */}
        {tags && tags.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Etiketler</Text>
            <View style={styles.tagsContainer}>
              {tags.map(tag => (
                <View
                  key={tag.id}
                  style={[
                    styles.tagBadge,
                    { backgroundColor: tag.color || Colors.PRIMARY }
                  ]}
                >
                  <Text style={styles.tagText}>{tag.name}</Text>
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Oluşturulma ve Güncellenme */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Diğer Bilgiler</Text>
          <View style={styles.infoItem}>
            <MaterialIcons name="add-circle-outline" size={20} color={Colors.TEXT_DARK} />
            <Text style={styles.infoTextSmall}>
              Oluşturulma: {format(parseISO(reminder.created_at), 'dd MMM yyyy HH:mm', { locale: tr })}
            </Text>
          </View>

          <View style={styles.infoItem}>
            <MaterialIcons name="update" size={20} color={Colors.TEXT_DARK} />
            <Text style={styles.infoTextSmall}>
              Güncelleme: {format(parseISO(reminder.updated_at), 'dd MMM yyyy HH:mm', { locale: tr })}
            </Text>
          </View>
        </View>
      </ScrollView>

      {/* Alt Butonlar */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={[
            styles.footerButton,
            { backgroundColor: reminder.is_enabled ? Colors.DANGER : Colors.SUCCESS }
          ]}
          onPress={toggleEnabled}
        >
          <MaterialIcons
            name={reminder.is_enabled ? 'notifications-off' : 'notifications-active'}
            size={20}
            color="#fff"
          />
          <Text style={styles.footerButtonText}>
            {reminder.is_enabled ? 'Devre Dışı Bırak' : 'Etkinleştir'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.footerButton, { backgroundColor: Colors.PRIMARY }]}
          onPress={editReminder}
        >
          <MaterialIcons name="edit" size={20} color="#fff" />
          <Text style={styles.footerButtonText}>Düzenle</Text>
        </TouchableOpacity>
      </View>


    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: Colors.PRIMARY,
  },
  notFoundContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  notFoundText: {
    fontSize: 18,
    color: Colors.TEXT_DARK,
    marginTop: 16,
    marginBottom: 24,
  },
  backButton: {
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  backButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },

  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    flex: 1,
  },
  headerActions: {
    flexDirection: 'row',
  },
  headerButton: {
    marginLeft: 16,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  statusContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  statusText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
    marginLeft: 4,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    marginBottom: 12,
  },
  message: {
    fontSize: 16,
    color: Colors.TEXT_DARK,
    lineHeight: 24,
    backgroundColor: Colors.GRAY_100,
    padding: 16,
    borderRadius: 8,
  },
  dateTimeContainer: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  dateTimeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 24,
  },
  dateTimeText: {
    fontSize: 16,
    color: Colors.TEXT_DARK,
    marginLeft: 8,
  },
  remainingDaysContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 16,
    alignSelf: 'flex-start',
  },
  todayContainer: {
    backgroundColor: Colors.DANGER,
  },
  urgentContainer: {
    backgroundColor: Colors.WARNING,
  },
  soonContainer: {
    backgroundColor: Colors.INFO,
  },
  normalContainer: {
    backgroundColor: Colors.SUCCESS,
  },
  pastContainer: {
    backgroundColor: Colors.GRAY_500,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 16,
    alignSelf: 'flex-start',
  },
  remainingDaysText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 16,
    color: Colors.TEXT_DARK,
    marginLeft: 12,
  },
  infoTextSmall: {
    fontSize: 14,
    color: Colors.TEXT_LIGHT,
    marginLeft: 12,
  },
  categoryContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  categoryBadge: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  categoryText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  groupBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  groupIcon: {
    marginRight: 4,
  },
  footer: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.GRAY_200,
  },
  footerButton: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 12,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  footerButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  tagBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  tagText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '500',
  },
});
