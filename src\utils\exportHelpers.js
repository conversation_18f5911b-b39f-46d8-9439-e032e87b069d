import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import * as Print from 'expo-print';
import { Alert } from 'react-native';

/**
 * CSV formatında veri export eder
 */
export const exportToCSV = async (data, filename = 'statistics') => {
  try {
    // CSV başlıkları
    const headers = ['Kategori', 'Tutar', 'Adet', '<PERSON><PERSON>z<PERSON>'];
    const csvHeaders = headers.join(',') + '\n';
    
    // CSV satırları
    const csvRows = data.categoryBreakdown.map(item => 
      `"${item.name}","${item.total}","${item.count}","${item.percentage}%"`
    ).join('\n');
    
    // CSV içeriği
    const csvContent = csvHeaders + csvRows;
    
    // Dosya yolunu oluştur
    const fileName = `${filename}_${new Date().toISOString().split('T')[0]}.csv`;
    const fileUri = FileSystem.documentDirectory + fileName;
    
    // Dosyayı yaz
    await FileSystem.writeAsStringAsync(fileUri, csvContent, {
      encoding: FileSystem.EncodingType.UTF8,
    });
    
    // Dosyayı paylaş
    if (await Sharing.isAvailableAsync()) {
      await Sharing.shareAsync(fileUri, {
        mimeType: 'text/csv',
        dialogTitle: 'İstatistik Verilerini Paylaş',
      });
    }
    
    return fileUri;
  } catch (error) {
    console.error('CSV export hatası:', error);
    throw error;
  }
};

/**
 * JSON formatında detaylı veri export eder
 */
export const exportToJSON = async (data, filename = 'statistics') => {
  try {
    // JSON içeriği
    const jsonContent = JSON.stringify(data, null, 2);
    
    // Dosya yolunu oluştur
    const fileName = `${filename}_${new Date().toISOString().split('T')[0]}.json`;
    const fileUri = FileSystem.documentDirectory + fileName;
    
    // Dosyayı yaz
    await FileSystem.writeAsStringAsync(fileUri, jsonContent, {
      encoding: FileSystem.EncodingType.UTF8,
    });
    
    // Dosyayı paylaş (mocked for testing)
    console.log('🔍 Export helpers: JSON sharing mocked, file saved to:', fileUri);
    Alert.alert('Başarılı', 'JSON veriler dışa aktarıldı (paylaşım mock edildi)');
    
    return fileUri;
  } catch (error) {
    console.error('JSON export hatası:', error);
    throw error;
  }
};

/**
 * PDF formatında rapor oluşturur
 */
export const exportToPDF = async (data, filename = 'statistics') => {
  try {
    const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>İstatistik Raporu</title>
    <style>
        body { 
            font-family: 'Helvetica', Arial, sans-serif; 
            margin: 20px; 
            color: #333;
            line-height: 1.4;
        }
        .header { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px; 
            border-radius: 12px; 
            margin-bottom: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 28px;
            font-weight: 300;
        }
        .header p {
            margin: 5px 0;
            opacity: 0.9;
        }
        .stats { 
            display: grid; 
            grid-template-columns: repeat(2, 1fr); 
            gap: 20px; 
            margin-bottom: 40px; 
        }
        .stat-card { 
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 25px; 
            border-radius: 12px; 
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stat-value { 
            font-size: 24px; 
            font-weight: bold; 
            color: #2196F3; 
            margin-bottom: 8px;
        }
        .stat-label { 
            font-size: 14px; 
            color: #666; 
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2c3e50;
            font-size: 20px;
            margin-bottom: 20px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        table { 
            width: 100%; 
            border-collapse: collapse; 
            margin-top: 15px;
            background: white;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        th, td { 
            border: 1px solid #ddd; 
            padding: 15px 12px; 
            text-align: left; 
        }
        th { 
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
            text-transform: uppercase;
            font-size: 12px;
            letter-spacing: 0.5px;
        }
        .amount { 
            text-align: right; 
            font-weight: 600;
            color: #2c3e50;
        }
        .percentage { 
            text-align: center;
            background: #e3f2fd;
            border-radius: 4px;
            font-weight: 600;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }
        .balance-positive {
            color: #27ae60;
        }
        .balance-negative {
            color: #e74c3c;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 İstatistik Raporu</h1>
        <p><strong>Tarih:</strong> ${new Date().toLocaleDateString('tr-TR')}</p>
        <p><strong>Dönem:</strong> ${getPeriodLabel(data.period)}</p>
    </div>
    
    <div class="stats">
        <div class="stat-card">
            <div class="stat-value">${formatCurrency(data.stats.totalIncome)}</div>
            <div class="stat-label">💰 Toplam Gelir</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">${formatCurrency(data.stats.totalExpense)}</div>
            <div class="stat-label">📤 Toplam Gider</div>
        </div>
        <div class="stat-card">
            <div class="stat-value ${data.stats.balance >= 0 ? 'balance-positive' : 'balance-negative'}">${formatCurrency(data.stats.balance)}</div>
            <div class="stat-label">⚖️ Bakiye</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">${data.stats.transactionCount}</div>
            <div class="stat-label">📋 İşlem Sayısı</div>
        </div>
    </div>
    
    <div class="section">
        <h2>📈 Kategori Analizi</h2>
        <table>
            <thead>
                <tr>
                    <th>Kategori</th>
                    <th>Tutar</th>
                    <th>Adet</th>
                    <th>Yüzde</th>
                </tr>
            </thead>
            <tbody>
                ${data.categoryBreakdown.map(item => `
                    <tr>
                        <td><strong>${item.name}</strong></td>
                        <td class="amount">${formatCurrency(item.total)}</td>
                        <td class="amount">${item.count}</td>
                        <td class="percentage">${item.percentage}%</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    </div>
    
    <div class="section">
        <h2>💼 Gelir Kaynakları</h2>
        <table>
            <thead>
                <tr>
                    <th>Kaynak</th>
                    <th>Tutar</th>
                    <th>Yüzde</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>💳 İşlemler</strong></td>
                    <td class="amount">${formatCurrency(data.incomeBreakdown.transactions)}</td>
                    <td class="percentage">${data.stats.totalIncome > 0 ? ((data.incomeBreakdown.transactions / data.stats.totalIncome) * 100).toFixed(1) : 0}%</td>
                </tr>
                <tr>
                    <td><strong>💰 Düzenli Gelir</strong></td>
                    <td class="amount">${formatCurrency(data.incomeBreakdown.regularIncome)}</td>
                    <td class="percentage">${data.stats.totalIncome > 0 ? ((data.incomeBreakdown.regularIncome / data.stats.totalIncome) * 100).toFixed(1) : 0}%</td>
                </tr>
                <tr>
                    <td><strong>⏰ Mesai</strong></td>
                    <td class="amount">${formatCurrency(data.incomeBreakdown.overtime)}</td>
                    <td class="percentage">${data.stats.totalIncome > 0 ? ((data.incomeBreakdown.overtime / data.stats.totalIncome) * 100).toFixed(1) : 0}%</td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div class="footer">
        <p>Bu rapor otomatik olarak oluşturulmuştur • ${new Date().toLocaleString('tr-TR')}</p>
    </div>
</body>
</html>`;
    
    // PDF oluştur
    const { uri } = await Print.printToFileAsync({
      html: htmlContent,
      base64: false,
    });
    
    // PDF dosyasının yeni adını oluştur
    const fileName = `${filename}_${new Date().toISOString().split('T')[0]}.pdf`;
    const newUri = FileSystem.documentDirectory + fileName;
    
    // Dosyayı yeniden adlandır
    await FileSystem.moveAsync({
      from: uri,
      to: newUri,
    });
    
    // Dosyayı paylaş
    if (await Sharing.isAvailableAsync()) {
      await Sharing.shareAsync(newUri, {
        mimeType: 'application/pdf',
        dialogTitle: 'İstatistik PDF Raporunu Paylaş',
      });
    }
    
    return newUri;
  } catch (error) {
    console.error('PDF export hatası:', error);
    throw error;
  }
};

/**
 * Para formatı
 */
const formatCurrency = (amount) => {
  return new Intl.NumberFormat('tr-TR', {
    style: 'currency',
    currency: 'TRY',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

/**
 * Dönem etiketi
 */
const getPeriodLabel = (period) => {
  const labels = {
    '7days': 'Son 7 Gün',
    '30days': 'Son 30 Gün',
    '90days': 'Son 3 Ay',
    'year': 'Bu Yıl',
  };
  return labels[period] || 'Bilinmiyor';
};

/**
 * Export seçenekleri menüsü gösterir
 */
export const showExportOptions = (data) => {
  Alert.alert(
    'Export Seçenekleri',
    'Hangi formatta export etmek istiyorsunuz?',
    [
      {
        text: 'CSV (Excel)',
        onPress: () => exportToCSV(data, 'istatistikler'),
      },
      {
        text: 'PDF Raporu',
        onPress: () => exportToPDF(data, 'istatistikler'),
      },
      {
        text: 'JSON',
        onPress: () => exportToJSON(data, 'istatistikler'),
      },
      {
        text: 'İptal',
        style: 'cancel',
      },
    ],
    { cancelable: true }
  );
};
