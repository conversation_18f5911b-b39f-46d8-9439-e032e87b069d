import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList, Alert } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useSQLiteContext } from 'expo-sqlite';
import { Colors } from '../constants/colors';
import EmptyState from '../components/common/EmptyState';

/**
 * Kategori listesi ekranı
 * @param {Object} props Component props
 * @param {Object} props.navigation Navigation objesi
 */
const CategoryListScreen = ({ navigation }) => {
  const db = useSQLiteContext();
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all'); // 'all', 'income', 'expense'

  // Kategorileri yükle
  const loadCategories = async () => {
    try {
      setLoading(true);
      
      let query = 'SELECT * FROM categories';
      
      if (filter === 'income') {
        query += " WHERE type = 'income' OR type = 'both'";
      } else if (filter === 'expense') {
        query += " WHERE type = 'expense' OR type = 'both'";
      }
      
      query += ' ORDER BY name';
      
      const result = await db.getAllAsync(query);
      setCategories(result);
    } catch (error) {
      console.error('Kategori yükleme hatası:', error);
      Alert.alert('Hata', 'Kategoriler yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  // İlk yükleme
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      loadCategories();
    });

    return unsubscribe;
  }, [navigation, filter]);

  // Kategori silme
  const handleDeleteCategory = async (id) => {
    try {
      // Önce bu kategoriye bağlı işlem var mı kontrol et
      const transactions = await db.getAllAsync(
        'SELECT COUNT(*) as count FROM transactions WHERE category_id = ?',
        [id]
      );
      
      if (transactions[0].count > 0) {
        Alert.alert(
          'Uyarı',
          'Bu kategoriye bağlı işlemler var. Silmek istediğinize emin misiniz? İşlemler varsayılan kategoriye taşınacaktır.',
          [
            { text: 'İptal', style: 'cancel' },
            { 
              text: 'Sil', 
              style: 'destructive',
              onPress: async () => {
                // Varsayılan kategoriyi bul
                const defaultCategories = await db.getAllAsync(
                  "SELECT * FROM categories WHERE is_default = 1 AND (type = ? OR type = 'both') LIMIT 1",
                  [categories.find(c => c.id === id)?.type || 'expense']
                );
                
                const defaultCategoryId = defaultCategories.length > 0 
                  ? defaultCategories[0].id 
                  : null;
                
                // İşlemleri varsayılan kategoriye taşı
                if (defaultCategoryId) {
                  await db.runAsync(
                    'UPDATE transactions SET category_id = ? WHERE category_id = ?',
                    [defaultCategoryId, id]
                  );
                }
                
                // Kategoriyi sil
                await db.runAsync('DELETE FROM categories WHERE id = ?', [id]);
                loadCategories();
              }
            }
          ]
        );
      } else {
        // Bağlı işlem yoksa doğrudan sil
        Alert.alert(
          'Onay',
          'Bu kategoriyi silmek istediğinize emin misiniz?',
          [
            { text: 'İptal', style: 'cancel' },
            { 
              text: 'Sil', 
              style: 'destructive',
              onPress: async () => {
                await db.runAsync('DELETE FROM categories WHERE id = ?', [id]);
                loadCategories();
              }
            }
          ]
        );
      }
    } catch (error) {
      console.error('Kategori silme hatası:', error);
      Alert.alert('Hata', 'Kategori silinirken bir hata oluştu.');
    }
  };

  // Kategori düzenleme
  const handleEditCategory = (category) => {
    navigation.navigate('CategoryEdit', { category });
  };

  // Kategori öğesi
  const renderCategoryItem = ({ item }) => {
    const typeText = item.type === 'income' 
      ? 'Gelir' 
      : item.type === 'expense' 
        ? 'Gider' 
        : 'Gelir/Gider';
    
    return (
      <TouchableOpacity
        style={styles.categoryItem}
        onPress={() => handleEditCategory(item)}
      >
        <View style={[styles.iconContainer, { backgroundColor: item.color || '#3498db' }]}>
          <MaterialIcons
            name={item.icon || 'category'}
            size={24}
            color="#fff"
          />
        </View>
        
        <View style={styles.categoryInfo}>
          <Text style={styles.categoryName}>{item.name}</Text>
          <Text style={styles.categoryType}>{typeText}</Text>
        </View>
        
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => handleDeleteCategory(item.id)}
        >
          <MaterialIcons name="delete" size={24} color="#e74c3c" />
        </TouchableOpacity>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.filterContainer}>
        <TouchableOpacity
          style={[styles.filterButton, filter === 'all' && styles.activeFilter]}
          onPress={() => setFilter('all')}
        >
          <Text style={[styles.filterText, filter === 'all' && styles.activeFilterText]}>
            Tümü
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.filterButton, filter === 'income' && styles.activeIncomeFilter]}
          onPress={() => setFilter('income')}
        >
          <Text style={[styles.filterText, filter === 'income' && styles.activeFilterText]}>
            Gelir
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.filterButton, filter === 'expense' && styles.activeExpenseFilter]}
          onPress={() => setFilter('expense')}
        >
          <Text style={[styles.filterText, filter === 'expense' && styles.activeFilterText]}>
            Gider
          </Text>
        </TouchableOpacity>
      </View>

      {categories.length === 0 ? (
        <EmptyState
          icon="category"
          title="Kategori Bulunamadı"
          message="Henüz hiç kategori eklenmemiş. Yeni bir kategori eklemek için + butonuna tıklayın."
          buttonText="Kategori Ekle"
          onButtonPress={() => navigation.navigate('CategoryEdit')}
        />
      ) : (
        <FlatList
          data={categories}
          keyExtractor={(item) => item.id.toString()}
          renderItem={renderCategoryItem}
          contentContainerStyle={styles.listContent}
        />
      )}

      <TouchableOpacity
        style={styles.addButton}
        onPress={() => navigation.navigate('CategoryEdit')}
      >
        <MaterialIcons name="add" size={24} color="#fff" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  filterContainer: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: '#fff',
  },
  filterButton: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 4,
    marginHorizontal: 4,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  activeFilter: {
    backgroundColor: Colors.PRIMARY,
    borderColor: Colors.PRIMARY,
  },
  activeIncomeFilter: {
    backgroundColor: Colors.INCOME,
    borderColor: Colors.INCOME,
  },
  activeExpenseFilter: {
    backgroundColor: Colors.EXPENSE,
    borderColor: Colors.EXPENSE,
  },
  filterText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
  },
  activeFilterText: {
    color: '#fff',
  },
  listContent: {
    padding: 16,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  categoryType: {
    fontSize: 14,
    color: '#666',
  },
  deleteButton: {
    padding: 8,
  },
  addButton: {
    position: 'absolute',
    right: 16,
    bottom: 16,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
});

export default CategoryListScreen;
