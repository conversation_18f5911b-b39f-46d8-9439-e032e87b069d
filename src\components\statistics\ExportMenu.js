import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  Modal, 
  ActivityIndicator,
  Alert,
  Platform 
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';
import * as FileSystem from 'expo-file-system';
// import * as Sharing from 'expo-sharing';
// import * as Print from 'expo-print';

/**
 * Export functionality component
 */
const ExportMenu = ({ 
  visible = false,
  onClose,
  data = {},
  title = 'İstatistik Raporu'
}) => {
  const { theme } = useTheme();
  const [loading, setLoading] = useState(false);
  const [exportType, setExportType] = useState(null);

  const exportOptions = [
    {
      id: 'csv',
      title: 'CSV Dosyası',
      description: 'Excel ve diğer tablolama programları için',
      icon: 'document-text',
      color: theme.SUCCESS || '#10b981'
    },
    {
      id: 'json',
      title: 'JSON Do<PERSON>ası',
      description: 'Teknik analiz ve yedekleme için',
      icon: 'code',
      color: theme.INFO || '#6366f1'
    },
    {
      id: 'txt',
      title: 'Metin Raporu',
      description: 'Basit metin formatında özet',
      icon: 'document-outline',
      color: theme.WARNING || '#f59e0b'
    },
    {
      id: 'pdf',
      title: 'PDF Raporu',
      description: 'Yazdırılabilir PDF formatında rapor',
      icon: 'document',
      color: theme.ERROR || '#ef4444'
    }
  ];

  /**
   * Generate CSV content with real data
   */
  const generateCSV = (data) => {
    let csv = 'Tarih,Miktar,Tür,Kategori,Açıklama\n';
    
    // Add transactions if available
    if (data.transactions && Array.isArray(data.transactions)) {
      data.transactions.forEach(transaction => {
        const date = transaction.date ? new Date(transaction.date).toLocaleDateString('tr-TR') : '';
        const amount = transaction.amount || 0;
        const type = transaction.type === 'income' ? 'Gelir' : 'Gider';
        const category = transaction.category_name || '';
        const description = (transaction.description || '').replace(/"/g, '""');
        
        csv += `"${date}","${amount.toFixed(2)}","${type}","${category}","${description}"\n`;
      });
    }

    // Add summary section
    csv += '\n\nÖZET\n';
    csv += `Toplam Gelir,"${(data.analytics?.totalIncome || 0).toFixed(2)}"\n`;
    csv += `Toplam Gider,"${(data.analytics?.totalExpense || 0).toFixed(2)}"\n`;
    csv += `Net Gelir,"${(data.analytics?.netIncome || 0).toFixed(2)}"\n`;
    csv += `İşlem Sayısı,"${data.analytics?.transactionCount || 0}"\n`;
    csv += `Ortalama İşlem,"${(data.analytics?.averageTransaction || 0).toFixed(2)}"\n`;
    csv += `Günlük Ortalama,"${(data.analytics?.averageDaily || 0).toFixed(2)}"\n`;

    // Add category breakdown
    if (data.analytics?.categoryBreakdown && data.analytics.categoryBreakdown.length > 0) {
      csv += '\nKATEGORİ DAĞILIMI\n';
      csv += 'Kategori,Miktar,Yüzde\n';
      data.analytics.categoryBreakdown.forEach(category => {
        const percentage = data.analytics.totalExpense > 0 
          ? ((category.amount / data.analytics.totalExpense) * 100).toFixed(1)
          : 0;
        csv += `"${category.name}","${category.amount.toFixed(2)}","${percentage}%"\n`;
      });
    }

    return csv;
  };

  /**
   * Generate JSON content with full data
   */
  const generateJSON = (data) => {
    const exportData = {
      title: title,
      exportDate: new Date().toISOString(),
      exportDateFormatted: new Date().toLocaleDateString('tr-TR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }),
      filter: {
        period: data.filter?.period || 'Belirtilmemiş',
        startDate: data.filter?.startDate || null,
        endDate: data.filter?.endDate || null,
        categories: data.filter?.categories || [],
        type: data.filter?.type || 'all'
      },
      summary: {
        totalIncome: data.analytics?.totalIncome || 0,
        totalExpense: data.analytics?.totalExpense || 0,
        netIncome: data.analytics?.netIncome || 0,
        transactionCount: data.analytics?.transactionCount || 0,
        averageTransaction: data.analytics?.averageTransaction || 0,
        averageDaily: data.analytics?.averageDaily || 0
      },
      transactions: (data.transactions || []).map(transaction => ({
        id: transaction.id,
        date: transaction.date,
        amount: transaction.amount,
        type: transaction.type,
        category_name: transaction.category_name,
        description: transaction.description,
        created_at: transaction.created_at
      })),
      categoryBreakdown: (data.analytics?.categoryBreakdown || []).map(category => ({
        name: category.name,
        amount: category.amount,
        count: category.count,
        color: category.color,
        percentage: data.analytics?.totalExpense > 0 
          ? ((category.amount / data.analytics.totalExpense) * 100).toFixed(1)
          : 0
      })),
      insights: data.insights || [],
      trends: data.analytics?.trends || []
    };

    return JSON.stringify(exportData, null, 2);
  };

  /**
   * Generate PDF content (HTML format) with comprehensive data
   */
  const generatePDFContent = (data) => {
    const currentDate = new Date().toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
    
    const filterInfo = data.filter ? `
      <div style="background: #f8fafc; padding: 15px; border-radius: 6px; margin: 15px 0;">
        <h3 style="color: #4b5563; margin-top: 0;">📅 Filtre Bilgileri</h3>
        <p><strong>Dönem:</strong> ${data.filter.period || 'Belirtilmemiş'}</p>
        ${data.filter.startDate ? `<p><strong>Başlangıç:</strong> ${new Date(data.filter.startDate).toLocaleDateString('tr-TR')}</p>` : ''}
        ${data.filter.endDate ? `<p><strong>Bitiş:</strong> ${new Date(data.filter.endDate).toLocaleDateString('tr-TR')}</p>` : ''}
        ${data.filter.type && data.filter.type !== 'all' ? `<p><strong>Tür:</strong> ${data.filter.type === 'income' ? 'Gelir' : 'Gider'}</p>` : ''}
      </div>
    ` : '';

    const transactionsTable = data.transactions && data.transactions.length > 0 ? `
      <h2>📋 İşlem Detayları</h2>
      <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
        <thead>
          <tr style="background: #f1f5f9;">
            <th style="border: 1px solid #e2e8f0; padding: 8px; text-align: left;">Tarih</th>
            <th style="border: 1px solid #e2e8f0; padding: 8px; text-align: left;">Tür</th>
            <th style="border: 1px solid #e2e8f0; padding: 8px; text-align: left;">Kategori</th>
            <th style="border: 1px solid #e2e8f0; padding: 8px; text-align: right;">Miktar</th>
            <th style="border: 1px solid #e2e8f0; padding: 8px; text-align: left;">Açıklama</th>
          </tr>
        </thead>
        <tbody>
          ${data.transactions.slice(0, 50).map(transaction => `
            <tr>
              <td style="border: 1px solid #e2e8f0; padding: 8px;">${transaction.date ? new Date(transaction.date).toLocaleDateString('tr-TR') : ''}</td>
              <td style="border: 1px solid #e2e8f0; padding: 8px;">${transaction.type === 'income' ? '💰 Gelir' : '💸 Gider'}</td>
              <td style="border: 1px solid #e2e8f0; padding: 8px;">${transaction.category_name || ''}</td>
              <td style="border: 1px solid #e2e8f0; padding: 8px; text-align: right;">${(transaction.amount || 0).toLocaleString('tr-TR')} ₺</td>
              <td style="border: 1px solid #e2e8f0; padding: 8px;">${transaction.description || ''}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
      ${data.transactions.length > 50 ? `<p><em>Not: Yalnızca ilk 50 işlem gösterilmektedir. Toplam ${data.transactions.length} işlem bulunmaktadır.</em></p>` : ''}
    ` : '';
    
    const html = `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>${title}</title>
  <style>
    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif; margin: 20px; color: #333; line-height: 1.6; }
    h1 { color: #2563eb; border-bottom: 2px solid #2563eb; padding-bottom: 10px; }
    h2 { color: #1f2937; margin-top: 30px; }
    h3 { color: #4b5563; margin-top: 20px; }
    .summary { background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0; }
    .summary-item { display: flex; justify-content: space-between; margin: 10px 0; padding: 5px 0; border-bottom: 1px solid #e2e8f0; }
    .summary-item:last-child { border-bottom: none; }
    .category-item { display: flex; justify-content: space-between; align-items: center; margin: 8px 0; padding: 8px; background: #f1f5f9; border-radius: 4px; }
    .category-color { width: 20px; height: 20px; border-radius: 50%; margin-right: 10px; }
    .insight { background: #ecfdf5; padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid #10b981; }
    .footer { margin-top: 40px; font-size: 12px; color: #6b7280; text-align: center; }
    table { font-size: 12px; }
    @media print { body { margin: 0; } }
  </style>
</head>
<body>
  <h1>📊 ${title}</h1>
  <p><strong>Rapor Tarihi:</strong> ${currentDate}</p>
  
  ${filterInfo}
  
  <div class="summary">
    <h2>📈 Finansal Özet</h2>
    <div class="summary-item">
      <span><strong>💰 Toplam Gelir:</strong></span>
      <span style="color: #10b981; font-weight: bold;">${(data.analytics?.totalIncome || 0).toLocaleString('tr-TR')} ₺</span>
    </div>
    <div class="summary-item">
      <span><strong>💸 Toplam Gider:</strong></span>
      <span style="color: #ef4444; font-weight: bold;">${(data.analytics?.totalExpense || 0).toLocaleString('tr-TR')} ₺</span>
    </div>
    <div class="summary-item">
      <span><strong>💵 Net Gelir:</strong></span>
      <span style="color: ${(data.analytics?.netIncome || 0) >= 0 ? '#10b981' : '#ef4444'}; font-weight: bold;">${(data.analytics?.netIncome || 0).toLocaleString('tr-TR')} ₺</span>
    </div>
    <div class="summary-item">
      <span><strong>📊 İşlem Sayısı:</strong></span>
      <span>${data.analytics?.transactionCount || 0}</span>
    </div>
    <div class="summary-item">
      <span><strong>💹 Ortalama İşlem:</strong></span>
      <span>${(data.analytics?.averageTransaction || 0).toLocaleString('tr-TR')} ₺</span>
    </div>
    <div class="summary-item">
      <span><strong>📅 Günlük Ortalama:</strong></span>
      <span>${(data.analytics?.averageDaily || 0).toLocaleString('tr-TR')} ₺</span>
    </div>
  </div>

  ${data.analytics?.categoryBreakdown?.length > 0 ? `
  <h2>📊 Kategori Dağılımı</h2>
  <div>
    ${data.analytics.categoryBreakdown.map(category => {
      const percentage = data.analytics.totalExpense > 0 
        ? ((category.amount / data.analytics.totalExpense) * 100).toFixed(1)
        : 0;
      return `
      <div class="category-item">
        <div style="display: flex; align-items: center;">
          <div class="category-color" style="background-color: ${category.color || '#6b7280'};"></div>
          <span>${category.name}</span>
        </div>
        <div style="text-align: right;">
          <div style="font-weight: bold;">${category.amount.toLocaleString('tr-TR')} ₺</div>
          <div style="font-size: 12px; color: #6b7280;">${percentage}%</div>
        </div>
      </div>`;
    }).join('')}
  </div>` : ''}

  ${transactionsTable}

  ${data.insights?.length > 0 ? `
  <h2>💡 Akıllı Öneriler</h2>
  ${data.insights.map((insight, index) => `
  <div class="insight">
    <strong>${index + 1}. ${insight.title}</strong>
    <p>${insight.message}</p>
  </div>`).join('')}` : ''}

  <div class="footer">
    <p>Bu rapor otomatik olarak oluşturulmuştur • ${currentDate}</p>
    <p>Toplam ${data.transactions?.length || 0} işlem analiz edilmiştir</p>
  </div>
</body>
</html>`;

    return html;
  };

  /**
   * Generate text report
   */
  const generateTextReport = (data) => {
    const currentDate = new Date().toLocaleDateString('tr-TR');
    
    let report = `${title}\n`;
    report += `Rapor Tarihi: ${currentDate}\n`;
    report += '='.repeat(50) + '\n\n';
    
    // Summary
    report += 'ÖZET\n';
    report += '-'.repeat(20) + '\n';
    report += `Toplam Gelir: ${(data.analytics?.totalIncome || 0).toLocaleString('tr-TR')} ₺\n`;
    report += `Toplam Gider: ${(data.analytics?.totalExpense || 0).toLocaleString('tr-TR')} ₺\n`;
    report += `Net Gelir: ${(data.analytics?.netIncome || 0).toLocaleString('tr-TR')} ₺\n`;
    report += `İşlem Sayısı: ${data.analytics?.transactionCount || 0}\n`;
    report += `Ortalama İşlem: ${(data.analytics?.averageTransaction || 0).toLocaleString('tr-TR')} ₺\n`;
    report += `Günlük Ortalama: ${(data.analytics?.averageDaily || 0).toLocaleString('tr-TR')} ₺\n\n`;
    
    // Category breakdown
    if (data.analytics?.categoryBreakdown?.length > 0) {
      report += 'KATEGORİ DAĞILIMI\n';
      report += '-'.repeat(30) + '\n';
      data.analytics.categoryBreakdown.forEach(category => {
        const percentage = data.analytics.totalExpense > 0 
          ? ((category.amount / data.analytics.totalExpense) * 100).toFixed(1)
          : 0;
        report += `${category.name}: ${category.amount.toLocaleString('tr-TR')} ₺ (${percentage}%)\n`;
      });
      report += '\n';
    }
    
    // Insights
    if (data.insights?.length > 0) {
      report += 'AKILLI ÖNERİLER\n';
      report += '-'.repeat(25) + '\n';
      data.insights.forEach((insight, index) => {
        report += `${index + 1}. ${insight.title}\n`;
        report += `   ${insight.message}\n\n`;
      });
    }

    // Transactions
    if (data.transactions?.length > 0) {
      report += 'İŞLEM DETAYLARI\n';
      report += '-'.repeat(25) + '\n';
      data.transactions.slice(0, 20).forEach(transaction => {
        const date = transaction.date ? new Date(transaction.date).toLocaleDateString('tr-TR') : '';
        const type = transaction.type === 'income' ? 'Gelir' : 'Gider';
        report += `${date} | ${type} | ${transaction.category_name || ''} | ${(transaction.amount || 0).toLocaleString('tr-TR')} ₺\n`;
      });
      if (data.transactions.length > 20) {
        report += `\n... ve ${data.transactions.length - 20} işlem daha\n`;
      }
    }

    return report;
  };

  /**
   * Handle export
   */
  const handleExport = async (type) => {
    try {
      setLoading(true);
      setExportType(type);

      let content = '';
      let fileName = '';
      let mimeType = '';

      switch (type) {
        case 'csv':
          content = generateCSV(data);
          fileName = `istatistik_raporu_${new Date().toISOString().split('T')[0]}.csv`;
          mimeType = 'text/csv';
          break;
        case 'json':
          content = generateJSON(data);
          fileName = `istatistik_raporu_${new Date().toISOString().split('T')[0]}.json`;
          mimeType = 'application/json';
          break;
        case 'txt':
          content = generateTextReport(data);
          fileName = `istatistik_raporu_${new Date().toISOString().split('T')[0]}.txt`;
          mimeType = 'text/plain';
          break;
        case 'pdf':
          content = generatePDFContent(data);
          
          // Create actual PDF using expo-print
          const { uri } = await Print.printToFileAsync({
            html: content,
            base64: false
          });
          
          // Share the PDF file
          if (await Sharing.isAvailableAsync()) {
            await Sharing.shareAsync(uri, {
              mimeType: 'application/pdf',
              dialogTitle: 'İstatistik Raporunu Paylaş',
            });
          } else {
            Alert.alert(
              'Başarılı',
              'PDF raporu başarıyla oluşturuldu',
              [{ text: 'Tamam' }]
            );
          }
          
          onClose();
          return;
        default:
          throw new Error('Geçersiz export türü');
      }

      // Create file for other types
      const fileUri = `${FileSystem.documentDirectory}${fileName}`;
      await FileSystem.writeAsStringAsync(fileUri, content, {
        encoding: FileSystem.EncodingType.UTF8,
      });

      // Share file
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri, {
          mimeType: mimeType,
          dialogTitle: 'İstatistik Raporunu Paylaş',
        });
      } else {
        Alert.alert(
          'Başarılı',
          `Rapor başarıyla oluşturuldu: ${fileName}`,
          [{ text: 'Tamam' }]
        );
      }

      onClose();
    } catch (error) {
      console.error('Export error:', error);
      Alert.alert(
        'Hata',
        'Rapor oluşturulurken hata oluştu: ' + error.message,
        [{ text: 'Tamam' }]
      );
    } finally {
      setLoading(false);
      setExportType(null);
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor: theme.SURFACE }]}>
          {/* Header */}
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: theme.TEXT_PRIMARY }]}>
              Rapor Dışa Aktar
            </Text>
            <TouchableOpacity onPress={onClose}>
              <Ionicons name="close" size={24} color={theme.TEXT_PRIMARY} />
            </TouchableOpacity>
          </View>

          {/* Export Options */}
          <View style={styles.exportOptions}>
            {exportOptions.map((option) => (
              <TouchableOpacity
                key={option.id}
                style={[
                  styles.exportOption,
                  { backgroundColor: theme.BACKGROUND, borderColor: theme.BORDER }
                ]}
                onPress={() => handleExport(option.id)}
                disabled={loading}
              >
                <View style={[styles.exportIcon, { backgroundColor: option.color }]}>
                  <Ionicons name={option.icon} size={24} color="#ffffff" />
                </View>
                <View style={styles.exportText}>
                  <Text style={[styles.exportTitle, { color: theme.TEXT_PRIMARY }]}>
                    {option.title}
                  </Text>
                  <Text style={[styles.exportDescription, { color: theme.TEXT_SECONDARY }]}>
                    {option.description}
                  </Text>
                </View>
                {loading && exportType === option.id ? (
                  <ActivityIndicator size="small" color={theme.PRIMARY} />
                ) : (
                  <Ionicons name="chevron-forward" size={20} color={theme.TEXT_SECONDARY} />
                )}
              </TouchableOpacity>
            ))}
          </View>

          {/* Info Text */}
          <View style={styles.infoContainer}>
            <Text style={[styles.infoText, { color: theme.TEXT_SECONDARY }]}>
              💡 Raporlar mevcut filtreler ve veri aralığına göre oluşturulur
            </Text>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  exportOptions: {
    paddingHorizontal: 20,
  },
  exportOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 12,
  },
  exportIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  exportText: {
    flex: 1,
  },
  exportTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  exportDescription: {
    fontSize: 14,
  },
  infoContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  infoText: {
    fontSize: 12,
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

export default ExportMenu;