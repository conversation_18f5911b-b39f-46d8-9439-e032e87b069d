import { useState, useEffect, useCallback } from 'react';

/**
 * <PERSON>or oluşturucu ana hook
 * <PERSON><PERSON> verileri, yapılandırma ve işlemler
 */
export const useReportBuilder = () => {
  const [reportData, setReportData] = useState([]);
  const [reportConfig, setReportConfig] = useState({
    title: '',
    description: '',
    dataSources: [],
    filters: [],
    dateRange: {
      start: '',
      end: '',
      period: 'thisMonth',
    },
    layout: 'grid',
    theme: 'default',
  });
  const [dataSources, setDataSources] = useState([]);
  const [filters, setFilters] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  /**
   * Rapor yapılandırmasını güncelle
   */
  const updateReportConfig = useCallback((updates) => {
    setReportConfig(prev => ({
      ...prev,
      ...updates,
    }));
  }, []);

  /**
   * Veri kaynaklarını yükle
   */
  const loadDataSources = useCallback(async () => {
    try {
      setIsLoading(true);
      
      // Mock data sources
      const mockDataSources = [
        {
          id: 'transactions',
          name: 'İşlemler',
          icon: '💳',
          count: 1247,
          fields: [
            { name: 'date', type: 'date', label: 'Tarih' },
            { name: 'amount', type: 'number', label: 'Tutar' },
            { name: 'category', type: 'string', label: 'Kategori' },
            { name: 'description', type: 'string', label: 'Açıklama' },
          ],
        },
        {
          id: 'budgets',
          name: 'Bütçeler',
          icon: '📊',
          count: 12,
          fields: [
            { name: 'name', type: 'string', label: 'Bütçe Adı' },
            { name: 'amount', type: 'number', label: 'Tutar' },
            { name: 'spent', type: 'number', label: 'Harcanan' },
            { name: 'remaining', type: 'number', label: 'Kalan' },
          ],
        },
        {
          id: 'goals',
          name: 'Hedefler',
          icon: '🎯',
          count: 5,
          fields: [
            { name: 'name', type: 'string', label: 'Hedef Adı' },
            { name: 'target', type: 'number', label: 'Hedef Tutar' },
            { name: 'current', type: 'number', label: 'Mevcut Tutar' },
            { name: 'progress', type: 'number', label: 'İlerleme (%)' },
          ],
        },
      ];
      
      setDataSources(mockDataSources);
    } catch (err) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Rapor verilerini yükle
   */
  const loadReportData = useCallback(async (config) => {
    try {
      setIsLoading(true);
      
      // Mock report data generation
      const mockData = [];
      const now = new Date();
      
      for (let i = 0; i < 30; i++) {
        const date = new Date(now);
        date.setDate(date.getDate() - i);
        
        mockData.push({
          date: date.toISOString().split('T')[0],
          amount: Math.floor(Math.random() * 1000) + 100,
          category: ['Gıda', 'Ulaşım', 'Eğlence', 'Faturalar'][Math.floor(Math.random() * 4)],
          description: 'Örnek işlem ' + (i + 1),
          type: Math.random() > 0.7 ? 'Gelir' : 'Gider',
        });
      }
      
      setReportData(mockData);
    } catch (err) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Rapor kaydet
   */
  const saveReport = useCallback(async (reportData) => {
    try {
      setIsLoading(true);
      
      // Mock save operation
      const savedReport = {
        id: Date.now().toString(),
        ...reportData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      // In real implementation, save to database
      
      return savedReport;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Rapor yükle
   */
  const loadReport = useCallback(async (reportId) => {
    try {
      setIsLoading(true);
      
      // Mock load operation
      const mockReport = {
        id: reportId,
        name: 'Yüklenen Rapor',
        config: reportConfig,
        elements: [],
        createdAt: new Date().toISOString(),
      };
      
      setReportConfig(mockReport.config);
      
      return mockReport;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [reportConfig]);

  /**
   * Rapor dışa aktar
   */
  const exportReport = useCallback(async (exportConfig) => {
    try {
      setIsLoading(true);
      
      // Mock export operation
      const exportData = {
        ...exportConfig,
        exportedAt: new Date().toISOString(),
        fileName: `report_${Date.now()}.${exportConfig.format.toLowerCase()}`,
      };
      
      return exportData;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Filtreleri uygula
   */
  const applyFilters = useCallback((data, filters) => {
    if (!filters || filters.length === 0) return data;
    
    return data.filter(item => {
      return filters.every(filter => {
        const value = item[filter.field];
        
        switch (filter.operator) {
          case 'equals':
            return value === filter.value;
          case 'contains':
            return value && value.toString().toLowerCase().includes(filter.value.toLowerCase());
          case 'greaterThan':
            return Number(value) > Number(filter.value);
          case 'lessThan':
            return Number(value) < Number(filter.value);
          case 'between':
            return Number(value) >= Number(filter.value[0]) && Number(value) <= Number(filter.value[1]);
          default:
            return true;
        }
      });
    });
  }, []);

  // Initialize data sources on mount
  useEffect(() => {
    loadDataSources();
  }, [loadDataSources]);

  // Load report data when config changes
  useEffect(() => {
    if (reportConfig.dataSources.length > 0) {
      loadReportData(reportConfig);
    }
  }, [reportConfig, loadReportData]);

  return {
    reportData,
    reportConfig,
    dataSources,
    filters,
    isLoading,
    error,
    updateReportConfig,
    loadDataSources,
    loadReportData,
    saveReport,
    loadReport,
    exportReport,
    applyFilters,
  };
};
