import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../../constants/colors';
import { useTheme } from '../../context/ThemeContext';

/**
 * Basit kategori seçici bileşeni
 * @param {Object} props Component props
 * @param {Array} props.categories Kategori listesi
 * @param {number|null} props.selectedCategoryId Seçili kategori ID'si
 * @param {Function} props.onSelectCategory Kategori seçildiğinde çağrılacak fonksiyon
 * @param {Function} props.onAddCategory Yeni kategori ekle butonuna tıklandığında çağrılacak fonksiyon
 * @param {string} props.type Kategori tipi ('income', 'expense', 'both')
 */
const CategorySelectorSimple = ({
  categories,
  selectedCategoryId,
  onSelectCategory,
  onAddCategory,
  type = 'both'
}) => {
  const { theme } = useTheme();

  return (
    <View style={styles.wrapper}>
      <View style={styles.header}>
        <Text style={[styles.headerTitle, { color: theme.TEXT_PRIMARY }]}>Kategori</Text>
        {onAddCategory && (
          <TouchableOpacity
            style={[styles.addCategoryButtonSmall, { backgroundColor: theme.SURFACE_VARIANT, borderColor: theme.BORDER }]}
            onPress={() => onAddCategory(type)}
          >
            <MaterialIcons name="add" size={16} color={Colors.PRIMARY} />
            <Text style={[styles.addCategoryTextSmall, { color: theme.TEXT_PRIMARY }]}>Yeni</Text>
          </TouchableOpacity>
        )}
      </View>

      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.container}
        contentContainerStyle={styles.scrollContent}
      >
        {categories.map(category => (
            <TouchableOpacity
              key={category.id}
              style={[
                styles.categoryItem,
                { backgroundColor: theme.SURFACE_VARIANT, borderColor: theme.BORDER },
                selectedCategoryId === category.id && {
                  backgroundColor: category.color || Colors.PRIMARY
                }
              ]}
              onPress={() => onSelectCategory(category.id)}
            >
              <View style={[styles.categoryIconContainer, { backgroundColor: theme.BACKGROUND }]}>
                <MaterialIcons
                  name={category.icon || 'category'}
                  size={24}
                  color={selectedCategoryId === category.id ? '#fff' : category.color || Colors.PRIMARY}
                />
              </View>
              <Text
                style={[
                  styles.categoryText,
                  { color: theme.TEXT_PRIMARY },
                  selectedCategoryId === category.id && styles.activeCategoryText
                ]}
                numberOfLines={1}
              >
                {category.name}
              </Text>
            </TouchableOpacity>
        ))}

        {/* Yeni Kategori Butonu */}
        {onAddCategory && (
          <TouchableOpacity
            style={[styles.addCategoryButton, { backgroundColor: theme.SURFACE_VARIANT, borderColor: theme.BORDER }]}
            onPress={() => onAddCategory(type)}
          >
            <View style={[styles.addIconContainer, { backgroundColor: theme.BACKGROUND }]}>
              <MaterialIcons
                name="add"
                size={24}
                color={Colors.PRIMARY}
              />
            </View>
            <Text style={[styles.addCategoryText, { color: theme.TEXT_PRIMARY }]}>
              Yeni Kategori
            </Text>
          </TouchableOpacity>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    marginBottom: 12,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  headerTitle: {
    fontSize: 14,
    fontWeight: '500',
  },
  addCategoryButtonSmall: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
  },
  addCategoryTextSmall: {
    fontSize: 12,
    marginLeft: 2,
  },
  container: {
    flexDirection: 'row',
  },
  scrollContent: {
    paddingVertical: 4,
  },
  categoryItem: {
    width: 80,
    alignItems: 'center',
    paddingVertical: 8,
    borderWidth: 1,
    borderRadius: 12,
    marginRight: 8,
  },
  categoryIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 4,
  },
  categoryText: {
    fontSize: 12,
    textAlign: 'center',
    paddingHorizontal: 4,
    width: '100%',
  },
  activeCategoryText: {
    color: '#fff',
  },
  addCategoryButton: {
    width: 80,
    alignItems: 'center',
    paddingVertical: 8,
    borderWidth: 1,
    borderRadius: 12,
    marginRight: 8,
  },
  addIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 4,
  },
  addCategoryText: {
    fontSize: 12,
    textAlign: 'center',
    paddingHorizontal: 4,
    width: '100%',
  },
});

export default CategorySelectorSimple;
