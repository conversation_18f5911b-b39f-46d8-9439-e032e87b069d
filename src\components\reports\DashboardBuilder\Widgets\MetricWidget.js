import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

/**
 * Metric Widget - <PERSON><PERSON><PERSON> met<PERSON>
 */
const MetricWidget = ({ widget, isPreviewMode, onUpdate, theme }) => {
  /**
   * <PERSON><PERSON><PERSON>li tema değeri alma
   * @param {string} property - <PERSON><PERSON>
   * @param {string} fallback - <PERSON><PERSON><PERSON><PERSON><PERSON>
   * @returns {string} <PERSON><PERSON><PERSON>li tema değeri
   */
  const getSafeThemeValue = (property, fallback) => {
    return theme[property] || theme.colors?.[property.toLowerCase()] || fallback;
  };

  const mockMetrics = [
    { label: 'Gelir', value: '₺25,430', color: getSafeThemeValue('SUCCESS', '#28a745') },
    { label: 'Gider', value: '₺18,250', color: getSafeThemeValue('ERROR', '#dc3545') },
    { label: 'Bakiye', value: '₺7,180', color: getSafeThemeValue('INFO', '#17a2b8') },
  ];

  return (
    <View style={[styles.container, { backgroundColor: getSafeThemeValue('BACKGROUND', '#fff') }]}>
      <View style={styles.metricsGrid}>
        {mockMetrics.map((metric, index) => (
          <View key={index} style={styles.metricItem}>
            <Text style={[styles.metricValue, { color: metric.color }]}>
              {metric.value}
            </Text>
            <Text style={[styles.metricLabel, { color: getSafeThemeValue('TEXT_SECONDARY', '#666') }]}>
              {metric.label}
            </Text>
          </View>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 8,
  },
  metricsGrid: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  metricItem: {
    alignItems: 'center',
    marginVertical: 4,
  },
  metricValue: {
    fontSize: 14,
    fontWeight: '700',
    marginBottom: 2,
  },
  metricLabel: {
    fontSize: 10,
    fontWeight: '500',
  },
});

export default MetricWidget;
