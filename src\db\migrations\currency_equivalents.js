/**
 * Tabloya döviz karşılığı sütunlarını ekler (kontrollü)
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {string} tableName - <PERSON>blo adı
 * @returns {Promise<void>}
 */
const addCurrencyColumnsToTable = async (db, tableName) => {
  try {
    // Tablo var mı kontrol et
    const tableExists = await db.getFirstAsync(`
      SELECT name FROM sqlite_master WHERE type='table' AND name=?
    `, [tableName]);

    if (!tableExists) {
      console.log(`${tableName} tablosu bulunamadı, atlanıyor...`);
      return;
    }

    // Mevcut sütunları kontrol et
    const columns = await db.getAllAsync(`PRAGMA table_info(${tableName})`);
    const columnNames = columns.map(col => col.name);

    // Her sütunu ayrı ayrı kontrol et ve ekle
    const currencyColumns = [
      { name: 'usd_equivalent', type: 'REAL DEFAULT 0' },
      { name: 'eur_equivalent', type: 'REAL DEFAULT 0' },
      { name: 'custom_currency', type: 'TEXT DEFAULT NULL' },
      { name: 'custom_equivalent', type: 'REAL DEFAULT 0' }
    ];

    for (const column of currencyColumns) {
      if (!columnNames.includes(column.name)) {
        try {
          await db.execAsync(`ALTER TABLE ${tableName} ADD COLUMN ${column.name} ${column.type}`);
          console.log(`${tableName} tablosuna ${column.name} sütunu eklendi.`);
        } catch (columnError) {
          if (columnError.message?.includes('duplicate column name')) {
            console.log(`${tableName} tablosunda ${column.name} sütunu zaten mevcut.`);
          } else {
            throw columnError;
          }
        }
      } else {
        console.log(`${tableName} tablosunda ${column.name} sütunu zaten mevcut.`);
      }
    }
  } catch (error) {
    console.error(`${tableName} tablosuna döviz sütunları ekleme hatası:`, error);
    throw error;
  }
};

/**
 * Para birimlerinin döviz karşılıklarını saklamak için veritabanı yapısını güncelleyen migrasyon
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<void>}
 */
export const migrateCurrencyEquivalents = async (db) => {
  try {
    console.log('Döviz karşılıkları migrasyonu başlatılıyor...');

    // Transaction kullanmadan migration yap
    // Her işlemi ayrı ayrı çalıştır

    // Tablolara döviz karşılıkları ekle (kontrollü)
    await addCurrencyColumnsToTable(db, 'salaries');
    await addCurrencyColumnsToTable(db, 'salary_payments');
    await addCurrencyColumnsToTable(db, 'transactions');
    await addCurrencyColumnsToTable(db, 'work_shifts');

    // Kullanıcı ayarları tablosunu oluştur veya güncelle
    const hasSettingsTable = await db.getFirstAsync(`
      SELECT name FROM sqlite_master WHERE type='table' AND name='user_settings'
    `);

    if (!hasSettingsTable) {
      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS user_settings (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          key TEXT NOT NULL UNIQUE,
          value TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);
    }

    // Varsayılan özel para birimini ekle
    await db.execAsync(`
      INSERT OR IGNORE INTO user_settings (key, value)
      VALUES ('custom_currency', 'GBP')
    `);

    console.log('Döviz karşılıkları migrasyonu tamamlandı.');
  } catch (error) {
    // Eğer sütun zaten varsa hata verme
    if (error.message?.includes('duplicate column name')) {
      console.warn('Döviz karşılıkları sütunları zaten mevcut:', error.message);
    } else {
      console.error('Döviz karşılıkları migrasyonu hatası:', error);
      throw error;
    }
  }
};
