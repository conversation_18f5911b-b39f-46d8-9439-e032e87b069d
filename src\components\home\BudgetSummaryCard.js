import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';

/**
 * Bütçe özeti kartı bileşeni
 * 
 * @param {Object} props - Bileşen props'ları
 * @param {Function} props.onPress - <PERSON>rta tı<PERSON> o<PERSON>
 * @returns {JSX.Element} BudgetSummaryCard bileşeni
 */
const BudgetSummaryCard = ({ onPress }) => {
  // Örnek bütçe verileri
  const budgetData = [
    { category: 'Yemek', spent: 750, budget: 1000, color: '#FF6384' },
    { category: 'Ulaşım', spent: 300, budget: 500, color: '#36A2EB' },
    { category: 'Eğlence', spent: 400, budget: 600, color: '#FFCE56' }
  ];
  
  // İlerleme çubuğu bileşeni
  const ProgressBar = ({ spent, budget, color }) => {
    const percentage = Math.min((spent / budget) * 100, 100);
    
    return (
      <View style={styles.progressBarContainer}>
        <View 
          style={[
            styles.progressBar, 
            { width: `${percentage}%`, backgroundColor: color }
          ]} 
        />
      </View>
    );
  };
  
  // Para birimini formatla
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0
    }).format(amount);
  };
  
  return (
    <TouchableOpacity style={styles.container} onPress={onPress} activeOpacity={0.9}>
      <View style={styles.header}>
        <Text style={styles.title}>Bütçe Özeti</Text>
        <MaterialIcons name="chevron-right" size={24} color="#666" />
      </View>
      
      {budgetData.map((item, index) => (
        <View key={index} style={styles.budgetItem}>
          <View style={styles.budgetItemHeader}>
            <Text style={styles.categoryName}>{item.category}</Text>
            <Text style={styles.budgetValues}>
              {formatCurrency(item.spent)} / {formatCurrency(item.budget)}
            </Text>
          </View>
          <ProgressBar 
            spent={item.spent} 
            budget={item.budget} 
            color={item.color} 
          />
        </View>
      ))}
      
      <TouchableOpacity style={styles.viewBudgetButton} onPress={onPress}>
        <Text style={styles.viewBudgetText}>Bütçe Detaylarını Görüntüle</Text>
      </TouchableOpacity>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    margin: 16,
    marginTop: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  budgetItem: {
    marginBottom: 16,
  },
  budgetItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryName: {
    fontSize: 16,
    color: '#333',
  },
  budgetValues: {
    fontSize: 14,
    color: '#666',
  },
  progressBarContainer: {
    height: 8,
    backgroundColor: '#f0f0f0',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    borderRadius: 4,
  },
  viewBudgetButton: {
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginTop: 8,
  },
  viewBudgetText: {
    color: '#3498db',
    fontWeight: '600',
  },
});

export default BudgetSummaryCard;
