/**
 * <PERSON><PERSON><PERSON> işlemleri için yardımcı fonksiyonlar
 */

/**
 * Gü<PERSON>li tarih aralığı oluşturur
 * @param {string} rangeType - Ta<PERSON>h aralığı tipi ('last3months', 'last6months', etc.)
 * @returns {Object} {startDate, endDate} formatında string tarihler
 */
export const getSafeDateRange = (rangeType = 'last30days') => {
  try {
    const endDate = new Date();
    let startDate = new Date();
    
    // Bugünün tarihini sıfırla (sadece tarih kısmı)
    endDate.setHours(23, 59, 59, 999);
    startDate.setHours(0, 0, 0, 0);
    
    switch (rangeType) {
      case 'last30days':
        startDate.setDate(endDate.getDate() - 30);
        break;
      case 'last3months':
        startDate.setMonth(endDate.getMonth() - 3);
        break;
      case 'last6months':
        startDate.setMonth(endDate.getMonth() - 6);
        break;
      case 'last12months':
        startDate.setMonth(endDate.getMonth() - 12);
        break;
      case 'thisMonth':
        startDate = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
        break;
      case 'thisYear':
        startDate = new Date(endDate.getFullYear(), 0, 1);
        break;
      case 'lastYear':
        const lastYear = endDate.getFullYear() - 1;
        startDate = new Date(lastYear, 0, 1);
        endDate.setFullYear(lastYear);
        endDate.setMonth(11);
        endDate.setDate(31);
        break;
      default:
        // Varsayılan: son 30 gün
        startDate.setDate(endDate.getDate() - 30);
    }
    
    // Tarih geçerliliğini kontrol et
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      // Fallback: son 30 gün
      const fallbackEnd = new Date();
      const fallbackStart = new Date();
      fallbackStart.setDate(fallbackEnd.getDate() - 30);
      
      return {
        startDate: fallbackStart.toISOString().split('T')[0],
        endDate: fallbackEnd.toISOString().split('T')[0],
        startDateObject: fallbackStart,
        endDateObject: fallbackEnd
      };
    }
    
    return {
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0],
      startDateObject: startDate,
      endDateObject: endDate
    };
  } catch (error) {
    console.error('Tarih aralığı oluşturma hatası:', error);
    
    // Hata durumunda güvenli fallback
    const fallbackEnd = new Date();
    const fallbackStart = new Date();
    fallbackStart.setDate(fallbackEnd.getDate() - 30);
    
    return {
      startDate: fallbackStart.toISOString().split('T')[0],
      endDate: fallbackEnd.toISOString().split('T')[0],
      startDateObject: fallbackStart,
      endDateObject: fallbackEnd
    };
  }
};

/**
 * Tarihi güvenli şekilde formatlar
 * @param {Date|string} date - Formatlanacak tarih
 * @param {string} format - Format tipi ('YYYY-MM-DD', 'DD/MM/YYYY', etc.)
 * @returns {string} Formatlanmış tarih
 */
export const formatDateSafely = (date, format = 'YYYY-MM-DD') => {
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    
    if (isNaN(dateObj.getTime())) {
      return 'Geçersiz Tarih';
    }
    
    switch (format) {
      case 'YYYY-MM-DD':
        return dateObj.toISOString().split('T')[0];
      case 'DD/MM/YYYY':
        return dateObj.toLocaleDateString('tr-TR');
      case 'DD.MM.YYYY':
        return dateObj.toLocaleDateString('tr-TR').replace(/\//g, '.');
      case 'readable':
        return dateObj.toLocaleDateString('tr-TR', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
      default:
        return dateObj.toISOString().split('T')[0];
    }
  } catch (error) {
    console.error('Tarih formatlama hatası:', error);
    return 'Tarih Hatası';
  }
};

/**
 * İki tarih arasındaki farkı hesaplar
 * @param {Date|string} startDate - Başlangıç tarihi
 * @param {Date|string} endDate - Bitiş tarihi
 * @returns {number} Gün cinsinden fark
 */
export const getDateDifferenceInDays = (startDate, endDate) => {
  try {
    const start = typeof startDate === 'string' ? new Date(startDate) : startDate;
    const end = typeof endDate === 'string' ? new Date(endDate) : endDate;
    
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return 0;
    }
    
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays;
  } catch (error) {
    console.error('Tarih farkı hesaplama hatası:', error);
    return 0;
  }
};

/**
 * Tarihin geçerli olup olmadığını kontrol eder
 * @param {Date|string} date - Kontrol edilecek tarih
 * @returns {boolean} Tarih geçerli mi?
 */
export const isValidDate = (date) => {
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return !isNaN(dateObj.getTime());
  } catch (error) {
    return false;
  }
};

/**
 * Önceden tanımlanmış tarih aralıkları
 */
export const DATE_RANGES = {
  LAST_30_DAYS: 'last30days',
  LAST_3_MONTHS: 'last3months',
  LAST_6_MONTHS: 'last6months',
  LAST_12_MONTHS: 'last12months',
  THIS_MONTH: 'thisMonth',
  THIS_YEAR: 'thisYear',
  LAST_YEAR: 'lastYear'
};

/**
 * Tarih aralığı seçenekleri (UI için)
 */
export const DATE_RANGE_OPTIONS = [
  { value: DATE_RANGES.LAST_30_DAYS, label: 'Son 30 Gün' },
  { value: DATE_RANGES.LAST_3_MONTHS, label: 'Son 3 Ay' },
  { value: DATE_RANGES.LAST_6_MONTHS, label: 'Son 6 Ay' },
  { value: DATE_RANGES.LAST_12_MONTHS, label: 'Son 12 Ay' },
  { value: DATE_RANGES.THIS_MONTH, label: 'Bu Ay' },
  { value: DATE_RANGES.THIS_YEAR, label: 'Bu Yıl' },
  { value: DATE_RANGES.LAST_YEAR, label: 'Geçen Yıl' }
];
