import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  TextInput,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const { width: screenWidth } = Dimensions.get('window');

/**
 * Component Palette
 * Provides a searchable library of draggable components
 * Organized by categories with preview and descriptions
 */
const ComponentPalette = ({ 
  onComponentSelect,
  onComponentDrag,
  theme 
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Component categories
  const categories = [
    { id: 'all', name: 'Tümü', icon: 'apps' },
    { id: 'charts', name: '<PERSON><PERSON><PERSON>', icon: 'bar-chart' },
    { id: 'data', name: 'Veri', icon: 'grid' },
    { id: 'text', name: 'Metin', icon: 'text' },
    { id: 'controls', name: '<PERSON><PERSON><PERSON><PERSON>', icon: 'options' },
    { id: 'layout', name: '<PERSON><PERSON><PERSON>', icon: 'resize' },
  ];

  // Available components
  const components = [
    // Charts Category
    {
      id: 'bar_chart',
      category: 'charts',
      title: 'Çubuk Grafik',
      icon: 'bar-chart',
      description: 'Kategorik verileri karşılaştırmak için',
      tags: ['grafik', 'çubuk', 'karşılaştırma'],
      defaultProps: {
        chartType: 'bar',
        dataSource: 'transactions',
        width: screenWidth - 32,
        height: 200,
        showLegend: true,
        showGrid: true,
      },
    },
    {
      id: 'pie_chart',
      category: 'charts',
      title: 'Pasta Grafik',
      icon: 'pie-chart',
      description: 'Oranları göstermek için',
      tags: ['grafik', 'pasta', 'oran', 'yüzde'],
      defaultProps: {
        chartType: 'pie',
        dataSource: 'categories',
        width: (screenWidth - 48) / 2,
        height: 200,
        showLabels: true,
        showPercentages: true,
      },
    },
    {
      id: 'line_chart',
      category: 'charts',
      title: 'Çizgi Grafik',
      icon: 'trending-up',
      description: 'Zaman serisi verileri için',
      tags: ['grafik', 'çizgi', 'trend', 'zaman'],
      defaultProps: {
        chartType: 'line',
        dataSource: 'monthly_data',
        width: screenWidth - 32,
        height: 200,
        showPoints: true,
        smooth: true,
      },
    },

    // Data Category
    {
      id: 'data_table',
      category: 'data',
      title: 'Veri Tablosu',
      icon: 'grid',
      description: 'Detaylı veri listesi',
      tags: ['tablo', 'veri', 'liste', 'detay'],
      defaultProps: {
        dataSource: 'transactions',
        columns: ['date', 'amount', 'category', 'description'],
        width: screenWidth - 32,
        height: 300,
        sortable: true,
        filterable: true,
      },
    },
    {
      id: 'kpi_card',
      category: 'data',
      title: 'KPI Kartı',
      icon: 'speedometer',
      description: 'Anahtar performans göstergesi',
      tags: ['kpi', 'metrik', 'performans', 'kart'],
      defaultProps: {
        metric: 'total_income',
        format: 'currency',
        width: (screenWidth - 48) / 2,
        height: 120,
        showTrend: true,
        trendPeriod: 'monthly',
      },
    },
    {
      id: 'summary_card',
      category: 'data',
      title: 'Özet Kartı',
      icon: 'list',
      description: 'Finansal özet bilgileri',
      tags: ['özet', 'kart', 'finansal', 'toplam'],
      defaultProps: {
        summaryType: 'monthly',
        width: screenWidth - 32,
        height: 150,
        showComparison: true,
        comparisonPeriod: 'previous_month',
      },
    },

    // Text Category
    {
      id: 'heading',
      category: 'text',
      title: 'Başlık',
      icon: 'text',
      description: 'Rapor başlığı veya bölüm başlığı',
      tags: ['başlık', 'metin', 'title'],
      defaultProps: {
        content: 'Yeni Başlık',
        fontSize: 24,
        fontWeight: 'bold',
        textAlign: 'center',
        width: screenWidth - 32,
        height: 60,
      },
    },
    {
      id: 'paragraph',
      category: 'text',
      title: 'Paragraf',
      icon: 'document-text',
      description: 'Açıklama metni veya notlar',
      tags: ['paragraf', 'metin', 'açıklama'],
      defaultProps: {
        content: 'Buraya açıklama metni yazın...',
        fontSize: 14,
        fontWeight: 'normal',
        textAlign: 'left',
        width: screenWidth - 32,
        height: 80,
      },
    },

    // Controls Category
    {
      id: 'date_filter',
      category: 'controls',
      title: 'Tarih Filtresi',
      icon: 'calendar',
      description: 'Tarih aralığı seçici',
      tags: ['filtre', 'tarih', 'seçici'],
      defaultProps: {
        filterType: 'date_range',
        defaultRange: 'this_month',
        width: (screenWidth - 48) / 2,
        height: 80,
        showPresets: true,
      },
    },
    {
      id: 'category_filter',
      category: 'controls',
      title: 'Kategori Filtresi',
      icon: 'filter',
      description: 'Kategori seçici',
      tags: ['filtre', 'kategori', 'seçici'],
      defaultProps: {
        filterType: 'category',
        multiSelect: true,
        width: (screenWidth - 48) / 2,
        height: 80,
        showSelectAll: true,
      },
    },

    // Layout Category
    {
      id: 'divider',
      category: 'layout',
      title: 'Ayırıcı',
      icon: 'remove',
      description: 'Bölümler arası ayırıcı çizgi',
      tags: ['ayırıcı', 'çizgi', 'bölüm'],
      defaultProps: {
        orientation: 'horizontal',
        thickness: 1,
        width: screenWidth - 32,
        height: 20,
        style: 'solid',
      },
    },
    {
      id: 'spacer',
      category: 'layout',
      title: 'Boşluk',
      icon: 'square-outline',
      description: 'Bileşenler arası boşluk',
      tags: ['boşluk', 'space', 'margin'],
      defaultProps: {
        width: screenWidth - 32,
        height: 40,
        backgroundColor: 'transparent',
      },
    },
  ];

  // Filter components based on search and category
  const filteredComponents = components.filter(component => {
    const matchesSearch = searchQuery === '' || 
      component.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      component.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      component.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || component.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const handleComponentPress = (component) => {
    onComponentSelect(component);
  };

  const handleComponentLongPress = (component) => {
    onComponentDrag(component);
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.SURFACE }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
          Bileşen Paleti
        </Text>
        
        {/* Search */}
        <View style={[styles.searchContainer, { backgroundColor: theme.BACKGROUND }]}>
          <Ionicons name="search" size={16} color={theme.TEXT_SECONDARY} />
          <TextInput
            style={[styles.searchInput, { color: theme.TEXT_PRIMARY }]}
            placeholder="Bileşen ara..."
            placeholderTextColor={theme.TEXT_SECONDARY}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery !== '' && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close" size={16} color={theme.TEXT_SECONDARY} />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Categories */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.categoriesContainer}
      >
        {categories.map((category) => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryButton,
              {
                backgroundColor: selectedCategory === category.id 
                  ? theme.PRIMARY 
                  : theme.BACKGROUND,
                borderColor: theme.BORDER,
              }
            ]}
            onPress={() => setSelectedCategory(category.id)}
          >
            <Ionicons 
              name={category.icon} 
              size={16} 
              color={selectedCategory === category.id ? theme.SURFACE : theme.TEXT_PRIMARY} 
            />
            <Text style={[
              styles.categoryText,
              { 
                color: selectedCategory === category.id 
                  ? theme.SURFACE 
                  : theme.TEXT_PRIMARY 
              }
            ]}>
              {category.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Components Grid */}
      <ScrollView style={styles.componentsContainer}>
        <View style={styles.componentsGrid}>
          {filteredComponents.map((component) => (
            <TouchableOpacity
              key={component.id}
              style={[
                styles.componentCard,
                { 
                  backgroundColor: theme.BACKGROUND,
                  borderColor: theme.BORDER,
                }
              ]}
              onPress={() => handleComponentPress(component)}
              onLongPress={() => handleComponentLongPress(component)}
              activeOpacity={0.7}
            >
              <View style={styles.componentIcon}>
                <Ionicons 
                  name={component.icon} 
                  size={24} 
                  color={theme.PRIMARY} 
                />
              </View>
              
              <Text style={[styles.componentTitle, { color: theme.TEXT_PRIMARY }]}>
                {component.title}
              </Text>
              
              <Text style={[styles.componentDescription, { color: theme.TEXT_SECONDARY }]}>
                {component.description}
              </Text>

              <View style={styles.componentTags}>
                {component.tags.slice(0, 2).map((tag, index) => (
                  <View 
                    key={index}
                    style={[styles.tag, { backgroundColor: theme.PRIMARY + '20' }]}
                  >
                    <Text style={[styles.tagText, { color: theme.PRIMARY }]}>
                      {tag}
                    </Text>
                  </View>
                ))}
              </View>
            </TouchableOpacity>
          ))}
        </View>

        {filteredComponents.length === 0 && (
          <View style={styles.emptyState}>
            <Ionicons name="search" size={48} color={theme.TEXT_SECONDARY} />
            <Text style={[styles.emptyStateText, { color: theme.TEXT_SECONDARY }]}>
              {searchQuery 
                ? `"${searchQuery}" için sonuç bulunamadı`
                : 'Bu kategoride bileşen bulunamadı'
              }
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    gap: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 14,
  },
  categoriesContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    borderWidth: 1,
    marginRight: 8,
    gap: 6,
  },
  categoryText: {
    fontSize: 12,
    fontWeight: '600',
  },
  componentsContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  componentsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  componentCard: {
    width: (screenWidth - 56) / 2,
    padding: 12,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 12,
  },
  componentIcon: {
    alignItems: 'center',
    marginBottom: 8,
  },
  componentTitle: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 4,
  },
  componentDescription: {
    fontSize: 12,
    textAlign: 'center',
    marginBottom: 8,
    lineHeight: 16,
  },
  componentTags: {
    flexDirection: 'row',
    justifyContent: 'center',
    flexWrap: 'wrap',
    gap: 4,
  },
  tag: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  tagText: {
    fontSize: 10,
    fontWeight: '500',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyStateText: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: 12,
  },
});

export default ComponentPalette;
