/**
 * <PERSON><PERSON>ının yapılandırma sistemi
 * Her şablon için meta veriler, parametreler ve yapılandırma seçenekleri
 */

export const TEMPLATE_TYPES = {
  MONTHLY_INCOME_EXPENSE: 'monthly_income_expense',
  CATEGORY_DISTRIBUTION: 'category_distribution',
  CASH_FLOW: 'cash_flow',
  BUDGET_VS_ACTUAL: 'budget_vs_actual',
  SHIFT_INCOME: 'shift_income',
  REGULAR_INCOME: 'regular_income',
  SUMMARY_OVERVIEW: 'summary_overview'
};

export const TEMPLATE_CATEGORIES = {
  FINANCIAL_ANALYSIS: 'financial_analysis',
  INCOME_TRACKING: 'income_tracking',
  EXPENSE_MANAGEMENT: 'expense_management',
  BUDGET_CONTROL: 'budget_control',
  OVERVIEW: 'overview'
};

/**
 * Şablon meta verilerini ve yapılandırmalarını içeren obje
 */
export const TEMPLATE_CONFIGS = {
  [TEMPLATE_TYPES.MONTHLY_INCOME_EXPENSE]: {
    id: TEMPLATE_TYPES.MONTHLY_INCOME_EXPENSE,
    name: 'Aylık Gelir-Gider Raporu',
    description: 'Aylık gelir ve gider karşılaştırması ile net kar/zarar analizi',
    icon: 'calendar',
    category: TEMPLATE_CATEGORIES.FINANCIAL_ANALYSIS,
    defaultParams: {
      dateRange: 'current_month',
      groupBy: 'month',
      includeCategories: true,
      showComparisons: true
    },
    requiredData: ['transactions', 'categories'],
    outputTypes: ['table', 'chart'],
    estimatedComplexity: 'medium',
    tags: ['gelir', 'gider', 'aylık', 'kar-zarar']
  },
  
  [TEMPLATE_TYPES.CATEGORY_DISTRIBUTION]: {
    id: TEMPLATE_TYPES.CATEGORY_DISTRIBUTION,
    name: 'Kategori Dağılım Analizi',
    description: 'Gelir ve gider kategorilerinin detaylı dağılım analizi ve görselleştirmesi',
    icon: 'pie-chart',
    category: TEMPLATE_CATEGORIES.EXPENSE_MANAGEMENT,
    defaultParams: {
      dateRange: 'current_month',
      groupBy: 'category',
      includeSubcategories: false,
      sortBy: 'amount',
      sortOrder: 'desc',
      showPercentages: true,
      showTransactionCounts: true,
      minAmountFilter: 0
    },
    requiredData: ['transactions', 'categories'],
    outputTypes: ['pie_chart', 'bar_chart', 'table', 'donut_chart'],
    estimatedComplexity: 'medium',
    tags: ['kategori', 'dağılım', 'analiz', 'görselleştirme', 'pasta', 'yüzde'],
    features: {
      interactiveCharts: true,
      exportToPdf: true,
      exportToCsv: true,
      realTimeUpdates: true,
      customization: {
        colors: true,
        chartTypes: true,
        filters: true
      }
    },
    sampleData: {
      categories: [
        { name: 'Gıda & İçecek', amount: 2500, percentage: 35.5 },
        { name: 'Ulaşım', amount: 1800, percentage: 25.5 },
        { name: 'Eğlence', amount: 1200, percentage: 17.0 },
        { name: 'Sağlık', amount: 800, percentage: 11.4 },
        { name: 'Diğer', amount: 750, percentage: 10.6 }
      ]
    }
  },
  
  [TEMPLATE_TYPES.CASH_FLOW]: {
    id: TEMPLATE_TYPES.CASH_FLOW,
    name: 'Nakit Akış Raporu',
    description: 'Günlük/haftalık nakit giriş-çıkış takibi',
    icon: 'trending-up',
    category: TEMPLATE_CATEGORIES.FINANCIAL_ANALYSIS,
    defaultParams: {
      dateRange: 'last_month',
      interval: 'daily',
      showCumulative: true,
      includeProjections: false
    },
    requiredData: ['transactions'],
    outputTypes: ['line_chart', 'table'],
    estimatedComplexity: 'medium',
    tags: ['nakit', 'akış', 'günlük', 'trend']
  },
  
  [TEMPLATE_TYPES.BUDGET_VS_ACTUAL]: {
    id: TEMPLATE_TYPES.BUDGET_VS_ACTUAL,
    name: 'Bütçe vs Gerçekleşen',
    description: 'Bütçe hedefleri ile gerçekleşen harcamaların karşılaştırması',
    icon: 'target',
    category: TEMPLATE_CATEGORIES.BUDGET_CONTROL,
    defaultParams: {
      dateRange: 'current_month',
      showVariances: true,
      highlightOverBudget: true,
      includeForecasts: true
    },
    requiredData: ['transactions', 'budgets', 'categories'],
    outputTypes: ['comparison_chart', 'table'],
    estimatedComplexity: 'high',
    tags: ['bütçe', 'hedef', 'karşılaştırma', 'varyans']
  },
  
  [TEMPLATE_TYPES.SHIFT_INCOME]: {
    id: TEMPLATE_TYPES.SHIFT_INCOME,
    name: 'Mesai Gelir Analizi',
    description: 'Mesai ve fazla mesai gelirlerinin detaylı analizi',
    icon: 'clock',
    category: TEMPLATE_CATEGORIES.INCOME_TRACKING,
    defaultParams: {
      dateRange: 'last_month',
      includeOvertimeCalculations: true,
      showHourlyRates: true,
      groupByShiftType: true
    },
    requiredData: ['transactions', 'shift_data'],
    outputTypes: ['table', 'bar_chart'],
    estimatedComplexity: 'medium',
    tags: ['mesai', 'gelir', 'analiz', 'saat']
  },
  
  [TEMPLATE_TYPES.REGULAR_INCOME]: {
    id: TEMPLATE_TYPES.REGULAR_INCOME,
    name: 'Düzenli Gelir Takibi',
    description: 'Maaş, kira vb. düzenli gelir kaynaklarının takibi',
    icon: 'repeat',
    category: TEMPLATE_CATEGORIES.INCOME_TRACKING,
    defaultParams: {
      dateRange: 'last_6_months',
      showTrends: true,
      predictNextPeriod: true,
      includeGrowthAnalysis: true
    },
    requiredData: ['transactions', 'recurring_incomes'],
    outputTypes: ['line_chart', 'table'],
    estimatedComplexity: 'medium',
    tags: ['düzenli', 'gelir', 'trend', 'tahmin']
  },
  
  [TEMPLATE_TYPES.SUMMARY_OVERVIEW]: {
    id: TEMPLATE_TYPES.SUMMARY_OVERVIEW,
    name: 'Temel Özet Raporu',
    description: 'Finansal durumun genel özeti ve temel metrikler',
    icon: 'file-text',
    category: TEMPLATE_CATEGORIES.OVERVIEW,
    defaultParams: {
      dateRange: 'current_month',
      includeComparisons: true,
      showKPIs: true,
      includeInsights: true
    },
    requiredData: ['transactions', 'categories'],
    outputTypes: ['dashboard', 'table'],
    estimatedComplexity: 'low',
    tags: ['özet', 'genel', 'kpi', 'dashboard']
  }
};

/**
 * Şablon kategorilerinin görüntüleme bilgileri
 */
export const CATEGORY_DISPLAY_INFO = {
  [TEMPLATE_CATEGORIES.FINANCIAL_ANALYSIS]: {
    name: 'Finansal Analiz',
    icon: 'analytics',
    color: '#3B82F6',
    description: 'Detaylı finansal analizler ve karşılaştırmalar'
  },
  [TEMPLATE_CATEGORIES.INCOME_TRACKING]: {
    name: 'Gelir Takibi',
    icon: 'trending-up',
    color: '#10B981',
    description: 'Gelir kaynaklarının takibi ve analizi'
  },
  [TEMPLATE_CATEGORIES.EXPENSE_MANAGEMENT]: {
    name: 'Gider Yönetimi',
    icon: 'shopping-cart',
    color: '#F59E0B',
    description: 'Gider kategorilerinin yönetimi ve analizi'
  },
  [TEMPLATE_CATEGORIES.BUDGET_CONTROL]: {
    name: 'Bütçe Kontrolü',
    icon: 'target',
    color: '#8B5CF6',
    description: 'Bütçe hedeflerinin kontrolü ve takibi'
  },
  [TEMPLATE_CATEGORIES.OVERVIEW]: {
    name: 'Genel Bakış',
    icon: 'eye',
    color: '#6B7280',
    description: 'Finansal durumun genel özeti'
  }
};

/**
 * Şablon listesini kategori bazında döndürür
 */
export const getTemplatesByCategory = () => {
  const categorizedTemplates = {};
  
  Object.values(TEMPLATE_CONFIGS).forEach(template => {
    const category = template.category;
    if (!categorizedTemplates[category]) {
      categorizedTemplates[category] = [];
    }
    categorizedTemplates[category].push(template);
  });
  
  return categorizedTemplates;
};

/**
 * Belirtilen ID'ye göre şablon yapılandırmasını döndürür
 */
export const getTemplateConfig = (templateId) => {
  return TEMPLATE_CONFIGS[templateId] || null;
};

/**
 * Şablon arama fonksiyonu
 */
export const searchTemplates = (query) => {
  if (!query) return Object.values(TEMPLATE_CONFIGS);
  
  const lowerQuery = query.toLowerCase();
  return Object.values(TEMPLATE_CONFIGS).filter(template => {
    return template.name.toLowerCase().includes(lowerQuery) ||
           template.description.toLowerCase().includes(lowerQuery) ||
           template.tags.some(tag => tag.toLowerCase().includes(lowerQuery));
  });
};
