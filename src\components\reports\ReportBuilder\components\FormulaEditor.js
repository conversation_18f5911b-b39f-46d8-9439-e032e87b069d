import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from '../../../../context/ThemeContext';

/**
 * Formül Editörü - Excel benzeri formül sistemi
 * Hesaplanmış alanlar ve özel formüller
 */
const FormulaEditor = ({
  reportData = [],
  onFormulaApply,
}) => {
  const { theme } = useTheme();

  return (
    <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
        🧮 Formül Editörü
      </Text>
      <Text style={[styles.subtitle, { color: theme.TEXT_SECONDARY }]}>
        Excel benzeri formül sistemi - Yakında aktif olacak
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    textAlign: 'center',
  },
});

export default FormulaEditor;
