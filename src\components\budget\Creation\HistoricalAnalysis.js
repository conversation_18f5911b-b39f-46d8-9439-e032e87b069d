/**
 * Geçmiş Harcama Analizi Komponenti
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md - Stage 2, Phase 3
 * 
 * Geçmiş harcama verilerinin analizi ve trendleri
 * Maksimum 300 satır - Tek sorumluluk prensibi
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../../../context/ThemeContext';
import { useSQLiteContext } from 'expo-sqlite';
import { formatTRY, formatUSD, formatEUR } from '../../../utils/formatters';

const { width } = Dimensions.get('window');

/**
 * Geçmiş harcama analizi komponenti
 * @param {Object} props - Component props
 * @param {Array} props.selectedCategories - Seçili kate<PERSON>iler
 * @param {string} props.period - Dönem türü ('monthly', 'weekly', 'custom')
 * @param {string} props.currency - Para birimi ('TRY', 'USD', 'EUR')
 * @param {Function} props.onAnalysisComplete - Analiz tamamlama callback
 * @param {Object} props.theme - Tema objesi (opsiyonel, context'ten alınır)
 */
const HistoricalAnalysis = ({ 
  selectedCategories = [], 
  period = 'monthly',
  currency = 'TRY',
  onAnalysisComplete,
  theme: propTheme 
}) => {
  const { theme } = useTheme();
  const currentTheme = propTheme || theme;
  const db = useSQLiteContext();

  // State yönetimi
  const [analysisData, setAnalysisData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [selectedPeriodRange, setSelectedPeriodRange] = useState('3months');

  // Dönem seçenekleri
  const periodOptions = [
    { key: '1month', label: '1 Ay', months: 1 },
    { key: '3months', label: '3 Ay', months: 3 },
    { key: '6months', label: '6 Ay', months: 6 },
    { key: '12months', label: '1 Yıl', months: 12 }
  ];

  /**
   * Geçmiş harcama verilerini analiz et
   */
  const analyzeHistoricalData = async () => {
    try {
      setLoading(true);
      
      const selectedOption = periodOptions.find(opt => opt.key === selectedPeriodRange);
      const monthsBack = selectedOption?.months || 3;
      
      // Tarih aralığını hesapla
      const endDate = new Date();
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() - monthsBack);

      // Kategori ID'lerini al
      const categoryIds = selectedCategories.map(cat => cat.id);
      
      // Geçmiş harcamaları getir
      const historicalSpending = await getHistoricalSpending(
        startDate.toISOString().split('T')[0],
        endDate.toISOString().split('T')[0],
        categoryIds
      );

      // Analiz verilerini hesapla
      const analysis = calculateAnalysis(historicalSpending, monthsBack);
      setAnalysisData(analysis);

      // Callback'i çağır
      if (onAnalysisComplete) {
        onAnalysisComplete(analysis);
      }

    } catch (error) {
      console.error('Geçmiş analiz hatası:', error);
      setAnalysisData(null);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Geçmiş harcama verilerini getir
   * @param {string} startDate - Başlangıç tarihi
   * @param {string} endDate - Bitiş tarihi
   * @param {Array} categoryIds - Kategori ID'leri
   * @returns {Promise<Array>} Harcama verileri
   */
  const getHistoricalSpending = async (startDate, endDate, categoryIds) => {
    if (categoryIds.length === 0) {
      return [];
    }

    const placeholders = categoryIds.map(() => '?').join(',');
    const query = `
      SELECT 
        t.category_id,
        c.name as category_name,
        c.icon as category_icon,
        t.amount,
        t.date,
        strftime('%Y-%m', t.date) as month_year
      FROM transactions t
      JOIN categories c ON t.category_id = c.id
      WHERE t.type = 'expense'
        AND t.date >= ?
        AND t.date <= ?
        AND t.category_id IN (${placeholders})
      ORDER BY t.date DESC
    `;

    const params = [startDate, endDate, ...categoryIds];
    return await db.getAllAsync(query, params);
  };

  /**
   * Analiz verilerini hesapla
   * @param {Array} transactions - İşlem verileri
   * @param {number} monthsBack - Kaç ay geriye
   * @returns {Object} Analiz sonuçları
   */
  const calculateAnalysis = (transactions, monthsBack) => {
    if (transactions.length === 0) {
      return {
        totalSpending: 0,
        averageMonthly: 0,
        categories: [],
        trends: [],
        recommendations: []
      };
    }

    // Toplam harcama
    const totalSpending = transactions.reduce((sum, t) => sum + t.amount, 0);
    const averageMonthly = totalSpending / monthsBack;

    // Kategori bazlı analiz
    const categoryAnalysis = {};
    transactions.forEach(t => {
      if (!categoryAnalysis[t.category_id]) {
        categoryAnalysis[t.category_id] = {
          id: t.category_id,
          name: t.category_name,
          icon: t.category_icon,
          total: 0,
          transactions: [],
          monthlyData: {}
        };
      }
      
      categoryAnalysis[t.category_id].total += t.amount;
      categoryAnalysis[t.category_id].transactions.push(t);
      
      // Aylık veri
      const monthKey = t.month_year;
      if (!categoryAnalysis[t.category_id].monthlyData[monthKey]) {
        categoryAnalysis[t.category_id].monthlyData[monthKey] = 0;
      }
      categoryAnalysis[t.category_id].monthlyData[monthKey] += t.amount;
    });

    // Kategori listesini oluştur
    const categories = Object.values(categoryAnalysis).map(cat => ({
      ...cat,
      average: cat.total / monthsBack,
      percentage: (cat.total / totalSpending) * 100,
      consistency: calculateConsistency(cat.monthlyData),
      trend: calculateTrend(cat.monthlyData)
    })).sort((a, b) => b.total - a.total);

    // Trend analizi
    const trends = calculateTrends(categoryAnalysis, monthsBack);

    // Öneriler oluştur
    const recommendations = generateRecommendations(categories, averageMonthly);

    return {
      totalSpending,
      averageMonthly,
      categories,
      trends,
      recommendations,
      monthsAnalyzed: monthsBack,
      transactionCount: transactions.length
    };
  };

  /**
   * Kategori tutarlılığını hesapla
   * @param {Object} monthlyData - Aylık veri
   * @returns {number} Tutarlılık skoru (0-1)
   */
  const calculateConsistency = (monthlyData) => {
    const values = Object.values(monthlyData);
    if (values.length < 2) return 0;

    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    const stdDev = Math.sqrt(variance);
    
    return mean > 0 ? Math.max(0, 1 - (stdDev / mean)) : 0;
  };

  /**
   * Trend hesapla
   * @param {Object} monthlyData - Aylık veri
   * @returns {string} Trend yönü ('increasing', 'decreasing', 'stable')
   */
  const calculateTrend = (monthlyData) => {
    const months = Object.keys(monthlyData).sort();
    if (months.length < 2) return 'stable';

    const firstHalf = months.slice(0, Math.floor(months.length / 2));
    const secondHalf = months.slice(Math.floor(months.length / 2));

    const firstAvg = firstHalf.reduce((sum, month) => sum + monthlyData[month], 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, month) => sum + monthlyData[month], 0) / secondHalf.length;

    const changePercent = ((secondAvg - firstAvg) / firstAvg) * 100;

    if (changePercent > 10) return 'increasing';
    if (changePercent < -10) return 'decreasing';
    return 'stable';
  };

  /**
   * Genel trendleri hesapla
   * @param {Object} categoryAnalysis - Kategori analizi
   * @param {number} monthsBack - Kaç ay geriye
   * @returns {Array} Trend listesi
   */
  const calculateTrends = (categoryAnalysis, monthsBack) => {
    const trends = [];

    // En çok harcama yapılan kategori
    const topCategory = Object.values(categoryAnalysis)
      .sort((a, b) => b.total - a.total)[0];
    
    if (topCategory) {
      trends.push({
        type: 'top_category',
        title: 'En Çok Harcama',
        description: `${topCategory.name} kategorisinde en çok harcama yapıyorsunuz`,
        value: topCategory.total,
        icon: 'trending-up'
      });
    }

    return trends;
  };

  /**
   * Öneriler oluştur
   * @param {Array} categories - Kategori analizi
   * @param {number} averageMonthly - Aylık ortalama
   * @returns {Array} Öneri listesi
   */
  const generateRecommendations = (categories, averageMonthly) => {
    const recommendations = [];

    categories.forEach(category => {
      if (category.trend === 'increasing') {
        recommendations.push({
          type: 'warning',
          title: `${category.name} Artış Trendi`,
          description: 'Bu kategorideki harcamalarınız artış gösteriyor',
          suggestedLimit: Math.round(category.average * 1.1),
          icon: 'warning'
        });
      } else if (category.consistency > 0.8) {
        recommendations.push({
          type: 'stable',
          title: `${category.name} Tutarlı Harcama`,
          description: 'Bu kategoride tutarlı harcama yapıyorsunuz',
          suggestedLimit: Math.round(category.average),
          icon: 'check-circle'
        });
      }
    });

    return recommendations;
  };

  // Component mount edildiğinde analizi başlat
  useEffect(() => {
    if (selectedCategories.length > 0) {
      analyzeHistoricalData();
    }
  }, [selectedCategories, selectedPeriodRange]);

  /**
   * Para birimi formatı
   * @param {number} value - Formatlanacak değer
   * @returns {string} Formatlanmış değer
   */
  const formatCurrency = (value) => {
    switch (currency) {
      case 'USD':
        return formatUSD(value);
      case 'EUR':
        return formatEUR(value);
      default:
        return formatTRY(value);
    }
  };

  /**
   * Trend ikonu
   * @param {string} trend - Trend yönü
   * @returns {string} İkon adı
   */
  const getTrendIcon = (trend) => {
    switch (trend) {
      case 'increasing':
        return 'trending-up';
      case 'decreasing':
        return 'trending-down';
      default:
        return 'trending-flat';
    }
  };

  /**
   * Trend rengi
   * @param {string} trend - Trend yönü
   * @returns {string} Renk kodu
   */
  const getTrendColor = (trend) => {
    switch (trend) {
      case 'increasing':
        return currentTheme.WARNING;
      case 'decreasing':
        return currentTheme.SUCCESS;
      default:
        return currentTheme.INFO;
    }
  };

  if (selectedCategories.length === 0) {
    return (
      <View style={[styles.emptyContainer, { backgroundColor: currentTheme.BACKGROUND }]}>
        <MaterialIcons name="analytics" size={64} color={currentTheme.TEXT_SECONDARY} />
        <Text style={[styles.emptyTitle, { color: currentTheme.TEXT_PRIMARY }]}>
          Kategori Seçin
        </Text>
        <Text style={[styles.emptySubtitle, { color: currentTheme.TEXT_SECONDARY }]}>
          Analiz için önce kategorileri seçmeniz gerekiyor
        </Text>
      </View>
    );
  }

  if (loading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: currentTheme.BACKGROUND }]}>
        <ActivityIndicator size="large" color={currentTheme.PRIMARY} />
        <Text style={[styles.loadingText, { color: currentTheme.TEXT_SECONDARY }]}>
          Geçmiş veriler analiz ediliyor...
        </Text>
      </View>
    );
  }

  if (!analysisData || analysisData.transactionCount === 0) {
    return (
      <View style={[styles.emptyContainer, { backgroundColor: currentTheme.BACKGROUND }]}>
        <MaterialIcons name="history" size={64} color={currentTheme.TEXT_SECONDARY} />
        <Text style={[styles.emptyTitle, { color: currentTheme.TEXT_PRIMARY }]}>
          Yeterli Veri Yok
        </Text>
        <Text style={[styles.emptySubtitle, { color: currentTheme.TEXT_SECONDARY }]}>
          Seçili kategorilerde yeterli geçmiş harcama verisi bulunmuyor
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: currentTheme.BACKGROUND }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: currentTheme.TEXT_PRIMARY }]}>
          Geçmiş Harcama Analizi
        </Text>
        <Text style={[styles.subtitle, { color: currentTheme.TEXT_SECONDARY }]}>
          {analysisData.transactionCount} işlem analiz edildi
        </Text>
      </View>

      {/* Dönem seçici */}
      <View style={styles.periodSelector}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles.periodButtons}>
            {periodOptions.map((option) => (
              <TouchableOpacity
                key={option.key}
                style={[
                  styles.periodButton,
                  {
                    backgroundColor: selectedPeriodRange === option.key 
                      ? currentTheme.PRIMARY 
                      : currentTheme.SURFACE,
                    borderColor: currentTheme.BORDER,
                  }
                ]}
                onPress={() => setSelectedPeriodRange(option.key)}
              >
                <Text style={[
                  styles.periodButtonText,
                  {
                    color: selectedPeriodRange === option.key 
                      ? currentTheme.WHITE 
                      : currentTheme.TEXT_PRIMARY
                  }
                ]}>
                  {option.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
      </View>

      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Genel özet */}
        <View style={[styles.summaryCard, { backgroundColor: currentTheme.SURFACE }]}>
          <Text style={[styles.summaryTitle, { color: currentTheme.TEXT_PRIMARY }]}>
            Genel Özet
          </Text>
          <View style={styles.summaryStats}>
            <View style={styles.statItem}>
              <Text style={[styles.statLabel, { color: currentTheme.TEXT_SECONDARY }]}>
                Toplam Harcama
              </Text>
              <Text style={[styles.statValue, { color: currentTheme.TEXT_PRIMARY }]}>
                {formatCurrency(analysisData.totalSpending)}
              </Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statLabel, { color: currentTheme.TEXT_SECONDARY }]}>
                Aylık Ortalama
              </Text>
              <Text style={[styles.statValue, { color: currentTheme.TEXT_PRIMARY }]}>
                {formatCurrency(analysisData.averageMonthly)}
              </Text>
            </View>
          </View>
        </View>

        {/* Kategori analizi */}
        <View style={styles.categoriesSection}>
          <Text style={[styles.sectionTitle, { color: currentTheme.TEXT_PRIMARY }]}>
            Kategori Analizi
          </Text>
          {analysisData.categories.map((category) => (
            <View 
              key={category.id} 
              style={[styles.categoryCard, { backgroundColor: currentTheme.SURFACE }]}
            >
              <View style={styles.categoryHeader}>
                <View style={[styles.categoryIcon, { backgroundColor: currentTheme.PRIMARY + '20' }]}>
                  <MaterialIcons 
                    name={category.icon || 'category'} 
                    size={20} 
                    color={currentTheme.PRIMARY} 
                  />
                </View>
                <View style={styles.categoryInfo}>
                  <Text style={[styles.categoryName, { color: currentTheme.TEXT_PRIMARY }]}>
                    {category.name}
                  </Text>
                  <Text style={[styles.categoryAmount, { color: currentTheme.TEXT_SECONDARY }]}>
                    {formatCurrency(category.average)} / ay
                  </Text>
                </View>
                <View style={styles.categoryTrend}>
                  <MaterialIcons 
                    name={getTrendIcon(category.trend)} 
                    size={20} 
                    color={getTrendColor(category.trend)} 
                  />
                </View>
              </View>
              
              <View style={styles.categoryStats}>
                <Text style={[styles.categoryPercentage, { color: currentTheme.TEXT_SECONDARY }]}>
                  Toplam harcamanın %{category.percentage.toFixed(1)}'i
                </Text>
                <Text style={[styles.categoryConsistency, { color: currentTheme.TEXT_SECONDARY }]}>
                  Tutarlılık: %{(category.consistency * 100).toFixed(0)}
                </Text>
              </View>
            </View>
          ))}
        </View>

        {/* Öneriler */}
        {analysisData.recommendations.length > 0 && (
          <View style={styles.recommendationsSection}>
            <Text style={[styles.sectionTitle, { color: currentTheme.TEXT_PRIMARY }]}>
              Öneriler
            </Text>
            {analysisData.recommendations.map((rec, index) => (
              <View 
                key={index} 
                style={[styles.recommendationCard, { backgroundColor: currentTheme.SURFACE }]}
              >
                <View style={styles.recommendationHeader}>
                  <MaterialIcons 
                    name={rec.icon} 
                    size={20} 
                    color={rec.type === 'warning' ? currentTheme.WARNING : currentTheme.SUCCESS} 
                  />
                  <Text style={[styles.recommendationTitle, { color: currentTheme.TEXT_PRIMARY }]}>
                    {rec.title}
                  </Text>
                </View>
                <Text style={[styles.recommendationDescription, { color: currentTheme.TEXT_SECONDARY }]}>
                  {rec.description}
                </Text>
                {rec.suggestedLimit && (
                  <Text style={[styles.recommendationLimit, { color: currentTheme.PRIMARY }]}>
                    Önerilen limit: {formatCurrency(rec.suggestedLimit)}
                  </Text>
                )}
              </View>
            ))}
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    marginTop: 16,
    textAlign: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  header: {
    paddingVertical: 16,
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    textAlign: 'center',
  },
  periodSelector: {
    marginBottom: 16,
  },
  periodButtons: {
    flexDirection: 'row',
    gap: 8,
    paddingHorizontal: 4,
  },
  periodButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
  },
  periodButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 16,
  },
  summaryCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  summaryStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  statValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  categoriesSection: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  categoryCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 12,
  },
  categoryIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: 14,
    fontWeight: '500',
  },
  categoryAmount: {
    fontSize: 12,
  },
  categoryTrend: {
    padding: 4,
  },
  categoryStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  categoryPercentage: {
    fontSize: 11,
  },
  categoryConsistency: {
    fontSize: 11,
  },
  recommendationsSection: {
    marginBottom: 16,
  },
  recommendationCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
  },
  recommendationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  recommendationTitle: {
    fontSize: 14,
    fontWeight: '500',
  },
  recommendationDescription: {
    fontSize: 12,
    lineHeight: 16,
    marginBottom: 4,
  },
  recommendationLimit: {
    fontSize: 12,
    fontWeight: '500',
  },
});

export default HistoricalAnalysis;
