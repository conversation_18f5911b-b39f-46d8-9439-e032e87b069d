import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import * as exchangeRateService from '../services/simpleExchangeRateService';
import { formatCurrency, getCurrencySymbol } from '../utils/formatters';

/**
 * Döviz kuru dönüştürücü bileşeni
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {number} props.amount - Dönüştürülecek miktar
 * @param {string} props.fromCurrency - Kaynak para birimi
 * @param {string} props.toCurrency - Hedef para birimi (varsayılan: TRY)
 * @param {string} props.date - Dönüşüm tarihi (varsayılan: bugün)
 * @param {boolean} props.showIcon - İkon gösterilsin mi
 * @param {Object} props.style - Ek stil özellikleri
 * @returns {JSX.Element} Döviz kuru dönüştürücü bileşeni
 */
const CurrencyConverter = ({
  amount,
  fromCurrency,
  toCurrency = 'TRY',
  date = null,
  showIcon = true,
  style = {}
}) => {
  const db = useSQLiteContext();

  const [convertedAmount, setConvertedAmount] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Aynı para birimi ise dönüşüm yapma
  const isSameCurrency = fromCurrency === toCurrency;

  useEffect(() => {
    // Miktar veya para birimi yoksa işlem yapma
    if (!amount || !fromCurrency || isSameCurrency) {
      setLoading(false);
      return;
    }

    const fetchRate = async () => {
      try {
        setLoading(true);
        setError(null);

        // Döviz kurunu al
        const convertedValue = await exchangeRateService.convertCurrency(
          db,
          amount,
          fromCurrency,
          toCurrency,
          date
        );

        setConvertedAmount(convertedValue);
        setLoading(false);
      } catch (err) {
        console.error('Döviz kuru dönüştürme hatası:', err);
        setError('Döviz kuru alınamadı');
        setLoading(false);
      }
    };

    fetchRate();
  }, [amount, fromCurrency, toCurrency, date, db, isSameCurrency]);

  // Aynı para birimi ise veya miktar yoksa gösterme
  if (isSameCurrency || !amount) {
    return null;
  }

  // Yükleniyor durumu
  if (loading) {
    return (
      <View style={[styles.container, style]}>
        <ActivityIndicator size="small" color={Colors.PRIMARY} />
      </View>
    );
  }

  // Hata durumu
  if (error) {
    return (
      <View style={[styles.container, style]}>
        <Text style={styles.errorText}>{error}</Text>
      </View>
    );
  }

  // Dönüştürülmüş miktar
  return (
    <View style={[styles.container, style]}>
      {showIcon && (
        <MaterialIcons
          name="currency-exchange"
          size={14}
          color={Colors.GRAY_500}
          style={styles.icon}
        />
      )}
      <Text style={styles.text}>
        {formatCurrency(convertedAmount, toCurrency)}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    marginRight: 4,
  },
  text: {
    fontSize: 12,
    color: Colors.GRAY_500,
  },
  errorText: {
    fontSize: 12,
    color: Colors.DANGER,
  }
});

export default CurrencyConverter;
