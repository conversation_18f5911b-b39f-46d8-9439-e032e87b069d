import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  TextInput,
  Modal,
  ActivityIndicator,
  RefreshControl
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import * as reminderTemplateService from '../services/reminderTemplateService';
import * as reminderTagService from '../services/reminderTagService';
import * as reminderService from '../services/reminderService';
import { Colors } from '../constants/colors';
import { format } from 'date-fns';

/**
 * Hatırlatıcı Şablonları Ekranı
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @returns {JSX.Element} Hatırlatıcı Şablonları Ekranı
 */
const ReminderTemplatesScreen = ({ navigation }) => {
  const db = useSQLiteContext();
  
  // Durum
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [filteredTemplates, setFilteredTemplates] = useState([]);
  
  // Modal durumu
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [datePickerVisible, setDatePickerVisible] = useState(false);
  const [timePickerVisible, setTimePickerVisible] = useState(false);
  const [reminderDate, setReminderDate] = useState(new Date());
  const [reminderTime, setReminderTime] = useState(new Date());
  
  // Verileri yükle
  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      const templateData = await reminderTemplateService.getAllTemplates(db);
      setTemplates(templateData);
      setFilteredTemplates(templateData);
      setLoading(false);
    } catch (error) {
      console.error('Şablonları yükleme hatası:', error);
      Alert.alert('Hata', 'Şablonlar yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  }, [db]);
  
  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [loadData])
  );
  
  // Arama metni değiştiğinde filtreleme yap
  React.useEffect(() => {
    if (!templates.length) {
      setFilteredTemplates([]);
      return;
    }
    
    if (!searchText.trim()) {
      setFilteredTemplates(templates);
      return;
    }
    
    const searchLower = searchText.toLowerCase();
    const filtered = templates.filter(template => 
      template.name.toLowerCase().includes(searchLower) ||
      template.title.toLowerCase().includes(searchLower) ||
      (template.message && template.message.toLowerCase().includes(searchLower))
    );
    
    setFilteredTemplates(filtered);
  }, [templates, searchText]);
  
  // Yenileme işlemi
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };
  
  // Şablon düzenleme ekranına git
  const editTemplate = (template) => {
    navigation.navigate('ReminderTemplateForm', { templateId: template.id });
  };
  
  // Şablon silme
  const deleteTemplate = (template) => {
    Alert.alert(
      'Şablonu Sil',
      `"${template.name}" şablonunu silmek istediğinize emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await reminderTemplateService.deleteTemplate(db, template.id);
              Alert.alert('Başarılı', 'Şablon silindi.');
              loadData();
            } catch (error) {
              console.error('Şablon silme hatası:', error);
              Alert.alert('Hata', 'Şablon silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };
  
  // Şablondan hatırlatıcı oluşturma modalını aç
  const openCreateReminderModal = (template) => {
    setSelectedTemplate(template);
    setReminderDate(new Date());
    setReminderTime(new Date());
    setModalVisible(true);
  };
  
  // Şablondan hatırlatıcı oluştur
  const createReminderFromTemplate = async () => {
    try {
      if (!selectedTemplate) return;
      
      // Tarih ve saat formatını düzenle
      const formattedDate = format(reminderDate, 'yyyy-MM-dd');
      const formattedTime = format(reminderTime, 'HH:mm');
      
      // Hatırlatıcı oluştur
      await reminderTemplateService.createReminderFromTemplate(
        db,
        selectedTemplate.id,
        { date: formattedDate, time: formattedTime },
        reminderService
      );
      
      Alert.alert('Başarılı', 'Hatırlatıcı oluşturuldu.');
      setModalVisible(false);
    } catch (error) {
      console.error('Hatırlatıcı oluşturma hatası:', error);
      Alert.alert('Hata', 'Hatırlatıcı oluşturulurken bir hata oluştu.');
    }
  };
  
  // Şablon öğesi
  const renderTemplateItem = ({ item }) => {
    // Öncelik rengi
    let priorityColor = Colors.WARNING;
    if (item.priority === 'low') {
      priorityColor = Colors.SUCCESS;
    } else if (item.priority === 'high') {
      priorityColor = Colors.DANGER;
    }
    
    // Tekrarlama metni
    let repeatText = 'Bir Kez';
    if (item.repeat_type === 'daily') {
      repeatText = 'Günlük';
    } else if (item.repeat_type === 'weekly') {
      repeatText = 'Haftalık';
    } else if (item.repeat_type === 'monthly') {
      repeatText = 'Aylık';
    } else if (item.repeat_type === 'yearly') {
      repeatText = 'Yıllık';
    }
    
    return (
      <View style={styles.templateItem}>
        <View style={styles.templateHeader}>
          <View style={[styles.priorityIndicator, { backgroundColor: priorityColor }]} />
          <Text style={styles.templateName}>{item.name}</Text>
          <View style={styles.templateActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => openCreateReminderModal(item)}
            >
              <MaterialIcons name="add-alert" size={20} color={Colors.PRIMARY} />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => editTemplate(item)}
            >
              <MaterialIcons name="edit" size={20} color={Colors.PRIMARY} />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => deleteTemplate(item)}
            >
              <MaterialIcons name="delete" size={20} color={Colors.DANGER} />
            </TouchableOpacity>
          </View>
        </View>
        
        <View style={styles.templateContent}>
          <Text style={styles.templateTitle}>{item.title}</Text>
          {item.message && (
            <Text style={styles.templateMessage} numberOfLines={2}>
              {item.message}
            </Text>
          )}
          
          <View style={styles.templateInfo}>
            <View style={styles.infoItem}>
              <MaterialIcons name="repeat" size={16} color={Colors.TEXT_LIGHT} />
              <Text style={styles.infoText}>{repeatText}</Text>
            </View>
            
            {item.tags && item.tags.length > 0 && (
              <View style={styles.tagsContainer}>
                {item.tags.map(tag => (
                  <View
                    key={tag.id}
                    style={[styles.tagBadge, { backgroundColor: tag.color || Colors.PRIMARY }]}
                  >
                    <Text style={styles.tagText}>{tag.name}</Text>
                  </View>
                ))}
              </View>
            )}
          </View>
        </View>
      </View>
    );
  };
  
  // Boş durum
  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <MaterialIcons name="content-paste" size={64} color={Colors.GRAY_300} />
      <Text style={styles.emptyTitle}>Henüz Şablon Yok</Text>
      <Text style={styles.emptyText}>
        Sık kullandığınız hatırlatıcılar için şablonlar oluşturun
      </Text>
      <TouchableOpacity
        style={styles.emptyButton}
        onPress={() => navigation.navigate('ReminderTemplateForm')}
      >
        <MaterialIcons name="add" size={20} color="#fff" />
        <Text style={styles.emptyButtonText}>Şablon Ekle</Text>
      </TouchableOpacity>
    </View>
  );
  
  // Yükleniyor durumu
  if (loading && !refreshing) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={styles.loadingText}>Yükleniyor...</Text>
      </View>
    );
  }
  
  return (
    <View style={styles.container}>
      {/* Başlık */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Hatırlatıcı Şablonları</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => navigation.navigate('ReminderTemplateForm')}
        >
          <MaterialIcons name="add" size={24} color="#fff" />
        </TouchableOpacity>
      </View>
      
      {/* Arama */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <MaterialIcons name="search" size={20} color={Colors.GRAY_500} />
          <TextInput
            style={styles.searchInput}
            placeholder="Şablon ara..."
            placeholderTextColor={Colors.GRAY_500}
            value={searchText}
            onChangeText={setSearchText}
          />
          {searchText ? (
            <TouchableOpacity
              style={styles.clearButton}
              onPress={() => setSearchText('')}
            >
              <MaterialIcons name="close" size={20} color={Colors.GRAY_500} />
            </TouchableOpacity>
          ) : null}
        </View>
      </View>
      
      {/* Şablon Listesi */}
      <FlatList
        data={filteredTemplates}
        renderItem={renderTemplateItem}
        keyExtractor={item => item.id.toString()}
        contentContainerStyle={styles.listContent}
        ListEmptyComponent={renderEmptyState}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[Colors.PRIMARY]}
          />
        }
      />
      
      {/* Hatırlatıcı Oluşturma Modalı */}
      <Modal
        visible={modalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Hatırlatıcı Oluştur</Text>
              <TouchableOpacity
                style={styles.modalCloseButton}
                onPress={() => setModalVisible(false)}
              >
                <MaterialIcons name="close" size={24} color={Colors.TEXT_DARK} />
              </TouchableOpacity>
            </View>
            
            {selectedTemplate && (
              <View style={styles.modalBody}>
                <Text style={styles.modalTemplateTitle}>{selectedTemplate.name}</Text>
                
                <View style={styles.formGroup}>
                  <Text style={styles.formLabel}>Tarih</Text>
                  <TouchableOpacity
                    style={styles.datePickerButton}
                    onPress={() => setDatePickerVisible(true)}
                  >
                    <MaterialIcons name="event" size={20} color={Colors.PRIMARY} />
                    <Text style={styles.datePickerText}>
                      {format(reminderDate, 'dd MMMM yyyy')}
                    </Text>
                  </TouchableOpacity>
                </View>
                
                <View style={styles.formGroup}>
                  <Text style={styles.formLabel}>Saat</Text>
                  <TouchableOpacity
                    style={styles.datePickerButton}
                    onPress={() => setTimePickerVisible(true)}
                  >
                    <MaterialIcons name="access-time" size={20} color={Colors.PRIMARY} />
                    <Text style={styles.datePickerText}>
                      {format(reminderTime, 'HH:mm')}
                    </Text>
                  </TouchableOpacity>
                </View>
                
                <TouchableOpacity
                  style={styles.createButton}
                  onPress={createReminderFromTemplate}
                >
                  <MaterialIcons name="add-alert" size={20} color="#fff" />
                  <Text style={styles.createButtonText}>Hatırlatıcı Oluştur</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
  },
  addButton: {
    backgroundColor: Colors.PRIMARY,
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    marginLeft: 8,
    color: Colors.TEXT_DARK,
  },
  clearButton: {
    padding: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: Colors.PRIMARY,
  },
  listContent: {
    padding: 16,
    flexGrow: 1,
  },
  templateItem: {
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
  },
  templateHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  priorityIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  templateName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    flex: 1,
  },
  templateActions: {
    flexDirection: 'row',
  },
  actionButton: {
    padding: 8,
    marginLeft: 4,
  },
  templateContent: {
    marginLeft: 20,
  },
  templateTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.TEXT_DARK,
    marginBottom: 4,
  },
  templateMessage: {
    fontSize: 14,
    color: Colors.TEXT_LIGHT,
    marginBottom: 8,
  },
  templateInfo: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    marginBottom: 4,
  },
  infoText: {
    fontSize: 12,
    color: Colors.TEXT_LIGHT,
    marginLeft: 4,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  tagBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    marginRight: 4,
    marginBottom: 4,
  },
  tagText: {
    fontSize: 10,
    color: '#fff',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    marginTop: 16,
  },
  emptyText: {
    fontSize: 14,
    color: Colors.TEXT_LIGHT,
    textAlign: 'center',
    marginTop: 8,
    marginHorizontal: 32,
  },
  emptyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 24,
  },
  emptyButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 8,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 12,
    width: '90%',
    maxWidth: 400,
    padding: 24,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
  },
  modalCloseButton: {
    padding: 4,
  },
  modalBody: {
    marginBottom: 16,
  },
  modalTemplateTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    marginBottom: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  formLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.TEXT_DARK,
    marginBottom: 8,
  },
  datePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    padding: 12,
  },
  datePickerText: {
    fontSize: 16,
    color: Colors.TEXT_DARK,
    marginLeft: 8,
  },
  createButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
    borderRadius: 8,
    padding: 16,
    marginTop: 16,
  },
  createButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 8,
  },
});

export default ReminderTemplatesScreen;
