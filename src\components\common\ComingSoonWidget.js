import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useAppContext } from '../../context/AppContext';
import { Colors } from '../../constants/colors';
import {
  getFeaturesByStatus,
  FEATURE_STATUS,
  getProgressStats,
  getStatusColors
} from '../../services/featureStatusService';

/**
 * Coming Soon Widget Komponenti
 * Diğer ekranlarda kullanılabilir küçük coming soon göstergesi
 */
const ComingSoonWidget = ({ 
  onPress = null,
  showProgress = true,
  showStats = true,
  compact = false,
  style = {}
}) => {
  const { theme } = useAppContext();
  
  // İlerleme istatistikleri
  const progressStats = getProgressStats();
  const comingSoonFeatures = getFeaturesByStatus(FEATURE_STATUS.COMING_SOON);
  const inProgressFeatures = getFeaturesByStatus(FEATURE_STATUS.IN_PROGRESS);
  
  // En yakın özellik
  const nextFeature = [...inProgressFeatures, ...comingSoonFeatures]
    .sort((a, b) => {
      if (a.estimatedCompletion && b.estimatedCompletion) {
        return new Date(a.estimatedCompletion) - new Date(b.estimatedCompletion);
      }
      return b.progress - a.progress;
    })[0];

  const handlePress = () => {
    if (onPress) {
      onPress();
    }
  };

  if (compact) {
    return (
      <TouchableOpacity
        style={[styles.compactContainer, { backgroundColor: theme.CARD }, style]}
        onPress={handlePress}
        disabled={!onPress}
      >
        <View style={styles.compactContent}>
          <MaterialIcons name="auto-awesome" size={20} color={theme.PRIMARY} />
          <Text style={[styles.compactText, { color: theme.TEXT_PRIMARY }]}>
            {inProgressFeatures.length + comingSoonFeatures.length} yeni özellik yakında
          </Text>
          {onPress && (
            <MaterialIcons name="chevron-right" size={20} color={theme.TEXT_SECONDARY} />
          )}
        </View>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      style={[styles.container, { backgroundColor: theme.CARD }, style]}
      onPress={handlePress}
      disabled={!onPress}
    >
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <MaterialIcons name="auto-awesome" size={24} color={theme.PRIMARY} />
          <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
            Yakında Gelecek Özellikler
          </Text>
        </View>
        {onPress && (
          <MaterialIcons name="chevron-right" size={24} color={theme.TEXT_SECONDARY} />
        )}
      </View>

      {/* İstatistikler */}
      {showStats && (
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: Colors.SUCCESS }]}>
              {progressStats.completed}
            </Text>
            <Text style={[styles.statLabel, { color: theme.TEXT_SECONDARY }]}>
              Tamamlandı
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: Colors.WARNING }]}>
              {progressStats.inProgress}
            </Text>
            <Text style={[styles.statLabel, { color: theme.TEXT_SECONDARY }]}>
              Devam Ediyor
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: Colors.PRIMARY }]}>
              {progressStats.comingSoon}
            </Text>
            <Text style={[styles.statLabel, { color: theme.TEXT_SECONDARY }]}>
              Yakında
            </Text>
          </View>
        </View>
      )}

      {/* Genel İlerleme */}
      {showProgress && (
        <View style={styles.progressContainer}>
          <View style={styles.progressHeader}>
            <Text style={[styles.progressLabel, { color: theme.TEXT_PRIMARY }]}>
              Genel İlerleme
            </Text>
            <Text style={[styles.progressPercentage, { color: theme.PRIMARY }]}>
              %{progressStats.completionRate}
            </Text>
          </View>
          <View style={[styles.progressBarBackground, { backgroundColor: theme.BORDER }]}>
            <View 
              style={[
                styles.progressBarFill, 
                { 
                  backgroundColor: theme.PRIMARY,
                  width: `${progressStats.completionRate}%`
                }
              ]} 
            />
          </View>
        </View>
      )}

      {/* En Yakın Özellik */}
      {nextFeature && (
        <View style={styles.nextFeatureContainer}>
          <Text style={[styles.nextFeatureLabel, { color: theme.TEXT_SECONDARY }]}>
            Sıradaki Özellik:
          </Text>
          <View style={styles.nextFeature}>
            <View style={styles.nextFeatureLeft}>
              <View style={[
                styles.nextFeatureIcon, 
                { backgroundColor: getStatusColors(nextFeature.status).light }
              ]}>
                <MaterialIcons 
                  name={nextFeature.icon} 
                  size={16} 
                  color={getStatusColors(nextFeature.status).primary} 
                />
              </View>
              <View style={styles.nextFeatureInfo}>
                <Text style={[styles.nextFeatureTitle, { color: theme.TEXT_PRIMARY }]}>
                  {nextFeature.title}
                </Text>
                {nextFeature.estimatedCompletion && (
                  <Text style={[styles.nextFeatureDate, { color: theme.TEXT_SECONDARY }]}>
                    {new Date(nextFeature.estimatedCompletion).toLocaleDateString('tr-TR')}
                  </Text>
                )}
              </View>
            </View>
            <View style={styles.nextFeatureProgress}>
              <Text style={[styles.nextFeatureProgressText, { color: theme.PRIMARY }]}>
                %{nextFeature.progress}
              </Text>
            </View>
          </View>
        </View>
      )}

      {/* Footer */}
      <View style={styles.footer}>
        <Text style={[styles.footerText, { color: theme.TEXT_SECONDARY }]}>
          Yeni özellikler düzenli olarak eklenmektedir
        </Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    padding: 16,
    marginVertical: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  compactContainer: {
    borderRadius: 12,
    padding: 12,
    marginVertical: 4,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  compactContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  compactText: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
    marginLeft: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '700',
    marginLeft: 8,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
    paddingVertical: 12,
    backgroundColor: 'rgba(0,0,0,0.02)',
    borderRadius: 12,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 2,
  },
  statLabel: {
    fontSize: 11,
    fontWeight: '500',
  },
  progressContainer: {
    marginBottom: 16,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressLabel: {
    fontSize: 14,
    fontWeight: '600',
  },
  progressPercentage: {
    fontSize: 14,
    fontWeight: '700',
  },
  progressBarBackground: {
    height: 8,
    borderRadius: 4,
  },
  progressBarFill: {
    height: '100%',
    borderRadius: 4,
  },
  nextFeatureContainer: {
    marginBottom: 12,
  },
  nextFeatureLabel: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 8,
  },
  nextFeature: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  nextFeatureLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  nextFeatureIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  nextFeatureInfo: {
    flex: 1,
  },
  nextFeatureTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  nextFeatureDate: {
    fontSize: 12,
    fontWeight: '400',
  },
  nextFeatureProgress: {
    alignItems: 'center',
  },
  nextFeatureProgressText: {
    fontSize: 12,
    fontWeight: '700',
  },
  footer: {
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
    alignItems: 'center',
  },
  footerText: {
    fontSize: 12,
    fontStyle: 'italic',
    textAlign: 'center',
  },
});

export default ComingSoonWidget;
