import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Share
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import { formatCurrency, formatDate } from '../utils/formatters';
import CurrencyConverter from '../components/CurrencyConverter';

/**
 * İşlem Detay Ekranı
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @param {Object} props.route - Route nesnesi
 * @returns {JSX.Element} İşlem Detay Ekranı
 */
export default function TransactionDetailScreen({ navigation, route }) {
  const db = useSQLiteContext();
  const { transactionId } = route.params;
  
  // Durum değişkenleri
  const [loading, setLoading] = useState(true);
  const [transaction, setTransaction] = useState(null);
  const [showExchangeRates, setShowExchangeRates] = useState(false);
  
  // Verileri yükle
  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      
      // İşlem detaylarını getir
      const transactionData = await db.getFirstAsync(`
        SELECT t.*, c.name as category_name, c.icon as category_icon, c.color as category_color
        FROM transactions t
        LEFT JOIN categories c ON t.category_id = c.id
        WHERE t.id = ?
      `, [transactionId]);
      
      if (!transactionData) {
        Alert.alert('Hata', 'İşlem bulunamadı.');
        navigation.goBack();
        return;
      }
      
      setTransaction(transactionData);
      setLoading(false);
    } catch (error) {
      console.error('İşlem detayları yükleme hatası:', error);
      Alert.alert('Hata', 'İşlem detayları yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  }, [transactionId, db, navigation]);
  
  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [loadData])
  );
  
  // İşlemi düzenle
  const editTransaction = () => {
    navigation.navigate('transaction/edit', { transaction });
  };
  
  // İşlemi sil
  const deleteTransaction = () => {
    Alert.alert(
      'İşlemi Sil',
      'Bu işlemi silmek istediğinize emin misiniz? Bu işlem geri alınamaz.',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await db.runAsync(`DELETE FROM transactions WHERE id = ?`, [transactionId]);
              Alert.alert('Başarılı', 'İşlem başarıyla silindi.');
              navigation.goBack();
            } catch (error) {
              console.error('İşlem silme hatası:', error);
              Alert.alert('Hata', 'İşlem silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };
  
  // İşlemi paylaş
  const shareTransaction = async () => {
    if (!transaction) return;
    
    try {
      // İşlem özeti oluştur
      let message = `İşlem: ${transaction.description || 'İsimsiz işlem'}\n`;
      message += `Tür: ${transaction.type === 'income' ? 'Gelir' : 'Gider'}\n`;
      message += `Tutar: ${formatCurrency(transaction.amount, transaction.currency)}\n`;
      message += `Tarih: ${formatDate(transaction.date)}\n`;
      
      if (transaction.category_name) {
        message += `Kategori: ${transaction.category_name}\n`;
      }
      
      if (transaction.notes) {
        message += `\nNotlar: ${transaction.notes}\n`;
      }
      
      // Paylaşım dialogunu aç
      await Share.share({
        message,
        title: `${transaction.description || 'İşlem'} Detayları`
      });
    } catch (error) {
      console.error('İşlem paylaşım hatası:', error);
      Alert.alert('Hata', 'İşlem paylaşılırken bir hata oluştu.');
    }
  };
  
  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={styles.loadingText}>İşlem detayları yükleniyor...</Text>
      </View>
    );
  }
  
  if (!transaction) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <MaterialIcons name="error-outline" size={48} color={Colors.DANGER} />
        <Text style={styles.errorText}>İşlem bulunamadı.</Text>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>Geri Dön</Text>
        </TouchableOpacity>
      </View>
    );
  }
  
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>İşlem Detayı</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerAction}
            onPress={shareTransaction}
          >
            <MaterialIcons name="share" size={24} color="#fff" />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerAction}
            onPress={editTransaction}
          >
            <MaterialIcons name="edit" size={24} color="#fff" />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerAction}
            onPress={deleteTransaction}
          >
            <MaterialIcons name="delete" size={24} color="#fff" />
          </TouchableOpacity>
        </View>
      </View>
      
      <ScrollView style={styles.content}>
        <View style={styles.transactionCard}>
          <View style={styles.transactionHeader}>
            <View style={[
              styles.transactionType,
              { backgroundColor: transaction.type === 'income' ? Colors.INCOME : Colors.EXPENSE }
            ]}>
              <MaterialIcons
                name={transaction.type === 'income' ? 'arrow-upward' : 'arrow-downward'}
                size={20}
                color="#fff"
              />
              <Text style={styles.transactionTypeText}>
                {transaction.type === 'income' ? 'Gelir' : 'Gider'}
              </Text>
            </View>
            <Text style={styles.transactionDate}>{formatDate(transaction.date)}</Text>
          </View>
          
          <View style={styles.transactionAmount}>
            <Text style={[
              styles.amountValue,
              { color: transaction.type === 'income' ? Colors.INCOME : Colors.EXPENSE }
            ]}>
              {formatCurrency(transaction.amount, transaction.currency)}
            </Text>
            
            {transaction.currency !== 'TRY' && (
              <TouchableOpacity
                style={styles.exchangeRateButton}
                onPress={() => setShowExchangeRates(!showExchangeRates)}
              >
                <MaterialIcons name="currency-exchange" size={16} color={Colors.PRIMARY} />
                <Text style={styles.exchangeRateButtonText}>
                  {showExchangeRates ? 'Döviz kurlarını gizle' : 'Döviz kurlarını göster'}
                </Text>
              </TouchableOpacity>
            )}
            
            {showExchangeRates && transaction.currency !== 'TRY' && (
              <View style={styles.exchangeRates}>
                <View style={styles.exchangeRateItem}>
                  <Text style={styles.exchangeRateLabel}>TRY:</Text>
                  <CurrencyConverter
                    amount={transaction.amount}
                    fromCurrency={transaction.currency}
                    toCurrency="TRY"
                    date={transaction.date}
                    showIcon={false}
                    style={styles.exchangeRateValue}
                  />
                </View>
                
                {transaction.currency !== 'USD' && (
                  <View style={styles.exchangeRateItem}>
                    <Text style={styles.exchangeRateLabel}>USD:</Text>
                    <CurrencyConverter
                      amount={transaction.amount}
                      fromCurrency={transaction.currency}
                      toCurrency="USD"
                      date={transaction.date}
                      showIcon={false}
                      style={styles.exchangeRateValue}
                    />
                  </View>
                )}
                
                {transaction.currency !== 'EUR' && (
                  <View style={styles.exchangeRateItem}>
                    <Text style={styles.exchangeRateLabel}>EUR:</Text>
                    <CurrencyConverter
                      amount={transaction.amount}
                      fromCurrency={transaction.currency}
                      toCurrency="EUR"
                      date={transaction.date}
                      showIcon={false}
                      style={styles.exchangeRateValue}
                    />
                  </View>
                )}
              </View>
            )}
          </View>
          
          <View style={styles.transactionDetails}>
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Açıklama</Text>
              <Text style={styles.detailValue}>
                {transaction.description || 'İsimsiz işlem'}
              </Text>
            </View>
            
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Kategori</Text>
              <View style={styles.categoryContainer}>
                <MaterialIcons
                  name={transaction.category_icon || 'category'}
                  size={16}
                  color={transaction.category_color || Colors.GRAY_600}
                />
                <Text style={styles.detailValue}>
                  {transaction.category_name || 'Kategorisiz'}
                </Text>
              </View>
            </View>
            
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Tarih</Text>
              <Text style={styles.detailValue}>{formatDate(transaction.date)}</Text>
            </View>
            
            {transaction.notes && (
              <View style={styles.detailItem}>
                <Text style={styles.detailLabel}>Notlar</Text>
                <Text style={styles.detailValue}>{transaction.notes}</Text>
              </View>
            )}
            
            {transaction.metadata && (
              <View style={styles.detailItem}>
                <Text style={styles.detailLabel}>Ek Bilgiler</Text>
                <Text style={styles.detailValue}>
                  {typeof transaction.metadata === 'string'
                    ? transaction.metadata
                    : JSON.stringify(JSON.parse(transaction.metadata), null, 2)}
                </Text>
              </View>
            )}
          </View>
        </View>
        
        {/* TRY Karşılığı */}
        {transaction.currency !== 'TRY' && (
          <View style={styles.exchangeRateCard}>
            <Text style={styles.exchangeRateCardTitle}>Döviz Karşılığı</Text>
            
            <View style={styles.exchangeRateCardContent}>
              <View style={styles.exchangeRateCardItem}>
                <Text style={styles.exchangeRateCardLabel}>TRY:</Text>
                <CurrencyConverter
                  amount={transaction.amount}
                  fromCurrency={transaction.currency}
                  toCurrency="TRY"
                  date={transaction.date}
                  showIcon={false}
                  style={styles.exchangeRateCardValue}
                />
              </View>
              
              {transaction.currency !== 'USD' && (
                <View style={styles.exchangeRateCardItem}>
                  <Text style={styles.exchangeRateCardLabel}>USD:</Text>
                  <CurrencyConverter
                    amount={transaction.amount}
                    fromCurrency={transaction.currency}
                    toCurrency="USD"
                    date={transaction.date}
                    showIcon={false}
                    style={styles.exchangeRateCardValue}
                  />
                </View>
              )}
              
              {transaction.currency !== 'EUR' && (
                <View style={styles.exchangeRateCardItem}>
                  <Text style={styles.exchangeRateCardLabel}>EUR:</Text>
                  <CurrencyConverter
                    amount={transaction.amount}
                    fromCurrency={transaction.currency}
                    toCurrency="EUR"
                    date={transaction.date}
                    showIcon={false}
                    style={styles.exchangeRateCardValue}
                  />
                </View>
              )}
            </View>
            
            <Text style={styles.exchangeRateCardNote}>
              * Döviz kurları, işlem tarihindeki değerlere göre hesaplanmıştır.
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.GRAY_600,
  },
  errorText: {
    marginTop: 16,
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.GRAY_800,
  },
  backButtonText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.PRIMARY,
    fontWeight: '500',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 16,
    paddingHorizontal: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginLeft: 8,
  },
  headerActions: {
    flexDirection: 'row',
  },
  headerAction: {
    padding: 8,
    marginLeft: 4,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  transactionCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  transactionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  transactionType: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  transactionTypeText: {
    color: '#fff',
    fontWeight: '500',
    marginLeft: 4,
  },
  transactionDate: {
    fontSize: 14,
    color: Colors.GRAY_600,
  },
  transactionAmount: {
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200,
  },
  amountValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  exchangeRateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  exchangeRateButtonText: {
    fontSize: 14,
    color: Colors.PRIMARY,
    marginLeft: 4,
  },
  exchangeRates: {
    width: '100%',
    marginTop: 12,
    padding: 12,
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
  },
  exchangeRateItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  exchangeRateLabel: {
    fontSize: 14,
    color: Colors.GRAY_700,
    fontWeight: '500',
  },
  exchangeRateValue: {
    fontSize: 14,
    color: Colors.GRAY_800,
  },
  transactionDetails: {
    marginBottom: 8,
  },
  detailItem: {
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 14,
    color: Colors.GRAY_600,
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    color: Colors.GRAY_800,
  },
  categoryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  exchangeRateCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  exchangeRateCardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.GRAY_800,
    marginBottom: 16,
  },
  exchangeRateCardContent: {
    marginBottom: 16,
  },
  exchangeRateCardItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200,
  },
  exchangeRateCardLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.GRAY_700,
  },
  exchangeRateCardValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.GRAY_800,
  },
  exchangeRateCardNote: {
    fontSize: 12,
    fontStyle: 'italic',
    color: Colors.GRAY_500,
  },
});
