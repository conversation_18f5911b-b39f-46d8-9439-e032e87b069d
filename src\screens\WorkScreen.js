import React from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { ShiftHomeScreen } from '../features/shifts';

/**
 * Vardiya takibi ekranı
 * Çalışanların vardiya saatlerini, mesailerini ve ödemelerini takip etmelerini sağlar
 *
 * @returns {JSX.Element} Vardiya takibi ekranı
 */
export default function WorkScreen() {
  const insets = useSafeAreaInsets();
  const navigation = useNavigation();

  // Yeni vardiya takibi modülünü kullan
  return <ShiftHomeScreen navigation={navigation} />;
}
