import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Modal,
  Alert,
} from 'react-native';
import { useTheme } from '../../../context/ThemeContext';

// Chart Components
import { ChartFactory } from './ChartComponents/ChartFactory';
import ChartTypeSelector from './ChartComponents/ChartTypeSelector';

/**
 * İnteraktif Grafik Oluşturucu
 * Sürükle-bırak ile grafik tasarlama
 * Gerçek zamanlı önizleme ve konfigürasyon
 */
const InteractiveChartBuilder = ({ 
  data = [], 
  onChartCreate, 
  onClose,
  initialConfig = null 
}) => {
  const { theme } = useTheme();
  
  const [chartConfig, setChartConfig] = useState({
    type: 'bar',
    title: 'Yeni Grafik',
    xAxis: null,
    yAxis: null,
    groupBy: null,
    aggregation: 'sum',
    colors: ['#007AFF', '#34C759', '#FF9500', '#FF3B30', '#AF52DE'],
    showLegend: true,
    showGrid: true,
    showLabels: true,
    animation: true,
    ...initialConfig
  });
  
  const [availableFields, setAvailableFields] = useState([]);
  const [showTypeSelector, setShowTypeSelector] = useState(false);
  const [previewData, setPreviewData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (data && data.length > 0) {
      const fields = Object.keys(data[0]).map(key => ({
        name: key,
        type: getFieldType(data[0][key]),
        displayName: formatFieldName(key)
      }));
      setAvailableFields(fields);
    }
  }, [data]);

  useEffect(() => {
    generatePreviewData();
  }, [chartConfig, data]);

  /**
   * Alan tipini belirle
   */
  const getFieldType = (value) => {
    if (typeof value === 'number') return 'number';
    if (value instanceof Date) return 'date';
    if (typeof value === 'boolean') return 'boolean';
    return 'text';
  };

  /**
   * Alan adını formatla
   */
  const formatFieldName = (fieldName) => {
    return fieldName
      .replace(/_/g, ' ')
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase());
  };

  /**
   * Önizleme verisi oluştur
   */
  const generatePreviewData = () => {
    if (!data || data.length === 0 || !chartConfig.xAxis || !chartConfig.yAxis) {
      setPreviewData(null);
      return;
    }

    setIsLoading(true);
    
    try {
      const processedData = processDataForChart(data, chartConfig);
      setPreviewData(processedData);
    } catch (error) {
      console.error('Chart data processing error:', error);
      setPreviewData(null);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Grafik için veri işleme
   */
  const processDataForChart = (rawData, config) => {
    const { xAxis, yAxis, groupBy, aggregation } = config;
    
    if (groupBy) {
      // Gruplandırılmış veri
      const grouped = rawData.reduce((acc, item) => {
        const groupKey = item[groupBy.name];
        const xValue = item[xAxis.name];
        const yValue = Number(item[yAxis.name]) || 0;
        
        if (!acc[groupKey]) {
          acc[groupKey] = {};
        }
        
        if (!acc[groupKey][xValue]) {
          acc[groupKey][xValue] = [];
        }
        
        acc[groupKey][xValue].push(yValue);
        return acc;
      }, {});

      // Toplama işlemi uygula
      const result = [];
      Object.entries(grouped).forEach(([group, xValues]) => {
        Object.entries(xValues).forEach(([x, values]) => {
          let aggregatedValue;
          switch (aggregation) {
            case 'sum':
              aggregatedValue = values.reduce((sum, val) => sum + val, 0);
              break;
            case 'avg':
              aggregatedValue = values.reduce((sum, val) => sum + val, 0) / values.length;
              break;
            case 'count':
              aggregatedValue = values.length;
              break;
            case 'min':
              aggregatedValue = Math.min(...values);
              break;
            case 'max':
              aggregatedValue = Math.max(...values);
              break;
            default:
              aggregatedValue = values.reduce((sum, val) => sum + val, 0);
          }
          
          result.push({
            x: x,
            y: aggregatedValue,
            group: group
          });
        });
      });
      
      return result;
    } else {
      // Basit veri
      return rawData.map(item => ({
        x: item[xAxis.name],
        y: Number(item[yAxis.name]) || 0
      }));
    }
  };

  /**
   * Grafik tipini değiştir
   */
  const handleChartTypeChange = (type) => {
    setChartConfig(prev => ({ ...prev, type }));
    setShowTypeSelector(false);
  };

  /**
   * Alan seçimi
   */
  const handleFieldSelect = (field, axis) => {
    setChartConfig(prev => ({ ...prev, [axis]: field }));
  };

  /**
   * Grafik oluştur
   */
  const handleCreateChart = () => {
    if (!chartConfig.xAxis || !chartConfig.yAxis) {
      Alert.alert('Hata', 'Lütfen X ve Y ekseni için alanları seçin');
      return;
    }

    const finalConfig = {
      ...chartConfig,
      data: previewData,
      id: Date.now().toString(),
      createdAt: new Date().toISOString()
    };

    if (onChartCreate) {
      onChartCreate(finalConfig);
    }
  };

  /**
   * Alan seçici render et
   */
  const renderFieldSelector = (title, selectedField, onSelect, filterType = null) => (
    <View style={[styles.fieldSelector, { backgroundColor: theme.SURFACE }]}>
      <Text style={[styles.fieldSelectorTitle, { color: theme.TEXT_PRIMARY }]}>
        {title}
      </Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {availableFields
          .filter(field => !filterType || field.type === filterType)
          .map((field, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.fieldOption,
                {
                  backgroundColor: selectedField?.name === field.name 
                    ? theme.PRIMARY 
                    : theme.BACKGROUND,
                  borderColor: theme.BORDER || theme.TEXT_SECONDARY + '20'
                }
              ]}
              onPress={() => onSelect(field)}
            >
              <Text style={[
                styles.fieldOptionText,
                {
                  color: selectedField?.name === field.name 
                    ? theme.SURFACE 
                    : theme.TEXT_PRIMARY
                }
              ]}>
                {field.displayName}
              </Text>
              <Text style={[
                styles.fieldTypeText,
                {
                  color: selectedField?.name === field.name 
                    ? theme.SURFACE + '80' 
                    : theme.TEXT_SECONDARY
                }
              ]}>
                {field.type}
              </Text>
            </TouchableOpacity>
          ))}
      </ScrollView>
    </View>
  );

  return (
    <Modal
      visible={true}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
        {/* Header */}
        <View style={[styles.header, { backgroundColor: theme.SURFACE }]}>
          <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
            📊 İnteraktif Grafik Oluşturucu
          </Text>
          <TouchableOpacity
            style={[styles.closeButton, { backgroundColor: theme.ERROR }]}
            onPress={onClose}
          >
            <Text style={[styles.closeButtonText, { color: theme.SURFACE }]}>
              ✕
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content}>
          {/* Chart Type Selector */}
          <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
            <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
              📈 Grafik Tipi
            </Text>
            <TouchableOpacity
              style={[styles.typeButton, { backgroundColor: theme.PRIMARY }]}
              onPress={() => setShowTypeSelector(true)}
            >
              <Text style={[styles.typeButtonText, { color: theme.SURFACE }]}>
                {getChartTypeDisplayName(chartConfig.type)}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Field Selectors */}
          {renderFieldSelector(
            '📊 X Ekseni (Kategori)',
            chartConfig.xAxis,
            (field) => handleFieldSelect(field, 'xAxis')
          )}

          {renderFieldSelector(
            '📈 Y Ekseni (Değer)',
            chartConfig.yAxis,
            (field) => handleFieldSelect(field, 'yAxis'),
            'number'
          )}

          {renderFieldSelector(
            '🏷️ Gruplandırma (Opsiyonel)',
            chartConfig.groupBy,
            (field) => handleFieldSelect(field, 'groupBy')
          )}

          {/* Aggregation Selector */}
          {chartConfig.groupBy && (
            <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
              <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
                🔢 Toplama Fonksiyonu
              </Text>
              <View style={styles.aggregationButtons}>
                {['sum', 'avg', 'count', 'min', 'max'].map(agg => (
                  <TouchableOpacity
                    key={agg}
                    style={[
                      styles.aggregationButton,
                      {
                        backgroundColor: chartConfig.aggregation === agg 
                          ? theme.PRIMARY 
                          : theme.BACKGROUND
                      }
                    ]}
                    onPress={() => setChartConfig(prev => ({ ...prev, aggregation: agg }))}
                  >
                    <Text style={[
                      styles.aggregationButtonText,
                      {
                        color: chartConfig.aggregation === agg 
                          ? theme.SURFACE 
                          : theme.TEXT_PRIMARY
                      }
                    ]}>
                      {agg.toUpperCase()}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          )}

          {/* Chart Preview */}
          {previewData && (
            <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
              <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
                👁️ Önizleme
              </Text>
              <View style={styles.chartPreview}>
                {isLoading ? (
                  <Text style={[styles.loadingText, { color: theme.TEXT_SECONDARY }]}>
                    Grafik oluşturuluyor...
                  </Text>
                ) : (
                  <ChartFactory
                    type={chartConfig.type}
                    data={previewData}
                    config={chartConfig}
                    width={300}
                    height={200}
                  />
                )}
              </View>
            </View>
          )}
        </ScrollView>

        {/* Footer Actions */}
        <View style={[styles.footer, { backgroundColor: theme.SURFACE }]}>
          <TouchableOpacity
            style={[styles.footerButton, { backgroundColor: theme.WARNING }]}
            onPress={() => setChartConfig({
              type: 'bar',
              title: 'Yeni Grafik',
              xAxis: null,
              yAxis: null,
              groupBy: null,
              aggregation: 'sum',
              colors: ['#007AFF', '#34C759', '#FF9500', '#FF3B30', '#AF52DE'],
              showLegend: true,
              showGrid: true,
              showLabels: true,
              animation: true
            })}
          >
            <Text style={[styles.footerButtonText, { color: theme.SURFACE }]}>
              🔄 Sıfırla
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.footerButton,
              {
                backgroundColor: (chartConfig.xAxis && chartConfig.yAxis) 
                  ? theme.SUCCESS 
                  : theme.TEXT_SECONDARY
              }
            ]}
            onPress={handleCreateChart}
            disabled={!chartConfig.xAxis || !chartConfig.yAxis}
          >
            <Text style={[styles.footerButtonText, { color: theme.SURFACE }]}>
              ✅ Grafik Oluştur
            </Text>
          </TouchableOpacity>
        </View>

        {/* Chart Type Selector Modal */}
        <ChartTypeSelector
          visible={showTypeSelector}
          selectedType={chartConfig.type}
          onTypeSelect={handleChartTypeChange}
          onClose={() => setShowTypeSelector(false)}
        />
      </View>
    </Modal>
  );
};

/**
 * Grafik tipi görünen adını al
 */
const getChartTypeDisplayName = (type) => {
  const typeNames = {
    bar: '📊 Çubuk Grafik',
    line: '📈 Çizgi Grafik',
    pie: '🥧 Pasta Grafik',
    area: '📊 Alan Grafik',
    scatter: '⚫ Nokta Grafik'
  };
  return typeNames[type] || type;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  typeButton: {
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  typeButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  fieldSelector: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  fieldSelectorTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  fieldOption: {
    padding: 12,
    borderRadius: 8,
    marginRight: 8,
    borderWidth: 1,
    alignItems: 'center',
    minWidth: 100,
  },
  fieldOptionText: {
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
  fieldTypeText: {
    fontSize: 12,
    marginTop: 4,
  },
  aggregationButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  aggregationButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  aggregationButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  chartPreview: {
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  loadingText: {
    fontSize: 16,
    fontStyle: 'italic',
  },
  footer: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  footerButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  footerButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default InteractiveChartBuilder;
