/**
 * Hat<PERSON>rlatıcı grupları için servis fonksiyonları
 */

/**
 * Tüm hatırlatıcı gruplarını getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {boolean} activeOnly - Sadece aktif grupları getir
 * @returns {Promise<Array>} Hatırlatıcı grupları listesi
 */
export const getAllReminderGroups = async (db, activeOnly = true) => {
  try {
    let query = `
      SELECT * FROM reminder_groups
      ${activeOnly ? 'WHERE is_active = 1' : ''}
      ORDER BY name ASC
    `;

    const groups = await db.getAllAsync(query);
    return groups;
  } catch (error) {
    console.error('Hatırlatıcı grupları getirme hatası:', error);
    throw error;
  }
};

/**
 * Belirli bir hatırlatıcı grubunu getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} groupId - Grup ID
 * @returns {Promise<Object|null>} Hatırlatıcı grubu
 */
export const getReminderGroupById = async (db, groupId) => {
  try {
    const group = await db.getFirstAsync(`
      SELECT * FROM reminder_groups
      WHERE id = ?
    `, [groupId]);

    return group;
  } catch (error) {
    console.error('Hatırlatıcı grubu getirme hatası:', error);
    throw error;
  }
};

/**
 * Yeni bir hatırlatıcı grubu ekler
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} group - Grup verileri
 * @param {string} group.name - Grup adı
 * @param {string} group.description - Grup açıklaması
 * @param {string} group.color - Grup rengi
 * @param {string} group.icon - Grup ikonu
 * @returns {Promise<number>} Eklenen grubun ID'si
 */
export const addReminderGroup = async (db, group) => {
  try {
    const result = await db.runAsync(`
      INSERT INTO reminder_groups (name, description, color, icon)
      VALUES (?, ?, ?, ?)
    `, [
      group.name,
      group.description || null,
      group.color || '#3498db',
      group.icon || 'folder'
    ]);

    return result.lastInsertRowId;
  } catch (error) {
    console.error('Hatırlatıcı grubu ekleme hatası:', error);
    throw error;
  }
};

/**
 * Bir hatırlatıcı grubunu günceller
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} groupId - Grup ID
 * @param {Object} group - Güncellenecek grup verileri
 * @returns {Promise<boolean>} Başarılı ise true
 */
export const updateReminderGroup = async (db, groupId, group) => {
  try {
    await db.runAsync(`
      UPDATE reminder_groups
      SET name = ?, description = ?, color = ?, icon = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [
      group.name,
      group.description || null,
      group.color || '#3498db',
      group.icon || 'folder',
      group.is_active !== undefined ? group.is_active : 1,
      groupId
    ]);

    return true;
  } catch (error) {
    console.error('Hatırlatıcı grubu güncelleme hatası:', error);
    throw error;
  }
};

/**
 * Bir hatırlatıcı grubunu siler
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} groupId - Grup ID
 * @returns {Promise<boolean>} Başarılı ise true
 */
export const deleteReminderGroup = async (db, groupId) => {
  try {
    // Önce bu gruba ait hatırlatıcıları varsayılan gruba taşı
    const defaultGroup = await db.getFirstAsync(`
      SELECT id FROM reminder_groups
      WHERE is_default = 1
      LIMIT 1
    `);

    if (defaultGroup) {
      await db.runAsync(`
        UPDATE notifications
        SET group_id = ?
        WHERE group_id = ?
      `, [defaultGroup.id, groupId]);
    } else {
      // Varsayılan grup yoksa, group_id'yi null yap
      await db.runAsync(`
        UPDATE notifications
        SET group_id = NULL
        WHERE group_id = ?
      `, [groupId]);
    }

    // Grubu sil
    await db.runAsync(`
      DELETE FROM reminder_groups
      WHERE id = ?
    `, [groupId]);

    return true;
  } catch (error) {
    console.error('Hatırlatıcı grubu silme hatası:', error);
    throw error;
  }
};

/**
 * Bir gruba ait hatırlatıcıları getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} groupId - Grup ID
 * @param {Object} options - Sorgu seçenekleri
 * @returns {Promise<Array>} Hatırlatıcı listesi
 */
export const getRemindersByGroup = async (db, groupId, options = {}) => {
  try {
    const {
      status,
      sortBy = 'scheduled_at',
      sortOrder = 'asc',
      limit = 100,
      offset = 0
    } = options;

    // Sorgu parametrelerini hazırla
    const queryParams = [groupId];
    let whereClause = "related_type = 'user_reminder' AND group_id = ?";

    if (status) {
      whereClause += ' AND status = ?';
      queryParams.push(status);
    }

    // Sorguyu oluştur
    const query = `
      SELECT n.*, c.name as category_name, c.color as category_color
      FROM notifications n
      LEFT JOIN categories c ON n.category_id = c.id
      WHERE ${whereClause}
      ORDER BY ${sortBy} ${sortOrder}
      LIMIT ? OFFSET ?
    `;

    queryParams.push(limit, offset);

    // Sorguyu çalıştır
    const reminders = await db.getAllAsync(query, queryParams);

    // Sonuçları işle
    return reminders.map(reminder => {
      // JSON alanlarını parse et
      const repeatDays = reminder.repeat_days ? JSON.parse(reminder.repeat_days) : null;
      const repeatMonths = reminder.repeat_months ? JSON.parse(reminder.repeat_months) : null;
      const data = reminder.data ? JSON.parse(reminder.data) : {};

      return {
        ...reminder,
        repeat_days: repeatDays,
        repeat_months: repeatMonths,
        data
      };
    });
  } catch (error) {
    console.error('Grup hatırlatıcıları getirme hatası:', error);
    throw error;
  }
};

/**
 * Bir hatırlatıcının grubunu günceller
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} reminderId - Hatırlatıcı ID
 * @param {number|null} groupId - Grup ID (null ise varsayılan gruba atar)
 * @returns {Promise<boolean>} Başarılı ise true
 */
export const updateReminderGroupAssignment = async (db, reminderId, groupId) => {
  try {
    // groupId null ise, varsayılan grubu bul
    if (groupId === null) {
      const defaultGroup = await db.getFirstAsync(`
        SELECT id FROM reminder_groups
        WHERE is_default = 1
        LIMIT 1
      `);

      if (defaultGroup) {
        groupId = defaultGroup.id;
      }
    }

    // Hatırlatıcının grubunu güncelle
    await db.runAsync(`
      UPDATE notifications
      SET group_id = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [groupId, reminderId]);

    return true;
  } catch (error) {
    console.error('Hatırlatıcı grubu güncelleme hatası:', error);
    throw error;
  }
};
