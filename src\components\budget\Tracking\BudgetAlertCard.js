/**
 * <PERSON><PERSON>tçe Uyarı Kartı Komponenti
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md - Stage 3, Phase 3
 * 
 * Genel bütçe uyarıları ve bildirimler
 * Maksimum 200 satır - Tek sorumluluk prensibi
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../../../context/ThemeContext';

/**
 * Bütçe uyarı kartı komponenti
 * @param {Object} props - Component props
 * @param {string} props.type - Uyarı türü ('warning', 'error', 'info', 'success')
 * @param {string} props.title - Uyarı başlığı
 * @param {string} props.message - Uyarı mesajı
 * @param {string} props.actionText - Aksiyon buton metni
 * @param {Function} props.onActionPress - Aksiyon buton callback
 * @param {Function} props.onDismiss - Uyarıyı kapatma callback
 * @param {boolean} props.dismissible - Kapatılabilir mi
 * @param {Object} props.theme - Tema objesi (opsiyonel, context'ten alınır)
 */
const BudgetAlertCard = ({ 
  type = 'info',
  title,
  message,
  actionText,
  onActionPress,
  onDismiss,
  dismissible = true,
  theme: propTheme 
}) => {
  const { theme, isDarkMode } = useTheme();
  const currentTheme = propTheme || theme;

  /**
   * Uyarı türüne göre renk belirleme
   * @returns {string} Uyarı rengi
   */
  const getAlertColor = () => {
    switch (type) {
      case 'error':
        return currentTheme.ERROR;
      case 'warning':
        return currentTheme.WARNING;
      case 'success':
        return currentTheme.SUCCESS;
      default:
        return currentTheme.INFO;
    }
  };

  /**
   * Uyarı türüne göre ikon belirleme
   * @returns {string} İkon adı
   */
  const getAlertIcon = () => {
    switch (type) {
      case 'error':
        return 'error';
      case 'warning':
        return 'warning';
      case 'success':
        return 'check-circle';
      default:
        return 'info';
    }
  };

  const alertColor = getAlertColor();
  const alertIcon = getAlertIcon();

  return (
    <View style={[
      styles.container,
      {
        backgroundColor: currentTheme.SURFACE,
        borderLeftColor: alertColor,
      }
    ]}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.iconTitleSection}>
          <View style={[styles.iconContainer, { backgroundColor: alertColor + '20' }]}>
            <MaterialIcons name={alertIcon} size={20} color={alertColor} />
          </View>
          
          {title && (
            <Text style={[styles.title, { color: currentTheme.TEXT_PRIMARY }]}>
              {title}
            </Text>
          )}
        </View>

        {dismissible && onDismiss && (
          <TouchableOpacity
            style={styles.dismissButton}
            onPress={onDismiss}
          >
            <MaterialIcons name="close" size={20} color={currentTheme.TEXT_SECONDARY} />
          </TouchableOpacity>
        )}
      </View>

      {/* Mesaj */}
      {message && (
        <Text style={[styles.message, { color: currentTheme.TEXT_SECONDARY }]}>
          {message}
        </Text>
      )}

      {/* Aksiyon butonu */}
      {actionText && onActionPress && (
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: alertColor + '20' }]}
          onPress={onActionPress}
        >
          <Text style={[styles.actionText, { color: alertColor }]}>
            {actionText}
          </Text>
          <MaterialIcons name="chevron-right" size={16} color={alertColor} />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    borderLeftWidth: 4,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  iconTitleSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 12,
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    flex: 1,
  },
  dismissButton: {
    padding: 4,
  },
  message: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
    marginLeft: 48, // Icon + gap offset
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginLeft: 48, // Icon + gap offset
  },
  actionText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default BudgetAlertCard;
