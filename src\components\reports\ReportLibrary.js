/**
 * <PERSON><PERSON>tüphanesi Bileşeni
 * Kayıtlı raporları gö<PERSON>, yönetme ve düzenleme
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  Alert,
  Modal,
  RefreshControl,
  Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';
import { ReportLibraryService } from '../../services/reportLibraryService';
import { ExportService } from '../../services/exportService';

const { width: screenWidth } = Dimensions.get('window');

const ReportLibrary = ({ visible, onClose, onSelectReport }) => {
  const { theme } = useTheme();
  const [reports, setReports] = useState([]);
  const [filteredReports, setFilteredReports] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('all'); // all, favorites, recent
  const [isLoading, setIsLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingReport, setEditingReport] = useState(null);

  useEffect(() => {
    if (visible) {
      loadReports();
    }
  }, [visible]);

  useEffect(() => {
    filterReports();
  }, [reports, searchQuery, filterType]);

  /**
   * Raporları yükle
   */
  const loadReports = async () => {
    try {
      setIsLoading(true);
      const savedReports = await ReportLibraryService.getSavedReports();
      const favorites = await ReportLibraryService.getFavorites();
      const recentReports = await ReportLibraryService.getRecentReports();

      // Favorite ve recent bilgilerini ekle
      const enrichedReports = savedReports.map(report => ({
        ...report,
        isFavorite: favorites.includes(report.id),
        isRecent: recentReports.some(r => r.id === report.id)
      }));

      setReports(enrichedReports);
    } catch (error) {
      Alert.alert('Hata', 'Raporlar yüklenirken bir hata oluştu.');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Raporları filtrele
   */
  const filterReports = () => {
    let filtered = reports;

    // Tip filtreleme
    if (filterType === 'favorites') {
      filtered = filtered.filter(report => report.isFavorite);
    } else if (filterType === 'recent') {
      filtered = filtered.filter(report => report.isRecent);
    }

    // Arama filtreleme
    if (searchQuery.trim()) {
      filtered = filtered.filter(report =>
        report.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        report.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        report.metadata?.category?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Tarihe göre sırala (en yeni önce)
    filtered.sort((a, b) => 
      new Date(b.metadata?.lastModified || b.metadata?.createdAt) - 
      new Date(a.metadata?.lastModified || a.metadata?.createdAt)
    );

    setFilteredReports(filtered);
  };

  /**
   * Raporu favorilere ekle/çıkar
   */
  const toggleFavorite = async (reportId) => {
    try {
      const result = await ReportLibraryService.toggleFavorite(reportId);
      if (result.success) {
        await loadReports();
      }
    } catch (error) {
      Alert.alert('Hata', 'Favori işlemi gerçekleştirilemedi.');
    }
  };

  /**
   * Raporu sil
   */
  const deleteReport = async (reportId, reportTitle) => {
    Alert.alert(
      'Raporu Sil',
      `"${reportTitle}" raporunu silmek istediğinize emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              const result = await ReportLibraryService.deleteReport(reportId);
              if (result.success) {
                await loadReports();
              }
            } catch (error) {
              Alert.alert('Hata', 'Rapor silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };

  /**
   * Raporu dışa aktar
   */
  const exportReport = async (report) => {
    Alert.alert(
      'Raporu Dışa Aktar',
      'Hangi formatta dışa aktarmak istiyorsunuz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'PDF',
          onPress: async () => {
            try {
              const result = await ExportService.exportToPDF(report, {
                title: report.title,
                subtitle: report.description
              });
              
              if (result.success) {
                await ReportLibraryService.incrementExportCount(report.id);
                Alert.alert('Başarılı', 'Rapor PDF olarak dışa aktarıldı.');
              }
            } catch (error) {
              Alert.alert('Hata', 'PDF dışa aktarma işlemi başarısız oldu.');
            }
          }
        },
        {
          text: 'CSV',
          onPress: async () => {
            try {
              const result = await ExportService.exportToCSV(report, {
                filename: `${report.title?.replace(/[^a-zA-Z0-9]/g, '_')}.csv`
              });
              
              if (result.success) {
                await ReportLibraryService.incrementExportCount(report.id);
                Alert.alert('Başarılı', 'Rapor CSV olarak dışa aktarıldı.');
              }
            } catch (error) {
              Alert.alert('Hata', 'CSV dışa aktarma işlemi başarısız oldu.');
            }
          }
        }
      ]
    );
  };

  /**
   * Raporu düzenle
   */
  const editReport = (report) => {
    setEditingReport({
      id: report.id,
      title: report.title || '',
      description: report.description || '',
      category: report.metadata?.category || ''
    });
    setShowEditModal(true);
  };

  /**
   * Rapor güncellemelerini kaydet
   */
  const saveReportChanges = async () => {
    try {
      if (!editingReport.title.trim()) {
        Alert.alert('Hata', 'Rapor başlığı boş olamaz.');
        return;
      }

      const result = await ReportLibraryService.updateReport(editingReport.id, {
        title: editingReport.title,
        description: editingReport.description,
        metadata: {
          category: editingReport.category
        }
      });

      if (result.success) {
        setShowEditModal(false);
        setEditingReport(null);
        await loadReports();
        Alert.alert('Başarılı', 'Rapor güncellendi.');
      }
    } catch (error) {
      Alert.alert('Hata', 'Rapor güncellenirken bir hata oluştu.');
    }
  };

  /**
   * Yenileme
   */
  const onRefresh = async () => {
    setRefreshing(true);
    await loadReports();
    setRefreshing(false);
  };

  /**
   * Rapor öğesi render et
   */
  const renderReportItem = ({ item }) => (
    <View style={[styles.reportItem, { backgroundColor: theme.cardBackground }]}>
      <TouchableOpacity
        style={styles.reportContent}
        onPress={() => {
          onSelectReport(item);
          onClose();
        }}
      >
        <View style={styles.reportHeader}>
          <Text style={[styles.reportTitle, { color: theme.textColor }]}>
            {item.title || 'Başlıksız Rapor'}
          </Text>
          <View style={styles.reportBadges}>
            {item.isFavorite && (
              <Ionicons name="star" size={16} color={theme.primary} />
            )}
            {item.isRecent && (
              <Ionicons name="time" size={16} color={theme.accent} />
            )}
          </View>
        </View>
        
        <Text style={[styles.reportDescription, { color: theme.textSecondary }]}>
          {item.description || 'Açıklama yok'}
        </Text>
        
        <View style={styles.reportMeta}>
          <Text style={[styles.reportDate, { color: theme.textSecondary }]}>
            {new Date(item.metadata?.lastModified || item.metadata?.createdAt).toLocaleDateString('tr-TR')}
          </Text>
          <Text style={[styles.reportCategory, { color: theme.accent }]}>
            {item.metadata?.category || 'Genel'}
          </Text>
        </View>
      </TouchableOpacity>
      
      <View style={styles.reportActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => toggleFavorite(item.id)}
        >
          <Ionicons
            name={item.isFavorite ? "star" : "star-outline"}
            size={20}
            color={item.isFavorite ? theme.primary : theme.textSecondary}
          />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => editReport(item)}
        >
          <Ionicons name="pencil" size={20} color={theme.textSecondary} />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => exportReport(item)}
        >
          <Ionicons name="share" size={20} color={theme.textSecondary} />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => deleteReport(item.id, item.title)}
        >
          <Ionicons name="trash" size={20} color={theme.error} />
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
        <View style={[styles.header, { backgroundColor: theme.cardBackground }]}>
          <Text style={[styles.title, { color: theme.textColor }]}>
            Rapor Kütüphanesi
          </Text>
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="close" size={24} color={theme.textColor} />
          </TouchableOpacity>
        </View>

        <View style={styles.searchContainer}>
          <TextInput
            style={[styles.searchInput, { 
              backgroundColor: theme.cardBackground,
              color: theme.textColor,
              borderColor: theme.border
            }]}
            placeholder="Rapor ara..."
            placeholderTextColor={theme.textSecondary}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>

        <View style={styles.filterContainer}>
          {['all', 'favorites', 'recent'].map(type => (
            <TouchableOpacity
              key={type}
              style={[
                styles.filterButton,
                {
                  backgroundColor: filterType === type ? theme.primary : theme.cardBackground,
                  borderColor: theme.border
                }
              ]}
              onPress={() => setFilterType(type)}
            >
              <Text style={[
                styles.filterButtonText,
                { color: filterType === type ? '#fff' : theme.textColor }
              ]}>
                {type === 'all' ? 'Tümü' : type === 'favorites' ? 'Favoriler' : 'Son Kullanılan'}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        <FlatList
          data={filteredReports}
          renderItem={renderReportItem}
          keyExtractor={item => item.id}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[theme.primary]}
            />
          }
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Ionicons name="document-text-outline" size={48} color={theme.textSecondary} />
              <Text style={[styles.emptyText, { color: theme.textSecondary }]}>
                {searchQuery ? 'Arama kriterine uygun rapor bulunamadı' : 'Henüz kayıtlı rapor yok'}
              </Text>
            </View>
          }
          contentContainerStyle={styles.listContainer}
        />

        {/* Edit Modal */}
        <Modal
          visible={showEditModal}
          animationType="slide"
          transparent={true}
        >
          <View style={styles.modalOverlay}>
            <View style={[styles.editModal, { backgroundColor: theme.cardBackground }]}>
              <Text style={[styles.modalTitle, { color: theme.textColor }]}>
                Raporu Düzenle
              </Text>
              
              <TextInput
                style={[styles.input, { 
                  backgroundColor: theme.BACKGROUND,
                  color: theme.textColor,
                  borderColor: theme.border
                }]}
                placeholder="Rapor başlığı"
                placeholderTextColor={theme.textSecondary}
                value={editingReport?.title || ''}
                onChangeText={text => setEditingReport(prev => ({ ...prev, title: text }))}
              />
              
              <TextInput
                style={[styles.input, styles.multilineInput, { 
                  backgroundColor: theme.BACKGROUND,
                  color: theme.textColor,
                  borderColor: theme.border
                }]}
                placeholder="Rapor açıklaması"
                placeholderTextColor={theme.textSecondary}
                multiline
                value={editingReport?.description || ''}
                onChangeText={text => setEditingReport(prev => ({ ...prev, description: text }))}
              />
              
              <TextInput
                style={[styles.input, { 
                  backgroundColor: theme.BACKGROUND,
                  color: theme.textColor,
                  borderColor: theme.border
                }]}
                placeholder="Kategori"
                placeholderTextColor={theme.textSecondary}
                value={editingReport?.category || ''}
                onChangeText={text => setEditingReport(prev => ({ ...prev, category: text }))}
              />
              
              <View style={styles.modalActions}>
                <TouchableOpacity
                  style={[styles.modalButton, { backgroundColor: theme.textSecondary }]}
                  onPress={() => setShowEditModal(false)}
                >
                  <Text style={styles.modalButtonText}>İptal</Text>
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={[styles.modalButton, { backgroundColor: theme.primary }]}
                  onPress={saveReportChanges}
                >
                  <Text style={styles.modalButtonText}>Kaydet</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchInput: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingBottom: 12,
    gap: 8,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  listContainer: {
    paddingHorizontal: 16,
  },
  reportItem: {
    marginBottom: 12,
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  reportContent: {
    padding: 16,
  },
  reportHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  reportTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    flex: 1,
  },
  reportBadges: {
    flexDirection: 'row',
    gap: 4,
  },
  reportDescription: {
    fontSize: 14,
    marginBottom: 8,
  },
  reportMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  reportDate: {
    fontSize: 12,
  },
  reportCategory: {
    fontSize: 12,
    fontWeight: '500',
  },
  reportActions: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    paddingHorizontal: 16,
    paddingVertical: 8,
    justifyContent: 'space-around',
  },
  actionButton: {
    padding: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 16,
    marginTop: 16,
    textAlign: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  editModal: {
    width: screenWidth * 0.9,
    borderRadius: 12,
    padding: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    marginBottom: 12,
    fontSize: 16,
  },
  multilineInput: {
    height: 80,
    textAlignVertical: 'top',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
    gap: 12,
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  modalButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default ReportLibrary;
