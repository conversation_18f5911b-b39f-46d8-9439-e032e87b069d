import { format, parseISO, addDays, addWeeks, addMonths, addYears } from 'date-fns';
import * as notificationDbService from './notificationDbService';
import * as reminderTagService from './reminderTagService';
import * as reminderPatternService from './reminderPatternService';

/**
 * Hatırlatıcı ekler
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} reminder - Hatırlatıcı verileri
 * @param {string} reminder.title - Hatırlatıcı başlığı
 * @param {string} reminder.message - Hatırlatıcı mesajı
 * @param {Date|string} reminder.date - Hatırlatıcı tarihi
 * @param {string} reminder.time - Hatırlatıcı saati (HH:MM formatında)
 * @param {string} reminder.repeat_type - Tekrarlama tipi ('once', 'daily', 'weekly', 'monthly', 'yearly')
 * @param {number} reminder.repeat_interval - Tekrarlama aralığı
 * @param {Array} reminder.repeat_days - Tekrarlama günleri (haft<PERSON><PERSON><PERSON> günleri, 0-6)
 * @param {Array} reminder.repeat_months - Tekrarlama ayları (1-12)
 * @param {Date|string} reminder.repeat_end_date - Tekrarlama bitiş tarihi
 * @param {string} reminder.priority - Öncelik ('low', 'normal', 'high')
 * @param {number} reminder.category_id - Kategori ID
 * @param {number} reminder.group_id - Grup ID
 * @param {Array<number>} tagIds - Etiket ID'leri (opsiyonel)
 * @returns {Promise<number>} Eklenen hatırlatıcı ID'si
 */
export const addReminder = async (db, reminder, tagIds = []) => {
  try {
    // Tarih ve saat formatını düzenle
    const reminderDate = typeof reminder.date === 'string'
      ? parseISO(reminder.date)
      : reminder.date;

    const timeComponents = reminder.time ? reminder.time.split(':') : ['12', '00'];
    reminderDate.setHours(parseInt(timeComponents[0], 10));
    reminderDate.setMinutes(parseInt(timeComponents[1], 10));
    reminderDate.setSeconds(0);
    reminderDate.setMilliseconds(0);

    // Bildirim verilerini hazırla
    const notificationData = {
      title: reminder.title,
      message: reminder.message,
      type: 'reminder',
      priority: reminder.priority || 'normal',
      scheduled_at: reminderDate.toISOString(),
      repeat_type: reminder.repeat_type || 'once',
      repeat_interval: reminder.repeat_interval || 1,
      repeat_days: reminder.repeat_days ? JSON.stringify(reminder.repeat_days) : null,
      repeat_months: reminder.repeat_months ? JSON.stringify(reminder.repeat_months) : null,
      repeat_end_date: reminder.repeat_end_date ? (typeof reminder.repeat_end_date === 'string' ? reminder.repeat_end_date : reminder.repeat_end_date.toISOString()) : null,
      category_id: reminder.category_id || null,
      group_id: reminder.group_id || null,
      custom_pattern: reminder.custom_pattern || null,
      custom_pattern_type: reminder.custom_pattern_type || null,
      custom_pattern_value: reminder.custom_pattern_value ? JSON.stringify(reminder.custom_pattern_value) : null,
      related_id: null,
      related_type: 'user_reminder',
      data: JSON.stringify({
        isUserDefined: true,
        originalDate: reminderDate.toISOString(),
        ...reminder.data
      })
    };

    // Bildirimi oluştur
    const notificationId = await notificationDbService.createNotification(db, notificationData);

    // Etiketleri ekle
    if (tagIds && tagIds.length > 0) {
      await reminderTagService.updateReminderTags(db, notificationId, tagIds);
    }

    return notificationId;
  } catch (error) {
    console.error('Hatırlatıcı ekleme hatası:', error);
    throw error;
  }
};

/**
 * Hatırlatıcıyı günceller
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} reminderId - Hatırlatıcı ID
 * @param {Object} reminder - Güncellenecek hatırlatıcı verileri
 * @param {Array<number>} tagIds - Etiket ID'leri (opsiyonel)
 * @returns {Promise<boolean>} Başarılı ise true, değilse false
 */
export const updateReminder = async (db, reminderId, reminder, tagIds = null) => {
  try {
    // Mevcut hatırlatıcıyı getir
    const existingReminder = await getReminderById(db, reminderId);

    if (!existingReminder) {
      throw new Error('Hatırlatıcı bulunamadı');
    }

    // Tarih ve saat formatını düzenle
    let reminderDate;
    if (reminder.date) {
      reminderDate = typeof reminder.date === 'string'
        ? parseISO(reminder.date)
        : reminder.date;

      if (reminder.time) {
        const timeComponents = reminder.time.split(':');
        reminderDate.setHours(parseInt(timeComponents[0], 10));
        reminderDate.setMinutes(parseInt(timeComponents[1], 10));
      }
      reminderDate.setSeconds(0);
      reminderDate.setMilliseconds(0);
    }

    // Güncellenecek alanları hazırla
    const updateData = {
      title: reminder.title !== undefined ? reminder.title : existingReminder.title,
      message: reminder.message !== undefined ? reminder.message : existingReminder.message,
      status: reminder.status || existingReminder.status,
      priority: reminder.priority || existingReminder.priority,
      scheduled_at: reminderDate ? reminderDate.toISOString() : existingReminder.scheduled_at,
      repeat_type: reminder.repeat_type || existingReminder.repeat_type,
      repeat_interval: reminder.repeat_interval || existingReminder.repeat_interval,
      repeat_days: reminder.repeat_days ? JSON.stringify(reminder.repeat_days) : existingReminder.repeat_days,
      repeat_months: reminder.repeat_months ? JSON.stringify(reminder.repeat_months) : existingReminder.repeat_months,
      repeat_end_date: reminder.repeat_end_date ? (typeof reminder.repeat_end_date === 'string' ? reminder.repeat_end_date : reminder.repeat_end_date.toISOString()) : existingReminder.repeat_end_date,
      custom_pattern: reminder.custom_pattern !== undefined ? reminder.custom_pattern : existingReminder.custom_pattern,
      custom_pattern_type: reminder.custom_pattern_type !== undefined ? reminder.custom_pattern_type : existingReminder.custom_pattern_type,
      custom_pattern_value: reminder.custom_pattern_value ? JSON.stringify(reminder.custom_pattern_value) : existingReminder.custom_pattern_value,
      category_id: reminder.category_id !== undefined ? reminder.category_id : existingReminder.category_id,
      group_id: reminder.group_id !== undefined ? reminder.group_id : existingReminder.group_id,
      is_enabled: reminder.is_enabled !== undefined ? reminder.is_enabled : existingReminder.is_enabled,
      updated_at: new Date().toISOString()
    };

    // Veri alanını güncelle
    let data = existingReminder.data ? JSON.parse(existingReminder.data) : {};
    if (reminder.data) {
      data = { ...data, ...reminder.data };
    }

    // Bildirimi güncelle
    await db.runAsync(`
      UPDATE notifications
      SET title = ?, message = ?, status = ?, priority = ?, scheduled_at = ?,
          repeat_type = ?, repeat_interval = ?, repeat_days = ?, repeat_months = ?,
          repeat_end_date = ?, custom_pattern = ?, custom_pattern_type = ?, custom_pattern_value = ?,
          category_id = ?, group_id = ?, data = ?, is_enabled = ?, updated_at = ?
      WHERE id = ?
    `, [
      updateData.title,
      updateData.message,
      updateData.status,
      updateData.priority,
      updateData.scheduled_at,
      updateData.repeat_type,
      updateData.repeat_interval,
      updateData.repeat_days,
      updateData.repeat_months,
      updateData.repeat_end_date,
      updateData.custom_pattern,
      updateData.custom_pattern_type,
      updateData.custom_pattern_value,
      updateData.category_id,
      updateData.group_id,
      JSON.stringify(data),
      updateData.is_enabled,
      updateData.updated_at,
      reminderId
    ]);

    // Etiketleri güncelle (eğer belirtilmişse)
    if (tagIds !== null) {
      await reminderTagService.updateReminderTags(db, reminderId, tagIds);
    }

    // Bildirimi yeniden zamanla
    await notificationDbService.scheduleNotification(db, reminderId);

    return true;
  } catch (error) {
    console.error('Hatırlatıcı güncelleme hatası:', error);
    throw error;
  }
};

/**
 * Hatırlatıcıyı siler
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} reminderId - Hatırlatıcı ID
 * @returns {Promise<boolean>} Başarılı ise true, değilse false
 */
export const deleteReminder = async (db, reminderId) => {
  try {
    // Bildirimi iptal et
    await notificationDbService.cancelScheduledNotification(db, reminderId);

    // Bildirimi sil
    await db.runAsync('DELETE FROM notifications WHERE id = ?', [reminderId]);

    return true;
  } catch (error) {
    console.error('Hatırlatıcı silme hatası:', error);
    throw error;
  }
};

/**
 * Hatırlatıcıyı ID'ye göre getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} reminderId - Hatırlatıcı ID
 * @returns {Promise<Object|null>} Hatırlatıcı verileri
 */
export const getReminderById = async (db, reminderId) => {
  try {
    console.log(`getReminderById çağrıldı: ID=${reminderId}`);

    const reminder = await db.getFirstAsync(`
      SELECT * FROM notifications
      WHERE id = ? AND related_type = 'user_reminder'
    `, [reminderId]);

    if (!reminder) {
      console.log(`ID=${reminderId} için hatırlatıcı bulunamadı`);
      return null;
    }

    // Tarih ve saat bilgilerini ayır
    const scheduledDate = parseISO(reminder.scheduled_at);
    const date = format(scheduledDate, 'yyyy-MM-dd');
    const time = format(scheduledDate, 'HH:mm');

    // Tekrarlama bilgilerini parse et
    let repeatDays = null;
    let repeatMonths = null;
    let data = {};
    let customPatternValue = null;

    try {
      if (reminder.repeat_days) repeatDays = JSON.parse(reminder.repeat_days);
      if (reminder.repeat_months) repeatMonths = JSON.parse(reminder.repeat_months);
      if (reminder.custom_pattern_value) customPatternValue = JSON.parse(reminder.custom_pattern_value);

      // Data alanını parse et
      if (reminder.data) {
        if (typeof reminder.data === 'string') {
          data = JSON.parse(reminder.data);
        } else {
          data = reminder.data;
        }
      }
    } catch (parseError) {
      console.error(`ID=${reminderId} için JSON parse hatası:`, parseError);
    }

    console.log(`ID=${reminderId} için hatırlatıcı başarıyla getirildi`);

    return {
      ...reminder,
      date,
      time,
      repeat_days: repeatDays,
      repeat_months: repeatMonths,
      custom_pattern_value: customPatternValue,
      data
    };
  } catch (error) {
    console.error('Hatırlatıcı getirme hatası:', error);
    return null; // Hata durumunda null döndür
  }
};

/**
 * Tüm hatırlatıcıları getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} options - Sorgu seçenekleri
 * @param {string} options.status - Durum filtresi ('pending', 'sent', 'read', 'cancelled')
 * @param {string} options.sortBy - Sıralama alanı ('scheduled_at', 'created_at', 'priority')
 * @param {string} options.sortOrder - Sıralama yönü ('asc', 'desc')
 * @param {number} options.limit - Limit
 * @param {number} options.offset - Offset
 * @returns {Promise<Array>} Hatırlatıcı listesi
 */
export const getReminders = async (db, options = {}) => {
  try {
    const {
      status,
      sortBy = 'scheduled_at',
      sortOrder = 'asc',
      limit = 100,
      offset = 0
    } = options;

    // Sorgu parametrelerini hazırla
    const queryParams = [];
    let whereClause = "related_type = 'user_reminder'";

    if (status) {
      whereClause += ' AND status = ?';
      queryParams.push(status);
    }

    // Sorguyu oluştur
    const query = `
      SELECT * FROM notifications
      WHERE ${whereClause}
      ORDER BY ${sortBy} ${sortOrder}
      LIMIT ? OFFSET ?
    `;

    queryParams.push(limit, offset);

    // Sorguyu çalıştır
    const reminders = await db.getAllAsync(query, queryParams);

    // Sonuçları işle
    return reminders.map(reminder => {
      // Tarih ve saat bilgilerini ayır
      const scheduledDate = parseISO(reminder.scheduled_at);
      const date = format(scheduledDate, 'yyyy-MM-dd');
      const time = format(scheduledDate, 'HH:mm');

      // Tekrarlama bilgilerini parse et
      const repeatDays = reminder.repeat_days ? JSON.parse(reminder.repeat_days) : null;
      const repeatMonths = reminder.repeat_months ? JSON.parse(reminder.repeat_months) : null;
      const data = reminder.data ? JSON.parse(reminder.data) : {};

      return {
        ...reminder,
        date,
        time,
        repeat_days: repeatDays,
        repeat_months: repeatMonths,
        data
      };
    });
  } catch (error) {
    console.error('Hatırlatıcı listesi getirme hatası:', error);
    throw error;
  }
};

/**
 * Yaklaşan hatırlatıcıları getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} days - Kaç gün içindeki hatırlatıcılar
 * @param {number} limit - Limit
 * @returns {Promise<Array>} Yaklaşan hatırlatıcı listesi
 */
export const getUpcomingReminders = async (db, days = 7, limit = 10) => {
  try {
    const now = new Date();
    const endDate = addDays(now, days);

    const reminders = await db.getAllAsync(`
      SELECT * FROM notifications
      WHERE related_type = 'user_reminder'
      AND status = 'pending'
      AND scheduled_at BETWEEN ? AND ?
      AND is_enabled = 1
      ORDER BY scheduled_at ASC
      LIMIT ?
    `, [now.toISOString(), endDate.toISOString(), limit]);

    // Sonuçları işle
    return reminders.map(reminder => {
      // Tarih ve saat bilgilerini ayır
      const scheduledDate = parseISO(reminder.scheduled_at);
      const date = format(scheduledDate, 'yyyy-MM-dd');
      const time = format(scheduledDate, 'HH:mm');

      // Tekrarlama bilgilerini parse et
      const repeatDays = reminder.repeat_days ? JSON.parse(reminder.repeat_days) : null;
      const repeatMonths = reminder.repeat_months ? JSON.parse(reminder.repeat_months) : null;
      const data = reminder.data ? JSON.parse(reminder.data) : {};

      return {
        ...reminder,
        date,
        time,
        repeat_days: repeatDays,
        repeat_months: repeatMonths,
        data
      };
    });
  } catch (error) {
    console.error('Yaklaşan hatırlatıcılar getirme hatası:', error);
    throw error;
  }
};

/**
 * Hatırlatıcıyı etkinleştirir veya devre dışı bırakır
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} reminderId - Hatırlatıcı ID
 * @param {boolean} isEnabled - Etkinleştirme durumu
 * @returns {Promise<boolean>} Başarılı ise true, değilse false
 */
export const toggleReminderEnabled = async (db, reminderId, isEnabled) => {
  try {
    await db.runAsync(`
      UPDATE notifications
      SET is_enabled = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [isEnabled ? 1 : 0, reminderId]);

    if (isEnabled) {
      // Bildirimi yeniden zamanla
      await notificationDbService.scheduleNotification(db, reminderId);
    } else {
      // Bildirimi iptal et
      await notificationDbService.cancelScheduledNotification(db, reminderId);
    }

    return true;
  } catch (error) {
    console.error('Hatırlatıcı etkinleştirme hatası:', error);
    throw error;
  }
};

/**
 * Hatırlatıcıyı okundu olarak işaretler
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} reminderId - Hatırlatıcı ID
 * @returns {Promise<boolean>} Başarılı ise true, değilse false
 */
export const markReminderAsRead = async (db, reminderId) => {
  try {
    await db.runAsync(`
      UPDATE notifications
      SET status = 'read', updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [reminderId]);

    return true;
  } catch (error) {
    console.error('Hatırlatıcı okundu işaretleme hatası:', error);
    throw error;
  }
};

/**
 * Tekrarlanan hatırlatıcının bir sonraki zamanını hesaplar
 *
 * @param {Date} currentDate - Mevcut tarih
 * @param {string} repeatType - Tekrarlama tipi ('daily', 'weekly', 'monthly', 'yearly')
 * @param {number} repeatInterval - Tekrarlama aralığı
 * @param {Array} repeatDays - Tekrarlama günleri (haftanın günleri, 0-6)
 * @param {Array} repeatMonths - Tekrarlama ayları (1-12)
 * @returns {Date} Bir sonraki tarih
 */
export const calculateNextOccurrence = (currentDate, repeatType, repeatInterval = 1, repeatDays = null, repeatMonths = null, customPattern = null, customPatternType = null, customPatternValue = null) => {
  // Özel tekrarlama deseni varsa, onu kullan
  if (customPattern && customPatternType) {
    try {
      const patternValue = customPatternValue ? JSON.parse(customPatternValue) : {};
      return reminderPatternService.calculateNextOccurrence(currentDate, customPatternType, patternValue);
    } catch (error) {
      console.error('Özel tekrarlama deseni hesaplama hatası:', error);
      // Hata durumunda standart hesaplamaya devam et
    }
  }

  let nextDate = new Date(currentDate);

  switch (repeatType) {
    case 'daily':
      nextDate = addDays(nextDate, repeatInterval);
      break;
    case 'weekly':
      nextDate = addWeeks(nextDate, repeatInterval);
      break;
    case 'monthly':
      nextDate = addMonths(nextDate, repeatInterval);
      break;
    case 'yearly':
      nextDate = addYears(nextDate, repeatInterval);
      break;
    case 'custom':
      // Özel tekrarlama deseni için fallback
      nextDate = addDays(nextDate, 1);
      break;
    default:
      // Tekrarlanmayan hatırlatıcı
      break;
  }

  return nextDate;
};

/**
 * Gelişmiş arama ve filtreleme ile hatırlatıcıları getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} filters - Filtre seçenekleri
 * @param {string} filters.searchText - Arama metni (başlık ve mesajda arar)
 * @param {string} filters.priority - Öncelik filtresi ('low', 'normal', 'high')
 * @param {number} filters.categoryId - Kategori ID filtresi
 * @param {number} filters.groupId - Grup ID filtresi
 * @param {Array<number>} filters.tagIds - Etiket ID'leri filtresi
 * @param {string} filters.status - Durum filtresi ('pending', 'sent', 'read', 'cancelled')
 * @param {string} filters.repeatType - Tekrarlama tipi filtresi ('once', 'daily', 'weekly', 'monthly', 'yearly')
 * @param {Date|string} filters.startDate - Başlangıç tarihi filtresi
 * @param {Date|string} filters.endDate - Bitiş tarihi filtresi
 * @param {boolean} filters.isEnabled - Etkinleştirme durumu filtresi
 * @param {string} filters.sortBy - Sıralama alanı ('scheduled_at', 'created_at', 'priority', 'title')
 * @param {string} filters.sortOrder - Sıralama yönü ('asc', 'desc')
 * @param {number} filters.limit - Limit
 * @param {number} filters.offset - Offset
 * @returns {Promise<Array>} Filtrelenmiş hatırlatıcı listesi
 */
export const searchReminders = async (db, filters = {}) => {
  try {
    const {
      searchText,
      priority,
      categoryId,
      groupId,
      tagIds,
      status,
      repeatType,
      startDate,
      endDate,
      isEnabled,
      sortBy = 'scheduled_at',
      sortOrder = 'asc',
      limit = 100,
      offset = 0
    } = filters;

    // Temel sorgu
    let query = `
      SELECT DISTINCT n.*
      FROM notifications n
    `;

    // Etiket filtresi varsa JOIN ekle
    if (tagIds && tagIds.length > 0) {
      query += `
        JOIN reminder_tag_relations rtr ON n.id = rtr.reminder_id
      `;
    }

    // WHERE koşulları
    const whereConditions = ["n.related_type = 'user_reminder'"];
    const queryParams = [];

    // Arama metni
    if (searchText && searchText.trim()) {
      whereConditions.push("(n.title LIKE ? OR n.message LIKE ?)");
      const searchPattern = `%${searchText.trim()}%`;
      queryParams.push(searchPattern, searchPattern);
    }

    // Öncelik
    if (priority) {
      whereConditions.push("n.priority = ?");
      queryParams.push(priority);
    }

    // Kategori
    if (categoryId !== undefined && categoryId !== null) {
      whereConditions.push("n.category_id = ?");
      queryParams.push(categoryId);
    }

    // Grup
    if (groupId !== undefined && groupId !== null) {
      whereConditions.push("n.group_id = ?");
      queryParams.push(groupId);
    }

    // Etiketler
    if (tagIds && tagIds.length > 0) {
      whereConditions.push("rtr.tag_id IN (" + tagIds.map(() => "?").join(",") + ")");
      queryParams.push(...tagIds);
    }

    // Durum
    if (status) {
      whereConditions.push("n.status = ?");
      queryParams.push(status);
    }

    // Tekrarlama tipi
    if (repeatType) {
      whereConditions.push("n.repeat_type = ?");
      queryParams.push(repeatType);
    }

    // Tarih aralığı
    if (startDate) {
      const formattedStartDate = typeof startDate === 'string' ? startDate : startDate.toISOString();
      whereConditions.push("n.scheduled_at >= ?");
      queryParams.push(formattedStartDate);
    }

    if (endDate) {
      const formattedEndDate = typeof endDate === 'string' ? endDate : endDate.toISOString();
      whereConditions.push("n.scheduled_at <= ?");
      queryParams.push(formattedEndDate);
    }

    // Etkinleştirme durumu
    if (isEnabled !== undefined) {
      whereConditions.push("n.is_enabled = ?");
      queryParams.push(isEnabled ? 1 : 0);
    }

    // WHERE koşullarını ekle
    if (whereConditions.length > 0) {
      query += " WHERE " + whereConditions.join(" AND ");
    }

    // Sıralama
    query += ` ORDER BY n.${sortBy} ${sortOrder}`;

    // Limit ve offset
    query += " LIMIT ? OFFSET ?";
    queryParams.push(limit, offset);

    // Sorguyu çalıştır
    const reminders = await db.getAllAsync(query, queryParams);

    // Sonuçları işle
    return await Promise.all(reminders.map(async (reminder) => {
      // Tarih ve saat bilgilerini ayır
      const scheduledDate = parseISO(reminder.scheduled_at);
      const date = format(scheduledDate, 'yyyy-MM-dd');
      const time = format(scheduledDate, 'HH:mm');

      // Tekrarlama bilgilerini parse et
      const repeatDays = reminder.repeat_days ? JSON.parse(reminder.repeat_days) : null;
      const repeatMonths = reminder.repeat_months ? JSON.parse(reminder.repeat_months) : null;
      const data = reminder.data ? JSON.parse(reminder.data) : {};

      // Etiketleri getir
      let tags = [];
      if (reminder.id) {
        tags = await reminderTagService.getTagsByReminderId(db, reminder.id);
      }

      return {
        ...reminder,
        date,
        time,
        repeat_days: repeatDays,
        repeat_months: repeatMonths,
        data,
        tags
      };
    }));
  } catch (error) {
    console.error('Hatırlatıcı arama hatası:', error);
    throw error;
  }
};
