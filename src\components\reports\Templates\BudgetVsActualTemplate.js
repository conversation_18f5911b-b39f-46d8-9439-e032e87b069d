/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> vs Gerçek Raporu Şablonu
 * Bütçe hedefleri ile gerçek harcamaları karşılaştırma raporu
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';
import { useTheme } from '../../../context/ThemeContext';
import { useDataIntegration } from '../../../context/DataIntegrationContext';
import { ExportManager } from '../Export';

const BudgetVsActualTemplate = ({ 
  templateConfig, 
  customParams = {}, 
  onExport, 
  onSave 
}) => {
  const { theme } = useTheme();
  const { reportDataService, isLoading: contextLoading } = useDataIntegration();

  const [loading, setLoading] = useState(true);
  const [reportData, setReportData] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadReportData();
  }, [reportDataService]);

  /**
   * Rapor verilerini y<PERSON>
   */
  const loadReportData = async () => {
    if (!reportDataService) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const params = {
        dateRange: customParams.dateRange || 'current_month',
        includeProjections: customParams.includeProjections || false,
        ...customParams
      };
      
      const data = await reportDataService.getBudgetVsActualData(params);
      setReportData(data);
    } catch (error) {
      setError('Bütçe karşılaştırma verileri yüklenemedi: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Loading state
  if (loading || contextLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.BACKGROUND }]}>
        <ActivityIndicator size="large" color={theme.PRIMARY} />
        <Text style={[styles.loadingText, { color: theme.TEXT_PRIMARY }]}>
          Bütçe karşılaştırma verileri yükleniyor...
        </Text>
      </View>
    );
  }

  // Error state
  if (error) {
    return (
      <View style={[styles.errorContainer, { backgroundColor: theme.BACKGROUND }]}>
        <Text style={[styles.errorText, { color: theme.ERROR }]}>
          {error}
        </Text>
        <TouchableOpacity 
          style={[styles.retryButton, { backgroundColor: theme.PRIMARY }]}
          onPress={loadReportData}
        >
          <Text style={[styles.retryText, { color: theme.SURFACE }]}>
            Tekrar Dene
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Use real data or fallback to empty state
  const budgetData = reportData || { 
    totalBudget: 0, 
    totalActual: 0, 
    variance: 0, 
    categories: [] 
  };

  const getVarianceColor = (variance) => {
    if (variance > 0) return '#4CAF50'; // Over budget (green)
    if (variance < 0) return '#f44336'; // Under budget (red)
    return theme.TEXT_SECONDARY; // Exact match
  };

  const getProgressPercentage = (actual, budget) => {
    if (budget === 0) return 0;
    return Math.min((actual / budget) * 100, 100);
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      <View style={[styles.header, { backgroundColor: theme.SURFACE }]}>
        <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
          📊 Bütçe vs Gerçek Raporu
        </Text>
        <Text style={[styles.subtitle, { color: theme.TEXT_SECONDARY }]}>
          Bütçe hedefleri ile gerçek harcamaları karşılaştırması
        </Text>
      </View>

      {/* Genel Özet */}
      <View style={[styles.summaryContainer, { backgroundColor: theme.SURFACE }]}>
        <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
          💰 Genel Özet
        </Text>
        
        <View style={styles.summaryRow}>
          <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>
            Toplam Bütçe:
          </Text>
          <Text style={[styles.summaryValue, { color: theme.TEXT_PRIMARY }]}>
            ₺{budgetData.totalBudget.toLocaleString('tr-TR')}
          </Text>
        </View>
        
        <View style={styles.summaryRow}>
          <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>
            Gerçek Harcama:
          </Text>
          <Text style={[styles.summaryValue, { color: theme.TEXT_PRIMARY }]}>
            ₺{budgetData.totalActual.toLocaleString('tr-TR')}
          </Text>
        </View>
        
        <View style={styles.summaryRow}>
          <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>
            Fark:
          </Text>
          <Text style={[styles.summaryValue, { color: getVarianceColor(budgetData.variance) }]}>
            {budgetData.variance >= 0 ? '+' : ''}₺{budgetData.variance.toLocaleString('tr-TR')}
          </Text>
        </View>
      </View>

      {/* Kategori Detayları */}
      <View style={[styles.categoriesContainer, { backgroundColor: theme.SURFACE }]}>
        <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
          📈 Kategori Detayları
        </Text>
        
        {budgetData.categories.length === 0 ? (
          <View style={styles.emptyState}>
            <Text style={[styles.emptyText, { color: theme.TEXT_SECONDARY }]}>
              Bütçe karşılaştırma verisi bulunamadı.
            </Text>
          </View>
        ) : (
          budgetData.categories.map((category, index) => (
            <View key={index} style={styles.categoryCard}>
              <View style={styles.categoryHeader}>
                <Text style={[styles.categoryName, { color: theme.TEXT_PRIMARY }]}>
                  {category.name}
                </Text>
                <Text style={[styles.categoryVariance, { 
                  color: getVarianceColor(category.variance) 
                }]}>
                  {category.variance >= 0 ? '+' : ''}₺{category.variance.toLocaleString('tr-TR')}
                </Text>
              </View>
              
              <View style={styles.categoryProgress}>
                <View style={[styles.progressBar, { backgroundColor: theme.BACKGROUND }]}>
                  <View 
                    style={[
                      styles.progressFill, 
                      { 
                        width: `${getProgressPercentage(category.actual, category.budget)}%`,
                        backgroundColor: getProgressPercentage(category.actual, category.budget) > 100 
                          ? '#f44336' 
                          : theme.PRIMARY 
                      }
                    ]} 
                  />
                </View>
                <Text style={[styles.progressText, { color: theme.TEXT_SECONDARY }]}>
                  %{Math.round(getProgressPercentage(category.actual, category.budget))}
                </Text>
              </View>
              
              <View style={styles.categoryDetails}>
                <View style={styles.detailRow}>
                  <Text style={[styles.detailLabel, { color: theme.TEXT_SECONDARY }]}>
                    Bütçe:
                  </Text>
                  <Text style={[styles.detailValue, { color: theme.TEXT_PRIMARY }]}>
                    ₺{category.budget.toLocaleString('tr-TR')}
                  </Text>
                </View>
                
                <View style={styles.detailRow}>
                  <Text style={[styles.detailLabel, { color: theme.TEXT_SECONDARY }]}>
                    Gerçek:
                  </Text>
                  <Text style={[styles.detailValue, { color: theme.TEXT_PRIMARY }]}>
                    ₺{category.actual.toLocaleString('tr-TR')}
                  </Text>
                </View>
              </View>
            </View>
          ))
        )}
      </View>

      {/* Aksiyon Butonları */}
      <View style={styles.actionButtons}>
        <ExportManager 
          reportData={budgetData}
          reportTitle="Bütçe vs Gerçek Raporu"
          reportType="budget_vs_actual"
          buttonStyle={styles.exportButton}
          buttonTextStyle={styles.exportButtonText}
          theme={theme}
        />
        
        <TouchableOpacity 
          style={[styles.actionButton, { backgroundColor: theme.ACCENT }]}
          onPress={onSave}
        >
          <Text style={[styles.actionButtonText, { color: theme.SURFACE }]}>
            💾 Raporu Kaydet
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    padding: 20,
    marginBottom: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    textAlign: 'center',
  },
  summaryContainer: {
    padding: 16,
    marginBottom: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 14,
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  categoriesContainer: {
    padding: 16,
    marginBottom: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  emptyState: {
    padding: 20,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
  },
  categoryCard: {
    padding: 16,
    marginBottom: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  categoryVariance: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  categoryProgress: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  progressBar: {
    flex: 1,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 12,
    fontWeight: 'bold',
    minWidth: 40,
    textAlign: 'right',
  },
  categoryDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  detailRow: {
    alignItems: 'center',
  },
  detailLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  actionButtons: {
    flexDirection: 'column',
    gap: 12,
    marginBottom: 16,
  },
  exportButton: {
    marginBottom: 8,
  },
  exportButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  actionButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  // Loading and error states
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryText: {
    fontSize: 14,
    fontWeight: '600',
  },
});

export default BudgetVsActualTemplate;
