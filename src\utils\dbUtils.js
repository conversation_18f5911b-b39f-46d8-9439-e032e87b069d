import * as SQLite from 'expo-sqlite';

/**
 * Veritabanı bağlantısını elde eder
 * @returns {Promise<SQLite.SQLiteDatabase>} - SQLite veritabanı bağlantısı
 */
export const getDatabase = async () => {
  try {
    // Yeni Expo SQLite sürümünde openDatabase kullanmak yerine
    // global.db'yi kullanıyoruz (App.js'de SQLiteProvider tarafından oluşturuldu)
    if (!global.db) {
      console.warn('Veritabanı bağlantısı bulunamadı. Yeniden bağlanma deneniyor...');
      
      // Burada yeniden bağlanma stratejisi eklenebilir
      // Eğer başka bir API mevcut ise
      
      if (!global.db) {
        throw new Error('Veritabanı bağlantısı bulunamadı. SQLiteProvider doğru yapılandırıldı mı?');
      }
    }
    
    // Bağlantı testi yapalım
    try {
      // Basit bir sorgu ile bağlantıyı test et
      await global.db.execAsync('SELECT 1');
    } catch (testError) {
      console.error('Veritabanı bağlantı testi başarısız:', testError);
      // Bağlantıyı yenileme girişimi yapılabilir
    }
    
    return global.db;
  } catch (error) {
    console.error('Veritabanı bağlantı hatası:', error);
    throw error;
  }
};

/**
 * Belirli bir süre bekler
 * @param {number} ms - Milisaniye cinsinden bekleme süresi
 * @returns {Promise<void>}
 */
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Yardımcı fonksiyon: SQL sorgusu çalıştırır
 * @param {SQLite.SQLiteDatabase} db - SQLite veritabanı bağlantısı
 * @param {string} query - Çalıştırılacak SQL sorgusu
 * @param {Array} params - Sorgu için parametreler (opsiyonel)
 * @param {Object} options - Ek seçenekler
 * @param {number} options.maxRetries - Maksimum yeniden deneme sayısı
 * @param {number} options.retryDelay - Yeniden denemeler arasındaki bekleme süresi (ms)
 * @returns {Promise<any>} - Sorgu sonucu
 */
export const executeSql = async (db, query, params = [], options = { maxRetries: 3, retryDelay: 200 }) => {
  const { maxRetries, retryDelay } = options;
  let lastError;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      if (!db) {
        throw new Error('Veritabanı bağlantısı bulunamadı');
      }      // Sorgu tipine göre uygun metodu çağır
      const firstWord = query.trim().split(' ')[0].toUpperCase();
      
      if (firstWord === 'SELECT') {
        // Veri okuma işlemi
        if (query.includes('LIMIT 1') || query.includes('limit 1')) {
          const result = await db.getFirstAsync(query, params);
          // Eğer getFirstAsync() null döndüyse, { rows: { _array: [] } } ile uyumlu bir format döndürelim
          return result ? { rows: { _array: [result], item: (idx) => [result][idx], length: 1 } } : { rows: { _array: [], item: () => null, length: 0 } };
        } else {
          // Yeni expo-sqlite sürümünde getAllAsync doğrudan diziyi döndürür
          // Eski biçimle uyumluluk için rows._array yapısını taklit edelim
          const results = await db.getAllAsync(query, params);
          return { 
            rows: { 
              _array: results, 
              item: (idx) => results[idx],
              length: results.length
            } 
          };
        }
      } else if (['INSERT', 'UPDATE', 'DELETE'].includes(firstWord)) {
        // Veri yazma işlemi
        const result = await db.runAsync(query, params);
        
        // result.insertId, result.rowsAffected gibi değerlerin uyumlu olduğundan emin olalım
        return {
          insertId: result.lastInsertRowId || 0,
          rowsAffected: result.changes || 0,
          ...result
        };
      } else if (firstWord === 'BEGIN' || firstWord === 'COMMIT' || firstWord === 'ROLLBACK' || firstWord === 'PRAGMA') {
        // Direkt çalıştırılacak sorgular
        return await db.execAsync(query);
      } else {
        // Diğer sorgular için varsayılan olarak execAsync kullan
        return await db.execAsync(query);
      }
    } catch (error) {
      lastError = error;

      // Veritabanı kilitli hatası ise ve yeniden deneme hakkı varsa bekle ve tekrar dene
      if (error.message && error.message.includes('database is locked') && attempt < maxRetries) {
        console.warn(`Veritabanı kilitli, ${attempt + 1}. deneme. Bekleniyor: ${retryDelay}ms`);
        await sleep(retryDelay);
        continue;
      }

      console.error(`SQL hatası (${attempt + 1}/${maxRetries + 1}): ${query}`, error);
      throw error;
    }
  }

  throw lastError;
};

/**
 * Veritabanı tablosunun varlığını kontrol eder
 * @param {SQLite.SQLiteDatabase} db - SQLite veritabanı bağlantısı
 * @param {string} tableName - Kontrol edilecek tablo adı
 * @returns {Promise<boolean>} - Tablo varsa true, yoksa false
 */
export const tableExists = async (db, tableName) => {
  try {
    const result = await db.getFirstAsync(
      `SELECT name FROM sqlite_master WHERE type='table' AND name=?`,
      [tableName]
    );
    return Boolean(result?.name);
  } catch (error) {
    console.error(`Tablo kontrol hatası: ${tableName}`, error);
    return false;
  }
};

/**
 * İşlem (transaction) içinde birden çok sorgu çalıştırır
 * @param {SQLite.SQLiteDatabase} db - SQLite veritabanı bağlantısı
 * @param {Function} callback - İşlem içinde çalıştırılacak fonksiyon
 * @param {Object} options - Ek seçenekler
 * @param {number} options.maxRetries - Maksimum yeniden deneme sayısı
 * @param {number} options.retryDelay - Yeniden denemeler arasındaki bekleme süresi (ms)
 * @returns {Promise<any>} - İşlem sonucu
 */
export const withTransaction = async (db, callback, options = { maxRetries: 3, retryDelay: 200 }) => {
  const { maxRetries, retryDelay } = options;
  let lastError;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      await executeSql(db, 'BEGIN TRANSACTION');
      const result = await callback();
      await executeSql(db, 'COMMIT');
      return result;
    } catch (error) {
      lastError = error;

      try {
        // Her durumda rollback yapmaya çalış
        await executeSql(db, 'ROLLBACK');
      } catch (rollbackError) {
        console.warn('Rollback hatası:', rollbackError);
      }

      // Veritabanı kilitli hatası ise ve yeniden deneme hakkı varsa bekle ve tekrar dene
      if (error.message?.includes('database is locked') && attempt < maxRetries) {
        console.warn(`Transaction sırasında veritabanı kilitli, ${attempt + 1}. deneme. Bekleniyor: ${retryDelay}ms`);
        await sleep(retryDelay * (attempt + 1)); // Her denemede bekleme süresini artır
        continue;
      }

      console.error(`Transaction hatası (${attempt + 1}/${maxRetries + 1}):`, error);
      throw error;
    }
  }

  throw lastError;
};

/**
 * Birden çok SQL sorgusunu sırayla çalıştırır
 * @param {SQLite.SQLiteDatabase} db - SQLite veritabanı bağlantısı
 * @param {string[]} queries - Çalıştırılacak SQL sorguları listesi
 * @param {Object} options - Ek seçenekler
 * @param {number} options.maxRetries - Maksimum yeniden deneme sayısı
 * @param {number} options.retryDelay - Yeniden denemeler arasındaki bekleme süresi (ms)
 * @returns {Promise<void>}
 */
export const executeQueries = async (db, queries, options = { maxRetries: 3, retryDelay: 200 }) => {
  return withTransaction(db, async () => {
    for (const query of queries) {
      await executeSql(db, query, [], options);
    }
  }, options);
};

/**
 * Veritabanı bağlantısını optimize eder
 * @param {SQLite.SQLiteDatabase} db - SQLite veritabanı bağlantısı
 * @returns {Promise<void>}
 */
export const optimizeDatabase = async (db) => {
  try {
    // PRAGMA ayarları transaction dışında yapılmalı
    // Bu ayarlar transaction içinde değiştirilemez

    // WAL modunu etkinleştir (transaction dışında)
    try {
      await db.execAsync('PRAGMA journal_mode = WAL');
    } catch (error) {
      console.warn('WAL modu ayarlama hatası (normal olabilir):', error.message);
    }

    // Senkron modunu ayarla (transaction dışında)
    try {
      await db.execAsync('PRAGMA synchronous = NORMAL');
    } catch (error) {
      console.warn('Synchronous modu ayarlama hatası (normal olabilir):', error.message);
    }

    // Geçici tabloları bellekte tut (transaction dışında)
    try {
      await db.execAsync('PRAGMA temp_store = MEMORY');
    } catch (error) {
      console.warn('Temp store ayarlama hatası (normal olabilir):', error.message);
    }

    // Veritabanını optimize et (transaction dışında)
    try {
      await db.execAsync('PRAGMA optimize');
    } catch (error) {
      console.warn('Database optimize hatası (normal olabilir):', error.message);
    }

    // Veritabanını vakumla (transaction dışında)
    try {
      await db.execAsync('VACUUM');
    } catch (error) {
      console.warn('Vacuum hatası (normal olabilir):', error.message);
    }

    console.log('Veritabanı optimize edildi.');
  } catch (error) {
    console.error('Veritabanı optimizasyon hatası:', error);
    // Hata fırlatma, sadece log
  }
};
