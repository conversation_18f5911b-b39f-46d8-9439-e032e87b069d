/**
 * Tasarruf tipleri ve özellikleri
 */
export const SAVING_TYPES = {
  REGULAR: {
    id: 'regular',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    icon: 'event-repeat',
    description: '<PERSON><PERSON><PERSON> aralıklarla düzenli tasarruf yapın',
    tip: 'Her maaş günü otomatik tasarruf için ideal'
  },
  TARGET: {
    id: 'target',
    name: 'Hede<PERSON>li Birikim',
    icon: 'flag',
    description: '<PERSON><PERSON><PERSON> bir hedef için tasarruf yapın',
    tip: 'Ev, araba gibi büyük hedefler için uygun'
  },
  CASUAL: {
    id: 'casual',
    name: 'Serbest Birikim',
    icon: 'savings',
    description: 'İstediğiniz zaman, istediğiniz miktar',
    tip: 'Ek gelirlerinizi değerlendirmek için ideal'
  }
};

/**
 * Tasarruf periyotları
 */
export const SAVING_FREQUENCIES = {
  DAILY: {
    id: 'daily',
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    days: 1,
    icon: 'today',
    multiplier: 30 // Aylık hesaplama için çarpan
  },
  WEEKLY: {
    id: 'weekly',
    name: '<PERSON>ftal<PERSON>k',
    days: 7,
    icon: 'view-week',
    multiplier: 4 // Aylık hesaplama için çarpan
  },
  MONTHLY: {
    id: 'monthly',
    name: 'Aylık',
    days: 30,
    icon: 'calendar-month',
    multiplier: 1
  }
};

/**
 * Tasarruf kategorileri
 */
export const SAVING_CATEGORIES = [
  {
    id: 'emergency',
    name: 'Acil Durum Fonu',
    icon: 'warning',
    color: '#e74c3c',
    description: 'Beklenmeyen durumlar için',
    recommendedMonths: 6, // Önerilen: 6 aylık gider
    priority: 'high'
  },
  {
    id: 'retirement',
    name: 'Emeklilik',
    icon: 'account-balance',
    color: '#3498db',
    description: 'Uzun vadeli emeklilik tasarrufu',
    recommendedPercent: 15, // Maaşın %15'i
    priority: 'high'
  },
  {
    name: 'Eğitim',
    icon: 'school',
    description: 'Eğitim harcamaları için birikim',
    color: '#f1c40f'
  },
  {
    name: 'Tatil',
    icon: 'beach-access',
    description: 'Tatil için ayrılan tasarruf',
    color: '#e67e22'
  },
  {
    name: 'Ev Alımı',
    icon: 'home',
    description: 'Ev almak için yapılan birikim',
    color: '#9b59b6'
  },
  {
    name: 'Araba',
    icon: 'directions-car',
    description: 'Araç alımı için birikim',
    color: '#34495e'
  },
  {
    name: 'Evlilik',
    icon: 'favorite',
    description: 'Düğün masrafları için birikim',
    color: '#e91e63'
  },
  {
    name: 'Genel Tasarruf',
    icon: 'savings',
    description: 'Genel amaçlı tasarruf',
    color: '#2ecc71',
    recommended_percent: 20
  }
];

/**
 * Tasarruf durumları
 */
export const SAVING_STATUS = {
  ACTIVE: {
    id: 'active',
    name: 'Devam Ediyor',
    color: '#2ecc71'
  },
  COMPLETED: {
    id: 'completed',
    name: 'Tamamlandı',
    color: '#3498db'
  },
  PAUSED: {
    id: 'paused',
    name: 'Duraklatıldı',
    color: '#f1c40f'
  },
  CANCELLED: {
    id: 'cancelled',
    name: 'İptal Edildi',
    color: '#e74c3c'
  }
};

/**
 * Tasarruf önerileri
 */
export const SAVING_TIPS = [
  {
    id: 'emergency_fund',
    title: 'Acil Durum Fonu',
    message: '3-6 aylık gideriniz kadar acil durum fonu bulundurmanız önerilir.',
    icon: 'warning',
    priority: 1
  },
  {
    id: 'regular_saving',
    title: 'Düzenli Tasarruf',
    message: 'Maaşınızın en az %20\'sini tasarruf etmeyi hedefleyin.',
    icon: 'trending-up',
    priority: 2
  },
  {
    title: 'Emeklilik Planı',
    message: 'Gelirinizin %10-15\'ini emeklilik için ayırmayı düşünün.'
  }
];

/**
 * Tasarruf tema renkleri
 */
export const SAVING_THEME = {
  primary: '#2ecc71',
  secondary: '#27ae60',
  success: '#2ecc71',
  warning: '#f1c40f',
  danger: '#e74c3c',
  info: '#3498db',
  background: {
    light: '#f0fff4',
    white: '#ffffff',
    gray: '#f8f9fa'
  },
  text: {
    primary: '#2c3e50',
    secondary: '#95a5a6',
    muted: '#7f8c8d'
  },
  border: {
    light: '#e9ecef',
    medium: '#dee2e6'
  }
};

/**
 * Tasarruf hedef hesaplama yardımcıları
 */
export const calculateRequiredSaving = ({ targetAmount, targetDate, frequency }) => {
  const today = new Date();
  const target = new Date(targetDate);
  const remainingDays = Math.ceil((target - today) / (1000 * 60 * 60 * 24));
  const freq = SAVING_FREQUENCIES[frequency];
  
  if (!freq) return 0;
  
  return targetAmount / (remainingDays / freq.days);
};
