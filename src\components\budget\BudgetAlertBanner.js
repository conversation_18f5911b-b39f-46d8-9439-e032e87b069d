/**
 * Budget Alert Banner Bileşeni
 * Bütçe uyarılarını güzel bir şekilde gösterir
 */

import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

/**
 * Budget Alert Banner Bileşeni
 * @param {Object} props - Bileşen özellikleri
 * @param {Array} props.alerts - Uyarı listesi
 * @param {Object} props.theme - Tema bilgileri
 * @param {Function} props.onDismiss - Uyarı kapatma fonksiyonu
 * @param {Function} props.onPress - Uyarı tıklama fonksiyonu
 */
const BudgetAlertBanner = ({ alerts = [], theme, onDismiss, onPress }) => {
  if (!alerts || alerts.length === 0) return null;

  const getAlertIcon = (type) => {
    switch (type) {
      case 'budget_exceeded':
        return 'warning';
      case 'budget_warning':
        return 'alert-circle';
      case 'budget_target':
        return 'information-circle';
      default:
        return 'notifications';
    }
  };

  const getAlertColor = (type) => {
    switch (type) {
      case 'budget_exceeded':
        return theme.error;
      case 'budget_warning':
        return theme.warning;
      case 'budget_target':
        return theme.primary;
      default:
        return theme.textSecondary;
    }
  };

  // En önemli uyarıyı göster
  const mainAlert = alerts[0];
  const hasMoreAlerts = alerts.length > 1;

  return (
    <TouchableOpacity
      style={[
        styles.alertBanner,
        {
          backgroundColor: getAlertColor(mainAlert.type) + '15',
          borderLeftColor: getAlertColor(mainAlert.type),
        },
      ]}
      onPress={() => onPress && onPress(mainAlert)}
      activeOpacity={0.8}
    >
      <View style={styles.alertContent}>
        <Ionicons
          name={getAlertIcon(mainAlert.type)}
          size={24}
          color={getAlertColor(mainAlert.type)}
          style={styles.alertIcon}
        />
        
        <View style={styles.alertTextContainer}>
          <Text
            style={[styles.alertTitle, { color: getAlertColor(mainAlert.type) }]}
            numberOfLines={1}
          >
            {mainAlert.title}
          </Text>
          <Text
            style={[styles.alertMessage, { color: theme.textSecondary }]}
            numberOfLines={2}
          >
            {mainAlert.message}
          </Text>
          {hasMoreAlerts && (
            <Text style={[styles.moreAlerts, { color: theme.primary }]}>
              +{alerts.length - 1} daha fazla uyarı
            </Text>
          )}
        </View>

        <TouchableOpacity
          style={styles.dismissButton}
          onPress={() => onDismiss && onDismiss(mainAlert.id)}
        >
          <Ionicons name="close" size={20} color={theme.textSecondary} />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  alertBanner: {
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    borderLeftWidth: 4,
    overflow: 'hidden',
  },
  alertContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 16,
  },
  alertIcon: {
    marginRight: 12,
    marginTop: 2,
  },
  alertTextContainer: {
    flex: 1,
  },
  alertTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  alertMessage: {
    fontSize: 14,
    lineHeight: 20,
  },
  moreAlerts: {
    fontSize: 12,
    fontWeight: '500',
    marginTop: 4,
  },
  dismissButton: {
    padding: 4,
    marginLeft: 8,
  },
});

export default BudgetAlertBanner;
