/**
 * Comprehensive Budget Creation Screen
 * Following BUDGET_MANAGEMENT_REDESIGN_PLAN.md specifications
 * 
 * Features:
 * - Advanced budget creation flow
 * - Category selection and limits
 * - Notification settings configuration
 * - Budget validity periods and dates
 * - Recurring budget options
 * - Multi-currency support with exchange rates
 * - Budget templates and customization
 * - Real database integration
 * - Dark mode support
 * - Turkish localization
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  StyleSheet,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useSQLiteContext } from 'expo-sqlite';
import DateTimePicker from '@react-native-community/datetimepicker';

// Context imports
import { useTheme } from '../../context/ThemeContext';
import { useExchangeRate } from '../../context/ExchangeRateProvider';

// Service imports
import * as RealBudgetService from '../../services/budget/realBudgetService';
import * as CategoryService from '../../services/categoryService';

/**
 * Budget Creation Screen Component
 * @param {Object} navigation - Navigation object
 */
export default function BudgetCreateScreen({ navigation }) {
  const { theme, isDarkMode } = useTheme();
  const { rates, baseCurrency } = useExchangeRate();
  const db = useSQLiteContext();

  // Form state
  const [step, setStep] = useState(1); // Multi-step form
  const [budgetData, setBudgetData] = useState({
    name: '',
    description: '',
    type: 'category_based',
    period_type: 'monthly',
    start_date: new Date(),
    end_date: null,
    total_limit: '',
    currency: 'TRY',
    auto_renew: false,
  });
  
  // Categories and limits
  const [categories, setCategories] = useState([]);
  const [categoryLimits, setCategoryLimits] = useState({});
  const [selectedCategories, setSelectedCategories] = useState([]);
  
  // Alert settings
  const [alertSettings, setAlertSettings] = useState({
    threshold_75: true,
    threshold_90: true,
    threshold_100: true,
    daily_limit_exceeded: true,
    category_limit_exceeded: true,
    weekly_summary: false,
  });
  
  // Templates
  const [templates, setTemplates] = useState([]);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  
  // UI state
  const [loading, setLoading] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [datePickerMode, setDatePickerMode] = useState('start');
  const [categoriesLoading, setCategoriesLoading] = useState(true);

  /**
   * Loads categories and templates
   */
  const loadInitialData = useCallback(async () => {
    try {
      setCategoriesLoading(true);
      
      // Load expense categories
      const allCategories = await CategoryService.getCategories(db);
      const expenseCategories = allCategories.filter(cat => 
        cat.type === 'expense' || cat.type === 'both'
      );
      setCategories(expenseCategories);
      
      // Load budget templates
      const budgetTemplates = await RealBudgetService.getBudgetTemplates(db);
      setTemplates(budgetTemplates);
      
    } catch (error) {
      console.error('❌ Initial data loading failed:', error);
      Alert.alert('Hata', 'Veriler yüklenirken bir hata oluştu.');
    } finally {
      setCategoriesLoading(false);
    }
  }, [db]);

  /**
   * Handles template selection
   */
  const handleTemplateSelect = useCallback((template) => {
    setSelectedTemplate(template);
    
    const templateData = template.template_data;
    if (templateData && templateData.categories) {
      // Calculate category limits based on total budget and percentages
      const totalBudget = parseFloat(budgetData.total_limit) || 0;
      const newCategoryLimits = {};
      const newSelectedCategories = [];
      
      templateData.categories.forEach(tempCat => {
        // Find matching category by name
        const matchingCategory = categories.find(cat => 
          cat.name.toLowerCase().includes(tempCat.name.toLowerCase()) ||
          tempCat.name.toLowerCase().includes(cat.name.toLowerCase())
        );
        
        if (matchingCategory) {
          const limitAmount = totalBudget * (tempCat.percentage / 100);
          newCategoryLimits[matchingCategory.id] = limitAmount.toString();
          newSelectedCategories.push(matchingCategory.id);
        }
      });
      
      setCategoryLimits(newCategoryLimits);
      setSelectedCategories(newSelectedCategories);
      setBudgetData(prev => ({
        ...prev,
        type: templateData.type || 'category_based',
        period_type: templateData.period_type || 'monthly'
      }));
    }
  }, [budgetData.total_limit, categories]);

  /**
   * Handles budget creation
   */
  const handleCreateBudget = useCallback(async () => {
    try {
      setLoading(true);
      
      // Validation
      if (!budgetData.name.trim()) {
        Alert.alert('Hata', 'Lütfen bütçe adı girin.');
        return;
      }
      
      if (!budgetData.total_limit || parseFloat(budgetData.total_limit) <= 0) {
        Alert.alert('Hata', 'Lütfen geçerli bir bütçe limiti girin.');
        return;
      }
      
      if (selectedCategories.length === 0) {
        Alert.alert('Hata', 'Lütfen en az bir kategori seçin.');
        return;
      }
      
      // Prepare category limits array
      const categoryLimitsArray = selectedCategories.map(categoryId => ({
        category_id: categoryId,
        limit_amount: parseFloat(categoryLimits[categoryId]) || 0,
      }));
      
      // Validate category limits total
      const totalCategoryLimits = categoryLimitsArray.reduce((sum, cat) => sum + cat.limit_amount, 0);
      const budgetLimit = parseFloat(budgetData.total_limit);
      
      if (totalCategoryLimits > budgetLimit * 1.1) { // Allow 10% tolerance
        Alert.alert(
          'Uyarı',
          `Kategori limitlerinin toplamı (${totalCategoryLimits.toLocaleString('tr-TR')} ₺) bütçe limitini (${budgetLimit.toLocaleString('tr-TR')} ₺) aşıyor. Devam etmek istiyor musunuz?`,
          [
            { text: 'İptal', style: 'cancel' },
            { text: 'Devam Et', onPress: () => proceedWithCreation() }
          ]
        );
        return;
      }
      
      await proceedWithCreation();
      
    } catch (error) {
      console.error('❌ Budget creation failed:', error);
      Alert.alert('Hata', 'Bütçe oluşturulurken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  }, [budgetData, selectedCategories, categoryLimits]);

  /**
   * Proceeds with budget creation
   */
  const proceedWithCreation = async () => {
    try {
      const categoryLimitsArray = selectedCategories.map(categoryId => ({
        category_id: categoryId,
        limit_amount: parseFloat(categoryLimits[categoryId]) || 0,
      }));
      
      const budgetId = await RealBudgetService.createBudget(
        db,
        {
          ...budgetData,
          total_limit: parseFloat(budgetData.total_limit),
          alerts: alertSettings,
        },
        categoryLimitsArray
      );
      
      Alert.alert(
        'Başarılı',
        `"${budgetData.name}" bütçesi oluşturuldu!`,
        [
          {
            text: 'Tamam',
            onPress: () => navigation.navigate('BudgetDetailScreen', { budgetId })
          }
        ]
      );
      
    } catch (error) {
      throw error;
    }
  };

  /**
   * Handles category limit change
   */
  const handleCategoryLimitChange = useCallback((categoryId, value) => {
    setCategoryLimits(prev => ({
      ...prev,
      [categoryId]: value
    }));
  }, []);

  /**
   * Toggles category selection
   */
  const toggleCategorySelection = useCallback((categoryId) => {
    setSelectedCategories(prev => {
      const isSelected = prev.includes(categoryId);
      if (isSelected) {
        // Remove category
        setCategoryLimits(prevLimits => {
          const newLimits = { ...prevLimits };
          delete newLimits[categoryId];
          return newLimits;
        });
        return prev.filter(id => id !== categoryId);
      } else {
        // Add category
        return [...prev, categoryId];
      }
    });
  }, []);

  /**
   * Auto-calculates category limits based on equal distribution
   */
  const autoCalculateLimits = useCallback(() => {
    const totalBudget = parseFloat(budgetData.total_limit) || 0;
    const categoryCount = selectedCategories.length;
    
    if (totalBudget > 0 && categoryCount > 0) {
      const limitPerCategory = totalBudget / categoryCount;
      const newLimits = {};
      
      selectedCategories.forEach(categoryId => {
        newLimits[categoryId] = limitPerCategory.toFixed(2);
      });
      
      setCategoryLimits(newLimits);
    }
  }, [budgetData.total_limit, selectedCategories]);

  /**
   * Date picker handler
   */
  const handleDateChange = (event, selectedDate) => {
    setShowDatePicker(false);
    
    if (selectedDate) {
      setBudgetData(prev => ({
        ...prev,
        [datePickerMode === 'start' ? 'start_date' : 'end_date']: selectedDate
      }));
    }
  };

  /**
   * Load initial data on mount
   */
  useEffect(() => {
    loadInitialData();
  }, [loadInitialData]);

  /**
   * Format currency display
   */
  const formatCurrency = (currency) => {
    const symbols = { TRY: '₺', USD: '$', EUR: '€' };
    return symbols[currency] || currency;
  };

  /**
   * Gets step content
   */
  const getStepContent = () => {
    switch (step) {
      case 1:
        return renderBasicInfoStep();
      case 2:
        return renderTemplatesStep();
      case 3:
        return renderCategoriesStep();
      case 4:
        return renderSettingsStep();
      default:
        return null;
    }
  };

  /**
   * Renders basic info step
   */
  const renderBasicInfoStep = () => (
    <View style={styles.stepContent}>
      <Text style={[styles.stepTitle, { color: theme.TEXT_PRIMARY }]}>
        Temel Bilgiler
      </Text>
      
      {/* Budget Name */}
      <View style={styles.inputGroup}>
        <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
          Bütçe Adı *
        </Text>
        <TextInput
          style={[styles.textInput, { backgroundColor: theme.SURFACE, borderColor: theme.BORDER, color: theme.TEXT_PRIMARY }]}
          value={budgetData.name}
          onChangeText={(text) => setBudgetData(prev => ({ ...prev, name: text }))}
          placeholder="Aylık bütçem"
          placeholderTextColor={theme.TEXT_DISABLED}
        />
      </View>
      
      {/* Description */}
      <View style={styles.inputGroup}>
        <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
          Açıklama
        </Text>
        <TextInput
          style={[styles.textInput, { backgroundColor: theme.SURFACE, borderColor: theme.BORDER, color: theme.TEXT_PRIMARY }]}
          value={budgetData.description}
          onChangeText={(text) => setBudgetData(prev => ({ ...prev, description: text }))}
          placeholder="Bütçe açıklaması (isteğe bağlı)"
          placeholderTextColor={theme.TEXT_DISABLED}
          multiline
          numberOfLines={2}
        />
      </View>
      
      {/* Total Limit */}
      <View style={styles.inputGroup}>
        <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
          Toplam Bütçe Limiti *
        </Text>
        <View style={[styles.amountContainer, { backgroundColor: theme.SURFACE, borderColor: theme.BORDER }]}>
          <TextInput
            style={[styles.amountInput, { color: theme.TEXT_PRIMARY }]}
            value={budgetData.total_limit}
            onChangeText={(text) => setBudgetData(prev => ({ ...prev, total_limit: text }))}
            placeholder="0.00"
            placeholderTextColor={theme.TEXT_DISABLED}
            keyboardType="numeric"
          />
          <Text style={[styles.currencySymbol, { color: theme.TEXT_SECONDARY }]}>
            {formatCurrency(budgetData.currency)}
          </Text>
        </View>
      </View>
      
      {/* Currency Selection */}
      <View style={styles.inputGroup}>
        <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
          Para Birimi
        </Text>
        <View style={styles.currencyButtons}>
          {['TRY', 'USD', 'EUR'].map(currency => (
            <TouchableOpacity
              key={currency}
              style={[
                styles.currencyButton,
                {
                  backgroundColor: budgetData.currency === currency ? theme.PRIMARY : theme.SURFACE,
                  borderColor: budgetData.currency === currency ? theme.PRIMARY : theme.BORDER,
                }
              ]}
              onPress={() => setBudgetData(prev => ({ ...prev, currency }))}
            >
              <Text style={[
                styles.currencyButtonText,
                { color: budgetData.currency === currency ? theme.ON_PRIMARY : theme.TEXT_PRIMARY }
              ]}>
                {currency} {formatCurrency(currency)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
      
      {/* Period Type */}
      <View style={styles.inputGroup}>
        <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
          Dönem Türü
        </Text>
        <View style={styles.periodButtons}>
          {[
            { key: 'weekly', label: 'Haftalık' },
            { key: 'monthly', label: 'Aylık' },
            { key: 'custom', label: 'Özel' }
          ].map(period => (
            <TouchableOpacity
              key={period.key}
              style={[
                styles.periodButton,
                {
                  backgroundColor: budgetData.period_type === period.key ? theme.PRIMARY : theme.SURFACE,
                  borderColor: budgetData.period_type === period.key ? theme.PRIMARY : theme.BORDER,
                }
              ]}
              onPress={() => setBudgetData(prev => ({ ...prev, period_type: period.key }))}
            >
              <Text style={[
                styles.periodButtonText,
                { color: budgetData.period_type === period.key ? theme.ON_PRIMARY : theme.TEXT_PRIMARY }
              ]}>
                {period.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
      
      {/* Date Range */}
      <View style={styles.inputGroup}>
        <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
          Başlangıç Tarihi
        </Text>
        <TouchableOpacity
          style={[styles.dateButton, { backgroundColor: theme.SURFACE, borderColor: theme.BORDER }]}
          onPress={() => {
            setDatePickerMode('start');
            setShowDatePicker(true);
          }}
        >
          <Text style={[styles.dateButtonText, { color: theme.TEXT_PRIMARY }]}>
            {budgetData.start_date.toLocaleDateString('tr-TR')}
          </Text>
          <Ionicons name="calendar-outline" size={20} color={theme.TEXT_SECONDARY} />
        </TouchableOpacity>
      </View>
      
      {budgetData.period_type === 'custom' && (
        <View style={styles.inputGroup}>
          <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
            Bitiş Tarihi
          </Text>
          <TouchableOpacity
            style={[styles.dateButton, { backgroundColor: theme.SURFACE, borderColor: theme.BORDER }]}
            onPress={() => {
              setDatePickerMode('end');
              setShowDatePicker(true);
            }}
          >
            <Text style={[styles.dateButtonText, { color: theme.TEXT_PRIMARY }]}>
              {budgetData.end_date ? budgetData.end_date.toLocaleDateString('tr-TR') : 'Bitiş tarihi seçin'}
            </Text>
            <Ionicons name="calendar-outline" size={20} color={theme.TEXT_SECONDARY} />
          </TouchableOpacity>
        </View>
      )}
    </View>
  );

  /**
   * Renders templates step
   */
  const renderTemplatesStep = () => (
    <View style={styles.stepContent}>
      <Text style={[styles.stepTitle, { color: theme.TEXT_PRIMARY }]}>
        Bütçe Şablonları
      </Text>
      <Text style={[styles.stepDescription, { color: theme.TEXT_SECONDARY }]}>
        Hızlı başlamak için bir şablon seçebilir veya boş başlayabilirsiniz.
      </Text>
      
      {/* No Template Option */}
      <TouchableOpacity
        style={[
          styles.templateCard,
          {
            backgroundColor: !selectedTemplate ? theme.PRIMARY : theme.SURFACE,
            borderColor: !selectedTemplate ? theme.PRIMARY : theme.BORDER,
          }
        ]}
        onPress={() => setSelectedTemplate(null)}
      >
        <Text style={[
          styles.templateName,
          { color: !selectedTemplate ? theme.ON_PRIMARY : theme.TEXT_PRIMARY }
        ]}>
          Şablon Kullanma
        </Text>
        <Text style={[
          styles.templateDescription,
          { color: !selectedTemplate ? theme.ON_PRIMARY : theme.TEXT_SECONDARY }
        ]}>
          Kategorileri ve limitleri kendiniz belirleyin
        </Text>
      </TouchableOpacity>
      
      {/* Template Options */}
      {templates.map(template => (
        <TouchableOpacity
          key={template.id}
          style={[
            styles.templateCard,
            {
              backgroundColor: selectedTemplate?.id === template.id ? theme.PRIMARY : theme.SURFACE,
              borderColor: selectedTemplate?.id === template.id ? theme.PRIMARY : theme.BORDER,
            }
          ]}
          onPress={() => handleTemplateSelect(template)}
        >
          <View style={styles.templateHeader}>
            <Text style={[
              styles.templateName,
              { color: selectedTemplate?.id === template.id ? theme.ON_PRIMARY : theme.TEXT_PRIMARY }
            ]}>
              {template.name}
            </Text>
            {selectedTemplate?.id === template.id && (
              <Ionicons name="checkmark-circle" size={20} color={theme.SUCCESS} />
            )}
          </View>
          <Text style={[
            styles.templateDescription,
            { color: selectedTemplate?.id === template.id ? theme.ON_PRIMARY : theme.TEXT_SECONDARY }
          ]}>
            {template.description}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  /**
   * Renders categories step
   */
  const renderCategoriesStep = () => (
    <View style={styles.stepContent}>
      <Text style={[styles.stepTitle, { color: theme.TEXT_PRIMARY }]}>
        Kategori Limitleri
      </Text>
      <Text style={[styles.stepDescription, { color: theme.TEXT_SECONDARY }]}>
        Her kategori için harcama limiti belirleyin.
      </Text>
      
      {/* Auto Calculate Button */}
      {selectedCategories.length > 0 && (
        <TouchableOpacity
          style={[styles.autoCalculateButton, { backgroundColor: theme.PRIMARY }]}
          onPress={autoCalculateLimits}
        >
          <Ionicons name="calculator-outline" size={20} color={theme.ON_PRIMARY} />
          <Text style={[styles.autoCalculateText, { color: theme.ON_PRIMARY }]}>
            Eşit Dağıt
          </Text>
        </TouchableOpacity>
      )}
      
      {categoriesLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.PRIMARY} />
          <Text style={[styles.loadingText, { color: theme.TEXT_SECONDARY }]}>
            Kategoriler yükleniyor...
          </Text>
        </View>
      ) : (
        <View style={styles.categoriesList}>
          {categories.map(category => {
            const isSelected = selectedCategories.includes(category.id);
            const limit = categoryLimits[category.id] || '';
            
            return (
              <View key={category.id} style={[styles.categoryItem, { backgroundColor: theme.SURFACE }]}>
                <TouchableOpacity
                  style={styles.categorySelectButton}
                  onPress={() => toggleCategorySelection(category.id)}
                >
                  <View style={styles.categoryInfo}>
                    <Text style={[styles.categoryName, { color: theme.TEXT_PRIMARY }]}>
                      {category.name}
                    </Text>
                    {category.description && (
                      <Text style={[styles.categoryDescription, { color: theme.TEXT_SECONDARY }]}>
                        {category.description}
                      </Text>
                    )}
                  </View>
                  
                  <Ionicons
                    name={isSelected ? 'checkbox' : 'square-outline'}
                    size={24}
                    color={isSelected ? theme.PRIMARY : theme.TEXT_DISABLED}
                  />
                </TouchableOpacity>
                
                {isSelected && (
                  <View style={styles.limitInputContainer}>
                    <TextInput
                      style={[
                        styles.limitInput,
                        { backgroundColor: theme.BACKGROUND, borderColor: theme.BORDER, color: theme.TEXT_PRIMARY }
                      ]}
                      value={limit}
                      onChangeText={(text) => handleCategoryLimitChange(category.id, text)}
                      placeholder="0.00"
                      placeholderTextColor={theme.TEXT_DISABLED}
                      keyboardType="numeric"
                    />
                    <Text style={[styles.limitCurrency, { color: theme.TEXT_SECONDARY }]}>
                      {formatCurrency(budgetData.currency)}
                    </Text>
                  </View>
                )}
              </View>
            );
          })}
        </View>
      )}
      
      {/* Summary */}
      {selectedCategories.length > 0 && (
        <View style={[styles.summaryCard, { backgroundColor: theme.SURFACE }]}>
          <Text style={[styles.summaryTitle, { color: theme.TEXT_PRIMARY }]}>
            Özet
          </Text>
          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>
              Seçilen Kategoriler:
            </Text>
            <Text style={[styles.summaryValue, { color: theme.TEXT_PRIMARY }]}>
              {selectedCategories.length}
            </Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>
              Toplam Limit:
            </Text>
            <Text style={[styles.summaryValue, { color: theme.TEXT_PRIMARY }]}>
              {Object.values(categoryLimits).reduce((sum, limit) => sum + (parseFloat(limit) || 0), 0).toLocaleString('tr-TR')} {formatCurrency(budgetData.currency)}
            </Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>
              Bütçe Limiti:
            </Text>
            <Text style={[styles.summaryValue, { color: theme.TEXT_PRIMARY }]}>
              {parseFloat(budgetData.total_limit || 0).toLocaleString('tr-TR')} {formatCurrency(budgetData.currency)}
            </Text>
          </View>
        </View>
      )}
    </View>
  );

  /**
   * Renders settings step
   */
  const renderSettingsStep = () => (
    <View style={styles.stepContent}>
      <Text style={[styles.stepTitle, { color: theme.TEXT_PRIMARY }]}>
        Bildirim Ayarları
      </Text>
      <Text style={[styles.stepDescription, { color: theme.TEXT_SECONDARY }]}>
        Hangi durumlarda bildirim almak istediğinizi seçin.
      </Text>
      
      {Object.entries({
        threshold_75: '%75 limitine ulaşıldığında',
        threshold_90: '%90 limitine ulaşıldığında',
        threshold_100: 'Limit aşıldığında',
        category_limit_exceeded: 'Kategori limiti aşıldığında',
        daily_limit_exceeded: 'Günlük limit aşıldığında',
        weekly_summary: 'Haftalık özet (isteğe bağlı)',
      }).map(([key, label]) => (
        <TouchableOpacity
          key={key}
          style={[styles.settingItem, { backgroundColor: theme.SURFACE }]}
          onPress={() => setAlertSettings(prev => ({ ...prev, [key]: !prev[key] }))}
        >
          <Text style={[styles.settingLabel, { color: theme.TEXT_PRIMARY }]}>
            {label}
          </Text>
          <Ionicons
            name={alertSettings[key] ? 'toggle' : 'toggle-outline'}
            size={32}
            color={alertSettings[key] ? theme.PRIMARY : theme.TEXT_DISABLED}
          />
        </TouchableOpacity>
      ))}
      
      {/* Auto Renew Option */}
      <TouchableOpacity
        style={[styles.settingItem, { backgroundColor: theme.SURFACE, marginTop: 16 }]}
        onPress={() => setBudgetData(prev => ({ ...prev, auto_renew: !prev.auto_renew }))}
      >
        <View>
          <Text style={[styles.settingLabel, { color: theme.TEXT_PRIMARY }]}>
            Otomatik Yenileme
          </Text>
          <Text style={[styles.settingDescription, { color: theme.TEXT_SECONDARY }]}>
            Dönem bittiğinde bütçeyi otomatik olarak yenile
          </Text>
        </View>
        <Ionicons
          name={budgetData.auto_renew ? 'toggle' : 'toggle-outline'}
          size={32}
          color={budgetData.auto_renew ? theme.PRIMARY : theme.TEXT_DISABLED}
        />
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.PRIMARY }]}>
        <TouchableOpacity
          onPress={() => step > 1 ? setStep(step - 1) : navigation.goBack()}
          style={styles.backButton}
        >
          <Ionicons name="arrow-back" size={24} color={theme.ON_PRIMARY} />
        </TouchableOpacity>
        
        <Text style={[styles.headerTitle, { color: theme.ON_PRIMARY }]}>
          {step === 1 && 'Temel Bilgiler'}
          {step === 2 && 'Şablon Seçimi'}
          {step === 3 && 'Kategori Limitleri'}
          {step === 4 && 'Ayarlar'}
        </Text>
        
        <View style={styles.headerRight}>
          <Text style={[styles.stepIndicator, { color: theme.ON_PRIMARY }]}>
            {step}/4
          </Text>
        </View>
      </View>

      {/* Progress Bar */}
      <View style={[styles.progressContainer, { backgroundColor: theme.SURFACE }]}>
        <View
          style={[
            styles.progressBar,
            { backgroundColor: theme.PRIMARY, width: `${(step / 4) * 100}%` }
          ]}
        />
      </View>

      {/* Content */}
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.content}
      >
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {getStepContent()}
        </ScrollView>

        {/* Bottom Actions */}
        <View style={[styles.bottomActions, { backgroundColor: theme.SURFACE }]}>
          {step < 4 ? (
            <TouchableOpacity
              style={[styles.nextButton, { backgroundColor: theme.PRIMARY }]}
              onPress={() => setStep(step + 1)}
              disabled={
                (step === 1 && (!budgetData.name.trim() || !budgetData.total_limit)) ||
                (step === 3 && selectedCategories.length === 0)
              }
            >
              <Text style={[styles.nextButtonText, { color: theme.ON_PRIMARY }]}>
                Devam Et
              </Text>
              <Ionicons name="arrow-forward" size={20} color={theme.ON_PRIMARY} />
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={[styles.createButton, { backgroundColor: theme.SUCCESS }]}
              onPress={handleCreateBudget}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator size="small" color={theme.ON_PRIMARY} />
              ) : (
                <>
                  <Text style={[styles.createButtonText, { color: theme.ON_PRIMARY }]}>
                    Bütçe Oluştur
                  </Text>
                  <Ionicons name="checkmark" size={20} color={theme.ON_PRIMARY} />
                </>
              )}
            </TouchableOpacity>
          )}
        </View>
      </KeyboardAvoidingView>

      {/* Date Picker */}
      {showDatePicker && (
        <DateTimePicker
          value={datePickerMode === 'start' ? budgetData.start_date : (budgetData.end_date || new Date())}
          mode="date"
          display="default"
          onChange={handleDateChange}
          minimumDate={datePickerMode === 'end' ? budgetData.start_date : new Date()}
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
    textAlign: 'center',
  },
  headerRight: {
    minWidth: 40,
    alignItems: 'flex-end',
  },
  stepIndicator: {
    fontSize: 14,
    fontWeight: '600',
  },
  progressContainer: {
    height: 4,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
  },
  content: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  stepContent: {
    gap: 20,
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  stepDescription: {
    fontSize: 16,
    lineHeight: 22,
  },
  inputGroup: {
    gap: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
  },
  amountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  amountInput: {
    flex: 1,
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'right',
  },
  currencySymbol: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  currencyButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  currencyButton: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  currencyButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  periodButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  periodButton: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  periodButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  dateButtonText: {
    fontSize: 16,
  },
  templateCard: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    gap: 8,
  },
  templateHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  templateName: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  templateDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  autoCalculateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  autoCalculateText: {
    fontSize: 16,
    fontWeight: '600',
  },
  loadingContainer: {
    alignItems: 'center',
    padding: 32,
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
  },
  categoriesList: {
    gap: 8,
  },
  categoryItem: {
    borderRadius: 8,
    padding: 12,
    gap: 8,
  },
  categorySelectButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '600',
  },
  categoryDescription: {
    fontSize: 14,
    marginTop: 2,
  },
  limitInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginTop: 8,
  },
  limitInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
    textAlign: 'right',
  },
  limitCurrency: {
    fontSize: 14,
    fontWeight: '600',
  },
  summaryCard: {
    borderRadius: 8,
    padding: 16,
    gap: 8,
    marginTop: 16,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 14,
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderRadius: 8,
    padding: 16,
  },
  settingLabel: {
    fontSize: 16,
    fontWeight: '600',
  },
  settingDescription: {
    fontSize: 14,
    marginTop: 2,
  },
  bottomActions: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 8,
    gap: 8,
  },
  nextButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  createButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 8,
    gap: 8,
  },
  createButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
});
