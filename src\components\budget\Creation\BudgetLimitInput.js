/**
 * Bütçe Limit Girişi Komponenti
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md - Stage 2, Phase 1
 * 
 * Bütçe limit miktarı girişi
 * Maksimum 200 satır - Tek sorumluluk prensibi
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../../../context/ThemeContext';
import { formatTRY, formatUSD, formatEUR } from '../../../utils/formatters';

/**
 * Bütçe limit girişi komponenti
 * @param {Object} props - Component props
 * @param {number} props.amount - Mevcut miktar
 * @param {string} props.currency - Seçili para birimi ('TRY', 'USD', 'EUR')
 * @param {Function} props.onAmountChange - Mi<PERSON>ar değişim callback fonksiyonu
 * @param {Function} props.onCurrencyChange - Para birimi değişim callback fonksiyonu
 * @param {Object} props.theme - Tema objesi (opsiyonel, context'ten alınır)
 */
const BudgetLimitInput = ({ 
  amount, 
  currency, 
  onAmountChange, 
  onCurrencyChange, 
  theme: propTheme 
}) => {
  const { theme } = useTheme();
  const currentTheme = propTheme || theme;

  // State yönetimi
  const [inputValue, setInputValue] = useState(amount ? amount.toString() : '');
  const [isFocused, setIsFocused] = useState(false);

  // Para birimleri tanımları
  const currencies = [
    { code: 'TRY', symbol: '₺', name: 'Türk Lirası' },
    { code: 'USD', symbol: '$', name: 'Amerikan Doları' },
    { code: 'EUR', symbol: '€', name: 'Euro' }
  ];

  // Amount prop değiştiğinde input'u güncelle
  useEffect(() => {
    if (amount !== undefined && amount !== null) {
      setInputValue(amount.toString());
    }
  }, [amount]);

  /**
   * Miktar değişim işleyicisi
   * @param {string} text - Girilen metin
   */
  const handleAmountChange = (text) => {
    // Sadece sayı ve nokta karakterlerine izin ver
    const cleanText = text.replace(/[^0-9.,]/g, '');
    
    // Virgülü noktaya çevir (Türkçe klavye desteği)
    const normalizedText = cleanText.replace(',', '.');
    
    // Birden fazla nokta kontrolü
    const dotCount = (normalizedText.match(/\./g) || []).length;
    if (dotCount > 1) {
      return;
    }

    setInputValue(normalizedText);

    // Geçerli sayı kontrolü ve callback çağırma
    const numericValue = parseFloat(normalizedText);
    if (!isNaN(numericValue) && numericValue >= 0) {
      if (onAmountChange) {
        onAmountChange(numericValue);
      }
    } else if (normalizedText === '' || normalizedText === '.') {
      if (onAmountChange) {
        onAmountChange(0);
      }
    }
  };

  /**
   * Para birimi değişim işleyicisi
   * @param {string} currencyCode - Seçilen para birimi kodu
   */
  const handleCurrencyChange = (currencyCode) => {
    if (onCurrencyChange) {
      onCurrencyChange(currencyCode);
    }
  };

  /**
   * Hızlı miktar seçim işleyicisi
   * @param {number} quickAmount - Hızlı seçim miktarı
   */
  const handleQuickAmountSelect = (quickAmount) => {
    setInputValue(quickAmount.toString());
    if (onAmountChange) {
      onAmountChange(quickAmount);
    }
  };

  /**
   * Para birimi formatı
   * @param {number} value - Formatlanacak değer
   * @param {string} currencyCode - Para birimi kodu
   * @returns {string} Formatlanmış değer
   */
  const formatCurrency = (value, currencyCode) => {
    switch (currencyCode) {
      case 'USD':
        return formatUSD(value);
      case 'EUR':
        return formatEUR(value);
      default:
        return formatTRY(value);
    }
  };

  // Seçili para birimi bilgisi
  const selectedCurrency = currencies.find(c => c.code === currency) || currencies[0];

  // Hızlı seçim miktarları (para birimine göre)
  const getQuickAmounts = () => {
    switch (currency) {
      case 'USD':
        return [100, 250, 500, 1000];
      case 'EUR':
        return [100, 250, 500, 1000];
      default: // TRY
        return [1000, 2500, 5000, 10000];
    }
  };

  const quickAmounts = getQuickAmounts();

  return (
    <View style={[styles.container, { backgroundColor: currentTheme.BACKGROUND }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: currentTheme.TEXT_PRIMARY }]}>
          Bütçe Limitini Belirleyin
        </Text>
        <Text style={[styles.subtitle, { color: currentTheme.TEXT_SECONDARY }]}>
          Toplam harcama limitinizi girin
        </Text>
      </View>

      {/* Ana miktar girişi */}
      <View style={[
        styles.inputContainer,
        {
          backgroundColor: currentTheme.SURFACE,
          borderColor: isFocused ? currentTheme.PRIMARY : currentTheme.BORDER,
        }
      ]}>
        <View style={styles.currencySection}>
          <Text style={[styles.currencySymbol, { color: currentTheme.PRIMARY }]}>
            {selectedCurrency.symbol}
          </Text>
        </View>

        <TextInput
          style={[styles.amountInput, { color: currentTheme.TEXT_PRIMARY }]}
          value={inputValue}
          onChangeText={handleAmountChange}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholder="0"
          placeholderTextColor={currentTheme.TEXT_SECONDARY}
          keyboardType="numeric"
          maxLength={10}
        />

        <TouchableOpacity
          style={styles.currencyButton}
          onPress={() => {
            // Para birimi seçim modalı açılabilir
            Alert.alert(
              'Para Birimi Seçin',
              'Hangi para birimini kullanmak istiyorsunuz?',
              currencies.map(curr => ({
                text: `${curr.symbol} ${curr.name}`,
                onPress: () => handleCurrencyChange(curr.code)
              }))
            );
          }}
        >
          <Text style={[styles.currencyCode, { color: currentTheme.TEXT_PRIMARY }]}>
            {selectedCurrency.code}
          </Text>
          <MaterialIcons name="expand-more" size={20} color={currentTheme.TEXT_SECONDARY} />
        </TouchableOpacity>
      </View>

      {/* Formatlanmış değer gösterimi */}
      {amount > 0 && (
        <View style={styles.formattedValue}>
          <Text style={[styles.formattedText, { color: currentTheme.TEXT_SECONDARY }]}>
            {formatCurrency(amount, currency)}
          </Text>
        </View>
      )}

      {/* Hızlı seçim butonları */}
      <View style={styles.quickSelection}>
        <Text style={[styles.quickTitle, { color: currentTheme.TEXT_SECONDARY }]}>
          Hızlı Seçim:
        </Text>
        <View style={styles.quickButtons}>
          {quickAmounts.map((quickAmount) => (
            <TouchableOpacity
              key={quickAmount}
              style={[
                styles.quickButton,
                {
                  backgroundColor: amount === quickAmount 
                    ? currentTheme.PRIMARY 
                    : currentTheme.SURFACE,
                  borderColor: currentTheme.BORDER,
                }
              ]}
              onPress={() => handleQuickAmountSelect(quickAmount)}
            >
              <Text style={[
                styles.quickButtonText,
                {
                  color: amount === quickAmount 
                    ? currentTheme.WHITE 
                    : currentTheme.TEXT_PRIMARY
                }
              ]}>
                {selectedCurrency.symbol}{quickAmount.toLocaleString('tr-TR')}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Bilgilendirme */}
      <View style={[styles.infoCard, { backgroundColor: currentTheme.INFO + '20' }]}>
        <MaterialIcons name="info" size={16} color={currentTheme.INFO} />
        <Text style={[styles.infoText, { color: currentTheme.TEXT_SECONDARY }]}>
          Bu limit, seçtiğiniz dönem boyunca toplam harcamanızın üst sınırıdır.
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
  },
  header: {
    paddingVertical: 16,
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    borderWidth: 2,
    marginBottom: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  currencySection: {
    marginRight: 8,
  },
  currencySymbol: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  amountInput: {
    flex: 1,
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    paddingVertical: 8,
  },
  currencyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 8,
    gap: 4,
  },
  currencyCode: {
    fontSize: 16,
    fontWeight: '500',
  },
  formattedValue: {
    alignItems: 'center',
    marginBottom: 16,
  },
  formattedText: {
    fontSize: 14,
  },
  quickSelection: {
    marginBottom: 16,
  },
  quickTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  quickButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  quickButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
  },
  quickButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  infoCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  infoText: {
    fontSize: 13,
    flex: 1,
    lineHeight: 18,
  },
});

export default BudgetLimitInput;
