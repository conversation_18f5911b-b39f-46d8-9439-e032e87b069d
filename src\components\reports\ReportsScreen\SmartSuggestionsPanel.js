import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from 'react-native';

/**
 * Smart Suggestions Panel Component
 * AI-powered report suggestions and recommendations
 * Follows REPORTS_SCREEN_REDESIGN_PLAN.md specifications
 */
const SmartSuggestionsPanel = ({ 
  suggestions = [],
  onSuggestionPress,
  onDismissSuggestion,
  onRefreshSuggestions,
  theme,
  isLoading = false
}) => {
  // Default suggestions if none provided
  const defaultSuggestions = [
    {
      id: 'monthly-analysis',
      title: 'Aylık Harcama <PERSON>zi',
      description: 'Bu ay harcamalarınızda %15 artış var. Detaylı analiz için rapor oluşturun.',
      type: 'insight',
      icon: '📈',
      priority: 'high',
      action: 'create_monthly_report',
    },
    {
      id: 'category-breakdown',
      title: 'Kategori <PERSON>ılımı',
      description: 'Gıda kategorisinde bütçenizi %20 aştınız. Kategori analizi yapın.',
      type: 'warning',
      icon: '⚠️',
      priority: 'medium',
      action: 'create_category_report',
    },
    {
      id: 'savings-opportunity',
      title: '<PERSON><PERSON><PERSON><PERSON>',
      description: 'Ulaşım harcamalarınızı optimize ederek aylık 500₺ tasarruf edebilirsiniz.',
      type: 'opportunity',
      icon: '💡',
      priority: 'medium',
      action: 'create_savings_report',
    },
    {
      id: 'budget-review',
      title: 'Bütçe Gözden Geçirme',
      description: 'Yıllık bütçe hedeflerinize %85 ulaştınız. İlerleme raporu oluşturun.',
      type: 'success',
      icon: '🎯',
      priority: 'low',
      action: 'create_budget_report',
    },
  ];

  const displaySuggestions = suggestions.length > 0 ? suggestions : defaultSuggestions;

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return theme.ERROR;
      case 'medium': return theme.WARNING;
      case 'low': return theme.SUCCESS;
      default: return theme.INFO;
    }
  };

  const getTypeColor = (type) => {
    switch (type) {
      case 'warning': return theme.ERROR;
      case 'opportunity': return theme.SUCCESS;
      case 'insight': return theme.INFO;
      case 'success': return theme.SUCCESS;
      default: return theme.PRIMARY;
    }
  };

  if (isLoading) {
    return (
      <View style={[styles.container, { backgroundColor: theme.SURFACE }]}>
        <Text style={[styles.loadingText, { color: theme.TEXT_SECONDARY }]}>
          Akıllı öneriler yükleniyor...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.SURFACE }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
          🤖 Akıllı Öneriler
        </Text>
        
        <TouchableOpacity 
          style={[styles.refreshButton, { backgroundColor: theme.BACKGROUND }]}
          onPress={onRefreshSuggestions}
        >
          <Text style={[styles.refreshButtonText, { color: theme.TEXT_PRIMARY }]}>
            🔄
          </Text>
        </TouchableOpacity>
      </View>

      {/* Suggestions List */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.suggestionsScrollView}
      >
        {displaySuggestions.map((suggestion) => (
          <TouchableOpacity
            key={suggestion.id}
            style={[
              styles.suggestionCard,
              { 
                backgroundColor: theme.BACKGROUND,
                borderLeftColor: getTypeColor(suggestion.type),
              }
            ]}
            onPress={() => onSuggestionPress?.(suggestion)}
            activeOpacity={0.8}
          >
            {/* Priority Indicator */}
            <View style={[
              styles.priorityIndicator,
              { backgroundColor: getPriorityColor(suggestion.priority) }
            ]} />

            {/* Dismiss Button */}
            <TouchableOpacity
              style={styles.dismissButton}
              onPress={() => onDismissSuggestion?.(suggestion.id)}
            >
              <Text style={[styles.dismissButtonText, { color: theme.TEXT_SECONDARY }]}>
                ×
              </Text>
            </TouchableOpacity>

            {/* Content */}
            <View style={styles.suggestionContent}>
              <Text style={[styles.suggestionIcon, { color: getTypeColor(suggestion.type) }]}>
                {suggestion.icon}
              </Text>
              
              <Text style={[styles.suggestionTitle, { color: theme.TEXT_PRIMARY }]}>
                {suggestion.title}
              </Text>
              
              <Text style={[styles.suggestionDescription, { color: theme.TEXT_SECONDARY }]}>
                {suggestion.description}
              </Text>

              {/* Action Button */}
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: getTypeColor(suggestion.type) }]}
                onPress={() => onSuggestionPress?.(suggestion)}
              >
                <Text style={[styles.actionButtonText, { color: theme.SURFACE }]}>
                  Rapor Oluştur
                </Text>
              </TouchableOpacity>
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Footer */}
      <View style={styles.footer}>
        <Text style={[styles.footerText, { color: theme.TEXT_SECONDARY }]}>
          💡 Öneriler finansal verileriniz analiz edilerek oluşturulur
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    paddingBottom: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  refreshButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  refreshButtonText: {
    fontSize: 16,
  },
  loadingText: {
    textAlign: 'center',
    padding: 40,
    fontSize: 16,
  },
  suggestionsScrollView: {
    paddingHorizontal: 16,
    marginBottom: 12,
  },
  suggestionCard: {
    width: 280,
    borderRadius: 12,
    padding: 16,
    marginRight: 12,
    borderLeftWidth: 4,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    position: 'relative',
  },
  priorityIndicator: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  dismissButton: {
    position: 'absolute',
    top: 8,
    right: 24,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  dismissButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  suggestionContent: {
    paddingRight: 32,
  },
  suggestionIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  suggestionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  suggestionDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
  },
  actionButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignSelf: 'flex-start',
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: '600',
  },
  footer: {
    padding: 16,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  footerText: {
    fontSize: 12,
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

export default SmartSuggestionsPanel;
