import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../../constants/colors';

/**
 * Optimize edilmiş form durumu bileşeni
 * Daha basit ve daha hızlı render
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.formStatus - Form durumu bilgisi
 * @param {boolean} props.isSubmitting - Form gönderilme durumu
 * @param {string} props.error - Hata mesajı
 * @param {boolean} props.isFormValid - Form geçerli mi?
 * @param {boolean} props.isEditMode - Düzenleme modu mu?
 * @returns {JSX.Element} Form durumu bileşeni
 */
const RegularIncomeFormStatus = React.memo(({ formStatus, isEditMode, isSubmitting, error, isFormValid }) => {
  // Eğer formStatus prop olarak geldiyse onu kullan, yoksa duruma göre hesapla
  const status = formStatus || (() => {
    if (isSubmitting) {
      return {
        color: Colors.PRIMARY,
        icon: 'hourglass-top',
        text: isEditMode ? 'Güncelleniyor...' : 'Kaydediliyor...',
        description: 'İşleminiz devam ediyor, lütfen bekleyin.'
      };
    }
    
    if (error) {
      return {
        icon: 'error-outline',
        color: Colors.DANGER,
        text: 'Hatalar Var',
        description: 'Lütfen formdaki hataları düzeltin ve tekrar deneyin.'
      };
    }
    
    if (isFormValid) {
      return {
        icon: 'check-circle-outline',
        color: Colors.SUCCESS,
        text: 'Form Hazır',
        description: isEditMode 
          ? 'Değişikliklerinizi kaydetmek için "Güncelle" tuşuna basın.' 
          : 'Bilgileri kaydetmek için "Kaydet" tuşuna basın.'
      };
    }
    
    return {
      icon: 'edit',
      color: Colors.GRAY_500,
      text: 'Form Düzenleniyor',
      description: 'Lütfen gerekli alanları doldurun.'
    };
  })();
  
  return (
    <View style={[
      styles.container,
      { 
        backgroundColor: status.color + '10',
        borderColor: status.color + '30',
        borderLeftWidth: 4,
        borderLeftColor: status.color
      }
    ]}>
      <View style={styles.content}>
        <MaterialIcons name={status.icon} size={20} color={status.color} />
        <View style={styles.textContainer}>
          <Text style={[styles.title, { color: status.color }]}>
            {status.text}
          </Text>
          <Text style={styles.description}>
            {status.description}
          </Text>
        </View>
      </View>
    </View>
  );
});

// Enhanced styles with Material Design 3.0 principles
const styles = StyleSheet.create({
  container: {
    borderWidth: 1.5,
    borderRadius: 16,
    marginBottom: 20,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    backgroundColor: '#fff',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
  },
  textContainer: {
    flex: 1,
    marginLeft: 16,
  },
  title: {
    fontSize: 17,
    fontWeight: '700',
    marginBottom: 4,
    letterSpacing: 0.5,
  },
  description: {
    fontSize: 14,
    color: Colors.GRAY_600,
    lineHeight: 20,
    letterSpacing: 0.25,
  },
});

export default RegularIncomeFormStatus;
