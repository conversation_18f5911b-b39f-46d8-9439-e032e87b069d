/**
 * Rapor verilerini SQLite veritabanından çeken servis
 * Farklı şablonlar için gerekli verileri hazırlar
 */

import { dbService } from '../db/dbService';

/**
 * Rapor veri servisi
 */
class ReportDataService {
  constructor() {
    this.dbService = dbService;
  }

  /**
   * Aylık gelir-gider verilerini çeker
   */
  async getMonthlyIncomeExpenseData(params) {
    const { dateRange, groupBy, includeCategories, showComparisons } = params;
    
    try {
      // Tarih aralığını hesapla
      const { startDate, endDate } = this.calculateDateRange(dateRange);
      
      // Temel aylık veriler
      const monthlyData = await this.getMonthlyTransactionSummary(startDate, endDate);
      
      // Toplam özet
      const summary = this.calculateMonthlySummary(monthlyData);
      
      // <PERSON><PERSON><PERSON> da<PERSON>ı<PERSON>ı (eğer istenmişse)
      const categoryBreakdown = includeCategories ? 
        await this.getCategoryBreakdown(startDate, endDate) : null;
      
      // Karşılaştırma verileri (eğer istenmişse)
      if (showComparisons) {
        const comparisonData = await this.getComparisonData(startDate, endDate);
        summary.previousTotalIncome = comparisonData.previousTotalIncome;
        summary.previousTotalExpense = comparisonData.previousTotalExpense;
        summary.previousNetAmount = comparisonData.previousNetAmount;
      }
      
      return {
        monthlyData,
        summary,
        categoryBreakdown,
        params,
        generatedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('Aylık gelir-gider verisi çekme hatası:', error);
      // Hata durumunda örnek veri döndür
      return await this.generateSampleData();
    }
  }

  /**
   * Kategori dağılım verilerini çeker
   */
  async getCategoryDistributionData(params) {
    const { dateRange, showPercentages, minimumAmount, excludeTransfers } = params;
    
    try {
      const { startDate, endDate } = this.calculateDateRange(dateRange);
      
      // Kategori bazında toplam tutarları çek
      const categoryTotals = await this.getCategoryTotals(startDate, endDate, excludeTransfers);
      
      // Minimum tutar filtrelemesi
      const filteredCategories = categoryTotals.filter(cat => 
        Math.abs(cat.amount) >= minimumAmount
      );
      
      // Toplam tutarları hesapla
      const totalIncome = filteredCategories
        .filter(cat => cat.type === 'income')
        .reduce((sum, cat) => sum + cat.amount, 0);
      
      const totalExpense = filteredCategories
        .filter(cat => cat.type === 'expense')
        .reduce((sum, cat) => sum + Math.abs(cat.amount), 0);
      
      // Yüzde hesapla (eğer istenmişse)
      if (showPercentages) {
        filteredCategories.forEach(cat => {
          const total = cat.type === 'income' ? totalIncome : totalExpense;
          cat.percentage = total > 0 ? (Math.abs(cat.amount) / total) * 100 : 0;
        });
      }
      
      return {
        categories: filteredCategories,
        totals: {
          totalIncome,
          totalExpense,
          netAmount: totalIncome - totalExpense
        },
        params,
        generatedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('Kategori dağılım verisi çekme hatası:', error);
      return await this.generateSampleCategoryDistributionData();
    }
  }

  /**
   * Tarih aralığını hesapla
   */
  calculateDateRange(dateRange) {
    const now = new Date();
    let startDate, endDate;
    
    switch (dateRange) {
      case 'current_month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        break;
        
      case 'last_month':
        startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        endDate = new Date(now.getFullYear(), now.getMonth(), 0);
        break;
        
      case 'last_3_months':
        startDate = new Date(now.getFullYear(), now.getMonth() - 2, 1);
        endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        break;
        
      case 'last_6_months':
        startDate = new Date(now.getFullYear(), now.getMonth() - 5, 1);
        endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        break;
        
      case 'current_year':
        startDate = new Date(now.getFullYear(), 0, 1);
        endDate = new Date(now.getFullYear() + 1, 0, 0);
        break;
        
      case 'last_year':
        startDate = new Date(now.getFullYear() - 1, 0, 1);
        endDate = new Date(now.getFullYear(), 0, 0);
        break;
        
      default:
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    }
    
    return { startDate, endDate };
  }

  /**
   * Aylık işlem özetini çeker
   */
  async getMonthlyTransactionSummary(startDate, endDate) {
    try {
      const query = `
        SELECT 
          strftime('%Y-%m', date) as month,
          SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END) as income,
          SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END) as expense
        FROM transactions 
        WHERE date BETWEEN ? AND ?
        GROUP BY strftime('%Y-%m', date)
        ORDER BY month
      `;
      
      const results = await dbService.executeSql(query, [
        startDate.toISOString().split('T')[0],
        endDate.toISOString().split('T')[0]
      ]);
      
      return results.map(row => ({
        month: this.formatMonth(row.month),
        income: row.income || 0,
        expense: row.expense || 0,
        net: (row.income || 0) - (row.expense || 0)
      }));
    } catch (error) {
      console.error('Aylık işlem özeti çekme hatası:', error);
      return this.generateSampleMonthlyData();
    }
  }

  /**
   * Kategori dağılımını çeker
   */
  async getCategoryBreakdown(startDate, endDate) {
    try {
      const query = `
        SELECT 
          c.name as category_name,
          c.type as category_type,
          SUM(t.amount) as total_amount
        FROM transactions t
        JOIN categories c ON t.category_id = c.id
        WHERE t.date BETWEEN ? AND ?
        GROUP BY c.id, c.name, c.type
        HAVING ABS(total_amount) > 0
        ORDER BY ABS(total_amount) DESC
      `;
      
      const results = await dbService.executeSql(query, [
        startDate.toISOString().split('T')[0],
        endDate.toISOString().split('T')[0]
      ]);
      
      return results.map(row => ({
        name: row.category_name,
        type: row.category_type,
        amount: Math.abs(row.total_amount)
      }));
    } catch (error) {
      console.error('Kategori dağılımı çekme hatası:', error);
      return this.generateSampleCategoryData();
    }
  }

  /**
   * Kategori toplamlarını çeker
   */
  async getCategoryTotals(startDate, endDate, excludeTransfers = false) {
    try {
      let query = `
        SELECT 
          c.name as category_name,
          c.type as category_type,
          SUM(t.amount) as total_amount
        FROM transactions t
        JOIN categories c ON t.category_id = c.id
        WHERE t.date BETWEEN ? AND ?
      `;
      
      if (excludeTransfers) {
        query += ` AND c.type != 'transfer'`;
      }
      
      query += `
        GROUP BY c.id, c.name, c.type
        HAVING ABS(total_amount) > 0
        ORDER BY ABS(total_amount) DESC
      `;
      
      const results = await dbService.executeSql(query, [
        startDate.toISOString().split('T')[0],
        endDate.toISOString().split('T')[0]
      ]);
      
      return results.map(row => ({
        name: row.category_name,
        type: row.category_type,
        amount: row.total_amount
      }));
    } catch (error) {
      console.error('Kategori toplamları çekme hatası:', error);
      return this.generateSampleCategoryData();
    }
  }

  /**
   * Karşılaştırma verilerini çeker
   */
  async getComparisonData(startDate, endDate) {
    try {
      // Aynı süre kadar önceki dönemi hesapla
      const daysDiff = (endDate - startDate) / (1000 * 60 * 60 * 24);
      const previousEndDate = new Date(startDate);
      previousEndDate.setDate(previousEndDate.getDate() - 1);
      const previousStartDate = new Date(previousEndDate);
      previousStartDate.setDate(previousStartDate.getDate() - daysDiff);
      
      const query = `
        SELECT 
          SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END) as income,
          SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END) as expense
        FROM transactions 
        WHERE date BETWEEN ? AND ?
      `;
      
      const results = await dbService.executeSql(query, [
        previousStartDate.toISOString().split('T')[0],
        previousEndDate.toISOString().split('T')[0]
      ]);
      
      const row = results[0];
      return {
        previousTotalIncome: row?.income || 0,
        previousTotalExpense: row?.expense || 0,
        previousNetAmount: (row?.income || 0) - (row?.expense || 0)
      };
    } catch (error) {
      console.error('Karşılaştırma verisi çekme hatası:', error);
      return {
        previousTotalIncome: 7500,
        previousTotalExpense: 6200,
        previousNetAmount: 1300
      };
    }
  }

  /**
   * Örnek aylık veri oluştur
   */
  generateSampleMonthlyData() {
    const now = new Date();
    const monthsBack = 3;
    
    const monthlyData = [];
    for (let i = monthsBack; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const income = Math.random() * 5000 + 8000; // 8000-13000 arası
      const expense = Math.random() * 4000 + 5000; // 5000-9000 arası
      
      monthlyData.push({
        month: this.formatMonth(date.toISOString().substring(0, 7)),
        income,
        expense,
        net: income - expense
      });
    }
    
    return monthlyData;
  }

  /**
   * Örnek kategori verisi oluştur
   */
  generateSampleCategoryData() {
    return [
      { name: 'Maaş', type: 'income', amount: 8000 },
      { name: 'Freelance', type: 'income', amount: 3000 },
      { name: 'Diğer Gelir', type: 'income', amount: 500 },
      { name: 'Market', type: 'expense', amount: 2500 },
      { name: 'Ulaşım', type: 'expense', amount: 800 },
      { name: 'Eğlence', type: 'expense', amount: 1200 },
      { name: 'Faturalar', type: 'expense', amount: 1800 },
      { name: 'Kişisel Bakım', type: 'expense', amount: 600 },
      { name: 'Giyim', type: 'expense', amount: 900 }
    ];
  }

  /**
   * Örnek kategori dağılım verisi oluştur
   */
  async generateSampleCategoryDistributionData() {
    const categories = this.generateSampleCategoryData();
    
    const totalIncome = categories
      .filter(cat => cat.type === 'income')
      .reduce((sum, cat) => sum + cat.amount, 0);
    
    const totalExpense = categories
      .filter(cat => cat.type === 'expense')
      .reduce((sum, cat) => sum + cat.amount, 0);
    
    return {
      categories,
      totals: {
        totalIncome,
        totalExpense,
        netAmount: totalIncome - totalExpense
      },
      params: {
        dateRange: 'current_month',
        showPercentages: true,
        minimumAmount: 0,
        excludeTransfers: false
      },
      generatedAt: new Date().toISOString()
    };
  }

  /**
   * Aylık özet hesaplama
   */
  calculateMonthlySummary(monthlyData) {
    const totalIncome = monthlyData.reduce((sum, month) => sum + month.income, 0);
    const totalExpense = monthlyData.reduce((sum, month) => sum + month.expense, 0);
    const netAmount = totalIncome - totalExpense;
    
    return {
      totalIncome,
      totalExpense,
      netAmount,
      averageMonthlyIncome: monthlyData.length > 0 ? totalIncome / monthlyData.length : 0,
      averageMonthlyExpense: monthlyData.length > 0 ? totalExpense / monthlyData.length : 0,
      monthCount: monthlyData.length
    };
  }

  /**
   * Ay formatla
   */
  formatMonth(yearMonth) {
    const months = [
      'Ocak', 'Şubat', 'Mart', 'Nisan', 'Mayıs', 'Haziran',
      'Temmuz', 'Ağustos', 'Eylül', 'Ekim', 'Kasım', 'Aralık'
    ];
    
    const [year, month] = yearMonth.split('-');
    const monthIndex = parseInt(month) - 1;
    return `${months[monthIndex]} ${year}`;
  }

  /**
   * Örnek veri oluştur (geliştirme/test amaçlı)
   */
  async generateSampleData() {
    const monthlyData = this.generateSampleMonthlyData();
    const summary = this.calculateMonthlySummary(monthlyData);
    const categoryBreakdown = this.generateSampleCategoryData();
    
    return {
      monthlyData,
      summary,
      categoryBreakdown,
      params: {
        dateRange: 'current_month',
        groupBy: 'month',
        includeCategories: true,
        showComparisons: false
      },
      generatedAt: new Date().toISOString()
    };
  }
}

export const reportDataService = new ReportDataService();
