import { useSQLiteContext } from 'expo-sqlite';

/**
 * Veritabanı işlemleri için yardımcı fonksiyonlar
 */

/**
 * Veritabanı işlemlerini yönetmek için hook
 * @returns {Object} Veritabanı işlemleri için fonksiyonlar
 */
export const useDatabase = () => {
  const db = useSQLiteContext();

  /**
   * Veritabanında bir işlemi gerçekleştirir
   * @param {Function} callback - İşlemi gerçekleştirecek callback fonksiyonu
   * @returns {Promise<any>} İşlem sonucu
   */
  const withTransaction = async (callback) => {
    try {
      await db.execAsync('BEGIN TRANSACTION');
      const result = await callback();
      await db.execAsync('COMMIT');
      return result;
    } catch (error) {
      await db.execAsync('ROLLBACK');
      console.error('Transaction hatası:', error);
      throw error;
    }
  };

  /**
   * Bir tablodan tüm kayıtları getirir
   * @param {string} table - Tablo adı
   * @param {string} orderBy - Sıralama kriteri
   * @returns {Promise<Array>} Kayıtlar
   */
  const getAll = async (table, orderBy = 'id DESC') => {
    return await db.getAllAsync(`SELECT * FROM ${table} ORDER BY ${orderBy}`);
  };

  /**
   * Bir tablodan belirli bir kaydı getirir
   * @param {string} table - Tablo adı
   * @param {number|string} id - Kayıt ID'si
   * @returns {Promise<Object>} Kayıt
   */
  const getById = async (table, id) => {
    return await db.getFirstAsync(`SELECT * FROM ${table} WHERE id = ?`, [id]);
  };

  /**
   * Bir tabloya yeni bir kayıt ekler
   * @param {string} table - Tablo adı
   * @param {Object} data - Eklenecek veri
   * @returns {Promise<number>} Eklenen kaydın ID'si
   */
  const insert = async (table, data) => {
    const keys = Object.keys(data);
    const values = Object.values(data);
    const placeholders = keys.map(() => '?').join(', ');

    const result = await db.runAsync(
      `INSERT INTO ${table} (${keys.join(', ')}) VALUES (${placeholders})`,
      values
    );

    return result.lastInsertRowId;
  };

  /**
   * Bir tablodan bir kaydı günceller
   * @param {string} table - Tablo adı
   * @param {number|string} id - Güncellenecek kaydın ID'si
   * @param {Object} data - Güncellenecek veriler
   * @returns {Promise<number>} Etkilenen satır sayısı
   */
  const update = async (table, id, data) => {
    const keys = Object.keys(data);
    const values = Object.values(data);
    const setClause = keys.map(key => `${key} = ?`).join(', ');

    const result = await db.runAsync(
      `UPDATE ${table} SET ${setClause} WHERE id = ?`,
      [...values, id]
    );

    return result.changes;
  };

  /**
   * Bir tablodan bir kaydı siler
   * @param {string} table - Tablo adı
   * @param {number|string} id - Silinecek kaydın ID'si
   * @returns {Promise<number>} Etkilenen satır sayısı
   */
  const remove = async (table, id) => {
    const result = await db.runAsync(
      `DELETE FROM ${table} WHERE id = ?`,
      [id]
    );

    return result.changes;
  };

  /**
   * Özel bir SQL sorgusu çalıştırır
   * @param {string} query - SQL sorgusu
   * @param {Array} params - Sorgu parametreleri
   * @returns {Promise<Array>} Sorgu sonuçları
   */
  const query = async (query, params = []) => {
    return await db.getAllAsync(query, params);
  };

  /**
   * Özel bir SQL sorgusu çalıştırır ve ilk sonucu döndürür
   * @param {string} query - SQL sorgusu
   * @param {Array} params - Sorgu parametreleri
   * @returns {Promise<Object>} İlk sonuç
   */
  const queryFirst = async (query, params = []) => {
    return await db.getFirstAsync(query, params);
  };

  /**
   * Özel bir SQL sorgusu çalıştırır (INSERT, UPDATE, DELETE)
   * @param {string} query - SQL sorgusu
   * @param {Array} params - Sorgu parametreleri
   * @returns {Promise<Object>} Sorgu sonucu
   */
  const execute = async (query, params = []) => {
    return await db.runAsync(query, params);
  };

  return {
    withTransaction,
    getAll,
    getById,
    insert,
    update,
    remove,
    query,
    queryFirst,
    execute,
    db
  };
};

/**
 * Kategorilerle ilgili veritabanı işlemleri
 */
export const useCategoryService = () => {
  const { getAll, getById, insert, update, remove, query } = useDatabase();

  /**
   * Tüm kategorileri getirir
   * @param {string} type - Kategori tipi (income, expense, all)
   * @returns {Promise<Array>} Kategoriler
   */
  const getCategories = async (type = 'all') => {
    if (type === 'all') {
      return await getAll('categories', 'name ASC');
    }

    return await query(
      'SELECT * FROM categories WHERE type = ? ORDER BY name ASC',
      [type]
    );
  };

  /**
   * Belirli bir kategoriyi getirir
   * @param {number} id - Kategori ID'si
   * @returns {Promise<Object>} Kategori
   */
  const getCategory = async (id) => {
    return await getById('categories', id);
  };

  /**
   * Yeni bir kategori ekler
   * @param {Object} category - Kategori verileri
   * @returns {Promise<number>} Eklenen kategorinin ID'si
   */
  const addCategory = async (category) => {
    return await insert('categories', {
      ...category,
      is_default: category.is_default || 0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    });
  };

  /**
   * Bir kategoriyi günceller
   * @param {number} id - Kategori ID'si
   * @param {Object} category - Güncellenecek veriler
   * @returns {Promise<number>} Etkilenen satır sayısı
   */
  const updateCategory = async (id, category) => {
    return await update('categories', id, {
      ...category,
      updated_at: new Date().toISOString()
    });
  };

  /**
   * Bir kategoriyi siler
   * @param {number} id - Kategori ID'si
   * @returns {Promise<number>} Etkilenen satır sayısı
   */
  const deleteCategory = async (id) => {
    return await remove('categories', id);
  };

  return {
    getCategories,
    getCategory,
    addCategory,
    updateCategory,
    deleteCategory
  };
};

/**
 * İşlemlerle ilgili veritabanı işlemleri
 */
export const useTransactionService = () => {
  const { getAll, getById, insert, update, remove, query, withTransaction } = useDatabase();

  /**
   * Tüm işlemleri getirir
   * @param {Object} filters - Filtreler
   * @returns {Promise<Array>} İşlemler
   */
  const getTransactions = async (filters = {}) => {
    let queryStr = `
      SELECT t.*, c.name as category_name, c.icon as category_icon, c.color as category_color
      FROM transactions t
      LEFT JOIN categories c ON t.category_id = c.id
    `;

    const queryParams = [];
    const whereConditions = [];

    if (filters.isIncome !== undefined) {
      whereConditions.push('t.is_income = ?');
      queryParams.push(filters.isIncome ? 1 : 0);
    }

    if (filters.categoryId) {
      whereConditions.push('t.category_id = ?');
      queryParams.push(filters.categoryId);
    }

    if (filters.startDate) {
      whereConditions.push('t.transaction_date >= ?');
      queryParams.push(filters.startDate);
    }

    if (filters.endDate) {
      whereConditions.push('t.transaction_date <= ?');
      queryParams.push(filters.endDate);
    }

    if (filters.minAmount) {
      whereConditions.push('t.amount >= ?');
      queryParams.push(filters.minAmount);
    }

    if (filters.maxAmount) {
      whereConditions.push('t.amount <= ?');
      queryParams.push(filters.maxAmount);
    }

    if (whereConditions.length > 0) {
      queryStr += ` WHERE ${whereConditions.join(' AND ')}`;
    }

    queryStr += ' ORDER BY t.transaction_date DESC, t.created_at DESC';

    if (filters.limit) {
      queryStr += ' LIMIT ?';
      queryParams.push(filters.limit);
    }

    return await query(queryStr, queryParams);
  };

  /**
   * Belirli bir işlemi getirir
   * @param {number} id - İşlem ID'si
   * @returns {Promise<Object>} İşlem
   */
  const getTransaction = async (id) => {
    return await query(`
      SELECT t.*, c.name as category_name, c.icon as category_icon, c.color as category_color
      FROM transactions t
      LEFT JOIN categories c ON t.category_id = c.id
      WHERE t.id = ?
    `, [id]);
  };

  /**
   * Yeni bir işlem ekler
   * @param {Object} transaction - İşlem verileri
   * @param {Object} exchangeRates - Döviz kurları (opsiyonel)
   * @returns {Promise<number>} Eklenen işlemin ID'si
   */
  const addTransaction = async (transaction, exchangeRates = null) => {
    // Döviz kuru bilgilerini ekle
    const transactionData = { ...transaction };

    // Varsayılan para birimi
    transactionData.currency = transactionData.currency || 'TRY';

    // Tercih edilen para birimi varsa, dönüşüm yap
    if (transactionData.preferred_currency &&
        transactionData.preferred_currency !== transactionData.currency &&
        exchangeRates) {

      // Döviz kuru bilgisini al
      const rate = exchangeRates[transactionData.preferred_currency];

      if (rate) {
        transactionData.exchange_rate = rate;
        transactionData.preferred_currency_amount = transactionData.amount * rate;
      }
    }

    // Zaman damgalarını ekle
    transactionData.created_at = new Date().toISOString();
    transactionData.updated_at = new Date().toISOString();

    return await insert('transactions', transactionData);
  };

  /**
   * Bir işlemi günceller
   * @param {number} id - İşlem ID'si
   * @param {Object} transaction - Güncellenecek veriler
   * @param {Object} exchangeRates - Döviz kurları (opsiyonel)
   * @returns {Promise<number>} Etkilenen satır sayısı
   */
  const updateTransaction = async (id, transaction, exchangeRates = null) => {
    // Döviz kuru bilgilerini ekle
    const transactionData = { ...transaction };

    // Tercih edilen para birimi varsa, dönüşüm yap
    if (transactionData.preferred_currency &&
        transactionData.currency &&
        transactionData.preferred_currency !== transactionData.currency &&
        exchangeRates) {

      // Döviz kuru bilgisini al
      const rate = exchangeRates[transactionData.preferred_currency];

      if (rate) {
        transactionData.exchange_rate = rate;
        transactionData.preferred_currency_amount = transactionData.amount * rate;
      }
    }

    // Zaman damgasını güncelle
    transactionData.updated_at = new Date().toISOString();

    return await update('transactions', id, transactionData);
  };

  /**
   * Bir işlemi siler
   * @param {number} id - İşlem ID'si
   * @returns {Promise<number>} Etkilenen satır sayısı
   */
  const deleteTransaction = async (id) => {
    return await remove('transactions', id);
  };

  /**
   * İşlem istatistiklerini getirir
   * @param {string} period - Dönem (day, week, month, quarter, year, all, custom)
   * @param {Object} options - Özel dönem için seçenekler (startDate, endDate)
   * @returns {Promise<Object>} İstatistikler
   */
  const getTransactionStats = async (period = 'month', options = {}) => {
    let dateFilter = '';

    switch (period) {
      case 'day':
        dateFilter = "date(transaction_date) = date('now')";
        break;
      case 'week':
        dateFilter = "date(transaction_date) >= date('now', 'weekday 0', '-7 days')";
        break;
      case 'month':
        dateFilter = "strftime('%Y-%m', transaction_date) = strftime('%Y-%m', 'now')";
        break;
      case 'quarter':
        dateFilter = "date(transaction_date) >= date('now', 'start of month', '-2 months')";
        break;
      case 'year':
        dateFilter = "strftime('%Y', transaction_date) = strftime('%Y', 'now')";
        break;
      case 'custom':
        dateFilter = options.startDate && options.endDate
          ? "date(transaction_date) BETWEEN date(?) AND date(?)"
          : "1=1";
        break;
      default:
        dateFilter = "1=1"; // Tüm zamanlar
    }

    const params = period === 'custom' && options.startDate && options.endDate
      ? [options.startDate, options.endDate]
      : [];

    const stats = await query(`
      SELECT
        SUM(CASE WHEN is_income = 1 THEN amount ELSE 0 END) as total_income,
        SUM(CASE WHEN is_income = 0 THEN amount ELSE 0 END) as total_expense,
        (SUM(CASE WHEN is_income = 1 THEN amount ELSE 0 END) - SUM(CASE WHEN is_income = 0 THEN amount ELSE 0 END)) as balance,
        COUNT(CASE WHEN is_income = 1 THEN 1 END) as income_count,
        COUNT(CASE WHEN is_income = 0 THEN 1 END) as expense_count
      FROM transactions
      WHERE ${dateFilter}
    `, params);

    return stats[0];
  };

  /**
   * Kategori bazında işlem istatistiklerini getirir
   * @param {string} period - Dönem (day, week, month, quarter, year, all, custom)
   * @param {Object} options - Özel dönem için seçenekler (startDate, endDate)
   * @returns {Promise<Array>} Kategori bazında istatistikler
   */
  const getCategoryStats = async (period = 'month', options = {}) => {
    let dateFilter = '';

    switch (period) {
      case 'day':
        dateFilter = "date(t.transaction_date) = date('now')";
        break;
      case 'week':
        dateFilter = "date(t.transaction_date) >= date('now', 'weekday 0', '-7 days')";
        break;
      case 'month':
        dateFilter = "strftime('%Y-%m', t.transaction_date) = strftime('%Y-%m', 'now')";
        break;
      case 'quarter':
        dateFilter = "date(t.transaction_date) >= date('now', 'start of month', '-2 months')";
        break;
      case 'year':
        dateFilter = "strftime('%Y', t.transaction_date) = strftime('%Y', 'now')";
        break;
      case 'custom':
        dateFilter = options.startDate && options.endDate
          ? "date(t.transaction_date) BETWEEN date(?) AND date(?)"
          : "1=1";
        break;
      default:
        dateFilter = "1=1"; // Tüm zamanlar
    }

    const params = period === 'custom' && options.startDate && options.endDate
      ? [options.startDate, options.endDate]
      : [];

    return await query(`
      SELECT
        t.is_income,
        c.id as category_id,
        c.name as category_name,
        c.icon as category_icon,
        c.color as category_color,
        SUM(t.amount) as total,
        COUNT(t.id) as count
      FROM transactions t
      LEFT JOIN categories c ON t.category_id = c.id
      WHERE ${dateFilter}
      GROUP BY t.is_income, t.category_id
      ORDER BY t.is_income, total DESC
    `, params);
  };

  /**
   * İşlemleri filtreler ile getirir (Statistics ekranı için)
   * @param {Object} filters - Filtreler
   * @returns {Promise<Array>} İşlemler
   */
  const getTransactionsByFilter = async (filters = {}) => {
    let queryStr = `
      SELECT 
        t.id,
        t.amount,
        t.description,
        t.transaction_date as date,
        t.datetime,
        CASE 
          WHEN t.is_income = 1 THEN 'income'
          ELSE 'expense'
        END as type,
        c.name as category_name,
        c.icon as category_icon,
        c.color as category_color
      FROM transactions t
      LEFT JOIN categories c ON t.category_id = c.id
    `;

    const queryParams = [];
    const whereConditions = [];

    // Date filters
    if (filters.startDate) {
      whereConditions.push('t.transaction_date >= ?');
      queryParams.push(filters.startDate);
    }

    if (filters.endDate) {
      whereConditions.push('t.transaction_date <= ?');
      queryParams.push(filters.endDate);
    }

    // Type filter
    if (filters.type === 'income') {
      whereConditions.push('t.is_income = 1');
    } else if (filters.type === 'expense') {
      whereConditions.push('t.is_income = 0');
    }

    // Category filter
    if (filters.category) {
      whereConditions.push('c.name = ?');
      queryParams.push(filters.category);
    }

    // Amount filters
    if (filters.minAmount) {
      whereConditions.push('t.amount >= ?');
      queryParams.push(filters.minAmount);
    }

    if (filters.maxAmount) {
      whereConditions.push('t.amount <= ?');
      queryParams.push(filters.maxAmount);
    }

    if (whereConditions.length > 0) {
      queryStr += ' WHERE ' + whereConditions.join(' AND ');
    }

    queryStr += ' ORDER BY t.transaction_date DESC, t.datetime DESC';

    try {
      const result = await db.getAllAsync(queryStr, queryParams);
      return result;
    } catch (error) {
      console.error('getTransactionsByFilter error:', error);
      throw error;
    }
  };

  return {
    getTransactions,
    getTransaction,
    addTransaction,
    updateTransaction,
    deleteTransaction,
    getTransactionStats,
    getCategoryStats,
    withTransaction,
    getTransactionsByFilter
  };
};

/**
 * Döviz kurlarıyla ilgili veritabanı işlemleri
 */
export const useExchangeRateService = () => {
  const { query, insert, execute } = useDatabase();

  /**
   * En son döviz kurlarını getirir
   * @returns {Promise<Object>} Döviz kurları
   */
  const getLatestRates = async () => {
    const rates = await query(`
      SELECT currency, rate
      FROM exchange_rates
      WHERE fetch_date = (
        SELECT MAX(fetch_date) FROM exchange_rates
      )
    `);

    // Sonuçları daha kullanışlı bir formata dönüştür
    const ratesObj = {};
    rates.forEach(rate => {
      ratesObj[rate.currency] = rate.rate;
    });

    return ratesObj;
  };

  /**
   * Yeni döviz kurlarını ekler
   * @param {Object} rates - Döviz kurları
   * @param {string} fetchDate - Çekme tarihi
   * @returns {Promise<void>}
   */
  const addRates = async (rates, fetchDate = new Date().toISOString().split('T')[0]) => {
    // Önce eski kayıtları temizle
    await execute(`DELETE FROM exchange_rates WHERE fetch_date = ?`, [fetchDate]);

    // Yeni kurları ekle
    for (const [currency, rate] of Object.entries(rates)) {
      await insert('exchange_rates', {
        currency,
        rate,
        fetch_date: fetchDate,
        created_at: new Date().toISOString()
      });
    }
  };

  /**
   * Belirli bir tarihteki döviz kurlarını getirir
   * @param {string} date - Tarih
   * @returns {Promise<Object>} Döviz kurları
   */
  const getRatesByDate = async (date) => {
    const rates = await query(`
      SELECT currency, rate
      FROM exchange_rates
      WHERE fetch_date = ?
    `, [date]);

    // Sonuçları daha kullanışlı bir formata dönüştür
    const ratesObj = {};
    rates.forEach(rate => {
      ratesObj[rate.currency] = rate.rate;
    });

    return ratesObj;
  };

  return {
    getLatestRates,
    addRates,
    getRatesByDate
  };
};

/**
 * Ayarlarla ilgili veritabanı işlemleri
 */
export const useSettingsService = () => {
  const { query, execute } = useDatabase();

  /**
   * Tüm ayarları getirir
   * @returns {Promise<Object>} Ayarlar
   */
  const getAllSettings = async () => {
    const settings = await query(`SELECT id, value FROM settings`);

    // Sonuçları daha kullanışlı bir formata dönüştür
    const settingsObj = {};
    settings.forEach(setting => {
      settingsObj[setting.id] = setting.value;
    });

    return settingsObj;
  };

  /**
   * Belirli bir ayarı getirir
   * @param {string} id - Ayar ID'si
   * @returns {Promise<string>} Ayar değeri
   */
  const getSetting = async (id) => {
    const setting = await query(`SELECT value FROM settings WHERE id = ?`, [id]);
    return setting.length > 0 ? setting[0].value : null;
  };

  /**
   * Bir ayarı günceller
   * @param {string} id - Ayar ID'si
   * @param {string} value - Ayar değeri
   * @returns {Promise<void>}
   */
  const updateSetting = async (id, value) => {
    await execute(`
      INSERT OR REPLACE INTO settings (id, value, updated_at)
      VALUES (?, ?, ?)
    `, [id, value, new Date().toISOString()]);
  };

  /**
   * Birden fazla ayarı günceller
   * @param {Object} settings - Ayarlar
   * @returns {Promise<void>}
   */
  const updateSettings = async (settings) => {
    for (const [id, value] of Object.entries(settings)) {
      await updateSetting(id, value);
    }
  };

  return {
    getAllSettings,
    getSetting,
    updateSetting,
    updateSettings
  };
};

/**
 * Tab bar yapılandırmasıyla ilgili veritabanı işlemleri
 */
export const useTabBarConfigService = () => {
  const { query, execute } = useDatabase();

  /**
   * Tab bar yapılandırmasını getirir
   * @returns {Promise<Array>} Tab bar yapılandırması
   */
  const getTabBarConfig = async () => {
    return await query(`
      SELECT * FROM tab_bar_config
      WHERE is_visible = 1
      ORDER BY position ASC
    `);
  };

  /**
   * Tab bar yapılandırmasını günceller
   * @param {Array} config - Tab bar yapılandırması
   * @returns {Promise<void>}
   */
  const updateTabBarConfig = async (config) => {
    for (const tab of config) {
      await execute(`
        UPDATE tab_bar_config
        SET title = ?, icon = ?, position = ?, is_visible = ?, updated_at = ?
        WHERE id = ?
      `, [
        tab.title,
        tab.icon,
        tab.position,
        tab.is_visible ? 1 : 0,
        new Date().toISOString(),
        tab.id
      ]);
    }
  };

  return {
    getTabBarConfig,
    updateTabBarConfig
  };
};

/**
 * Veritabanını tamamen sıfırlar - Tüm tabloları siler ve yeniden oluşturur
 * @returns {Promise<void>}
 */
export const resetDatabase = async () => {
  try {
    const SQLite = require('expo-sqlite');
    const db = SQLite.openDatabaseSync('maas.db');
    
    console.log('🔄 Veritabanı sıfırlanıyor...');
    
    // Tüm tabloları sil
    const tables = [
      'transactions',
      'categories', 
      'budgets',
      'budget_alerts',
      'savings_goals',
      'investments',
      'currency_rates',
      'regular_incomes',
      'expense_reminders',
      'user_settings',
      'app_settings',
      'notifications',
      'tab_bar_config',
      'migration_log'
    ];
    
    for (const table of tables) {
      try {
        await db.execAsync(`DROP TABLE IF EXISTS ${table}`);
        console.log(`✅ Tablo silindi: ${table}`);
      } catch (error) {
        console.log(`⚠️ Tablo silinemedi (zaten yok olabilir): ${table}`, error.message);
      }
    }
    
    // Veritabanını yeniden başlat
    try {
      const { initializeDatabase } = require('./migrations');
      if (initializeDatabase) {
        await initializeDatabase();
        console.log('✅ Veritabanı yeniden başlatıldı');
      }
    } catch (error) {
      console.error('⚠️ Veritabanı yeniden başlatma hatası:', error);
    }
    
    console.log('✅ Veritabanı sıfırlama tamamlandı');
  } catch (error) {
    console.error('❌ Veritabanı sıfırlama hatası:', error);
    throw error;
  }
};
