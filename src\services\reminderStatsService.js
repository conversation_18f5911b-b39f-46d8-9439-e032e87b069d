/**
 * Hatırlatıcı istatistikleri için servis fonksi<PERSON>ı
 */

/**
 * <PERSON><PERSON>rlatıcı sayılarını duruma göre getirir
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<Object>} Durumlara göre hatırlatıcı sayıları
 */
export const getReminderCountsByStatus = async (db) => {
  try {
    const result = await db.getAllAsync(`
      SELECT status, COUNT(*) as count
      FROM notifications
      WHERE related_type = 'user_reminder'
      GROUP BY status
    `);
    
    // Sonuçları işle
    const counts = {
      pending: 0,
      sent: 0,
      read: 0,
      cancelled: 0,
      total: 0
    };
    
    result.forEach(row => {
      if (row.status in counts) {
        counts[row.status] = row.count;
      }
      counts.total += row.count;
    });
    
    return counts;
  } catch (error) {
    console.error('Hatırlatıcı sayıları getirme hatası:', error);
    throw error;
  }
};

/**
 * <PERSON><PERSON>rl<PERSON>ıcı sayılarını önceliğe göre getirir
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<Object>} Önceliklere göre hatırlatıcı sayıları
 */
export const getReminderCountsByPriority = async (db) => {
  try {
    const result = await db.getAllAsync(`
      SELECT priority, COUNT(*) as count
      FROM notifications
      WHERE related_type = 'user_reminder'
      GROUP BY priority
    `);
    
    // Sonuçları işle
    const counts = {
      low: 0,
      normal: 0,
      high: 0,
      total: 0
    };
    
    result.forEach(row => {
      if (row.priority in counts) {
        counts[row.priority] = row.count;
      }
      counts.total += row.count;
    });
    
    return counts;
  } catch (error) {
    console.error('Hatırlatıcı öncelik sayıları getirme hatası:', error);
    throw error;
  }
};

/**
 * Hatırlatıcı sayılarını gruplara göre getirir
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<Array>} Gruplara göre hatırlatıcı sayıları
 */
export const getReminderCountsByGroup = async (db) => {
  try {
    const result = await db.getAllAsync(`
      SELECT g.id, g.name, g.color, g.icon, COUNT(n.id) as count
      FROM reminder_groups g
      LEFT JOIN notifications n ON g.id = n.group_id AND n.related_type = 'user_reminder'
      GROUP BY g.id
      ORDER BY count DESC
    `);
    
    return result;
  } catch (error) {
    console.error('Grup hatırlatıcı sayıları getirme hatası:', error);
    throw error;
  }
};

/**
 * Hatırlatıcı sayılarını kategorilere göre getirir
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<Array>} Kategorilere göre hatırlatıcı sayıları
 */
export const getReminderCountsByCategory = async (db) => {
  try {
    const result = await db.getAllAsync(`
      SELECT c.id, c.name, c.color, COUNT(n.id) as count
      FROM categories c
      LEFT JOIN notifications n ON c.id = n.category_id AND n.related_type = 'user_reminder'
      GROUP BY c.id
      ORDER BY count DESC
    `);
    
    // Kategorisiz hatırlatıcıları da ekle
    const uncategorized = await db.getFirstAsync(`
      SELECT COUNT(*) as count
      FROM notifications
      WHERE related_type = 'user_reminder'
      AND category_id IS NULL
    `);
    
    if (uncategorized && uncategorized.count > 0) {
      result.push({
        id: null,
        name: 'Kategorisiz',
        color: '#999999',
        count: uncategorized.count
      });
    }
    
    return result;
  } catch (error) {
    console.error('Kategori hatırlatıcı sayıları getirme hatası:', error);
    throw error;
  }
};

/**
 * Hatırlatıcı sayılarını aylara göre getirir
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} monthsCount - Kaç ay geriye gidileceği
 * @returns {Promise<Array>} Aylara göre hatırlatıcı sayıları
 */
export const getReminderCountsByMonth = async (db, monthsCount = 6) => {
  try {
    // Son n ay için tarih aralıklarını oluştur
    const months = [];
    const now = new Date();
    
    for (let i = 0; i < monthsCount; i++) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const year = date.getFullYear();
      const month = date.getMonth() + 1; // JavaScript'te aylar 0-11 arasında
      
      const startDate = `${year}-${month.toString().padStart(2, '0')}-01`;
      
      // Ay sonu tarihini hesapla
      const endDate = new Date(year, month, 0); // Bir sonraki ayın 0. günü = bu ayın son günü
      const endDateStr = `${year}-${month.toString().padStart(2, '0')}-${endDate.getDate().toString().padStart(2, '0')}`;
      
      months.push({
        year,
        month,
        monthName: new Date(year, month - 1, 1).toLocaleString('tr-TR', { month: 'long' }),
        startDate,
        endDate: endDateStr
      });
    }
    
    // Her ay için hatırlatıcı sayılarını getir
    const result = [];
    
    for (const monthData of months) {
      const created = await db.getFirstAsync(`
        SELECT COUNT(*) as count
        FROM notifications
        WHERE related_type = 'user_reminder'
        AND created_at BETWEEN ? AND ?
      `, [`${monthData.startDate} 00:00:00`, `${monthData.endDate} 23:59:59`]);
      
      const scheduled = await db.getFirstAsync(`
        SELECT COUNT(*) as count
        FROM notifications
        WHERE related_type = 'user_reminder'
        AND scheduled_at BETWEEN ? AND ?
      `, [`${monthData.startDate} 00:00:00`, `${monthData.endDate} 23:59:59`]);
      
      result.push({
        ...monthData,
        created: created.count,
        scheduled: scheduled.count
      });
    }
    
    return result.reverse(); // En eski aydan en yeniye doğru sırala
  } catch (error) {
    console.error('Aylık hatırlatıcı sayıları getirme hatası:', error);
    throw error;
  }
};

/**
 * Hatırlatıcı tamamlanma oranını getirir
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<Object>} Tamamlanma oranı
 */
export const getReminderCompletionRate = async (db) => {
  try {
    const total = await db.getFirstAsync(`
      SELECT COUNT(*) as count
      FROM notifications
      WHERE related_type = 'user_reminder'
    `);
    
    const completed = await db.getFirstAsync(`
      SELECT COUNT(*) as count
      FROM notifications
      WHERE related_type = 'user_reminder'
      AND (status = 'sent' OR status = 'read')
    `);
    
    const rate = total.count > 0 ? (completed.count / total.count) * 100 : 0;
    
    return {
      total: total.count,
      completed: completed.count,
      rate: Math.round(rate * 100) / 100 // İki ondalık basamağa yuvarla
    };
  } catch (error) {
    console.error('Hatırlatıcı tamamlanma oranı getirme hatası:', error);
    throw error;
  }
};

/**
 * Hatırlatıcı tekrarlama tiplerinin dağılımını getirir
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<Object>} Tekrarlama tiplerine göre hatırlatıcı sayıları
 */
export const getReminderCountsByRepeatType = async (db) => {
  try {
    const result = await db.getAllAsync(`
      SELECT repeat_type, COUNT(*) as count
      FROM notifications
      WHERE related_type = 'user_reminder'
      GROUP BY repeat_type
    `);
    
    // Sonuçları işle
    const counts = {
      once: 0,
      daily: 0,
      weekly: 0,
      monthly: 0,
      yearly: 0,
      total: 0
    };
    
    result.forEach(row => {
      if (row.repeat_type in counts) {
        counts[row.repeat_type] = row.count;
      } else if (row.repeat_type === null) {
        counts.once += row.count;
      }
      counts.total += row.count;
    });
    
    return counts;
  } catch (error) {
    console.error('Hatırlatıcı tekrarlama tipleri getirme hatası:', error);
    throw error;
  }
};
