/**
 * Mesai servisi
 * Mesai işlemlerini yönetir
 */

/**
 * Mesai kayıtlarını getirir
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} options - Sorgu seçenekleri
 * @param {string} options.startDate - <PERSON><PERSON><PERSON><PERSON><PERSON> tarihi (YYYY-MM-DD)
 * @param {string} options.endDate - Bitiş tarihi (YYYY-MM-DD)
 * @param {boolean} options.onlyUnpaid - Sadece ödenmemiş mesaileri getir
 * @returns {Promise<Array>} Mesai kayıtları
 */
export const getOvertimeRecords = async (db, options = {}) => {
  try {
    let query = `
      SELECT o.*, c.name as category_name, c.icon as category_icon, c.color as category_color
      FROM overtime o
      LEFT JOIN categories c ON o.category_id = c.id
      WHERE 1=1
    `;
    
    const params = [];
    
    if (options.startDate) {
      query += ' AND o.date >= ?';
      params.push(options.startDate);
    }
    
    if (options.endDate) {
      query += ' AND o.date <= ?';
      params.push(options.endDate);
    }
    
    if (options.onlyUnpaid) {
      query += ' AND o.is_paid = 0';
    }
    
    query += ' ORDER BY o.date DESC, o.start_time DESC';
    
    return await db.getAllAsync(query, params);
  } catch (error) {
    console.error('Mesai kayıtları getirme hatası:', error);
    throw error;
  }
};

/**
 * Belirli bir tarihteki mesai kayıtlarını getirir
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {string} date - Tarih (YYYY-MM-DD)
 * @returns {Promise<Array>} Mesai kayıtları
 */
export const getOvertimeRecordsByDate = async (db, date) => {
  try {
    return await db.getAllAsync(`
      SELECT o.*, c.name as category_name, c.icon as category_icon, c.color as category_color
      FROM overtime o
      LEFT JOIN categories c ON o.category_id = c.id
      WHERE o.date = ?
      ORDER BY o.start_time
    `, [date]);
  } catch (error) {
    console.error('Tarih bazlı mesai kayıtları getirme hatası:', error);
    throw error;
  }
};

/**
 * Mesai detaylarını getirir
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} overtimeId - Mesai ID'si
 * @returns {Promise<Object>} Mesai detayları
 */
export const getOvertimeDetails = async (db, overtimeId) => {
  try {
    const overtime = await db.getFirstAsync(`
      SELECT o.*, c.name as category_name, c.icon as category_icon, c.color as category_color
      FROM overtime o
      LEFT JOIN categories c ON o.category_id = c.id
      WHERE o.id = ?
    `, [overtimeId]);
    
    if (!overtime) {
      throw new Error('Mesai kaydı bulunamadı');
    }
    
    // İlgili işlem varsa getir
    if (overtime.transaction_id) {
      const transaction = await db.getFirstAsync(`
        SELECT * FROM transactions WHERE id = ?
      `, [overtime.transaction_id]);
      
      overtime.transaction = transaction;
    }
    
    return overtime;
  } catch (error) {
    console.error('Mesai detayları getirme hatası:', error);
    throw error;
  }
};

/**
 * Yeni mesai kaydı oluşturur
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} overtime - Mesai verileri
 * @returns {Promise<number>} Oluşturulan mesai ID'si
 */
export const createOvertime = async (db, overtime) => {
  try {
    // Süreyi hesapla (saat cinsinden)
    const duration = calculateDuration(overtime.start_time, overtime.end_time);
    
    const result = await db.runAsync(`
      INSERT INTO overtime (
        title, description, date, start_time, end_time, duration,
        hourly_rate, currency, is_paid, payment_date, category_id, color, notes
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      overtime.title,
      overtime.description || null,
      overtime.date,
      overtime.start_time,
      overtime.end_time,
      duration,
      overtime.hourly_rate || 0,
      overtime.currency || 'TRY',
      overtime.is_paid || 0,
      overtime.payment_date || null,
      overtime.category_id || null,
      overtime.color || null,
      overtime.notes || null
    ]);
    
    return result.lastInsertRowId;
  } catch (error) {
    console.error('Mesai kaydı oluşturma hatası:', error);
    throw error;
  }
};

/**
 * Mesai kaydını günceller
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} overtimeId - Mesai ID'si
 * @param {Object} overtime - Güncellenecek mesai verileri
 * @returns {Promise<void>}
 */
export const updateOvertime = async (db, overtimeId, overtime) => {
  try {
    // Süreyi hesapla (saat cinsinden)
    const duration = calculateDuration(overtime.start_time, overtime.end_time);
    
    await db.runAsync(`
      UPDATE overtime
      SET title = ?, description = ?, date = ?, start_time = ?, end_time = ?, duration = ?,
          hourly_rate = ?, currency = ?, is_paid = ?, payment_date = ?, 
          category_id = ?, color = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [
      overtime.title,
      overtime.description || null,
      overtime.date,
      overtime.start_time,
      overtime.end_time,
      duration,
      overtime.hourly_rate || 0,
      overtime.currency || 'TRY',
      overtime.is_paid || 0,
      overtime.payment_date || null,
      overtime.category_id || null,
      overtime.color || null,
      overtime.notes || null,
      overtimeId
    ]);
  } catch (error) {
    console.error('Mesai kaydı güncelleme hatası:', error);
    throw error;
  }
};

/**
 * Mesai kaydını siler
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} overtimeId - Mesai ID'si
 * @returns {Promise<void>}
 */
export const deleteOvertime = async (db, overtimeId) => {
  try {
    await db.withTransactionAsync(async () => {
      // Mesai kaydını getir
      const overtime = await getOvertimeDetails(db, overtimeId);
      
      // İlgili işlem varsa sil
      if (overtime.transaction_id) {
        await db.runAsync(`
          DELETE FROM transactions WHERE id = ?
        `, [overtime.transaction_id]);
      }
      
      // Mesai kaydını sil
      await db.runAsync(`
        DELETE FROM overtime WHERE id = ?
      `, [overtimeId]);
    });
  } catch (error) {
    console.error('Mesai kaydı silme hatası:', error);
    throw error;
  }
};

/**
 * Mesai ödemesi oluşturur (gelir olarak kaydeder)
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} overtimeId - Mesai ID'si
 * @param {Object} options - İşlem seçenekleri
 * @returns {Promise<number>} Oluşturulan işlem ID'si
 */
export const processOvertimePayment = async (db, overtimeId, options = {}) => {
  try {
    return await db.withTransactionAsync(async () => {
      // Mesai kaydını getir
      const overtime = await getOvertimeDetails(db, overtimeId);
      
      if (!overtime) {
        throw new Error('Mesai kaydı bulunamadı');
      }
      
      if (overtime.is_paid && overtime.transaction_id) {
        throw new Error('Bu mesai ödemesi zaten işlenmiş');
      }
      
      // Toplam tutarı hesapla
      const totalAmount = overtime.duration * overtime.hourly_rate;
      
      // İşlem oluştur
      const result = await db.runAsync(`
        INSERT INTO transactions (
          type, amount, currency, date, category_id, 
          description, notes, metadata
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        'income',
        totalAmount,
        overtime.currency,
        options.payment_date || new Date().toISOString().split('T')[0],
        overtime.category_id || options.category_id || null,
        options.description || `${overtime.title} mesai ödemesi`,
        overtime.notes || null,
        JSON.stringify({ overtime_id: overtime.id })
      ]);
      
      const transactionId = result.lastInsertRowId;
      
      // Mesai kaydını güncelle
      await db.runAsync(`
        UPDATE overtime
        SET is_paid = 1, payment_date = ?, transaction_id = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `, [
        options.payment_date || new Date().toISOString().split('T')[0],
        transactionId,
        overtimeId
      ]);
      
      return transactionId;
    });
  } catch (error) {
    console.error('Mesai ödemesi işleme hatası:', error);
    throw error;
  }
};

/**
 * Aylık mesai istatistiklerini getirir
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {string} year - Yıl (YYYY)
 * @param {string} month - Ay (MM)
 * @returns {Promise<Object>} Mesai istatistikleri
 */
export const getMonthlyOvertimeStats = async (db, year, month) => {
  try {
    // Ay başlangıç ve bitiş tarihlerini hesapla
    const startDate = `${year}-${month.padStart(2, '0')}-01`;
    const lastDay = new Date(year, month, 0).getDate();
    const endDate = `${year}-${month.padStart(2, '0')}-${lastDay}`;
    
    // Toplam mesai saati
    const totalHours = await db.getFirstAsync(`
      SELECT SUM(duration) as total_hours
      FROM overtime
      WHERE date >= ? AND date <= ?
    `, [startDate, endDate]);
    
    // Toplam mesai ücreti
    const totalAmount = await db.getFirstAsync(`
      SELECT SUM(duration * hourly_rate) as total_amount, currency
      FROM overtime
      WHERE date >= ? AND date <= ?
      GROUP BY currency
    `, [startDate, endDate]);
    
    // Ödenen mesai sayısı
    const paidCount = await db.getFirstAsync(`
      SELECT COUNT(*) as count
      FROM overtime
      WHERE date >= ? AND date <= ? AND is_paid = 1
    `, [startDate, endDate]);
    
    // Ödenmemiş mesai sayısı
    const unpaidCount = await db.getFirstAsync(`
      SELECT COUNT(*) as count
      FROM overtime
      WHERE date >= ? AND date <= ? AND is_paid = 0
    `, [startDate, endDate]);
    
    return {
      total_hours: totalHours?.total_hours || 0,
      total_amount: totalAmount?.total_amount || 0,
      currency: totalAmount?.currency || 'TRY',
      paid_count: paidCount?.count || 0,
      unpaid_count: unpaidCount?.count || 0,
      total_count: (paidCount?.count || 0) + (unpaidCount?.count || 0)
    };
  } catch (error) {
    console.error('Aylık mesai istatistikleri getirme hatası:', error);
    throw error;
  }
};

/**
 * Belirli bir tarih aralığındaki mesai kayıtlarını getirir
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {string} startDate - Başlangıç tarihi (YYYY-MM-DD)
 * @param {string} endDate - Bitiş tarihi (YYYY-MM-DD)
 * @returns {Promise<Array>} Mesai kayıtları
 */
export const getOvertimeRecordsByDateRange = async (db, startDate, endDate) => {
  try {
    return await db.getAllAsync(`
      SELECT o.*, c.name as category_name, c.icon as category_icon, c.color as category_color
      FROM overtime o
      LEFT JOIN categories c ON o.category_id = c.id
      WHERE o.date >= ? AND o.date <= ?
      ORDER BY o.date, o.start_time
    `, [startDate, endDate]);
  } catch (error) {
    console.error('Tarih aralığı mesai kayıtları getirme hatası:', error);
    throw error;
  }
};

/**
 * Başlangıç ve bitiş saatleri arasındaki süreyi hesaplar (saat cinsinden)
 * 
 * @param {string} startTime - Başlangıç saati (HH:MM)
 * @param {string} endTime - Bitiş saati (HH:MM)
 * @returns {number} Süre (saat cinsinden)
 */
export const calculateDuration = (startTime, endTime) => {
  const [startHour, startMinute] = startTime.split(':').map(Number);
  const [endHour, endMinute] = endTime.split(':').map(Number);
  
  let durationHours = endHour - startHour;
  let durationMinutes = endMinute - startMinute;
  
  if (durationMinutes < 0) {
    durationHours -= 1;
    durationMinutes += 60;
  }
  
  // Eğer bitiş saati başlangıç saatinden küçükse, ertesi güne geçmiş demektir
  if (durationHours < 0) {
    durationHours += 24;
  }
  
  return durationHours + (durationMinutes / 60);
};

/**
 * Süreyi formatlar
 * 
 * @param {number} duration - Süre (saat cinsinden)
 * @returns {string} Formatlanmış süre (X saat Y dakika)
 */
export const formatDuration = (duration) => {
  const hours = Math.floor(duration);
  const minutes = Math.round((duration - hours) * 60);
  
  if (hours === 0) {
    return `${minutes} dakika`;
  } else if (minutes === 0) {
    return `${hours} saat`;
  } else {
    return `${hours} saat ${minutes} dakika`;
  }
};
