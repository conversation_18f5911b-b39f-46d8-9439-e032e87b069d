/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> tabloları için migrasyon
 *
 * @param {object} db - SQLite veritabanı nesnesi
 * @returns {Promise<void>}
 */
export const migrateBudgets = async (db) => {
  try {
    console.log('Bütçe tabloları migrasyonu başlatılıyor...');

    // budgets tablosunu oluştur
    const hasBudgetsTable = await db.getFirstAsync(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='budgets'
    `);

    if (!hasBudgetsTable) {
      console.log('budgets tablosu oluşturuluyor...');

      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS budgets (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          period TEXT NOT NULL CHECK(period IN ('daily', 'weekly', 'monthly', 'yearly')),
          start_date TEXT NOT NULL,
          end_date TEXT,
          is_active INTEGER DEFAULT 1,
          notes TEXT,
          amount DECIMAL(10,2) DEFAULT 0,
          currency TEXT DEFAULT 'TRY',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      console.log('budgets tablosu başarıyla oluşturuldu.');
    } else {
      console.log('budgets tablosu zaten mevcut.');

      // Tabloda eksik sütunlar var mı kontrol et
      const columns = await db.getAllAsync(`PRAGMA table_info(budgets)`);

      // Sütun adlarını bir diziye dönüştür
      const columnNames = columns.map(col => col.name);

      // Eksik sütunları kontrol et ve ekle
      if (!columnNames.includes('is_active')) {
        console.log('is_active sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE budgets ADD COLUMN is_active INTEGER DEFAULT 1`);
      }

      if (!columnNames.includes('notes')) {
        console.log('notes sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE budgets ADD COLUMN notes TEXT`);
      }

      if (!columnNames.includes('updated_at')) {
        console.log('updated_at sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE budgets ADD COLUMN updated_at DATETIME DEFAULT CURRENT_TIMESTAMP`);
      }

      // amount sütunu var mı kontrol et
      if (!columnNames.includes('amount')) {
        console.log('amount sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE budgets ADD COLUMN amount DECIMAL(10,2) DEFAULT 0`);
      } else {
        console.log('amount sütunu mevcut, NULL değerlere 0 atanıyor...');
        // amount sütunundaki NULL değerleri 0 olarak güncelle
        await db.execAsync(`UPDATE budgets SET amount = 0 WHERE amount IS NULL`);
      }

      // currency sütunu var mı kontrol et
      if (!columnNames.includes('currency')) {
        console.log('currency sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE budgets ADD COLUMN currency TEXT DEFAULT 'TRY'`);
      }
    }

    // budget_categories tablosunu oluştur
    const hasBudgetCategoriesTable = await db.getFirstAsync(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='budget_categories'
    `);

    if (!hasBudgetCategoriesTable) {
      console.log('budget_categories tablosu oluşturuluyor...');

      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS budget_categories (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          budget_id INTEGER NOT NULL,
          category_id INTEGER NOT NULL,
          amount DECIMAL(10,2) NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (budget_id) REFERENCES budgets (id) ON DELETE CASCADE,
          FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE CASCADE,
          UNIQUE(budget_id, category_id)
        )
      `);

      console.log('budget_categories tablosu başarıyla oluşturuldu.');
    } else {
      console.log('budget_categories tablosu zaten mevcut.');

      // Tabloda eksik sütunlar var mı kontrol et
      const columns = await db.getAllAsync(`PRAGMA table_info(budget_categories)`);

      // Sütun adlarını bir diziye dönüştür
      const columnNames = columns.map(col => col.name);

      // Eksik sütunları kontrol et ve ekle
      if (!columnNames.includes('updated_at')) {
        console.log('updated_at sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE budget_categories ADD COLUMN updated_at DATETIME DEFAULT CURRENT_TIMESTAMP`);
      }
    }

    console.log('Bütçe tabloları migrasyonu tamamlandı.');
  } catch (error) {
    console.error('Bütçe tabloları migrasyon hatası:', error);
    throw error;
  }
};
