/**
 * <PERSON><PERSON>i Seçici Komponenti
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md - Stage 2, Phase 2
 * 
 * Mevcut kategorileri seçme ve yeni kategori ekleme
 * Maksimum 250 satır - Tek sorumluluk prensibi
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  TextInput,
  Alert,
  Modal,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../../../context/ThemeContext';
import { categoryService } from '../../../services/categoryService';

/**
 * Kategori seçici komponenti
 * @param {Object} props - Component props
 * @param {Array} props.selectedCategories - Seçili kategori ID'leri
 * @param {Function} props.onCategoriesChange - <PERSON><PERSON><PERSON> callback fonksiyonu
 * @param {boolean} props.multiSelect - Çoklu seçim modu
 * @param {Object} props.theme - <PERSON><PERSON> objesi (opsiyonel, context'ten alınır)
 */
const CategorySelector = ({ 
  selectedCategories = [], 
  onCategoriesChange, 
  multiSelect = true,
  theme: propTheme 
}) => {
  const { theme } = useTheme();
  const currentTheme = propTheme || theme;

  // State yönetimi
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [newCategoryName, setNewCategoryName] = useState('');
  const [newCategoryIcon, setNewCategoryIcon] = useState('category');

  // Kategori ikonları
  const categoryIcons = [
    'restaurant', 'local-grocery-store', 'directions-car', 'home',
    'medical-services', 'school', 'sports-esports', 'shopping-bag',
    'flight', 'movie', 'fitness-center', 'pets', 'child-care',
    'work', 'savings', 'phone', 'wifi', 'electric-bolt'
  ];

  /**
   * Kategorileri yükle
   */
  const loadCategories = async () => {
    try {
      setLoading(true);
      const userCategories = await categoryService.getAllCategories();
      setCategories(userCategories);
    } catch (error) {
      console.error('Kategoriler yüklenirken hata:', error);
      Alert.alert('Hata', 'Kategoriler yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  // Component mount edildiğinde kategorileri yükle
  useEffect(() => {
    loadCategories();
  }, []);

  /**
   * Kategori seçim işleyicisi
   * @param {number} categoryId - Seçilen kategori ID'si
   */
  const handleCategorySelect = (categoryId) => {
    let newSelection;

    if (multiSelect) {
      if (selectedCategories.includes(categoryId)) {
        newSelection = selectedCategories.filter(id => id !== categoryId);
      } else {
        newSelection = [...selectedCategories, categoryId];
      }
    } else {
      newSelection = selectedCategories.includes(categoryId) ? [] : [categoryId];
    }

    if (onCategoriesChange) {
      onCategoriesChange(newSelection);
    }
  };

  /**
   * Yeni kategori ekleme işleyicisi
   */
  const handleAddCategory = async () => {
    if (!newCategoryName.trim()) {
      Alert.alert('Hata', 'Lütfen kategori adını girin.');
      return;
    }

    try {
      const newCategory = await categoryService.createCategory({
        name: newCategoryName.trim(),
        icon: newCategoryIcon,
        type: 'expense' // Varsayılan olarak gider kategorisi
      });

      setCategories(prev => [...prev, newCategory]);
      setNewCategoryName('');
      setNewCategoryIcon('category');
      setShowAddModal(false);

      Alert.alert('Başarılı', 'Yeni kategori eklendi.');
    } catch (error) {
      console.error('Kategori eklenirken hata:', error);
      Alert.alert('Hata', 'Kategori eklenirken bir hata oluştu.');
    }
  };

  /**
   * Kategori seçili mi kontrol et
   * @param {number} categoryId - Kontrol edilecek kategori ID'si
   * @returns {boolean} Seçili olup olmadığı
   */
  const isCategorySelected = (categoryId) => {
    return selectedCategories.includes(categoryId);
  };

  /**
   * Kategori listesi render fonksiyonu
   * @param {Object} item - Kategori objesi
   * @returns {JSX.Element} Kategori list item
   */
  const renderCategoryItem = ({ item }) => {
    const isSelected = isCategorySelected(item.id);

    return (
      <TouchableOpacity
        style={[
          styles.categoryItem,
          {
            backgroundColor: isSelected 
              ? currentTheme.PRIMARY + '20' 
              : currentTheme.SURFACE,
            borderColor: isSelected 
              ? currentTheme.PRIMARY 
              : currentTheme.BORDER,
          }
        ]}
        onPress={() => handleCategorySelect(item.id)}
        activeOpacity={0.7}
      >
        <View style={[styles.categoryIcon, { backgroundColor: currentTheme.PRIMARY + '20' }]}>
          <MaterialIcons 
            name={item.icon || 'category'} 
            size={24} 
            color={currentTheme.PRIMARY} 
          />
        </View>
        
        <Text style={[styles.categoryName, { color: currentTheme.TEXT_PRIMARY }]}>
          {item.name}
        </Text>

        <MaterialIcons
          name={isSelected ? 'check-circle' : 'radio-button-unchecked'}
          size={24}
          color={isSelected ? currentTheme.SUCCESS : currentTheme.TEXT_SECONDARY}
        />
      </TouchableOpacity>
    );
  };

  /**
   * İkon seçim render fonksiyonu
   * @param {Object} item - İkon adı
   * @returns {JSX.Element} İkon seçim item
   */
  const renderIconItem = ({ item }) => {
    const isSelected = item === newCategoryIcon;

    return (
      <TouchableOpacity
        style={[
          styles.iconItem,
          {
            backgroundColor: isSelected 
              ? currentTheme.PRIMARY 
              : currentTheme.SURFACE,
            borderColor: currentTheme.BORDER,
          }
        ]}
        onPress={() => setNewCategoryIcon(item)}
      >
        <MaterialIcons 
          name={item} 
          size={24} 
          color={isSelected ? currentTheme.WHITE : currentTheme.TEXT_PRIMARY} 
        />
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: currentTheme.BACKGROUND }]}>
        <Text style={[styles.loadingText, { color: currentTheme.TEXT_SECONDARY }]}>
          Kategoriler yükleniyor...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: currentTheme.BACKGROUND }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: currentTheme.TEXT_PRIMARY }]}>
          Kategorileri Seçin
        </Text>
        <Text style={[styles.subtitle, { color: currentTheme.TEXT_SECONDARY }]}>
          {multiSelect 
            ? 'Bütçe takibi yapmak istediğiniz kategorileri seçin'
            : 'Bir kategori seçin'
          }
        </Text>
      </View>

      {/* Kategori listesi */}
      <FlatList
        data={categories}
        renderItem={renderCategoryItem}
        keyExtractor={(item) => item.id.toString()}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.categoryList}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <MaterialIcons name="category" size={48} color={currentTheme.TEXT_SECONDARY} />
            <Text style={[styles.emptyText, { color: currentTheme.TEXT_SECONDARY }]}>
              Henüz kategori bulunmuyor
            </Text>
          </View>
        }
      />

      {/* Yeni kategori ekleme butonu */}
      <TouchableOpacity
        style={[styles.addButton, { backgroundColor: currentTheme.PRIMARY }]}
        onPress={() => setShowAddModal(true)}
      >
        <MaterialIcons name="add" size={24} color={currentTheme.WHITE} />
        <Text style={[styles.addButtonText, { color: currentTheme.WHITE }]}>
          Yeni Kategori Ekle
        </Text>
      </TouchableOpacity>

      {/* Yeni kategori ekleme modalı */}
      <Modal
        visible={showAddModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowAddModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: currentTheme.SURFACE }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: currentTheme.TEXT_PRIMARY }]}>
                Yeni Kategori Ekle
              </Text>
              <TouchableOpacity onPress={() => setShowAddModal(false)}>
                <MaterialIcons name="close" size={24} color={currentTheme.TEXT_SECONDARY} />
              </TouchableOpacity>
            </View>

            {/* Kategori adı girişi */}
            <View style={styles.inputSection}>
              <Text style={[styles.inputLabel, { color: currentTheme.TEXT_SECONDARY }]}>
                Kategori Adı
              </Text>
              <TextInput
                style={[
                  styles.textInput,
                  {
                    backgroundColor: currentTheme.BACKGROUND,
                    borderColor: currentTheme.BORDER,
                    color: currentTheme.TEXT_PRIMARY,
                  }
                ]}
                value={newCategoryName}
                onChangeText={setNewCategoryName}
                placeholder="Kategori adını girin"
                placeholderTextColor={currentTheme.TEXT_SECONDARY}
                maxLength={50}
              />
            </View>

            {/* İkon seçimi */}
            <View style={styles.iconSection}>
              <Text style={[styles.inputLabel, { color: currentTheme.TEXT_SECONDARY }]}>
                İkon Seçin
              </Text>
              <FlatList
                data={categoryIcons}
                renderItem={renderIconItem}
                keyExtractor={(item) => item}
                numColumns={6}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.iconGrid}
              />
            </View>

            {/* Modal butonları */}
            <View style={styles.modalActions}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton, { borderColor: currentTheme.BORDER }]}
                onPress={() => setShowAddModal(false)}
              >
                <Text style={[styles.cancelButtonText, { color: currentTheme.TEXT_SECONDARY }]}>
                  İptal
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.modalButton, styles.confirmButton, { backgroundColor: currentTheme.PRIMARY }]}
                onPress={handleAddCategory}
              >
                <Text style={[styles.confirmButtonText, { color: currentTheme.WHITE }]}>
                  Ekle
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
  header: {
    paddingVertical: 16,
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  categoryList: {
    paddingBottom: 80,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    borderWidth: 1,
    gap: 12,
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoryName: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 16,
    marginTop: 16,
  },
  addButton: {
    position: 'absolute',
    bottom: 16,
    left: 16,
    right: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
  },
  addButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    borderRadius: 16,
    padding: 16,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  inputSection: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
  },
  iconSection: {
    marginBottom: 16,
  },
  iconGrid: {
    gap: 8,
  },
  iconItem: {
    width: 48,
    height: 48,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    margin: 4,
  },
  modalActions: {
    flexDirection: 'row',
    gap: 12,
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    borderWidth: 1,
  },
  confirmButton: {
    // backgroundColor set via style prop
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default CategorySelector;
