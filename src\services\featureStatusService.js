/**
 * Özellik Durumu Yönetim Servisi
 * Tüm uygulama özelliklerinin durumunu takip eder
 */

/**
 * Özellik durumları
 */
export const FEATURE_STATUS = {
  COMPLETED: 'completed',
  IN_PROGRESS: 'in_progress', 
  COMING_SOON: 'coming_soon',
  PLANNED: 'planned',
  BETA: 'beta',
  REMOVED: 'removed'
};

/**
 * <PERSON>zellik kategorileri
 */
export const FEATURE_CATEGORIES = {
  CORE: 'core',
  ANALYTICS: 'analytics',
  AUTOMATION: 'automation',
  ADVANCED: 'advanced',
  INTEGRATION: 'integration',
  SECURITY: 'security'
};

/**
 * Tüm uygulama özellikleri ve durumları
 */
export const ALL_FEATURES = [
  // ✅ TAMAMLANAN ÖZELLİKLER
  {
    id: 'income_expense_tracking',
    title: '<PERSON><PERSON><PERSON>-<PERSON><PERSON> Takibi',
    description: 'Temel finansal işlem kayıtları ve kategori yönetimi',
    category: FEATURE_CATEGORIES.CORE,
    status: FEATURE_STATUS.COMPLETED,
    icon: 'receipt-long',
    completedAt: '2024-01-15',
    progress: 100
  },
  {
    id: 'investment_tracking',
    title: 'Yatırım Takibi',
    description: 'Hisse senedi, altın, döviz yatırımlarının takibi',
    category: FEATURE_CATEGORIES.CORE,
    status: FEATURE_STATUS.COMPLETED,
    icon: 'trending-up',
    completedAt: '2024-01-20',
    progress: 100
  },
  {
    id: 'salary_management',
    title: 'Maaş Yönetimi',
    description: 'Düzenli maaş ödemeleri ve bordro takibi',
    category: FEATURE_CATEGORIES.CORE,
    status: FEATURE_STATUS.COMPLETED,
    icon: 'payments',
    completedAt: '2024-01-25',
    progress: 100
  },
  {
    id: 'overtime_tracking',
    title: 'Mesai Takibi',
    description: 'Çalışma saatleri ve mesai ücretleri hesaplama',
    category: FEATURE_CATEGORIES.CORE,
    status: FEATURE_STATUS.COMPLETED,
    icon: 'schedule',
    completedAt: '2024-01-30',
    progress: 100
  },
  {
    id: 'reminder_system',
    title: 'Hatırlatıcı Sistemi',
    description: 'Ödemeler ve finansal görevler için hatırlatıcılar',
    category: FEATURE_CATEGORIES.AUTOMATION,
    status: FEATURE_STATUS.COMPLETED,
    icon: 'alarm',
    completedAt: '2024-02-05',
    progress: 100
  },
  {
    id: 'budget_management',
    title: 'Bütçe Yönetimi',
    description: 'Aylık bütçe planlama ve takip sistemi',
    category: FEATURE_CATEGORIES.CORE,
    status: FEATURE_STATUS.COMPLETED,
    icon: 'account-balance-wallet',
    completedAt: '2024-02-10',
    progress: 100
  },
  {
    id: 'advanced_reporting',
    title: 'Gelişmiş Raporlama',
    description: 'Detaylı grafikler, trend analizi ve karşılaştırmalı raporlar',
    category: FEATURE_CATEGORIES.ANALYTICS,
    status: FEATURE_STATUS.COMPLETED,
    icon: 'assessment',
    completedAt: '2024-02-15',
    progress: 100
  },
  {
    id: 'pin_biometric_auth',
    title: 'PIN/Biometrik Güvenlik',
    description: 'Uygulama güvenliği için PIN ve parmak izi koruması (Kaldırıldı)',
    category: FEATURE_CATEGORIES.SECURITY,
    status: FEATURE_STATUS.REMOVED,
    icon: 'security',
    completedAt: '2024-02-20',
    removedAt: '2025-07-03',
    progress: 0,
    notes: 'PIN sistemi kullanıcı deneyimini iyileştirmek için kaldırıldı'
  },
  {
    id: 'dark_mode',
    title: 'Karanlık Mod',
    description: 'Sistem ayarlarını takip eden karanlık/aydınlık tema',
    category: FEATURE_CATEGORIES.CORE,
    status: FEATURE_STATUS.COMPLETED,
    icon: 'dark-mode',
    completedAt: '2024-02-25',
    progress: 100
  },

  // 🔄 DEVAM EDEN ÖZELLİKLER
  {
    id: 'regular_income_management',
    title: 'Düzenli Gelir Yönetimi',
    description: 'Tekrarlayan gelir kaynakları ve otomatik kayıt sistemi',
    category: FEATURE_CATEGORIES.AUTOMATION,
    status: FEATURE_STATUS.IN_PROGRESS,
    icon: 'repeat',
    estimatedCompletion: '2024-03-01',
    progress: 85
  },
  {
    id: 'savings_goals',
    title: 'Birikim Hedefleri',
    description: 'Hedef belirleme, ilerleme takibi ve motivasyon sistemi',
    category: FEATURE_CATEGORIES.CORE,
    status: FEATURE_STATUS.IN_PROGRESS,
    icon: 'savings',
    estimatedCompletion: '2024-03-05',
    progress: 70
  },

  // 🔜 YAKINDA GELECEK ÖZELLİKLER
  {
    id: 'ai_insights',
    title: 'Akıllı İçgörüler',
    description: 'AI destekli finansal öneriler ve kişiselleştirilmiş tavsiyeler',
    category: FEATURE_CATEGORIES.ADVANCED,
    status: FEATURE_STATUS.COMING_SOON,
    icon: 'lightbulb-outline',
    estimatedCompletion: '2024-03-15',
    progress: 30
  },
  {
    id: 'financial_health_score',
    title: 'Finansal Sağlık Skoru',
    description: '100 üzerinden finansal durumunuzun değerlendirilmesi',
    category: FEATURE_CATEGORIES.ANALYTICS,
    status: FEATURE_STATUS.COMING_SOON,
    icon: 'favorite',
    estimatedCompletion: '2024-03-20',
    progress: 25
  },
  {
    id: 'expense_prediction',
    title: 'Harcama Tahmini',
    description: 'Geçmiş verilere dayalı gelecek harcama tahminleri',
    category: FEATURE_CATEGORIES.ADVANCED,
    status: FEATURE_STATUS.COMING_SOON,
    icon: 'trending-up',
    estimatedCompletion: '2024-03-25',
    progress: 20
  },
  {
    id: 'automatic_categorization',
    title: 'Otomatik Kategorizasyon',
    description: 'İşlem açıklamalarına göre otomatik kategori atama',
    category: FEATURE_CATEGORIES.AUTOMATION,
    status: FEATURE_STATUS.COMING_SOON,
    icon: 'auto-fix-high',
    estimatedCompletion: '2024-04-01',
    progress: 15
  },
  {
    id: 'multi_currency_support',
    title: 'Çoklu Para Birimi',
    description: 'Farklı para birimlerinde işlem kayıtları ve otomatik çevrim',
    category: FEATURE_CATEGORIES.ADVANCED,
    status: FEATURE_STATUS.COMING_SOON,
    icon: 'currency-exchange',
    estimatedCompletion: '2024-04-05',
    progress: 10
  },
  {
    id: 'receipt_scanning',
    title: 'Fiş Tarama',
    description: 'Kamera ile fiş tarayarak otomatik harcama kaydı',
    category: FEATURE_CATEGORIES.AUTOMATION,
    status: FEATURE_STATUS.COMING_SOON,
    icon: 'camera-alt',
    estimatedCompletion: '2024-04-10',
    progress: 5
  },

  // 📋 PLANLANMIŞ ÖZELLİKLER
  {
    id: 'cloud_sync',
    title: 'Bulut Senkronizasyonu',
    description: 'Verilerinizi güvenli şekilde bulutta saklama ve senkronizasyon',
    category: FEATURE_CATEGORIES.INTEGRATION,
    status: FEATURE_STATUS.PLANNED,
    icon: 'cloud-sync',
    estimatedCompletion: '2024-05-01',
    progress: 0
  },
  {
    id: 'family_sharing',
    title: 'Aile Paylaşımı',
    description: 'Aile üyeleriyle bütçe ve harcama paylaşımı',
    category: FEATURE_CATEGORIES.ADVANCED,
    status: FEATURE_STATUS.PLANNED,
    icon: 'family-restroom',
    estimatedCompletion: '2024-05-15',
    progress: 0
  },
  {
    id: 'bank_integration',
    title: 'Banka Entegrasyonu',
    description: 'Banka hesaplarından otomatik işlem çekme',
    category: FEATURE_CATEGORIES.INTEGRATION,
    status: FEATURE_STATUS.PLANNED,
    icon: 'account-balance',
    estimatedCompletion: '2024-06-01',
    progress: 0
  },
  {
    id: 'tax_calculation',
    title: 'Vergi Hesaplama',
    description: 'Gelir vergisi ve KDV hesaplamaları',
    category: FEATURE_CATEGORIES.ADVANCED,
    status: FEATURE_STATUS.PLANNED,
    icon: 'calculate',
    estimatedCompletion: '2024-06-15',
    progress: 0
  },
  {
    id: 'investment_recommendations',
    title: 'Yatırım Önerileri',
    description: 'Risk profilinize uygun yatırım önerileri',
    category: FEATURE_CATEGORIES.ADVANCED,
    status: FEATURE_STATUS.PLANNED,
    icon: 'recommend',
    estimatedCompletion: '2024-07-01',
    progress: 0
  }
];

/**
 * Duruma göre özellikleri filtrele
 * @param {string} status - Özellik durumu
 * @returns {Array} Filtrelenmiş özellik listesi
 */
export const getFeaturesByStatus = (status) => {
  return ALL_FEATURES.filter(feature => feature.status === status);
};

/**
 * Kategoriye göre özellikleri filtrele
 * @param {string} category - Özellik kategorisi
 * @returns {Array} Filtrelenmiş özellik listesi
 */
export const getFeaturesByCategory = (category) => {
  return ALL_FEATURES.filter(feature => feature.category === category);
};

/**
 * Tamamlanma oranına göre özellikleri sırala
 * @returns {Array} Sıralanmış özellik listesi
 */
export const getFeaturesByProgress = () => {
  return [...ALL_FEATURES].sort((a, b) => b.progress - a.progress);
};

/**
 * Genel ilerleme istatistikleri
 * @returns {Object} İlerleme istatistikleri
 */
export const getProgressStats = () => {
  const total = ALL_FEATURES.length;
  const completed = getFeaturesByStatus(FEATURE_STATUS.COMPLETED).length;
  const inProgress = getFeaturesByStatus(FEATURE_STATUS.IN_PROGRESS).length;
  const comingSoon = getFeaturesByStatus(FEATURE_STATUS.COMING_SOON).length;
  const planned = getFeaturesByStatus(FEATURE_STATUS.PLANNED).length;

  return {
    total,
    completed,
    inProgress,
    comingSoon,
    planned,
    completionRate: Math.round((completed / total) * 100),
    activeFeatures: completed + inProgress
  };
};

/**
 * Özellik durumu renklerini al
 * @param {string} status - Özellik durumu
 * @returns {Object} Renk bilgileri
 */
export const getStatusColors = (status) => {
  const colors = {
    [FEATURE_STATUS.COMPLETED]: {
      primary: '#4CAF50',
      light: '#E8F5E8',
      text: '#2E7D32'
    },
    [FEATURE_STATUS.IN_PROGRESS]: {
      primary: '#FF9800',
      light: '#FFF3E0',
      text: '#F57C00'
    },
    [FEATURE_STATUS.COMING_SOON]: {
      primary: '#2196F3',
      light: '#E3F2FD',
      text: '#1976D2'
    },
    [FEATURE_STATUS.PLANNED]: {
      primary: '#9E9E9E',
      light: '#F5F5F5',
      text: '#616161'
    },
    [FEATURE_STATUS.BETA]: {
      primary: '#9C27B0',
      light: '#F3E5F5',
      text: '#7B1FA2'
    }
  };

  return colors[status] || colors[FEATURE_STATUS.PLANNED];
};

/**
 * Özellik durumu etiketlerini al
 * @param {string} status - Özellik durumu
 * @returns {string} Türkçe etiket
 */
export const getStatusLabel = (status) => {
  const labels = {
    [FEATURE_STATUS.COMPLETED]: 'Tamamlandı',
    [FEATURE_STATUS.IN_PROGRESS]: 'Devam Ediyor',
    [FEATURE_STATUS.COMING_SOON]: 'Yakında',
    [FEATURE_STATUS.PLANNED]: 'Planlandı',
    [FEATURE_STATUS.BETA]: 'Beta'
  };

  return labels[status] || 'Bilinmiyor';
};
