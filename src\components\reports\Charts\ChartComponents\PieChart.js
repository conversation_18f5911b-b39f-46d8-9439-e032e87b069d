import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-native-chart-kit';

const screenWidth = Dimensions.get('window').width;

/**
 * Pie Chart component using react-native-chart-kit
 * @param {Object} props - Component props
 * @param {Array} props.data - Chart data
 * @param {Object} props.theme - Theme object
 * @param {string} props.title - Chart title
 * @param {Object} props.config - Chart configuration
 * @returns {JSX.Element} Pie chart component
 */
const PieChart = ({ data, theme, title, config = {} }) => {
  /**
   * Get safe theme value
   * @param {string} property - Theme property
   * @param {string} fallback - Fallback value
   * @returns {string} Safe theme value
   */
  const getSafeThemeValue = (property, fallback) => {
    return theme?.[property] || theme?.colors?.[property.toLowerCase()] || fallback;
  };

  const chartConfig = {
    backgroundColor: getSafeThemeValue('BACKGROUND', '#ffffff'),
    backgroundGradientFrom: getSafeThemeValue('BACKGROUND', '#ffffff'),
    backgroundGradientTo: getSafeThemeValue('BACKGROUND', '#ffffff'),
    decimalPlaces: config.decimalPlaces || 2,
    color: (opacity = 1) => getSafeThemeValue('PRIMARY', '#007AFF').replace(')', `, ${opacity})`).replace('#', 'rgba(').replace(/^rgba\((\d+), (\d+), (\d+)/, 'rgba($1, $2, $3'),
    labelColor: (opacity = 1) => getSafeThemeValue('TEXT_PRIMARY', '#333333').replace(')', `, ${opacity})`).replace('#', 'rgba(').replace(/^rgba\((\d+), (\d+), (\d+)/, 'rgba($1, $2, $3'),
    style: {
      borderRadius: 16,
    },
    ...config,
  };

  const defaultData = data || [
    {
      name: 'Gelir',
      population: 25000,
      color: getSafeThemeValue('SUCCESS', '#28a745'),
      legendFontColor: getSafeThemeValue('TEXT_PRIMARY', '#333333'),
      legendFontSize: 12,
    },
    {
      name: 'Gider',
      population: 18000,
      color: getSafeThemeValue('DANGER', '#dc3545'),
      legendFontColor: getSafeThemeValue('TEXT_PRIMARY', '#333333'),
      legendFontSize: 12,
    },
    {
      name: 'Yatırım',
      population: 12000,
      color: getSafeThemeValue('WARNING', '#ffc107'),
      legendFontColor: getSafeThemeValue('TEXT_PRIMARY', '#333333'),
      legendFontSize: 12,
    },
    {
      name: 'Tasarruf',
      population: 8000,
      color: getSafeThemeValue('INFO', '#17a2b8'),
      legendFontColor: getSafeThemeValue('TEXT_PRIMARY', '#333333'),
      legendFontSize: 12,
    },
  ];

  return (
    <View style={[styles.container, { backgroundColor: getSafeThemeValue('BACKGROUND', '#ffffff') }]}>
      {title && (
        <Text style={[styles.title, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
          {title}
        </Text>
      )}
      <RNPieChart
        data={defaultData}
        width={screenWidth - 32}
        height={220}
        chartConfig={chartConfig}
        accessor="population"
        backgroundColor="transparent"
        paddingLeft="15"
        style={styles.chart}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    borderRadius: 8,
    marginVertical: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
    textAlign: 'center',
  },
  chart: {
    borderRadius: 8,
  },
});

export default PieChart;
