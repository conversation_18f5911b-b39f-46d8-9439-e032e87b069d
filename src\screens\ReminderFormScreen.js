import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Switch,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  Modal
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useFocusEffect } from '@react-navigation/native';

import { MaterialIcons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { format, parseISO } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Colors } from '../constants/colors';
import * as reminderService from '../services/reminderService';
import * as reminderGroupService from '../services/reminderGroupService';
import * as reminderTagService from '../services/reminderTagService';
import * as reminderTemplateService from '../services/reminderTemplateService';
import * as reminderPatternService from '../services/reminderPatternService';

/**
 * Hatırlatıcı Ekleme/Düzenleme Ekranı
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @param {Object} props.route - Route nesnesi
 * @returns {JSX.Element} Hatırlatıcı Form Ekranı
 */
export default function ReminderFormScreen({ navigation, route }) {
  const db = useSQLiteContext();
  const { reminderId, templateId } = route?.params || {};
  const isEditing = !!reminderId;
  const isFromTemplate = !!templateId;

  // Form durumu
  const [title, setTitle] = useState('');
  const [message, setMessage] = useState('');
  const [date, setDate] = useState(new Date());
  const [time, setTime] = useState(new Date());
  const [repeatType, setRepeatType] = useState('once');
  const [repeatInterval, setRepeatInterval] = useState(1);
  const [repeatDays, setRepeatDays] = useState([]);
  const [repeatEndDate, setRepeatEndDate] = useState(null);
  const [priority, setPriority] = useState('normal');
  const [categoryId, setCategoryId] = useState(null);
  const [groupId, setGroupId] = useState(route?.params?.groupId || null);
  const [isEnabled, setIsEnabled] = useState(true);
  const [selectedTags, setSelectedTags] = useState([]);
  const [customPattern, setCustomPattern] = useState(null);
  const [customPatternType, setCustomPatternType] = useState(null);
  const [customPatternValue, setCustomPatternValue] = useState(null);

  // UI durumu
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [categories, setCategories] = useState([]);
  const [groups, setGroups] = useState([]);
  const [tags, setTags] = useState([]);
  const [patterns, setPatterns] = useState([]);
  const [showPatternPicker, setShowPatternPicker] = useState(false);

  // Kategori ekleme modalı
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [newCategoryName, setNewCategoryName] = useState('');
  const [newCategoryColor, setNewCategoryColor] = useState('#3498db');
  const [newCategoryType, setNewCategoryType] = useState('both'); // 'income', 'expense', 'both'
  const [savingCategory, setSavingCategory] = useState(false);

  // Verileri yükle
  const loadData = useCallback(async () => {
    try {
      setLoading(true);

      if (isEditing) {
        // Hatırlatıcı detaylarını getir
        const reminderDetails = await reminderService.getReminderById(db, reminderId);

        if (reminderDetails) {
          setTitle(reminderDetails.title);
          setMessage(reminderDetails.message);

          // Tarih ve saat ayarla
          if (reminderDetails.date) {
            setDate(parseISO(reminderDetails.date));
          }

          if (reminderDetails.time) {
            const [hours, minutes] = reminderDetails.time.split(':');
            const timeDate = new Date();
            timeDate.setHours(parseInt(hours, 10));
            timeDate.setMinutes(parseInt(minutes, 10));
            setTime(timeDate);
          }

          // Tekrarlama ayarları
          setRepeatType(reminderDetails.repeat_type || 'once');
          setRepeatInterval(reminderDetails.repeat_interval || 1);
          setRepeatDays(reminderDetails.repeat_days || []);

          if (reminderDetails.repeat_end_date) {
            setRepeatEndDate(parseISO(reminderDetails.repeat_end_date));
          }

          // Diğer ayarlar
          setPriority(reminderDetails.priority || 'normal');
          setCategoryId(reminderDetails.category_id);
          setGroupId(reminderDetails.group_id);
          setIsEnabled(reminderDetails.is_enabled === 1);

          // Etiketleri yükle
          const tagData = await reminderTagService.getTagsByReminderId(db, reminderId);
          if (tagData && tagData.length > 0) {
            setSelectedTags(tagData.map(tag => tag.id));
          }
        }
      } else if (templateId) {
        // Şablon detaylarını getir
        const templateDetails = await reminderTemplateService.getTemplateById(db, templateId);

        if (templateDetails) {
          setTitle(templateDetails.title);
          setMessage(templateDetails.message || '');

          // Tekrarlama ayarları
          setRepeatType(templateDetails.repeat_type || 'once');
          setRepeatInterval(templateDetails.repeat_interval || 1);
          setRepeatDays(templateDetails.repeat_days || []);

          // Diğer ayarlar
          setPriority(templateDetails.priority || 'normal');
          setCategoryId(templateDetails.category_id);
          setGroupId(templateDetails.group_id);

          // Etiketleri ayarla
          if (templateDetails.tags && templateDetails.tags.length > 0) {
            setSelectedTags(templateDetails.tags.map(tag => tag.id));
          }
        }
      }

      setLoading(false);
    } catch (error) {
      console.error('Veri yükleme hatası:', error);
      Alert.alert('Hata', 'Veriler yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  }, [reminderId, templateId, db, isEditing]);

  // Kategorileri yükle
  const loadCategories = useCallback(async () => {
    try {
      const result = await db.getAllAsync(`
        SELECT * FROM categories
        ORDER BY name ASC
      `);

      setCategories(result);
    } catch (error) {
      console.error('Kategorileri yükleme hatası:', error);
    }
  }, [db]);

  // Özel tekrarlama desenlerini yükle
  const loadPatterns = useCallback(async () => {
    try {
      const result = await reminderPatternService.getAllPatterns(db);
      setPatterns(result);
    } catch (error) {
      console.error('Tekrarlama desenlerini yükleme hatası:', error);
    }
  }, [db]);

  // Kategori ekleme modalını göster
  const showAddCategoryModal = () => {
    setNewCategoryName('');
    setNewCategoryColor('#3498db');
    setNewCategoryType('both');
    setShowCategoryModal(true);
  };

  // Yeni kategori ekle
  const addNewCategory = async () => {
    if (!newCategoryName.trim()) {
      Alert.alert('Hata', 'Lütfen bir kategori adı girin.');
      return;
    }

    try {
      setSavingCategory(true);

      // Kategori rengini kontrol et
      const color = newCategoryColor || '#3498db';

      // Kategoriyi veritabanına ekle
      const result = await db.runAsync(`
        INSERT INTO categories (name, color, type, icon, is_default)
        VALUES (?, ?, ?, ?, ?)
      `, [
        newCategoryName.trim(),
        color,
        newCategoryType,
        'label',
        0
      ]);

      // Yeni kategori ID'sini al
      const newCategoryId = result.lastInsertRowId;

      // Kategorileri yeniden yükle
      await loadCategories();

      // Yeni kategoriyi seç
      setCategoryId(newCategoryId);

      // Modalı kapat
      setShowCategoryModal(false);
      setSavingCategory(false);

      Alert.alert('Başarılı', 'Yeni kategori eklendi.');
    } catch (error) {
      console.error('Kategori ekleme hatası:', error);
      Alert.alert('Hata', 'Kategori eklenirken bir hata oluştu.');
      setSavingCategory(false);
    }
  };

  // Grupları yükle
  const loadGroups = useCallback(async () => {
    try {
      const result = await reminderGroupService.getAllReminderGroups(db);
      setGroups(result);
    } catch (error) {
      console.error('Grupları yükleme hatası:', error);
    }
  }, [db]);

  // Etiketleri yükle
  const loadTags = useCallback(async () => {
    try {
      const tagData = await reminderTagService.getAllTags(db);
      setTags(tagData);
    } catch (error) {
      console.error('Etiketleri yükleme hatası:', error);
    }
  }, [db]);

  // Etiketler ekranına git
  const goToTagsScreen = () => {
    navigation.navigate('ReminderTags');
  };

  // Etiket seçimini güncelle
  const toggleTagSelection = (tagId) => {
    setSelectedTags(prevTags => {
      if (prevTags.includes(tagId)) {
        return prevTags.filter(id => id !== tagId);
      } else {
        return [...prevTags, tagId];
      }
    });
  };

  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadData();
      loadCategories();
      loadGroups();
      loadTags();
      loadPatterns();
    }, [loadData, loadCategories, loadGroups, loadTags, loadPatterns])
  );

  // Tarih değişikliği
  const handleDateChange = (event, selectedDate) => {
    setShowDatePicker(false);
    if (selectedDate) {
      setDate(selectedDate);
    }
  };

  // Saat değişikliği
  const handleTimeChange = (event, selectedTime) => {
    setShowTimePicker(false);
    if (selectedTime) {
      setTime(selectedTime);
    }
  };

  // Bitiş tarihi değişikliği
  const handleEndDateChange = (event, selectedDate) => {
    setShowEndDatePicker(false);
    if (selectedDate) {
      setRepeatEndDate(selectedDate);
    }
  };

  // Tekrarlama günü seçimi
  const toggleDay = (day) => {
    if (repeatDays.includes(day)) {
      setRepeatDays(repeatDays.filter(d => d !== day));
    } else {
      setRepeatDays([...repeatDays, day]);
    }
  };

  // Hatırlatıcıyı kaydet
  const saveReminder = async () => {
    // Form doğrulama
    if (!title.trim()) {
      Alert.alert('Hata', 'Lütfen bir başlık girin.');
      return;
    }

    try {
      setSaving(true);

      // Tarih ve saat birleştirme
      const reminderDate = new Date(date);
      reminderDate.setHours(time.getHours());
      reminderDate.setMinutes(time.getMinutes());
      reminderDate.setSeconds(0);
      reminderDate.setMilliseconds(0);

      // Özel tekrarlama deseni için veri hazırla
      let customPatternData = null;
      let customPatternTypeData = null;
      let customPatternValueData = null;

      if (repeatType === 'custom') {
        try {
          // Özel desen seçilmişse, desen bilgilerini getir
          const patterns = await reminderPatternService.getAllPatterns(db);
          if (patterns && patterns.length > 0) {
            // Varsayılan olarak ilk deseni kullan
            const selectedPattern = patterns[0];
            customPatternData = selectedPattern.id;
            customPatternTypeData = selectedPattern.pattern_type;
            customPatternValueData = selectedPattern.pattern_value;
          }
        } catch (error) {
          console.error('Özel desen bilgilerini getirme hatası:', error);
        }
      }

      // Hatırlatıcı verilerini hazırla
      const reminderData = {
        title,
        message,
        date: reminderDate,
        time: `${time.getHours().toString().padStart(2, '0')}:${time.getMinutes().toString().padStart(2, '0')}`,
        repeat_type: repeatType,
        repeat_interval: repeatInterval,
        repeat_days: repeatDays.length > 0 ? repeatDays : null,
        repeat_end_date: repeatEndDate,
        priority,
        category_id: categoryId,
        group_id: groupId,
        is_enabled: isEnabled,
        custom_pattern: customPatternData,
        custom_pattern_type: customPatternTypeData,
        custom_pattern_value: customPatternValueData,
        data: {
          isUserDefined: true
        }
      };

      if (isEditing) {
        // Hatırlatıcıyı güncelle
        await reminderService.updateReminder(db, reminderId, reminderData, selectedTags);
        Alert.alert('Başarılı', 'Hatırlatıcı güncellendi.');
      } else {
        // Yeni hatırlatıcı ekle
        await reminderService.addReminder(db, reminderData, selectedTags);
        Alert.alert('Başarılı', 'Hatırlatıcı eklendi.');
      }

      setSaving(false);

      // Navigasyon
      if (navigation?.goBack) {
        navigation.goBack();
      } else {
        router.back();
      }
    } catch (error) {
      console.error('Hatırlatıcı kaydetme hatası:', error);
      Alert.alert('Hata', 'Hatırlatıcı kaydedilirken bir hata oluştu.');
      setSaving(false);
    }
  };

  // Yükleniyor durumu
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={styles.loadingText}>Yükleniyor...</Text>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <ScrollView style={styles.content}>
        {/* Başlık */}
        <View style={styles.formGroup}>
          <Text style={styles.label}>Başlık</Text>
          <TextInput
            style={styles.input}
            value={title}
            onChangeText={setTitle}
            placeholder="Hatırlatıcı başlığı"
            maxLength={100}
          />
        </View>

        {/* Mesaj */}
        <View style={styles.formGroup}>
          <Text style={styles.label}>Mesaj</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={message}
            onChangeText={setMessage}
            placeholder="Hatırlatıcı mesajı"
            multiline
            numberOfLines={3}
            maxLength={500}
          />
        </View>

        {/* Tarih */}
        <View style={styles.formGroup}>
          <Text style={styles.label}>Tarih</Text>
          <TouchableOpacity
            style={styles.datePickerButton}
            onPress={() => setShowDatePicker(true)}
          >
            <Text style={styles.dateText}>
              {format(date, 'dd MMMM yyyy', { locale: tr })}
            </Text>
            <MaterialIcons name="event" size={24} color={Colors.PRIMARY} />
          </TouchableOpacity>
          {showDatePicker && (
            <DateTimePicker
              value={date}
              mode="date"
              display="default"
              onChange={handleDateChange}
              minimumDate={new Date()}
            />
          )}
        </View>

        {/* Saat */}
        <View style={styles.formGroup}>
          <Text style={styles.label}>Saat</Text>
          <TouchableOpacity
            style={styles.datePickerButton}
            onPress={() => setShowTimePicker(true)}
          >
            <Text style={styles.dateText}>
              {format(time, 'HH:mm')}
            </Text>
            <MaterialIcons name="access-time" size={24} color={Colors.PRIMARY} />
          </TouchableOpacity>
          {showTimePicker && (
            <DateTimePicker
              value={time}
              mode="time"
              display="default"
              onChange={handleTimeChange}
              is24Hour={true}
            />
          )}
        </View>

        {/* Tekrarlama */}
        <View style={styles.formGroup}>
          <Text style={styles.label}>Tekrarlama</Text>
          <View style={styles.repeatTypeContainer}>
            <TouchableOpacity
              style={[
                styles.repeatTypeButton,
                repeatType === 'once' && styles.repeatTypeButtonActive
              ]}
              onPress={() => setRepeatType('once')}
            >
              <Text style={[
                styles.repeatTypeText,
                repeatType === 'once' && styles.repeatTypeTextActive
              ]}>
                Bir Kez
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.repeatTypeButton,
                repeatType === 'daily' && styles.repeatTypeButtonActive
              ]}
              onPress={() => setRepeatType('daily')}
            >
              <Text style={[
                styles.repeatTypeText,
                repeatType === 'daily' && styles.repeatTypeTextActive
              ]}>
                Günlük
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.repeatTypeButton,
                repeatType === 'weekly' && styles.repeatTypeButtonActive
              ]}
              onPress={() => setRepeatType('weekly')}
            >
              <Text style={[
                styles.repeatTypeText,
                repeatType === 'weekly' && styles.repeatTypeTextActive
              ]}>
                Haftalık
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.repeatTypeButton,
                repeatType === 'monthly' && styles.repeatTypeButtonActive
              ]}
              onPress={() => setRepeatType('monthly')}
            >
              <Text style={[
                styles.repeatTypeText,
                repeatType === 'monthly' && styles.repeatTypeTextActive
              ]}>
                Aylık
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.repeatTypeButton,
                repeatType === 'custom' && styles.repeatTypeButtonActive
              ]}
              onPress={() => {
                setRepeatType('custom');
                navigation.navigate('ReminderPatterns');
              }}
            >
              <Text style={[
                styles.repeatTypeText,
                repeatType === 'custom' && styles.repeatTypeTextActive
              ]}>
                Özel
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Tekrarlama aralığı */}
        {repeatType !== 'once' && (
          <View style={styles.formGroup}>
            <Text style={styles.label}>Tekrarlama Aralığı</Text>
            <View style={styles.intervalContainer}>
              <TouchableOpacity
                style={styles.intervalButton}
                onPress={() => setRepeatInterval(Math.max(1, repeatInterval - 1))}
              >
                <MaterialIcons name="remove" size={24} color={Colors.PRIMARY} />
              </TouchableOpacity>
              <Text style={styles.intervalText}>{repeatInterval}</Text>
              <TouchableOpacity
                style={styles.intervalButton}
                onPress={() => setRepeatInterval(repeatInterval + 1)}
              >
                <MaterialIcons name="add" size={24} color={Colors.PRIMARY} />
              </TouchableOpacity>
              <Text style={styles.intervalLabel}>
                {repeatType === 'daily' ? 'Gün' :
                 repeatType === 'weekly' ? 'Hafta' :
                 repeatType === 'monthly' ? 'Ay' : 'Yıl'}
              </Text>
            </View>
          </View>
        )}

        {/* Haftalık tekrarlama için günler */}
        {repeatType === 'weekly' && (
          <View style={styles.formGroup}>
            <Text style={styles.label}>Günler</Text>
            <View style={styles.daysContainer}>
              {['Pzt', 'Sal', 'Çar', 'Per', 'Cum', 'Cmt', 'Paz'].map((day, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.dayButton,
                    repeatDays.includes(index + 1) && styles.dayButtonActive
                  ]}
                  onPress={() => toggleDay(index + 1)}
                >
                  <Text style={[
                    styles.dayText,
                    repeatDays.includes(index + 1) && styles.dayTextActive
                  ]}>
                    {day}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        )}

        {/* Tekrarlama bitiş tarihi */}
        {repeatType !== 'once' && (
          <View style={styles.formGroup}>
            <View style={styles.endDateHeader}>
              <Text style={styles.label}>Bitiş Tarihi</Text>
              <Switch
                value={!!repeatEndDate}
                onValueChange={(value) => {
                  if (value) {
                    // Varsayılan olarak 1 ay sonrasını ayarla
                    const endDate = new Date();
                    endDate.setMonth(endDate.getMonth() + 1);
                    setRepeatEndDate(endDate);
                  } else {
                    setRepeatEndDate(null);
                  }
                }}
                trackColor={{ false: Colors.GRAY_300, true: Colors.PRIMARY_LIGHT }}
                thumbColor={repeatEndDate ? Colors.PRIMARY : Colors.GRAY_500}
              />
            </View>
            {repeatEndDate && (
              <TouchableOpacity
                style={styles.datePickerButton}
                onPress={() => setShowEndDatePicker(true)}
              >
                <Text style={styles.dateText}>
                  {format(repeatEndDate, 'dd MMMM yyyy', { locale: tr })}
                </Text>
                <MaterialIcons name="event" size={24} color={Colors.PRIMARY} />
              </TouchableOpacity>
            )}
            {showEndDatePicker && (
              <DateTimePicker
                value={repeatEndDate || new Date()}
                mode="date"
                display="default"
                onChange={handleEndDateChange}
                minimumDate={new Date()}
              />
            )}
          </View>
        )}

        {/* Öncelik */}
        <View style={styles.formGroup}>
          <Text style={styles.label}>Öncelik</Text>
          <View style={styles.priorityContainer}>
            <TouchableOpacity
              style={[
                styles.priorityButton,
                priority === 'low' && styles.priorityButtonLow
              ]}
              onPress={() => setPriority('low')}
            >
              <MaterialIcons
                name="arrow-downward"
                size={20}
                color={priority === 'low' ? '#fff' : Colors.GRAY_500}
              />
              <Text style={[
                styles.priorityText,
                priority === 'low' && styles.priorityTextActive
              ]}>
                Düşük
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.priorityButton,
                priority === 'normal' && styles.priorityButtonNormal
              ]}
              onPress={() => setPriority('normal')}
            >
              <MaterialIcons
                name="remove"
                size={20}
                color={priority === 'normal' ? '#fff' : Colors.GRAY_500}
              />
              <Text style={[
                styles.priorityText,
                priority === 'normal' && styles.priorityTextActive
              ]}>
                Normal
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.priorityButton,
                priority === 'high' && styles.priorityButtonHigh
              ]}
              onPress={() => setPriority('high')}
            >
              <MaterialIcons
                name="arrow-upward"
                size={20}
                color={priority === 'high' ? '#fff' : Colors.GRAY_500}
              />
              <Text style={[
                styles.priorityText,
                priority === 'high' && styles.priorityTextActive
              ]}>
                Yüksek
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Kategori */}
        <View style={styles.formGroup}>
          <Text style={styles.label}>Kategori</Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.categoriesContainer}
          >
            <TouchableOpacity
              style={[
                styles.categoryButton,
                !categoryId && styles.categoryButtonActive
              ]}
              onPress={() => setCategoryId(null)}
            >
              <Text style={[
                styles.categoryText,
                !categoryId && styles.categoryTextActive
              ]}>
                Genel
              </Text>
            </TouchableOpacity>
            {categories.map(category => (
              <TouchableOpacity
                key={category.id}
                style={[
                  styles.categoryButton,
                  categoryId === category.id && styles.categoryButtonActive,
                  { backgroundColor: categoryId === category.id ? category.color : 'transparent' }
                ]}
                onPress={() => setCategoryId(category.id)}
              >
                <Text style={[
                  styles.categoryText,
                  categoryId === category.id && styles.categoryTextActive
                ]}>
                  {category.name}
                </Text>
              </TouchableOpacity>
            ))}
            <TouchableOpacity
              style={[
                styles.categoryButton,
                styles.newCategoryButton
              ]}
              onPress={() => showAddCategoryModal()}
            >
              <MaterialIcons name="add" size={16} color={Colors.PRIMARY} />
              <Text style={[
                styles.categoryText,
                { color: Colors.PRIMARY }
              ]}>
                Yeni Ekle
              </Text>
            </TouchableOpacity>
          </ScrollView>
        </View>

        {/* Grup */}
        <View style={styles.formGroup}>
          <Text style={styles.label}>Grup</Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.categoriesContainer}
          >
            {groups.map(group => (
              <TouchableOpacity
                key={group.id}
                style={[
                  styles.categoryButton,
                  groupId === group.id && styles.categoryButtonActive,
                  {
                    backgroundColor: groupId === group.id ? group.color : 'transparent',
                    borderColor: group.color,
                    borderWidth: groupId !== group.id ? 1 : 0
                  }
                ]}
                onPress={() => setGroupId(group.id)}
              >
                <View style={styles.groupButtonContent}>
                  <MaterialIcons
                    name={group.icon || 'folder'}
                    size={16}
                    color={groupId === group.id ? '#fff' : group.color}
                    style={styles.groupIcon}
                  />
                  <Text style={[
                    styles.categoryText,
                    groupId === group.id && styles.categoryTextActive
                  ]}>
                    {group.name}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
            <TouchableOpacity
              style={[
                styles.categoryButton,
                styles.newCategoryButton
              ]}
              onPress={() => navigation.navigate('ReminderGroups')}
            >
              <MaterialIcons name="settings" size={16} color={Colors.PRIMARY} />
              <Text style={[
                styles.categoryText,
                { color: Colors.PRIMARY }
              ]}>
                Grupları Yönet
              </Text>
            </TouchableOpacity>
          </ScrollView>
        </View>

        {/* Etiketler */}
        <View style={styles.formGroup}>
          <Text style={styles.label}>Etiketler</Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.categoriesContainer}
          >
            {tags.map(tag => (
              <TouchableOpacity
                key={tag.id}
                style={[
                  styles.tagButton,
                  selectedTags.includes(tag.id) && styles.tagButtonActive,
                  {
                    backgroundColor: selectedTags.includes(tag.id) ? tag.color : 'transparent',
                    borderColor: tag.color,
                    borderWidth: !selectedTags.includes(tag.id) ? 1 : 0
                  }
                ]}
                onPress={() => toggleTagSelection(tag.id)}
              >
                <Text style={[
                  styles.tagText,
                  selectedTags.includes(tag.id) && styles.tagTextActive
                ]}>
                  {tag.name}
                </Text>
              </TouchableOpacity>
            ))}
            <TouchableOpacity
              style={[
                styles.categoryButton,
                styles.newCategoryButton
              ]}
              onPress={() => navigation.navigate('ReminderTags')}
            >
              <MaterialIcons name="label" size={16} color={Colors.PRIMARY} />
              <Text style={[
                styles.categoryText,
                { color: Colors.PRIMARY }
              ]}>
                Etiketleri Yönet
              </Text>
            </TouchableOpacity>
          </ScrollView>
        </View>

        {/* Etkinleştirme */}
        {isEditing && (
          <View style={styles.formGroup}>
            <View style={styles.enabledContainer}>
              <Text style={styles.label}>Etkinleştir</Text>
              <Switch
                value={isEnabled}
                onValueChange={setIsEnabled}
                trackColor={{ false: Colors.GRAY_300, true: Colors.PRIMARY_LIGHT }}
                thumbColor={isEnabled ? Colors.PRIMARY : Colors.GRAY_500}
              />
            </View>
          </View>
        )}

        {/* Kaydet Butonu */}
        <TouchableOpacity
          style={styles.saveButton}
          onPress={saveReminder}
          disabled={saving}
        >
          {saving ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <>
              <MaterialIcons name="save" size={20} color="#fff" />
              <Text style={styles.saveButtonText}>
                {isEditing ? 'Güncelle' : 'Kaydet'}
              </Text>
            </>
          )}
        </TouchableOpacity>
      </ScrollView>

      {/* Kategori Ekleme Modalı */}
      <Modal
        visible={showCategoryModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowCategoryModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Yeni Kategori Ekle</Text>
              <TouchableOpacity
                style={styles.modalCloseButton}
                onPress={() => setShowCategoryModal(false)}
              >
                <MaterialIcons name="close" size={24} color="#333" />
              </TouchableOpacity>
            </View>

            <View style={styles.modalContent}>
              {/* Kategori Adı */}
              <View style={styles.formGroup}>
                <Text style={styles.label}>Kategori Adı</Text>
                <TextInput
                  style={styles.input}
                  value={newCategoryName}
                  onChangeText={setNewCategoryName}
                  placeholder="Kategori adı girin"
                  maxLength={50}
                />
              </View>

              {/* Kategori Rengi */}
              <View style={styles.formGroup}>
                <Text style={styles.label}>Renk</Text>
                <View style={styles.colorPickerContainer}>
                  {['#3498db', '#2ecc71', '#e74c3c', '#f39c12', '#9b59b6', '#1abc9c', '#e67e22', '#34495e'].map(color => (
                    <TouchableOpacity
                      key={color}
                      style={[
                        styles.colorOption,
                        { backgroundColor: color },
                        newCategoryColor === color && styles.colorOptionSelected
                      ]}
                      onPress={() => setNewCategoryColor(color)}
                    >
                      {newCategoryColor === color && (
                        <MaterialIcons name="check" size={16} color="#fff" />
                      )}
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              {/* Kategori Tipi */}
              <View style={styles.formGroup}>
                <Text style={styles.label}>Kategori Tipi</Text>
                <View style={styles.categoryTypeContainer}>
                  <TouchableOpacity
                    style={[
                      styles.categoryTypeButton,
                      newCategoryType === 'income' && styles.categoryTypeButtonActive
                    ]}
                    onPress={() => setNewCategoryType('income')}
                  >
                    <MaterialIcons
                      name="arrow-upward"
                      size={16}
                      color={newCategoryType === 'income' ? '#fff' : '#333'}
                    />
                    <Text style={[
                      styles.categoryTypeText,
                      newCategoryType === 'income' && styles.categoryTypeTextActive
                    ]}>
                      Gelir
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.categoryTypeButton,
                      newCategoryType === 'expense' && styles.categoryTypeButtonActive
                    ]}
                    onPress={() => setNewCategoryType('expense')}
                  >
                    <MaterialIcons
                      name="arrow-downward"
                      size={16}
                      color={newCategoryType === 'expense' ? '#fff' : '#333'}
                    />
                    <Text style={[
                      styles.categoryTypeText,
                      newCategoryType === 'expense' && styles.categoryTypeTextActive
                    ]}>
                      Gider
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.categoryTypeButton,
                      newCategoryType === 'both' && styles.categoryTypeButtonActive
                    ]}
                    onPress={() => setNewCategoryType('both')}
                  >
                    <MaterialIcons
                      name="swap-vert"
                      size={16}
                      color={newCategoryType === 'both' ? '#fff' : '#333'}
                    />
                    <Text style={[
                      styles.categoryTypeText,
                      newCategoryType === 'both' && styles.categoryTypeTextActive
                    ]}>
                      Her İkisi
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={styles.modalCancelButton}
                onPress={() => setShowCategoryModal(false)}
              >
                <Text style={styles.modalCancelButtonText}>İptal</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.modalSaveButton}
                onPress={addNewCategory}
                disabled={savingCategory}
              >
                {savingCategory ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <Text style={styles.modalSaveButtonText}>Kaydet</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: Colors.PRIMARY,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: Colors.TEXT_DARK,
  },
  input: {
    borderWidth: 1,
    borderColor: Colors.GRAY_300,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: Colors.GRAY_100,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  datePickerButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.GRAY_300,
    borderRadius: 8,
    padding: 12,
    backgroundColor: Colors.GRAY_100,
  },
  dateText: {
    fontSize: 16,
    color: Colors.TEXT_DARK,
  },
  repeatTypeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  repeatTypeButton: {
    flex: 1,
    paddingVertical: 10,
    paddingHorizontal: 5,
    borderWidth: 1,
    borderColor: Colors.GRAY_300,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 2,
    backgroundColor: Colors.GRAY_100,
  },
  repeatTypeButtonActive: {
    backgroundColor: Colors.PRIMARY,
    borderColor: Colors.PRIMARY,
  },
  repeatTypeText: {
    fontSize: 14,
    color: Colors.TEXT_DARK,
  },
  repeatTypeTextActive: {
    color: '#fff',
    fontWeight: 'bold',
  },
  intervalContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  intervalButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.GRAY_100,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: Colors.GRAY_300,
  },
  intervalText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginHorizontal: 16,
    color: Colors.TEXT_DARK,
  },
  intervalLabel: {
    fontSize: 16,
    marginLeft: 8,
    color: Colors.TEXT_DARK,
  },
  daysContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  dayButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.GRAY_100,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: Colors.GRAY_300,
  },
  dayButtonActive: {
    backgroundColor: Colors.PRIMARY,
    borderColor: Colors.PRIMARY,
  },
  dayText: {
    fontSize: 12,
    color: Colors.TEXT_DARK,
  },
  dayTextActive: {
    color: '#fff',
    fontWeight: 'bold',
  },
  endDateHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  priorityContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  priorityButton: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 10,
    borderWidth: 1,
    borderColor: Colors.GRAY_300,
    borderRadius: 8,
    marginHorizontal: 4,
    backgroundColor: Colors.GRAY_100,
  },
  priorityButtonLow: {
    backgroundColor: Colors.SUCCESS,
    borderColor: Colors.SUCCESS,
  },
  priorityButtonNormal: {
    backgroundColor: Colors.WARNING,
    borderColor: Colors.WARNING,
  },
  priorityButtonHigh: {
    backgroundColor: Colors.DANGER,
    borderColor: Colors.DANGER,
  },
  priorityText: {
    fontSize: 14,
    marginLeft: 4,
    color: Colors.TEXT_DARK,
  },
  priorityTextActive: {
    color: '#fff',
    fontWeight: 'bold',
  },
  categoriesContainer: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  categoryButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: Colors.GRAY_300,
    borderRadius: 20,
    marginRight: 8,
    backgroundColor: Colors.GRAY_100,
  },
  categoryButtonActive: {
    backgroundColor: Colors.PRIMARY,
    borderColor: Colors.PRIMARY,
  },
  categoryText: {
    fontSize: 14,
    color: Colors.TEXT_DARK,
  },
  categoryTextActive: {
    color: '#fff',
    fontWeight: 'bold',
  },
  enabledContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  tagButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    marginRight: 8,
    borderWidth: 1,
    borderColor: Colors.PRIMARY,
  },
  tagButtonActive: {
    backgroundColor: Colors.PRIMARY,
  },
  tagText: {
    fontSize: 14,
    color: Colors.TEXT_DARK,
  },
  tagTextActive: {
    color: '#fff',
  },
  saveButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
    borderRadius: 8,
    padding: 16,
    marginTop: 16,
    marginBottom: 32,
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  newCategoryButton: {
    borderStyle: 'dashed',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  // Modal stilleri
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    width: '100%',
    maxWidth: 500,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
  },
  modalCloseButton: {
    padding: 4,
  },
  modalContent: {
    padding: 16,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.GRAY_200,
  },
  modalCancelButton: {
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginRight: 8,
  },
  modalCancelButtonText: {
    color: Colors.TEXT_DARK,
    fontSize: 16,
  },
  modalSaveButton: {
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 80,
  },
  modalSaveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  colorPickerContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  colorOption: {
    width: 40,
    height: 40,
    borderRadius: 20,
    margin: 8,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.GRAY_300,
  },
  colorOptionSelected: {
    borderWidth: 2,
    borderColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
    elevation: 2,
  },
  categoryTypeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  categoryTypeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 8,
    borderWidth: 1,
    borderColor: Colors.GRAY_300,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  categoryTypeButtonActive: {
    backgroundColor: Colors.PRIMARY,
    borderColor: Colors.PRIMARY,
  },
  categoryTypeText: {
    fontSize: 14,
    color: Colors.TEXT_DARK,
    marginLeft: 4,
  },
  categoryTypeTextActive: {
    color: '#fff',
    fontWeight: 'bold',
  },
  groupButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  groupIcon: {
    marginRight: 4,
  },
});
