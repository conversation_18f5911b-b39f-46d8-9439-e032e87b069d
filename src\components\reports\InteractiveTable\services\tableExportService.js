/**
 * Tablo dışa aktarma servisi - PDF, Excel, CSV formatlarında dışa aktarma
 */

import * as FileSystem from 'expo-file-system';
// import * as Sharing from 'expo-sharing';
import { Alert } from 'react-native';
import { formatCurrency, autoFormatCellValue } from '../utils/tableFormatters';

/**
 * Tablo dışa aktarma servisi sınıfı
 */
class TableExportService {
  constructor() {
    this.exportHistory = [];
  }

  /**
   * Ana dışa aktarma fonksiyonu
   * @param {Array} data - Tablo verisi
   * @param {Array} columns - Sütun yapılandırması
   * @param {string} format - Dışa aktarma formatı (PDF, Excel, CSV)
   * @param {Object} options - Dışa aktarma seçenekleri
   * @returns {Promise<boolean>} Başarı durumu
   */
  async exportTable(data, columns, format, options = {}) {
    try {
      const exportOptions = {
        filename: options.filename || `tablo_${new Date().getTime()}`,
        includeHeaders: options.includeHeaders !== false,
        includeFilters: options.includeFilters || false,
        includeSummary: options.includeSummary || false,
        title: options.title || 'Tablo Raporu',
        subtitle: options.subtitle || '',
        orientation: options.orientation || 'landscape',
        pageSize: options.pageSize || 'A4',
        ...options
      };

      let result = false;

      switch (format.toLowerCase()) {
        case 'csv':
          result = await this.exportToCSV(data, columns, exportOptions);
          break;
        case 'excel':
          result = await this.exportToExcel(data, columns, exportOptions);
          break;
        case 'pdf':
          result = await this.exportToPDF(data, columns, exportOptions);
          break;
        default:
          throw new Error(`Desteklenmeyen format: ${format}`);
      }

      if (result) {
        // Dışa aktarma geçmişini kaydet
        this.addToHistory({
          format,
          filename: exportOptions.filename,
          timestamp: new Date(),
          rowCount: data.length,
          columnCount: columns.length,
        });
      }

      return result;
    } catch (error) {
      Alert.alert('Hata', `Dışa aktarma sırasında hata oluştu: ${error.message}`);
      return false;
    }
  }

  /**
   * CSV formatında dışa aktarma
   * @param {Array} data - Tablo verisi
   * @param {Array} columns - Sütun yapılandırması
   * @param {Object} options - Dışa aktarma seçenekleri
   * @returns {Promise<boolean>} Başarı durumu
   */
  async exportToCSV(data, columns, options) {
    try {
      const visibleColumns = columns.filter(col => col.visible !== false);
      let csvContent = '';

      // Başlık ekleme
      if (options.includeHeaders) {
        const headers = visibleColumns.map(col => this.escapeCSV(col.name));
        csvContent += headers.join(',') + '\n';
      }

      // Veri satırları
      data.forEach(row => {
        const values = visibleColumns.map(col => {
          const value = row[col.id];
          const formattedValue = autoFormatCellValue(value, col);
          return this.escapeCSV(formattedValue);
        });
        csvContent += values.join(',') + '\n';
      });

      // Dosyayı kaydet
      const filename = `${options.filename}.csv`;
      const fileUri = FileSystem.documentDirectory + filename;
      
      await FileSystem.writeAsStringAsync(fileUri, csvContent, {
        encoding: FileSystem.EncodingType.UTF8,
      });

      // Paylaş
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri, {
          mimeType: 'text/csv',
          dialogTitle: 'CSV Dosyasını Paylaş',
        });
      }

      Alert.alert('Başarılı', 'CSV dosyası başarıyla oluşturuldu');
      return true;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Excel formatında dışa aktarma (XLSX)
   * @param {Array} data - Tablo verisi
   * @param {Array} columns - Sütun yapılandırması
   * @param {Object} options - Dışa aktarma seçenekleri
   * @returns {Promise<boolean>} Başarı durumu
   */
  async exportToExcel(data, columns, options) {
    try {
      // Excel kütüphanesi olmadığı için CSV benzeri format oluştur
      // Gerçek uygulamada xlsx kütüphanesi kullanılabilir
      const visibleColumns = columns.filter(col => col.visible !== false);
      let xmlContent = this.createExcelXML(data, visibleColumns, options);

      // Dosyayı kaydet
      const filename = `${options.filename}.xls`;
      const fileUri = FileSystem.documentDirectory + filename;
      
      await FileSystem.writeAsStringAsync(fileUri, xmlContent, {
        encoding: FileSystem.EncodingType.UTF8,
      });

      // Paylaş
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri, {
          mimeType: 'application/vnd.ms-excel',
          dialogTitle: 'Excel Dosyasını Paylaş',
        });
      }

      Alert.alert('Başarılı', 'Excel dosyası başarıyla oluşturuldu');
      return true;
    } catch (error) {
      throw error;
    }
  }

  /**
   * PDF formatında dışa aktarma
   * @param {Array} data - Tablo verisi
   * @param {Array} columns - Sütun yapılandırması
   * @param {Object} options - Dışa aktarma seçenekleri
   * @returns {Promise<boolean>} Başarı durumu
   */
  async exportToPDF(data, columns, options) {
    try {
      // PDF oluşturma için HTML template kullan
      const htmlContent = this.createPDFHTML(data, columns, options);
      
      // HTML dosyasını kaydet (gerçek uygulamada PDF kütüphanesi kullanılabilir)
      const filename = `${options.filename}.html`;
      const fileUri = FileSystem.documentDirectory + filename;
      
      await FileSystem.writeAsStringAsync(fileUri, htmlContent, {
        encoding: FileSystem.EncodingType.UTF8,
      });

      // Paylaş
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri, {
          mimeType: 'text/html',
          dialogTitle: 'PDF Raporu Paylaş',
        });
      }

      Alert.alert('Başarılı', 'PDF raporu başarıyla oluşturuldu');
      return true;
    } catch (error) {
      throw error;
    }
  }

  /**
   * CSV değerlerini escape etme
   * @param {string} value - Escape edilecek değer
   * @returns {string} Escape edilmiş değer
   */
  escapeCSV(value) {
    if (value === null || value === undefined) return '';
    
    const str = value.toString();
    
    // Virgül, çift tırnak veya yeni satır içeriyorsa çift tırnak içine al
    if (str.includes(',') || str.includes('"') || str.includes('\n')) {
      return `"${str.replace(/"/g, '""')}"`;
    }
    
    return str;
  }

  /**
   * Excel XML içeriği oluşturma
   * @param {Array} data - Tablo verisi
   * @param {Array} columns - Sütun yapılandırması
   * @param {Object} options - Seçenekler
   * @returns {string} XML içeriği
   */
  createExcelXML(data, columns, options) {
    let xml = `<?xml version="1.0"?>
<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:o="urn:schemas-microsoft-com:office:office"
 xmlns:x="urn:schemas-microsoft-com:office:excel"
 xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:html="http://www.w3.org/TR/REC-html40">
<Worksheet ss:Name="${options.title}">
<Table>`;

    // Başlık satırı
    if (options.includeHeaders) {
      xml += '<Row>';
      columns.forEach(col => {
        xml += `<Cell><Data ss:Type="String">${this.escapeXML(col.name)}</Data></Cell>`;
      });
      xml += '</Row>';
    }

    // Veri satırları
    data.forEach(row => {
      xml += '<Row>';
      columns.forEach(col => {
        const value = row[col.id];
        const formattedValue = autoFormatCellValue(value, col);
        const dataType = this.getExcelDataType(col.type);
        xml += `<Cell><Data ss:Type="${dataType}">${this.escapeXML(formattedValue)}</Data></Cell>`;
      });
      xml += '</Row>';
    });

    xml += '</Table></Worksheet></Workbook>';
    return xml;
  }

  /**
   * Excel veri tipi belirleme
   * @param {string} type - Sütun tipi
   * @returns {string} Excel veri tipi
   */
  getExcelDataType(type) {
    switch (type) {
      case 'number':
      case 'currency':
        return 'Number';
      case 'date':
      case 'datetime':
        return 'DateTime';
      case 'boolean':
        return 'Boolean';
      default:
        return 'String';
    }
  }

  /**
   * XML karakterlerini escape etme
   * @param {string} value - Escape edilecek değer
   * @returns {string} Escape edilmiş değer
   */
  escapeXML(value) {
    if (value === null || value === undefined) return '';
    
    return value.toString()
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&apos;');
  }

  /**
   * PDF için HTML içeriği oluşturma
   * @param {Array} data - Tablo verisi
   * @param {Array} columns - Sütun yapılandırması
   * @param {Object} options - Seçenekler
   * @returns {string} HTML içeriği
   */
  createPDFHTML(data, columns, options) {
    const visibleColumns = columns.filter(col => col.visible !== false);
    
    let html = `<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>${options.title}</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            font-size: 12px; 
        }
        .header { 
            text-align: center; 
            margin-bottom: 20px; 
        }
        .title { 
            font-size: 18px; 
            font-weight: bold; 
            margin-bottom: 5px; 
        }
        .subtitle { 
            font-size: 14px; 
            color: #666; 
            margin-bottom: 10px; 
        }
        .meta { 
            font-size: 10px; 
            color: #999; 
        }
        table { 
            width: 100%; 
            border-collapse: collapse; 
            margin-top: 20px; 
        }
        th, td { 
            border: 1px solid #ddd; 
            padding: 6px; 
            text-align: left; 
        }
        th { 
            background-color: #f5f5f5; 
            font-weight: bold; 
        }
        .number { text-align: right; }
        .center { text-align: center; }
        .footer { 
            margin-top: 20px; 
            text-align: center; 
            font-size: 10px; 
            color: #999; 
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="title">${options.title}</div>
        ${options.subtitle ? `<div class="subtitle">${options.subtitle}</div>` : ''}
        <div class="meta">
            Oluşturulma Tarihi: ${new Date().toLocaleDateString('tr-TR')} | 
            Kayıt Sayısı: ${data.length} | 
            Sütun Sayısı: ${visibleColumns.length}
        </div>
    </div>
    
    <table>`;

    // Başlık satırı
    if (options.includeHeaders) {
      html += '<thead><tr>';
      visibleColumns.forEach(col => {
        const alignClass = col.type === 'number' || col.type === 'currency' ? 'number' : '';
        html += `<th class="${alignClass}">${col.name}</th>`;
      });
      html += '</tr></thead>';
    }

    // Veri satırları
    html += '<tbody>';
    data.forEach((row, index) => {
      html += `<tr${index % 2 === 1 ? ' style="background-color: #f9f9f9;"' : ''}>`;
      visibleColumns.forEach(col => {
        const value = row[col.id];
        const formattedValue = autoFormatCellValue(value, col);
        const alignClass = col.type === 'number' || col.type === 'currency' ? 'number' : '';
        html += `<td class="${alignClass}">${formattedValue}</td>`;
      });
      html += '</tr>';
    });
    html += '</tbody></table>';

    // Özet bilgileri
    if (options.includeSummary) {
      html += this.createSummaryHTML(data, visibleColumns);
    }

    html += `
    <div class="footer">
        Bu rapor sistem tarafından otomatik olarak oluşturulmuştur.
    </div>
</body>
</html>`;

    return html;
  }

  /**
   * Özet bilgileri HTML'i oluşturma
   * @param {Array} data - Tablo verisi
   * @param {Array} columns - Sütun yapılandırması
   * @returns {string} Özet HTML'i
   */
  createSummaryHTML(data, columns) {
    const numericColumns = columns.filter(col => 
      col.type === 'number' || col.type === 'currency'
    );

    if (numericColumns.length === 0) return '';

    let html = '<h3>Özet İstatistikleri</h3><table style="width: auto;">';
    html += '<thead><tr><th>Sütun</th><th>Toplam</th><th>Ortalama</th><th>Min</th><th>Max</th></tr></thead>';
    html += '<tbody>';

    numericColumns.forEach(col => {
      const values = data
        .map(row => parseFloat(row[col.id]))
        .filter(val => !isNaN(val));

      if (values.length > 0) {
        const sum = values.reduce((acc, val) => acc + val, 0);
        const avg = sum / values.length;
        const min = Math.min(...values);
        const max = Math.max(...values);

        html += '<tr>';
        html += `<td>${col.name}</td>`;
        html += `<td class="number">${col.type === 'currency' ? formatCurrency(sum) : sum.toFixed(2)}</td>`;
        html += `<td class="number">${col.type === 'currency' ? formatCurrency(avg) : avg.toFixed(2)}</td>`;
        html += `<td class="number">${col.type === 'currency' ? formatCurrency(min) : min.toFixed(2)}</td>`;
        html += `<td class="number">${col.type === 'currency' ? formatCurrency(max) : max.toFixed(2)}</td>`;
        html += '</tr>';
      }
    });

    html += '</tbody></table>';
    return html;
  }

  /**
   * Dışa aktarma geçmişine ekleme
   * @param {Object} exportInfo - Dışa aktarma bilgisi
   */
  addToHistory(exportInfo) {
    this.exportHistory.unshift(exportInfo);
    
    // Geçmişi 50 kayıtla sınırla
    if (this.exportHistory.length > 50) {
      this.exportHistory = this.exportHistory.slice(0, 50);
    }
  }

  /**
   * Dışa aktarma geçmişini getir
   * @returns {Array} Dışa aktarma geçmişi
   */
  getExportHistory() {
    return [...this.exportHistory];
  }

  /**
   * Dışa aktarma geçmişini temizle
   */
  clearHistory() {
    this.exportHistory = [];
  }

  /**
   * Desteklenen formatları getir
   * @returns {Array} Desteklenen formatlar
   */
  getSupportedFormats() {
    return [
      {
        key: 'csv',
        name: 'CSV',
        description: 'Virgülle ayrılmış değerler',
        extension: '.csv',
        mimeType: 'text/csv',
      },
      {
        key: 'excel',
        name: 'Excel',
        description: 'Microsoft Excel dosyası',
        extension: '.xls',
        mimeType: 'application/vnd.ms-excel',
      },
      {
        key: 'pdf',
        name: 'PDF',
        description: 'Taşınabilir belge formatı',
        extension: '.pdf',
        mimeType: 'application/pdf',
      },
    ];
  }
}

// Singleton instance
const tableExportService = new TableExportService();

export default tableExportService;
