import { Dimensions } from 'react-native';
import tokens from '../tokens';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

/**
 * Responsive Design Utilities
 * Ekran boyutuna göre responsive değerler hesaplar
 */

/**
 * Ekran boyutuna göre değer döndürür
 * @param {Object} values - Breakpoint değerleri { sm: value, md: value, lg: value }
 * @param {*} defaultValue - Varsayılan değer
 * @returns {*} Responsive değer
 */
export const responsive = (values, defaultValue = null) => {
  const { sm, md, lg, xl } = values;

  if (screenWidth >= tokens.breakpoints.xl && xl !== undefined) {
    return xl;
  }
  if (screenWidth >= tokens.breakpoints.lg && lg !== undefined) {
    return lg;
  }
  if (screenWidth >= tokens.breakpoints.md && md !== undefined) {
    return md;
  }
  if (screenWidth >= tokens.breakpoints.sm && sm !== undefined) {
    return sm;
  }

  return defaultValue;
};

/**
 * Responsive spacing
 * @param {Object} values - Spacing değerleri
 * @returns {number} Responsive spacing değeri
 */
export const responsiveSpacing = (values) => {
  const value = responsive(values, values.base || 0);
  return tokens.spacing[value] || value;
};

/**
 * Responsive font size
 * @param {Object} values - Font size değerleri
 * @returns {number} Responsive font size
 */
export const responsiveFontSize = (values) => {
  const value = responsive(values, values.base || 'base');
  return tokens.typography.fontSize[value] || value;
};

/**
 * Responsive width
 * @param {Object} values - Width değerleri (yüzde veya piksel)
 * @returns {string|number} Responsive width
 */
export const responsiveWidth = (values) => {
  return responsive(values, values.base || '100%');
};

/**
 * Responsive height
 * @param {Object} values - Height değerleri
 * @returns {string|number} Responsive height
 */
export const responsiveHeight = (values) => {
  return responsive(values, values.base || 'auto');
};

/**
 * Responsive grid columns
 * @param {Object} values - Column sayıları
 * @returns {number} Column sayısı
 */
export const responsiveColumns = (values) => {
  return responsive(values, values.base || 1);
};

/**
 * Breakpoint kontrolleri
 */
export const breakpoints = {
  isSmall: screenWidth < tokens.breakpoints.sm,
  isMedium: screenWidth >= tokens.breakpoints.sm && screenWidth < tokens.breakpoints.lg,
  isLarge: screenWidth >= tokens.breakpoints.lg && screenWidth < tokens.breakpoints.xl,
  isExtraLarge: screenWidth >= tokens.breakpoints.xl,
  
  // Convenience methods
  up: (breakpoint) => screenWidth >= tokens.breakpoints[breakpoint],
  down: (breakpoint) => screenWidth < tokens.breakpoints[breakpoint],
  between: (min, max) => 
    screenWidth >= tokens.breakpoints[min] && screenWidth < tokens.breakpoints[max],
};

/**
 * Ekran boyutu bilgileri
 */
export const screen = {
  width: screenWidth,
  height: screenHeight,
  isPortrait: screenHeight > screenWidth,
  isLandscape: screenWidth > screenHeight,
  aspectRatio: screenWidth / screenHeight,
};

/**
 * Grid sistem yardımcıları
 */
export const grid = {
  /**
   * Grid container stilleri
   * @param {number} columns - Column sayısı
   * @param {number} gap - Grid gap
   * @returns {Object} Grid container stilleri
   */
  container: (columns = 12, gap = tokens.spacing[4]) => ({
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -gap / 2,
  }),

  /**
   * Grid item stilleri
   * @param {number} span - Kaç column kaplayacağı
   * @param {number} columns - Toplam column sayısı
   * @param {number} gap - Grid gap
   * @returns {Object} Grid item stilleri
   */
  item: (span = 1, columns = 12, gap = tokens.spacing[4]) => {
    const percentage = (span / columns) * 100;
    return {
      width: `${percentage}%`,
      paddingHorizontal: gap / 2,
    };
  },

  /**
   * Responsive grid item
   * @param {Object} spans - Responsive span değerleri
   * @param {number} columns - Toplam column sayısı
   * @param {number} gap - Grid gap
   * @returns {Object} Responsive grid item stilleri
   */
  responsiveItem: (spans, columns = 12, gap = tokens.spacing[4]) => {
    const span = responsive(spans, spans.base || 1);
    return grid.item(span, columns, gap);
  },
};

/**
 * Flexbox yardımcıları
 */
export const flex = {
  center: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  centerVertical: {
    justifyContent: 'center',
  },
  centerHorizontal: {
    alignItems: 'center',
  },
  spaceBetween: {
    justifyContent: 'space-between',
  },
  spaceAround: {
    justifyContent: 'space-around',
  },
  spaceEvenly: {
    justifyContent: 'space-evenly',
  },
  row: {
    flexDirection: 'row',
  },
  column: {
    flexDirection: 'column',
  },
  wrap: {
    flexWrap: 'wrap',
  },
  nowrap: {
    flexWrap: 'nowrap',
  },
};

/**
 * Position yardımcıları
 */
export const position = {
  absolute: {
    position: 'absolute',
  },
  relative: {
    position: 'relative',
  },
  absoluteFill: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  absoluteCenter: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -50 }, { translateY: -50 }],
  },
};

/**
 * Responsive style creator
 * @param {Function} styleFunction - Style üreten fonksiyon
 * @returns {Object} Responsive stiller
 */
export const createResponsiveStyle = (styleFunction) => {
  return styleFunction({
    screen,
    breakpoints,
    responsive,
    tokens,
  });
};

export default {
  responsive,
  responsiveSpacing,
  responsiveFontSize,
  responsiveWidth,
  responsiveHeight,
  responsiveColumns,
  breakpoints,
  screen,
  grid,
  flex,
  position,
  createResponsiveStyle,
};
