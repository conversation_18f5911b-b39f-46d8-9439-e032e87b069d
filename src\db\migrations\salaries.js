/**
 * <PERSON><PERSON><PERSON> tabloları için migrasyon
 *
 * @param {object} db - SQLite veritabanı nesnesi
 * @returns {Promise<void>}
 */
export const migrateSalaries = async (db) => {
  try {
    console.log('<PERSON>aş tabloları migrasyonu başlatılıyor...');

    // Eski tabloları temizle
    await db.execAsync(`DROP TABLE IF EXISTS salaries`);
    await db.execAsync(`DROP TABLE IF EXISTS salary_payments`);

    // salaries tablosunu oluştur
    const hasSalariesTable = await db.getFirstAsync(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='salaries'
    `);

    if (!hasSalariesTable) {
      console.log('salaries tablosu oluşturuluyor...');

      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS salaries (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          amount DECIMAL(10,2) NOT NULL,
          currency TEXT NOT NULL DEFAULT 'TRY',
          payment_day INTEGER NOT NULL,
          is_active INTEGER DEFAULT 1,
          category_id INTEGER,
          account_id INTEGER,
          tax_rate DECIMAL(5,2) DEFAULT 0,
          notes TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE SET NULL,
          FOREIGN KEY (account_id) REFERENCES accounts (id) ON DELETE SET NULL
        )
      `);

      console.log('salaries tablosu başarıyla oluşturuldu.');
    } else {
      console.log('salaries tablosu zaten mevcut.');

      // Tabloda eksik sütunlar var mı kontrol et
      const columns = await db.getAllAsync(`PRAGMA table_info(salaries)`);

      // Sütun adlarını bir diziye dönüştür
      const columnNames = columns.map(col => col.name);

      // Eksik sütunları kontrol et ve ekle
      if (!columnNames.includes('currency')) {
        console.log('currency sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE salaries ADD COLUMN currency TEXT NOT NULL DEFAULT 'TRY'`);
      }

      if (!columnNames.includes('is_active')) {
        console.log('is_active sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE salaries ADD COLUMN is_active INTEGER DEFAULT 1`);
      }

      if (!columnNames.includes('tax_rate')) {
        console.log('tax_rate sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE salaries ADD COLUMN tax_rate DECIMAL(5,2) DEFAULT 0`);
      }

      if (!columnNames.includes('notes')) {
        console.log('notes sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE salaries ADD COLUMN notes TEXT`);
      }

      if (!columnNames.includes('updated_at')) {
        console.log('updated_at sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE salaries ADD COLUMN updated_at DATETIME DEFAULT CURRENT_TIMESTAMP`);
      }

      if (!columnNames.includes('category_id')) {
        console.log('category_id sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE salaries ADD COLUMN category_id INTEGER REFERENCES categories(id) ON DELETE SET NULL`);
      }

      if (!columnNames.includes('account_id')) {
        console.log('account_id sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE salaries ADD COLUMN account_id INTEGER REFERENCES accounts(id) ON DELETE SET NULL`);
      }
    }

    // salary_payments tablosunu oluştur
    const hasSalaryPaymentsTable = await db.getFirstAsync(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='salary_payments'
    `);

    if (!hasSalaryPaymentsTable) {
      console.log('salary_payments tablosu oluşturuluyor...');

      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS salary_payments (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          salary_id INTEGER NOT NULL,
          amount DECIMAL(10,2) NOT NULL,
          currency TEXT NOT NULL DEFAULT 'TRY',
          payment_date TEXT NOT NULL,
          is_paid INTEGER DEFAULT 0,
          transaction_id INTEGER,
          notes TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (salary_id) REFERENCES salaries (id) ON DELETE CASCADE,
          FOREIGN KEY (transaction_id) REFERENCES transactions (id) ON DELETE SET NULL
        )
      `);

      console.log('salary_payments tablosu başarıyla oluşturuldu.');
    } else {
      console.log('salary_payments tablosu zaten mevcut.');

      // Tabloda eksik sütunlar var mı kontrol et
      const columns = await db.getAllAsync(`PRAGMA table_info(salary_payments)`);

      // Sütun adlarını bir diziye dönüştür
      const columnNames = columns.map(col => col.name);

      // Eksik sütunları kontrol et ve ekle
      if (!columnNames.includes('currency')) {
        console.log('currency sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE salary_payments ADD COLUMN currency TEXT NOT NULL DEFAULT 'TRY'`);
      }

      if (!columnNames.includes('is_paid')) {
        console.log('is_paid sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE salary_payments ADD COLUMN is_paid INTEGER DEFAULT 0`);
      }

      if (!columnNames.includes('notes')) {
        console.log('notes sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE salary_payments ADD COLUMN notes TEXT`);
      }

      if (!columnNames.includes('updated_at')) {
        console.log('updated_at sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE salary_payments ADD COLUMN updated_at DATETIME DEFAULT CURRENT_TIMESTAMP`);
      }
    }

    console.log('Maaş tabloları migrasyonu tamamlandı.');
  } catch (error) {
    console.error('Maaş tabloları migrasyon hatası:', error);
    throw error;
  }
};
