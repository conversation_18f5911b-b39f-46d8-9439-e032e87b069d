import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Share,
  Platform
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';

/**
 * Export Manager Component
 * PDF/CSV export ve share functionality
 */
export default function ExportManager({ 
  budgetData, 
  analysisData, 
  theme,
  style 
}) {
  const [exporting, setExporting] = useState(false);

  /**
   * CSV formatında veri export et
   */
  const exportToCSV = async () => {
    try {
      setExporting(true);

      // CSV header
      let csvContent = 'Kategori,Harcama,Bütçe,Kullanım Oranı,Tarih\n';

      // Kategori verilerini CSV'ye ekle
      if (analysisData?.categoryData) {
        analysisData.categoryData.forEach(category => {
          const utilizationRate = category.budget > 0 ? 
            Math.round((category.amount / category.budget) * 100) : 0;
          
          csvContent += `"${category.name}",${category.amount},${category.budget},${utilizationRate}%,${new Date().toLocaleDateString('tr-TR')}\n`;
        });
      }

      // Performans verilerini ekle
      if (analysisData?.performanceData) {
        csvContent += '\nDönem,Hedef,Gerçekleşen,Skor\n';
        analysisData.performanceData.forEach(perf => {
          csvContent += `"${perf.period}",${perf.target},${perf.actual},${perf.score}\n`;
        });
      }

      // Dosya oluştur
      const fileName = `butce_raporu_${new Date().toISOString().slice(0, 10)}.csv`;
      const fileUri = FileSystem.documentDirectory + fileName;

      await FileSystem.writeAsStringAsync(fileUri, csvContent, {
        encoding: FileSystem.EncodingType.UTF8,
      });

      // Share
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri, {
          mimeType: 'text/csv',
          dialogTitle: 'Bütçe Raporu Paylaş',
        });
      } else {
        Alert.alert('Başarılı', `Rapor kaydedildi: ${fileName}`);
      }

    } catch (error) {
      console.error('CSV export hatası:', error);
      Alert.alert('Hata', 'CSV export işlemi başarısız oldu.');
    } finally {
      setExporting(false);
    }
  };

  /**
   * PDF formatında rapor oluştur (basit text-based)
   */
  const exportToPDF = async () => {
    try {
      setExporting(true);

      // Basit text-based PDF content
      let pdfContent = `BÜTÇE RAPORU\n`;
      pdfContent += `Tarih: ${new Date().toLocaleDateString('tr-TR')}\n`;
      pdfContent += `${'='.repeat(50)}\n\n`;

      // Genel Özet
      if (analysisData?.reportData) {
        const report = analysisData.reportData;
        pdfContent += `GENEL ÖZET\n`;
        pdfContent += `Toplam Bütçe: ${report.totalBudget?.toLocaleString('tr-TR')} TL\n`;
        pdfContent += `Toplam Harcama: ${report.totalSpent?.toLocaleString('tr-TR')} TL\n`;
        pdfContent += `Kalan Miktar: ${report.totalRemaining?.toLocaleString('tr-TR')} TL\n`;
        pdfContent += `Kullanım Oranı: %${report.budgetUtilization}\n`;
        pdfContent += `Performans Skoru: ${report.performanceScore}\n\n`;
      }

      // Kategori Detayları
      if (analysisData?.categoryData) {
        pdfContent += `KATEGORİ DETAYLARI\n`;
        pdfContent += `${'-'.repeat(30)}\n`;
        analysisData.categoryData.forEach(category => {
          const utilizationRate = category.budget > 0 ? 
            Math.round((category.amount / category.budget) * 100) : 0;
          
          pdfContent += `${category.name}:\n`;
          pdfContent += `  Harcama: ${category.amount?.toLocaleString('tr-TR')} TL\n`;
          pdfContent += `  Bütçe: ${category.budget?.toLocaleString('tr-TR')} TL\n`;
          pdfContent += `  Kullanım: %${utilizationRate}\n\n`;
        });
      }

      // Performans Analizi
      if (analysisData?.performanceData) {
        pdfContent += `PERFORMANS ANALİZİ\n`;
        pdfContent += `${'-'.repeat(30)}\n`;
        analysisData.performanceData.forEach(perf => {
          pdfContent += `${perf.period}: Hedef ${perf.target?.toLocaleString('tr-TR')} TL, `;
          pdfContent += `Gerçekleşen ${perf.actual?.toLocaleString('tr-TR')} TL (Skor: ${perf.score})\n`;
        });
      }

      // Dosya oluştur
      const fileName = `butce_raporu_${new Date().toISOString().slice(0, 10)}.txt`;
      const fileUri = FileSystem.documentDirectory + fileName;

      await FileSystem.writeAsStringAsync(fileUri, pdfContent, {
        encoding: FileSystem.EncodingType.UTF8,
      });

      // Share
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri, {
          mimeType: 'text/plain',
          dialogTitle: 'Bütçe Raporu Paylaş',
        });
      } else {
        Alert.alert('Başarılı', `Rapor kaydedildi: ${fileName}`);
      }

    } catch (error) {
      console.error('PDF export hatası:', error);
      Alert.alert('Hata', 'PDF export işlemi başarısız oldu.');
    } finally {
      setExporting(false);
    }
  };

  /**
   * Özet bilgileri paylaş
   */
  const shareQuickSummary = async () => {
    try {
      let summary = `📊 BÜTÇE ÖZETİ\n`;
      summary += `📅 ${new Date().toLocaleDateString('tr-TR')}\n\n`;

      if (analysisData?.reportData) {
        const report = analysisData.reportData;
        summary += `💰 Toplam Bütçe: ${report.totalBudget?.toLocaleString('tr-TR')} TL\n`;
        summary += `💸 Harcama: ${report.totalSpent?.toLocaleString('tr-TR')} TL\n`;
        summary += `💵 Kalan: ${report.totalRemaining?.toLocaleString('tr-TR')} TL\n`;
        summary += `📈 Kullanım: %${report.budgetUtilization}\n`;
        summary += `⭐ Skor: ${report.performanceScore}/100\n\n`;
      }

      if (analysisData?.categoryData && analysisData.categoryData.length > 0) {
        summary += `🏷️ En Çok Harcanan:\n`;
        const topCategory = analysisData.categoryData[0];
        summary += `${topCategory.name}: ${topCategory.amount?.toLocaleString('tr-TR')} TL\n`;
      }

      await Share.share({
        message: summary,
        title: 'Bütçe Özeti'
      });

    } catch (error) {
      console.error('Share hatası:', error);
      Alert.alert('Hata', 'Paylaşım işlemi başarısız oldu.');
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.SURFACE }, style]}>
      <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
        Rapor Export
      </Text>

      <View style={styles.buttonContainer}>
        {/* CSV Export */}
        <TouchableOpacity
          style={[
            styles.exportButton,
            { backgroundColor: theme.SUCCESS + '20', borderColor: theme.SUCCESS }
          ]}
          onPress={exportToCSV}
          disabled={exporting}
        >
          <MaterialIcons name="table-chart" size={24} color={theme.SUCCESS} />
          <Text style={[styles.buttonText, { color: theme.SUCCESS }]}>
            CSV Export
          </Text>
        </TouchableOpacity>

        {/* PDF Export */}
        <TouchableOpacity
          style={[
            styles.exportButton,
            { backgroundColor: theme.DANGER + '20', borderColor: theme.DANGER }
          ]}
          onPress={exportToPDF}
          disabled={exporting}
        >
          <MaterialIcons name="picture-as-pdf" size={24} color={theme.DANGER} />
          <Text style={[styles.buttonText, { color: theme.DANGER }]}>
            PDF Export
          </Text>
        </TouchableOpacity>

        {/* Quick Share */}
        <TouchableOpacity
          style={[
            styles.exportButton,
            { backgroundColor: theme.INFO + '20', borderColor: theme.INFO }
          ]}
          onPress={shareQuickSummary}
          disabled={exporting}
        >
          <MaterialIcons name="share" size={24} color={theme.INFO} />
          <Text style={[styles.buttonText, { color: theme.INFO }]}>
            Hızlı Paylaş
          </Text>
        </TouchableOpacity>
      </View>

      {exporting && (
        <Text style={[styles.exportingText, { color: theme.TEXT_SECONDARY }]}>
          Export işlemi devam ediyor...
        </Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
    borderRadius: 12,
    margin: 16,
    borderWidth: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    flexWrap: 'wrap',
    gap: 12,
  },
  exportButton: {
    flexDirection: 'column',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    minWidth: 100,
    flex: 1,
    maxWidth: '30%',
  },
  buttonText: {
    marginTop: 8,
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
  exportingText: {
    textAlign: 'center',
    marginTop: 12,
    fontSize: 14,
    fontStyle: 'italic',
  },
});
