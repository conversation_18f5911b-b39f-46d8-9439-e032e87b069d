/**
 * Email Exporter - E-posta gönderimi
 * Temporary mock implementation for testing ExportManager
 */
export class EmailExporter {
  
  /**
   * E-posta gönderir
   * @param {Object} params - E-posta parametreleri
   * @returns {Promise<Object>} E-posta gönderim sonucu
   */
  static async sendEmail({ data, title, type, config = {} }) {
    console.log('🔍 EmailExporter.sendEmail called with:', { title, type });
    
    // Temporary mock implementation
    return {
      success: true,
      message: 'Email export mocked (packages removed for testing)',
      filePath: null
    };
  }
}
