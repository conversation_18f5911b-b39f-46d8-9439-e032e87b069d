import { Platform } from 'react-native';
import * as MailComposer from 'expo-mail-composer';
import * as FileSystem from 'expo-file-system';
import { PDFGenerator } from './PDFGenerator';
import { ExcelExporter } from './ExcelExporter';

/**
 * Email Exporter - Raporları e-posta ile gönderir
 * Expo MailComposer kullanarak yerli e-posta uygulamasını açar
 */
export class EmailExporter {
  
  /**
   * Raporu e-posta ile gönderir
   * @param {Object} params - E-posta parametreleri
   * @param {Object} params.data - Rapor verisi
   * @param {string} params.title - <PERSON>or başlığı
   * @param {string} params.type - <PERSON>or türü
   * @param {Object} params.config - E-posta yapılandırması
   * @returns {Promise<Object>} E-posta gönderme sonucu
   */
  static async sendReport({ data, title, type, config = {} }) {
    try {
      // E-posta yapılandı<PERSON>ı
      const emailConfig = {
        subject: config.subject || `${title} - Finansal Rapor`,
        recipients: config.recipients || [],
        ccRecipients: config.ccRecipients || [],
        bccRecipients: config.bccRecipients || [],
        body: config.body || this.generateEmailBody(title, type),
        isHtml: config.isHtml !== false,
        attachments: config.attachments || [],
        includeAttachments: config.includeAttachments !== false,
        attachmentFormats: config.attachmentFormats || ['pdf', 'excel'],
        ...config
      };

      // E-posta gönderilebilir mi kontrol et
      const isAvailable = await MailComposer.isAvailableAsync();
      if (!isAvailable) {
        throw new Error('E-posta gönderme özelliği bu cihazda kullanılamıyor');
      }

      // Ekleri hazırla
      const attachments = [];
      if (emailConfig.includeAttachments) {
        const preparedAttachments = await this.prepareAttachments({
          data,
          title,
          type,
          formats: emailConfig.attachmentFormats
        });
        attachments.push(...preparedAttachments);
      }

      // Mevcut ekleri ekle
      attachments.push(...emailConfig.attachments);

      // E-posta seçeneklerini hazırla
      const mailOptions = {
        subject: emailConfig.subject,
        recipients: emailConfig.recipients,
        ccRecipients: emailConfig.ccRecipients,
        bccRecipients: emailConfig.bccRecipients,
        body: emailConfig.body,
        isHtml: emailConfig.isHtml,
        attachments: attachments.length > 0 ? attachments : undefined
      };

      // E-posta composer'ı aç
      const result = await MailComposer.composeAsync(mailOptions);

      return {
        success: true,
        result,
        attachments: attachments.length
      };

    } catch (error) {
      console.error('Email Export Error:', error);
      return {
        success: false,
        error: error.message || 'E-posta gönderilirken bilinmeyen hata'
      };
    }
  }

  /**
   * Raporu doğrudan e-posta ile gönderir (attachments olmadan)
   * @param {Object} params - Basit e-posta parametreleri
   * @returns {Promise<Object>} E-posta gönderme sonucu
   */
  static async sendSimpleReport({ data, title, type, recipients = [], subject, customBody }) {
    try {
      const emailBody = customBody || this.generateDetailedEmailBody(data, title, type);
      
      const mailOptions = {
        subject: subject || `${title} - Finansal Rapor`,
        recipients,
        body: emailBody,
        isHtml: true
      };

      const result = await MailComposer.composeAsync(mailOptions);

      return {
        success: true,
        result,
        attachments: 0
      };

    } catch (error) {
      console.error('Simple Email Export Error:', error);
      return {
        success: false,
        error: error.message || 'E-posta gönderilirken hata oluştu'
      };
    }
  }

  /**
   * Toplu e-posta gönderir
   * @param {Object} params - Toplu e-posta parametreleri
   * @returns {Promise<Object>} Toplu e-posta sonucu
   */
  static async sendBulkReport({ data, title, type, recipientGroups = [], config = {} }) {
    try {
      const results = [];
      
      for (const group of recipientGroups) {
        const emailConfig = {
          ...config,
          recipients: group.recipients,
          subject: group.subject || config.subject || `${title} - Finansal Rapor`,
          body: group.body || config.body || this.generateEmailBody(title, type)
        };

        const result = await this.sendReport({
          data,
          title,
          type,
          config: emailConfig
        });

        results.push({
          group: group.name || 'Grup',
          recipients: group.recipients,
          ...result
        });
      }

      const successCount = results.filter(r => r.success).length;
      const failureCount = results.length - successCount;

      return {
        success: failureCount === 0,
        results,
        summary: {
          total: results.length,
          success: successCount,
          failure: failureCount
        }
      };

    } catch (error) {
      console.error('Bulk Email Export Error:', error);
      return {
        success: false,
        error: error.message || 'Toplu e-posta gönderilirken hata oluştu'
      };
    }
  }

  /**
   * E-posta ekleri hazırlar
   * @param {Object} params - Ek parametreleri
   * @returns {Promise<Array>} Hazırlanan ekler
   */
  static async prepareAttachments({ data, title, type, formats = ['pdf'] }) {
    const attachments = [];

    try {
      // PDF eki
      if (formats.includes('pdf')) {
        const pdfResult = await PDFGenerator.generatePDF({
          data,
          title,
          type,
          config: { format: 'A4' }
        });

        if (pdfResult.success) {
          attachments.push({
            uri: pdfResult.uri,
            mimeType: 'application/pdf',
            filename: pdfResult.fileName
          });
        }
      }

      // Excel eki
      if (formats.includes('excel') || formats.includes('csv')) {
        const excelResult = await ExcelExporter.exportToExcel({
          data,
          title,
          type,
          config: { format: 'csv' }
        });

        if (excelResult.success) {
          attachments.push({
            uri: excelResult.uri,
            mimeType: 'text/csv',
            filename: excelResult.fileName
          });
        }
      }

    } catch (error) {
      console.error('Attachment Preparation Error:', error);
      // Hata durumunda boş array döndür
    }

    return attachments;
  }

  /**
   * E-posta gövdesini oluşturur
   * @param {string} title - Rapor başlığı
   * @param {string} type - Rapor türü
   * @returns {string} E-posta gövdesi
   */
  static generateEmailBody(title, type) {
    const currentDate = new Date().toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                   color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0;">
          <h1 style="margin: 0; font-size: 28px;">${title}</h1>
          <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">
            Finansal Rapor
          </p>
        </div>
        
        <div style="background: white; padding: 30px; border: 1px solid #e0e0e0; border-radius: 0 0 8px 8px;">
          <p style="font-size: 16px; color: #333; margin-bottom: 20px;">
            Merhaba,
          </p>
          
          <p style="font-size: 16px; color: #333; line-height: 1.6; margin-bottom: 20px;">
            ${currentDate} tarihinde oluşturulan <strong>${title}</strong> raporu ekte bulunmaktadır.
            Bu rapor, finansal durumunuzu detaylı bir şekilde analiz etmenize yardımcı olacaktır.
          </p>
          
          <div style="background: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0;">
            <h3 style="color: #333; margin-top: 0;">Rapor Detayları:</h3>
            <ul style="color: #666; line-height: 1.8;">
              <li><strong>Rapor Türü:</strong> ${this.getTypeDisplayName(type)}</li>
              <li><strong>Oluşturulma Tarihi:</strong> ${currentDate}</li>
              <li><strong>Format:</strong> PDF ve Excel</li>
              <li><strong>Kaynak:</strong> Maaş Takip Uygulaması</li>
            </ul>
          </div>
          
          <p style="font-size: 16px; color: #333; line-height: 1.6; margin-bottom: 20px;">
            Raporu incelemek için ekteki dosyaları açabilirsiniz. Herhangi bir sorunuz olursa 
            lütfen bizimle iletişime geçmekten çekinmeyin.
          </p>
          
          <div style="text-align: center; margin-top: 30px;">
            <p style="font-size: 14px; color: #666;">
              Bu rapor <strong>Maaş Takip Uygulaması</strong> ile otomatik olarak oluşturulmuştur.
            </p>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Detaylı e-posta gövdesini oluşturur (veri içerikli)
   * @param {Object} data - Rapor verisi
   * @param {string} title - Rapor başlığı
   * @param {string} type - Rapor türü
   * @returns {string} Detaylı e-posta gövdesi
   */
  static generateDetailedEmailBody(data, title, type) {
    const basicBody = this.generateEmailBody(title, type);
    const dataContent = this.generateDataContent(data, type);
    
    // Temel body'nin içine veri içeriğini ekle
    const insertPosition = basicBody.indexOf('<div style="text-align: center; margin-top: 30px;">');
    
    if (insertPosition !== -1) {
      return basicBody.substring(0, insertPosition) + 
             dataContent + 
             basicBody.substring(insertPosition);
    }
    
    return basicBody + dataContent;
  }

  /**
   * Veri içeriğini HTML formatında oluşturur
   * @param {Object} data - Rapor verisi
   * @param {string} type - Rapor türü
   * @returns {string} HTML veri içeriği
   */
  static generateDataContent(data, type) {
    switch (type) {
      case 'monthly-income-expense':
        return this.generateMonthlyIncomeExpenseDataContent(data);
      case 'basic-summary':
        return this.generateBasicSummaryDataContent(data);
      case 'category-distribution':
        return this.generateCategoryDistributionDataContent(data);
      default:
        return this.generateGenericDataContent(data);
    }
  }

  /**
   * Aylık gelir-gider veri içeriği
   */
  static generateMonthlyIncomeExpenseDataContent(data) {
    const { summary } = data;
    
    return `
      <div style="background: white; padding: 20px; border-radius: 6px; margin: 20px 0; border: 1px solid #e0e0e0;">
        <h3 style="color: #333; margin-top: 0; margin-bottom: 20px;">Özet Bilgiler:</h3>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px;">
          <div style="background: #e8f5e8; padding: 15px; border-radius: 6px; text-align: center;">
            <div style="font-size: 20px; font-weight: bold; color: #27ae60;">
              ₺${summary.totalIncome?.toLocaleString('tr-TR') || '0'}
            </div>
            <div style="font-size: 14px; color: #666; margin-top: 5px;">Toplam Gelir</div>
          </div>
          
          <div style="background: #fdeaea; padding: 15px; border-radius: 6px; text-align: center;">
            <div style="font-size: 20px; font-weight: bold; color: #e74c3c;">
              ₺${summary.totalExpense?.toLocaleString('tr-TR') || '0'}
            </div>
            <div style="font-size: 14px; color: #666; margin-top: 5px;">Toplam Gider</div>
          </div>
          
          <div style="background: ${(summary.netIncome || 0) >= 0 ? '#e8f5e8' : '#fdeaea'}; padding: 15px; border-radius: 6px; text-align: center;">
            <div style="font-size: 20px; font-weight: bold; color: ${(summary.netIncome || 0) >= 0 ? '#27ae60' : '#e74c3c'};">
              ₺${summary.netIncome?.toLocaleString('tr-TR') || '0'}
            </div>
            <div style="font-size: 14px; color: #666; margin-top: 5px;">Net Gelir</div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Temel özet veri içeriği
   */
  static generateBasicSummaryDataContent(data) {
    const { summary } = data;
    
    return `
      <div style="background: white; padding: 20px; border-radius: 6px; margin: 20px 0; border: 1px solid #e0e0e0;">
        <h3 style="color: #333; margin-top: 0; margin-bottom: 20px;">Finansal Özet:</h3>
        
        <table style="width: 100%; border-collapse: collapse;">
          <tr>
            <td style="padding: 10px; border-bottom: 1px solid #eee; font-weight: bold;">Toplam Gelir:</td>
            <td style="padding: 10px; border-bottom: 1px solid #eee; color: #27ae60; font-weight: bold; text-align: right;">
              ₺${summary.totalIncome?.toLocaleString('tr-TR') || '0'}
            </td>
          </tr>
          <tr>
            <td style="padding: 10px; border-bottom: 1px solid #eee; font-weight: bold;">Toplam Gider:</td>
            <td style="padding: 10px; border-bottom: 1px solid #eee; color: #e74c3c; font-weight: bold; text-align: right;">
              ₺${summary.totalExpense?.toLocaleString('tr-TR') || '0'}
            </td>
          </tr>
          <tr>
            <td style="padding: 10px; border-bottom: 1px solid #eee; font-weight: bold;">Ortalama Gelir:</td>
            <td style="padding: 10px; border-bottom: 1px solid #eee; color: #3498db; font-weight: bold; text-align: right;">
              ₺${summary.averageIncome?.toLocaleString('tr-TR') || '0'}
            </td>
          </tr>
          <tr>
            <td style="padding: 10px; font-weight: bold;">Ortalama Gider:</td>
            <td style="padding: 10px; color: #f39c12; font-weight: bold; text-align: right;">
              ₺${summary.averageExpense?.toLocaleString('tr-TR') || '0'}
            </td>
          </tr>
        </table>
      </div>
    `;
  }

  /**
   * Kategori dağılımı veri içeriği
   */
  static generateCategoryDistributionDataContent(data) {
    const { categories } = data;
    
    if (!categories || categories.length === 0) {
      return `
        <div style="background: white; padding: 20px; border-radius: 6px; margin: 20px 0; border: 1px solid #e0e0e0;">
          <p style="color: #666; text-align: center;">Kategori verisi bulunamadı.</p>
        </div>
      `;
    }

    return `
      <div style="background: white; padding: 20px; border-radius: 6px; margin: 20px 0; border: 1px solid #e0e0e0;">
        <h3 style="color: #333; margin-top: 0; margin-bottom: 20px;">Kategori Dağılımı:</h3>
        
        <table style="width: 100%; border-collapse: collapse;">
          <thead>
            <tr style="background: #f8f9fa;">
              <th style="padding: 12px; border-bottom: 2px solid #ddd; text-align: left;">Kategori</th>
              <th style="padding: 12px; border-bottom: 2px solid #ddd; text-align: right;">Tutar</th>
              <th style="padding: 12px; border-bottom: 2px solid #ddd; text-align: right;">Yüzde</th>
            </tr>
          </thead>
          <tbody>
            ${categories.slice(0, 10).map(category => `
              <tr>
                <td style="padding: 10px; border-bottom: 1px solid #eee;">${category.name}</td>
                <td style="padding: 10px; border-bottom: 1px solid #eee; text-align: right; color: ${category.type === 'income' ? '#27ae60' : '#e74c3c'}; font-weight: bold;">
                  ₺${category.amount?.toLocaleString('tr-TR') || '0'}
                </td>
                <td style="padding: 10px; border-bottom: 1px solid #eee; text-align: right;">
                  ${category.percentage?.toFixed(1) || '0'}%
                </td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>
    `;
  }

  /**
   * Genel veri içeriği
   */
  static generateGenericDataContent(data) {
    return `
      <div style="background: white; padding: 20px; border-radius: 6px; margin: 20px 0; border: 1px solid #e0e0e0;">
        <h3 style="color: #333; margin-top: 0; margin-bottom: 20px;">Rapor Verisi:</h3>
        <p style="color: #666; font-style: italic;">
          Detaylı veri görüntülemek için ekteki dosyaları inceleyiniz.
        </p>
      </div>
    `;
  }

  /**
   * Rapor türünün görüntülenme adını döndürür
   * @param {string} type - Rapor türü
   * @returns {string} Görüntülenme adı
   */
  static getTypeDisplayName(type) {
    const typeNames = {
      'monthly-income-expense': 'Aylık Gelir-Gider Raporu',
      'basic-summary': 'Temel Finansal Özet',
      'category-distribution': 'Kategori Dağılımı',
      'cash-flow': 'Nakit Akış Raporu',
      'budget-vs-actual': 'Bütçe vs Gerçekleşen',
      'transaction-list': 'İşlem Listesi',
      'regular-income-tracking': 'Düzenli Gelir Takibi',
      'overtime-income': 'Mesai Geliri Analizi'
    };
    
    return typeNames[type] || 'Finansal Rapor';
  }

  /**
   * E-posta şablonu seçenekleri
   */
  static getEmailTemplates() {
    return {
      formal: {
        subject: 'Finansal Rapor - {title}',
        greeting: 'Sayın İlgili,',
        closing: 'Saygılarımızla,'
      },
      casual: {
        subject: '{title} - Rapor',
        greeting: 'Merhaba,',
        closing: 'İyi günler,'
      },
      urgent: {
        subject: 'ACİL: {title} - Finansal Rapor',
        greeting: 'Acil dikkat:',
        closing: 'Lütfen en kısa sürede inceleyin.'
      }
    };
  }

  /**
   * E-posta ayarlarını doğrular
   * @param {Object} config - E-posta yapılandırması
   * @returns {Object} Doğrulama sonucu
   */
  static validateEmailConfig(config) {
    const errors = [];
    
    if (!config.recipients || config.recipients.length === 0) {
      errors.push('En az bir alıcı adresi gereklidir');
    }
    
    if (!config.subject || config.subject.trim() === '') {
      errors.push('E-posta konusu gereklidir');
    }
    
    // E-posta adreslerini doğrula
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const allRecipients = [
      ...(config.recipients || []),
      ...(config.ccRecipients || []),
      ...(config.bccRecipients || [])
    ];
    
    const invalidEmails = allRecipients.filter(email => !emailRegex.test(email));
    if (invalidEmails.length > 0) {
      errors.push(`Geçersiz e-posta adresleri: ${invalidEmails.join(', ')}`);
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
