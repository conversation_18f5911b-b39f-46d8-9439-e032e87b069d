import * as MailComposer from 'expo-mail-composer';
import { Alert } from 'react-native';

/**
 * Email Exporter - E-posta gönderimi
 */
export class EmailExporter {

  /**
   * E-posta gönderir
   * @param {Object} params - E-posta parametreleri
   * @returns {Promise<Object>} E-posta gönderim sonucu
   */
  static async sendEmail({ data, title, type, config = {} }) {
    try {
      const isAvailable = await MailComposer.isAvailableAsync();

      if (!isAvailable) {
        Alert.alert('Hata', 'E-posta uygulaması bulunamadı');
        return {
          success: false,
          message: 'E-posta uygulaması bulunamadı',
          filePath: null
        };
      }

      const emailContent = this.generateEmailContent(data, title, type);

      const result = await MailComposer.composeAsync({
        subject: title,
        body: emailContent,
        isHtml: true,
        recipients: config.recipients || [],
        attachments: config.attachments || []
      });

      if (result.status === 'sent') {
        return {
          success: true,
          message: 'E-posta başarıyla gönderildi',
          filePath: null
        };
      } else {
        return {
          success: false,
          message: 'E-posta gönderilmedi',
          filePath: null
        };
      }
    } catch (error) {
      console.error('Email sending error:', error);
      return {
        success: false,
        message: `E-posta gönderilirken hata oluştu: ${error.message}`,
        filePath: null
      };
    }
  }

  /**
   * E-posta içeriği oluşturur
   */
  static generateEmailContent(data, title, type) {
    const currentDate = new Date().toLocaleDateString('tr-TR');

    let html = `
      <html>
      <head>
        <meta charset="UTF-8">
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .header { text-align: center; margin-bottom: 30px; }
          .title { font-size: 24px; font-weight: bold; color: #333; }
          .date { font-size: 14px; color: #666; margin-top: 10px; }
          .content { margin-top: 20px; }
          .section { margin-bottom: 20px; }
          .section-title { font-size: 18px; font-weight: bold; margin-bottom: 10px; }
          table { width: 100%; border-collapse: collapse; margin-top: 10px; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          th { background-color: #f2f2f2; }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="title">${title}</div>
          <div class="date">Rapor Tarihi: ${currentDate}</div>
        </div>
        <div class="content">
    `;

    if (data && data.summary) {
      html += `
        <div class="section">
          <div class="section-title">Özet</div>
          <p>Toplam Gelir: ${data.summary.totalIncome || 0} ₺</p>
          <p>Toplam Gider: ${data.summary.totalExpense || 0} ₺</p>
          <p>Net Gelir: ${data.summary.netIncome || 0} ₺</p>
        </div>
      `;
    }

    if (data && data.transactions && data.transactions.length > 0) {
      html += `
        <div class="section">
          <div class="section-title">Son İşlemler (İlk 10)</div>
          <table>
            <tr>
              <th>Tarih</th>
              <th>Açıklama</th>
              <th>Kategori</th>
              <th>Tutar</th>
              <th>Tür</th>
            </tr>
      `;

      data.transactions.slice(0, 10).forEach(transaction => {
        html += `
          <tr>
            <td>${new Date(transaction.date).toLocaleDateString('tr-TR')}</td>
            <td>${transaction.description || '-'}</td>
            <td>${transaction.category_name || '-'}</td>
            <td>${transaction.amount} ₺</td>
            <td>${transaction.type === 'income' ? 'Gelir' : 'Gider'}</td>
          </tr>
        `;
      });

      html += '</table>';

      if (data.transactions.length > 10) {
        html += `<p><em>Not: Yalnızca ilk 10 işlem gösterilmektedir. Toplam ${data.transactions.length} işlem bulunmaktadır.</em></p>`;
      }
      html += '</div>';
    }

    html += `
        </div>
      </body>
      </html>
    `;

    return html;
  }
}
