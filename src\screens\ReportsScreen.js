import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>Vie<PERSON>,
  <PERSON><PERSON>View,
  Modal,
} from 'react-native';
import { useTheme } from '../context/ThemeContext';
import { useNavigation } from '@react-navigation/native';
import { DataIntegrationProvider } from '../context/DataIntegrationContext';

// New modular components for redesigned Reports screen
import Reports<PERSON><PERSON>enHeader from '../components/reports/ReportsScreen/ReportsScreenHeader';
import DashboardHub from '../components/reports/ReportsScreen/DashboardHub';
import QuickActionsPanel from '../components/reports/ReportsScreen/QuickActionsPanel';
import ReportLibrarySection from '../components/reports/ReportsScreen/ReportLibrarySection';
import SmartSuggestionsPanel from '../components/reports/ReportsScreen/SmartSuggestionsPanel';
import ActiveReportView from '../components/reports/Common/ActiveReportView';

// Drag & Drop components
import DragDropEditor from '../components/reports/DragDrop/DragDropEditor';

// AI Analysis components
import AIAnalysisEngine from '../components/reports/AI/AIAnalysisEngine';
import SmartInsights from '../components/reports/AI/SmartInsights';
import PredictiveAnalytics from '../components/reports/AI/PredictiveAnalytics';

// Template imports (legacy support)
import TemplateSelector from '../components/reports/Templates/TemplateSelector';
import { getTemplateConfig } from '../components/reports/Templates/TemplateConfig';

// Services and utilities
import { reportService } from '../services/reports/reportService';
import {
  handleExportReport,
  handleSaveReport,
  handleOpenLibrary
} from '../utils/reportHandlers';

// Styles
import { reportsScreenStyles as styles } from './styles/ReportsScreenStyles';

/**
 * Ana Raporlar Ekranı - Interaktif Finansal Raporlama Platformu
 * Kullanıcı odaklı, sınırsız esneklik sunan rapor sistemi
 */
const ReportsScreenContent = () => {
  const { theme } = useTheme();
  const navigation = useNavigation();

  // State management
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showTemplateSelector, setShowTemplateSelector] = useState(false);
  const [activeReport, setActiveReport] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  // New modal states for Stage 5
  const [showDragDropEditor, setShowDragDropEditor] = useState(false);
  const [showAIAnalysis, setShowAIAnalysis] = useState(false);
  const [showSmartInsights, setShowSmartInsights] = useState(false);
  const [showPredictiveAnalytics, setShowPredictiveAnalytics] = useState(false);

  // Data states
  const [reportStats, setReportStats] = useState({
    totalReports: 0,
    totalExports: 0,
    favoritesCount: 0,
    scheduledCount: 0,
  });
  const [recentReports, setRecentReports] = useState([]);
  const [favoriteReports, setFavoriteReports] = useState([]);
  const [allReports, setAllReports] = useState([]);
  const [reportCategories, setReportCategories] = useState([]);
  const [smartSuggestions, setSmartSuggestions] = useState([]);

  // AI Analysis data
  const [financialData, setFinancialData] = useState([]);
  const [aiInsights, setAiInsights] = useState([]);
  const [predictions, setPredictions] = useState([]);

  useEffect(() => {
    loadReportData();
  }, []);

  /**
   * Rapor verilerini yükle
   */
  const loadReportData = async () => {
    try {
      setIsLoading(true);

      // Load data using new services
      const [
        stats,
        recent,
        favorites,
        all,
        categories,
        suggestions
      ] = await Promise.all([
        reportService.getReportStats(),
        reportService.getRecentReports(),
        reportService.getFavoriteReports(),
        reportService.getAllReports(),
        reportService.getReportCategories(),
        reportService.getSmartSuggestions(),
      ]);

      setReportStats(stats);
      setRecentReports(recent);
      setFavoriteReports(favorites);
      setAllReports(all);
      setReportCategories(categories);
      setSmartSuggestions(suggestions);

    } catch (error) {
      console.error('Rapor verileri yüklenirken hata:', error);
      // Hata durumunda default değerleri kullan
      const defaultStats = {
        totalReports: 0,
        totalExports: 0,
        favoritesCount: 0,
        scheduledCount: 0,
      };
      setReportStats(defaultStats);
      setRecentReports([]);
      setFavoriteReports([]);
      setAllReports([]);
      setReportCategories([]);
      setSmartSuggestions([]);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Yeni rapor oluştur
   */
  const handleCreateReport = (template) => {
    // Eğer template object ise, yeni handler'ı kullan
    if (typeof template === 'object' && template.id) {
      // Navigation targets için özel kontrol
      if (template.navigationTarget) {
        switch (template.navigationTarget) {
          case 'InteractiveTableBuilder':
            navigation.navigate('InteractiveTableBuilder');
            return;
          case 'ReportBuilder':
            navigation.navigate('ReportBuilder');
            return;
          case 'DashboardBuilder':
            navigation.navigate('DashboardBuilder');
            return;
          default:
            // Normal template handling
            break;
        }
      }
      
      // Import the new handler
      const { handleTemplateSelect } = require('../utils/reportHandlers');
      handleTemplateSelect(template, navigation, setActiveReport);
      return;
    }
    
    // Eski logic - template ID için
    const templateId = template;
    const templateMapping = {
      'monthly-summary': 'monthly_income_expense',
      'category-breakdown': 'category_distribution',
      'cash-flow': 'cash_flow',
      'budget-analysis': 'budget_vs_actual',
      'overtime-income': 'shift_income',
      'tax-report': 'summary_overview',
      'custom': 'custom'
    };

    const mappedTemplateId = templateMapping[templateId] || templateId;
    const templateConfig = getTemplateConfig(mappedTemplateId);
    
    if (templateConfig) {
      // Direkt template'i aç
      setActiveReport({
        template: templateConfig,
        config: { id: mappedTemplateId }
      });
    } else {
      // Template bulunamadıysa template selector'ü aç
      setShowTemplateSelector(true);
    }
  };

  // New handlers for Stage 5 features
  const handleCreateCustomReport = () => {
    setShowDragDropEditor(true);
  };

  const handleAIAnalysis = () => {
    setShowAIAnalysis(true);
  };

  const handleSmartInsights = () => {
    setShowSmartInsights(true);
  };

  const handlePredictiveAnalytics = () => {
    setShowPredictiveAnalytics(true);
  };

  const handleAIAnalysisComplete = (analysisResult) => {
    setAiInsights(analysisResult.insights || []);
    setPredictions(analysisResult.predictions || []);
  };

  const handleInsightAction = (insight, action) => {
    console.log('Insight action:', insight, action);
    // Handle insight actions based on action type
  };

  const handleDismissInsight = (insightId) => {
    setAiInsights(prev => prev.filter(insight => insight.id !== insightId));
  };

  /**
   * Şablon seçimi
   */
  const handleTemplateSelect = (template) => {
    setSelectedTemplate(template);
    setShowTemplateSelector(false);

    // Şablon tipine göre uygun bileşeni seç
    const templateConfig = getTemplateConfig(template.id);
    if (templateConfig) {
      setActiveReport({
        template: templateConfig,
        config: template
      });
    }
  };

  /**
   * Rapor görünümünü kapat
   */
  const handleCloseReport = () => {
    setActiveReport(null);
    setSelectedTemplate(null);
  };

  /**
   * Tablo builder'ı aç
   */
  const handleOpenTableBuilder = () => {
    navigation.navigate('InteractiveTableBuilder');
  };

  // Event handlers for new components
  const handleStatsPress = (type) => {
    console.log('Stats pressed:', type);
    // Navigate to specific stats view
  };

  const handleReportPress = (report) => {
    setActiveReport(report);
    console.log('Report pressed:', report);
  };

  const handleSuggestionPress = (suggestion) => {
    console.log('Suggestion pressed:', suggestion);
    // Handle suggestion action
  };

  const handleDismissSuggestion = (suggestionId) => {
    setSmartSuggestions(prev => prev.filter(s => s.id !== suggestionId));
  };

  const handleRefreshSuggestions = async () => {
    try {
      const suggestions = await reportService.getSmartSuggestions();
      setSmartSuggestions(suggestions);
    } catch (error) {
      console.error('Error refreshing suggestions:', error);
    }
  };

  const handleCategorySelect = (category) => {
    setSelectedCategory(category);
  };

  const handleReportEdit = (report) => {
    console.log('Edit report:', report);
    // Navigate to edit screen
  };

  const handleReportDelete = (report) => {
    console.log('Delete report:', report);
    // Show confirmation and delete
  };

  const handleReportFavorite = async (report) => {
    try {
      await reportService.toggleReportFavorite(report.id);
      // Refresh data
      loadReportData();
    } catch (error) {
      console.error('Error toggling favorite:', error);
    }
  };

  // Aktif rapor görünümü
  if (activeReport) {
    return (
      <ActiveReportView
        activeReport={activeReport}
        theme={theme}
        onCloseReport={handleCloseReport}
        onExport={handleExportReport}
        onSave={handleSaveReport}
      />
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      {/* Header */}
      <ReportsScreenHeader
        title="📊 İnteraktif Raporlar"
        subtitle="Finansal Analiz Platformu"
        onBackPress={() => navigation.goBack()}
        onSearchPress={() => console.log('Search pressed')}
        onSettingsPress={() => console.log('Settings pressed')}
        theme={theme}
      />

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Dashboard Hub */}
        <DashboardHub
          reportStats={reportStats}
          recentReports={recentReports}
          favoriteReports={favoriteReports}
          onReportPress={handleReportPress}
          onStatsPress={handleStatsPress}
          theme={theme}
          isLoading={isLoading}
        />

        {/* Quick Actions Panel */}
        <QuickActionsPanel
          onCreateTable={handleOpenTableBuilder}
          onOpenLibrary={() => handleOpenLibrary(navigation)}
          onCreateCustomReport={handleCreateCustomReport}
          onOpenDashboardBuilder={() => navigation.navigate('DashboardBuilder')}
          onOpenReportBuilder={() => navigation.navigate('ReportBuilder')}
          onOpenTemplateSelector={() => setShowTemplateSelector(true)}
          onAIAnalysis={handleAIAnalysis}
          onSmartInsights={handleSmartInsights}
          onPredictiveAnalytics={handlePredictiveAnalytics}
          theme={theme}
        />

        {/* Smart Suggestions Panel */}
        <SmartSuggestionsPanel
          suggestions={smartSuggestions}
          onSuggestionPress={handleSuggestionPress}
          onDismissSuggestion={handleDismissSuggestion}
          onRefreshSuggestions={handleRefreshSuggestions}
          theme={theme}
          isLoading={isLoading}
        />

        {/* Report Library Section */}
        <ReportLibrarySection
          reports={allReports}
          categories={reportCategories}
          selectedCategory={selectedCategory}
          onCategorySelect={handleCategorySelect}
          onReportPress={handleReportPress}
          onReportEdit={handleReportEdit}
          onReportDelete={handleReportDelete}
          onReportFavorite={handleReportFavorite}
          theme={theme}
        />
      </ScrollView>

      {/* Şablon Seçici Modal */}
      <Modal
        visible={showTemplateSelector}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowTemplateSelector(false)}
      >
        <TemplateSelector
          onTemplateSelect={handleTemplateSelect}
          onClose={() => setShowTemplateSelector(false)}
        />
      </Modal>

      {/* Drag & Drop Editor Modal */}
      <Modal
        visible={showDragDropEditor}
        animationType="slide"
        presentationStyle="fullScreen"
        onRequestClose={() => setShowDragDropEditor(false)}
      >
        <DragDropEditor
          onSave={(reportData) => {
            console.log('Report saved:', reportData);
            setShowDragDropEditor(false);
          }}
          onCancel={() => setShowDragDropEditor(false)}
          theme={theme}
        />
      </Modal>

      {/* AI Analysis Modal */}
      <Modal
        visible={showAIAnalysis}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowAIAnalysis(false)}
      >
        <AIAnalysisEngine
          data={financialData}
          onAnalysisComplete={handleAIAnalysisComplete}
          theme={theme}
        />
      </Modal>

      {/* Smart Insights Modal */}
      <Modal
        visible={showSmartInsights}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowSmartInsights(false)}
      >
        <SmartInsights
          insights={aiInsights}
          onInsightAction={handleInsightAction}
          onDismissInsight={handleDismissInsight}
          theme={theme}
        />
      </Modal>

      {/* Predictive Analytics Modal */}
      <Modal
        visible={showPredictiveAnalytics}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowPredictiveAnalytics(false)}
      >
        <PredictiveAnalytics
          historicalData={financialData}
          onPredictionUpdate={(predictions) => setPredictions(predictions)}
          theme={theme}
        />
      </Modal>
    </SafeAreaView>
  );
};

/**
 * Ana ReportsScreen komponenti - DataIntegrationProvider ile sarılmış
 */
const ReportsScreen = () => {
  return (
    <DataIntegrationProvider>
      <ReportsScreenContent />
    </DataIntegrationProvider>
  );
};

export default ReportsScreen;
