import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
  StatusBar,
  Modal,
  ActivityIndicator,
} from 'react-native';
import { useTheme } from '../context/ThemeContext';
import { useNavigation } from '@react-navigation/native';
import { DataIntegrationProvider } from '../context/DataIntegrationContext';

// Template imports
import TemplateSelector from '../components/reports/Templates/TemplateSelector';
import MonthlyIncomeExpenseTemplate from '../components/reports/Templates/MonthlyIncomeExpenseTemplate';
import CategoryDistributionTemplate from '../components/reports/Templates/CategoryDistributionTemplate';
import { getTemplateConfig } from '../components/reports/Templates/TemplateConfig';

// Modular components
import ReportHeader from '../components/reports/Common/ReportHeader';
import ReportStatsCards from '../components/reports/Common/ReportStatsCards';
import QuickTemplateCards from '../components/reports/Common/QuickTemplateCards';
import ReportCategories from '../components/reports/Common/ReportCategories';
import RecentReportsList from '../components/reports/Common/RecentReportsList';
import ActiveReportView from '../components/reports/Common/ActiveReportView';

// Utilities
import { getQuickTemplates, getReportCategories, getMockReportData } from '../utils/reportUtils';
import { 
  handleExportReport, 
  handleSaveReport, 
  handleOpenLibrary, 
  handleOpenReport 
} from '../utils/reportHandlers';

// Styles
import { reportsScreenStyles as styles } from './styles/ReportsScreenStyles';

const { width: screenWidth } = Dimensions.get('window');

/**
 * Ana Raporlar Ekranı - Interaktif Finansal Raporlama Platformu
 * Kullanıcı odaklı, sınırsız esneklik sunan rapor sistemi
 */
const ReportsScreenContent = () => {
  const { theme } = useTheme();
  const navigation = useNavigation();
  
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showTemplateSelector, setShowTemplateSelector] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [activeReport, setActiveReport] = useState(null);
  const [recentReports, setRecentReports] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [reportStats, setReportStats] = useState({
    totalReports: 0,
    totalExports: 0,
    favoritesCount: 0,
    scheduledCount: 0,
  });

  useEffect(() => {
    loadReportData();
  }, []);

  /**
   * Rapor verilerini yükle
   */
  const loadReportData = async () => {
    try {
      setIsLoading(true);
      const mockData = getMockReportData();
      setRecentReports(mockData.recentReports || []);
      setReportStats(mockData.reportStats || {
        totalReports: 0,
        totalExports: 0,
        favoritesCount: 0,
        scheduledCount: 0,
      });
    } catch (error) {
      // Hata durumunda default değerleri kullan
      const defaultStats = {
        totalReports: 0,
        totalExports: 0,
        favoritesCount: 0,
        scheduledCount: 0,
      };
      setReportStats(defaultStats);
      setRecentReports([]);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Yeni rapor oluştur
   */
  const handleCreateReport = (template) => {
    // Eğer template object ise, yeni handler'ı kullan
    if (typeof template === 'object' && template.id) {
      // Navigation targets için özel kontrol
      if (template.navigationTarget) {
        switch (template.navigationTarget) {
          case 'InteractiveTableBuilder':
            navigation.navigate('InteractiveTableBuilder');
            return;
          case 'ReportBuilder':
            navigation.navigate('ReportBuilder');
            return;
          case 'DashboardBuilder':
            navigation.navigate('DashboardBuilder');
            return;
          default:
            // Normal template handling
            break;
        }
      }
      
      // Import the new handler
      const { handleTemplateSelect } = require('../utils/reportHandlers');
      handleTemplateSelect(template, navigation, setActiveReport);
      return;
    }
    
    // Eski logic - template ID için
    const templateId = template;
    const templateMapping = {
      'monthly-summary': 'monthly_income_expense',
      'category-breakdown': 'category_distribution',
      'cash-flow': 'cash_flow',
      'budget-analysis': 'budget_vs_actual',
      'overtime-income': 'shift_income',
      'tax-report': 'summary_overview',
      'custom': 'custom'
    };

    const mappedTemplateId = templateMapping[templateId] || templateId;
    const templateConfig = getTemplateConfig(mappedTemplateId);
    
    if (templateConfig) {
      // Direkt template'i aç
      setActiveReport({
        template: templateConfig,
        config: { id: mappedTemplateId }
      });
    } else {
      // Template bulunamadıysa template selector'ü aç
      setShowTemplateSelector(true);
    }
  };

  /**
   * Şablon seçimi
   */
  const handleTemplateSelect = (template) => {
    setSelectedTemplate(template);
    setShowTemplateSelector(false);
    
    // Şablon tipine göre uygun bileşeni seç
    const templateConfig = getTemplateConfig(template.id);
    if (templateConfig) {
      setActiveReport({
        template: templateConfig,
        config: template
      });
    }
  };

  /**
   * Rapor görünümünü kapat
   */
  const handleCloseReport = () => {
    setActiveReport(null);
    setSelectedTemplate(null);
  };

  /**
   * Tablo builder'ı aç
   */
  const handleOpenTableBuilder = () => {
    navigation.navigate('InteractiveTableBuilder');
  };

  // Aktif rapor görünümü
  if (activeReport) {
    return (
      <ActiveReportView
        activeReport={activeReport}
        theme={theme}
        onCloseReport={handleCloseReport}
        onExport={handleExportReport}
        onSave={handleSaveReport}
      />
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      <StatusBar barStyle={theme.STATUS_BAR_STYLE} backgroundColor={theme.STATUS_BAR_COLOR} />
      
      {/* Header */}
      <ReportHeader
        title="📊 Raporlar"
        subtitle="İnteraktif Finansal Analiz Platformu"
        onBackPress={() => navigation.goBack()}
        theme={theme}
      />

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Loading Indicator */}
        {isLoading && (
          <View style={{ padding: 50, alignItems: 'center' }}>
            <ActivityIndicator size="large" color={theme.PRIMARY} />
            <Text style={{ color: theme.TEXT_SECONDARY, marginTop: 16 }}>
              Raporlar yükleniyor...
            </Text>
          </View>
        )}

        {/* İstatistik Kartları - Sadece yükleme tamamlandıktan sonra göster */}
        {!isLoading && (
          <ReportStatsCards
            stats={reportStats}
            theme={theme}
          />
        )}

        {/* Hızlı Aksiyonlar */}
        <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            🚀 Hızlı Aksiyonlar
          </Text>
          
          <View style={styles.quickActionsContainer}>
            <TouchableOpacity 
              style={[styles.quickAction, { backgroundColor: theme.PRIMARY }]}
              onPress={handleOpenTableBuilder}
            >
              <Text style={styles.quickActionIcon}>📊</Text>
              <Text style={[styles.quickActionText, { color: theme.SURFACE }]}>
                Tablo Oluştur
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.quickAction, { backgroundColor: theme.SUCCESS }]}
              onPress={() => handleOpenLibrary(navigation)}
            >
              <Text style={styles.quickActionIcon}>📚</Text>
              <Text style={[styles.quickActionText, { color: theme.SURFACE }]}>
                Kütüphane
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.quickAction, { backgroundColor: theme.WARNING }]}
              onPress={() => handleCreateReport('custom')}
            >
              <Text style={styles.quickActionIcon}>🎨</Text>
              <Text style={[styles.quickActionText, { color: theme.SURFACE }]}>
                Özel Rapor
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Hızlı Şablonlar */}
        {!isLoading && (
          <QuickTemplateCards
            templates={getQuickTemplates(theme)}
            onTemplateSelect={handleCreateReport}
            theme={theme}
          />
        )}

        {/* Son Kullanılan Raporlar */}
        {!isLoading && recentReports.length > 0 && (
          <RecentReportsList
            recentReports={recentReports}
            theme={theme}
            onReportPress={(report) => handleOpenReport(report, setActiveReport)}
          />
        )}

        {/* Kategori Navigasyonu */}
        {!isLoading && (
          <ReportCategories
            categories={getReportCategories(reportStats)}
            selectedCategory={selectedCategory}
            onCategorySelect={setSelectedCategory}
            theme={theme}
          />
        )}
      </ScrollView>

      {/* Şablon Seçici Modal */}
      <Modal
        visible={showTemplateSelector}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowTemplateSelector(false)}
      >
        <TemplateSelector
          onTemplateSelect={handleTemplateSelect}
          onClose={() => setShowTemplateSelector(false)}
        />
      </Modal>
    </SafeAreaView>
  );
};

/**
 * Ana ReportsScreen komponenti - DataIntegrationProvider ile sarılmış
 */
const ReportsScreen = () => {
  return (
    <DataIntegrationProvider>
      <ReportsScreenContent />
    </DataIntegrationProvider>
  );
};

export default ReportsScreen;
