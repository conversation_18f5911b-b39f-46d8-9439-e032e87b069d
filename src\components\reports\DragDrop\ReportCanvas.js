import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Dimensions,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

/**
 * Report Canvas Component
 * Provides a canvas area for arranging report components
 * Supports grid snapping and component alignment
 */
const ReportCanvas = ({ 
  layout = [],
  onUpdateLayout,
  onSelectComponent,
  selectedComponent,
  gridSize = 20,
  showGrid = true,
  theme 
}) => {
  const [canvasSize, setCanvasSize] = useState({
    width: screenWidth - 32,
    height: screenHeight - 200,
  });
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });

  // Grid snapping function
  const snapToGrid = (value) => {
    return Math.round(value / gridSize) * gridSize;
  };

  // Handle component selection
  const handleComponentPress = (component) => {
    onSelectComponent(component);
  };

  // Handle component drag start
  const handleDragStart = (component, event) => {
    const { locationX, locationY } = event.nativeEvent;
    setIsDragging(true);
    setDragOffset({ x: locationX, y: locationY });
    onSelectComponent(component);
  };

  // Handle component drag
  const handleDrag = (component, event) => {
    if (!isDragging) return;

    const { pageX, pageY } = event.nativeEvent;
    const newX = snapToGrid(pageX - dragOffset.x);
    const newY = snapToGrid(pageY - dragOffset.y);

    // Ensure component stays within canvas bounds
    const boundedX = Math.max(0, Math.min(newX, canvasSize.width - component.props.width));
    const boundedY = Math.max(0, Math.min(newY, canvasSize.height - component.props.height));

    const updatedComponent = {
      ...component,
      position: { x: boundedX, y: boundedY },
    };

    const updatedLayout = layout.map(comp => 
      comp.id === component.id ? updatedComponent : comp
    );

    onUpdateLayout(updatedLayout);
  };

  // Handle component drag end
  const handleDragEnd = () => {
    setIsDragging(false);
    setDragOffset({ x: 0, y: 0 });
  };

  // Handle component resize
  const handleResize = (component, newSize) => {
    const updatedComponent = {
      ...component,
      props: {
        ...component.props,
        width: Math.max(100, newSize.width),
        height: Math.max(50, newSize.height),
      },
    };

    const updatedLayout = layout.map(comp => 
      comp.id === component.id ? updatedComponent : comp
    );

    onUpdateLayout(updatedLayout);
  };

  // Handle component deletion
  const handleDelete = (componentId) => {
    Alert.alert(
      'Bileşeni Sil',
      'Bu bileşeni silmek istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        { 
          text: 'Sil', 
          style: 'destructive',
          onPress: () => {
            const updatedLayout = layout.filter(comp => comp.id !== componentId);
            onUpdateLayout(updatedLayout);
            onSelectComponent(null);
          }
        },
      ]
    );
  };

  // Render grid background
  const renderGrid = () => {
    if (!showGrid) return null;

    const gridLines = [];
    const cols = Math.ceil(canvasSize.width / gridSize);
    const rows = Math.ceil(canvasSize.height / gridSize);

    // Vertical lines
    for (let i = 0; i <= cols; i++) {
      gridLines.push(
        <View
          key={`v-${i}`}
          style={[
            styles.gridLine,
            styles.verticalLine,
            {
              left: i * gridSize,
              height: canvasSize.height,
              backgroundColor: theme.BORDER,
            },
          ]}
        />
      );
    }

    // Horizontal lines
    for (let i = 0; i <= rows; i++) {
      gridLines.push(
        <View
          key={`h-${i}`}
          style={[
            styles.gridLine,
            styles.horizontalLine,
            {
              top: i * gridSize,
              width: canvasSize.width,
              backgroundColor: theme.BORDER,
            },
          ]}
        />
      );
    }

    return gridLines;
  };

  // Render component on canvas
  const renderCanvasComponent = (component) => {
    const { id, type, title, props, position } = component;
    const isSelected = selectedComponent?.id === id;

    const componentStyle = {
      position: 'absolute',
      left: position?.x || 0,
      top: position?.y || 0,
      width: props.width,
      height: props.height,
      backgroundColor: theme.SURFACE,
      borderRadius: 8,
      borderWidth: isSelected ? 2 : 1,
      borderColor: isSelected ? theme.PRIMARY : theme.BORDER,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: isSelected ? 0.2 : 0.1,
      shadowRadius: 4,
      elevation: isSelected ? 4 : 2,
    };

    return (
      <TouchableOpacity
        key={id}
        style={componentStyle}
        onPress={() => handleComponentPress(component)}
        onPressIn={(event) => handleDragStart(component, event)}
        onPressOut={handleDragEnd}
        activeOpacity={0.8}
      >
        {/* Component Header */}
        <View style={[styles.componentHeader, { borderBottomColor: theme.BORDER }]}>
          <Text style={[styles.componentTitle, { color: theme.TEXT_PRIMARY }]}>
            {title}
          </Text>
          
          {isSelected && (
            <View style={styles.componentActions}>
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: theme.WARNING }]}
                onPress={() => {
                  // Open component settings
                  console.log('Edit component:', id);
                }}
              >
                <Ionicons name="settings" size={12} color={theme.SURFACE} />
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: theme.ERROR }]}
                onPress={() => handleDelete(id)}
              >
                <Ionicons name="trash" size={12} color={theme.SURFACE} />
              </TouchableOpacity>
            </View>
          )}
        </View>

        {/* Component Content */}
        <View style={styles.componentContent}>
          {type === 'chart' && (
            <View style={styles.placeholder}>
              <Ionicons name="bar-chart" size={24} color={theme.TEXT_SECONDARY} />
              <Text style={[styles.placeholderText, { color: theme.TEXT_SECONDARY }]}>
                {props.chartType?.toUpperCase()} Grafik
              </Text>
            </View>
          )}

          {type === 'table' && (
            <View style={styles.placeholder}>
              <Ionicons name="grid" size={24} color={theme.TEXT_SECONDARY} />
              <Text style={[styles.placeholderText, { color: theme.TEXT_SECONDARY }]}>
                {props.columns?.length || 0} Sütun
              </Text>
            </View>
          )}

          {type === 'kpi' && (
            <View style={styles.kpiContent}>
              <Text style={[styles.kpiValue, { color: theme.PRIMARY }]}>
                ₺12,345
              </Text>
              <Text style={[styles.kpiLabel, { color: theme.TEXT_SECONDARY }]}>
                {props.metric?.replace('_', ' ').toUpperCase()}
              </Text>
            </View>
          )}

          {type === 'text' && (
            <Text style={[
              styles.textContent, 
              { 
                color: theme.TEXT_PRIMARY,
                fontSize: props.fontSize || 14,
                fontWeight: props.fontWeight || 'normal',
              }
            ]}>
              {props.content}
            </Text>
          )}

          {type === 'filter' && (
            <View style={styles.filterContent}>
              <Ionicons name="filter" size={20} color={theme.PRIMARY} />
              <Text style={[styles.filterLabel, { color: theme.TEXT_PRIMARY }]}>
                {props.filterType?.toUpperCase()} Filtresi
              </Text>
            </View>
          )}

          {type === 'summary' && (
            <View style={styles.summaryContent}>
              <Ionicons name="list" size={20} color={theme.PRIMARY} />
              <Text style={[styles.summaryLabel, { color: theme.TEXT_PRIMARY }]}>
                {props.summaryType?.toUpperCase()} Özet
              </Text>
            </View>
          )}
        </View>

        {/* Resize Handle */}
        {isSelected && (
          <View style={[styles.resizeHandle, { backgroundColor: theme.PRIMARY }]}>
            <Ionicons name="resize" size={12} color={theme.SURFACE} />
          </View>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={[
          styles.canvas,
          {
            width: canvasSize.width,
            height: canvasSize.height,
          },
        ]}
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
      >
        {/* Grid Background */}
        {renderGrid()}

        {/* Components */}
        {layout.map(renderCanvasComponent)}

        {/* Empty State */}
        {layout.length === 0 && (
          <View style={styles.emptyState}>
            <Ionicons name="add-circle-outline" size={48} color={theme.TEXT_SECONDARY} />
            <Text style={[styles.emptyStateText, { color: theme.TEXT_SECONDARY }]}>
              Bileşenleri buraya sürükleyerek rapor oluşturun
            </Text>
          </View>
        )}
      </ScrollView>

      {/* Canvas Info */}
      <View style={[styles.canvasInfo, { backgroundColor: theme.SURFACE }]}>
        <Text style={[styles.infoText, { color: theme.TEXT_SECONDARY }]}>
          📐 {canvasSize.width} × {canvasSize.height} • 
          🧩 {layout.length} bileşen • 
          📏 {gridSize}px grid
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  canvas: {
    position: 'relative',
    minHeight: 600,
  },
  gridLine: {
    position: 'absolute',
    opacity: 0.3,
  },
  verticalLine: {
    width: 1,
  },
  horizontalLine: {
    height: 1,
  },
  componentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 8,
    borderBottomWidth: 1,
  },
  componentTitle: {
    fontSize: 12,
    fontWeight: '600',
  },
  componentActions: {
    flexDirection: 'row',
    gap: 4,
  },
  actionButton: {
    width: 20,
    height: 20,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  componentContent: {
    flex: 1,
    padding: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholder: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  placeholderText: {
    fontSize: 10,
    marginTop: 4,
    textAlign: 'center',
  },
  kpiContent: {
    alignItems: 'center',
  },
  kpiValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  kpiLabel: {
    fontSize: 10,
    marginTop: 2,
  },
  textContent: {
    textAlign: 'center',
  },
  filterContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  filterLabel: {
    fontSize: 12,
    fontWeight: '600',
  },
  summaryContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  summaryLabel: {
    fontSize: 12,
    fontWeight: '600',
  },
  resizeHandle: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 16,
    height: 16,
    borderTopLeftRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyStateText: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: 12,
  },
  canvasInfo: {
    padding: 8,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  infoText: {
    fontSize: 12,
    textAlign: 'center',
  },
});

export default ReportCanvas;
