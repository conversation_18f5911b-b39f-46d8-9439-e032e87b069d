import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Modal,
  Platform,
  KeyboardAvoidingView,
  Alert,
  ActivityIndicator
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { MaterialIcons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Picker } from '@react-native-picker/picker';
import { Colors } from '../constants/colors';
import * as investmentService from '../services/investmentService';
import { formatCurrency } from '../utils/formatters';
import { useExchangeRate } from '../contexts/ExchangeRateContext';

/**
 * Yatırım işlemi ekleme/düzenleme formu
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {boolean} props.visible - Formun görünür olup olmadığı
 * @param {Function} props.onClose - Form kapatıldığında çağrılacak fonksiyon
 * @param {Function} props.onSave - Form kaydedildiğinde çağrılacak fonksiyon
 * @param {Object} props.transaction - Düzenlenecek işlem (varsa)
 * @param {number} props.assetId - Varlık ID'si (yeni işlem için)
 * @returns {JSX.Element} Yatırım işlemi formu
 */
export default function InvestmentTransactionForm({ visible, onClose, onSave, transaction, assetId }) {
  const db = useSQLiteContext();
  const { rates } = useExchangeRate();
  
  // Durum değişkenleri
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [assets, setAssets] = useState([]);
  const [currencies, setCurrencies] = useState(['TRY', 'USD', 'EUR', 'GBP']);
  const [showDatePicker, setShowDatePicker] = useState(false);
  
  // Form değerleri
  const [formData, setFormData] = useState({
    asset_id: assetId || '',
    type: 'buy',
    quantity: '',
    price_per_unit: '',
    total_amount: '',
    fee: '0',
    date: new Date(),
    currency: 'TRY',
    notes: ''
  });
  
  // Varlıkları yükle
  useEffect(() => {
    const loadAssets = async () => {
      try {
        const assetList = await investmentService.getInvestmentAssets({ activeOnly: true });
        setAssets(assetList);
        
        // Eğer assetId verilmişse ve formData.asset_id boşsa, onu ayarla
        if (assetId && !formData.asset_id) {
          setFormData(prev => ({ ...prev, asset_id: assetId }));
        }
        
        setLoading(false);
      } catch (error) {
        console.error('Varlıkları yükleme hatası:', error);
        Alert.alert('Hata', 'Varlıklar yüklenirken bir hata oluştu.');
        setLoading(false);
      }
    };
    
    // Para birimlerini ayarla
    if (rates && rates.rates) {
      setCurrencies(['TRY', ...Object.keys(rates.rates).filter(c => c !== 'TRY')]);
    }
    
    // Düzenleme modunda ise, işlem verilerini yükle
    if (transaction) {
      setFormData({
        asset_id: transaction.asset_id.toString(),
        type: transaction.type,
        quantity: transaction.quantity.toString(),
        price_per_unit: transaction.price_per_unit.toString(),
        total_amount: transaction.total_amount.toString(),
        fee: (transaction.fee || 0).toString(),
        date: new Date(transaction.date),
        currency: transaction.currency || 'TRY',
        notes: transaction.notes || ''
      });
    }
    
    loadAssets();
  }, [db, assetId, transaction, rates]);
  
  // Toplam tutarı hesapla
  const calculateTotalAmount = () => {
    const quantity = parseFloat(formData.quantity) || 0;
    const pricePerUnit = parseFloat(formData.price_per_unit) || 0;
    const fee = parseFloat(formData.fee) || 0;
    
    let totalAmount = quantity * pricePerUnit;
    
    if (formData.type === 'buy') {
      totalAmount += fee;
    } else if (formData.type === 'sell') {
      totalAmount -= fee;
    }
    
    return totalAmount.toFixed(2);
  };
  
  // Form değişikliklerini işle
  const handleChange = (name, value) => {
    setFormData(prev => {
      const updated = { ...prev, [name]: value };
      
      // Miktar veya birim fiyat değiştiğinde toplam tutarı güncelle
      if (name === 'quantity' || name === 'price_per_unit' || name === 'fee') {
        const quantity = parseFloat(updated.quantity) || 0;
        const pricePerUnit = parseFloat(updated.price_per_unit) || 0;
        const fee = parseFloat(updated.fee) || 0;
        
        let totalAmount = quantity * pricePerUnit;
        
        if (updated.type === 'buy') {
          totalAmount += fee;
        } else if (updated.type === 'sell') {
          totalAmount -= fee;
        }
        
        updated.total_amount = totalAmount.toFixed(2);
      }
      
      return updated;
    });
  };
  
  // Tarih değişikliğini işle
  const handleDateChange = (event, selectedDate) => {
    setShowDatePicker(Platform.OS === 'ios');
    if (selectedDate) {
      setFormData(prev => ({ ...prev, date: selectedDate }));
    }
  };
  
  // Formu kaydet
  const handleSave = async () => {
    // Validasyon
    if (!formData.asset_id) {
      Alert.alert('Hata', 'Lütfen bir varlık seçin.');
      return;
    }
    
    if (!formData.quantity || parseFloat(formData.quantity) <= 0) {
      Alert.alert('Hata', 'Lütfen geçerli bir miktar girin.');
      return;
    }
    
    if (!formData.price_per_unit || parseFloat(formData.price_per_unit) <= 0) {
      Alert.alert('Hata', 'Lütfen geçerli bir birim fiyat girin.');
      return;
    }
    
    try {
      setSaving(true);
      
      const transactionData = {
        asset_id: parseInt(formData.asset_id),
        type: formData.type,
        quantity: parseFloat(formData.quantity),
        price_per_unit: parseFloat(formData.price_per_unit),
        total_amount: parseFloat(formData.total_amount),
        fee: parseFloat(formData.fee) || 0,
        date: formData.date.toISOString().split('T')[0],
        currency: formData.currency,
        notes: formData.notes
      };
      
      if (transaction && transaction.id) {
        // Mevcut işlemi güncelle
        await investmentService.updateInvestmentTransaction(transaction.id, transactionData);
      } else {
        // Yeni işlem ekle
        await investmentService.addInvestmentTransaction(transactionData);
      }
      
      setSaving(false);
      
      if (onSave) {
        onSave();
      }
      
      onClose();
    } catch (error) {
      console.error('İşlem kaydedilirken hata:', error);
      Alert.alert('Hata', 'İşlem kaydedilirken bir hata oluştu.');
      setSaving(false);
    }
  };
  
  // İşlem tipini formatla
  const formatTransactionType = (type) => {
    switch (type) {
      case 'buy': return 'Alım';
      case 'sell': return 'Satım';
      case 'dividend': return 'Temettü';
      case 'interest': return 'Faiz';
      case 'fee': return 'Komisyon/Ücret';
      case 'transfer': return 'Transfer';
      case 'other': return 'Diğer';
      default: return type;
    }
  };
  
  if (!visible) return null;
  
  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      >
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.title}>
              {transaction?.id ? 'İşlemi Düzenle' : 'Yeni Yatırım İşlemi'}
            </Text>
            <TouchableOpacity onPress={onClose}>
              <MaterialIcons name="close" size={24} color="#999" />
            </TouchableOpacity>
          </View>
          
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={Colors.PRIMARY} />
              <Text style={styles.loadingText}>Yükleniyor...</Text>
            </View>
          ) : (
            <ScrollView style={styles.form}>
              {/* Varlık Seçimi */}
              <View style={styles.formGroup}>
                <Text style={styles.label}>Varlık</Text>
                <View style={styles.pickerContainer}>
                  <Picker
                    selectedValue={formData.asset_id}
                    onValueChange={(value) => handleChange('asset_id', value)}
                    style={styles.picker}
                    enabled={!assetId && !transaction}
                  >
                    <Picker.Item label="Varlık Seçin" value="" />
                    {assets.map((asset) => (
                      <Picker.Item 
                        key={asset.id} 
                        label={`${asset.name} (${asset.symbol})`} 
                        value={asset.id.toString()} 
                      />
                    ))}
                  </Picker>
                </View>
              </View>
              
              {/* İşlem Tipi */}
              <View style={styles.formGroup}>
                <Text style={styles.label}>İşlem Tipi</Text>
                <View style={styles.pickerContainer}>
                  <Picker
                    selectedValue={formData.type}
                    onValueChange={(value) => handleChange('type', value)}
                    style={styles.picker}
                  >
                    <Picker.Item label="Alım" value="buy" />
                    <Picker.Item label="Satım" value="sell" />
                    <Picker.Item label="Temettü" value="dividend" />
                    <Picker.Item label="Faiz" value="interest" />
                    <Picker.Item label="Komisyon/Ücret" value="fee" />
                    <Picker.Item label="Transfer" value="transfer" />
                    <Picker.Item label="Diğer" value="other" />
                  </Picker>
                </View>
              </View>
              
              {/* Miktar */}
              <View style={styles.formGroup}>
                <Text style={styles.label}>Miktar</Text>
                <TextInput
                  style={styles.input}
                  value={formData.quantity}
                  onChangeText={(value) => handleChange('quantity', value)}
                  placeholder="Miktar"
                  keyboardType="decimal-pad"
                />
              </View>
              
              {/* Birim Fiyat */}
              <View style={styles.formGroup}>
                <Text style={styles.label}>Birim Fiyat</Text>
                <TextInput
                  style={styles.input}
                  value={formData.price_per_unit}
                  onChangeText={(value) => handleChange('price_per_unit', value)}
                  placeholder="Birim Fiyat"
                  keyboardType="decimal-pad"
                />
              </View>
              
              {/* Komisyon/Ücret */}
              <View style={styles.formGroup}>
                <Text style={styles.label}>Komisyon/Ücret</Text>
                <TextInput
                  style={styles.input}
                  value={formData.fee}
                  onChangeText={(value) => handleChange('fee', value)}
                  placeholder="Komisyon/Ücret"
                  keyboardType="decimal-pad"
                />
              </View>
              
              {/* Toplam Tutar */}
              <View style={styles.formGroup}>
                <Text style={styles.label}>Toplam Tutar</Text>
                <TextInput
                  style={[styles.input, styles.disabledInput]}
                  value={formData.total_amount}
                  editable={false}
                  placeholder="Toplam Tutar"
                />
                <Text style={styles.helperText}>
                  {formData.type === 'buy' ? 'Miktar × Birim Fiyat + Komisyon' : 'Miktar × Birim Fiyat - Komisyon'}
                </Text>
              </View>
              
              {/* Tarih */}
              <View style={styles.formGroup}>
                <Text style={styles.label}>Tarih</Text>
                <TouchableOpacity
                  style={styles.datePickerButton}
                  onPress={() => setShowDatePicker(true)}
                >
                  <Text style={styles.datePickerButtonText}>
                    {formData.date.toLocaleDateString('tr-TR')}
                  </Text>
                  <MaterialIcons name="calendar-today" size={20} color="#666" />
                </TouchableOpacity>
                {showDatePicker && (
                  <DateTimePicker
                    value={formData.date}
                    mode="date"
                    display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                    onChange={handleDateChange}
                  />
                )}
              </View>
              
              {/* Para Birimi */}
              <View style={styles.formGroup}>
                <Text style={styles.label}>Para Birimi</Text>
                <View style={styles.pickerContainer}>
                  <Picker
                    selectedValue={formData.currency}
                    onValueChange={(value) => handleChange('currency', value)}
                    style={styles.picker}
                  >
                    {currencies.map((currency) => (
                      <Picker.Item key={currency} label={currency} value={currency} />
                    ))}
                  </Picker>
                </View>
              </View>
              
              {/* Notlar */}
              <View style={styles.formGroup}>
                <Text style={styles.label}>Notlar</Text>
                <TextInput
                  style={[styles.input, styles.textArea]}
                  value={formData.notes}
                  onChangeText={(value) => handleChange('notes', value)}
                  placeholder="Notlar"
                  multiline
                  numberOfLines={4}
                />
              </View>
              
              {/* Butonlar */}
              <View style={styles.buttonContainer}>
                <TouchableOpacity
                  style={[styles.button, styles.cancelButton]}
                  onPress={onClose}
                  disabled={saving}
                >
                  <Text style={styles.buttonText}>İptal</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.button, styles.saveButton]}
                  onPress={handleSave}
                  disabled={saving}
                >
                  {saving ? (
                    <ActivityIndicator size="small" color="#fff" />
                  ) : (
                    <Text style={styles.buttonText}>Kaydet</Text>
                  )}
                </TouchableOpacity>
              </View>
            </ScrollView>
          )}
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  content: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: '90%',
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  form: {
    flex: 1,
    paddingTop: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    color: '#333',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: '#333',
  },
  disabledInput: {
    backgroundColor: '#f5f5f5',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    overflow: 'hidden',
  },
  picker: {
    height: 50,
  },
  datePickerButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  datePickerButtonText: {
    fontSize: 16,
    color: '#333',
  },
  helperText: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
    marginBottom: 24,
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#f5f5f5',
    marginRight: 8,
  },
  saveButton: {
    backgroundColor: Colors.PRIMARY,
    marginLeft: 8,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
});
