import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import tokens from '../tokens';

/**
 * Modern Card Component
 * Design system'e uygun, tutarlı kart bileşeni
 */
const Card = ({
  children,
  variant = 'default',
  padding = 'md',
  margin = 'none',
  shadow = 'sm',
  borderRadius = 'lg',
  onPress,
  style,
  ...props
}) => {
  // Variant stilleri
  const getVariantStyles = () => {
    switch (variant) {
      case 'default':
        return {
          backgroundColor: '#ffffff',
          borderColor: tokens.colors.gray[200],
          borderWidth: 1,
        };
      case 'elevated':
        return {
          backgroundColor: '#ffffff',
          borderColor: 'transparent',
          borderWidth: 0,
        };
      case 'outlined':
        return {
          backgroundColor: 'transparent',
          borderColor: tokens.colors.gray[300],
          borderWidth: 1,
        };
      case 'filled':
        return {
          backgroundColor: tokens.colors.gray[50],
          borderColor: 'transparent',
          borderWidth: 0,
        };
      case 'primary':
        return {
          backgroundColor: tokens.colors.primary[50],
          borderColor: tokens.colors.primary[200],
          borderWidth: 1,
        };
      case 'success':
        return {
          backgroundColor: tokens.colors.success[50],
          borderColor: tokens.colors.success[200],
          borderWidth: 1,
        };
      case 'danger':
        return {
          backgroundColor: tokens.colors.danger[50],
          borderColor: tokens.colors.danger[200],
          borderWidth: 1,
        };
      case 'warning':
        return {
          backgroundColor: tokens.colors.warning[50],
          borderColor: tokens.colors.warning[200],
          borderWidth: 1,
        };
      default:
        return {
          backgroundColor: '#ffffff',
          borderColor: tokens.colors.gray[200],
          borderWidth: 1,
        };
    }
  };

  // Padding stilleri
  const getPaddingStyles = () => {
    switch (padding) {
      case 'none':
        return { padding: 0 };
      case 'xs':
        return { padding: tokens.spacing[2] };
      case 'sm':
        return { padding: tokens.spacing[3] };
      case 'md':
        return { padding: tokens.spacing[4] };
      case 'lg':
        return { padding: tokens.spacing[6] };
      case 'xl':
        return { padding: tokens.spacing[8] };
      default:
        return { padding: tokens.spacing[4] };
    }
  };

  // Margin stilleri
  const getMarginStyles = () => {
    switch (margin) {
      case 'none':
        return { margin: 0 };
      case 'xs':
        return { margin: tokens.spacing[1] };
      case 'sm':
        return { margin: tokens.spacing[2] };
      case 'md':
        return { margin: tokens.spacing[3] };
      case 'lg':
        return { margin: tokens.spacing[4] };
      case 'xl':
        return { margin: tokens.spacing[6] };
      default:
        return { margin: 0 };
    }
  };

  // Shadow stilleri
  const getShadowStyles = () => {
    switch (shadow) {
      case 'none':
        return {};
      case 'xs':
        return tokens.shadows.xs;
      case 'sm':
        return tokens.shadows.sm;
      case 'base':
        return tokens.shadows.base;
      case 'md':
        return tokens.shadows.md;
      case 'lg':
        return tokens.shadows.lg;
      case 'xl':
        return tokens.shadows.xl;
      default:
        return tokens.shadows.sm;
    }
  };

  // Border radius stilleri
  const getBorderRadiusStyles = () => {
    switch (borderRadius) {
      case 'none':
        return { borderRadius: tokens.borderRadius.none };
      case 'sm':
        return { borderRadius: tokens.borderRadius.sm };
      case 'base':
        return { borderRadius: tokens.borderRadius.base };
      case 'md':
        return { borderRadius: tokens.borderRadius.md };
      case 'lg':
        return { borderRadius: tokens.borderRadius.lg };
      case 'xl':
        return { borderRadius: tokens.borderRadius.xl };
      case '2xl':
        return { borderRadius: tokens.borderRadius['2xl'] };
      case '3xl':
        return { borderRadius: tokens.borderRadius['3xl'] };
      case 'full':
        return { borderRadius: tokens.borderRadius.full };
      default:
        return { borderRadius: tokens.borderRadius.lg };
    }
  };

  const variantStyles = getVariantStyles();
  const paddingStyles = getPaddingStyles();
  const marginStyles = getMarginStyles();
  const shadowStyles = getShadowStyles();
  const borderRadiusStyles = getBorderRadiusStyles();

  const cardStyles = [
    styles.card,
    variantStyles,
    paddingStyles,
    marginStyles,
    shadowStyles,
    borderRadiusStyles,
    style,
  ];

  // Eğer onPress varsa TouchableOpacity kullan
  if (onPress) {
    return (
      <TouchableOpacity
        style={cardStyles}
        onPress={onPress}
        activeOpacity={0.8}
        {...props}
      >
        {children}
      </TouchableOpacity>
    );
  }

  // Yoksa normal View kullan
  return (
    <View style={cardStyles} {...props}>
      {children}
    </View>
  );
};

/**
 * Card Header Component
 */
export const CardHeader = ({ children, style, ...props }) => (
  <View style={[styles.header, style]} {...props}>
    {children}
  </View>
);

/**
 * Card Body Component
 */
export const CardBody = ({ children, style, ...props }) => (
  <View style={[styles.body, style]} {...props}>
    {children}
  </View>
);

/**
 * Card Footer Component
 */
export const CardFooter = ({ children, style, ...props }) => (
  <View style={[styles.footer, style]} {...props}>
    {children}
  </View>
);

const styles = StyleSheet.create({
  card: {
    overflow: 'hidden',
  },
  header: {
    paddingBottom: tokens.spacing[3],
    borderBottomWidth: 1,
    borderBottomColor: tokens.colors.gray[100],
    marginBottom: tokens.spacing[3],
  },
  body: {
    flex: 1,
  },
  footer: {
    paddingTop: tokens.spacing[3],
    borderTopWidth: 1,
    borderTopColor: tokens.colors.gray[100],
    marginTop: tokens.spacing[3],
  },
});

export default Card;
