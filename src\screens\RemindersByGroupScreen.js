import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
  ActivityIndicator
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import { format, parseISO, differenceInDays } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Colors } from '../constants/colors';
import * as reminderService from '../services/reminderService';
import * as reminderGroupService from '../services/reminderGroupService';

/**
 * Gruptaki Hatırlatıcılar Ekranı
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @param {Object} props.route - Route nesnesi
 * @returns {JSX.Element} Gruptaki Hatırlatıcılar Ekranı
 */
export default function RemindersByGroupScreen({ navigation, route }) {
  const db = useSQLiteContext();
  const { groupId } = route?.params || {};

  // Durum
  const [reminders, setReminders] = useState([]);
  const [group, setGroup] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Verileri yükle
  const loadData = useCallback(async () => {
    if (!groupId) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);

      // Grup bilgilerini getir
      const groupData = await reminderGroupService.getReminderGroupById(db, groupId);
      setGroup(groupData);

      // Gruptaki hatırlatıcıları getir
      const options = {
        sortBy: 'scheduled_at',
        sortOrder: 'asc'
      };

      const groupReminders = await reminderGroupService.getRemindersByGroup(db, groupId, options);
      setReminders(groupReminders);

      setLoading(false);
    } catch (error) {
      console.error('Grup hatırlatıcıları yükleme hatası:', error);
      Alert.alert('Hata', 'Grup hatırlatıcıları yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  }, [db, groupId]);

  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [loadData])
  );

  // Başlık güncelleme
  useEffect(() => {
    if (group && navigation?.setOptions) {
      navigation.setOptions({
        title: group.name
      });
    }
  }, [group, navigation]);

  // Yenileme işlemi
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  // Hatırlatıcı detayına git
  const goToReminderDetail = (reminderId) => {
    navigation.navigate('ReminderDetail', { reminderId });
  };

  // Yeni hatırlatıcı ekle
  const addNewReminder = () => {
    navigation.navigate('ReminderForm', { groupId });
  };

  // Hatırlatıcıyı etkinleştir/devre dışı bırak
  const toggleReminderEnabled = async (reminderId, currentState) => {
    try {
      const newState = !currentState;
      await reminderService.toggleReminderEnabled(db, reminderId, newState);

      // Listeyi güncelle
      setReminders(reminders.map(reminder => {
        if (reminder.id === reminderId) {
          return {
            ...reminder,
            is_enabled: newState ? 1 : 0
          };
        }
        return reminder;
      }));
    } catch (error) {
      console.error('Hatırlatıcı etkinleştirme hatası:', error);
      Alert.alert('Hata', 'Hatırlatıcı durumu değiştirilirken bir hata oluştu.');
    }
  };

  // Hatırlatıcıyı sil
  const deleteReminder = (reminderId) => {
    Alert.alert(
      'Hatırlatıcıyı Sil',
      'Bu hatırlatıcıyı silmek istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await reminderService.deleteReminder(db, reminderId);

              // Listeyi güncelle
              setReminders(reminders.filter(reminder => reminder.id !== reminderId));

              Alert.alert('Başarılı', 'Hatırlatıcı silindi.');
            } catch (error) {
              console.error('Hatırlatıcı silme hatası:', error);
              Alert.alert('Hata', 'Hatırlatıcı silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };

  // Hatırlatıcı öğesi
  const renderReminderItem = ({ item }) => {
    // Tarih ve saat bilgilerini ayır
    const scheduledDate = parseISO(item.scheduled_at);
    const formattedDate = format(scheduledDate, 'dd MMM yyyy', { locale: tr });
    const formattedTime = format(scheduledDate, 'HH:mm');

    // Kalan gün hesapla
    const today = new Date();
    const daysRemaining = differenceInDays(scheduledDate, today);

    // Öncelik rengi
    let priorityColor = Colors.WARNING;
    if (item.priority === 'low') {
      priorityColor = Colors.SUCCESS;
    } else if (item.priority === 'high') {
      priorityColor = Colors.DANGER;
    }

    // Tekrarlama ikonu
    let repeatIcon = null;
    if (item.repeat_type !== 'once') {
      repeatIcon = (
        <MaterialIcons
          name={
            item.repeat_type === 'daily' ? 'today' :
            item.repeat_type === 'weekly' ? 'view-week' :
            item.repeat_type === 'monthly' ? 'date-range' :
            'event-repeat'
          }
          size={16}
          color={Colors.TEXT_LIGHT}
        />
      );
    }

    return (
      <TouchableOpacity
        style={styles.reminderItem}
        onPress={() => goToReminderDetail(item.id)}
      >
        <View style={styles.reminderHeader}>
          <View style={[styles.priorityIndicator, { backgroundColor: priorityColor }]} />
          <Text style={styles.reminderTitle} numberOfLines={1}>
            {item.title}
          </Text>
          <View style={styles.reminderActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => toggleReminderEnabled(item.id, item.is_enabled === 1)}
            >
              <MaterialIcons
                name={item.is_enabled === 1 ? 'notifications-active' : 'notifications-off'}
                size={20}
                color={item.is_enabled === 1 ? Colors.PRIMARY : Colors.GRAY_500}
              />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => deleteReminder(item.id)}
            >
              <MaterialIcons name="delete" size={20} color={Colors.DANGER} />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.reminderContent}>
          {item.message && (
            <Text style={styles.reminderMessage} numberOfLines={2}>
              {item.message}
            </Text>
          )}

          <View style={styles.reminderInfo}>
            <View style={styles.reminderInfoItem}>
              <MaterialIcons name="event" size={16} color={Colors.TEXT_LIGHT} />
              <Text style={styles.reminderInfoText}>{formattedDate}</Text>
            </View>

            <View style={styles.reminderInfoItem}>
              <MaterialIcons name="access-time" size={16} color={Colors.TEXT_LIGHT} />
              <Text style={styles.reminderInfoText}>{formattedTime}</Text>
            </View>

            {repeatIcon && (
              <View style={styles.reminderInfoItem}>
                {repeatIcon}
                <Text style={styles.reminderInfoText}>Tekrarlı</Text>
              </View>
            )}

            {item.category_name && (
              <View style={styles.reminderInfoItem}>
                <View style={[
                  styles.categoryIndicator,
                  { backgroundColor: item.category_color || Colors.PRIMARY }
                ]} />
                <Text style={styles.reminderInfoText}>{item.category_name}</Text>
              </View>
            )}
          </View>
        </View>

        {daysRemaining >= 0 ? (
          <View style={[
            styles.reminderFooter,
            daysRemaining === 0 ? styles.todayFooter :
            daysRemaining <= 1 ? styles.urgentFooter :
            daysRemaining <= 3 ? styles.soonFooter :
            styles.normalFooter
          ]}>
            <MaterialIcons
              name={
                daysRemaining === 0 ? 'today' :
                daysRemaining <= 1 ? 'priority-high' :
                'event-available'
              }
              size={16}
              color="#fff"
            />
            <Text style={styles.footerText}>
              {daysRemaining === 0 ? 'Bugün' :
               daysRemaining === 1 ? 'Yarın' :
               `${daysRemaining} gün sonra`}
            </Text>
          </View>
        ) : (
          <View style={styles.pastFooter}>
            <MaterialIcons name="event-busy" size={16} color="#fff" />
            <Text style={styles.footerText}>
              {Math.abs(daysRemaining) === 1 ? 'Dün' :
               `${Math.abs(daysRemaining)} gün önce`}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  // Boş durum
  const renderEmptyState = () => {
    if (!group) {
      return (
        <View style={styles.emptyContainer}>
          <MaterialIcons name="error-outline" size={64} color={Colors.GRAY_500} />
          <Text style={styles.emptyTitle}>Grup Bulunamadı</Text>
          <Text style={styles.emptyText}>
            Belirtilen grup bulunamadı veya silinmiş olabilir.
          </Text>
          <TouchableOpacity
            style={styles.emptyButton}
            onPress={() => navigation.goBack()}
          >
            <MaterialIcons name="arrow-back" size={20} color="#fff" />
            <Text style={styles.emptyButtonText}>Geri Dön</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <View style={styles.emptyContainer}>
        <MaterialIcons name="notifications-none" size={64} color={Colors.GRAY_300} />
        <Text style={styles.emptyTitle}>Hatırlatıcı Bulunamadı</Text>
        <Text style={styles.emptyText}>
          Bu grupta henüz hatırlatıcı bulunmuyor. Yeni bir hatırlatıcı ekleyin.
        </Text>
        <TouchableOpacity
          style={styles.emptyButton}
          onPress={addNewReminder}
        >
          <MaterialIcons name="add" size={20} color="#fff" />
          <Text style={styles.emptyButtonText}>Yeni Hatırlatıcı</Text>
        </TouchableOpacity>
      </View>
    );
  };

  // Yükleniyor durumu
  if (loading && !refreshing) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={styles.loadingText}>Yükleniyor...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Grup Bilgisi */}
      {group && (
        <View style={[
          styles.groupInfoContainer,
          { borderLeftColor: group.color || Colors.PRIMARY }
        ]}>
          <View style={styles.groupHeader}>
            <View style={styles.groupTitleContainer}>
              <MaterialIcons
                name={group.icon || 'folder'}
                size={24}
                color={group.color || Colors.PRIMARY}
              />
              <Text style={styles.groupTitle}>{group.name}</Text>
            </View>

            {group.is_default === 1 && (
              <View style={styles.defaultBadge}>
                <Text style={styles.defaultBadgeText}>Varsayılan</Text>
              </View>
            )}
          </View>

          {group.description && (
            <Text style={styles.groupDescription}>{group.description}</Text>
          )}
        </View>
      )}

      {/* İçerik */}
      <FlatList
        data={reminders}
        renderItem={renderReminderItem}
        keyExtractor={item => item.id.toString()}
        contentContainerStyle={styles.listContent}
        ListEmptyComponent={renderEmptyState}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[Colors.PRIMARY]}
          />
        }
      />

      {/* Yeni Hatırlatıcı Butonu */}
      {group && (
        <TouchableOpacity
          style={styles.floatingButton}
          onPress={addNewReminder}
        >
          <MaterialIcons name="add" size={24} color="#fff" />
        </TouchableOpacity>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: Colors.PRIMARY,
  },
  groupInfoContainer: {
    backgroundColor: Colors.GRAY_100,
    padding: 16,
    borderLeftWidth: 4,
    marginBottom: 8,
  },
  groupHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  groupTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  groupTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    marginLeft: 8,
  },
  defaultBadge: {
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 12,
  },
  defaultBadgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  groupDescription: {
    fontSize: 14,
    color: Colors.TEXT_LIGHT,
    marginTop: 8,
  },
  listContent: {
    padding: 16,
    paddingBottom: 80,
  },
  reminderItem: {
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    overflow: 'hidden',
  },
  reminderHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  priorityIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  reminderTitle: {
    flex: 1,
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
  },
  reminderActions: {
    flexDirection: 'row',
  },
  actionButton: {
    marginLeft: 12,
  },
  reminderContent: {
    padding: 12,
    paddingTop: 0,
  },
  reminderMessage: {
    fontSize: 14,
    color: Colors.TEXT_LIGHT,
    marginBottom: 8,
  },
  reminderInfo: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  reminderInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    marginBottom: 4,
  },
  reminderInfoText: {
    fontSize: 12,
    color: Colors.TEXT_LIGHT,
    marginLeft: 4,
  },
  categoryIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  reminderFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    justifyContent: 'center',
  },
  todayFooter: {
    backgroundColor: Colors.DANGER,
  },
  urgentFooter: {
    backgroundColor: Colors.WARNING,
  },
  soonFooter: {
    backgroundColor: Colors.INFO,
  },
  normalFooter: {
    backgroundColor: Colors.SUCCESS,
  },
  pastFooter: {
    backgroundColor: Colors.GRAY_500,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    justifyContent: 'center',
  },
  footerText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    marginLeft: 4,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
    marginTop: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    color: Colors.TEXT_LIGHT,
    textAlign: 'center',
    marginBottom: 24,
  },
  emptyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  emptyButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  floatingButton: {
    position: 'absolute',
    right: 16,
    bottom: 16,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
});
