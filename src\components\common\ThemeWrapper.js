import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useTheme } from '../../theme/ThemeProvider';
import { getColor, BaseColors } from '../../utils/colorUtils';

/**
 * Tema uyumlu wrapper bileşeni
 * İçeriği aktif temaya göre stillendiren bir kapsayıcı
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {React.ReactNode} props.children - Alt bileşenler
 * @param {Object} [props.style] - Ek stil özellikleri
 * @param {boolean} [props.useSafeArea=false] - Safe area padding kullanılsın mı?
 * @param {boolean} [props.useThemedBackground=true] - Tema arka plan rengi kullanılsın mı?
 * @returns {JSX.Element} ThemeWrapper bileşeni
 */
const ThemeWrapper = ({
  children,
  style,
  useSafeArea = false,
  useThemedBackground = true
}) => {
  const { theme, isDarkMode } = useTheme();
  
  // Tema yoksa veya henüz yüklenmemişse güvenli renkler kullan
  const backgroundColor = useThemedBackground 
    ? (theme?.colors?.background || (isDarkMode ? '#121212' : getColor('WHITE', '#FFFFFF')))
    : 'transparent';
  
  return (
    <View
      style={[
        styles.container,
        { backgroundColor },
        useSafeArea && styles.safeArea,
        style
      ]}
    >
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    paddingTop: 50, // SafeAreaView yoksa varsayılan değer
  }
});

export default ThemeWrapper;
