import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  TextInput,
  Modal,
  ActivityIndicator,
  RefreshControl
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import * as reminderTagService from '../services/reminderTagService';
import { Colors } from '../constants/colors';

const ReminderTagsScreen = ({ navigation }) => {
  const db = useSQLiteContext();
  const [tags, setTags] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingTag, setEditingTag] = useState(null);
  const [tagName, setTagName] = useState('');
  const [tagColor, setTagColor] = useState('#3498db');

  // Renk seçenekleri
  const colorOptions = [
    '#3498db', // Mavi
    '#2ecc71', // Yeşil
    '#e74c3c', // Kırmızı
    '#f39c12', // Turuncu
    '#9b59b6', // Mor
    '#1abc9c', // Turkuaz
    '#34495e', // Lacivert
    '#e67e22', // Turuncu-Kahve
    '#95a5a6', // Gri
    '#16a085', // Koyu Turkuaz
  ];

  // Verileri yükle
  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      const tagData = await reminderTagService.getAllTags(db);
      setTags(tagData);
      setLoading(false);
    } catch (error) {
      console.error('Etiketleri yükleme hatası:', error);
      Alert.alert('Hata', 'Etiketler yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  }, [db]);

  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [loadData])
  );

  // Yenileme işlemi
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  // Modal'ı aç
  const openAddModal = () => {
    setEditingTag(null);
    setTagName('');
    setTagColor('#3498db');
    setModalVisible(true);
  };

  // Modal'ı aç (düzenleme modu)
  const openEditModal = (tag) => {
    setEditingTag(tag);
    setTagName(tag.name);
    setTagColor(tag.color || '#3498db');
    setModalVisible(true);
  };

  // Etiket ekle/güncelle
  const saveTag = async () => {
    if (!tagName.trim()) {
      Alert.alert('Hata', 'Etiket adı boş olamaz.');
      return;
    }

    try {
      if (editingTag) {
        // Etiketi güncelle
        await reminderTagService.updateTag(db, editingTag.id, {
          name: tagName.trim(),
          color: tagColor
        });
        Alert.alert('Başarılı', 'Etiket güncellendi.');
      } else {
        // Yeni etiket ekle
        await reminderTagService.addTag(db, {
          name: tagName.trim(),
          color: tagColor
        });
        Alert.alert('Başarılı', 'Etiket eklendi.');
      }

      setModalVisible(false);
      loadData();
    } catch (error) {
      console.error('Etiket kaydetme hatası:', error);
      Alert.alert('Hata', 'Etiket kaydedilirken bir hata oluştu.');
    }
  };

  // Etiketi sil
  const deleteTag = async (tag) => {
    Alert.alert(
      'Etiketi Sil',
      `"${tag.name}" etiketini silmek istediğinize emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await reminderTagService.deleteTag(db, tag.id);
              Alert.alert('Başarılı', 'Etiket silindi.');
              loadData();
            } catch (error) {
              console.error('Etiket silme hatası:', error);
              Alert.alert('Hata', 'Etiket silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };

  // Etiket listesi öğesi
  const renderTagItem = ({ item }) => (
    <View style={styles.tagItem}>
      <View style={styles.tagHeader}>
        <View style={[styles.tagIndicator, { backgroundColor: item.color || '#3498db' }]} />
        <Text style={styles.tagName}>{item.name}</Text>
        <View style={styles.tagActions}>
          <TouchableOpacity
            style={styles.tagAction}
            onPress={() => openEditModal(item)}
          >
            <MaterialIcons name="edit" size={20} color={Colors.PRIMARY} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.tagAction}
            onPress={() => deleteTag(item)}
          >
            <MaterialIcons name="delete" size={20} color={Colors.DANGER} />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  // Etiket listesi boş durumu
  const renderEmptyList = () => (
    <View style={styles.emptyContainer}>
      <MaterialIcons name="label" size={64} color={Colors.GRAY_300} />
      <Text style={styles.emptyText}>Henüz etiket eklenmemiş</Text>
      <Text style={styles.emptySubtext}>Hatırlatıcılarınızı organize etmek için etiketler ekleyin</Text>
      <TouchableOpacity
        style={styles.emptyButton}
        onPress={openAddModal}
      >
        <Text style={styles.emptyButtonText}>Etiket Ekle</Text>
      </TouchableOpacity>
    </View>
  );

  // Renk seçici
  const renderColorPicker = () => (
    <View style={styles.colorPickerContainer}>
      <Text style={styles.colorPickerTitle}>Renk Seçin</Text>
      <View style={styles.colorOptions}>
        {colorOptions.map((color) => (
          <TouchableOpacity
            key={color}
            style={[
              styles.colorOption,
              { backgroundColor: color },
              tagColor === color && styles.colorOptionSelected
            ]}
            onPress={() => setTagColor(color)}
          />
        ))}
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Başlık ve Ekle Butonu */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Etiketler</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={openAddModal}
        >
          <MaterialIcons name="add" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      {/* Etiket Listesi */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.PRIMARY} />
        </View>
      ) : (
        <FlatList
          data={tags}
          renderItem={renderTagItem}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.listContent}
          ListEmptyComponent={renderEmptyList}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[Colors.PRIMARY]}
            />
          }
        />
      )}

      {/* Etiket Ekleme/Düzenleme Modal */}
      <Modal
        visible={modalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {editingTag ? 'Etiketi Düzenle' : 'Yeni Etiket Ekle'}
              </Text>
              <TouchableOpacity
                style={styles.modalCloseButton}
                onPress={() => setModalVisible(false)}
              >
                <MaterialIcons name="close" size={24} color={Colors.TEXT_DARK} />
              </TouchableOpacity>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.formLabel}>Etiket Adı</Text>
              <TextInput
                style={styles.formInput}
                value={tagName}
                onChangeText={setTagName}
                placeholder="Etiket adını girin"
                placeholderTextColor={Colors.TEXT_LIGHT}
              />
            </View>

            {renderColorPicker()}

            <View style={styles.modalActions}>
              <TouchableOpacity
                style={[styles.modalButton, styles.modalCancelButton]}
                onPress={() => setModalVisible(false)}
              >
                <Text style={styles.modalButtonText}>İptal</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.modalSaveButton]}
                onPress={saveTag}
              >
                <Text style={[styles.modalButtonText, styles.modalSaveButtonText]}>
                  {editingTag ? 'Güncelle' : 'Ekle'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
  },
  addButton: {
    backgroundColor: Colors.PRIMARY,
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContent: {
    padding: 16,
    flexGrow: 1,
  },
  tagItem: {
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
  },
  tagHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tagIndicator: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 12,
  },
  tagName: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.TEXT_DARK,
    flex: 1,
  },
  tagActions: {
    flexDirection: 'row',
  },
  tagAction: {
    padding: 8,
    marginLeft: 4,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: Colors.TEXT_LIGHT,
    textAlign: 'center',
    marginTop: 8,
    marginHorizontal: 32,
  },
  emptyButton: {
    backgroundColor: Colors.PRIMARY,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 24,
  },
  emptyButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 12,
    width: '90%',
    maxWidth: 400,
    padding: 24,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
  },
  modalCloseButton: {
    padding: 4,
  },
  formGroup: {
    marginBottom: 16,
  },
  formLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.TEXT_DARK,
    marginBottom: 8,
  },
  formInput: {
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: Colors.TEXT_DARK,
  },
  colorPickerContainer: {
    marginBottom: 24,
  },
  colorPickerTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.TEXT_DARK,
    marginBottom: 12,
  },
  colorOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  colorOption: {
    width: 32,
    height: 32,
    borderRadius: 16,
    margin: 4,
  },
  colorOptionSelected: {
    borderWidth: 2,
    borderColor: '#000',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  modalButton: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    marginLeft: 8,
  },
  modalCancelButton: {
    backgroundColor: Colors.GRAY_200,
  },
  modalSaveButton: {
    backgroundColor: Colors.PRIMARY,
  },
  modalButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.TEXT_DARK,
  },
  modalSaveButtonText: {
    color: '#fff',
  },
});

export default ReminderTagsScreen;
