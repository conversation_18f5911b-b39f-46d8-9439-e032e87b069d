import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { useAuth } from '../context/AuthContext';
import { useAppContext } from '../context/AppContext';

/**
 * Test ekranı - Basit authentication test ekranı
 */
export default function AuthTestScreen({ navigation }) {
  const { theme } = useAppContext();
  const { isAuthenticated, isFirstTime, setIsAuthenticated, resetAllData } = useAuth();

  const handleLogin = () => {
    setIsAuthenticated(true);
    Alert.alert('✅ Başarılı', 'Giriş yapıldı!');
  };

  const handleLogout = () => {
    setIsAuthenticated(false);
    Alert.alert('🔓 Çıkış', 'Çıkış yapıldı!');
  };

  const handleResetData = async () => {
    try {
      const result = await resetAllData();
      if (result.success) {
        Alert.alert('✅ Başarılı', 'Tüm veriler sıfırlandı!');
      } else {
        Alert.alert('❌ Hata', result.error || 'Sıfırlama başarısız');
      }
    } catch (error) {
      Alert.alert('❌ Hata', 'Sıfırlama sırasında hata oluştu');
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
        🔐 Authentication Test
      </Text>
      
      <View style={styles.infoContainer}>
        <Text style={[styles.label, { color: theme.TEXT_SECONDARY }]}>
          Kimlik Doğrulandı: {isAuthenticated ? '✅ Evet' : '❌ Hayır'}
        </Text>
        
        <Text style={[styles.label, { color: theme.TEXT_SECONDARY }]}>
          İlk Kez Açılış: {isFirstTime ? '✅ Evet' : '❌ Hayır'}
        </Text>
        
        <Text style={[styles.label, { color: theme.TEXT_SECONDARY }]}>
          PIN Sistemi: ❌ Kaldırıldı
        </Text>
        
        <Text style={[styles.label, { color: theme.TEXT_SECONDARY }]}>
          Biometrik Sistemi: ❌ Kaldırıldı
        </Text>
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, { backgroundColor: theme.PRIMARY }]}
          onPress={handleLogin}
        >
          <Text style={[styles.buttonText, { color: theme.SURFACE }]}>
            🔓 Giriş Yap
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, { backgroundColor: theme.ERROR || '#FF6B6B' }]}
          onPress={handleLogout}
        >
          <Text style={[styles.buttonText, { color: theme.SURFACE }]}>
            🔒 Çıkış Yap
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, { backgroundColor: '#FF6B6B' }]}
          onPress={handleResetData}
        >
          <Text style={[styles.buttonText, { color: theme.SURFACE }]}>
            🗑️ Tüm Verileri Sıfırla
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, { backgroundColor: theme.TEXT_SECONDARY }]}
          onPress={() => navigation?.goBack()}
        >
          <Text style={[styles.buttonText, { color: theme.SURFACE }]}>
            ← Geri Dön
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 30,
  },
  infoContainer: {
    marginBottom: 30,
    padding: 20,
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderRadius: 12,
  },
  label: {
    fontSize: 16,
    marginBottom: 10,
    fontWeight: '500',
  },
  buttonContainer: {
    gap: 15,
  },
  button: {
    padding: 15,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});
