import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useTheme } from '../../../../context/ThemeContext';

/**
 * Pivot Tablo Görüntüleme Bileşeni
 * Hesaplanmış pivot tabloyu görüntüler
 * Sıralama, filtreleme ve export özellikleri
 */
const PivotTableView = ({ 
  pivotData, 
  onExport, 
  onSort, 
  showSummary = true,
  maxHeight = 400 
}) => {
  const { theme } = useTheme();
  const [sortConfig, setSortConfig] = useState({ field: null, direction: 'asc' });
  const [selectedCells, setSelectedCells] = useState(new Set());

  // Pivot data kontrolü
  if (!pivotData || !pivotData.headers || !pivotData.rows) {
    return (
      <View style={[styles.emptyContainer, { backgroundColor: theme.SURFACE }]}>
        <Text style={[styles.emptyText, { color: theme.TEXT_SECONDARY }]}>
          📊 Pivot tablo verisi bulunamadı
        </Text>
        <Text style={[styles.emptySubtext, { color: theme.TEXT_SECONDARY }]}>
          Lütfen satır, sütun ve değer alanlarını yapılandırın
        </Text>
      </View>
    );
  }

  const { headers, rows, summary, metadata } = pivotData;

  // Sıralama işlemi
  const sortedRows = useMemo(() => {
    if (!sortConfig.field) return rows;

    return [...rows].sort((a, b) => {
      const aValue = a[sortConfig.field];
      const bValue = b[sortConfig.field];

      if (aValue === bValue) return 0;

      const comparison = aValue < bValue ? -1 : 1;
      return sortConfig.direction === 'asc' ? comparison : -comparison;
    });
  }, [rows, sortConfig]);

  // Sütun sıralama
  const handleSort = (field) => {
    const newDirection = 
      sortConfig.field === field && sortConfig.direction === 'asc' 
        ? 'desc' 
        : 'asc';
    
    const newSortConfig = { field, direction: newDirection };
    setSortConfig(newSortConfig);
    
    if (onSort) {
      onSort(newSortConfig);
    }
  };

  // Hücre seçimi
  const toggleCellSelection = (rowIndex, columnKey) => {
    const cellId = `${rowIndex}-${columnKey}`;
    const newSelection = new Set(selectedCells);
    
    if (newSelection.has(cellId)) {
      newSelection.delete(cellId);
    } else {
      newSelection.add(cellId);
    }
    
    setSelectedCells(newSelection);
  };

  // Seçimi temizle
  const clearSelection = () => {
    setSelectedCells(new Set());
  };

  // Export menüsü
  const showExportMenu = () => {
    Alert.alert(
      'Dışa Aktar',
      'Pivot tabloyu hangi formatta dışa aktarmak istiyorsunuz?',
      [
        { text: 'CSV', onPress: () => onExport && onExport('csv') },
        { text: 'Excel', onPress: () => onExport && onExport('excel') },
        { text: 'PDF', onPress: () => onExport && onExport('pdf') },
        { text: 'İptal', style: 'cancel' }
      ]
    );
  };

  // Değer formatla
  const formatValue = (value, header) => {
    if (value == null || value === '') return '-';
    
    if (header.type === 'value' && typeof value === 'number') {
      return value.toLocaleString('tr-TR', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
      });
    }
    
    return String(value);
  };

  // Hücre stilini al
  const getCellStyle = (rowIndex, columnKey, header) => {
    const cellId = `${rowIndex}-${columnKey}`;
    const isSelected = selectedCells.has(cellId);
    
    let backgroundColor = theme.SURFACE;
    
    if (isSelected) {
      backgroundColor = theme.PRIMARY + '20';
    } else if (header.type === 'row_header') {
      backgroundColor = theme.BACKGROUND;
    }
    
    return [
      styles.cell,
      {
        backgroundColor,
        borderColor: theme.BORDER || theme.TEXT_SECONDARY + '20'
      }
    ];
  };

  // Başlık stilini al
  const getHeaderStyle = (header) => {
    const isSorted = sortConfig.field === header.key;
    
    return [
      styles.headerCell,
      {
        backgroundColor: theme.PRIMARY + '10',
        borderColor: theme.BORDER || theme.TEXT_SECONDARY + '20'
      },
      isSorted && { backgroundColor: theme.PRIMARY + '20' }
    ];
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      {/* Header Controls */}
      <View style={[styles.controls, { backgroundColor: theme.SURFACE }]}>
        <View style={styles.controlsLeft}>
          <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
            📊 Pivot Tablo
          </Text>
          <Text style={[styles.subtitle, { color: theme.TEXT_SECONDARY }]}>
            {metadata.totalRows} satır × {metadata.totalColumns} sütun
          </Text>
        </View>
        
        <View style={styles.controlsRight}>
          {selectedCells.size > 0 && (
            <TouchableOpacity
              style={[styles.controlButton, { backgroundColor: theme.WARNING }]}
              onPress={clearSelection}
            >
              <Text style={[styles.controlButtonText, { color: theme.SURFACE }]}>
                Seçimi Temizle ({selectedCells.size})
              </Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity
            style={[styles.controlButton, { backgroundColor: theme.PRIMARY }]}
            onPress={showExportMenu}
          >
            <Text style={[styles.controlButtonText, { color: theme.SURFACE }]}>
              📤 Dışa Aktar
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Pivot Table */}
      <ScrollView 
        horizontal 
        style={[styles.tableContainer, { maxHeight }]}
        showsHorizontalScrollIndicator={true}
      >
        <ScrollView showsVerticalScrollIndicator={true}>
          <View style={styles.table}>
            {/* Headers */}
            <View style={styles.headerRow}>
              {headers.map((header, index) => (
                <TouchableOpacity
                  key={header.key}
                  style={getHeaderStyle(header)}
                  onPress={() => handleSort(header.key)}
                >
                  <Text style={[styles.headerText, { color: theme.TEXT_PRIMARY }]}>
                    {header.title}
                    {sortConfig.field === header.key && (
                      <Text style={styles.sortIndicator}>
                        {sortConfig.direction === 'asc' ? ' ↑' : ' ↓'}
                      </Text>
                    )}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            {/* Data Rows */}
            {sortedRows.map((row, rowIndex) => (
              <View key={rowIndex} style={styles.dataRow}>
                {headers.map((header) => (
                  <TouchableOpacity
                    key={`${rowIndex}-${header.key}`}
                    style={getCellStyle(rowIndex, header.key, header)}
                    onPress={() => toggleCellSelection(rowIndex, header.key)}
                  >
                    <Text style={[styles.cellText, { color: theme.TEXT_PRIMARY }]}>
                      {formatValue(row[header.key], header)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            ))}
          </View>
        </ScrollView>
      </ScrollView>

      {/* Summary */}
      {showSummary && summary && Object.keys(summary).length > 0 && (
        <View style={[styles.summary, { backgroundColor: theme.SURFACE }]}>
          <Text style={[styles.summaryTitle, { color: theme.TEXT_PRIMARY }]}>
            📋 Özet
          </Text>
          <View style={styles.summaryGrid}>
            {Object.entries(summary).map(([key, value]) => (
              <View key={key} style={[styles.summaryItem, { backgroundColor: theme.BACKGROUND }]}>
                <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>
                  {value.field} ({value.type})
                </Text>
                <Text style={[styles.summaryValue, { color: theme.TEXT_PRIMARY }]}>
                  {formatValue(value.value, { type: 'value' })}
                </Text>
              </View>
            ))}
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  controlsLeft: {
    flex: 1,
  },
  controlsRight: {
    flexDirection: 'row',
    gap: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  subtitle: {
    fontSize: 14,
    marginTop: 4,
  },
  controlButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  controlButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  tableContainer: {
    flex: 1,
    borderRadius: 12,
    overflow: 'hidden',
  },
  table: {
    minWidth: '100%',
  },
  headerRow: {
    flexDirection: 'row',
  },
  dataRow: {
    flexDirection: 'row',
  },
  headerCell: {
    minWidth: 120,
    padding: 12,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cell: {
    minWidth: 120,
    padding: 12,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerText: {
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  cellText: {
    fontSize: 14,
    textAlign: 'center',
  },
  sortIndicator: {
    fontSize: 12,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    borderRadius: 12,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
  summary: {
    padding: 16,
    borderRadius: 12,
    marginTop: 16,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  summaryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  summaryItem: {
    padding: 12,
    borderRadius: 8,
    minWidth: 120,
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 12,
    textAlign: 'center',
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

export default PivotTableView;
