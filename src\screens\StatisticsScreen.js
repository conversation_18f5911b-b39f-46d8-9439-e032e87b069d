import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { useAppContext } from '../context/AppContext';
import PieChart from '../components/PieChart';
import BarChart from '../components/BarChart';
import FilterBar from '../components/FilterBar';

/**
 * İstatistikler ekranı
 *
 * @returns {JSX.Element} İstatistikler ekranı
 */
function StatisticsScreen() {
  const insets = useSafeAreaInsets();
  const db = useSQLiteContext();
  const { theme } = useTheme();
  const { defaultCurrency } = useAppContext();
  
  const [filter, setFilter] = useState({
    type: 'expense', // 'income', 'expense'
    period: 'month', // 'week', 'month', 'year', 'all'
  });
  
  const [categoryData, setCategoryData] = useState([]);
  const [timeData, setTimeData] = useState([]);
  const [summary, setSummary] = useState({
    total: 0,
    average: 0,
    max: 0,
    min: 0,
  });

  // Verileri yükle
  const loadData = async () => {
    try {
      // Kategori bazında veri
      const categoryQuery = `
        SELECT 
          c.id, 
          c.name, 
          c.color, 
          SUM(t.amount) as total
        FROM transactions t
        JOIN categories c ON t.category_id = c.id
        WHERE t.type = ?
        ${getDateFilterQuery(filter.period)}
        GROUP BY c.id
        ORDER BY total DESC
      `;
      
      const categoryResult = await db.getAllAsync(categoryQuery, [filter.type]);
      
      // Kategori verilerini formatlama
      const formattedCategoryData = categoryResult.map(item => ({
        id: item.id,
        name: item.name,
        value: item.total,
        color: item.color || Colors.PRIMARY,
      }));
      
      setCategoryData(formattedCategoryData);
      
      // Zaman bazında veri
      const timeQuery = `
        SELECT 
          ${getTimeGrouping(filter.period)} as label,
          SUM(amount) as total
        FROM transactions
        WHERE type = ?
        ${getDateFilterQuery(filter.period)}
        GROUP BY label
        ORDER BY label
      `;
      
      const timeResult = await db.getAllAsync(timeQuery, [filter.type]);
      
      // Zaman verilerini formatlama
      const formattedTimeData = timeResult.map(item => ({
        label: item.label,
        value: item.total,
      }));
      
      setTimeData(formattedTimeData);
      
      // Özet bilgileri
      const total = formattedCategoryData.reduce((sum, item) => sum + item.value, 0);
      const values = formattedTimeData.map(item => item.value);
      const max = values.length > 0 ? Math.max(...values) : 0;
      const min = values.length > 0 ? Math.min(...values) : 0;
      const average = values.length > 0 ? total / values.length : 0;
      
      setSummary({
        total,
        average,
        max,
        min,
      });
    } catch (error) {
      console.error('Veri yükleme hatası:', error);
    }
  };

  // Tarih filtresi sorgusu
  const getDateFilterQuery = (period) => {
    if (period === 'all') return '';
    
    const now = new Date();
    let startDate;
    
    if (period === 'week') {
      startDate = new Date(now);
      startDate.setDate(now.getDate() - 7);
    } else if (period === 'month') {
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
    } else if (period === 'year') {
      startDate = new Date(now.getFullYear(), 0, 1);
    }
    
    if (startDate) {
      return ` AND date >= '${startDate.toISOString().split('T')[0]}'`;
    }
    
    return '';
  };

  // Zaman gruplandırma
  const getTimeGrouping = (period) => {
    if (period === 'week') {
      return "strftime('%Y-%m-%d', date)";
    } else if (period === 'month') {
      return "strftime('%Y-%m-%d', date)";
    } else if (period === 'year') {
      return "strftime('%Y-%m', date)";
    }
    return "strftime('%Y-%m', date)";
  };

  // İlk yükleme
  useEffect(() => {
    loadData();
  }, [filter]);

  return (
    <View style={[getStyles(theme).container, { paddingTop: insets.top }]}>
      <View style={getStyles(theme).header}>
        <Text style={getStyles(theme).title}>İstatistikler</Text>
      </View>
      
      <View style={getStyles(theme).filterContainer}>
        <TouchableOpacity
          style={[
            getStyles(theme).filterButton,
            filter.type === 'income' && getStyles(theme).activeFilterButton
          ]}
          onPress={() => setFilter({ ...filter, type: 'income' })}
        >
          <Text style={[
            getStyles(theme).filterButtonText,
            filter.type === 'income' && getStyles(theme).activeFilterButtonText
          ]}>Gelir</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            getStyles(theme).filterButton,
            filter.type === 'expense' && getStyles(theme).activeFilterButton
          ]}
          onPress={() => setFilter({ ...filter, type: 'expense' })}
        >
          <Text style={[
            getStyles(theme).filterButtonText,
            filter.type === 'expense' && getStyles(theme).activeFilterButtonText
          ]}>Gider</Text>
        </TouchableOpacity>
      </View>
      
      <View style={getStyles(theme).periodContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <TouchableOpacity
            style={[
              getStyles(theme).periodButton,
              filter.period === 'week' && getStyles(theme).activePeriodButton
            ]}
            onPress={() => setFilter({ ...filter, period: 'week' })}
          >
            <Text style={[
              getStyles(theme).periodButtonText,
              filter.period === 'week' && getStyles(theme).activePeriodButtonText
            ]}>Hafta</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              getStyles(theme).periodButton,
              filter.period === 'month' && getStyles(theme).activePeriodButton
            ]}
            onPress={() => setFilter({ ...filter, period: 'month' })}
          >
            <Text style={[
              getStyles(theme).periodButtonText,
              filter.period === 'month' && getStyles(theme).activePeriodButtonText
            ]}>Ay</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              getStyles(theme).periodButton,
              filter.period === 'year' && getStyles(theme).activePeriodButton
            ]}
            onPress={() => setFilter({ ...filter, period: 'year' })}
          >
            <Text style={[
              getStyles(theme).periodButtonText,
              filter.period === 'year' && getStyles(theme).activePeriodButtonText
            ]}>Yıl</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              getStyles(theme).periodButton,
              filter.period === 'all' && getStyles(theme).activePeriodButton
            ]}
            onPress={() => setFilter({ ...filter, period: 'all' })}
          >
            <Text style={[
              getStyles(theme).periodButtonText,
              filter.period === 'all' && getStyles(theme).activePeriodButtonText
            ]}>Tümü</Text>
          </TouchableOpacity>
        </ScrollView>
      </View>
      
      <ScrollView style={getStyles(theme).content}>
        <View style={getStyles(theme).summaryContainer}>
          <View style={getStyles(theme).summaryItem}>
            <Text style={getStyles(theme).summaryLabel}>Toplam</Text>
            <Text style={getStyles(theme).summaryValue}>₺{summary.total.toLocaleString('tr-TR')}</Text>
          </View>
          
          <View style={getStyles(theme).summaryItem}>
            <Text style={getStyles(theme).summaryLabel}>Ortalama</Text>
            <Text style={getStyles(theme).summaryValue}>₺{summary.average.toLocaleString('tr-TR', { maximumFractionDigits: 2 })}</Text>
          </View>
          
          <View style={getStyles(theme).summaryItem}>
            <Text style={getStyles(theme).summaryLabel}>En Yüksek</Text>
            <Text style={getStyles(theme).summaryValue}>₺{summary.max.toLocaleString('tr-TR')}</Text>
          </View>
          
          <View style={getStyles(theme).summaryItem}>
            <Text style={getStyles(theme).summaryLabel}>En Düşük</Text>
            <Text style={getStyles(theme).summaryValue}>₺{summary.min.toLocaleString('tr-TR')}</Text>
          </View>
        </View>
        
        <View style={getStyles(theme).chartContainer}>
          <Text style={getStyles(theme).chartTitle}>Kategori Dağılımı</Text>
          <PieChart data={categoryData} />
        </View>
        
        <View style={getStyles(theme).chartContainer}>
          <Text style={getStyles(theme).chartTitle}>Zaman Dağılımı</Text>
          <BarChart data={timeData} />
        </View>
      </ScrollView>
    </View>
  );
}

const getStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.BACKGROUND,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: theme.PRIMARY,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.ON_PRIMARY,
  },
  filterContainer: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: theme.SURFACE,
  },
  filterButton: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 8,
  },
  activeFilterButton: {
    backgroundColor: theme.PRIMARY,
  },
  filterButtonText: {
    fontSize: 16,
    color: theme.TEXT_PRIMARY,
  },
  activeFilterButtonText: {
    color: theme.ON_PRIMARY,
  },
  periodContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: theme.SURFACE,
    borderBottomWidth: 1,
    borderBottomColor: theme.BORDER,
  },
  periodButton: {
    paddingHorizontal: 16,
    paddingVertical: 6,
    marginRight: 8,
    borderRadius: 16,
    backgroundColor: theme.SURFACE_VARIANT,
  },
  activePeriodButton: {
    backgroundColor: theme.PRIMARY,
  },
  periodButtonText: {
    fontSize: 14,
    color: theme.TEXT_PRIMARY,
  },
  activePeriodButtonText: {
    color: theme.ON_PRIMARY,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  summaryContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    backgroundColor: theme.SURFACE,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: theme.SHADOW,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  summaryItem: {
    width: '48%',
    marginBottom: 16,
  },
  summaryLabel: {
    fontSize: 14,
    color: theme.TEXT_SECONDARY,
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.TEXT_PRIMARY,
  },
  chartContainer: {
    backgroundColor: theme.SURFACE,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: theme.SHADOW,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.TEXT_PRIMARY,
    marginBottom: 16,
  },
});

export default StatisticsScreen;
