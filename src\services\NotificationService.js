// Mock bildirim servisi kullanılıyor (Expo Go'da bildirim desteği sınırlı)
import * as Notifications from './mockNotificationService';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform, Alert } from 'react-native';

// Konsola uyarı mesajı
console.warn('MOCK BİLDİRİM SERVİSİ KULLANILIYOR: Expo Go\'da bildirim desteği sınırlıdır. Development build kullanmanız önerilir.');

const NOTIFICATION_SETTINGS_KEY = '@notification_settings';

/**
 * Bildirim izinlerini kontrol eder ve gerekirse ister
 *
 * @returns {Promise<boolean>} İzin durumu
 */
export const checkNotificationPermissions = async () => {
  try {
    // Expo Go uyarısı
    console.warn('Expo Go\'da push notification desteği sınırlıdır. Sadece yerel bildirimler kullanılabilir.');

    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;

    // İzin yoksa iste
    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }

    return finalStatus === 'granted';
  } catch (error) {
    console.error('Bildirim izinleri kontrol hatası:', error);
    return false;
  }
};

/**
 * Bildirim ayarlarını yapılandırır
 */
export const configureNotifications = () => {
  // Bildirim davranışını yapılandır
  Notifications.setNotificationHandler({
    handleNotification: async () => ({
      shouldShowAlert: true,
      shouldPlaySound: true,
      shouldSetBadge: true,
    }),
  });

  // Ön planda bildirim davranışını yapılandır
  if (Platform.OS === 'android') {
    Notifications.setNotificationChannelAsync('default', {
      name: 'default',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
    });
  }
};

/**
 * Bildirim gönderir
 *
 * @param {Object} notification - Bildirim verileri
 * @param {string} notification.title - Bildirim başlığı
 * @param {string} notification.body - Bildirim içeriği
 * @param {Object} notification.data - Bildirim verileri
 * @param {Date} notification.trigger - Bildirim tetikleyicisi (null ise hemen gönderilir)
 * @returns {Promise<string>} Bildirim ID'si
 */
export const sendNotification = async (notification) => {
  try {
    // İzinleri kontrol et
    const hasPermission = await checkNotificationPermissions();

    if (!hasPermission) {
      throw new Error('Bildirim izni yok');
    }

    // Bildirimi gönder
    return await Notifications.scheduleNotificationAsync({
      content: {
        title: notification.title,
        body: notification.body,
        data: notification.data || {},
        sound: true,
        vibrate: [0, 250, 250, 250],
        priority: Notifications.AndroidNotificationPriority.HIGH,
      },
      trigger: notification.trigger || null,
    });
  } catch (error) {
    console.error('Bildirim gönderme hatası:', error);
    throw error;
  }
};

/**
 * Bildirimi iptal eder
 *
 * @param {string} notificationId - Bildirim ID'si
 * @returns {Promise<void>}
 */
export const cancelNotification = async (notificationId) => {
  try {
    await Notifications.cancelScheduledNotificationAsync(notificationId);
  } catch (error) {
    console.error('Bildirim iptal hatası:', error);
    throw error;
  }
};

/**
 * Tüm bildirimleri iptal eder
 *
 * @returns {Promise<void>}
 */
export const cancelAllNotifications = async () => {
  try {
    await Notifications.cancelAllScheduledNotificationsAsync();
  } catch (error) {
    console.error('Tüm bildirimleri iptal hatası:', error);
    throw error;
  }
};

/**
 * Zamanlanmış bildirimleri getirir
 *
 * @returns {Promise<Array>} Zamanlanmış bildirimler
 */
export const getScheduledNotifications = async () => {
  try {
    return await Notifications.getAllScheduledNotificationsAsync();
  } catch (error) {
    console.error('Zamanlanmış bildirimleri getirme hatası:', error);
    throw error;
  }
};

/**
 * Bildirim dinleyicisi ekler
 *
 * @param {Function} onReceive - Bildirim alındığında çağrılacak fonksiyon
 * @param {Function} onResponse - Bildirime yanıt verildiğinde çağrılacak fonksiyon
 * @returns {Object} Dinleyici abonelikleri
 */
export const addNotificationListeners = (onReceive, onResponse) => {
  const receiveListener = Notifications.addNotificationReceivedListener(onReceive);
  const responseListener = Notifications.addNotificationResponseReceivedListener(onResponse);

  return {
    receiveListener,
    responseListener,
  };
};

/**
 * Bildirim dinleyicilerini kaldırır
 *
 * @param {Object} listeners - Dinleyici abonelikleri
 */
export const removeNotificationListeners = (listeners) => {
  if (listeners.receiveListener) {
    Notifications.removeNotificationSubscription(listeners.receiveListener);
  }

  if (listeners.responseListener) {
    Notifications.removeNotificationSubscription(listeners.responseListener);
  }
};

const NotificationService = {
  /**
   * Bildirim izinlerini kontrol eder ve gerekirse ister
   */
  requestPermissions: async () => {
    try {
      const { status: existingStatus } = await Notifications.getPermissionsAsync();

      if (existingStatus === 'granted') return true;

      const { status } = await Notifications.requestPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Bildirim izni hatası:', error);
      return false;
    }
  },

  /**
   * Bildirim ayarlarını günceller
   * @param {Object} params - Güncelleme parametreleri
   * @param {Object} db - Veritabanı bağlantısı
   */
  updateNotificationSettings: async ({ type = 'all', enabled, quietHoursEnabled, quietHoursStart, quietHoursEnd }, db) => {
    if (!db) throw new Error('Database connection is required');

    try {
      // Önce tablonun var olduğunu kontrol et
      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS notification_settings (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          type TEXT NOT NULL UNIQUE,
          enabled BOOLEAN DEFAULT 1,
          quiet_hours_enabled BOOLEAN DEFAULT 0,
          quiet_hours_start TEXT DEFAULT '23:00',
          quiet_hours_end TEXT DEFAULT '07:00',
          created_at TEXT DEFAULT CURRENT_TIMESTAMP,
          updated_at TEXT
        )
      `);

      // Ayarları güncelle veya ekle
      await db.runAsync(`
        INSERT INTO notification_settings
        (type, enabled, quiet_hours_enabled, quiet_hours_start, quiet_hours_end, updated_at)
        VALUES (?, ?, ?, ?, ?, datetime('now'))
        ON CONFLICT(type) DO UPDATE SET
          enabled = ?,
          quiet_hours_enabled = COALESCE(?, quiet_hours_enabled),
          quiet_hours_start = COALESCE(?, quiet_hours_start),
          quiet_hours_end = COALESCE(?, quiet_hours_end),
          updated_at = datetime('now')
      `, [
        type, enabled ? 1 : 0, quietHoursEnabled ? 1 : 0, quietHoursStart, quietHoursEnd,
        enabled ? 1 : 0, quietHoursEnabled ? 1 : 0, quietHoursStart, quietHoursEnd
      ]);

      // AsyncStorage'a da kaydet
      const currentSettings = await NotificationService.loadSettings();
      await NotificationService.saveSettings({
        ...currentSettings,
        [type]: { enabled, quietHoursEnabled, quietHoursStart, quietHoursEnd }
      });

      return true;
    } catch (error) {
      console.error('Bildirim ayarları güncellenirken hata:', error);
      throw error;
    }
  },

  /**
   * Bildirim göndermeden önce kontrolleri yapar
   */
  canSendNotification: async ({ type }, db) => {
    try {
      // Genel bildirim ayarını ve spesifik tip ayarını kontrol et
      const settings = await db.getFirstAsync(`
        SELECT
          all_settings.enabled as all_enabled,
          type_settings.enabled as type_enabled,
          type_settings.quiet_hours_enabled,
          type_settings.quiet_hours_start,
          type_settings.quiet_hours_end
        FROM notification_settings all_settings
        LEFT JOIN notification_settings type_settings ON type_settings.type = ?
        WHERE all_settings.type = 'all'
      `, [type]);

      if (!settings?.all_enabled || !settings?.type_enabled) return false;

      // Sessiz saatleri kontrol et
      if (settings.quiet_hours_enabled) {
        const currentTime = format(new Date(), 'HH:mm');
        if (currentTime >= settings.quiet_hours_start &&
            currentTime <= settings.quiet_hours_end) {
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error('Bildirim kontrolü hatası:', error);
      return false;
    }
  },

  /**
   * Bildirim ayarlarını yükler
   */
  loadSettings: async () => {
    try {
      const settings = await AsyncStorage.getItem(NOTIFICATION_SETTINGS_KEY);
      return settings ? JSON.parse(settings) : {
        enabled: true,
        all: {
          enabled: true,
          quietHoursEnabled: true,
          quietHoursStart: '23:00',
          quietHoursEnd: '07:00'
        }
      };
    } catch (error) {
      console.error('Bildirim ayarları yüklenirken hata:', error);
      return null;
    }
  },

  /**
   * Bildirim ayarlarını kaydeder
   */
  saveSettings: async (settings) => {
    try {
      await AsyncStorage.setItem(NOTIFICATION_SETTINGS_KEY, JSON.stringify(settings));
      return true;
    } catch (error) {
      console.error('Bildirim ayarları kaydedilirken hata:', error);
      return false;
    }
  },

  /**
   * Bildirim servisini başlatır ve ayarları yükler
   * @param {Object} db Veritabanı bağlantısı
   */
  initialize: async (db) => {
    if (!db) throw new Error('Database connection is required');

    try {
      // Expo Go uyarısı
      console.warn('Expo Go\'da push notification desteği sınırlıdır. Sadece yerel bildirimler kullanılabilir.');

      // Kayıtlı ayarları yükle
      const savedSettings = await NotificationService.loadSettings();

      // Eğer kayıtlı ayar varsa ve bildirimler açıksa
      if (savedSettings?.enabled) {
        // İzinleri kontrol et ve yapılandır
        const { status } = await Notifications.getPermissionsAsync();
        if (status !== 'granted') {
          const { status: newStatus } = await Notifications.requestPermissionsAsync();
          if (newStatus !== 'granted') return false;
        }

        // Handler'ı ayarla
        await Notifications.setNotificationHandler({
          handleNotification: async () => ({
            shouldShowAlert: true,
            shouldPlaySound: true,
            shouldSetBadge: true,
          }),
        });

        // Veritabanına kaydet
        await NotificationService.updateNotificationSettings({
          ...savedSettings.all,
          type: 'all'
        }, db);

        return true;
      }

      return false;
    } catch (error) {
      console.error('Bildirim servisi başlatılırken hata:', error);
      return false;
    }
  }
};

// Eski API'yi de export et
export default NotificationService;
