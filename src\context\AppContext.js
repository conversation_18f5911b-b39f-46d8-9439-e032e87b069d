import React, { createContext, useState, useContext, useEffect } from 'react';
import { View, Text, Appearance } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useSQLiteContext } from 'expo-sqlite';
import * as settingsService from '../services/settingsService';
import { Colors, lightTheme, darkTheme } from '../constants/colors';
import tokens from '../design-system/tokens';
import globalStyles from '../design-system/globalStyles';

// AppContext - Uygulama genelinde kullanılacak değerler için merkezi depo
const AppContext = createContext({
  theme: Colors,
  defaultCurrency: 'TRY',
  isDarkMode: false,
  toggleTheme: () => {},
  // New additions for HomeScreen redesign
  userProfile: {
    name: '',
    greeting: 'Merhaba',
    avatarUrl: null,
    joinDate: null,
  },
  homeWidgets: [],
  notifications: {
    unread: 0,
    settings: {
      enabled: true,
      budget: true,
      reminders: true,
    },
  },
  fabConfig: {
    position: 'right',
    primaryAction: 'ADD_TRANSACTION',
    secondaryActions: ['ADD_INCOME', 'ADD_EXPENSE'],
  },
  // New functions
  updateUserProfile: () => {},
  updateWidgetConfig: () => {},
  updateFABConfig: () => {},
  markNotificationsRead: () => {},
});

/**
 * Ana uygulama context sağlayıcısı
 * Tema ve para birimi gibi uygulama genelindeki ayarları yönetir
 *
 * @param {Object} props - Bileşen props'ları
 * @param {React.ReactNode} props.children - Alt bileşenler
 * @returns {JSX.Element} AppProvider bileşeni
 */
export const AppProvider = ({ children }) => {
  const db = useSQLiteContext();
  const [defaultCurrency, setDefaultCurrency] = useState('TRY');
  
  // Sistem temasını hemen al ve initial state olarak kullan
  const systemColorScheme = Appearance.getColorScheme();
  const [isDarkMode, setIsDarkMode] = useState(systemColorScheme === 'dark');
  const [themePreference, setThemePreference] = useState('system');

  // New state for HomeScreen redesign
  const [userProfile, setUserProfile] = useState({
    name: '',
    greeting: 'Merhaba',
    avatar: 'person',
    avatarUrl: null,
    joinDate: null,
  });

  const [homeWidgets, setHomeWidgets] = useState([]);
  
  const [notifications, setNotifications] = useState({
    unread: 3, // Test için birkaç bildirim
    settings: {
      enabled: true,
      budget: true,
      reminders: true,
    },
  });

  const [fabConfig, setFabConfig] = useState({
    position: 'right',
    primaryAction: 'ADD_TRANSACTION',
    secondaryActions: ['ADD_INCOME', 'ADD_EXPENSE'],
  });

  // Tema ayarlarını yükle
  useEffect(() => {
    const loadThemeSettings = async () => {
      try {
        const savedThemePreference = await AsyncStorage.getItem('themePreference');
        const currentThemePreference = savedThemePreference || 'system';
        
        setThemePreference(currentThemePreference);

        // Sistem temasını tekrar kontrol et
        const currentSystemColorScheme = Appearance.getColorScheme();
        const shouldUseDarkMode = currentThemePreference === 'dark' ||
          (currentThemePreference === 'system' && currentSystemColorScheme === 'dark');

        setIsDarkMode(shouldUseDarkMode);
      } catch (error) {
        console.error('Tema ayarları yüklenirken hata:', error);
      }
    };

    loadThemeSettings();
  }, []);

  // Sistem tema değişikliklerini dinle
  useEffect(() => {
    const subscription = Appearance.addChangeListener(({ colorScheme }) => {
      if (themePreference === 'system') {
        setIsDarkMode(colorScheme === 'dark');
      }
    });

    return () => subscription?.remove();
  }, [themePreference]);

  // Varsayılan para birimini yükle
  useEffect(() => {
    const loadDefaultCurrency = async () => {
      try {
        // Önce AsyncStorage'den kontrol et
        const storedCurrency = await AsyncStorage.getItem('defaultCurrency');

        if (storedCurrency) {
          setDefaultCurrency(storedCurrency);
          return;
        }

        // Veritabanından kontrol et (eğer settingsService mevcutsa)
        if (db && settingsService.getSetting) {
          const defaultCurrencySetting = await settingsService.getSetting(db, 'defaultCurrency');
          if (defaultCurrencySetting) {
            setDefaultCurrency(defaultCurrencySetting);

            // AsyncStorage'e de kaydet
            await AsyncStorage.setItem('defaultCurrency', defaultCurrencySetting);
          }
        }
      } catch (error) {
        console.error('Varsayılan para birimi yüklenirken hata:', error);
      }
    };

    loadDefaultCurrency();
  }, [db]);

  // Load user profile and widget settings
  useEffect(() => {
    const loadUserSettings = async () => {
      try {
        // Load user profile
        const savedProfile = await AsyncStorage.getItem('userProfile');
        if (savedProfile) {
          setUserProfile(JSON.parse(savedProfile));
        }

        // Load widget configuration
        const savedWidgets = await AsyncStorage.getItem('homeWidgets');
        if (savedWidgets) {
          setHomeWidgets(JSON.parse(savedWidgets));
        } else {
          // Set default widget configuration
          const defaultWidgets = [
            { id: 'balance', type: 'BALANCE_SUMMARY', enabled: false, order: 1 },
            { id: 'recent', type: 'RECENT_TRANSACTIONS', enabled: false, order: 2 },
            { id: 'custom', type: 'CUSTOMIZABLE', enabled: false, order: 3 },
          ];
          setHomeWidgets(defaultWidgets);
        }

        // Load notifications
        const savedNotifications = await AsyncStorage.getItem('notifications');
        if (savedNotifications) {
          setNotifications(JSON.parse(savedNotifications));
        }

        // Load FAB configuration
        const savedFabConfig = await AsyncStorage.getItem('fabConfig');
        if (savedFabConfig) {
          setFabConfig(JSON.parse(savedFabConfig));
        }
      } catch (error) {
        console.error('Kullanıcı ayarları yüklenirken hata:', error);
      }
    };

    loadUserSettings();
  }, []);

  // Tema değiştirme fonksiyonu
  const toggleTheme = async (newThemePreference) => {
    try {
      setThemePreference(newThemePreference);
      await AsyncStorage.setItem('themePreference', newThemePreference);

      if (newThemePreference === 'system') {
        const systemColorScheme = Appearance.getColorScheme();
        setIsDarkMode(systemColorScheme === 'dark');
      } else {
        setIsDarkMode(newThemePreference === 'dark');
      }
    } catch (error) {
      console.error('Tema değiştirilirken hata:', error);
    }
  };

  // New functions for managing user settings
  const updateUserProfile = async (newProfile) => {
    try {
      const updatedProfile = { ...userProfile, ...newProfile };
      setUserProfile(updatedProfile);
      await AsyncStorage.setItem('userProfile', JSON.stringify(updatedProfile));
    } catch (error) {
      console.error('Kullanıcı profili güncellenirken hata:', error);
    }
  };

  const updateWidgetConfig = async (newWidgets) => {
    try {
      setHomeWidgets(newWidgets);
      await AsyncStorage.setItem('homeWidgets', JSON.stringify(newWidgets));
    } catch (error) {
      console.error('Widget konfigürasyonu güncellenirken hata:', error);
    }
  };

  const updateFABConfig = async (newConfig) => {
    try {
      const updatedConfig = { ...fabConfig, ...newConfig };
      setFabConfig(updatedConfig);
      await AsyncStorage.setItem('fabConfig', JSON.stringify(updatedConfig));
    } catch (error) {
      console.error('FAB konfigürasyonu güncellenirken hata:', error);
    }
  };

  const markNotificationsRead = async () => {
    try {
      const updatedNotifications = { ...notifications, unread: 0 };
      setNotifications(updatedNotifications);
      await AsyncStorage.setItem('notifications', JSON.stringify(updatedNotifications));
    } catch (error) {
      console.error('Bildirimler güncellenirken hata:', error);
    }
  };

  // Mevcut tema renklerini al
  const currentTheme = isDarkMode ? darkTheme : lightTheme;

  // Context değeri
  const value = {
    theme: currentTheme,
    defaultCurrency,
    isDarkMode,
    themePreference,
    toggleTheme,
    tokens,
    globalStyles,
    userProfile,
    homeWidgets,
    notifications,
    fabConfig,
    updateUserProfile,
    updateWidgetConfig,
    updateFABConfig,
    markNotificationsRead,
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
};

/**
 * Uygulama context'ini kullanmak için hook
 * @returns {Object} AppContext değerleri
 */
export const useAppContext = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
};
