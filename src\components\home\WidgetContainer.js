import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Animated } from 'react-native';
import { useTheme } from '../../context/ThemeContext';
import { useAppContext } from '../../context/AppContext';
import DefaultDashboardWidget from './DefaultDashboardWidget';
import BalanceSummaryWidget from './BalanceSummaryWidget';
import RecentTransactionsWidget from './RecentTransactionsWidget';
import CustomizableWidget from './CustomizableWidget';

/**
 * Widget container that manages all home screen widgets
 * 
 * @param {Object} props - Component props
 * @param {Array} props.widgets - Widget configuration array
 * @param {Object} props.widgetData - Data for widgets
 * @param {Function} props.onWidgetPress - Widget press handler
 * @param {Function} props.onWidgetReorder - Widget reorder handler
 * @param {Function} props.onWidgetCustomize - Widget customize handler
 * @param {Function} props.onDefaultWidgetAction - Default widget action handler
 * @returns {JSX.Element} Widget container
 */
const WidgetContainer = ({
  widgets = [],
  widgetData = {},
  onWidgetPress,
  onWidgetReorder,
  onWidgetCustomize,
  onDefaultWidgetAction,
}) => {
  const { theme } = useTheme();
  const { userProfile } = useAppContext();
  const [animatedValue] = useState(new Animated.Value(0));

  // Check if any widgets are enabled
  const hasEnabledWidgets = widgets.some(widget => widget.enabled);

  useEffect(() => {
    Animated.timing(animatedValue, {
      toValue: 1,
      duration: 400,
      useNativeDriver: true,
    }).start();
  }, []);

  /**
   * Renders a widget based on its type
   * @param {Object} widget - Widget configuration
   * @param {number} index - Widget index
   * @returns {JSX.Element} Widget component
   */
  const renderWidget = (widget, index) => {
    const commonAnimatedStyle = {
      opacity: animatedValue,
      transform: [
        {
          translateY: animatedValue.interpolate({
            inputRange: [0, 1],
            outputRange: [30, 0],
          }),
        },
      ],
    };

    const commonStyles = { marginBottom: 16 };

    switch (widget.type) {
      case 'BALANCE_SUMMARY':
        return (
          <Animated.View
            key={`widget-${widget.id}-${index}`}
            style={[commonAnimatedStyle, commonStyles]}
          >
            <BalanceSummaryWidget
              balanceSummary={widgetData.balanceSummary || { income: 0, expense: 0, balance: 0 }}
              onPress={() => onWidgetPress?.(widget)}
              onIncomePress={() => onWidgetPress?.(widget, 'income')}
              onExpensePress={() => onWidgetPress?.(widget, 'expense')}
              onBalancePress={() => onWidgetPress?.(widget, 'balance')}
            />
          </Animated.View>
        );

      case 'RECENT_TRANSACTIONS':
        return (
          <Animated.View
            key={`widget-${widget.id}-${index}`}
            style={[commonAnimatedStyle, commonStyles]}
          >
            <RecentTransactionsWidget
              recentTransactions={widgetData.recentTransactions || []}
              onPress={() => onWidgetPress?.(widget)}
              onTransactionPress={(transaction) => onWidgetPress?.(widget, 'transaction', transaction)}
              onViewAllPress={() => onWidgetPress?.(widget, 'viewAll')}
            />
          </Animated.View>
        );

      case 'CUSTOMIZABLE':
        return (
          <Animated.View
            key={`widget-${widget.id}-${index}`}
            style={[commonAnimatedStyle, commonStyles]}
          >
            <CustomizableWidget
              quickStats={widgetData.quickStats || { thisMonth: 0, lastWeek: 0, trend: 'neutral' }}
              balanceData={widgetData.balanceSummary || { income: 0, expense: 0, balance: 0 }}
              onPress={() => onWidgetPress?.(widget)}
              onQuickActionPress={(action) => onWidgetPress?.(widget, 'quickAction', action)}
              onCustomizePress={() => onWidgetCustomize?.(widget)}
            />
          </Animated.View>
        );

      default:
        return null;
    }
  };

  /**
   * Renders default dashboard widget
   * @returns {JSX.Element} Default dashboard widget
   */
  const renderDefaultWidget = () => {
    return (
      <DefaultDashboardWidget
        balanceData={widgetData.balanceSummary || { income: 0, expense: 0, balance: 0 }}
        lastTransaction={widgetData.lastTransaction || null}
        quickStats={widgetData.quickStats || { thisMonth: 0, lastWeek: 0, trend: 'neutral' }}
        onCustomizePress={() => onDefaultWidgetAction?.('CUSTOMIZE')}
        onAddIncomePress={() => onDefaultWidgetAction?.('ADD_INCOME')}
        onAddExpensePress={() => onDefaultWidgetAction?.('ADD_EXPENSE')}
        onViewStatsPress={() => onDefaultWidgetAction?.('VIEW_STATS')}
      />
    );
  };

  // Sort widgets by order
  const sortedWidgets = [...widgets].sort((a, b) => (a.order || 0) - (b.order || 0));

  return (
    <View style={styles.container}>
      {/* Always show default widget if no widgets are enabled */}
      {!hasEnabledWidgets && renderDefaultWidget()}

      {/* Render enabled widgets */}
      {hasEnabledWidgets && (
        <View style={styles.widgetsContainer}>
          {sortedWidgets
            .filter(widget => widget.enabled)
            .map((widget, index) => renderWidget(widget, index))}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  widgetsContainer: {
    gap: 0, // Gap is handled by marginBottom in renderWidget
  },
  widgetWrapper: {
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
  },
});

export default WidgetContainer;
