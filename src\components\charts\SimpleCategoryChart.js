import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Dimensions } from 'react-native';
import { useTheme } from '../../context/ThemeContext';

const { width: screenWidth } = Dimensions.get('window');

/**
 * <PERSON><PERSON><PERSON> kate<PERSON>lım chart'ı - <PERSON><PERSON>ullan<PERSON>n, sadece View'lar ile
 */
const SimpleCategoryChart = ({ 
  data = [], 
  title = '<PERSON><PERSON><PERSON>ı<PERSON>',
  type = 'donut',
  onCategoryPress
}) => {
  const { theme } = useTheme();

  // Renk paleti (tema uyumlu)
  const getChartColors = () => [
    theme.PRIMARY,
    theme.SUCCESS,
    theme.WARNING,
    theme.ERROR,
    theme.INFO,
    '#9C27B0', // Purple
    '#FF9800', // Orange
    '#607D8B', // Blue Grey
  ];

  /**
   * Para formatı
   */
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  /**
   * Yüzde hesaplama
   */
  const calculatePercentage = (amount, total) => {
    return total > 0 ? ((amount / total) * 100).toFixed(1) : 0;
  };

  // Toplam tutarı hesapla
  const totalAmount = data.reduce((sum, item) => sum + (item.total || 0), 0);
  const colors = getChartColors();

  if (data.length === 0) {
    return (
      <View style={[styles.container, { backgroundColor: theme.SURFACE }]}>
        <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
          📊 {title}
        </Text>
        <View style={styles.emptyContainer}>
          <Text style={[styles.emptyIcon]}>📈</Text>
          <Text style={[styles.emptyText, { color: theme.TEXT_SECONDARY }]}>
            Henüz kategori verisi bulunmuyor
          </Text>
        </View>
      </View>
    );
  }

  if (type === 'bar') {
    // Basit çubuk grafik
    const maxValue = Math.max(...data.map(item => item.total || 0));

    return (
      <View style={[styles.container, { backgroundColor: theme.SURFACE }]}>
        <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
          📊 {title}
        </Text>
        
        <View style={styles.barChartContainer}>
          {data.slice(0, 5).map((item, index) => {
            const percentage = (item.total / maxValue) * 100;
            const color = colors[index % colors.length];
            
            return (
              <TouchableOpacity
                key={index}
                style={styles.barItem}
                onPress={() => onCategoryPress?.(item)}
              >
                <Text style={[styles.barLabel, { color: theme.TEXT_PRIMARY }]}>
                  {item.name}
                </Text>
                <View style={[styles.barTrack, { backgroundColor: theme.BORDER }]}>
                  <View 
                    style={[
                      styles.barFill, 
                      { 
                        backgroundColor: color,
                        width: `${percentage}%`
                      }
                    ]} 
                  />
                </View>
                <Text style={[styles.barValue, { color: theme.TEXT_SECONDARY }]}>
                  {formatCurrency(item.total)}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    );
  }

  // Basit donut/liste görünümü (default)
  return (
    <View style={[styles.container, { backgroundColor: theme.SURFACE }]}>
      <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
        📊 {title}
      </Text>
      
      {/* Merkez özet */}
      <View style={styles.centerSummary}>
        <Text style={[styles.totalAmount, { color: theme.TEXT_PRIMARY }]}>
          {formatCurrency(totalAmount)}
        </Text>
        <Text style={[styles.totalLabel, { color: theme.TEXT_SECONDARY }]}>
          Toplam Harcama
        </Text>
      </View>

      {/* Kategori listesi */}
      <View style={styles.categoryList}>
        {data.slice(0, 6).map((item, index) => {
          const percentage = calculatePercentage(item.total, totalAmount);
          const color = colors[index % colors.length];
          
          return (
            <TouchableOpacity
              key={index}
              style={styles.categoryItem}
              onPress={() => onCategoryPress?.(item)}
            >
              {/* Renk göstergesi */}
              <View style={[styles.colorIndicator, { backgroundColor: color }]} />
              
              {/* Kategori bilgileri */}
              <View style={styles.categoryInfo}>
                <Text style={[styles.categoryName, { color: theme.TEXT_PRIMARY }]}>
                  {item.name}
                </Text>
                <Text style={[styles.categoryAmount, { color: theme.TEXT_SECONDARY }]}>
                  {formatCurrency(item.total)} • %{percentage}
                </Text>
              </View>

              {/* Progress bar */}
              <View style={styles.progressContainer}>
                <View style={[styles.progressTrack, { backgroundColor: theme.BORDER }]}>
                  <View 
                    style={[
                      styles.progressFill, 
                      { 
                        backgroundColor: color,
                        width: `${percentage}%`
                      }
                    ]} 
                  />
                </View>
              </View>
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    padding: 16,
    marginVertical: 8,
    marginHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    textAlign: 'center',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    textAlign: 'center',
  },
  centerSummary: {
    alignItems: 'center',
    marginBottom: 20,
    paddingVertical: 16,
  },
  totalAmount: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 4,
  },
  totalLabel: {
    fontSize: 12,
    opacity: 0.7,
  },
  categoryList: {
    gap: 12,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  colorIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 2,
  },
  categoryAmount: {
    fontSize: 12,
  },
  progressContainer: {
    width: 60,
    marginLeft: 12,
  },
  progressTrack: {
    height: 4,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  // Bar chart styles
  barChartContainer: {
    gap: 16,
    paddingTop: 8,
  },
  barItem: {
    gap: 8,
  },
  barLabel: {
    fontSize: 12,
    fontWeight: '500',
  },
  barTrack: {
    height: 24,
    borderRadius: 12,
    overflow: 'hidden',
    position: 'relative',
  },
  barFill: {
    height: '100%',
    borderRadius: 12,
    minWidth: 4,
  },
  barValue: {
    fontSize: 10,
    textAlign: 'right',
  },
});

export default SimpleCategoryChart;
