import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../../constants/colors';

/**
 * Düzenli gelir durumunu gösteren rozet bileşeni
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {string} props.status - <PERSON><PERSON><PERSON> durum<PERSON> ('active', 'paused', 'ended')
 * @param {Function} props.toggleStatus - Durum değiştirme fonksiyonu
 * @param {Object} props.statusInfo - Durum bilgisi nesnesi
 * @returns {JSX.Element} Durum rozet bileşeni
 */
const RegularIncomeStatusBadge = React.memo(({ status, toggleStatus, statusInfo }) => {
  // Eğer statusInfo prop olarak gelmemişse, hesapla
  const info = statusInfo || (() => {
    switch (status) {
      case 'active':
        return {
          color: Colors.SUCCESS,
          icon: 'check-circle',
          text: 'Aktif',
          description: '<PERSON><PERSON><PERSON> aktif olarak takip ediliyor ve bildirimler etkin.'
        };
      case 'paused':
        return {
          color: Colors.WARNING,
          icon: 'pause-circle-filled',
          text: 'Duraklatıldı',
          description: 'Gelir geçici olarak durduruldu. Bildirimler gönderilmeyecek.'
        };
      case 'ended':
        return {
          color: Colors.GRAY_500,
          icon: 'cancel',
          text: 'Sonlandırıldı',
          description: 'Gelir artık takip edilmiyor. Tekrar aktifleştirilebilir.'
        };
      default:
        return {
          color: Colors.PRIMARY,
          icon: 'help',
          text: 'Bilinmiyor',
          description: 'Durum bilgisi belirtilmemiş.'
        };
    }
  })();

  return (
    <View style={styles.statusSection}>
      <View style={styles.statusHeader}>
        <Text style={styles.sectionTitle}>Durum</Text>
        <TouchableOpacity
          onPress={toggleStatus}
          style={styles.statusBadgeContainer}
          accessibilityLabel={`Gelir durumu: ${info.text}. Dokunarak değiştirebilirsiniz.`}
          accessibilityRole="button"
        >
          <View 
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingVertical: 8,
              paddingHorizontal: 12,
              borderRadius: 16,
              backgroundColor: info.color
            }}
          >
            <MaterialIcons name={info.icon} size={18} color="#fff" />
            <Text style={styles.statusText}>{info.text}</Text>
          </View>
        </TouchableOpacity>
      </View>
      <Text style={styles.statusHelp}>
        <MaterialIcons name="info-outline" size={14} color={Colors.GRAY_600} /> Durum değiştirmek için rozete dokunun.{"\n"}
        <Text style={{ fontWeight: 'bold', color: Colors.PRIMARY }}>Aktif → Duraklatıldı → Sona Erdi → Aktif</Text>
      </Text>
    </View>
  );
});

/**
 * Enhanced modern styles for RegularIncomeStatusBadge component
 * Following Material Design 3.0 principles with improved visual hierarchy
 */
const styles = StyleSheet.create({
  statusSection: {
    backgroundColor: Colors.WHITE,
    borderRadius: 18,
    padding: 20,
    marginBottom: 20,
    borderWidth: 2,
    borderColor: Colors.GRAY_100,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 6,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '800',
    color: Colors.GRAY_900,
    letterSpacing: 0.3,
  },
  statusBadgeContainer: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 6,
    elevation: 4,
    borderRadius: 20,
  },
  statusText: {
    color: Colors.WHITE,
    fontSize: 15,
    fontWeight: '700',
    marginLeft: 8,
    letterSpacing: 0.2,
  },
  statusHelp: {
    fontSize: 14,
    color: Colors.GRAY_600,
    lineHeight: 22,
    fontWeight: '500',
    backgroundColor: Colors.INFO + '10',
    padding: 14,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: Colors.INFO,
  },
});

export default RegularIncomeStatusBadge;
