import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { formatDate } from '../utils/shiftUtils';
import { shiftStyles } from '../styles/shiftStyles';

/**
 * Vardiya Planlaması Öğesi Bileşeni
 *
 * Bu bileşen, vardiya planlamaları listesinde her bir planlamayı temsil eder.
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.schedule - Vardiya planlaması verisi
 * @param {Function} props.onPress - Planlamaya tıklandığında çalışacak fonksiyon
 * @param {Function} props.onDelete - Silme butonuna tıklandığında çalışacak fonksiyon
 * @returns {JSX.Element} Vardiya planlaması öğesi
 */
const ShiftScheduleItem = ({ schedule, onPress, onDelete }) => {
  // Haftanın günlerini Türkçe olarak hazırla
  const daysOfWeek = {
    1: 'Pazartesi',
    2: 'Salı',
    3: 'Çarşamba',
    4: 'Perşembe',
    5: 'Cuma',
    6: 'Cumartesi',
    7: 'Pazar'
  };

  // Planlama günlerini formatlı olarak göster
  const formatDaysOfWeek = (daysString) => {
    if (!daysString) return '';

    const days = daysString.split(',').map(day => parseInt(day.trim()));
    return days.map(day => daysOfWeek[day]).join(', ');
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        { borderLeftColor: schedule.shift_type_color || '#9e9e9e' }
      ]}
      onPress={() => onPress(schedule)}
    >
      {/* Planlama Başlığı */}
      <View style={styles.header}>
        <Text style={styles.title}>{schedule.name}</Text>

        {/* Aktif/Pasif Durumu */}
        <View style={[
          styles.statusBadge,
          { backgroundColor: schedule.is_active ? '#4caf50' : '#9e9e9e' }
        ]}>
          <Text style={styles.statusText}>
            {schedule.is_active ? 'Aktif' : 'Pasif'}
          </Text>
        </View>
      </View>

      {/* Vardiya Türü */}
      <View style={styles.typeContainer}>
        <View style={[styles.colorDot, { backgroundColor: schedule.shift_type_color || '#9e9e9e' }]} />
        <Text style={styles.typeText}>{schedule.shift_type_name || 'Belirsiz Vardiya'}</Text>
      </View>

      {/* Planlama Detayları */}
      <View style={styles.details}>
        {/* Çalışma Günleri */}
        <View style={styles.infoItem}>
          <MaterialIcons name="event" size={16} color="#666" />
          <Text style={styles.infoText}>
            {formatDaysOfWeek(schedule.days_of_week)}
          </Text>
        </View>

        {/* Tarih Aralığı */}
        <View style={styles.infoItem}>
          <MaterialIcons name="date-range" size={16} color="#666" />
          <Text style={styles.infoText}>
            {formatDate(schedule.start_date)}
            {schedule.end_date ? ` - ${formatDate(schedule.end_date)}` : ' - Süresiz'}
          </Text>
        </View>
      </View>

      {/* Silme Butonu */}
      {onDelete && (
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => onDelete(schedule.id)}
          hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
        >
          <MaterialIcons name="delete-outline" size={20} color="#ff5252" />
        </TouchableOpacity>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    ...shiftStyles.listItem,
    borderLeftWidth: 4,
    paddingLeft: 12,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 30, // Silme butonuna yer açmak için sağ margin ekle
  },
  statusText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#fff',
  },
  typeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  colorDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  typeText: {
    fontSize: 14,
    color: '#333',
  },
  details: {
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    paddingTop: 12,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 6,
    flexShrink: 1,
  },
  deleteButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    padding: 4,
  },
});

export default ShiftScheduleItem;
