/**
 * <PERSON><PERSON><PERSON>çe Türü Seçici Komponenti
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md - Stage 2, Phase 1
 * 
 * Bütçe türü seçimi (Toplam/Kategori Bazlı/Esnek)
 * Maksimum 200 satır - Tek sorumluluk prensibi
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../../../context/ThemeContext';

/**
 * Bütçe türü seçici komponenti
 * @param {Object} props - Component props
 * @param {string} props.selectedType - Seçili bütçe türü ('total', 'category_based', 'flexible')
 * @param {Function} props.onTypeSelect - Tür seçim callback fonksiyonu
 * @param {Object} props.theme - <PERSON><PERSON> obje<PERSON> (opsiyonel, context'ten alınır)
 */
const BudgetTypeSelector = ({ 
  selectedType, 
  onTypeSelect, 
  theme: propTheme 
}) => {
  const { theme } = useTheme();
  const currentTheme = propTheme || theme;

  // Bütçe türleri tanımları
  const budgetTypes = [
    {
      id: 'total',
      title: 'Toplam Bütçe',
      description: 'Genel harcama limiti belirle',
      icon: 'account-balance-wallet',
      features: [
        'Tek toplam limit',
        'Basit takip',
        'Hızlı kurulum'
      ],
      recommended: false
    },
    {
      id: 'category_based',
      title: 'Kategori Bazlı Bütçe',
      description: 'Her kategori için ayrı limit',
      icon: 'category',
      features: [
        'Kategori bazlı limitler',
        'Detaylı takip',
        'Daha iyi kontrol'
      ],
      recommended: true
    },
    {
      id: 'flexible',
      title: 'Esnek Bütçe',
      description: 'Kategoriler arası transfer',
      icon: 'swap-horiz',
      features: [
        'Esnek limit yönetimi',
        'Kategori transferi',
        'Adaptif planlama'
      ],
      recommended: false
    }
  ];

  /**
   * Bütçe türü seçim işleyicisi
   * @param {string} typeId - Seçilen bütçe türü ID'si
   */
  const handleTypeSelect = (typeId) => {
    if (onTypeSelect) {
      onTypeSelect(typeId);
    }
  };

  /**
   * Seçili durumu kontrol eder
   * @param {string} typeId - Kontrol edilecek tür ID'si
   * @returns {boolean} Seçili olup olmadığı
   */
  const isSelected = (typeId) => selectedType === typeId;

  return (
    <View style={[styles.container, { backgroundColor: currentTheme.BACKGROUND }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: currentTheme.TEXT_PRIMARY }]}>
          Bütçe Türünü Seçin
        </Text>
        <Text style={[styles.subtitle, { color: currentTheme.TEXT_SECONDARY }]}>
          İhtiyaçlarınıza en uygun bütçe türünü belirleyin
        </Text>
      </View>

      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {budgetTypes.map((type) => (
          <TouchableOpacity
            key={type.id}
            style={[
              styles.typeCard,
              {
                backgroundColor: currentTheme.SURFACE,
                borderColor: isSelected(type.id) 
                  ? currentTheme.PRIMARY 
                  : currentTheme.BORDER,
                borderWidth: isSelected(type.id) ? 2 : 1,
              }
            ]}
            onPress={() => handleTypeSelect(type.id)}
            activeOpacity={0.7}
          >
            {/* Önerilen rozeti */}
            {type.recommended && (
              <View style={[styles.recommendedBadge, { backgroundColor: currentTheme.SUCCESS }]}>
                <Text style={[styles.recommendedText, { color: currentTheme.WHITE }]}>
                  Önerilen
                </Text>
              </View>
            )}

            {/* Seçim göstergesi */}
            <View style={styles.cardHeader}>
              <View style={[styles.iconContainer, { backgroundColor: currentTheme.PRIMARY + '20' }]}>
                <MaterialIcons 
                  name={type.icon} 
                  size={32} 
                  color={currentTheme.PRIMARY} 
                />
              </View>
              
              <View style={styles.selectionIndicator}>
                <MaterialIcons
                  name={isSelected(type.id) ? 'radio-button-checked' : 'radio-button-unchecked'}
                  size={24}
                  color={isSelected(type.id) ? currentTheme.PRIMARY : currentTheme.TEXT_SECONDARY}
                />
              </View>
            </View>

            {/* Başlık ve açıklama */}
            <View style={styles.cardContent}>
              <Text style={[styles.typeTitle, { color: currentTheme.TEXT_PRIMARY }]}>
                {type.title}
              </Text>
              <Text style={[styles.typeDescription, { color: currentTheme.TEXT_SECONDARY }]}>
                {type.description}
              </Text>

              {/* Özellikler listesi */}
              <View style={styles.featuresList}>
                {type.features.map((feature, index) => (
                  <View key={index} style={styles.featureItem}>
                    <MaterialIcons 
                      name="check-circle" 
                      size={16} 
                      color={currentTheme.SUCCESS} 
                    />
                    <Text style={[styles.featureText, { color: currentTheme.TEXT_SECONDARY }]}>
                      {feature}
                    </Text>
                  </View>
                ))}
              </View>
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
  },
  header: {
    paddingVertical: 16,
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 16,
  },
  typeCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    position: 'relative',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  recommendedBadge: {
    position: 'absolute',
    top: -8,
    right: 16,
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    zIndex: 1,
  },
  recommendedText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  iconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectionIndicator: {
    padding: 4,
  },
  cardContent: {
    flex: 1,
  },
  typeTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  typeDescription: {
    fontSize: 14,
    marginBottom: 12,
    lineHeight: 20,
  },
  featuresList: {
    gap: 6,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  featureText: {
    fontSize: 13,
    flex: 1,
  },
});

export default BudgetTypeSelector;
