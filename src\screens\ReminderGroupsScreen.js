import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  Modal,
  TextInput,
  ActivityIndicator,
  RefreshControl
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import * as reminderGroupService from '../services/reminderGroupService';

/**
 * Hatırlatıcı Grupları Ekranı
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @returns {JSX.Element} Hatırlatıcı Grupları Ekranı
 */
export default function ReminderGroupsScreen({ navigation }) {
  const db = useSQLiteContext();

  // Durum
  const [groups, setGroups] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Modal durumu
  const [modalVisible, setModalVisible] = useState(false);
  const [editingGroup, setEditingGroup] = useState(null);
  const [groupName, setGroupName] = useState('');
  const [groupDescription, setGroupDescription] = useState('');
  const [groupColor, setGroupColor] = useState('#3498db');
  const [groupIcon, setGroupIcon] = useState('folder');
  const [saving, setSaving] = useState(false);

  // Verileri yükle
  const loadData = useCallback(async () => {
    try {
      setLoading(true);

      // Tüm grupları getir
      const reminderGroups = await reminderGroupService.getAllReminderGroups(db, false);
      setGroups(reminderGroups);

      setLoading(false);
    } catch (error) {
      console.error('Hatırlatıcı grupları yükleme hatası:', error);
      Alert.alert('Hata', 'Hatırlatıcı grupları yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  }, [db]);

  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [loadData])
  );

  // Yenileme işlemi
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  // Grup detayına git
  const goToGroupDetail = (groupId) => {
    navigation.navigate('RemindersByGroup', { groupId });
  };

  // Yeni grup ekle
  const showAddGroupModal = () => {
    setEditingGroup(null);
    setGroupName('');
    setGroupDescription('');
    setGroupColor('#3498db');
    setGroupIcon('folder');
    setModalVisible(true);
  };

  // Grup düzenle
  const showEditGroupModal = (group) => {
    setEditingGroup(group);
    setGroupName(group.name);
    setGroupDescription(group.description || '');
    setGroupColor(group.color || '#3498db');
    setGroupIcon(group.icon || 'folder');
    setModalVisible(true);
  };

  // Grup kaydet
  const saveGroup = async () => {
    if (!groupName.trim()) {
      Alert.alert('Hata', 'Lütfen bir grup adı girin.');
      return;
    }

    try {
      setSaving(true);

      if (editingGroup) {
        // Grubu güncelle
        await reminderGroupService.updateReminderGroup(db, editingGroup.id, {
          name: groupName.trim(),
          description: groupDescription.trim(),
          color: groupColor,
          icon: groupIcon
        });

        Alert.alert('Başarılı', 'Grup güncellendi.');
      } else {
        // Yeni grup ekle
        await reminderGroupService.addReminderGroup(db, {
          name: groupName.trim(),
          description: groupDescription.trim(),
          color: groupColor,
          icon: groupIcon
        });

        Alert.alert('Başarılı', 'Yeni grup eklendi.');
      }

      // Modalı kapat ve verileri yenile
      setModalVisible(false);
      setSaving(false);
      loadData();
    } catch (error) {
      console.error('Grup kaydetme hatası:', error);
      Alert.alert('Hata', 'Grup kaydedilirken bir hata oluştu.');
      setSaving(false);
    }
  };

  // Grup sil
  const deleteGroup = (group) => {
    // Varsayılan grup silinemez
    if (group.is_default === 1) {
      Alert.alert('Hata', 'Varsayılan grup silinemez.');
      return;
    }

    Alert.alert(
      'Grubu Sil',
      `"${group.name}" grubunu silmek istediğinizden emin misiniz? Bu gruptaki tüm hatırlatıcılar varsayılan gruba taşınacaktır.`,
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await reminderGroupService.deleteReminderGroup(db, group.id);
              Alert.alert('Başarılı', 'Grup silindi.');
              loadData();
            } catch (error) {
              console.error('Grup silme hatası:', error);
              Alert.alert('Hata', 'Grup silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };

  // Grup etkinleştir/devre dışı bırak
  const toggleGroupActive = async (group) => {
    // Varsayılan grup devre dışı bırakılamaz
    if (group.is_default === 1 && group.is_active === 1) {
      Alert.alert('Hata', 'Varsayılan grup devre dışı bırakılamaz.');
      return;
    }

    try {
      await reminderGroupService.updateReminderGroup(db, group.id, {
        is_active: group.is_active === 1 ? 0 : 1
      });

      // Verileri yenile
      loadData();
    } catch (error) {
      console.error('Grup durumu değiştirme hatası:', error);
      Alert.alert('Hata', 'Grup durumu değiştirilirken bir hata oluştu.');
    }
  };

  // Grup öğesi
  const renderGroupItem = ({ item }) => {
    return (
      <TouchableOpacity
        style={[
          styles.groupItem,
          { borderLeftColor: item.color || Colors.PRIMARY }
        ]}
        onPress={() => goToGroupDetail(item.id)}
      >
        <View style={styles.groupHeader}>
          <View style={styles.groupTitleContainer}>
            <MaterialIcons
              name={item.icon || 'folder'}
              size={24}
              color={item.color || Colors.PRIMARY}
            />
            <Text style={[
              styles.groupTitle,
              item.is_active === 0 && styles.inactiveText
            ]}>
              {item.name}
              {item.is_default === 1 && (
                <Text style={styles.defaultBadge}> (Varsayılan)</Text>
              )}
            </Text>
          </View>

          <View style={styles.groupActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => toggleGroupActive(item)}
            >
              <MaterialIcons
                name={item.is_active === 1 ? 'visibility' : 'visibility-off'}
                size={20}
                color={item.is_active === 1 ? Colors.PRIMARY : Colors.GRAY_500}
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => showEditGroupModal(item)}
            >
              <MaterialIcons name="edit" size={20} color={Colors.PRIMARY} />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => deleteGroup(item)}
              disabled={item.is_default === 1}
            >
              <MaterialIcons
                name="delete"
                size={20}
                color={item.is_default === 1 ? Colors.GRAY_300 : Colors.DANGER}
              />
            </TouchableOpacity>
          </View>
        </View>

        {item.description && (
          <Text style={[
            styles.groupDescription,
            item.is_active === 0 && styles.inactiveText
          ]}>
            {item.description}
          </Text>
        )}

        <View style={styles.groupFooter}>
          <View style={styles.groupInfoItem}>
            <MaterialIcons name="access-time" size={16} color={Colors.TEXT_LIGHT} />
            <Text style={styles.groupInfoText}>
              {new Date(item.created_at).toLocaleDateString()}
            </Text>
          </View>

          <View style={styles.groupInfoItem}>
            <MaterialIcons
              name={item.is_active === 1 ? 'check-circle' : 'cancel'}
              size={16}
              color={item.is_active === 1 ? Colors.SUCCESS : Colors.GRAY_500}
            />
            <Text style={styles.groupInfoText}>
              {item.is_active === 1 ? 'Etkin' : 'Devre Dışı'}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  // Boş durum
  const renderEmptyState = () => {
    return (
      <View style={styles.emptyContainer}>
        <MaterialIcons name="folder-open" size={64} color={Colors.GRAY_300} />
        <Text style={styles.emptyTitle}>Grup Bulunamadı</Text>
        <Text style={styles.emptyText}>
          Henüz hatırlatıcı grubu eklenmemiş. Yeni bir grup ekleyin.
        </Text>
        <TouchableOpacity
          style={styles.emptyButton}
          onPress={showAddGroupModal}
        >
          <MaterialIcons name="add" size={20} color="#fff" />
          <Text style={styles.emptyButtonText}>Yeni Grup</Text>
        </TouchableOpacity>
      </View>
    );
  };

  // Yükleniyor durumu
  if (loading && !refreshing) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={styles.loadingText}>Yükleniyor...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Başlık */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Hatırlatıcı Grupları</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={showAddGroupModal}
        >
          <MaterialIcons name="add" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      {/* İçerik */}
      <FlatList
        data={groups}
        renderItem={renderGroupItem}
        keyExtractor={item => item.id.toString()}
        contentContainerStyle={styles.listContent}
        ListEmptyComponent={renderEmptyState}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[Colors.PRIMARY]}
          />
        }
      />

      {/* Grup Ekleme/Düzenleme Modalı */}
      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {editingGroup ? 'Grubu Düzenle' : 'Yeni Grup Ekle'}
              </Text>
              <TouchableOpacity
                style={styles.modalCloseButton}
                onPress={() => setModalVisible(false)}
              >
                <MaterialIcons name="close" size={24} color="#333" />
              </TouchableOpacity>
            </View>

            <View style={styles.modalContent}>
              {/* Grup Adı */}
              <View style={styles.formGroup}>
                <Text style={styles.label}>Grup Adı</Text>
                <TextInput
                  style={styles.input}
                  value={groupName}
                  onChangeText={setGroupName}
                  placeholder="Grup adı girin"
                  maxLength={50}
                />
              </View>

              {/* Grup Açıklaması */}
              <View style={styles.formGroup}>
                <Text style={styles.label}>Açıklama</Text>
                <TextInput
                  style={[styles.input, styles.textArea]}
                  value={groupDescription}
                  onChangeText={setGroupDescription}
                  placeholder="Grup açıklaması (isteğe bağlı)"
                  multiline
                  numberOfLines={3}
                  maxLength={200}
                />
              </View>

              {/* Grup Rengi */}
              <View style={styles.formGroup}>
                <Text style={styles.label}>Renk</Text>
                <View style={styles.colorPickerContainer}>
                  {['#3498db', '#2ecc71', '#e74c3c', '#f39c12', '#9b59b6', '#1abc9c', '#e67e22', '#34495e'].map(color => (
                    <TouchableOpacity
                      key={color}
                      style={[
                        styles.colorOption,
                        { backgroundColor: color },
                        groupColor === color && styles.colorOptionSelected
                      ]}
                      onPress={() => setGroupColor(color)}
                    >
                      {groupColor === color && (
                        <MaterialIcons name="check" size={16} color="#fff" />
                      )}
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              {/* Grup İkonu */}
              <View style={styles.formGroup}>
                <Text style={styles.label}>İkon</Text>
                <View style={styles.iconPickerContainer}>
                  {['folder', 'work', 'home', 'favorite', 'star', 'flag', 'label', 'bookmark', 'event', 'shopping-cart', 'school', 'account-balance'].map(icon => (
                    <TouchableOpacity
                      key={icon}
                      style={[
                        styles.iconOption,
                        groupIcon === icon && styles.iconOptionSelected
                      ]}
                      onPress={() => setGroupIcon(icon)}
                    >
                      <MaterialIcons
                        name={icon}
                        size={24}
                        color={groupIcon === icon ? groupColor : Colors.TEXT_DARK}
                      />
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            </View>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={styles.modalCancelButton}
                onPress={() => setModalVisible(false)}
              >
                <Text style={styles.modalCancelButtonText}>İptal</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.modalSaveButton}
                onPress={saveGroup}
                disabled={saving}
              >
                {saving ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <Text style={styles.modalSaveButtonText}>Kaydet</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Yeni Grup Butonu */}
      <TouchableOpacity
        style={styles.floatingButton}
        onPress={showAddGroupModal}
      >
        <MaterialIcons name="add" size={24} color="#fff" />
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: Colors.PRIMARY,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContent: {
    padding: 16,
    paddingBottom: 80,
  },
  groupItem: {
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    borderLeftWidth: 4,
    overflow: 'hidden',
  },
  groupHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
  },
  groupTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  groupTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    marginLeft: 8,
  },
  defaultBadge: {
    fontSize: 12,
    fontStyle: 'italic',
    color: Colors.TEXT_LIGHT,
  },
  inactiveText: {
    color: Colors.GRAY_500,
  },
  groupDescription: {
    fontSize: 14,
    color: Colors.TEXT_LIGHT,
    paddingHorizontal: 12,
    paddingBottom: 8,
  },
  groupFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 12,
    borderTopWidth: 1,
    borderTopColor: Colors.GRAY_200,
  },
  groupInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  groupInfoText: {
    fontSize: 12,
    color: Colors.TEXT_LIGHT,
    marginLeft: 4,
  },
  groupActions: {
    flexDirection: 'row',
  },
  actionButton: {
    marginLeft: 12,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
    marginTop: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    color: Colors.TEXT_LIGHT,
    textAlign: 'center',
    marginBottom: 24,
  },
  emptyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  emptyButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  floatingButton: {
    position: 'absolute',
    right: 16,
    bottom: 16,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    width: '100%',
    maxWidth: 500,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
  },
  modalCloseButton: {
    padding: 4,
  },
  modalContent: {
    padding: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: Colors.TEXT_DARK,
  },
  input: {
    borderWidth: 1,
    borderColor: Colors.GRAY_300,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: Colors.GRAY_100,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  colorPickerContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  colorOption: {
    width: 40,
    height: 40,
    borderRadius: 20,
    margin: 8,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.GRAY_300,
  },
  colorOptionSelected: {
    borderWidth: 2,
    borderColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
    elevation: 2,
  },
  iconPickerContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  iconOption: {
    width: 48,
    height: 48,
    borderRadius: 24,
    margin: 8,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.GRAY_300,
  },
  iconOptionSelected: {
    borderWidth: 2,
    borderColor: Colors.PRIMARY,
    backgroundColor: Colors.GRAY_100,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.GRAY_200,
  },
  modalCancelButton: {
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginRight: 8,
  },
  modalCancelButtonText: {
    color: Colors.TEXT_DARK,
    fontSize: 16,
  },
  modalSaveButton: {
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 80,
  },
  modalSaveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
