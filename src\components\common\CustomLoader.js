import React from 'react';
import { View, StyleSheet, Animated, Easing } from 'react-native';

/**
 * <PERSON>zel yükleme göstergesi bileşeni
 * ActivityIndicator kullanmadığı için platform hatalarından etkilenmez
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {string} [props.color='#3498db'] - Dönen çemberlerin rengi
 * @param {number} [props.size=40] - Yükleme göstergesinin boyutu
 * @returns {JSX.Element} Özel yükleme göstergesi
 */
const CustomLoader = ({ color = '#3498db', size = 40 }) => {
  // Dönen animasyonlar için 3 farklı değer oluşturuyoruz
  const rotation1 = React.useRef(new Animated.Value(0)).current;
  const rotation2 = React.useRef(new Animated.Value(0)).current;
  const rotation3 = React.useRef(new Animated.Value(0)).current;
  
  // Animasyonları başlat
  React.useEffect(() => {
    const createAnimation = (value) => {
      return Animated.loop(
        Animated.timing(value, {
          toValue: 1,
          duration: 1200,
          easing: Easing.linear,
          useNativeDriver: true,
        })
      );
    };
    
    // Animasyonları farklı başlangıçlarla başlat
    createAnimation(rotation1).start();
    
    setTimeout(() => {
      createAnimation(rotation2).start();
    }, 150);
    
    setTimeout(() => {
      createAnimation(rotation3).start();
    }, 300);
    
    return () => {
      // Bileşen unmount olduğunda animasyonları durdur
      rotation1.stopAnimation();
      rotation2.stopAnimation();
      rotation3.stopAnimation();
    };
  }, [rotation1, rotation2, rotation3]);
  
  // Dönüş animasyonlarını hesapla
  const spin1 = rotation1.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });
  
  const spin2 = rotation2.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });
  
  const spin3 = rotation3.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const dotSize = size * 0.2;
  const dotDistance = size * 0.4;

  return (
    <View style={styles.container}>
      <Animated.View 
        style={[
          styles.dot,
          { 
            width: dotSize,
            height: dotSize,
            borderRadius: dotSize / 2,
            backgroundColor: color,
            transform: [
              { translateX: -dotDistance },
              { rotate: spin1 },
              { translateX: dotDistance }
            ]
          }
        ]} 
      />
      <Animated.View 
        style={[
          styles.dot, 
          { 
            width: dotSize,
            height: dotSize,
            borderRadius: dotSize / 2,
            backgroundColor: color,
            transform: [
              { rotate: spin2 }
            ]
          }
        ]} 
      />
      <Animated.View 
        style={[
          styles.dot, 
          { 
            width: dotSize,
            height: dotSize,
            borderRadius: dotSize / 2,
            backgroundColor: color,
            transform: [
              { translateX: dotDistance },
              { rotate: spin3 },
              { translateX: -dotDistance }
            ]
          }
        ]} 
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    height: 50,
  },
  dot: {
    margin: 5,
  },
});

export default CustomLoader;
