import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView, Modal } from 'react-native';

/**
 * Chart Type Selector component
 * @param {Object} props - Component props
 * @param {string} props.selectedType - Currently selected chart type
 * @param {Function} props.onTypeSelect - <PERSON><PERSON> for chart type selection
 * @param {Object} props.theme - Theme object
 * @param {Array} props.availableTypes - Available chart types
 * @param {boolean} props.visible - Modal visibility
 * @param {Function} props.onClose - Modal close handler
 * @returns {JSX.Element} Chart type selector component
 */
const ChartTypeSelector = ({
  selectedType,
  onTypeSelect,
  theme,
  availableTypes = ['line', 'bar', 'pie', 'progress', 'contribution'],
  visible = false,
  onClose
}) => {
  /**
   * Get safe theme value
   * @param {string} property - Theme property
   * @param {string} fallback - Fallback value
   * @returns {string} Safe theme value
   */
  const getSafeThemeValue = (property, fallback) => {
    return theme?.[property] || theme?.colors?.[property.toLowerCase()] || fallback;
  };

  /**
   * Get chart type display info
   * @param {string} type - Chart type
   * @returns {Object} Chart type display info
   */
  const getChartTypeInfo = (type) => {
    const typeInfo = {
      line: { label: 'Çizgi Grafik', icon: '📈' },
      bar: { label: 'Çubuk Grafik', icon: '📊' },
      pie: { label: 'Pasta Grafik', icon: '🥧' },
      progress: { label: 'İlerleme Grafik', icon: '⭕' },
      contribution: { label: 'Katkı Grafik', icon: '📅' },
    };
    return typeInfo[type] || { label: type, icon: '📊' };
  };

  const content = (
    <View style={[styles.container, { backgroundColor: getSafeThemeValue('BACKGROUND', '#ffffff') }]}>
      <View style={[styles.header, { backgroundColor: getSafeThemeValue('SURFACE', '#f5f5f5') }]}>
        <Text style={[styles.title, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
          Grafik Türü Seçin
        </Text>
        {onClose && (
          <TouchableOpacity
            style={[styles.closeButton, { backgroundColor: getSafeThemeValue('ERROR', '#ff3b30') }]}
            onPress={onClose}
          >
            <Text style={[styles.closeButtonText, { color: getSafeThemeValue('SURFACE', '#ffffff') }]}>
              ✕
            </Text>
          </TouchableOpacity>
        )}
      </View>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.scrollView}>
        <View style={styles.typesContainer}>
          {availableTypes.map((type) => {
            const typeInfo = getChartTypeInfo(type);
            const isSelected = selectedType === type;
            return (
              <TouchableOpacity
                key={type}
                style={[
                  styles.typeButton,
                  {
                    backgroundColor: isSelected 
                      ? getSafeThemeValue('PRIMARY', '#007AFF') 
                      : getSafeThemeValue('BACKGROUND_SECONDARY', '#f5f5f5'),
                    borderColor: isSelected 
                      ? getSafeThemeValue('PRIMARY', '#007AFF') 
                      : getSafeThemeValue('BORDER', '#e0e0e0'),
                  },
                ]}
                onPress={() => onTypeSelect(type)}
              >
                <Text style={styles.typeIcon}>{typeInfo.icon}</Text>
                <Text
                  style={[
                    styles.typeLabel,
                    {
                      color: isSelected 
                        ? '#ffffff' 
                        : getSafeThemeValue('TEXT_PRIMARY', '#333333'),
                    },
                  ]}
                >
                  {typeInfo.label}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    borderRadius: 8,
    marginVertical: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
  },
  scrollView: {
    flexGrow: 0,
  },
  typesContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  typeButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
    minWidth: 100,
  },
  typeIcon: {
    fontSize: 20,
    marginBottom: 4,
  },
  typeLabel: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
});

export default ChartTypeSelector;
