import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';

/**
 * <PERSON><PERSON><PERSON><PERSON> ödemeler kartı bileşeni
 * 
 * @param {Object} props - Bileşen props'ları
 * @param {Array} props.payments - Yaklaşan ödemeler listesi
 * @param {Function} props.onViewAll - Tümünü görüntüle butonuna tıklama olayı
 * @returns {JSX.Element} UpcomingPaymentsCard bileşeni
 */
const UpcomingPaymentsCard = ({ payments = [], onViewAll }) => {
  // Eğer ödeme yoksa kartı gösterme
  if (!payments || payments.length === 0) {
    return null;
  }
  
  // Para birimini formatla
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 2
    }).format(amount);
  };
  
  // Tarihi formatla
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return format(date, 'd MMMM', { locale: tr });
  };
  
  // Ödeme öğesi
  const renderPaymentItem = ({ item }) => (
    <View style={styles.paymentItem}>
      <View style={styles.paymentInfo}>
        <Text style={styles.paymentName} numberOfLines={1}>
          {item.description || 'Ödeme'}
        </Text>
        <Text style={styles.paymentDate}>
          {formatDate(item.transaction_date)}
        </Text>
      </View>
      <Text style={styles.paymentAmount}>
        {formatCurrency(item.amount)}
      </Text>
    </View>
  );
  
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Yaklaşan Ödemeler</Text>
        <TouchableOpacity onPress={onViewAll}>
          <Text style={styles.viewAllText}>Tümünü Gör</Text>
        </TouchableOpacity>
      </View>
      
      <FlatList
        data={payments.slice(0, 3)}
        renderItem={renderPaymentItem}
        keyExtractor={(item) => item.id.toString()}
        scrollEnabled={false}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
      />
      
      {payments.length > 3 && (
        <TouchableOpacity style={styles.moreButton} onPress={onViewAll}>
          <Text style={styles.moreButtonText}>
            {payments.length - 3} daha fazla ödeme
          </Text>
          <MaterialIcons name="chevron-right" size={16} color="#666" />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    margin: 16,
    marginTop: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  viewAllText: {
    color: '#3498db',
    fontSize: 14,
  },
  paymentItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  paymentInfo: {
    flex: 1,
    marginRight: 16,
  },
  paymentName: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
  },
  paymentDate: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  paymentAmount: {
    fontSize: 16,
    fontWeight: '600',
    color: '#e74c3c',
  },
  separator: {
    height: 1,
    backgroundColor: '#f0f0f0',
  },
  moreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    marginTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  moreButtonText: {
    color: '#666',
    fontSize: 14,
    marginRight: 4,
  },
});

export default UpcomingPaymentsCard;
