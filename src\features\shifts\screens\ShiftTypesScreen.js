import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Alert,
  Modal,
  TextInput,
  ScrollView
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import ColorPicker from 'react-native-wheel-color-picker';
import { Colors } from '../../../constants/colors';
import { shiftStyles } from '../styles/shiftStyles';
import * as shiftService from '../services/shiftService';
import ShiftTypeItem from '../components/ShiftTypeItem';

/**
 * Vardiya Türleri Ekranı
 *
 * Bu ekran, vardiya türlerini listeler ve yönetmeyi sağlar.
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @returns {JSX.Element} Vardiya türleri ekranı
 */
const ShiftTypesScreen = ({ navigation }) => {
  const db = useSQLiteContext();
  const insets = useSafeAreaInsets();

  // Durum değişkenleri
  const [loading, setLoading] = useState(true);
  const [shiftTypes, setShiftTypes] = useState([]);

  // Form durumları
  const [showModal, setShowModal] = useState(false);
  const [editingType, setEditingType] = useState(null);
  const [name, setName] = useState('');
  const [color, setColor] = useState('#4caf50');
  const [startTime, setStartTime] = useState('');
  const [endTime, setEndTime] = useState('');
  const [breakDuration, setBreakDuration] = useState('');
  const [hourlyRate, setHourlyRate] = useState('');

  // Verileri yükle
  useEffect(() => {
    loadData();
  }, []);

  // Verileri yükle
  const loadData = async () => {
    try {
      setLoading(true);
      const types = await shiftService.getShiftTypes(db);
      setShiftTypes(types);
      setLoading(false);
    } catch (error) {
      console.error('Veri yükleme hatası:', error);
      Alert.alert('Hata', 'Veriler yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  };

  // Modal aç
  const handleOpenModal = (type = null) => {
    if (type) {
      setEditingType(type);
      setName(type.name);
      setColor(type.color);
      setStartTime(type.start_time || '');
      setEndTime(type.end_time || '');
      setBreakDuration(type.break_duration?.toString() || '');
      setHourlyRate(type.hourly_rate?.toString() || '');
    } else {
      setEditingType(null);
      setName('');
      setColor('#4caf50');
      setStartTime('');
      setEndTime('');
      setBreakDuration('');
      setHourlyRate('');
    }

    setShowModal(true);
  };

  // Modal kapat
  const handleCloseModal = () => {
    setShowModal(false);
    setEditingType(null);
  };

  // Vardiya türü kaydet
  const handleSaveShiftType = async () => {
    // Validasyon
    if (!name.trim()) {
      Alert.alert('Hata', 'Vardiya türü adı boş olamaz.');
      return;
    }

    try {
      const shiftTypeData = {
        name: name.trim(),
        color: color,
        start_time: startTime.trim() || null,
        end_time: endTime.trim() || null,
        break_duration: breakDuration ? parseInt(breakDuration) : 0,
        hourly_rate: hourlyRate ? parseFloat(hourlyRate) : null
      };

      if (editingType) {
        // Vardiya türünü güncelle
        await shiftService.updateShiftType(db, editingType.id, shiftTypeData);
        Alert.alert('Başarılı', 'Vardiya türü başarıyla güncellendi.');
      } else {
        // Yeni vardiya türü ekle
        await shiftService.addShiftType(db, shiftTypeData);
        Alert.alert('Başarılı', 'Yeni vardiya türü başarıyla eklendi.');
      }

      handleCloseModal();
      loadData();
    } catch (error) {
      console.error('Vardiya türü kaydetme hatası:', error);
      Alert.alert('Hata', 'Vardiya türü kaydedilirken bir hata oluştu.');
    }
  };

  // Vardiya türü sil
  const handleDeleteShiftType = (id) => {
    Alert.alert(
      'Vardiya Türü Sil',
      'Bu vardiya türünü silmek istediğinize emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await shiftService.deleteShiftType(db, id);
              Alert.alert('Başarılı', 'Vardiya türü başarıyla silindi.');
              loadData();
            } catch (error) {
              console.error('Vardiya türü silme hatası:', error);
              Alert.alert('Hata', 'Vardiya türü silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };

  // Liste öğesi render fonksiyonu
  const renderShiftTypeItem = ({ item }) => (
    <ShiftTypeItem
      shiftType={item}
      onPress={() => handleOpenModal(item)}
      onDelete={handleDeleteShiftType}
    />
  );

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      {/* Başlık */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>

        <Text style={styles.headerTitle}>Vardiya Türleri</Text>

        <View style={{ width: 40 }} />
      </View>

      {/* Vardiya Türleri Listesi */}
      {loading ? (
        <View style={shiftStyles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.PRIMARY} />
          <Text style={shiftStyles.loadingText}>Vardiya türleri yükleniyor...</Text>
        </View>
      ) : (
        <FlatList
          data={shiftTypes}
          renderItem={renderShiftTypeItem}
          keyExtractor={item => item.id.toString()}
          contentContainerStyle={styles.listContent}
          ListEmptyComponent={
            <View style={shiftStyles.emptyState}>
              <MaterialIcons name="category" size={48} color={Colors.GRAY_400} />
              <Text style={shiftStyles.emptyStateText}>Henüz vardiya türü yok</Text>
              <Text style={shiftStyles.emptyStateSubText}>
                Yeni bir vardiya türü eklemek için aşağıdaki butona tıklayın
              </Text>
            </View>
          }
        />
      )}

      {/* Yeni Vardiya Türü Ekle Butonu */}
      <TouchableOpacity
        style={styles.addButton}
        onPress={() => handleOpenModal()}
      >
        <MaterialIcons name="add" size={24} color="#fff" />
      </TouchableOpacity>

      {/* Vardiya Türü Ekleme/Düzenleme Modalı */}
      <Modal
        visible={showModal}
        transparent={true}
        animationType="slide"
        onRequestClose={handleCloseModal}
      >
        <View style={shiftStyles.modalOverlay}>
          <View style={shiftStyles.modalContent}>
            <Text style={shiftStyles.modalTitle}>
              {editingType ? 'Vardiya Türü Düzenle' : 'Yeni Vardiya Türü Ekle'}
            </Text>

            <ScrollView>
              {/* Vardiya Türü Adı */}
              <View style={styles.formGroup}>
                <Text style={styles.label}>Vardiya Türü Adı</Text>
                <TextInput
                  style={styles.input}
                  value={name}
                  onChangeText={setName}
                  placeholder="Örn: Gündüz Vardiyası"
                />
              </View>

              {/* Renk Seçici */}
              <View style={styles.formGroup}>
                <Text style={styles.label}>Renk</Text>
                <View style={styles.colorPreviewContainer}>
                  <View style={[styles.colorPreview, { backgroundColor: color }]} />
                </View>
                <View style={styles.colorPickerContainer}>
                  <ColorPicker
                    color={color}
                    onColorChange={setColor}
                    thumbSize={30}
                    sliderSize={20}
                    noSnap={true}
                    row={false}
                    swatches={false}
                  />
                </View>
              </View>

              {/* Başlangıç ve Bitiş Saatleri */}
              <View style={styles.timeContainer}>
                <View style={[styles.formGroup, { flex: 1, marginRight: 8 }]}>
                  <Text style={styles.label}>Başlangıç Saati</Text>
                  <TextInput
                    style={styles.input}
                    value={startTime}
                    onChangeText={setStartTime}
                    placeholder="09:00"
                  />
                </View>

                <View style={[styles.formGroup, { flex: 1 }]}>
                  <Text style={styles.label}>Bitiş Saati</Text>
                  <TextInput
                    style={styles.input}
                    value={endTime}
                    onChangeText={setEndTime}
                    placeholder="17:00"
                  />
                </View>
              </View>

              {/* Mola Süresi */}
              <View style={styles.formGroup}>
                <Text style={styles.label}>Mola Süresi (dakika)</Text>
                <TextInput
                  style={styles.input}
                  value={breakDuration}
                  onChangeText={setBreakDuration}
                  keyboardType="numeric"
                  placeholder="60"
                />
              </View>

              {/* Saatlik Ücret */}
              <View style={styles.formGroup}>
                <Text style={styles.label}>Saatlik Ücret</Text>
                <View style={styles.inputWithIcon}>
                  <Text style={styles.inputIcon}>₺</Text>
                  <TextInput
                    style={styles.inputWithIconField}
                    value={hourlyRate}
                    onChangeText={setHourlyRate}
                    keyboardType="numeric"
                    placeholder="0.00"
                  />
                </View>
              </View>
            </ScrollView>

            {/* Butonlar */}
            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={[styles.button, styles.cancelButton]}
                onPress={handleCloseModal}
              >
                <Text style={styles.cancelButtonText}>İptal</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.button, styles.saveButton]}
                onPress={handleSaveShiftType}
              >
                <Text style={styles.saveButtonText}>Kaydet</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...shiftStyles.container,
  },
  header: {
    ...shiftStyles.header,
  },
  headerTitle: {
    ...shiftStyles.headerTitle,
  },
  backButton: {
    ...shiftStyles.backButton,
  },
  listContent: {
    padding: 16,
  },
  addButton: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  colorPickerContainer: {
    height: 220,
    marginBottom: 16,
  },
  colorPreviewContainer: {
    marginBottom: 16,
  },
  colorPreview: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  timeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  inputWithIcon: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 12,
    backgroundColor: '#fff',
  },
  inputIcon: {
    paddingHorizontal: 16,
    fontSize: 16,
    color: '#666',
  },
  inputWithIconField: {
    flex: 1,
    paddingVertical: 14,
    fontSize: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
    paddingTop: 16,
  },
  button: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButton: {
    backgroundColor: '#f0f0f0',
    marginRight: 8,
  },
  saveButton: {
    backgroundColor: Colors.PRIMARY,
    marginLeft: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#666',
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
  },
});

export default ShiftTypesScreen;
