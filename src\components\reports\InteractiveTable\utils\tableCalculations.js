/**
 * Tablo hesaplama yardımcı fonksiyonları
 */

/**
 * Tablo verilerinin istatistiklerini hesapla
 * @param {Array} data - Tablo verisi
 * @param {Array} columns - Sütun bilgileri
 * @returns {Object} İstatistikler
 */
export const calculateTableStats = (data, columns) => {
  if (!data || !Array.isArray(data) || data.length === 0) {
    return {
      rowCount: 0,
      columnCount: columns ? columns.length : 0,
      numericalStats: {},
      textStats: {},
      dateStats: {},
    };
  }

  const stats = {
    rowCount: data.length,
    columnCount: columns ? columns.length : 0,
    numericalStats: {},
    textStats: {},
    dateStats: {},
  };

  if (!columns || !Array.isArray(columns)) {
    return stats;
  }

  columns.forEach(column => {
    const values = data.map(row => row[column.id]).filter(val => val !== null && val !== undefined);
    
    if (values.length === 0) return;

    switch (column.type) {
      case 'number':
      case 'currency':
        stats.numericalStats[column.id] = calculateNumericalStats(values);
        break;
      case 'text':
        stats.textStats[column.id] = calculateTextStats(values);
        break;
      case 'date':
        stats.dateStats[column.id] = calculateDateStats(values);
        break;
    }
  });

  return stats;
};

/**
 * Sayısal veri istatistikleri hesapla
 * @param {Array} values - Sayısal değerler
 * @returns {Object} Sayısal istatistikler
 */
export const calculateNumericalStats = (values) => {
  const numbers = values.map(val => parseFloat(val)).filter(val => !isNaN(val));
  
  if (numbers.length === 0) {
    return {
      count: 0,
      sum: 0,
      avg: 0,
      min: 0,
      max: 0,
      median: 0,
      mode: 0,
      variance: 0,
      standardDeviation: 0,
    };
  }

  const sum = numbers.reduce((acc, val) => acc + val, 0);
  const avg = sum / numbers.length;
  const min = Math.min(...numbers);
  const max = Math.max(...numbers);
  
  // Medyan hesaplama
  const sorted = [...numbers].sort((a, b) => a - b);
  const median = sorted.length % 2 === 0
    ? (sorted[sorted.length / 2 - 1] + sorted[sorted.length / 2]) / 2
    : sorted[Math.floor(sorted.length / 2)];

  // Mod hesaplama
  const frequency = {};
  numbers.forEach(val => {
    frequency[val] = (frequency[val] || 0) + 1;
  });
  const mode = Object.keys(frequency).reduce((a, b) => frequency[a] > frequency[b] ? a : b);

  // Varyans ve standart sapma
  const variance = numbers.reduce((acc, val) => acc + Math.pow(val - avg, 2), 0) / numbers.length;
  const standardDeviation = Math.sqrt(variance);

  return {
    count: numbers.length,
    sum,
    avg,
    min,
    max,
    median,
    mode: parseFloat(mode),
    variance,
    standardDeviation,
  };
};

/**
 * Metin veri istatistikleri hesapla
 * @param {Array} values - Metin değerler
 * @returns {Object} Metin istatistikleri
 */
export const calculateTextStats = (values) => {
  const texts = values.map(val => val.toString()).filter(val => val.length > 0);
  
  if (texts.length === 0) {
    return {
      count: 0,
      uniqueCount: 0,
      mostCommon: null,
      leastCommon: null,
      avgLength: 0,
      minLength: 0,
      maxLength: 0,
      emptyCount: 0,
    };
  }

  const frequency = {};
  let totalLength = 0;
  let minLength = Infinity;
  let maxLength = 0;

  texts.forEach(text => {
    frequency[text] = (frequency[text] || 0) + 1;
    totalLength += text.length;
    minLength = Math.min(minLength, text.length);
    maxLength = Math.max(maxLength, text.length);
  });

  const uniqueCount = Object.keys(frequency).length;
  const sortedByFreq = Object.entries(frequency).sort((a, b) => b[1] - a[1]);
  const mostCommon = sortedByFreq[0] ? sortedByFreq[0][0] : null;
  const leastCommon = sortedByFreq[sortedByFreq.length - 1] ? sortedByFreq[sortedByFreq.length - 1][0] : null;

  return {
    count: texts.length,
    uniqueCount,
    mostCommon,
    leastCommon,
    avgLength: totalLength / texts.length,
    minLength: minLength === Infinity ? 0 : minLength,
    maxLength,
    emptyCount: values.length - texts.length,
  };
};

/**
 * Tarih veri istatistikleri hesapla
 * @param {Array} values - Tarih değerler
 * @returns {Object} Tarih istatistikleri
 */
export const calculateDateStats = (values) => {
  const dates = values.map(val => new Date(val)).filter(date => !isNaN(date.getTime()));
  
  if (dates.length === 0) {
    return {
      count: 0,
      earliest: null,
      latest: null,
      range: 0,
      avgAge: 0,
      distribution: {},
    };
  }

  const timestamps = dates.map(date => date.getTime());
  const earliest = new Date(Math.min(...timestamps));
  const latest = new Date(Math.max(...timestamps));
  const range = latest.getTime() - earliest.getTime();

  // Yaş ortalaması (günler cinsinden)
  const now = new Date();
  const ages = dates.map(date => (now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
  const avgAge = ages.reduce((acc, age) => acc + age, 0) / ages.length;

  // Yıl bazında dağılım
  const distribution = {};
  dates.forEach(date => {
    const year = date.getFullYear();
    distribution[year] = (distribution[year] || 0) + 1;
  });

  return {
    count: dates.length,
    earliest,
    latest,
    range,
    avgAge,
    distribution,
  };
};

/**
 * Pivot tablo hesaplama
 * @param {Array} data - Kaynak veri
 * @param {string} rowField - Satır alanı
 * @param {string} colField - Sütun alanı
 * @param {string} valueField - Değer alanı
 * @param {string} aggregateType - Toplama türü (sum, avg, count, min, max)
 * @returns {Object} Pivot tablo verisi
 */
export const calculatePivotTable = (data, rowField, colField, valueField, aggregateType = 'sum') => {
  if (!data || !Array.isArray(data) || data.length === 0) {
    return {
      rows: [],
      columns: [],
      data: {},
      totals: { rows: {}, columns: {}, grandTotal: 0 },
    };
  }

  const pivotData = {};
  const rowSet = new Set();
  const colSet = new Set();
  const totals = { rows: {}, columns: {}, grandTotal: 0 };

  // Veriyi grupla
  data.forEach(row => {
    const rowKey = row[rowField];
    const colKey = row[colField];
    const value = parseFloat(row[valueField]) || 0;

    if (rowKey !== undefined && colKey !== undefined) {
      rowSet.add(rowKey);
      colSet.add(colKey);

      if (!pivotData[rowKey]) {
        pivotData[rowKey] = {};
      }
      if (!pivotData[rowKey][colKey]) {
        pivotData[rowKey][colKey] = [];
      }
      pivotData[rowKey][colKey].push(value);
    }
  });

  const rows = Array.from(rowSet).sort();
  const columns = Array.from(colSet).sort();

  // Toplama işlemini gerçekleştir
  const aggregatedData = {};
  rows.forEach(rowKey => {
    aggregatedData[rowKey] = {};
    let rowTotal = 0;

    columns.forEach(colKey => {
      const values = pivotData[rowKey]?.[colKey] || [];
      let aggregatedValue = 0;

      if (values.length > 0) {
        switch (aggregateType) {
          case 'sum':
            aggregatedValue = values.reduce((sum, val) => sum + val, 0);
            break;
          case 'avg':
            aggregatedValue = values.reduce((sum, val) => sum + val, 0) / values.length;
            break;
          case 'count':
            aggregatedValue = values.length;
            break;
          case 'min':
            aggregatedValue = Math.min(...values);
            break;
          case 'max':
            aggregatedValue = Math.max(...values);
            break;
          default:
            aggregatedValue = values.reduce((sum, val) => sum + val, 0);
        }
      }

      aggregatedData[rowKey][colKey] = aggregatedValue;
      rowTotal += aggregatedValue;
      totals.columns[colKey] = (totals.columns[colKey] || 0) + aggregatedValue;
    });

    totals.rows[rowKey] = rowTotal;
    totals.grandTotal += rowTotal;
  });

  return {
    rows,
    columns,
    data: aggregatedData,
    totals,
  };
};

/**
 * Tablo verilerini gruplama
 * @param {Array} data - Kaynak veri
 * @param {string} groupField - Gruplama alanı
 * @param {Array} aggregateFields - Toplama alanları
 * @returns {Array} Gruplenmiş veri
 */
export const groupTableData = (data, groupField, aggregateFields = []) => {
  if (!data || !Array.isArray(data) || data.length === 0) {
    return [];
  }

  const groups = {};

  data.forEach(row => {
    const groupKey = row[groupField];
    if (groupKey !== undefined) {
      if (!groups[groupKey]) {
        groups[groupKey] = {
          [groupField]: groupKey,
          count: 0,
          items: [],
        };
      }
      groups[groupKey].count++;
      groups[groupKey].items.push(row);
    }
  });

  // Toplama alanlarını hesapla
  Object.keys(groups).forEach(groupKey => {
    const group = groups[groupKey];
    aggregateFields.forEach(field => {
      const values = group.items.map(item => parseFloat(item[field.column]) || 0);
      
      switch (field.type) {
        case 'sum':
          group[field.column] = values.reduce((sum, val) => sum + val, 0);
          break;
        case 'avg':
          group[field.column] = values.reduce((sum, val) => sum + val, 0) / values.length;
          break;
        case 'min':
          group[field.column] = Math.min(...values);
          break;
        case 'max':
          group[field.column] = Math.max(...values);
          break;
        case 'count':
          group[field.column] = values.length;
          break;
      }
    });
  });

  return Object.values(groups);
};

/**
 * Yüzde hesaplama
 * @param {number} value - Değer
 * @param {number} total - Toplam
 * @param {number} decimals - Ondalık basamak sayısı
 * @returns {number} Yüzde değeri
 */
export const calculatePercentage = (value, total, decimals = 2) => {
  if (total === 0) return 0;
  return parseFloat(((value / total) * 100).toFixed(decimals));
};

/**
 * Büyüme oranı hesaplama
 * @param {number} newValue - Yeni değer
 * @param {number} oldValue - Eski değer
 * @param {number} decimals - Ondalık basamak sayısı
 * @returns {number} Büyüme oranı
 */
export const calculateGrowthRate = (newValue, oldValue, decimals = 2) => {
  if (oldValue === 0) return newValue > 0 ? 100 : 0;
  return parseFloat(((newValue - oldValue) / oldValue * 100).toFixed(decimals));
};

/**
 * Korelasyon hesaplama
 * @param {Array} x - X değerleri
 * @param {Array} y - Y değerleri
 * @returns {number} Korelasyon katsayısı
 */
export const calculateCorrelation = (x, y) => {
  if (x.length !== y.length || x.length === 0) return 0;

  const n = x.length;
  const sumX = x.reduce((sum, val) => sum + val, 0);
  const sumY = y.reduce((sum, val) => sum + val, 0);
  const sumXY = x.reduce((sum, val, i) => sum + val * y[i], 0);
  const sumX2 = x.reduce((sum, val) => sum + val * val, 0);
  const sumY2 = y.reduce((sum, val) => sum + val * val, 0);

  const numerator = n * sumXY - sumX * sumY;
  const denominator = Math.sqrt((n * sumX2 - sumX * sumX) * (n * sumY2 - sumY * sumY));

  return denominator === 0 ? 0 : numerator / denominator;
};

/**
 * Kümülatif toplam hesaplama
 * @param {Array} values - Değerler
 * @returns {Array} Kümülatif toplamlar
 */
export const calculateCumulativeSum = (values) => {
  let cumSum = 0;
  return values.map(val => {
    cumSum += parseFloat(val) || 0;
    return cumSum;
  });
};

/**
 * Hareketli ortalama hesaplama
 * @param {Array} values - Değerler
 * @param {number} period - Periyot
 * @returns {Array} Hareketli ortalamalar
 */
export const calculateMovingAverage = (values, period = 3) => {
  if (values.length < period) return [];
  
  const movingAverages = [];
  for (let i = period - 1; i < values.length; i++) {
    const sum = values.slice(i - period + 1, i + 1).reduce((sum, val) => sum + (parseFloat(val) || 0), 0);
    movingAverages.push(sum / period);
  }
  return movingAverages;
};
