import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated, Dimensions } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';
import { useAppContext } from '../../context/AppContext';

const { width: screenWidth } = Dimensions.get('window');

/**
 * Varsayılan dashboard widget'ı - hiç widget seçilmediğinde gösterilir
 * 
 * @param {Object} props - Component props
 * @param {Object} props.balanceData - Bakiye verileri
 * @param {Object} props.lastTransaction - Son işlem
 * @param {Object} props.quickStats - Hızlı istatistikler
 * @param {Function} props.onCustomizePress - Widget özelleştirme butonu
 * @param {Function} props.onAddIncomePress - <PERSON><PERSON><PERSON> ekleme butonu
 * @param {Function} props.onAddExpensePress - Gider ekleme butonu
 * @param {Function} props.onViewStatsPress - İstatistikler görüntüleme butonu
 * @returns {JSX.Element} Varsayılan dashboard widget
 */
const DefaultDashboardWidget = ({
  balanceData = { income: 0, expense: 0, balance: 0 },
  lastTransaction = null,
  quickStats = { thisMonth: 0, lastWeek: 0, trend: 'neutral' },
  onCustomizePress,
  onAddIncomePress,
  onAddExpensePress,
  onViewStatsPress,
}) => {
  const { theme } = useTheme();
  const { defaultCurrency } = useAppContext();
  const [animatedValue] = useState(new Animated.Value(0));

  useEffect(() => {
    Animated.timing(animatedValue, {
      toValue: 1,
      duration: 600,
      useNativeDriver: true,
    }).start();
  }, []);

  /**
   * Para formatı
   * @param {number} amount - Miktar
   * @returns {string} Formatlanmış miktar
   */
  const formatCurrency = (amount) => {
    return `₺${Math.abs(amount).toLocaleString('tr-TR')}`;
  };

  /**
   * Trend ikonu ve rengi
   * @param {string} trend - Trend yönü
   * @returns {Object} Icon ve renk bilgisi
   */
  const getTrendInfo = (trend) => {
    switch (trend) {
      case 'up':
        return { icon: 'trending-up', color: theme.SUCCESS };
      case 'down':
        return { icon: 'trending-down', color: theme.DANGER };
      default:
        return { icon: 'trending-flat', color: theme.TEXT_SECONDARY };
    }
  };

  /**
   * Kategori ikonu
   * @param {string} categoryName - Kategori adı
   * @returns {string} Material icon adı
   */
  const getCategoryIcon = (categoryName) => {
    const iconMap = {
      'Market': 'shopping-cart',
      'Ulaşım': 'directions-car',
      'Yeme-İçme': 'restaurant',
      'Fatura': 'receipt',
      'Maaş': 'work',
      'Freelance': 'computer',
      'Hediye': 'card-giftcard',
      'Diğer': 'category',
    };
    return iconMap[categoryName] || 'category';
  };

  const trendInfo = getTrendInfo(quickStats.trend);

  return (
    <Animated.View
      style={[
        styles.container,
        { backgroundColor: theme.CARD },
        {
          opacity: animatedValue,
          transform: [
            {
              translateY: animatedValue.interpolate({
                inputRange: [0, 1],
                outputRange: [30, 0],
              }),
            },
          ],
        },
      ]}
    >
      {/* Widget Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <View style={[styles.iconContainer, { backgroundColor: theme.PRIMARY + '20' }]}>
            <MaterialIcons name="dashboard" size={24} color={theme.PRIMARY} />
          </View>
          <View>
            <Text style={[styles.headerTitle, { color: theme.TEXT_PRIMARY }]}>
              Finansal Özet
            </Text>
            <Text style={[styles.headerSubtitle, { color: theme.TEXT_SECONDARY }]}>
              Bu ayın durumu
            </Text>
          </View>
        </View>
        <TouchableOpacity
          style={styles.customizeButton}
          onPress={onCustomizePress}
          activeOpacity={0.7}
        >
          <MaterialIcons name="tune" size={20} color={theme.TEXT_SECONDARY} />
        </TouchableOpacity>
      </View>

      {/* Bakiye Overview */}
      <View style={styles.balanceSection}>
        <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
          Bakiye Durumu
        </Text>
        <View style={styles.balanceCards}>
          <View style={[styles.balanceCard, { backgroundColor: theme.SUCCESS + '15' }]}>
            <MaterialIcons name="arrow-upward" size={20} color={theme.SUCCESS} />
            <Text style={[styles.balanceLabel, { color: theme.TEXT_SECONDARY }]}>Gelir</Text>
            <Text style={[styles.balanceAmount, { color: theme.SUCCESS }]}>
              {formatCurrency(balanceData.income)}
            </Text>
          </View>
          <View style={[styles.balanceCard, { backgroundColor: theme.DANGER + '15' }]}>
            <MaterialIcons name="arrow-downward" size={20} color={theme.DANGER} />
            <Text style={[styles.balanceLabel, { color: theme.TEXT_SECONDARY }]}>Gider</Text>
            <Text style={[styles.balanceAmount, { color: theme.DANGER }]}>
              {formatCurrency(balanceData.expense)}
            </Text>
          </View>
          <View style={[styles.balanceCard, { backgroundColor: theme.PRIMARY + '15' }]}>
            <MaterialIcons name="account-balance-wallet" size={20} color={theme.PRIMARY} />
            <Text style={[styles.balanceLabel, { color: theme.TEXT_SECONDARY }]}>Bakiye</Text>
            <Text style={[styles.balanceAmount, { color: theme.PRIMARY }]}>
              {formatCurrency(balanceData.balance)}
            </Text>
          </View>
        </View>
      </View>

      {/* Son İşlem */}
      {lastTransaction && (
        <View style={styles.lastTransactionSection}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            Son İşlem
          </Text>
          <View style={[styles.transactionCard, { backgroundColor: theme.SURFACE }]}>
            <View style={styles.transactionLeft}>
              <View style={[styles.transactionIcon, { backgroundColor: theme.PRIMARY + '20' }]}>
                <MaterialIcons
                  name={getCategoryIcon(lastTransaction.category_name)}
                  size={24}
                  color={theme.PRIMARY}
                />
              </View>
              <View style={styles.transactionInfo}>
                <Text style={[styles.transactionDescription, { color: theme.TEXT_PRIMARY }]}>
                  {lastTransaction.description}
                </Text>
                <Text style={[styles.transactionCategory, { color: theme.TEXT_SECONDARY }]}>
                  {lastTransaction.category_name} • {new Date(lastTransaction.date).toLocaleDateString('tr-TR')}
                </Text>
              </View>
            </View>
            <Text style={[
              styles.transactionAmount,
              { color: lastTransaction.type === 'income' ? theme.SUCCESS : theme.DANGER }
            ]}>
              {lastTransaction.type === 'income' ? '+' : '-'}{formatCurrency(lastTransaction.amount)}
            </Text>
          </View>
        </View>
      )}

      {/* Hızlı İstatistikler */}
      <View style={styles.quickStatsSection}>
        <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
          Bu Ay
        </Text>
        <View style={styles.statsRow}>
          <View style={styles.statItem}>
            <View style={styles.statInfo}>
              <MaterialIcons name={trendInfo.icon} size={20} color={trendInfo.color} />
              <Text style={[styles.statLabel, { color: theme.TEXT_SECONDARY }]}>Trend</Text>
            </View>
            <Text style={[styles.statValue, { color: trendInfo.color }]}>
              {quickStats.trend === 'up' ? 'Artış' : quickStats.trend === 'down' ? 'Azalış' : 'Stabil'}
            </Text>
          </View>
          <TouchableOpacity
            style={[styles.statItem, { backgroundColor: theme.SURFACE }]}
            onPress={onViewStatsPress}
            activeOpacity={0.7}
          >
            <View style={styles.statInfo}>
              <MaterialIcons name="bar-chart" size={20} color={theme.PRIMARY} />
              <Text style={[styles.statLabel, { color: theme.TEXT_SECONDARY }]}>Detaylı</Text>
            </View>
            <Text style={[styles.statValue, { color: theme.PRIMARY }]}>
              İstatistikler
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Hızlı Aksiyonlar */}
      <View style={styles.quickActionsSection}>
        <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
          Hızlı İşlemler
        </Text>
        <View style={styles.actionButtonsRow}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.SUCCESS + '15' }]}
            onPress={onAddIncomePress}
            activeOpacity={0.7}
          >
            <MaterialIcons name="add-circle" size={24} color={theme.SUCCESS} />
            <Text style={[styles.actionButtonText, { color: theme.SUCCESS }]}>
              Gelir Ekle
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.DANGER + '15' }]}
            onPress={onAddExpensePress}
            activeOpacity={0.7}
          >
            <MaterialIcons name="remove-circle" size={24} color={theme.DANGER} />
            <Text style={[styles.actionButtonText, { color: theme.DANGER }]}>
              Gider Ekle
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Widget Özelleştirme CTA */}
      <View style={styles.customizationCTA}>
        <Text style={[styles.ctaTitle, { color: theme.TEXT_PRIMARY }]}>
          🎨 Ana sayfanızı kişiselleştirin
        </Text>
        <Text style={[styles.ctaDescription, { color: theme.TEXT_SECONDARY }]}>
          Daha fazla widget ekleyerek dashboard'unuzu özelleştirin
        </Text>
        <TouchableOpacity
          style={[styles.ctaButton, { backgroundColor: theme.PRIMARY }]}
          onPress={onCustomizePress}
          activeOpacity={0.8}
        >
          <MaterialIcons name="widgets" size={20} color="#fff" />
          <Text style={[styles.ctaButtonText, { color: '#fff' }]}>
            Widget'ları Düzenle
          </Text>
        </TouchableOpacity>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    padding: 20,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 2,
  },
  headerSubtitle: {
    fontSize: 12,
    fontWeight: '400',
  },
  customizeButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  balanceSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 12,
  },
  balanceCards: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 8,
  },
  balanceCard: {
    flex: 1,
    padding: 12,
    borderRadius: 12,
    alignItems: 'center',
    gap: 4,
  },
  balanceLabel: {
    fontSize: 11,
    fontWeight: '500',
  },
  balanceAmount: {
    fontSize: 14,
    fontWeight: '700',
  },
  lastTransactionSection: {
    marginBottom: 20,
  },
  transactionCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderRadius: 12,
  },
  transactionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  transactionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionDescription: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 2,
  },
  transactionCategory: {
    fontSize: 11,
    fontWeight: '400',
  },
  transactionAmount: {
    fontSize: 14,
    fontWeight: '700',
  },
  quickStatsSection: {
    marginBottom: 20,
  },
  statsRow: {
    flexDirection: 'row',
    gap: 12,
  },
  statItem: {
    flex: 1,
    padding: 12,
    borderRadius: 12,
    alignItems: 'center',
  },
  statInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    marginBottom: 6,
  },
  statLabel: {
    fontSize: 11,
    fontWeight: '500',
  },
  statValue: {
    fontSize: 12,
    fontWeight: '600',
  },
  quickActionsSection: {
    marginBottom: 20,
  },
  actionButtonsRow: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 12,
    gap: 8,
  },
  actionButtonText: {
    fontSize: 13,
    fontWeight: '600',
  },
  customizationCTA: {
    alignItems: 'center',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.05)',
  },
  ctaTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  ctaDescription: {
    fontSize: 12,
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 16,
  },
  ctaButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 20,
    gap: 8,
  },
  ctaButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
});

export default DefaultDashboardWidget;
