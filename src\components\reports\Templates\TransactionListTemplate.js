import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  FlatList,
} from 'react-native';
import { useTheme } from '../../../context/ThemeContext';
import { useDataIntegration } from '../../../context/DataIntegrationContext';
import { ExportManager } from '../Export';

/**
 * İşlem Listesi Şablonu
 * Detaylı işlem listesi raporu
 */
const TransactionListTemplate = ({ 
  templateConfig, 
  customParams = {}, 
  onExport, 
  onSave 
}) => {
  const { theme } = useTheme();
  const { reportDataService, isLoading: contextLoading } = useDataIntegration();

  const [loading, setLoading] = useState(true);
  const [reportData, setReportData] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadReportData();
  }, [reportDataService]);

  /**
   * <PERSON>or verileri<PERSON> y<PERSON>
   */
  const loadReportData = async () => {
    if (!reportDataService) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const params = {
        dateRange: customParams.dateRange || 'current_month',
        category: customParams.category || 'all',
        transactionType: customParams.transactionType || 'all',
        ...customParams
      };
      
      const data = await reportDataService.getTransactionListData(params);
      setReportData(data);
    } catch (error) {
      setError('İşlem listesi verileri yüklenemedi: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Format date for display
   */
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  /**
   * Format currency
   */
  const formatCurrency = (amount) => {
    return `₺${amount.toLocaleString('tr-TR', { minimumFractionDigits: 2 })}`;
  };

  /**
   * Render transaction item
   */
  const renderTransactionItem = ({ item }) => (
    <View style={[styles.transactionItem, { backgroundColor: theme.SURFACE }]}>
      <View style={styles.transactionHeader}>
        <Text style={[styles.transactionDate, { color: theme.TEXT_SECONDARY }]}>
          {formatDate(item.date)}
        </Text>
        <Text style={[
          styles.transactionAmount, 
          { color: item.type === 'income' ? theme.SUCCESS : theme.ERROR }
        ]}>
          {item.type === 'income' ? '+' : '-'}{formatCurrency(item.amount)}
        </Text>
      </View>
      
      <Text style={[styles.transactionDescription, { color: theme.TEXT_PRIMARY }]}>
        {item.description}
      </Text>
      
      <View style={styles.transactionFooter}>
        <Text style={[styles.transactionCategory, { color: theme.TEXT_SECONDARY }]}>
          {item.category}
        </Text>
        <Text style={[styles.transactionType, { color: theme.TEXT_SECONDARY }]}>
          {item.type === 'income' ? '📈 Gelir' : '📉 Gider'}
        </Text>
      </View>
    </View>
  );

  // Loading state
  if (loading || contextLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.BACKGROUND }]}>
        <ActivityIndicator size="large" color={theme.PRIMARY} />
        <Text style={[styles.loadingText, { color: theme.TEXT_PRIMARY }]}>
          İşlem listesi yükleniyor...
        </Text>
      </View>
    );
  }

  // Error state
  if (error) {
    return (
      <View style={[styles.errorContainer, { backgroundColor: theme.BACKGROUND }]}>
        <Text style={[styles.errorText, { color: theme.ERROR }]}>
          {error}
        </Text>
        <TouchableOpacity 
          style={[styles.retryButton, { backgroundColor: theme.PRIMARY }]}
          onPress={loadReportData}
        >
          <Text style={[styles.retryText, { color: theme.SURFACE }]}>
            Tekrar Dene
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Use real data or fallback to empty state
  const transactionData = reportData || { 
    transactions: [], 
    totalIncome: 0, 
    totalExpense: 0, 
    netAmount: 0,
    transactionCount: 0
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={[styles.header, { backgroundColor: theme.SURFACE }]}>
          <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
            📋 İşlem Listesi Raporu
          </Text>
          <Text style={[styles.subtitle, { color: theme.TEXT_SECONDARY }]}>
            Detaylı işlem kayıtları
          </Text>
        </View>

        {/* Summary Cards */}
        <View style={styles.summaryContainer}>
          <View style={[styles.summaryCard, { backgroundColor: theme.SUCCESS }]}>
            <Text style={[styles.summaryLabel, { color: theme.SURFACE }]}>
              Toplam Gelir
            </Text>
            <Text style={[styles.summaryAmount, { color: theme.SURFACE }]}>
              {formatCurrency(transactionData.totalIncome)}
            </Text>
          </View>
          
          <View style={[styles.summaryCard, { backgroundColor: theme.ERROR }]}>
            <Text style={[styles.summaryLabel, { color: theme.SURFACE }]}>
              Toplam Gider
            </Text>
            <Text style={[styles.summaryAmount, { color: theme.SURFACE }]}>
              {formatCurrency(transactionData.totalExpense)}
            </Text>
          </View>
          
          <View style={[styles.summaryCard, { backgroundColor: theme.PRIMARY }]}>
            <Text style={[styles.summaryLabel, { color: theme.SURFACE }]}>
              Net Tutar
            </Text>
            <Text style={[styles.summaryAmount, { color: theme.SURFACE }]}>
              {formatCurrency(transactionData.netAmount)}
            </Text>
          </View>
        </View>

        {/* Transaction List */}
        <View style={[styles.listContainer, { backgroundColor: theme.SURFACE }]}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
            İşlemler ({transactionData.transactionCount} adet)
          </Text>
          
          {transactionData.transactions.length === 0 ? (
            <View style={styles.emptyState}>
              <Text style={[styles.emptyText, { color: theme.TEXT_SECONDARY }]}>
                Seçilen dönem için işlem bulunamadı.
              </Text>
            </View>
          ) : (
            <FlatList
              data={transactionData.transactions}
              renderItem={renderTransactionItem}
              keyExtractor={(item, index) => `transaction-${index}`}
              scrollEnabled={false}
              showsVerticalScrollIndicator={false}
            />
          )}
        </View>

        {/* Export Actions */}
        <View style={styles.actionButtons}>
          <ExportManager 
            reportData={transactionData}
            reportTitle="İşlem Listesi Raporu"
            reportType="transaction_list"
            buttonStyle={styles.exportButton}
            buttonTextStyle={styles.exportButtonText}
            theme={theme}
          />
          
          <TouchableOpacity 
            style={[styles.actionButton, { backgroundColor: theme.ACCENT }]}
            onPress={onSave}
          >
            <Text style={[styles.actionButtonText, { color: theme.SURFACE }]}>
              💾 Raporu Kaydet
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    padding: 20,
    marginBottom: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    textAlign: 'center',
  },
  summaryContainer: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  summaryCard: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  summaryLabel: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
  },
  summaryAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  listContainer: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  emptyState: {
    padding: 20,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
  },
  transactionItem: {
    padding: 16,
    marginBottom: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
  },
  transactionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  transactionDate: {
    fontSize: 12,
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  transactionDescription: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  transactionFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  transactionCategory: {
    fontSize: 12,
  },
  transactionType: {
    fontSize: 12,
  },
  actionButtons: {
    flexDirection: 'column',
    gap: 12,
    marginBottom: 16,
  },
  exportButton: {
    marginBottom: 8,
  },
  exportButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  actionButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  // Loading and error states
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryText: {
    fontSize: 14,
    fontWeight: '600',
  },
});

export default TransactionListTemplate;
