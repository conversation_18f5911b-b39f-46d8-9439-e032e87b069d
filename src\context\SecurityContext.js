import React, { createContext, useContext, useState, useEffect } from 'react';
import * as SecureStore from 'expo-secure-store';
import * as LocalAuthentication from 'expo-local-authentication';
import * as Crypto from 'expo-crypto';
import AsyncStorage from '@react-native-async-storage/async-storage';

const SecurityContext = createContext();

/**
 * Güvenlik Context Provider
 * PIN, biyometrik kimlik doğrulama ve güvenlik ayarlarını yönetir
 */
export const SecurityProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [pinEnabled, setPinEnabled] = useState(false);
  const [biometricEnabled, setBiometricEnabled] = useState(false);
  const [biometricAvailable, setBiometricAvailable] = useState(false);
  const [biometricType, setBiometricType] = useState(null);
  const [securityLoading, setSecurityLoading] = useState(true);

  useEffect(() => {
    initializeSecurity();
  }, []);

  /**
   * Güvenlik sistemini başlat
   */
  const initializeSecurity = async () => {
    try {
      setSecurityLoading(true);
      
      // PIN durumunu kontrol et
      const pinStatus = await SecureStore.getItemAsync('pinEnabled');
      setPinEnabled(pinStatus === 'true');
      
      // Biyometrik durumunu kontrol et
      const biometricStatus = await SecureStore.getItemAsync('biometricEnabled');
      setBiometricEnabled(biometricStatus === 'true');
      
      // Biyometrik uygunluğunu kontrol et
      await checkBiometricAvailability();
      
      // Eğer PIN aktif değilse, otomatik olarak kimlik doğrulanmış say
      if (pinStatus !== 'true') {
        setIsAuthenticated(true);
      }
      
    } catch (error) {
      console.error('Güvenlik başlatma hatası:', error);
      setIsAuthenticated(true); // Hata durumunda erişim ver
    } finally {
      setSecurityLoading(false);
    }
  };

  /**
   * Biyometrik uygunluk kontrolü
   */
  const checkBiometricAvailability = async () => {
    try {
      const isAvailable = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      const supportedTypes = await LocalAuthentication.supportedAuthenticationTypesAsync();
      
      setBiometricAvailable(isAvailable && isEnrolled);
      
      if (supportedTypes.includes(LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION)) {
        setBiometricType('face');
      } else if (supportedTypes.includes(LocalAuthentication.AuthenticationType.FINGERPRINT)) {
        setBiometricType('fingerprint');
      }
    } catch (error) {
      console.error('Biyometrik kontrol hatası:', error);
      setBiometricAvailable(false);
    }
  };

  /**
   * PIN doğrulama
   */
  const verifyPin = async (inputPin) => {
    try {
      const storedPinHash = await SecureStore.getItemAsync('pinHash');
      if (!storedPinHash) {
        return { success: false, error: 'PIN bulunamadı' };
      }
      
      const inputPinHash = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        inputPin + 'finansal-takip-salt'
      );
      
      const isValid = inputPinHash === storedPinHash;
      
      if (isValid) {
        setIsAuthenticated(true);
        // Başarılı giriş sayısını kaydet
        await recordSuccessfulLogin();
        return { success: true };
      } else {
        // Başarısız deneme sayısını artır
        await recordFailedAttempt();
        const lockStatus = await checkLockStatus();
        return { 
          success: false, 
          error: 'PIN hatalı',
          locked: lockStatus.locked,
          remainingTime: lockStatus.remainingTime
        };
      }
    } catch (error) {
      console.error('PIN doğrulama hatası:', error);
      return { success: false, error: 'PIN doğrulama hatası' };
    }
  };

  /**
   * Biyometrik doğrulama
   */
  const verifyBiometric = async () => {
    try {
      if (!biometricAvailable || !biometricEnabled) {
        return { success: false, error: 'Biyometrik güvenlik kullanılamıyor' };
      }
      
      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Kimliğinizi doğrulayın',
        cancelLabel: 'İptal',
        fallbackLabel: 'PIN Kullan',
        disableDeviceFallback: false,
      });
      
      if (result.success) {
        setIsAuthenticated(true);
        await recordSuccessfulLogin();
        return { success: true };
      }
      
      // Kullanıcı iptal durumları - hata mesajı gösterme
      const cancelErrors = [
        'UserCancel', 
        'UserFallback', 
        'SystemCancel',
        'user_cancel',
        'cancelled',
        'canceled'
      ];
      
      if (cancelErrors.some(cancelError => 
        result.error && result.error.toLowerCase().includes(cancelError.toLowerCase())
      )) {
        return { success: false, error: 'user_canceled', silent: true };
      }
      
      // Gerçek hatalar
      return { success: false, error: result.error || 'Biyometrik doğrulama başarısız' };
    } catch (error) {
      console.error('Biyometrik doğrulama hatası:', error);
      
      // Catch bloğunda da iptal kontrolü
      const errorMessage = error.message || error.toString();
      const cancelKeywords = ['cancel', 'dismiss', 'abort'];
      
      if (cancelKeywords.some(keyword => 
        errorMessage.toLowerCase().includes(keyword)
      )) {
        return { success: false, error: 'user_canceled', silent: true };
      }
      
      return { success: false, error: errorMessage || 'Biyometrik doğrulama hatası' };
    }
  };

  /**
   * PIN kurulum
   */
  const setupPin = async (pin) => {
    try {
      const pinHash = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        pin + 'finansal-takip-salt'
      );
      
      await SecureStore.setItemAsync('pinHash', pinHash);
      await SecureStore.setItemAsync('pinEnabled', 'true');
      setPinEnabled(true);
      
      return true;
    } catch (error) {
      console.error('PIN kurulum hatası:', error);
      return false;
    }
  };

  /**
   * PIN değiştir
   */
  const changePin = async (oldPin, newPin) => {
    try {
      // Önce eski PIN'i doğrula
      const isOldPinValid = await verifyPin(oldPin);
      if (!isOldPinValid) {
        return { success: false, error: 'Mevcut PIN yanlış' };
      }
      
      // Yeni PIN'i kaydet
      const success = await setupPin(newPin);
      return { success, error: success ? null : 'PIN değiştirilemedi' };
    } catch (error) {
      console.error('PIN değiştirme hatası:', error);
      return { success: false, error: 'PIN değiştirme hatası' };
    }
  };

  /**
   * PIN'i devre dışı bırak
   */
  const disablePin = async (pin) => {
    try {
      // PIN'i doğrula
      const isValid = await verifyPin(pin);
      if (!isValid) {
        return { success: false, error: 'PIN yanlış' };
      }
      
      await SecureStore.deleteItemAsync('pinHash');
      await SecureStore.deleteItemAsync('pinEnabled');
      setPinEnabled(false);
      setIsAuthenticated(true); // PIN kapatıldığında otomatik kimlik doğrula
      
      return { success: true };
    } catch (error) {
      console.error('PIN devre dışı bırakma hatası:', error);
      return { success: false, error: 'PIN devre dışı bırakılamadı' };
    }
  };

  /**
   * Biyometrik ayarları değiştir
   */
  const toggleBiometric = async (enable) => {
    try {
      if (enable && biometricAvailable) {
        await SecureStore.setItemAsync('biometricEnabled', 'true');
        setBiometricEnabled(true);
      } else {
        await SecureStore.deleteItemAsync('biometricEnabled');
        setBiometricEnabled(false);
      }
      return true;
    } catch (error) {
      console.error('Biyometrik ayar hatası:', error);
      return false;
    }
  };

  /**
   * Başarılı giriş kaydet
   */
  const recordSuccessfulLogin = async () => {
    try {
      const now = new Date().getTime();
      await AsyncStorage.setItem('lastSuccessfulLogin', now.toString());
      await AsyncStorage.removeItem('failedAttempts'); // Başarılı girişte başarısız denemeleri sıfırla
    } catch (error) {
      console.error('Başarılı giriş kaydetme hatası:', error);
    }
  };

  /**
   * Başarısız deneme kaydet
   */
  const recordFailedAttempt = async () => {
    try {
      const attempts = await AsyncStorage.getItem('failedAttempts');
      const newAttempts = attempts ? parseInt(attempts) + 1 : 1;
      await AsyncStorage.setItem('failedAttempts', newAttempts.toString());
      
      if (newAttempts >= 5) {
        // 5 başarısız denemeden sonra kilitle
        const lockUntil = new Date().getTime() + (30 * 60 * 1000); // 30 dakika
        await AsyncStorage.setItem('lockedUntil', lockUntil.toString());
      }
    } catch (error) {
      console.error('Başarısız deneme kaydetme hatası:', error);
    }
  };

  /**
   * Kilit durumunu kontrol et
   */
  const checkLockStatus = async () => {
    try {
      const lockedUntil = await AsyncStorage.getItem('lockedUntil');
      if (lockedUntil) {
        const lockTime = parseInt(lockedUntil);
        const now = new Date().getTime();
        
        if (now < lockTime) {
          return { locked: true, remainingTime: lockTime - now };
        } else {
          // Kilit süresi dolmuş, temizle
          await AsyncStorage.removeItem('lockedUntil');
          await AsyncStorage.removeItem('failedAttempts');
        }
      }
      
      return { locked: false, remainingTime: 0 };
    } catch (error) {
      console.error('Kilit durumu kontrol hatası:', error);
      return { locked: false, remainingTime: 0 };
    }
  };

  /**
   * Çıkış yap
   */
  const logout = () => {
    setIsAuthenticated(false);
  };

  /**
   * Güvenlik ayarlarını sıfırla
   */
  const resetSecurity = async () => {
    try {
      await SecureStore.deleteItemAsync('pinHash');
      await SecureStore.deleteItemAsync('pinEnabled');
      await SecureStore.deleteItemAsync('biometricEnabled');
      await AsyncStorage.removeItem('failedAttempts');
      await AsyncStorage.removeItem('lockedUntil');
      await AsyncStorage.removeItem('lastSuccessfulLogin');
      
      setPinEnabled(false);
      setBiometricEnabled(false);
      setIsAuthenticated(true);
      
      return true;
    } catch (error) {
      console.error('Güvenlik sıfırlama hatası:', error);
      return false;
    }
  };

  const value = {
    // Durum
    isAuthenticated,
    pinEnabled,
    biometricEnabled,
    biometricAvailable,
    biometricType,
    securityLoading,
    
    // Fonksiyonlar
    verifyPin,
    verifyBiometric,
    setupPin,
    changePin,
    disablePin,
    toggleBiometric,
    checkLockStatus,
    logout,
    resetSecurity,
    initializeSecurity,
  };

  return (
    <SecurityContext.Provider value={value}>
      {children}
    </SecurityContext.Provider>
  );
};

/**
 * Güvenlik context hook'u
 */
export const useSecurity = () => {
  const context = useContext(SecurityContext);
  if (!context) {
    throw new Error('useSecurity must be used within a SecurityProvider');
  }
  return context;
};
