/**
 * <PERSON>llanıc<PERSON> ayarları servisi
 * Kullanıcı ayarlarını yönetir
 */

/**
 * Kullanıcı ayarları tablosunun varlığını kontrol eder ve gerekirse oluşturur
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<boolean>} Başarılı mı?
 */
export const ensureSettingsTable = async (db) => {
  try {
    // Tablo var mı kontrol et
    const hasTable = await db.getFirstAsync(`
      SELECT name FROM sqlite_master WHERE type='table' AND name='user_settings'
    `);

    if (!hasTable) {
      console.log('user_settings tablosu bulunamadı, oluşturuluyor...');

      // Tabloyu oluştur
      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS user_settings (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          key TEXT NOT NULL UNIQUE,
          value TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Varsayılan değerleri ekle
      await db.execAsync(`
        INSERT OR IGNORE INTO user_settings (key, value)
        VALUES ('custom_currency', 'GBP')
      `);

      console.log('user_settings tablosu başarıyla oluşturuldu.');
    }

    return true;
  } catch (error) {
    console.error('Ayarlar tablosu oluşturma hatası:', error);
    return false;
  }
};

/**
 * Bir ayarın değerini getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {string} key - Ayar anahtarı
 * @param {any} defaultValue - Varsayılan değer
 * @returns {Promise<any>} Ayar değeri
 */
export const getSetting = async (db, key, defaultValue = null) => {
  try {
    // Önce tablonun varlığını kontrol et
    await ensureSettingsTable(db);

    const result = await db.getFirstAsync(`
      SELECT value FROM user_settings WHERE key = ?
    `, [key]);

    if (result) {
      try {
        // JSON olarak parse etmeyi dene
        return JSON.parse(result.value);
      } catch (e) {
        // JSON değilse string olarak döndür
        return result.value;
      }
    }

    return defaultValue;
  } catch (error) {
    console.error(`Ayar getirme hatası (${key}):`, error);
    return defaultValue;
  }
};

/**
 * Bir ayarın değerini kaydeder
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {string} key - Ayar anahtarı
 * @param {any} value - Ayar değeri
 * @returns {Promise<boolean>} Başarılı mı?
 */
export const saveSetting = async (db, key, value) => {
  try {
    // Önce tablonun varlığını kontrol et
    await ensureSettingsTable(db);

    // Değeri string'e çevir
    const stringValue = typeof value === 'object' ? JSON.stringify(value) : String(value);

    // Ayarı kaydet
    await db.runAsync(`
      INSERT OR REPLACE INTO user_settings (key, value, updated_at)
      VALUES (?, ?, CURRENT_TIMESTAMP)
    `, [key, stringValue]);

    return true;
  } catch (error) {
    console.error(`Ayar kaydetme hatası (${key}):`, error);
    return false;
  }
};

/**
 * Özel para birimini getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<string>} Özel para birimi
 */
export const getCustomCurrency = async (db) => {
  try {
    // Önce tablonun varlığını kontrol et
    await ensureSettingsTable(db);
    return await getSetting(db, 'custom_currency', 'GBP');
  } catch (error) {
    console.error('Özel para birimi getirme hatası:', error);
    return 'GBP'; // Hata durumunda varsayılan değeri döndür
  }
};

/**
 * Özel para birimini kaydeder
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {string} currency - Para birimi
 * @returns {Promise<boolean>} Başarılı mı?
 */
export const saveCustomCurrency = async (db, currency) => {
  try {
    // Önce tablonun varlığını kontrol et
    await ensureSettingsTable(db);
    return await saveSetting(db, 'custom_currency', currency);
  } catch (error) {
    console.error('Özel para birimi kaydetme hatası:', error);
    return false;
  }
};

/**
 * Tüm ayarları getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<Object>} Ayarlar
 */
export const getAllSettings = async (db) => {
  try {
    // Önce tablonun varlığını kontrol et
    await ensureSettingsTable(db);

    const results = await db.getAllAsync(`
      SELECT key, value FROM user_settings
    `);

    const settings = {};

    for (const row of results) {
      try {
        // JSON olarak parse etmeyi dene
        settings[row.key] = JSON.parse(row.value);
      } catch (e) {
        // JSON değilse string olarak ekle
        settings[row.key] = row.value;
      }
    }

    return settings;
  } catch (error) {
    console.error('Tüm ayarları getirme hatası:', error);
    return {};
  }
};
