/**
 * Bütçe uyarıları için servis fonksiyonları
 */
import * as budgetService from './budgetService';
import { formatCurrency } from '../utils/formatters';

/**
 * Bir bütçenin uyarılarını getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} budgetId - Bütçe ID
 * @returns {Promise<Array>} Bütçe uyarıları listesi
 */
export const getBudgetAlerts = async (db, budgetId) => {
  try {
    const alerts = await db.getAllAsync(`
      SELECT *
      FROM budget_alerts
      WHERE budget_id = ?
      ORDER BY threshold_value DESC
    `, [budgetId]);

    return alerts;
  } catch (error) {
    console.error('Bütçe uyarılarını getirme hatası:', error);
    throw error;
  }
};

/**
 * Bir bütçe uyarısını getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} alertId - Uyarı ID
 * @returns {Promise<Object|null>} Bütçe uyarısı
 */
export const getBudgetAlertById = async (db, alertId) => {
  try {
    const alert = await db.getFirstAsync(`
      SELECT ba.*, b.name as budget_name
      FROM budget_alerts ba
      JOIN budgets b ON ba.budget_id = b.id
      WHERE ba.id = ?
    `, [alertId]);

    return alert;
  } catch (error) {
    console.error('Bütçe uyarısı getirme hatası:', error);
    throw error;
  }
};

/**
 * Yeni bir bütçe uyarısı ekler
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} alert - Uyarı verileri
 * @param {number} alert.budget_id - Bütçe ID
 * @param {string} alert.threshold_type - Eşik tipi ('percentage' veya 'amount')
 * @param {number} alert.threshold_value - Eşik değeri
 * @param {number} alert.is_active - Etkin mi
 * @param {string} alert.notification_type - Bildirim tipi ('once' veya 'recurring')
 * @param {string} alert.notification_message - Bildirim mesajı
 * @returns {Promise<number>} Eklenen uyarının ID'si
 */
export const createBudgetAlert = async (db, alert) => {
  try {
    const result = await db.runAsync(`
      INSERT INTO budget_alerts (
        budget_id, threshold_type, threshold_value,
        is_active, notification_type, notification_message,
        created_at, updated_at
      )
      VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `, [
      alert.budget_id,
      alert.threshold_type,
      alert.threshold_value,
      alert.is_active,
      alert.notification_type,
      alert.notification_message
    ]);

    return result.lastInsertRowId;
  } catch (error) {
    console.error('Bütçe uyarısı ekleme hatası:', error);
    throw error;
  }
};

/**
 * Bir bütçe uyarısını günceller
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} alertId - Uyarı ID
 * @param {Object} alert - Güncellenecek uyarı verileri
 * @returns {Promise<boolean>} Başarılı ise true
 */
export const updateBudgetAlert = async (db, alertId, alert) => {
  try {
    await db.runAsync(`
      UPDATE budget_alerts
      SET budget_id = ?,
          threshold_type = ?,
          threshold_value = ?,
          is_active = ?,
          notification_type = ?,
          notification_message = ?,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [
      alert.budget_id,
      alert.threshold_type,
      alert.threshold_value,
      alert.is_active,
      alert.notification_type,
      alert.notification_message,
      alertId
    ]);

    return true;
  } catch (error) {
    console.error('Bütçe uyarısı güncelleme hatası:', error);
    throw error;
  }
};

/**
 * Bir bütçe uyarısını siler
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} alertId - Uyarı ID
 * @returns {Promise<boolean>} Başarılı ise true
 */
export const deleteBudgetAlert = async (db, alertId) => {
  try {
    await db.runAsync(`
      DELETE FROM budget_alerts
      WHERE id = ?
    `, [alertId]);

    return true;
  } catch (error) {
    console.error('Bütçe uyarısı silme hatası:', error);
    throw error;
  }
};

/**
 * Bir bütçe uyarısını sıfırlar (tetiklenmemiş duruma getirir)
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} alertId - Uyarı ID
 * @returns {Promise<boolean>} Başarılı ise true
 */
export const resetBudgetAlert = async (db, alertId) => {
  try {
    await db.runAsync(`
      UPDATE budget_alerts
      SET is_triggered = 0, last_triggered_at = NULL, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [alertId]);

    return true;
  } catch (error) {
    console.error('Bütçe uyarısı sıfırlama hatası:', error);
    throw error;
  }
};

/**
 * Tüm bütçe uyarılarını kontrol eder ve gerekirse tetikler
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Function} notificationCallback - Bildirim gönderme fonksiyonu
 * @returns {Promise<Array>} Tetiklenen uyarılar listesi
 */
export const checkBudgetAlerts = async (db, notificationCallback) => {
  try {
    // Aktif bütçeleri getir
    const activeBudgets = await budgetService.getActiveBudgets(db);
    const triggeredAlerts = [];

    for (const budget of activeBudgets) {
      // Bütçe uyarılarını getir
      const alerts = await getBudgetAlerts(db, budget.id);

      // Bütçe kullanımını hesapla
      const usage = budget.usage;
      const usagePercent = usage.percent;

      // Her uyarı için kontrol et
      for (const alert of alerts) {
        if (!alert.is_active || alert.is_triggered) continue;

        let isTriggered = false;

        // Eşik değerini aştıysa uyarıyı tetikle
        if (alert.threshold_type === 'percentage') {
          isTriggered = usagePercent >= alert.threshold_value;
        } else if (alert.threshold_type === 'amount') {
          isTriggered = usage.total_spent >= alert.threshold_value;
        }

        if (isTriggered) {
          // Uyarıyı tetiklenmiş olarak işaretle
          await db.runAsync(`
            UPDATE budget_alerts
            SET is_triggered = 1, last_triggered_at = CURRENT_TIMESTAMP
            WHERE id = ?
          `, [alert.id]);

          // Bildirim gönder
          if (notificationCallback) {
            const message = alert.notification_message ||
              `"${budget.name}" bütçeniz ${alert.threshold_type === 'percentage' ?
                `%${alert.threshold_value}` :
                formatCurrency(alert.threshold_value, budget.currency || 'TRY')}
              kullanım sınırına ulaştı.`;

            const notification = {
              title: 'Bütçe Uyarısı',
              message,
              type: 'budget_alert',
              priority: 'high',
              data: {
                budgetId: budget.id,
                alertId: alert.id,
                usagePercent
              }
            };

            await notificationCallback(notification);
          }

          triggeredAlerts.push({
            budget_id: budget.id,
            budget_name: budget.name,
            alert_id: alert.id,
            threshold_type: alert.threshold_type,
            threshold_value: alert.threshold_value,
            usage_percent: usagePercent,
            usage_amount: usage.total_spent
          });
        }
      }
    }

    return triggeredAlerts;
  } catch (error) {
    console.error('Bütçe uyarıları kontrol hatası:', error);
    throw error;
  }
};
