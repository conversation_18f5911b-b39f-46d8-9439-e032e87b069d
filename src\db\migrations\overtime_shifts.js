/**
 * Overtime Shifts Migration
 * <PERSON>i var<PERSON>arı için tablo oluşturma
 */

export const createOvertimeShiftsTable = async (db) => {
  try {
    console.log('🕐 Overtime shifts tablosu oluşturuluyor...');
    
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS overtime_shifts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER DEFAULT 1,
        shift_date TEXT NOT NULL,
        start_time TEXT NOT NULL,
        end_time TEXT NOT NULL,
        break_duration INTEGER DEFAULT 0,
        shift_type TEXT NOT NULL DEFAULT 'overtime',
        hourly_rate DECIMAL(10,2) NOT NULL DEFAULT 0,
        overtime_multiplier DECIMAL(3,1) DEFAULT 1.5,
        hours_worked DECIMAL(10,2) NOT NULL DEFAULT 0,
        regular_hours DECIMAL(10,2) DEFAULT 0,
        overtime_hours DECIMAL(10,2) DEFAULT 0,
        total_payment DECIMAL(10,2) NOT NULL DEFAULT 0,
        status TEXT DEFAULT 'completed',
        notes TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    console.log('✅ Overtime shifts tablosu başarıyla oluşturuldu');
    
    // Örnek veri ekle
    await seedOvertimeShifts(db);
    
  } catch (error) {
    console.error('❌ Overtime shifts tablosu oluşturulurken hata:', error);
    throw error;
  }
};

/**
 * Örnek mesai verilerini ekle
 */
export const seedOvertimeShifts = async (db) => {
  try {
    console.log('📊 Örnek mesai verileri ekleniyor...');
    
    const existingShifts = await db.getFirstAsync(
      'SELECT COUNT(*) as count FROM overtime_shifts'
    );
    
    if (existingShifts.count > 0) {
      console.log('Mesai verileri zaten mevcut');
      return;
    }
    
    const sampleShifts = [
      {
        shift_date: '2024-12-01',
        start_time: '18:00',
        end_time: '22:00',
        shift_type: 'evening_overtime',
        hourly_rate: 150.00,
        hours_worked: 4.0,
        overtime_hours: 4.0,
        total_payment: 900.00,
        notes: 'Akşam mesaisi'
      },
      {
        shift_date: '2024-12-05',
        start_time: '08:00',
        end_time: '16:00',
        shift_type: 'weekend_overtime',
        hourly_rate: 120.00,
        overtime_multiplier: 2.0,
        hours_worked: 8.0,
        overtime_hours: 8.0,
        total_payment: 1920.00,
        notes: 'Hafta sonu mesaisi'
      },
      {
        shift_date: '2024-12-10',
        start_time: '17:00',
        end_time: '21:00',
        shift_type: 'evening_overtime',
        hourly_rate: 150.00,
        hours_worked: 4.0,
        overtime_hours: 4.0,
        total_payment: 900.00,
        notes: 'Proje teslimi için ek mesai'
      },
      {
        shift_date: '2024-12-15',
        start_time: '09:00',
        end_time: '17:00',
        shift_type: 'holiday_overtime',
        hourly_rate: 120.00,
        overtime_multiplier: 2.5,
        hours_worked: 8.0,
        overtime_hours: 8.0,
        total_payment: 2400.00,
        notes: 'Tatil günü mesaisi'
      },
      {
        shift_date: '2024-12-20',
        start_time: '18:30',
        end_time: '22:30',
        shift_type: 'evening_overtime',
        hourly_rate: 150.00,
        hours_worked: 4.0,
        overtime_hours: 4.0,
        total_payment: 900.00,
        notes: 'Yıl sonu yoğunluğu'
      }
    ];
    
    for (const shift of sampleShifts) {
      await db.runAsync(`
        INSERT INTO overtime_shifts (
          shift_date, start_time, end_time, shift_type, hourly_rate,
          overtime_multiplier, hours_worked, overtime_hours, total_payment, notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        shift.shift_date,
        shift.start_time,
        shift.end_time,
        shift.shift_type,
        shift.hourly_rate,
        shift.overtime_multiplier || 1.5,
        shift.hours_worked,
        shift.overtime_hours,
        shift.total_payment,
        shift.notes
      ]);
    }
    
    console.log('✅ Örnek mesai verileri başarıyla eklendi');
    
  } catch (error) {
    console.error('❌ Mesai verileri eklenirken hata:', error);
    throw error;
  }
};
