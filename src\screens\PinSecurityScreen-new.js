import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Animated,
  Vibration,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { useAppContext } from '../context/AppContext';
import { useSecurity } from '../context/SecurityContext';

/**
 * PIN ve Biyometrik Güvenlik Ekranı
 * SecurityContext entegrasyonu ile güncellenmiş PIN doğrulama ekranı
 */
export default function PinSecurityScreen({ navigation, route }) {
  const insets = useSafeAreaInsets();
  const { theme } = useAppContext();
  const {
    isAuthenticated,
    pinEnabled,
    biometricEnabled,
    biometricAvailable,
    biometricType,
    securityLoading,
    verifyPin,
    verifyBiometric,
    setupPin,
    changePin,
    checkLockStatus,
  } = useSecurity();
  
  // Route parametreleri
  const { mode = 'verify' } = route.params || {}; // 'setup', 'verify', 'change'
  
  const [pin, setPin] = useState('');
  const [confirmPin, setConfirmPin] = useState('');
  const [step, setStep] = useState(mode === 'setup' ? 'setup' : 'verify');
  const [isLocked, setIsLocked] = useState(false);
  const [lockTimeRemaining, setLockTimeRemaining] = useState(0);
  const [showBiometric, setShowBiometric] = useState(false);
  
  // Animasyonlar
  const [shakeAnimation] = useState(new Animated.Value(0));

  // Başlangıç kontrolleri
  useEffect(() => {
    initializeScreen();
  }, []);
  
  // Kilitleme zamanlayıcısı
  useEffect(() => {
    let interval;
    if (isLocked && lockTimeRemaining > 0) {
      interval = setInterval(() => {
        setLockTimeRemaining(prev => {
          if (prev <= 1) {
            setIsLocked(false);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isLocked, lockTimeRemaining]);

  // Kimlik doğrulama başarılı olduğunda main ekrana yönlendir
  useEffect(() => {
    if (isAuthenticated && mode === 'verify') {
      navigation.replace('Main');
    }
  }, [isAuthenticated, mode, navigation]);

  /**
   * Ekranı başlat ve gerekli kontrolleri yap
   */
  const initializeScreen = async () => {
    try {
      // Kilitleme durumunu kontrol et
      const lockStatus = await checkLockStatus();
      if (lockStatus.locked) {
        setIsLocked(true);
        setLockTimeRemaining(Math.ceil(lockStatus.remainingTime / 1000));
      }
      
      // Biyometrik doğrulama sadece verify modunda otomatik göster
      if (mode === 'verify' && biometricEnabled && biometricAvailable && !lockStatus.locked) {
        setShowBiometric(true);
      }
    } catch (error) {
      console.error('Ekran başlatma hatası:', error);
    }
  };

  /**
   * PIN rakam ekleme
   */
  const addDigit = (digit) => {
    if (isLocked) return;
    
    if (step === 'verify' || step === 'setup') {
      if (pin.length < 6) {
        setPin(pin + digit);
      }
    } else if (step === 'confirm') {
      if (confirmPin.length < 6) {
        setConfirmPin(confirmPin + digit);
      }
    }
  };

  /**
   * Son rakamı silme
   */
  const removeDigit = () => {
    if (isLocked) return;
    
    if (step === 'verify' || step === 'setup') {
      setPin(pin.slice(0, -1));
    } else if (step === 'confirm') {
      setConfirmPin(confirmPin.slice(0, -1));
    }
  };

  /**
   * PIN onaylama
   */
  const handlePinSubmit = async () => {
    if (isLocked) return;
    
    try {
      if (step === 'verify') {
        // PIN doğrulama
        if (pin.length !== 6) return;
        
        const result = await verifyPin(pin);
        if (result.success) {
          // Başarılı - main ekrana yönlendir
          navigation.replace('Main');
        } else {
          // Başarısız - hata göster
          playShakeAnimation();
          setPin('');
          
          if (result.locked) {
            setIsLocked(true);
            setLockTimeRemaining(Math.ceil(result.remainingTime / 1000));
            Alert.alert('Hesap Kilitlendi', `Çok fazla yanlış deneme. ${Math.ceil(result.remainingTime / 60)} dakika sonra tekrar deneyin.`);
          } else {
            Alert.alert('Yanlış PIN', result.error || 'Girdiğiniz PIN yanlış.');
          }
        }
      } else if (step === 'setup') {
        // PIN kurulumu
        if (pin.length !== 6) return;
        setStep('confirm');
      } else if (step === 'confirm') {
        // PIN onaylama
        if (confirmPin.length !== 6) return;
        
        if (pin !== confirmPin) {
          Alert.alert('PIN Uyuşmadı', 'Girdiğiniz PIN\'ler uyuşmuyor.');
          playShakeAnimation();
          setConfirmPin('');
          return;
        }
        
        const result = await setupPin(pin);
        if (result.success) {
          Alert.alert(
            'PIN Kuruldu',
            'PIN başarıyla kuruldu.',
            [
              {
                text: 'Tamam',
                onPress: () => navigation.replace('Main')
              }
            ]
          );
        } else {
          Alert.alert('Hata', result.error || 'PIN kurulurken bir hata oluştu.');
        }
      }
    } catch (error) {
      console.error('PIN işlem hatası:', error);
      Alert.alert('Hata', 'Bir hata oluştu. Lütfen tekrar deneyin.');
    }
  };

  /**
   * Biyometrik doğrulama
   */
  const handleBiometricAuth = async () => {
    try {
      const result = await verifyBiometric();
      if (result.success) {
        navigation.replace('Main');
      } else {
        Alert.alert('Biyometrik Doğrulama Başarısız', result.error || 'Doğrulama başarısız.');
      }
    } catch (error) {
      console.error('Biyometrik doğrulama hatası:', error);
    }
  };

  /**
   * Sallama animasyonu
   */
  const playShakeAnimation = () => {
    Vibration.vibrate(500);
    Animated.sequence([
      Animated.timing(shakeAnimation, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: -10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 0, duration: 50, useNativeDriver: true }),
    ]).start();
  };

  /**
   * Adım başlığını belirle
   */
  const getStepTitle = () => {
    switch (step) {
      case 'verify':
        return 'PIN Girişi';
      case 'setup':
        return 'PIN Oluşturun';
      case 'confirm':
        return 'PIN\'i Onaylayın';
      default:
        return 'Güvenlik';
    }
  };

  /**
   * Adım açıklamasını belirle
   */
  const getStepDescription = () => {
    switch (step) {
      case 'verify':
        return 'Devam etmek için PIN\'inizi girin';
      case 'setup':
        return '6 haneli güvenli bir PIN oluşturun';
      case 'confirm':
        return 'Oluşturduğunuz PIN\'i tekrar girin';
      default:
        return '';
    }
  };

  /**
   * PIN noktalarını render et
   */
  const renderPinDots = () => {
    const currentPin = step === 'confirm' ? confirmPin : pin;
    const maxLength = 6;
    
    return (
      <Animated.View 
        style={[
          styles.pinContainer,
          { backgroundColor: theme.colors.surface, transform: [{ translateX: shakeAnimation }] }
        ]}
      >
        {[...Array(maxLength)].map((_, index) => (
          <View
            key={index}
            style={[
              styles.pinDot,
              { 
                backgroundColor: index < currentPin.length ? theme.colors.primary : theme.colors.border,
                borderColor: theme.colors.border
              }
            ]}
          />
        ))}
      </Animated.View>
    );
  };

  /**
   * Sayısal tuş takımı
   */
  const renderNumPad = () => {
    return (
      <View style={styles.numPad}>
        {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((digit) => (
          <TouchableOpacity
            key={digit}
            style={[styles.numButton, { backgroundColor: theme.colors.surface }]}
            onPress={() => addDigit(digit.toString())}
            disabled={isLocked}
          >
            <Text style={[styles.numButtonText, { color: theme.colors.text }]}>
              {digit}
            </Text>
          </TouchableOpacity>
        ))}
        
        <TouchableOpacity
          style={[styles.numButton, { backgroundColor: theme.colors.surface }]}
          onPress={() => {/* Boş alan */}}
          disabled
        />
        
        <TouchableOpacity
          style={[styles.numButton, { backgroundColor: theme.colors.surface }]}
          onPress={() => addDigit('0')}
          disabled={isLocked}
        >
          <Text style={[styles.numButtonText, { color: theme.colors.text }]}>
            0
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.numButton, { backgroundColor: theme.colors.surface }]}
          onPress={removeDigit}
          disabled={isLocked}
        >
          <MaterialIcons name="backspace" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>
    );
  };

  // Otomatik PIN submit (6 rakam tamamlandığında)
  useEffect(() => {
    const currentPin = step === 'confirm' ? confirmPin : pin;
    if (currentPin.length === 6 && !isLocked) {
      setTimeout(() => {
        handlePinSubmit();
      }, 100);
    }
  }, [pin, confirmPin, step, isLocked]);

  if (securityLoading) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.content}>
          <Text style={[styles.title, { color: theme.colors.text }]}>
            Yükleniyor...
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background, paddingTop: insets.top }]}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={styles.backButton}
        >
          <MaterialIcons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      {/* Content */}
      <View style={styles.content}>
        <View style={styles.titleContainer}>
          <MaterialIcons name="security" size={64} color={theme.colors.primary} />
          <Text style={[styles.title, { color: theme.colors.text }]}>
            {getStepTitle()}
          </Text>
          <Text style={[styles.subtitle, { color: theme.colors.textSecondary }]}>
            {getStepDescription()}
          </Text>
        </View>

        {/* Kilitleme uyarısı */}
        {isLocked && (
          <View style={[styles.lockWarning, { backgroundColor: theme.colors.danger + '20' }]}>
            <MaterialIcons name="lock" size={24} color={theme.colors.danger} />
            <Text style={[styles.lockText, { color: theme.colors.danger }]}>
              {Math.floor(lockTimeRemaining / 60)}:{(lockTimeRemaining % 60).toString().padStart(2, '0')} kaldı
            </Text>
          </View>
        )}

        {/* PIN Dots */}
        {renderPinDots()}

        {/* Biyometrik buton */}
        {showBiometric && step === 'verify' && !isLocked && (
          <TouchableOpacity
            style={[styles.biometricButton, { backgroundColor: theme.colors.primary }]}
            onPress={handleBiometricAuth}
          >
            <MaterialIcons 
              name={biometricType === 'face' ? 'face' : 'fingerprint'} 
              size={24} 
              color={theme.colors.white} 
            />
            <Text style={[styles.biometricText, { color: theme.colors.white }]}>
              {biometricType === 'face' ? 'Yüz Tanıma' : 'Parmak İzi'}
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {/* NumPad */}
      {renderNumPad()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  backButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  titleContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginTop: 24,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
  lockWarning: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 24,
    gap: 8,
  },
  lockText: {
    fontSize: 16,
    fontWeight: '600',
  },
  pinContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 16,
    paddingVertical: 24,
    paddingHorizontal: 32,
    borderRadius: 16,
    marginBottom: 40,
  },
  pinDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    borderWidth: 2,
  },
  biometricButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 24,
    gap: 8,
    marginBottom: 20,
  },
  biometricText: {
    fontSize: 16,
    fontWeight: '600',
  },
  numPad: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    paddingHorizontal: 40,
    marginBottom: 32,
  },
  numButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  numButtonText: {
    fontSize: 24,
    fontWeight: '600',
  },
});
