/**
 * Düşük Bütçe Bildirimi Komponenti
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md - Stage 3, Phase 3
 * 
 * Bütçe azaldığında uyarı bildirimi
 * Maksimum 150 satır - Tek sorumluluk prensibi
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../../../context/ThemeContext';
import { formatTRY, formatUSD, formatEUR } from '../../../utils/formatters';

/**
 * Düşük bütçe bildirimi komponenti
 * @param {Object} props - Component props
 * @param {number} props.remainingAmount - <PERSON><PERSON> miktar
 * @param {number} props.warningThreshold - Uyarı eşiği (yüzde)
 * @param {string} props.currency - Para birimi ('TRY', 'USD', 'EUR')
 * @param {number} props.daysLeft - <PERSON><PERSON> gün sayı<PERSON>
 * @param {string} props.categoryName - <PERSON><PERSON><PERSON> adı (opsiyonel)
 * @param {Function} props.onDismiss - Bildirimi kapatma callback
 * @param {Function} props.onViewBudget - Bütçe görüntüleme callback
 * @param {Object} props.theme - Tema objesi (opsiyonel, context'ten alınır)
 */
const LowBudgetNotification = ({ 
  remainingAmount = 0,
  warningThreshold = 20,
  currency = 'TRY',
  daysLeft = 0,
  categoryName,
  onDismiss,
  onViewBudget,
  theme: propTheme 
}) => {
  const { theme, isDarkMode } = useTheme();
  const currentTheme = propTheme || theme;

  /**
   * Para birimi formatı
   * @param {number} value - Formatlanacak değer
   * @returns {string} Formatlanmış değer
   */
  const formatCurrency = (value) => {
    switch (currency) {
      case 'USD':
        return formatUSD(value);
      case 'EUR':
        return formatEUR(value);
      default:
        return formatTRY(value);
    }
  };

  /**
   * Para birimi sembolü
   * @returns {string} Para birimi sembolü
   */
  const getCurrencySymbol = () => {
    switch (currency) {
      case 'USD':
        return '$';
      case 'EUR':
        return '€';
      default:
        return '₺';
    }
  };

  /**
   * Günlük önerilen harcama hesaplama
   * @returns {number} Günlük önerilen harcama
   */
  const getDailyRecommendation = () => {
    if (daysLeft <= 0) return 0;
    return remainingAmount / daysLeft;
  };

  /**
   * Uyarı seviyesi belirleme
   * @returns {string} Uyarı seviyesi ('low', 'critical')
   */
  const getWarningLevel = () => {
    if (remainingAmount <= 0) return 'critical';
    if (warningThreshold <= 10) return 'critical';
    return 'low';
  };

  const currencySymbol = getCurrencySymbol();
  const dailyRecommendation = getDailyRecommendation();
  const warningLevel = getWarningLevel();
  const warningColor = warningLevel === 'critical' ? currentTheme.ERROR : currentTheme.WARNING;

  return (
    <View style={[
      styles.container,
      {
        backgroundColor: warningColor + '10',
        borderColor: warningColor,
      }
    ]}>
      {/* Header */}
      <View style={styles.header}>
        <View style={[styles.warningIcon, { backgroundColor: warningColor }]}>
          <MaterialIcons 
            name={warningLevel === 'critical' ? 'error' : 'warning'} 
            size={20} 
            color={currentTheme.WHITE} 
          />
        </View>
        
        <View style={styles.headerText}>
          <Text style={[styles.title, { color: warningColor }]}>
            {warningLevel === 'critical' ? 'Kritik Durum!' : 'Dikkat!'}
          </Text>
          <Text style={[styles.subtitle, { color: currentTheme.TEXT_SECONDARY }]}>
            {categoryName ? `${categoryName} kategorisinde` : 'Toplam bütçede'} az miktar kaldı
          </Text>
        </View>

        {onDismiss && (
          <TouchableOpacity
            style={styles.dismissButton}
            onPress={onDismiss}
          >
            <MaterialIcons name="close" size={20} color={currentTheme.TEXT_SECONDARY} />
          </TouchableOpacity>
        )}
      </View>

      {/* Kalan miktar bilgisi */}
      <View style={styles.amountInfo}>
        <View style={styles.remainingAmount}>
          <Text style={[styles.amountLabel, { color: currentTheme.TEXT_SECONDARY }]}>
            Kalan Miktar:
          </Text>
          <View style={styles.amountDisplay}>
            <Text style={[styles.currencySymbol, { color: warningColor }]}>
              {currencySymbol}
            </Text>
            <Text style={[styles.amount, { color: warningColor }]}>
              {remainingAmount.toLocaleString('tr-TR')}
            </Text>
          </View>
        </View>

        {daysLeft > 0 && (
          <View style={styles.dailyRecommendation}>
            <Text style={[styles.recommendationLabel, { color: currentTheme.TEXT_SECONDARY }]}>
              Günlük önerilen:
            </Text>
            <Text style={[styles.recommendationAmount, { color: currentTheme.PRIMARY }]}>
              {currencySymbol}{dailyRecommendation.toLocaleString('tr-TR', { maximumFractionDigits: 0 })}
            </Text>
          </View>
        )}
      </View>

      {/* Aksiyon butonu */}
      {onViewBudget && (
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: warningColor }]}
          onPress={onViewBudget}
        >
          <MaterialIcons name="visibility" size={16} color={currentTheme.WHITE} />
          <Text style={[styles.actionText, { color: currentTheme.WHITE }]}>
            Bütçeyi Görüntüle
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    borderWidth: 1,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 12,
  },
  warningIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerText: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  subtitle: {
    fontSize: 13,
  },
  dismissButton: {
    padding: 4,
  },
  amountInfo: {
    marginBottom: 12,
  },
  remainingAmount: {
    alignItems: 'center',
    marginBottom: 8,
  },
  amountLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  amountDisplay: {
    flexDirection: 'row',
    alignItems: 'baseline',
    gap: 2,
  },
  currencySymbol: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  amount: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  dailyRecommendation: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  recommendationLabel: {
    fontSize: 12,
  },
  recommendationAmount: {
    fontSize: 14,
    fontWeight: '500',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 6,
  },
  actionText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default LowBudgetNotification;
