import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { ContributionGraph } from 'react-native-chart-kit';

const screenWidth = Dimensions.get('window').width;

/**
 * Contribution Graph (Heatmap) component using react-native-chart-kit
 * @param {Object} props - Component props
 * @param {Array} props.data - Chart data
 * @param {Object} props.theme - Theme object
 * @param {string} props.title - Chart title
 * @param {Object} props.config - Chart configuration
 * @returns {JSX.Element} Contribution graph component
 */
const ContributionChart = ({ data, theme, title, config = {} }) => {
  /**
   * Get safe theme value
   * @param {string} property - Theme property
   * @param {string} fallback - Fallback value
   * @returns {string} Safe theme value
   */
  const getSafeThemeValue = (property, fallback) => {
    return theme?.[property] || theme?.colors?.[property.toLowerCase()] || fallback;
  };

  const chartConfig = {
    backgroundColor: getSafeThemeValue('BACKGROUND', '#ffffff'),
    backgroundGradientFrom: getSafeThemeValue('BACKGROUND', '#ffffff'),
    backgroundGradientTo: getSafeThemeValue('BACKGROUND', '#ffffff'),
    decimalPlaces: config.decimalPlaces || 2,
    color: (opacity = 1) => getSafeThemeValue('PRIMARY', '#007AFF').replace(')', `, ${opacity})`).replace('#', 'rgba(').replace(/^rgba\((\d+), (\d+), (\d+)/, 'rgba($1, $2, $3'),
    labelColor: (opacity = 1) => getSafeThemeValue('TEXT_PRIMARY', '#333333').replace(')', `, ${opacity})`).replace('#', 'rgba(').replace(/^rgba\((\d+), (\d+), (\d+)/, 'rgba($1, $2, $3'),
    style: {
      borderRadius: 16,
    },
    ...config,
  };

  // Generate sample data for contribution graph
  const generateSampleData = () => {
    const commitsData = [];
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - 12);
    
    for (let i = 0; i < 365; i++) {
      const date = new Date(startDate);
      date.setDate(date.getDate() + i);
      commitsData.push({
        date: date.toISOString().split('T')[0],
        count: Math.floor(Math.random() * 5),
      });
    }
    
    return commitsData;
  };

  const defaultData = data || generateSampleData();

  return (
    <View style={[styles.container, { backgroundColor: getSafeThemeValue('BACKGROUND', '#ffffff') }]}>
      {title && (
        <Text style={[styles.title, { color: getSafeThemeValue('TEXT_PRIMARY', '#333333') }]}>
          {title}
        </Text>
      )}
      <ContributionGraph
        values={defaultData}
        endDate={new Date()}
        numDays={105}
        width={screenWidth - 32}
        height={220}
        chartConfig={chartConfig}
        style={styles.chart}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    borderRadius: 8,
    marginVertical: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
    textAlign: 'center',
  },
  chart: {
    borderRadius: 8,
  },
});

export default ContributionChart;
