import React, { useState, useCallback } from 'react';
import { View, Text, StyleSheet, Alert, ActivityIndicator } from 'react-native';
import { useTheme } from '../../../context/ThemeContext';
import { globalStyles } from '../../../design-system/globalStyles';

// Debug logging
console.log('🔍 ExportManager.js: Module loading started');

import { PDFGenerator } from './PDFGenerator';
import { ExcelExporter } from './ExcelExporter';
import { PowerPointExporter } from './PowerPointExporter';
import { ImageExporter } from './ImageExporter';
import { EmailExporter } from './EmailExporter';

console.log('🔍 ExportManager.js: All imports loaded successfully');

/**
 * Export Manager - Static methods for report export functionality
 */
export class ExportManager {
  constructor() {
    console.log('🔍 ExportManager: Class instantiated');
  }
  /**
   * Export a report to specified format
   * @param {string} type - Export type (pdf, excel, powerpoint, image, email)
   * @param {Object} data - Export data
   * @returns {Promise<Object>} Export result
   */
  static async exportReport(type, data) {
    console.log('🔍 ExportManager.exportReport called with type:', type);
    try {
      switch (type.toLowerCase()) {
        case 'pdf':
          return await PDFGenerator.generatePDF(data);
        case 'excel':
          return await ExcelExporter.exportToExcel(data);
        case 'powerpoint':
          return await PowerPointExporter.exportToPowerPoint(data);
        case 'image':
          return await ImageExporter.exportToImage(data);
        case 'email':
          return await EmailExporter.sendEmail(data);
        default:
          throw new Error(`Unsupported export type: ${type}`);
      }
    } catch (error) {
      console.error('Export error:', error);
      throw error;
    }
  }

  /**
   * Get supported export types
   * @returns {Array<string>} Supported export types
   */
  static getSupportedTypes() {
    return ['pdf', 'excel', 'powerpoint', 'image', 'email'];
  }

  /**
   * Validate export data
   * @param {Object} data - Export data
   * @returns {boolean} Is valid
   */
  static validateExportData(data) {
    return data && typeof data === 'object' && data.title;
  }
}

/**
 * Export Manager Component - UI component for export operations
 * 
 * @param {Object} props - Component props
 * @param {Object} props.reportData - Dışa aktarılacak rapor verisi
 * @param {string} props.reportTitle - Rapor başlığı
 * @param {string} props.reportType - Rapor türü
 * @param {Object} props.exportConfig - Export yapılandırması
 * @returns {JSX.Element} Export Manager component
 */
export const ExportManagerComponent = ({ 
  reportData, 
  reportTitle, 
  reportType, 
  exportConfig = {} 
}) => {
  const { theme } = useTheme();
  const [isExporting, setIsExporting] = useState(false);
  const [exportStatus, setExportStatus] = useState(null);

  // PDF dışa aktarma
  const handlePDFExport = useCallback(async () => {
    if (!reportData) {
      Alert.alert('Hata', 'Dışa aktarılacak veri bulunamadı.');
      return;
    }

    setIsExporting(true);
    setExportStatus('PDF oluşturuluyor...');

    try {
      const result = await ExportManager.exportReport('pdf', {
        data: reportData,
        title: reportTitle,
        type: reportType,
        config: exportConfig.pdf
      });

      if (result.success) {
        Alert.alert('Başarılı', 'PDF başarıyla oluşturuldu.', [
          { text: 'Tamam', onPress: () => setExportStatus(null) }
        ]);
      } else {
        throw new Error(result.error || 'PDF oluşturulurken hata oluştu');
      }
    } catch (error) {
      Alert.alert('Hata', `PDF oluşturulurken hata: ${error.message}`);
      setExportStatus(null);
    } finally {
      setIsExporting(false);
    }
  }, [reportData, reportTitle, reportType, exportConfig]);

  // Excel dışa aktarma
  const handleExcelExport = useCallback(async () => {
    if (!reportData) {
      Alert.alert('Hata', 'Dışa aktarılacak veri bulunamadı.');
      return;
    }

    setIsExporting(true);
    setExportStatus('Excel dosyası oluşturuluyor...');

    try {
      const result = await ExportManager.exportReport('excel', {
        data: reportData,
        title: reportTitle,
        type: reportType,
        config: exportConfig.excel
      });

      if (result.success) {
        Alert.alert('Başarılı', 'Excel dosyası başarıyla oluşturuldu.', [
          { text: 'Tamam', onPress: () => setExportStatus(null) }
        ]);
      } else {
        throw new Error(result.error || 'Excel dosyası oluşturulurken hata oluştu');
      }
    } catch (error) {
      Alert.alert('Hata', `Excel dosyası oluşturulurken hata: ${error.message}`);
      setExportStatus(null);
    } finally {
      setIsExporting(false);
    }
  }, [reportData, reportTitle, reportType, exportConfig]);

  // PowerPoint dışa aktarma
  const handlePowerPointExport = useCallback(async () => {
    if (!reportData) {
      Alert.alert('Hata', 'Dışa aktarılacak veri bulunamadı.');
      return;
    }

    setIsExporting(true);
    setExportStatus('PowerPoint sunumu oluşturuluyor...');

    try {
      const result = await ExportManager.exportReport('powerpoint', {
        data: reportData,
        title: reportTitle,
        type: reportType,
        config: exportConfig.powerpoint
      });

      if (result.success) {
        Alert.alert('Başarılı', 'PowerPoint sunumu başarıyla oluşturuldu.', [
          { text: 'Tamam', onPress: () => setExportStatus(null) }
        ]);
      } else {
        throw new Error(result.error || 'PowerPoint sunumu oluşturulurken hata oluştu');
      }
    } catch (error) {
      Alert.alert('Hata', `PowerPoint sunumu oluşturulurken hata: ${error.message}`);
      setExportStatus(null);
    } finally {
      setIsExporting(false);
    }
  }, [reportData, reportTitle, reportType, exportConfig]);

  // Resim dışa aktarma
  const handleImageExport = useCallback(async () => {
    if (!reportData) {
      Alert.alert('Hata', 'Dışa aktarılacak veri bulunamadı.');
      return;
    }

    setIsExporting(true);
    setExportStatus('Resim oluşturuluyor...');

    try {
      const result = await ExportManager.exportReport('image', {
        data: reportData,
        title: reportTitle,
        type: reportType,
        config: exportConfig.image
      });

      if (result.success) {
        Alert.alert('Başarılı', 'Resim başarıyla oluşturuldu.', [
          { text: 'Tamam', onPress: () => setExportStatus(null) }
        ]);
      } else {
        throw new Error(result.error || 'Resim oluşturulurken hata oluştu');
      }
    } catch (error) {
      Alert.alert('Hata', `Resim oluşturulurken hata: ${error.message}`);
      setExportStatus(null);
    } finally {
      setIsExporting(false);
    }
  }, [reportData, reportTitle, reportType, exportConfig]);

  // E-posta dışa aktarma
  const handleEmailExport = useCallback(async () => {
    if (!reportData) {
      Alert.alert('Hata', 'Dışa aktarılacak veri bulunamadı.');
      return;
    }

    setIsExporting(true);
    setExportStatus('E-posta hazırlanıyor...');

    try {
      const result = await ExportManager.exportReport('email', {
        data: reportData,
        title: reportTitle,
        type: reportType,
        config: exportConfig.email
      });

      if (result.success) {
        Alert.alert('Başarılı', 'E-posta başarıyla gönderildi.', [
          { text: 'Tamam', onPress: () => setExportStatus(null) }
        ]);
      } else {
        throw new Error(result.error || 'E-posta gönderilirken hata oluştu');
      }
    } catch (error) {
      Alert.alert('Hata', `E-posta gönderilirken hata: ${error.message}`);
      setExportStatus(null);
    } finally {
      setIsExporting(false);
    }
  }, [reportData, reportTitle, reportType, exportConfig]);

  // Tüm export türlerini tek seferde yönetme
  const exportOptions = [
    {
      id: 'pdf',
      title: 'PDF',
      description: 'Profesyonel PDF raporu',
      icon: '📄',
      handler: handlePDFExport,
      enabled: true
    },
    {
      id: 'excel',
      title: 'Excel',
      description: 'Elektronik tablo',
      icon: '📊',
      handler: handleExcelExport,
      enabled: true
    },
    {
      id: 'powerpoint',
      title: 'PowerPoint',
      description: 'Sunum formatı',
      icon: '📈',
      handler: handlePowerPointExport,
      enabled: true
    },
    {
      id: 'image',
      title: 'Resim',
      description: 'PNG/JPEG formatı',
      icon: '🖼️',
      handler: handleImageExport,
      enabled: true
    },
    {
      id: 'email',
      title: 'E-posta',
      description: 'E-posta gönder',
      icon: '📧',
      handler: handleEmailExport,
      enabled: true
    }
  ];

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
      padding: globalStyles.spacing.md
    },
    title: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.text.primary,
      marginBottom: globalStyles.spacing.md
    },
    exportGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between'
    },
    exportOption: {
      width: '48%',
      backgroundColor: theme.colors.surface,
      borderRadius: globalStyles.borderRadius.md,
      padding: globalStyles.spacing.md,
      marginBottom: globalStyles.spacing.md,
      alignItems: 'center',
      borderWidth: 1,
      borderColor: theme.colors.border,
      opacity: isExporting ? 0.7 : 1
    },
    exportIcon: {
      fontSize: 24,
      marginBottom: globalStyles.spacing.sm
    },
    exportTitle: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.text.primary,
      marginBottom: globalStyles.spacing.xs
    },
    exportDescription: {
      fontSize: 12,
      color: theme.colors.text.secondary,
      textAlign: 'center'
    },
    statusContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: globalStyles.spacing.md,
      padding: globalStyles.spacing.md,
      backgroundColor: theme.colors.surface,
      borderRadius: globalStyles.borderRadius.md
    },
    statusText: {
      fontSize: 14,
      color: theme.colors.text.primary,
      marginLeft: globalStyles.spacing.sm
    }
  });

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Raporu Dışa Aktar</Text>
      
      <View style={styles.exportGrid}>
        {exportOptions.map((option) => (
          <View
            key={option.id}
            style={[
              styles.exportOption,
              !option.enabled && { opacity: 0.5 }
            ]}
            onTouchEnd={option.enabled && !isExporting ? option.handler : null}
          >
            <Text style={styles.exportIcon}>{option.icon}</Text>
            <Text style={styles.exportTitle}>{option.title}</Text>
            <Text style={styles.exportDescription}>{option.description}</Text>
          </View>
        ))}
      </View>

      {isExporting && exportStatus && (
        <View style={styles.statusContainer}>
          <ActivityIndicator size="small" color={theme.colors.primary} />
          <Text style={styles.statusText}>{exportStatus}</Text>
        </View>
      )}
    </View>
  );
};

// Export both the class and component
console.log('🔍 ExportManager.js: About to export ExportManager class:', typeof ExportManager);
console.log('🔍 ExportManager.js: About to export ExportManagerComponent:', typeof ExportManagerComponent);

export default ExportManager;
