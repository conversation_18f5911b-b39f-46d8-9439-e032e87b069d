import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  TouchableOpacity,
  Platform,
  KeyboardAvoidingView,
  ActivityIndicator
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { useSQLiteContext } from 'expo-sqlite';

import { useAppContext } from '../context/AppContext';
import { Colors } from '../constants/colors';

// Import modular components
import {
  RegularIncomeBasicInfo,
  RegularIncomeNotificationSettings,
  RegularIncomeRecurrenceSettings,
  RegularIncomeAdditionalInfo,
  } from '../components/regular-income';

/**
 * Düzenli Gelir Formu Ekranı
 * Yeni gelir ekleme ve mevcut geliri düzenleme işlevselliği sağlar
 * @returns {JSX.Element} Düzenli Gelir Form Ekranı
 */
const RegularIncomeFormScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const insets = useSafeAreaInsets();
  const db = useSQLiteContext();
  const { theme } = useAppContext();
  const incomeId = route.params?.incomeId;
  const isEditMode = !!incomeId;

  // State yönetimi
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState({});
  
  // Form data state
  const [title, setTitle] = useState('');
  const [amount, setAmount] = useState('');
  const [currencyCode, setCurrencyCode] = useState('TRY');
  const [recurrenceType, setRecurrenceType] = useState('monthly');
  const [recurrenceInterval, setRecurrenceInterval] = useState(1);
  const [paymentDay, setPaymentDay] = useState(1);
  const [nextPaymentDate, setNextPaymentDate] = useState(new Date());
  const [notificationEnabled, setNotificationEnabled] = useState(false);
  const [notificationDaysBefore, setNotificationDaysBefore] = useState('1');
  const [notificationTime, setNotificationTime] = useState(new Date());
  const [notes, setNotes] = useState('');
  const [status, setStatus] = useState('active');
  
  // Additional state for enhanced form interaction
  const [touched, setTouched] = useState({});
  const [focusedField, setFocusedField] = useState(null);
  const [fieldErrors, setFieldErrors] = useState({});

  // Helper function to check if a field is empty
  const isFieldEmpty = useCallback((fieldName) => {
    const emptyChecks = {
      title: !title?.trim(),
      amount: !amount?.trim(),
      notes: !notes?.trim(),
      paymentDay: recurrenceType === 'monthly' && !paymentDay,
      notificationDaysBefore: notificationEnabled && !notificationDaysBefore,
    };
    
    return emptyChecks[fieldName] || false;
  }, [title, amount, notes, paymentDay, recurrenceType, notificationDaysBefore, notificationEnabled]);

  // Input styling function based on field state
  const getInputStyle = useCallback((fieldName) => {
    // Base style - always applied
    const baseStyle = [styles.input];
    
    // Error state - highest priority
    if (fieldErrors?.[fieldName] || errors?.[fieldName] || (touched[fieldName] && isFieldEmpty(fieldName))) {
      return [styles.input, styles.inputError];
    }
    
    // Focus state
    if (focusedField === fieldName) {
      return [styles.input, styles.inputFocused];
    }

    // Success state - when field is touched, valid, and not empty
    if (touched[fieldName] && !fieldErrors?.[fieldName] && !errors?.[fieldName] && !isFieldEmpty(fieldName)) {
      return [
        styles.input,
        {
          borderColor: Colors.SUCCESS,
          backgroundColor: `${Colors.SUCCESS}08`,
        }
      ];
    }
    
    return baseStyle;
  }, [fieldErrors, errors, focusedField, touched, isFieldEmpty]);

  // Handle input focus
  const handleInputFocus = useCallback((fieldName) => {
    setFocusedField(fieldName);
    if (!touched[fieldName]) {
      setTouched(prev => ({ ...prev, [fieldName]: true }));
    }
  }, [touched]);

  // Handle input blur
  const handleInputBlur = useCallback((fieldName) => {
    setFocusedField(null);
    setTouched(prev => ({ ...prev, [fieldName]: true }));
  }, []);

  // Get field error text
  const getFieldErrorText = useCallback((fieldName) => {
    if (fieldErrors?.[fieldName]) {
      return typeof fieldErrors[fieldName] === 'string' ? fieldErrors[fieldName] : 'Geçersiz değer';
    }
    if (errors?.[fieldName]) {
      return errors[fieldName];
    }
    if (touched[fieldName] && isFieldEmpty(fieldName)) {
      const fieldLabels = {
        title: 'Gelir adı',
        amount: 'Tutar',
        notes: 'Açıklama',
        paymentDay: 'Ödeme günü',
        notificationDaysBefore: 'Bildirim süresi'
      };
      return `${fieldLabels[fieldName] || 'Bu alan'} zorunludur`;
    }
    return null;
  }, [fieldErrors, errors, touched, isFieldEmpty]);

  // Format amount for display
  const getFormattedAmount = useCallback(() => {
    if (!amount) return '';
    const cleanAmount = amount.replace(/[^\d.,]/g, '');
    const numericAmount = parseFloat(cleanAmount.replace(',', '.'));
    if (isNaN(numericAmount)) return amount;
    
    return numericAmount.toLocaleString('tr-TR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  }, [amount]);

  // Handle amount change with formatting
  const handleAmountChange = useCallback((text) => {
    // Remove any non-numeric characters except comma and period
    const cleanText = text.replace(/[^\d.,]/g, '');
    setAmount(cleanText);

    // Mark field as touched and validate
    setTouched(prev => ({ ...prev, amount: true }));

    // Validate amount
    const numericValue = parseFloat(cleanText.replace(',', '.'));
    const isValid = !isNaN(numericValue) && numericValue > 0 && numericValue <= 999_999_999;
    setFieldErrors(prev => ({ ...prev, amount: !isValid }));
  }, []);

  // Load existing income data
  const loadIncomeData = useCallback(async () => {
    if (!isEditMode || !incomeId) return;

    try {
      setLoading(true);
      const result = await db.getFirstAsync(
        'SELECT * FROM regular_incomes WHERE id = ?',
        [incomeId]
      );

      if (result) {
        setTitle(result.title || '');
        setAmount(result.amount?.toString() || '');
        setCurrencyCode(result.currency_code || 'TRY');
        setRecurrenceType(result.recurrence_type || 'monthly');
        setRecurrenceInterval(result.recurrence_interval || 1);
        setPaymentDay(result.payment_day || 1);
        setNextPaymentDate(result.next_payment_date ? new Date(result.next_payment_date) : new Date());
        setNotificationEnabled(result.notification_enabled === 1);
        setNotificationDaysBefore(result.notification_days_before?.toString() || '1');
        setNotificationTime(result.notification_time ? new Date(`2000-01-01T${result.notification_time}`) : new Date());
        setNotes(result.notes || '');
        setStatus(result.status || 'active');
      }
    } catch (error) {
      console.error('Error loading income data:', error);
      Alert.alert('Hata', 'Gelir bilgileri yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  }, [isEditMode, incomeId, db]);

  // Validate form
  const validateForm = useCallback(() => {
    const newErrors = {};

    if (!title.trim()) {
      newErrors.title = 'Gelir adı gereklidir';
    }

    const numericAmount = parseFloat(amount.replace(',', '.'));
    if (!amount.trim() || isNaN(numericAmount) || numericAmount <= 0) {
      newErrors.amount = 'Geçerli bir tutar giriniz';
    }

    if (recurrenceType === 'monthly' && (!paymentDay || paymentDay < 1 || paymentDay > 31)) {
      newErrors.paymentDay = 'Geçerli bir ödeme günü seçiniz';
    }

    if (notificationEnabled) {
      const notifDays = parseInt(notificationDaysBefore);
      if (isNaN(notifDays) || notifDays < 0 || notifDays > 30) {
        newErrors.notificationDaysBefore = 'Bildirim günü 0-30 arasında olmalıdır';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [title, amount, recurrenceType, paymentDay, notificationEnabled, notificationDaysBefore]);

  // Save income
  const handleSave = useCallback(async () => {
    if (!validateForm()) {
      Alert.alert('Hata', 'Lütfen tüm gerekli alanları doğru şekilde doldurunuz.');
      return;
    }

    try {
      setSaving(true);

      const numericAmount = parseFloat(amount.replace(',', '.'));
      const notificationTimeStr = notificationTime.toTimeString().slice(0, 5);

      const incomeData = {
        title: title.trim(),
        amount: numericAmount,
        currency_code: currencyCode,
        recurrence_type: recurrenceType,
        recurrence_interval: recurrenceInterval,
        payment_day: recurrenceType === 'monthly' ? paymentDay : null,
        next_payment_date: nextPaymentDate.toISOString().split('T')[0],
        notification_enabled: notificationEnabled ? 1 : 0,
        notification_days_before: notificationEnabled ? parseInt(notificationDaysBefore) : null,
        notification_time: notificationEnabled ? notificationTimeStr : null,
        notes: notes.trim(),
        status: status,
        updated_at: new Date().toISOString()
      };

      if (isEditMode) {
        await db.runAsync(
          `UPDATE regular_incomes SET
           title = ?, amount = ?, currency_code = ?, recurrence_type = ?,
           recurrence_interval = ?, payment_day = ?, next_payment_date = ?,
           notification_enabled = ?, notification_days_before = ?, notification_time = ?,
           notes = ?, status = ?, updated_at = ?
           WHERE id = ?`,
          [
            incomeData.title, incomeData.amount, incomeData.currency_code,
            incomeData.recurrence_type, incomeData.recurrence_interval,
            incomeData.payment_day, incomeData.next_payment_date,
            incomeData.notification_enabled, incomeData.notification_days_before,
            incomeData.notification_time, incomeData.notes, incomeData.status,
            incomeData.updated_at, incomeId
          ]
        );
        Alert.alert('Başarılı', 'Düzenli gelir güncellendi.');
      } else {
        await db.runAsync(
          `INSERT INTO regular_incomes (
           title, amount, currency_code, recurrence_type, recurrence_interval,
           payment_day, next_payment_date, notification_enabled, notification_days_before,
           notification_time, notes, status, created_at, updated_at
           ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            incomeData.title, incomeData.amount, incomeData.currency_code,
            incomeData.recurrence_type, incomeData.recurrence_interval,
            incomeData.payment_day, incomeData.next_payment_date,
            incomeData.notification_enabled, incomeData.notification_days_before,
            incomeData.notification_time, incomeData.notes, incomeData.status,
            incomeData.updated_at, incomeData.updated_at
          ]
        );
        Alert.alert('Başarılı', 'Düzenli gelir eklendi.');
      }

      navigation.goBack();
    } catch (error) {
      console.error('Error saving income:', error);
      Alert.alert('Hata', 'Kaydetme sırasında bir hata oluştu.');
    } finally {
      setSaving(false);
    }
  }, [validateForm, amount, title, currencyCode, recurrenceType, recurrenceInterval, paymentDay, nextPaymentDate, notificationEnabled, notificationDaysBefore, notificationTime, notes, status, isEditMode, incomeId, db, navigation]);

  // Load data on mount
  useEffect(() => {
    loadIncomeData();
  }, [loadIncomeData]);

  return (
    <View style={[styles.container, { paddingTop: insets.top, backgroundColor: theme.BACKGROUND }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.PRIMARY }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {isEditMode ? 'Geliri Düzenle' : 'Yeni Düzenli Gelir'}
        </Text>
        <TouchableOpacity
          style={styles.saveHeaderButton}
          onPress={handleSave}
          disabled={saving}
        >
          {saving ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <MaterialIcons name="check" size={24} color="#fff" />
          )}
        </TouchableOpacity>
      </View>

      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardView}
        keyboardVerticalOffset={Platform.OS === "ios" ? 88 : 0}
      >
      <ScrollView
        style={{ flex: 1, backgroundColor: theme.BACKGROUND }}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        bounces={true}
      >
        {/* Basic Information Section */}
        <RegularIncomeBasicInfo
          title={title}
          setTitle={setTitle}
          amount={amount}
          handleAmountChange={handleAmountChange}
          currencyCode={currencyCode}
          setCurrencyCode={setCurrencyCode}
          fieldErrors={fieldErrors}
          handleInputFocus={handleInputFocus}
          handleInputBlur={handleInputBlur}
          getInputStyle={getInputStyle}
          getFieldErrorText={getFieldErrorText}
          getFormattedAmount={getFormattedAmount}
        />

        {/* Recurrence Configuration Section */}
        <RegularIncomeRecurrenceSettings
          recurrenceType={recurrenceType}
          setRecurrenceType={setRecurrenceType}
          recurrenceInterval={recurrenceInterval}
          setRecurrenceInterval={setRecurrenceInterval}
          paymentDay={paymentDay}
          setPaymentDay={setPaymentDay}
          nextPaymentDate={nextPaymentDate}
        />

        {/* Notification Settings Section */}
        <RegularIncomeNotificationSettings
          notificationEnabled={notificationEnabled}
          setNotificationEnabled={setNotificationEnabled}
          notificationDaysBefore={notificationDaysBefore}
          setNotificationDaysBefore={setNotificationDaysBefore}
          getInputStyle={getInputStyle}
          handleInputFocus={handleInputFocus}
          handleInputBlur={handleInputBlur}
          getFieldErrorText={getFieldErrorText}
          fieldErrors={fieldErrors}
        />

        {/* Additional Information Section */}
        <RegularIncomeAdditionalInfo
          notes={notes}
          setNotes={setNotes}
          getInputStyle={getInputStyle}
          handleInputFocus={handleInputFocus}
          handleInputBlur={handleInputBlur}
        />

        {/* Form Actions */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.button, styles.cancelButton]}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.cancelButtonText}>İptal</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.button, styles.saveButton]}
            onPress={handleSave}
            disabled={saving}
          >
            {saving ? (
              <ActivityIndicator color="#fff" size="small" />
            ) : (
              <Text style={styles.saveButtonText}>
                {isEditMode ? 'Güncelle' : 'Kaydet'}
              </Text>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#fff',
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 16,
  },
  saveHeaderButton: {
    padding: 8,
  },
  keyboardView: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
    paddingBottom: 40,
    gap: 24,
  },
  input: {
    borderWidth: 2,
    borderColor: Colors.GRAY_200,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: Colors.WHITE,
  },
  inputFocused: {
    borderColor: Colors.PRIMARY,
    backgroundColor: Colors.PRIMARY + '08',
  },
  inputError: {
    borderColor: Colors.DANGER,
    backgroundColor: Colors.DANGER + '08',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 32,
    gap: 16,
    paddingHorizontal: 4,
  },
  button: {
    flex: 1,
    paddingVertical: 16,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    minHeight: 54,
  },
  cancelButton: {
    backgroundColor: '#ffffff',
    borderWidth: 2,
    borderColor: '#e9ecef',
  },
  saveButton: {
    backgroundColor: Colors.PRIMARY,
    borderWidth: 2,
    borderColor: Colors.PRIMARY,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#6c757d',
    letterSpacing: 0.5,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#ffffff',
    letterSpacing: 0.5,
  },
});

export default RegularIncomeFormScreen;
