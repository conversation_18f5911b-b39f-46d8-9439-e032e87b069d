import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
// import { <PERSON><PERSON><PERSON>, PieChart, LineChart } from 'react-native-chart-kit';
import { Colors } from '../constants/colors';
import * as reminderStatsService from '../services/reminderStatsService';

/**
 * Hatırlatıcı İstatistikleri Ekranı
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @returns {JSX.Element} Hatırlatıcı İstatistikleri Ekranı
 */
export default function ReminderStatsScreen({ navigation }) {
  const db = useSQLiteContext();

  // Durum
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState('overview'); // 'overview', 'groups', 'categories', 'trends'

  // İstatistik verileri
  const [statusCounts, setStatusCounts] = useState(null);
  const [priorityCounts, setPriorityCounts] = useState(null);
  const [groupCounts, setGroupCounts] = useState([]);
  const [categoryCounts, setCategoryCounts] = useState([]);
  const [monthlyStats, setMonthlyStats] = useState([]);
  const [completionRate, setCompletionRate] = useState(null);
  const [repeatTypeCounts, setRepeatTypeCounts] = useState(null);



  // Verileri yükle
  const loadData = useCallback(async () => {
    try {
      setLoading(true);

      // Durum sayıları
      const statusData = await reminderStatsService.getReminderCountsByStatus(db);
      setStatusCounts(statusData);

      // Öncelik sayıları
      const priorityData = await reminderStatsService.getReminderCountsByPriority(db);
      setPriorityCounts(priorityData);

      // Grup sayıları
      const groupData = await reminderStatsService.getReminderCountsByGroup(db);
      setGroupCounts(groupData);

      // Kategori sayıları
      const categoryData = await reminderStatsService.getReminderCountsByCategory(db);
      setCategoryCounts(categoryData);

      // Aylık istatistikler
      const monthlyData = await reminderStatsService.getReminderCountsByMonth(db, 6);
      setMonthlyStats(monthlyData);

      // Tamamlanma oranı
      const rateData = await reminderStatsService.getReminderCompletionRate(db);
      setCompletionRate(rateData);

      // Tekrarlama tipleri
      const repeatTypeData = await reminderStatsService.getReminderCountsByRepeatType(db);
      setRepeatTypeCounts(repeatTypeData);

      setLoading(false);
    } catch (error) {
      console.error('İstatistik verileri yükleme hatası:', error);
      setLoading(false);
    }
  }, [db]);

  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [loadData])
  );

  // Yenileme işlemi
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  // Durum sayıları grafiği
  const renderStatusChart = () => {
    if (!statusCounts) return null;

    return (
      <View style={styles.chartContainer}>
        <Text style={styles.chartTitle}>Hatırlatıcı Durumları</Text>
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{statusCounts.pending}</Text>
            <Text style={styles.statLabel}>Bekleyen</Text>
            <View style={[styles.statIndicator, { backgroundColor: Colors.WARNING }]} />
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{statusCounts.sent}</Text>
            <Text style={styles.statLabel}>Gönderildi</Text>
            <View style={[styles.statIndicator, { backgroundColor: Colors.INFO }]} />
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{statusCounts.read}</Text>
            <Text style={styles.statLabel}>Okundu</Text>
            <View style={[styles.statIndicator, { backgroundColor: Colors.SUCCESS }]} />
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{statusCounts.cancelled}</Text>
            <Text style={styles.statLabel}>İptal Edildi</Text>
            <View style={[styles.statIndicator, { backgroundColor: Colors.DANGER }]} />
          </View>
        </View>
      </View>
    );
  };

  // Öncelik sayıları grafiği
  const renderPriorityChart = () => {
    if (!priorityCounts) return null;

    // Tüm değerler sıfır ise
    if (priorityCounts.low === 0 && priorityCounts.normal === 0 && priorityCounts.high === 0) {
      return (
        <View style={styles.noDataContainer}>
          <Text style={styles.noDataText}>Henüz hatırlatıcı verisi yok</Text>
        </View>
      );
    }

    // Toplam değer
    const total = priorityCounts.low + priorityCounts.normal + priorityCounts.high;

    // Yüzde hesapla
    const lowPercent = total > 0 ? Math.round((priorityCounts.low / total) * 100) : 0;
    const normalPercent = total > 0 ? Math.round((priorityCounts.normal / total) * 100) : 0;
    const highPercent = total > 0 ? Math.round((priorityCounts.high / total) * 100) : 0;

    return (
      <View style={styles.chartContainer}>
        <Text style={styles.chartTitle}>Öncelik Dağılımı</Text>
        <View style={styles.priorityContainer}>
          <View style={styles.priorityItem}>
            <View style={styles.priorityHeader}>
              <View style={[styles.priorityIndicator, { backgroundColor: Colors.SUCCESS }]} />
              <Text style={styles.priorityLabel}>Düşük</Text>
            </View>
            <Text style={styles.priorityValue}>{priorityCounts.low}</Text>
            <View style={styles.percentBarContainer}>
              <View
                style={[
                  styles.percentBar,
                  { width: `${lowPercent}%`, backgroundColor: Colors.SUCCESS }
                ]}
              />
            </View>
            <Text style={styles.percentText}>{lowPercent}%</Text>
          </View>

          <View style={styles.priorityItem}>
            <View style={styles.priorityHeader}>
              <View style={[styles.priorityIndicator, { backgroundColor: Colors.WARNING }]} />
              <Text style={styles.priorityLabel}>Normal</Text>
            </View>
            <Text style={styles.priorityValue}>{priorityCounts.normal}</Text>
            <View style={styles.percentBarContainer}>
              <View
                style={[
                  styles.percentBar,
                  { width: `${normalPercent}%`, backgroundColor: Colors.WARNING }
                ]}
              />
            </View>
            <Text style={styles.percentText}>{normalPercent}%</Text>
          </View>

          <View style={styles.priorityItem}>
            <View style={styles.priorityHeader}>
              <View style={[styles.priorityIndicator, { backgroundColor: Colors.DANGER }]} />
              <Text style={styles.priorityLabel}>Yüksek</Text>
            </View>
            <Text style={styles.priorityValue}>{priorityCounts.high}</Text>
            <View style={styles.percentBarContainer}>
              <View
                style={[
                  styles.percentBar,
                  { width: `${highPercent}%`, backgroundColor: Colors.DANGER }
                ]}
              />
            </View>
            <Text style={styles.percentText}>{highPercent}%</Text>
          </View>
        </View>
      </View>
    );
  };

  // Tekrarlama tipleri grafiği
  const renderRepeatTypeChart = () => {
    if (!repeatTypeCounts) return null;

    const repeatTypes = [
      { name: 'Bir Kez', count: repeatTypeCounts.once, color: '#3498db' },
      { name: 'Günlük', count: repeatTypeCounts.daily, color: '#2ecc71' },
      { name: 'Haftalık', count: repeatTypeCounts.weekly, color: '#e74c3c' },
      { name: 'Aylık', count: repeatTypeCounts.monthly, color: '#f39c12' },
      { name: 'Yıllık', count: repeatTypeCounts.yearly, color: '#9b59b6' }
    ];

    // Sıfır değerlerini filtrele
    const filteredTypes = repeatTypes.filter(item => item.count > 0);

    if (filteredTypes.length === 0) {
      return (
        <View style={styles.noDataContainer}>
          <Text style={styles.noDataText}>Henüz hatırlatıcı verisi yok</Text>
        </View>
      );
    }

    // Toplam değer
    const total = filteredTypes.reduce((sum, item) => sum + item.count, 0);

    return (
      <View style={styles.chartContainer}>
        <Text style={styles.chartTitle}>Tekrarlama Tipleri</Text>
        <View style={styles.repeatTypeContainer}>
          {filteredTypes.map((type, index) => {
            const percent = total > 0 ? Math.round((type.count / total) * 100) : 0;

            return (
              <View key={index} style={styles.repeatTypeItem}>
                <View style={styles.repeatTypeHeader}>
                  <View style={[styles.repeatTypeIndicator, { backgroundColor: type.color }]} />
                  <Text style={styles.repeatTypeLabel}>{type.name}</Text>
                  <Text style={styles.repeatTypeCount}>{type.count}</Text>
                </View>
                <View style={styles.percentBarContainer}>
                  <View
                    style={[
                      styles.percentBar,
                      { width: `${percent}%`, backgroundColor: type.color }
                    ]}
                  />
                </View>
                <Text style={styles.percentText}>{percent}%</Text>
              </View>
            );
          })}
        </View>
      </View>
    );
  };

  // Tamamlanma oranı
  const renderCompletionRate = () => {
    if (!completionRate) return null;

    return (
      <View style={styles.statsContainer}>
        <Text style={styles.statsTitle}>Tamamlanma Oranı</Text>
        <View style={styles.completionContainer}>
          <View style={styles.completionRateContainer}>
            <Text style={styles.completionRateText}>{completionRate.rate}%</Text>
            <View style={styles.completionBarContainer}>
              <View
                style={[
                  styles.completionBar,
                  { width: `${completionRate.rate}%` }
                ]}
              />
            </View>
          </View>
          <View style={styles.completionDetails}>
            <View style={styles.completionDetailItem}>
              <Text style={styles.completionDetailLabel}>Toplam:</Text>
              <Text style={styles.completionDetailValue}>{completionRate.total}</Text>
            </View>
            <View style={styles.completionDetailItem}>
              <Text style={styles.completionDetailLabel}>Tamamlanan:</Text>
              <Text style={styles.completionDetailValue}>{completionRate.completed}</Text>
            </View>
          </View>
        </View>
      </View>
    );
  };

  // Aylık trend grafiği
  const renderMonthlyTrend = () => {
    if (!monthlyStats || monthlyStats.length === 0) return null;

    return (
      <View style={styles.chartContainer}>
        <Text style={styles.chartTitle}>Aylık Hatırlatıcı Trendi</Text>
        <View style={styles.monthlyTrendContainer}>
          <View style={styles.trendLegend}>
            <View style={styles.legendItem}>
              <View style={[styles.legendIndicator, { backgroundColor: Colors.PRIMARY }]} />
              <Text style={styles.legendText}>Oluşturulan</Text>
            </View>
            <View style={styles.legendItem}>
              <View style={[styles.legendIndicator, { backgroundColor: Colors.SUCCESS }]} />
              <Text style={styles.legendText}>Zamanlanmış</Text>
            </View>
          </View>

          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.monthlyBarContainer}>
              {monthlyStats.map((month, index) => (
                <View key={index} style={styles.monthlyBarGroup}>
                  <Text style={styles.monthName}>{month.monthName.substring(0, 3)}</Text>
                  <View style={styles.barGroup}>
                    <View style={styles.barItem}>
                      <Text style={styles.barValue}>{month.created}</Text>
                      <View style={styles.barContainer}>
                        <View
                          style={[
                            styles.bar,
                            {
                              height: `${Math.min(100, (month.created / Math.max(...monthlyStats.map(m => Math.max(m.created, m.scheduled)))) * 100)}%`,
                              backgroundColor: Colors.PRIMARY
                            }
                          ]}
                        />
                      </View>
                    </View>
                    <View style={styles.barItem}>
                      <Text style={styles.barValue}>{month.scheduled}</Text>
                      <View style={styles.barContainer}>
                        <View
                          style={[
                            styles.bar,
                            {
                              height: `${Math.min(100, (month.scheduled / Math.max(...monthlyStats.map(m => Math.max(m.created, m.scheduled)))) * 100)}%`,
                              backgroundColor: Colors.SUCCESS
                            }
                          ]}
                        />
                      </View>
                    </View>
                  </View>
                </View>
              ))}
            </View>
          </ScrollView>
        </View>
      </View>
    );
  };

  // Grup istatistikleri
  const renderGroupStats = () => {
    if (!groupCounts || groupCounts.length === 0) {
      return (
        <View style={styles.noDataContainer}>
          <Text style={styles.noDataText}>Henüz grup verisi yok</Text>
        </View>
      );
    }

    return (
      <View style={styles.listContainer}>
        <Text style={styles.listTitle}>Gruplara Göre Hatırlatıcılar</Text>
        {groupCounts.map(group => (
          <View
            key={group.id}
            style={[
              styles.listItem,
              { borderLeftColor: group.color || Colors.PRIMARY }
            ]}
          >
            <View style={styles.listItemHeader}>
              <View style={styles.listItemTitleContainer}>
                <MaterialIcons
                  name={group.icon || 'folder'}
                  size={20}
                  color={group.color || Colors.PRIMARY}
                />
                <Text style={styles.listItemTitle}>{group.name}</Text>
              </View>
              <Text style={styles.listItemCount}>{group.count}</Text>
            </View>
            <View style={styles.progressBarContainer}>
              <View
                style={[
                  styles.progressBar,
                  {
                    width: `${Math.min(100, (group.count / Math.max(...groupCounts.map(g => g.count))) * 100)}%`,
                    backgroundColor: group.color || Colors.PRIMARY
                  }
                ]}
              />
            </View>
          </View>
        ))}
      </View>
    );
  };

  // Kategori istatistikleri
  const renderCategoryStats = () => {
    if (!categoryCounts || categoryCounts.length === 0) {
      return (
        <View style={styles.noDataContainer}>
          <Text style={styles.noDataText}>Henüz kategori verisi yok</Text>
        </View>
      );
    }

    return (
      <View style={styles.listContainer}>
        <Text style={styles.listTitle}>Kategorilere Göre Hatırlatıcılar</Text>
        {categoryCounts.map(category => (
          <View
            key={category.id || 'uncategorized'}
            style={[
              styles.listItem,
              { borderLeftColor: category.color || Colors.PRIMARY }
            ]}
          >
            <View style={styles.listItemHeader}>
              <View style={styles.listItemTitleContainer}>
                <View
                  style={[
                    styles.categoryIndicator,
                    { backgroundColor: category.color || Colors.PRIMARY }
                  ]}
                />
                <Text style={styles.listItemTitle}>{category.name}</Text>
              </View>
              <Text style={styles.listItemCount}>{category.count}</Text>
            </View>
            <View style={styles.progressBarContainer}>
              <View
                style={[
                  styles.progressBar,
                  {
                    width: `${Math.min(100, (category.count / Math.max(...categoryCounts.map(c => c.count))) * 100)}%`,
                    backgroundColor: category.color || Colors.PRIMARY
                  }
                ]}
              />
            </View>
          </View>
        ))}
      </View>
    );
  };

  // Genel bakış içeriği
  const renderOverviewContent = () => {
    return (
      <View style={styles.tabContent}>
        {renderStatusChart()}
        {renderCompletionRate()}
        {renderPriorityChart()}
        {renderRepeatTypeChart()}
      </View>
    );
  };

  // Gruplar içeriği
  const renderGroupsContent = () => {
    return (
      <View style={styles.tabContent}>
        {renderGroupStats()}
      </View>
    );
  };

  // Kategoriler içeriği
  const renderCategoriesContent = () => {
    return (
      <View style={styles.tabContent}>
        {renderCategoryStats()}
      </View>
    );
  };

  // Trendler içeriği
  const renderTrendsContent = () => {
    return (
      <View style={styles.tabContent}>
        {renderMonthlyTrend()}
      </View>
    );
  };

  // Aktif sekmeye göre içerik
  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return renderOverviewContent();
      case 'groups':
        return renderGroupsContent();
      case 'categories':
        return renderCategoriesContent();
      case 'trends':
        return renderTrendsContent();
      default:
        return renderOverviewContent();
    }
  };

  // Yükleniyor durumu
  if (loading && !refreshing) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={styles.loadingText}>İstatistikler yükleniyor...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Başlık */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Hatırlatıcı İstatistikleri</Text>
      </View>

      {/* Sekmeler */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === 'overview' && styles.activeTabButton
          ]}
          onPress={() => setActiveTab('overview')}
        >
          <MaterialIcons
            name="dashboard"
            size={20}
            color={activeTab === 'overview' ? Colors.PRIMARY : Colors.GRAY_500}
          />
          <Text style={[
            styles.tabText,
            activeTab === 'overview' && styles.activeTabText
          ]}>
            Genel
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === 'groups' && styles.activeTabButton
          ]}
          onPress={() => setActiveTab('groups')}
        >
          <MaterialIcons
            name="folder"
            size={20}
            color={activeTab === 'groups' ? Colors.PRIMARY : Colors.GRAY_500}
          />
          <Text style={[
            styles.tabText,
            activeTab === 'groups' && styles.activeTabText
          ]}>
            Gruplar
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === 'categories' && styles.activeTabButton
          ]}
          onPress={() => setActiveTab('categories')}
        >
          <MaterialIcons
            name="label"
            size={20}
            color={activeTab === 'categories' ? Colors.PRIMARY : Colors.GRAY_500}
          />
          <Text style={[
            styles.tabText,
            activeTab === 'categories' && styles.activeTabText
          ]}>
            Kategoriler
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === 'trends' && styles.activeTabButton
          ]}
          onPress={() => setActiveTab('trends')}
        >
          <MaterialIcons
            name="trending-up"
            size={20}
            color={activeTab === 'trends' ? Colors.PRIMARY : Colors.GRAY_500}
          />
          <Text style={[
            styles.tabText,
            activeTab === 'trends' && styles.activeTabText
          ]}>
            Trendler
          </Text>
        </TouchableOpacity>
      </View>

      {/* İçerik */}
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[Colors.PRIMARY]}
          />
        }
      >
        {renderContent()}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: Colors.PRIMARY,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200,
  },
  tabButton: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 12,
  },
  activeTabButton: {
    borderBottomWidth: 2,
    borderBottomColor: Colors.PRIMARY,
  },
  tabText: {
    fontSize: 14,
    color: Colors.GRAY_500,
    marginLeft: 4,
  },
  activeTabText: {
    color: Colors.PRIMARY,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  tabContent: {
    padding: 16,
    paddingBottom: 32,
  },
  chartContainer: {
    marginBottom: 24,
    backgroundColor: Colors.GRAY_100,
    borderRadius: 12,
    padding: 16,
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  statItem: {
    width: '48%',
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
    color: Colors.TEXT_LIGHT,
    marginBottom: 8,
  },
  statIndicator: {
    width: 24,
    height: 4,
    borderRadius: 2,
  },
  priorityContainer: {
    marginTop: 16,
    width: '100%',
  },
  priorityItem: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  priorityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  priorityIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  priorityLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    flex: 1,
  },
  priorityValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    marginBottom: 8,
  },
  percentBarContainer: {
    height: 8,
    backgroundColor: Colors.GRAY_200,
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 4,
  },
  percentBar: {
    height: '100%',
    borderRadius: 4,
  },
  percentText: {
    fontSize: 12,
    color: Colors.TEXT_LIGHT,
    textAlign: 'right',
  },
  repeatTypeContainer: {
    marginTop: 16,
    width: '100%',
  },
  repeatTypeItem: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  repeatTypeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  repeatTypeIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  repeatTypeLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    flex: 1,
  },
  repeatTypeCount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
  },
  monthlyTrendContainer: {
    marginTop: 16,
    width: '100%',
  },
  trendLegend: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  legendIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 4,
  },
  legendText: {
    fontSize: 12,
    color: Colors.TEXT_DARK,
  },
  monthlyBarContainer: {
    flexDirection: 'row',
    paddingBottom: 16,
    minWidth: '100%',
  },
  monthlyBarGroup: {
    alignItems: 'center',
    marginRight: 16,
    minWidth: 60,
  },
  monthName: {
    fontSize: 12,
    color: Colors.TEXT_DARK,
    marginBottom: 8,
  },
  barGroup: {
    flexDirection: 'row',
    height: 150,
    alignItems: 'flex-end',
  },
  barItem: {
    alignItems: 'center',
    marginHorizontal: 4,
  },
  barValue: {
    fontSize: 10,
    color: Colors.TEXT_LIGHT,
    marginBottom: 4,
  },
  barContainer: {
    width: 20,
    height: 120,
    backgroundColor: Colors.GRAY_200,
    borderRadius: 4,
    justifyContent: 'flex-end',
  },
  bar: {
    width: '100%',
    borderRadius: 4,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    marginBottom: 16,
    alignSelf: 'flex-start',
  },
  noDataContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.GRAY_100,
    borderRadius: 12,
    marginBottom: 24,
  },
  noDataText: {
    fontSize: 16,
    color: Colors.TEXT_LIGHT,
  },
  statsContainer: {
    marginBottom: 24,
    backgroundColor: Colors.GRAY_100,
    borderRadius: 12,
    padding: 16,
  },
  statsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    marginBottom: 16,
  },
  completionContainer: {
    marginBottom: 8,
  },
  completionRateContainer: {
    marginBottom: 16,
  },
  completionRateText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.PRIMARY,
    marginBottom: 8,
  },
  completionBarContainer: {
    height: 12,
    backgroundColor: Colors.GRAY_300,
    borderRadius: 6,
    overflow: 'hidden',
  },
  completionBar: {
    height: '100%',
    backgroundColor: Colors.SUCCESS,
    borderRadius: 6,
  },
  completionDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  completionDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  completionDetailLabel: {
    fontSize: 14,
    color: Colors.TEXT_LIGHT,
    marginRight: 4,
  },
  completionDetailValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
  },
  listContainer: {
    marginBottom: 24,
    backgroundColor: Colors.GRAY_100,
    borderRadius: 12,
    padding: 16,
  },
  listTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    marginBottom: 16,
  },
  listItem: {
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 12,
    padding: 12,
    borderLeftWidth: 4,
  },
  listItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  listItemTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  listItemTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    marginLeft: 8,
  },
  listItemCount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.PRIMARY,
  },
  progressBarContainer: {
    height: 6,
    backgroundColor: Colors.GRAY_200,
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: Colors.PRIMARY,
    borderRadius: 3,
  },
  categoryIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
});
