import React from 'react';
import { TouchableOpacity, Text, StyleSheet, View } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';

/**
 * Hızlı işlem butonu bileşeni
 * 
 * @param {Object} props - Bileşen props'ları
 * @param {string} props.icon - Buton ikonu
 * @param {string} props.title - <PERSON><PERSON> b<PERSON>
 * @param {string} props.color - Buton rengi
 * @param {Function} props.onPress - Tıklama olayı
 * @returns {JSX.Element} QuickActionButton bileşeni
 */
const QuickActionButton = ({ icon, title, color = '#3498db', onPress }) => {
  return (
    <TouchableOpacity 
      style={styles.container}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={[styles.iconContainer, { backgroundColor: `${color}20` }]}>
        <MaterialIcons name={icon} size={24} color={color} />
      </View>
      <Text style={styles.title} numberOfLines={1}>{title}</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    width: '23%',
    marginBottom: 8,
  },
  iconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  title: {
    fontSize: 12,
    color: '#333',
    textAlign: 'center',
  },
});

export default QuickActionButton;
