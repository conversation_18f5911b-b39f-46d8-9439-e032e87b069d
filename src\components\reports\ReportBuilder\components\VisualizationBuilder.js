import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from '../../../../context/ThemeContext';

/**
 * Görselleştirme Oluşturucu - Grafik yapılandırması
 * 15+ grafik türü ve özelleştirme seçenekleri
 */
const VisualizationBuilder = ({
  reportData = [],
  onAddChart,
  onUpdateChart,
}) => {
  const { theme } = useTheme();

  return (
    <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
        📈 Görselleştirme Oluşturucu
      </Text>
      <Text style={[styles.subtitle, { color: theme.TEXT_SECONDARY }]}>
        <PERSON>ik yapılandırma sistemi - Yakında aktif olacak
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    textAlign: 'center',
  },
});

export default VisualizationBuilder;
