/**
 * Hat<PERSON>rlatıcı şablonları için servis fonksiyonları
 */
import * as reminderTagService from './reminderTagService';

/**
 * Tüm şablonları getirir
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<Array>} Şablonlar listesi
 */
export const getAllTemplates = async (db) => {
  try {
    const templates = await db.getAllAsync(`
      SELECT * FROM reminder_templates
      ORDER BY name ASC
    `);
    
    // Her şablon için etiketleri getir
    return await Promise.all(templates.map(async (template) => {
      const tags = await getTemplateTagsById(db, template.id);
      
      // JSON alanlarını parse et
      const repeatDays = template.repeat_days ? JSON.parse(template.repeat_days) : null;
      const repeatMonths = template.repeat_months ? JSON.parse(template.repeat_months) : null;
      const data = template.data ? JSON.parse(template.data) : {};
      
      return {
        ...template,
        repeat_days: repeatDays,
        repeat_months: repeatMonths,
        data,
        tags
      };
    }));
  } catch (error) {
    console.error('Şablonları getirme hatası:', error);
    throw error;
  }
};

/**
 * Belirli bir şablonu getirir
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} templateId - Şablon ID
 * @returns {Promise<Object|null>} Şablon
 */
export const getTemplateById = async (db, templateId) => {
  try {
    const template = await db.getFirstAsync(`
      SELECT * FROM reminder_templates
      WHERE id = ?
    `, [templateId]);
    
    if (!template) return null;
    
    // Etiketleri getir
    const tags = await getTemplateTagsById(db, templateId);
    
    // JSON alanlarını parse et
    const repeatDays = template.repeat_days ? JSON.parse(template.repeat_days) : null;
    const repeatMonths = template.repeat_months ? JSON.parse(template.repeat_months) : null;
    const data = template.data ? JSON.parse(template.data) : {};
    
    return {
      ...template,
      repeat_days: repeatDays,
      repeat_months: repeatMonths,
      data,
      tags
    };
  } catch (error) {
    console.error('Şablon getirme hatası:', error);
    throw error;
  }
};

/**
 * Bir şablonun etiketlerini getirir
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} templateId - Şablon ID
 * @returns {Promise<Array>} Etiketler listesi
 */
export const getTemplateTagsById = async (db, templateId) => {
  try {
    const tags = await db.getAllAsync(`
      SELECT t.*
      FROM reminder_tags t
      JOIN reminder_template_tags rt ON t.id = rt.tag_id
      WHERE rt.template_id = ?
      ORDER BY t.name ASC
    `, [templateId]);
    
    return tags;
  } catch (error) {
    console.error('Şablon etiketlerini getirme hatası:', error);
    throw error;
  }
};

/**
 * Yeni bir şablon ekler
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} template - Şablon verileri
 * @param {string} template.name - Şablon adı
 * @param {string} template.title - Hatırlatıcı başlığı
 * @param {string} template.message - Hatırlatıcı mesajı
 * @param {string} template.repeat_type - Tekrarlama tipi
 * @param {number} template.repeat_interval - Tekrarlama aralığı
 * @param {Array} template.repeat_days - Tekrarlama günleri
 * @param {Array} template.repeat_months - Tekrarlama ayları
 * @param {string} template.priority - Öncelik
 * @param {number} template.category_id - Kategori ID
 * @param {number} template.group_id - Grup ID
 * @param {string} template.icon - İkon
 * @param {string} template.color - Renk
 * @param {Object} template.data - Ek veriler
 * @param {Array<number>} tagIds - Etiket ID'leri
 * @returns {Promise<number>} Eklenen şablonun ID'si
 */
export const addTemplate = async (db, template, tagIds = []) => {
  try {
    // Transaction başlat
    await db.execAsync('BEGIN TRANSACTION');
    
    try {
      // Şablonu ekle
      const result = await db.runAsync(`
        INSERT INTO reminder_templates (
          name, title, message, repeat_type, repeat_interval, repeat_days, repeat_months,
          priority, category_id, group_id, icon, color, data
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        template.name,
        template.title,
        template.message || '',
        template.repeat_type || 'once',
        template.repeat_interval || 1,
        template.repeat_days ? JSON.stringify(template.repeat_days) : null,
        template.repeat_months ? JSON.stringify(template.repeat_months) : null,
        template.priority || 'normal',
        template.category_id || null,
        template.group_id || null,
        template.icon || null,
        template.color || '#3498db',
        template.data ? JSON.stringify(template.data) : '{}'
      ]);
      
      const templateId = result.lastInsertRowId;
      
      // Etiketleri ekle
      if (tagIds && tagIds.length > 0) {
        for (const tagId of tagIds) {
          await db.runAsync(`
            INSERT INTO reminder_template_tags (template_id, tag_id)
            VALUES (?, ?)
          `, [templateId, tagId]);
        }
      }
      
      // Transaction'ı tamamla
      await db.execAsync('COMMIT');
      
      return templateId;
    } catch (error) {
      // Hata durumunda transaction'ı geri al
      await db.execAsync('ROLLBACK');
      throw error;
    }
  } catch (error) {
    console.error('Şablon ekleme hatası:', error);
    throw error;
  }
};

/**
 * Bir şablonu günceller
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} templateId - Şablon ID
 * @param {Object} template - Güncellenecek şablon verileri
 * @param {Array<number>} tagIds - Etiket ID'leri
 * @returns {Promise<boolean>} Başarılı ise true
 */
export const updateTemplate = async (db, templateId, template, tagIds = null) => {
  try {
    // Transaction başlat
    await db.execAsync('BEGIN TRANSACTION');
    
    try {
      // Şablonu güncelle
      await db.runAsync(`
        UPDATE reminder_templates
        SET name = ?, title = ?, message = ?, repeat_type = ?, repeat_interval = ?,
            repeat_days = ?, repeat_months = ?, priority = ?, category_id = ?,
            group_id = ?, icon = ?, color = ?, data = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `, [
        template.name,
        template.title,
        template.message || '',
        template.repeat_type || 'once',
        template.repeat_interval || 1,
        template.repeat_days ? JSON.stringify(template.repeat_days) : null,
        template.repeat_months ? JSON.stringify(template.repeat_months) : null,
        template.priority || 'normal',
        template.category_id || null,
        template.group_id || null,
        template.icon || null,
        template.color || '#3498db',
        template.data ? JSON.stringify(template.data) : '{}',
        templateId
      ]);
      
      // Etiketleri güncelle (eğer belirtilmişse)
      if (tagIds !== null) {
        // Önce mevcut etiketleri sil
        await db.runAsync(`
          DELETE FROM reminder_template_tags
          WHERE template_id = ?
        `, [templateId]);
        
        // Yeni etiketleri ekle
        if (tagIds && tagIds.length > 0) {
          for (const tagId of tagIds) {
            await db.runAsync(`
              INSERT INTO reminder_template_tags (template_id, tag_id)
              VALUES (?, ?)
            `, [templateId, tagId]);
          }
        }
      }
      
      // Transaction'ı tamamla
      await db.execAsync('COMMIT');
      
      return true;
    } catch (error) {
      // Hata durumunda transaction'ı geri al
      await db.execAsync('ROLLBACK');
      throw error;
    }
  } catch (error) {
    console.error('Şablon güncelleme hatası:', error);
    throw error;
  }
};

/**
 * Bir şablonu siler
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} templateId - Şablon ID
 * @returns {Promise<boolean>} Başarılı ise true
 */
export const deleteTemplate = async (db, templateId) => {
  try {
    // Transaction başlat
    await db.execAsync('BEGIN TRANSACTION');
    
    try {
      // Önce şablon-etiket ilişkilerini sil
      await db.runAsync(`
        DELETE FROM reminder_template_tags
        WHERE template_id = ?
      `, [templateId]);
      
      // Sonra şablonu sil
      await db.runAsync(`
        DELETE FROM reminder_templates
        WHERE id = ?
      `, [templateId]);
      
      // Transaction'ı tamamla
      await db.execAsync('COMMIT');
      
      return true;
    } catch (error) {
      // Hata durumunda transaction'ı geri al
      await db.execAsync('ROLLBACK');
      throw error;
    }
  } catch (error) {
    console.error('Şablon silme hatası:', error);
    throw error;
  }
};

/**
 * Şablondan hatırlatıcı oluşturur
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} templateId - Şablon ID
 * @param {Object} reminderData - Hatırlatıcı verileri (tarih, saat vb.)
 * @param {Object} reminderService - Hatırlatıcı servisi
 * @returns {Promise<number>} Oluşturulan hatırlatıcının ID'si
 */
export const createReminderFromTemplate = async (db, templateId, reminderData, reminderService) => {
  try {
    // Şablonu getir
    const template = await getTemplateById(db, templateId);
    
    if (!template) {
      throw new Error('Şablon bulunamadı');
    }
    
    // Hatırlatıcı verilerini hazırla
    const reminder = {
      title: template.title,
      message: template.message,
      date: reminderData.date,
      time: reminderData.time,
      repeat_type: template.repeat_type,
      repeat_interval: template.repeat_interval,
      repeat_days: template.repeat_days,
      repeat_months: template.repeat_months,
      priority: template.priority,
      category_id: template.category_id,
      group_id: template.group_id,
      data: {
        ...template.data,
        createdFromTemplate: template.id
      }
    };
    
    // Etiket ID'lerini hazırla
    const tagIds = template.tags ? template.tags.map(tag => tag.id) : [];
    
    // Hatırlatıcıyı oluştur
    const reminderId = await reminderService.addReminder(db, reminder, tagIds);
    
    return reminderId;
  } catch (error) {
    console.error('Şablondan hatırlatıcı oluşturma hatası:', error);
    throw error;
  }
};
