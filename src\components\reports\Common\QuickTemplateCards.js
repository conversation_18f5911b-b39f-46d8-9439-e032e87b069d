import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, FlatList } from 'react-native';

/**
 * Hızlı Şablon Kartları
 * Ana sayfadaki hızlı erişim şablonları
 */
const QuickTemplateCards = ({ templates, onTemplateSelect, theme }) => {
  /**
   * Güvenli tema değeri alma fonksiyonu
   * @param {string} property - Tema özelliği
   * @param {string} fallback - <PERSON><PERSON><PERSON><PERSON><PERSON>
   * @returns {string} Gü<PERSON>li tema değeri
   */
  const getSafeThemeValue = (property, fallback) => {
    return theme[property] || theme.colors?.[property.toLowerCase()] || fallback;
  };
  const renderTemplate = ({ item }) => (
    <TouchableOpacity
      style={[styles.templateCard, { backgroundColor: theme.SURFACE || theme.colors?.surface || '#f9f9f9' }]}
      onPress={() => onTemplateSelect(item)}
    >
      <View style={styles.templateHeader}>
        <Text style={styles.templateIcon}>{item.icon}</Text>
        <Text style={[styles.templateTime, { color: theme.TEXT_SECONDARY }]}>
          {item.estimatedTime}
        </Text>
      </View>
      
      <Text style={[styles.templateName, { color: theme.TEXT_PRIMARY }]}>
        {item.name}
      </Text>
      
      <Text style={[styles.templateDescription, { color: theme.TEXT_SECONDARY }]}>
        {item.description}
      </Text>
      
      <View style={[styles.templateAccent, { backgroundColor: item.color }]} />
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
        🚀 Hızlı Başlangıç
      </Text>
      
      <FlatList
        data={templates}
        renderItem={renderTemplate}
        keyExtractor={(item) => item.id}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.templateList}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  templateList: {
    paddingHorizontal: 12,
  },
  templateCard: {
    width: 180,
    padding: 16,
    marginHorizontal: 4,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    position: 'relative',
  },
  templateHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  templateIcon: {
    fontSize: 28,
  },
  templateTime: {
    fontSize: 12,
    opacity: 0.7,
  },
  templateName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  templateDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  templateAccent: {
    position: 'absolute',
    top: 0,
    right: 0,
    width: 4,
    height: '100%',
    borderTopRightRadius: 12,
    borderBottomRightRadius: 12,
  },
});

export default QuickTemplateCards;
