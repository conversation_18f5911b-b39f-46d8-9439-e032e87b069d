/**
 * AI-Powered Analiz Servisi
 * Finansal verilerin otomatik analizi ve öngörüleri
 * Trend analizi, anomali tespiti ve öneriler
 */

/**
 * Finansal veri analizi yap
 * @param {Array} data - Finansal veri
 * @param {Object} options - <PERSON><PERSON><PERSON>
 * @returns {Object} Analiz sonuçları
 */
export const analyzeFinancialData = (data, options = {}) => {
  if (!data || data.length === 0) {
    return {
      summary: 'Analiz için yeterli veri bulunamadı',
      insights: [],
      recommendations: [],
      trends: {},
      anomalies: [],
      predictions: {}
    };
  }

  const {
    timeField = 'date',
    amountField = 'amount',
    categoryField = 'category',
    typeField = 'type',
    analysisType = 'comprehensive'
  } = options;

  // Temel istatistikler
  const basicStats = calculateBasicStatistics(data, amountField);
  
  // Trend analizi
  const trendAnalysis = analyzeTrends(data, timeField, amountField);
  
  // Kategori analizi
  const categoryAnalysis = analyzeCategoriesDistribution(data, categoryField, amountField);
  
  // Anomali tespiti
  const anomalies = detectAnomalies(data, amountField);
  
  // Öngörüler
  const predictions = generatePredictions(data, timeField, amountField);
  
  // AI önerileri
  const recommendations = generateRecommendations(basicStats, trendAnalysis, categoryAnalysis);
  
  // Öngörüler
  const insights = generateInsights(basicStats, trendAnalysis, categoryAnalysis, anomalies);

  return {
    summary: generateSummary(basicStats, trendAnalysis),
    insights,
    recommendations,
    trends: trendAnalysis,
    anomalies,
    predictions,
    statistics: basicStats,
    categoryBreakdown: categoryAnalysis
  };
};

/**
 * Temel istatistikleri hesapla
 */
const calculateBasicStatistics = (data, amountField) => {
  const amounts = data.map(item => Number(item[amountField]) || 0);
  const positiveAmounts = amounts.filter(amount => amount > 0);
  const negativeAmounts = amounts.filter(amount => amount < 0);
  
  const total = amounts.reduce((sum, amount) => sum + amount, 0);
  const average = amounts.length > 0 ? total / amounts.length : 0;
  const median = calculateMedian(amounts);
  const standardDeviation = calculateStandardDeviation(amounts, average);
  
  return {
    total,
    average,
    median,
    standardDeviation,
    count: amounts.length,
    positiveCount: positiveAmounts.length,
    negativeCount: negativeAmounts.length,
    positiveTotal: positiveAmounts.reduce((sum, amount) => sum + amount, 0),
    negativeTotal: Math.abs(negativeAmounts.reduce((sum, amount) => sum + amount, 0)),
    min: Math.min(...amounts),
    max: Math.max(...amounts),
    range: Math.max(...amounts) - Math.min(...amounts)
  };
};

/**
 * Trend analizi yap
 */
const analyzeTrends = (data, timeField, amountField) => {
  // Tarihe göre sırala
  const sortedData = data
    .filter(item => item[timeField] && item[amountField] != null)
    .sort((a, b) => new Date(a[timeField]) - new Date(b[timeField]));

  if (sortedData.length < 2) {
    return { direction: 'insufficient_data', strength: 0, pattern: 'none' };
  }

  // Aylık toplam hesapla
  const monthlyTotals = groupByMonth(sortedData, timeField, amountField);
  const monthlyValues = Object.values(monthlyTotals);
  
  // Trend yönü hesapla
  const trendDirection = calculateTrendDirection(monthlyValues);
  const trendStrength = calculateTrendStrength(monthlyValues);
  const seasonality = detectSeasonality(monthlyTotals);
  
  return {
    direction: trendDirection,
    strength: trendStrength,
    pattern: seasonality.pattern,
    monthlyData: monthlyTotals,
    growthRate: calculateGrowthRate(monthlyValues),
    volatility: calculateVolatility(monthlyValues)
  };
};

/**
 * Kategori dağılım analizi
 */
const analyzeCategoriesDistribution = (data, categoryField, amountField) => {
  const categoryTotals = data.reduce((acc, item) => {
    const category = item[categoryField] || 'Diğer';
    const amount = Number(item[amountField]) || 0;
    
    if (!acc[category]) {
      acc[category] = { total: 0, count: 0, average: 0 };
    }
    
    acc[category].total += amount;
    acc[category].count += 1;
    acc[category].average = acc[category].total / acc[category].count;
    
    return acc;
  }, {});

  // Kategorileri büyükten küçüğe sırala
  const sortedCategories = Object.entries(categoryTotals)
    .sort(([,a], [,b]) => Math.abs(b.total) - Math.abs(a.total))
    .map(([category, stats]) => ({
      category,
      ...stats,
      percentage: (Math.abs(stats.total) / Object.values(categoryTotals).reduce((sum, cat) => sum + Math.abs(cat.total), 0)) * 100
    }));

  return {
    categories: sortedCategories,
    topCategory: sortedCategories[0],
    categoryCount: sortedCategories.length,
    concentration: calculateConcentration(sortedCategories)
  };
};

/**
 * Anomali tespiti
 */
const detectAnomalies = (data, amountField) => {
  const amounts = data.map(item => Number(item[amountField]) || 0);
  const mean = amounts.reduce((sum, val) => sum + val, 0) / amounts.length;
  const stdDev = calculateStandardDeviation(amounts, mean);
  
  const threshold = 2; // 2 standart sapma
  
  return data
    .map((item, index) => {
      const amount = Number(item[amountField]) || 0;
      const zScore = Math.abs((amount - mean) / stdDev);
      
      if (zScore > threshold) {
        return {
          index,
          item,
          amount,
          zScore,
          type: amount > mean ? 'high_outlier' : 'low_outlier',
          severity: zScore > 3 ? 'extreme' : 'moderate'
        };
      }
      return null;
    })
    .filter(Boolean);
};

/**
 * Öngörüler oluştur
 */
const generatePredictions = (data, timeField, amountField) => {
  const sortedData = data
    .filter(item => item[timeField] && item[amountField] != null)
    .sort((a, b) => new Date(a[timeField]) - new Date(b[timeField]));

  if (sortedData.length < 3) {
    return { nextMonth: null, nextQuarter: null, confidence: 'low' };
  }

  const monthlyTotals = groupByMonth(sortedData, timeField, amountField);
  const values = Object.values(monthlyTotals);
  
  // Basit linear regression
  const trend = calculateLinearTrend(values);
  const nextMonthPrediction = trend.slope * values.length + trend.intercept;
  const nextQuarterPrediction = (trend.slope * (values.length + 3) + trend.intercept) * 3;
  
  // Güven seviyesi hesapla
  const confidence = calculatePredictionConfidence(values, trend);
  
  return {
    nextMonth: nextMonthPrediction,
    nextQuarter: nextQuarterPrediction,
    confidence,
    trend: trend.slope > 0 ? 'increasing' : 'decreasing',
    reliability: confidence > 0.7 ? 'high' : confidence > 0.4 ? 'medium' : 'low'
  };
};

/**
 * AI önerileri oluştur
 */
const generateRecommendations = (stats, trends, categories) => {
  const recommendations = [];

  // Gelir-gider dengesi önerisi
  if (stats.negativeTotal > stats.positiveTotal * 0.9) {
    recommendations.push({
      type: 'budget_warning',
      priority: 'high',
      title: 'Bütçe Uyarısı',
      description: 'Giderleriniz gelirinize çok yakın. Harcamalarınızı gözden geçirmeniz önerilir.',
      action: 'Kategori bazında harcama analizi yapın'
    });
  }

  // Trend önerisi
  if (trends.direction === 'decreasing' && trends.strength > 0.5) {
    recommendations.push({
      type: 'trend_warning',
      priority: 'medium',
      title: 'Azalan Trend',
      description: 'Gelirlerinizde azalış trendi gözlemleniyor.',
      action: 'Gelir kaynaklarınızı çeşitlendirmeyi düşünün'
    });
  }

  // Kategori önerisi
  if (categories.concentration > 0.7) {
    recommendations.push({
      type: 'diversification',
      priority: 'low',
      title: 'Çeşitlendirme Önerisi',
      description: 'Harcamalarınız belirli kategorilerde yoğunlaşmış.',
      action: 'Harcama kategorilerinizi çeşitlendirin'
    });
  }

  // Volatilite önerisi
  if (trends.volatility > 0.3) {
    recommendations.push({
      type: 'stability',
      priority: 'medium',
      title: 'Finansal İstikrar',
      description: 'Gelir-gider dengenizde yüksek dalgalanma var.',
      action: 'Acil durum fonu oluşturmayı düşünün'
    });
  }

  return recommendations;
};

/**
 * İçgörüler oluştur
 */
const generateInsights = (stats, trends, categories, anomalies) => {
  const insights = [];

  // Temel istatistik içgörüleri
  insights.push({
    type: 'statistics',
    title: 'Finansal Özet',
    description: `Toplam ${stats.count} işlem analiz edildi. Ortalama işlem tutarı ${stats.average.toFixed(2)} TL.`,
    value: stats.total,
    trend: trends.direction
  });

  // En büyük kategori
  if (categories.topCategory) {
    insights.push({
      type: 'category',
      title: 'En Büyük Kategori',
      description: `${categories.topCategory.category} kategorisi toplam harcamanın %${categories.topCategory.percentage.toFixed(1)}'ini oluşturuyor.`,
      value: categories.topCategory.total,
      percentage: categories.topCategory.percentage
    });
  }

  // Anomali içgörüsü
  if (anomalies.length > 0) {
    insights.push({
      type: 'anomaly',
      title: 'Olağandışı İşlemler',
      description: `${anomalies.length} adet olağandışı işlem tespit edildi.`,
      count: anomalies.length,
      severity: anomalies.filter(a => a.severity === 'extreme').length > 0 ? 'high' : 'medium'
    });
  }

  return insights;
};

/**
 * Özet oluştur
 */
const generateSummary = (stats, trends) => {
  const trendText = trends.direction === 'increasing' ? 'artış' : 
                   trends.direction === 'decreasing' ? 'azalış' : 'stabil';
  
  return `${stats.count} işlem analiz edildi. Toplam tutar ${stats.total.toFixed(2)} TL. ` +
         `Trend: ${trendText} (güç: ${(trends.strength * 100).toFixed(0)}%)`;
};

// Yardımcı fonksiyonlar
const calculateMedian = (values) => {
  const sorted = [...values].sort((a, b) => a - b);
  const mid = Math.floor(sorted.length / 2);
  return sorted.length % 2 === 0 ? (sorted[mid - 1] + sorted[mid]) / 2 : sorted[mid];
};

const calculateStandardDeviation = (values, mean) => {
  const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
  return Math.sqrt(variance);
};

const groupByMonth = (data, timeField, amountField) => {
  return data.reduce((acc, item) => {
    const date = new Date(item[timeField]);
    const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
    
    if (!acc[monthKey]) {
      acc[monthKey] = 0;
    }
    
    acc[monthKey] += Number(item[amountField]) || 0;
    return acc;
  }, {});
};

const calculateTrendDirection = (values) => {
  if (values.length < 2) return 'insufficient_data';
  
  const firstHalf = values.slice(0, Math.floor(values.length / 2));
  const secondHalf = values.slice(Math.floor(values.length / 2));
  
  const firstAvg = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length;
  const secondAvg = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length;
  
  const change = (secondAvg - firstAvg) / Math.abs(firstAvg);
  
  if (Math.abs(change) < 0.05) return 'stable';
  return change > 0 ? 'increasing' : 'decreasing';
};

const calculateTrendStrength = (values) => {
  if (values.length < 2) return 0;
  
  const trend = calculateLinearTrend(values);
  const predictions = values.map((_, i) => trend.slope * i + trend.intercept);
  const errors = values.map((val, i) => Math.abs(val - predictions[i]));
  const meanError = errors.reduce((sum, err) => sum + err, 0) / errors.length;
  const meanValue = values.reduce((sum, val) => sum + Math.abs(val), 0) / values.length;
  
  return Math.max(0, 1 - (meanError / meanValue));
};

const calculateLinearTrend = (values) => {
  const n = values.length;
  const sumX = (n * (n - 1)) / 2;
  const sumY = values.reduce((sum, val) => sum + val, 0);
  const sumXY = values.reduce((sum, val, i) => sum + i * val, 0);
  const sumXX = (n * (n - 1) * (2 * n - 1)) / 6;
  
  const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
  const intercept = (sumY - slope * sumX) / n;
  
  return { slope, intercept };
};

const detectSeasonality = (monthlyData) => {
  // Basit mevsimsellik tespiti
  const months = Object.keys(monthlyData);
  if (months.length < 12) {
    return { pattern: 'insufficient_data', confidence: 0 };
  }
  
  // Bu basit bir implementasyon - gerçek uygulamada daha karmaşık algoritmalar kullanılabilir
  return { pattern: 'none', confidence: 0.5 };
};

const calculateGrowthRate = (values) => {
  if (values.length < 2) return 0;
  return ((values[values.length - 1] - values[0]) / Math.abs(values[0])) * 100;
};

const calculateVolatility = (values) => {
  if (values.length < 2) return 0;
  const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
  const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
  return Math.sqrt(variance) / Math.abs(mean);
};

const calculateConcentration = (categories) => {
  const total = categories.reduce((sum, cat) => sum + Math.abs(cat.total), 0);
  const topThree = categories.slice(0, 3).reduce((sum, cat) => sum + Math.abs(cat.total), 0);
  return total > 0 ? topThree / total : 0;
};

const calculatePredictionConfidence = (values, trend) => {
  const predictions = values.map((_, i) => trend.slope * i + trend.intercept);
  const errors = values.map((val, i) => Math.abs(val - predictions[i]));
  const meanError = errors.reduce((sum, err) => sum + err, 0) / errors.length;
  const meanValue = values.reduce((sum, val) => sum + Math.abs(val), 0) / values.length;
  
  return Math.max(0, 1 - (meanError / meanValue));
};
