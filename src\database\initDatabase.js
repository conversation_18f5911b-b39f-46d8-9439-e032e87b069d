import { formatSQLiteDate } from '../utils/dateFormatters';
import { createBudgetTables, updateBudgetSpentAmounts } from './migrations/budget-system-migration';

/**
 * Veritabanı başlatma ve migrasyon işlemleri
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<void>}
 */
export const initDatabase = async (db) => {
  try {
    console.log('🚀 Veritabanı başlatılıyor...');
    
    // Veritabanı versiyonunu kontrol et
    const { user_version } = await db.getFirstAsync('PRAGMA user_version');
    console.log(`📊 Mevcut veritabanı versiyonu: ${user_version}`);
    
    // WAL modu ve foreign keys'i aktifleştir
    await db.execAsync(`
      PRAGMA journal_mode = WAL;
      PRAGMA foreign_keys = ON;
    `);
    
    // Migration işlemleri
    if (user_version < 1) {
      await performV1Migration(db);
    }
    
    // Budget system migration (version 2)
    if (user_version < 2) {
      await performV2Migration(db);
    }
    
    console.log('✅ Veritabanı başlatma tamamlandı');
  } catch (error) {
    console.error('❌ Veritabanı başlatma hatası:', error);
    throw error;
  }
};

/**
 * Veritabanı V1 migrasyonu
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<void>}
 */
const performV1Migration = async (db) => {
  try {
    console.log('🔧 V1 migrasyonu başlatılıyor...');
    
    // Transaction içinde tablo oluşturma işlemleri
    await db.execAsync(`
      BEGIN TRANSACTION;
      
      -- Tablo oluşturma ve güncelleme işlemleri burada
      
      -- Versiyon güncelleme
      PRAGMA user_version = 1;
      
      COMMIT;
    `);
    
    console.log('✅ V1 migrasyonu tamamlandı');
  } catch (error) {
    console.error('❌ V1 migrasyon hatası:', error);
    await db.execAsync('ROLLBACK');
    throw error;
  }
};

/**
 * Budget system migration (V2)
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<void>}
 */
const performV2Migration = async (db) => {
  try {
    console.log('🏦 V2 migrasyonu başlatılıyor (Budget System)...');
    
    // Create budget tables
    await createBudgetTables(db);
    
    // Update budget spent amounts from existing transactions
    await updateBudgetSpentAmounts(db);
    
    // Update version
    await db.execAsync('PRAGMA user_version = 2');
    
    console.log('✅ V2 migrasyonu tamamlandı (Budget System)');
  } catch (error) {
    console.error('❌ V2 migrasyon hatası:', error);
    throw error;
  }
};
