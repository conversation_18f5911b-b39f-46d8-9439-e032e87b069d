import React from 'react';
import { TouchableOpacity, View, Text, StyleSheet } from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';

/**
 * Özelleştirilmiş checkbox bileşeni
 * @param {Object} props Component props
 * @param {string} props.label Checkbox etiketi
 * @param {boolean} props.value Checkbox değeri
 * @param {Function} props.onValueChange Değer değ<PERSON> callback fonksiyonu
 * @param {boolean} [props.disabled] Devre dışı bırakma durumu
 * @param {Object} [props.style] Özel stil
 * @returns {JSX.Element} CheckBox component
 */
const CheckBox = ({ 
  label, 
  value, 
  onValueChange, 
  disabled = false,
  style 
}) => {
  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={() => !disabled && onValueChange(!value)}
      disabled={disabled}
      activeOpacity={0.7}
    >
      <View style={[
        styles.checkbox,
        value && styles.checked,
        disabled && styles.disabled
      ]}>
        {value && (
          <MaterialCommunityIcons
            name="check"
            size={16}
            color={disabled ? '#999' : '#fff'}
          />
        )}
      </View>
      <Text style={[
        styles.label,
        disabled && styles.disabledText
      ]}>
        {label}
      </Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    marginTop: 8
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#3498db',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
    backgroundColor: '#fff'
  },
  checked: {
    backgroundColor: '#3498db',
    borderColor: '#3498db'
  },
  disabled: {
    borderColor: '#ddd',
    backgroundColor: '#f5f5f5'
  },
  label: {
    fontSize: 16,
    color: '#2c3e50'
  },
  disabledText: {
    color: '#999'
  }
});

export default CheckBox;
