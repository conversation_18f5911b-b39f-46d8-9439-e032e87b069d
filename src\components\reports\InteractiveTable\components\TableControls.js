import React from 'react';
import { View, Text, TouchableOpacity, ScrollView, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../../../context/ThemeContext';

/**
 * <PERSON><PERSON><PERSON> kontrolleri bileşeni - ka<PERSON>me, dı<PERSON><PERSON> aktarma ve diğer e<PERSON>
 */
const TableControls = ({
  onSave,
  onExport,
  onClear,
  onPreview,
  onUndo,
  onRedo,
  canUndo = false,
  canRedo = false,
  hasChanges = false,
  isLoading = false,
  dataCount = 0,
  exportFormats = ['PDF', 'Excel', 'CSV'],
  onOpenAdvancedColumnManager,
  onOpenAdvancedFilterManager,
  onOpenFormulaBuilder
}) => {
  const { theme } = useTheme();

  // Tema güvenlik kontrolü
  if (!theme) {
    return null;
  }

  const styles = {
    container: {
      backgroundColor: theme.SURFACE || theme.colors?.surface || '#f9f9f9',
      borderTopWidth: 1,
      borderTopColor: theme.BORDER || theme.colors?.border || '#e0e0e0',
      padding: 16,
    },
    topRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 12,
    },
    statusContainer: {
      flex: 1,
    },
    statusText: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.TEXT_PRIMARY || theme.colors?.text || '#333',
    },
    statusSubtext: {
      fontSize: 12,
      color: theme.TEXT_SECONDARY || theme.colors?.textSecondary || '#666',
      marginTop: 2,
    },
    saveButton: {
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 20,
      flexDirection: 'row',
      alignItems: 'center',
    },
    saveButtonActive: {
      backgroundColor: theme.SUCCESS || theme.colors?.success || '#28a745',
    },
    saveButtonDisabled: {
      backgroundColor: theme.WARNING || theme.colors?.warning || '#ffc107',
    },
    saveButtonText: {
      marginLeft: 6,
      fontSize: 14,
      fontWeight: '600',
    },
    saveButtonTextActive: {
      color: theme.SURFACE || theme.colors?.surface || '#f9f9f9',
    },
    saveButtonTextDisabled: {
      color: theme.TEXT_SECONDARY || theme.colors?.textSecondary || '#666',
    },
    actionsRow: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
    },
    actionButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 12,
      paddingVertical: 8,
      borderRadius: 8,
      borderWidth: 1,
    },
    actionButtonPrimary: {
      backgroundColor: theme.PRIMARY || theme.colors?.primary || '#007AFF',
    },
    actionButtonSecondary: {
      backgroundColor: theme.SURFACE_VARIANT || theme.colors?.surface || '#f9f9f9',
      borderColor: theme.BORDER || theme.colors?.border || '#e0e0e0',
    },
    actionButtonDanger: {
      backgroundColor: theme.DANGER || theme.colors?.error || '#dc3545',
    },
    actionButtonInfo: {
      backgroundColor: theme.INFO || theme.colors?.info || '#17a2b8',
    },
    actionButtonWarning: {
      backgroundColor: theme.WARNING || theme.colors?.warning || '#ffc107',
    },
    actionButtonSuccess: {
      backgroundColor: theme.SUCCESS || theme.colors?.success || '#28a745',
    },
    actionButtonText: {
      marginLeft: 6,
      fontSize: 12,
      fontWeight: '600',
    },
    actionButtonTextPrimary: {
      color: theme.SURFACE || theme.colors?.surface || '#f9f9f9',
    },
    actionButtonTextSecondary: {
      color: theme.TEXT_PRIMARY || theme.colors?.text || '#333',
    },
    actionButtonTextDanger: {
      color: theme.SURFACE || theme.colors?.surface || '#f9f9f9',
    },
    actionButtonTextInfo: {
      color: theme.SURFACE || theme.colors?.surface || '#f9f9f9',
    },
    actionButtonTextWarning: {
      color: theme.SURFACE || theme.colors?.surface || '#f9f9f9',
    },
    actionButtonTextSuccess: {
      color: theme.SURFACE || theme.colors?.surface || '#f9f9f9',
    },
    exportModal: {
      position: 'absolute',
      top: -160,
      right: 0,
      backgroundColor: theme.SURFACE || theme.colors?.surface || '#f9f9f9',
      borderRadius: 8,
      padding: 8,
      shadowColor: theme.SHADOW || theme.colors?.shadow || '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.15,
      shadowRadius: 8,
      elevation: 5,
      borderWidth: 1,
      borderColor: theme.BORDER || theme.colors?.border || '#e0e0e0',
      minWidth: 120,
    },
    exportOption: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 8,
      borderRadius: 4,
    },
    exportOptionText: {
      marginLeft: 8,
      fontSize: 14,
      color: theme.TEXT_PRIMARY || theme.colors?.text || '#333',
    },
    disabledButton: {
      opacity: 0.5,
    },
    tooltipText: {
      fontSize: 10,
      color: theme.TEXT_SECONDARY || theme.colors?.textSecondary || '#666',
      textAlign: 'center',
      marginTop: 4,
    },
  };

  /**
   * Dışa aktarma modal'ını göster
   */
  const handleExportPress = () => {
    if (!onExport) return;

    Alert.alert(
      'Dışa Aktar',
      'Hangi formatta dışa aktarmak istiyorsunuz?',
      [
        { text: 'İptal', style: 'cancel' },
        ...exportFormats.map(format => ({
          text: format,
          onPress: () => onExport(format.toLowerCase())
        }))
      ]
    );
  };

  /**
   * Kaydetme işlemi
   */
  const handleSavePress = () => {
    if (!onSave || isLoading) return;

    if (hasChanges) {
      onSave();
    } else {
      Alert.alert('Bilgi', 'Kaydedilecek değişiklik bulunmamaktadır.');
    }
  };

  /**
   * Temizleme işlemi
   */
  const handleClearPress = () => {
    if (!onClear) return;

    Alert.alert(
      'Tabloyu Temizle',
      'Tüm yapılandırmaları sıfırlamak istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        { text: 'Temizle', onPress: onClear, style: 'destructive' }
      ]
    );
  };

  return (
    <View style={styles.container}>
      {/* Üst satır - Durum ve Kaydet */}
      <View style={styles.topRow}>
        <View style={styles.statusContainer}>
          <Text style={styles.statusText}>
            📊 Tablo Hazır
          </Text>
          <Text style={styles.statusSubtext}>
            {isLoading 
              ? 'Veriler işleniyor...'
              : hasChanges 
                ? `${dataCount} kayıt - Kaydedilmemiş değişiklikler var`
                : `${dataCount} kayıt - Tüm değişiklikler kaydedildi`
            }
          </Text>
        </View>
        
        <TouchableOpacity
          style={[
            styles.saveButton,
            hasChanges ? styles.saveButtonActive : styles.saveButtonDisabled,
            isLoading && styles.disabledButton
          ]}
          onPress={handleSavePress}
          disabled={isLoading}
        >
          {isLoading ? (
            <Ionicons name="hourglass" size={16} color={theme.TEXT_SECONDARY || theme.colors?.textSecondary || '#666'} />
          ) : (
            <Ionicons 
              name="save" 
              size={16} 
              color={hasChanges ? (theme.SURFACE || theme.colors?.surface || '#f9f9f9') : (theme.TEXT_SECONDARY || theme.colors?.textSecondary || '#666')} 
            />
          )}
          <Text style={[
            styles.saveButtonText,
            hasChanges ? styles.saveButtonTextActive : styles.saveButtonTextDisabled
          ]}>
            {isLoading ? 'Kaydediliyor...' : 'Kaydet'}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Alt satır - Eylemler */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.actionsRow}
      >
        {/* Geri Al */}
        <TouchableOpacity
          style={[
            styles.actionButton,
            styles.actionButtonSecondary,
            !canUndo && styles.disabledButton
          ]}
          onPress={onUndo}
          disabled={!canUndo}
        >
          <Ionicons name="arrow-undo" size={16} color={theme.TEXT_PRIMARY || theme.colors?.text || '#333'} />
          <Text style={[styles.actionButtonText, styles.actionButtonTextSecondary]}>
            Geri Al
          </Text>
        </TouchableOpacity>

        {/* Yinele */}
        <TouchableOpacity
          style={[
            styles.actionButton,
            styles.actionButtonSecondary,
            !canRedo && styles.disabledButton
          ]}
          onPress={onRedo}
          disabled={!canRedo}
        >
          <Ionicons name="arrow-redo" size={16} color={theme.TEXT_PRIMARY || theme.colors?.text || '#333'} />
          <Text style={[styles.actionButtonText, styles.actionButtonTextSecondary]}>
            Yinele
          </Text>
        </TouchableOpacity>

        {/* Önizleme */}
        {onPreview && (
          <TouchableOpacity
            style={[styles.actionButton, styles.actionButtonSecondary]}
            onPress={onPreview}
          >
            <Ionicons name="eye" size={16} color={theme.TEXT_PRIMARY || theme.colors?.text || '#333'} />
            <Text style={[styles.actionButtonText, styles.actionButtonTextSecondary]}>
              Önizleme
            </Text>
          </TouchableOpacity>
        )}

        {/* Gelişmiş Sütun Yöneticisi */}
        {onOpenAdvancedColumnManager && (
          <TouchableOpacity
            style={[styles.actionButton, styles.actionButtonInfo]}
            onPress={onOpenAdvancedColumnManager}
          >
            <Ionicons name="grid" size={16} color={theme.SURFACE || theme.colors?.surface || '#f9f9f9'} />
            <Text style={[styles.actionButtonText, styles.actionButtonTextInfo]}>
              Sütunlar
            </Text>
          </TouchableOpacity>
        )}

        {/* Gelişmiş Filtre Yöneticisi */}
        {onOpenAdvancedFilterManager && (
          <TouchableOpacity
            style={[styles.actionButton, styles.actionButtonWarning]}
            onPress={onOpenAdvancedFilterManager}
          >
            <Ionicons name="filter" size={16} color={theme.SURFACE || theme.colors?.surface || '#f9f9f9'} />
            <Text style={[styles.actionButtonText, styles.actionButtonTextWarning]}>
              Filtreler
            </Text>
          </TouchableOpacity>
        )}

        {/* Formül Editörü */}
        {onOpenFormulaBuilder && (
          <TouchableOpacity
            style={[styles.actionButton, styles.actionButtonSuccess]}
            onPress={onOpenFormulaBuilder}
          >
            <Ionicons name="calculator" size={16} color={theme.SURFACE || theme.colors?.surface || '#f9f9f9'} />
            <Text style={[styles.actionButtonText, styles.actionButtonTextSuccess]}>
              Formüller
            </Text>
          </TouchableOpacity>
        )}

        {/* Temizle */}
        <TouchableOpacity
          style={[styles.actionButton, styles.actionButtonDanger]}
          onPress={handleClearPress}
        >
          <Ionicons name="trash" size={16} color={theme.SURFACE || theme.colors?.surface || '#f9f9f9'} />
          <Text style={[styles.actionButtonText, styles.actionButtonTextDanger]}>
            Temizle
          </Text>
        </TouchableOpacity>

        {/* Dışa Aktar */}
        <TouchableOpacity
          style={[styles.actionButton, styles.actionButtonSecondary]}
          onPress={handleExportPress}
        >
          <Ionicons name="download" size={16} color={theme.TEXT_PRIMARY || theme.colors?.text || '#333'} />
          <Text style={[styles.actionButtonText, styles.actionButtonTextSecondary]}>
            Dışa Aktar
          </Text>
        </TouchableOpacity>

        {/* Ana Kaydet Butonu */}
        <TouchableOpacity
          style={[styles.actionButton, styles.actionButtonPrimary]}
          onPress={handleSavePress}
          disabled={isLoading}
        >
          <Ionicons name="save" size={16} color={theme.SURFACE || theme.colors?.surface || '#f9f9f9'} />
          <Text style={[styles.actionButtonText, styles.actionButtonTextPrimary]}>
            Kaydet
          </Text>
        </TouchableOpacity>
      </ScrollView>

      {/* Tooltip */}
      {!hasChanges && (
        <Text style={styles.tooltipText}>
          💡 Tabloda değişiklik yaptığınızda kaydet butonu aktif olacak
        </Text>
      )}

      {dataCount > 0 && (
        <Text style={styles.tooltipText}>
          📊 Toplam {dataCount} kayıt • {exportFormats.join(', ')} formatlarında dışa aktarılabilir
        </Text>
      )}
    </View>
  );
};

export default TableControls;
