const fs = require('fs');
const path = require('path');

// Renk sabitleri
const colorMappings = {
  'COLORS.primary': 'Colors.PRIMARY',
  'COLORS.primary.main': 'Colors.PRIMARY',
  'COLORS.common.white': '#FFFFFF',
  'COLORS.neutral[50]': '#F9FAFB',
  'COLORS.neutral[400]': '#9CA3AF',
  'COLORS.neutral[500]': '#6B7280',
  'COLORS.neutral[600]': '#4B5563',
  'COLORS.neutral[700]': '#374151',
  'COLORS.neutral[900]': '#111827',
  'COLORS.income.main': '#2ecc71',
  'COLORS.income.light': 'rgba(46, 204, 113, 0.2)',
  'COLORS.expense.main': '#e74c3c',
  'COLORS.expense.light': 'rgba(231, 76, 60, 0.2)',
  'COLORS.text': '#333333',
  'COLORS.textSecondary': '#999999',
  'COLORS.background': '#FFFFFF',
  'COLORS.card': '#F5F5F5'
};

// Dosyaları tarama ve düzeltme fonksiyonu
function processFile(filePath) {
  try {
    // Dosya içeriğini oku
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Import ifadesini değiştir
    if (content.includes("import { COLORS }") || content.includes("import {COLORS}")) {
      content = content.replace(
        /import\s*{\s*COLORS\s*(?:,\s*([^}]*))?}\s*from\s*['"]([^'"]*\/themes)['"];?/g,
        (match, otherImports, importPath) => {
          const newPath = importPath.replace('/themes', '/colors');
          if (otherImports) {
            return `import { ${otherImports} } from '${importPath}';\nimport { Colors } from '${newPath}';`;
          } else {
            return `import { Colors } from '${newPath}';`;
          }
        }
      );
    }
    
    // COLORS referanslarını değiştir
    for (const [oldColor, newColor] of Object.entries(colorMappings)) {
      const regex = new RegExp(oldColor.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
      content = content.replace(regex, newColor);
    }
    
    // Değişiklikleri kaydet
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`Fixed: ${filePath}`);
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error);
  }
}

// Klasörü tarama fonksiyonu
function scanDirectory(directory) {
  const files = fs.readdirSync(directory);
  
  for (const file of files) {
    const filePath = path.join(directory, file);
    const stats = fs.statSync(filePath);
    
    if (stats.isDirectory()) {
      scanDirectory(filePath); // Alt klasörleri de tara
    } else if (stats.isFile() && file.endsWith('.js')) {
      processFile(filePath);
    }
  }
}

// src klasörünü tara
scanDirectory('./src');

console.log('All files processed successfully!');
