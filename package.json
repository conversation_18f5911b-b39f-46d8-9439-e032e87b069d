{"name": "maas-sqlite-expo", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "clean": "rm -rf node_modules && npm cache clean --force", "reset": "npm run clean && npm install"}, "dependencies": {"@expo/vector-icons": "^14.0.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.4.1", "@react-native-picker/picker": "2.11.1", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@react-navigation/stack": "^6.3.20", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "expo": "53.0.17", "expo-background-fetch": "~13.1.6", "expo-blur": "^14.1.5", "expo-constants": "~17.1.5", "expo-crypto": "~14.1.5", "expo-document-picker": "~13.1.6", "expo-file-system": "~18.1.9", "expo-font": "~13.3.1", "expo-linear-gradient": "^14.1.5", "expo-linking": "~7.1.7", "expo-local-authentication": "~16.0.5", "expo-mail-composer": "^14.1.5", "expo-notifications": "~0.31.4", "expo-print": "^14.1.4", "expo-secure-store": "~14.2.3", "expo-sharing": "^13.1.5", "expo-splash-screen": "~0.30.10", "expo-sqlite": "~15.2.13", "expo-status-bar": "~2.2.3", "expo-task-manager": "~13.1.6", "memory-bank-mcp": "^1.0.0", "metro": "^0.82.0", "metro-resolver": "0.80.0", "react": "19.0.0", "react-native": "0.79.5", "react-native-calendars": "^1.1313.0", "react-native-chart-kit": "^6.12.0", "react-native-draggable-flatlist": "^4.0.1", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "~2.24.0", "react-native-html-to-pdf": "^0.12.0", "react-native-print": "^0.11.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-share": "^12.1.0", "react-native-svg": "15.11.2", "react-native-view-shot": "^4.0.3", "react-native-wheel-color-picker": "^1.3.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "babel-plugin-module-resolver": "^5.0.0", "typescript": "^5.3.3"}, "resolutions": {"metro": "0.80.0", "metro-resolver": "0.80.0", "@types/react": "^19.0.0"}, "private": true, "expo": {"install": {"exclude": ["react-native@~0.76.6", "react-native-reanimated@~3.16.1", "react-native-gesture-handler@~2.20.0", "react-native-screens@~4.4.0", "react-native-safe-area-context@~4.12.0"]}}}