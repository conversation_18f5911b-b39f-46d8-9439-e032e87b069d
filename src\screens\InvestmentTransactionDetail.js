import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import * as investmentService from '../services/investmentService';
import { formatCurrency } from '../utils/formatters';
import InvestmentTransactionForm from './InvestmentTransactionForm';

/**
 * Yatırım işlemi detay ekranı
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @param {Object} props.route - Route nesnesi
 * @param {Object} props.route.params - Route parametreleri
 * @param {number} props.route.params.transactionId - İşlem ID'si
 * @returns {JSX.Element} Yatırım işlemi detay ekranı
 */
export default function InvestmentTransactionDetail({ navigation, route }) {
  const { transactionId } = route.params;
  const db = useSQLiteContext();
  const insets = useSafeAreaInsets();
  
  // Durum değişkenleri
  const [loading, setLoading] = useState(true);
  const [transaction, setTransaction] = useState(null);
  const [asset, setAsset] = useState(null);
  const [showTransactionForm, setShowTransactionForm] = useState(false);
  
  // Verileri yükle
  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      
      // İşlem bilgilerini getir
      const transactionData = await db.getFirstAsync(`
        SELECT t.*, a.name as asset_name, a.symbol as asset_symbol, a.type as asset_type, a.icon, a.color
        FROM investment_transactions t
        JOIN investment_assets a ON t.asset_id = a.id
        WHERE t.id = ?
      `, [transactionId]);
      
      if (!transactionData) {
        Alert.alert('Hata', 'İşlem bulunamadı.');
        navigation.goBack();
        return;
      }
      
      setTransaction(transactionData);
      
      // Varlık bilgilerini getir
      const assetData = await db.getFirstAsync(`
        SELECT * FROM investment_assets
        WHERE id = ?
      `, [transactionData.asset_id]);
      
      setAsset(assetData);
      
      setLoading(false);
    } catch (error) {
      console.error('İşlem detayları yükleme hatası:', error);
      Alert.alert('Hata', 'İşlem detayları yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  }, [db, transactionId]);
  
  // İlk yükleme
  useEffect(() => {
    loadData();
  }, [loadData]);
  
  // İşlemi düzenle
  const editTransaction = () => {
    setShowTransactionForm(true);
  };
  
  // İşlemi sil
  const deleteTransaction = async () => {
    Alert.alert(
      'İşlemi Sil',
      'Bu işlemi silmek istediğinize emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await investmentService.deleteInvestmentTransaction(transactionId);
              navigation.goBack();
            } catch (error) {
              console.error('İşlem silme hatası:', error);
              Alert.alert('Hata', 'İşlem silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };
  
  // İşlem tipini formatla
  const formatTransactionType = (type) => {
    switch (type) {
      case 'buy': return 'Alım';
      case 'sell': return 'Satım';
      case 'dividend': return 'Temettü';
      case 'interest': return 'Faiz';
      case 'fee': return 'Komisyon/Ücret';
      case 'transfer': return 'Transfer';
      case 'other': return 'Diğer';
      default: return type;
    }
  };
  
  // İşlem tipine göre renk
  const getTransactionTypeColor = (type) => {
    switch (type) {
      case 'buy': return Colors.expense.main;
      case 'sell': return Colors.income.main;
      case 'dividend': return Colors.income.main;
      case 'interest': return Colors.income.main;
      case 'fee': return Colors.expense.main;
      case 'transfer': return Colors.PRIMARY;
      case 'other': return Colors.GRAY_600;
      default: return Colors.GRAY_600;
    }
  };
  
  // İşlem tipine göre ikon
  const getTransactionTypeIcon = (type) => {
    switch (type) {
      case 'buy': return 'arrow-downward';
      case 'sell': return 'arrow-upward';
      case 'dividend': return 'payments';
      case 'interest': return 'payments';
      case 'fee': return 'money-off';
      case 'transfer': return 'swap-horiz';
      case 'other': return 'more-horiz';
      default: return 'more-horiz';
    }
  };
  
  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          İşlem Detayı
        </Text>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={editTransaction}
          >
            <MaterialIcons name="edit" size={24} color={Colors.PRIMARY} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={deleteTransaction}
          >
            <MaterialIcons name="delete" size={24} color={Colors.expense.main} />
          </TouchableOpacity>
        </View>
      </View>
      
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.PRIMARY} />
          <Text style={styles.loadingText}>Yükleniyor...</Text>
        </View>
      ) : (
        <ScrollView style={styles.scrollView}>
          {/* İşlem Özeti */}
          <View style={styles.transactionSummary}>
            <View style={[
              styles.transactionTypeIconContainer,
              { backgroundColor: getTransactionTypeColor(transaction.type) }
            ]}>
              <MaterialIcons 
                name={getTransactionTypeIcon(transaction.type)} 
                size={32} 
                color="#fff" 
              />
            </View>
            <Text style={styles.transactionTypeText}>
              {formatTransactionType(transaction.type)}
            </Text>
            <Text style={styles.transactionAmount}>
              {formatCurrency(transaction.total_amount, transaction.currency || 'TRY')}
            </Text>
            <Text style={styles.transactionDate}>
              {new Date(transaction.date).toLocaleDateString('tr-TR', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </Text>
          </View>
          
          {/* Varlık Bilgisi */}
          <View style={styles.detailCard}>
            <Text style={styles.cardTitle}>Varlık Bilgisi</Text>
            
            <TouchableOpacity
              style={styles.assetRow}
              onPress={() => navigation.navigate('InvestmentAssetDetail', { assetId: asset.id })}
            >
              <View style={styles.assetInfo}>
                <View style={[
                  styles.assetIconContainer,
                  { backgroundColor: asset.color || Colors.GRAY_100 }
                ]}>
                  <MaterialIcons 
                    name={asset.icon || 'category'} 
                    size={20} 
                    color="#fff" 
                  />
                </View>
                <View>
                  <Text style={styles.assetName}>{asset.name}</Text>
                  <Text style={styles.assetSymbol}>{asset.symbol}</Text>
                </View>
              </View>
              <MaterialIcons name="chevron-right" size={24} color="#999" />
            </TouchableOpacity>
          </View>
          
          {/* İşlem Detayları */}
          <View style={styles.detailCard}>
            <Text style={styles.cardTitle}>İşlem Detayları</Text>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>İşlem Tipi</Text>
              <Text style={styles.detailValue}>
                {formatTransactionType(transaction.type)}
              </Text>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Miktar</Text>
              <Text style={styles.detailValue}>
                {transaction.quantity.toFixed(asset.type === 'crypto' ? 8 : 2)} {asset.symbol}
              </Text>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Birim Fiyat</Text>
              <Text style={styles.detailValue}>
                {formatCurrency(transaction.price_per_unit, transaction.currency || 'TRY')}
              </Text>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Komisyon/Ücret</Text>
              <Text style={styles.detailValue}>
                {formatCurrency(transaction.fee || 0, transaction.currency || 'TRY')}
              </Text>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Toplam Tutar</Text>
              <Text style={styles.detailValue}>
                {formatCurrency(transaction.total_amount, transaction.currency || 'TRY')}
              </Text>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Para Birimi</Text>
              <Text style={styles.detailValue}>
                {transaction.currency || 'TRY'}
              </Text>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Tarih</Text>
              <Text style={styles.detailValue}>
                {new Date(transaction.date).toLocaleDateString('tr-TR')}
              </Text>
            </View>
            
            {transaction.exchange_rate && (
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Döviz Kuru</Text>
                <Text style={styles.detailValue}>
                  {transaction.exchange_rate.toFixed(6)}
                </Text>
              </View>
            )}
            
            {transaction.preferred_currency && (
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Tercih Edilen Para Birimi</Text>
                <Text style={styles.detailValue}>
                  {formatCurrency(transaction.preferred_currency_amount, transaction.preferred_currency)}
                </Text>
              </View>
            )}
          </View>
          
          {/* Notlar */}
          {transaction.notes && (
            <View style={styles.detailCard}>
              <Text style={styles.cardTitle}>Notlar</Text>
              <Text style={styles.notesText}>{transaction.notes}</Text>
            </View>
          )}
          
          {/* Metadata */}
          {transaction.metadata && (
            <View style={styles.detailCard}>
              <Text style={styles.cardTitle}>Ek Bilgiler</Text>
              <Text style={styles.metadataText}>
                {typeof transaction.metadata === 'string' 
                  ? transaction.metadata 
                  : JSON.stringify(transaction.metadata, null, 2)}
              </Text>
            </View>
          )}
        </ScrollView>
      )}
      
      {/* İşlem Formu */}
      <InvestmentTransactionForm
        visible={showTransactionForm}
        onClose={() => setShowTransactionForm(false)}
        onSave={loadData}
        transaction={transaction}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
    textAlign: 'center',
  },
  headerActions: {
    flexDirection: 'row',
  },
  headerButton: {
    padding: 4,
    marginLeft: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  scrollView: {
    flex: 1,
  },
  transactionSummary: {
    backgroundColor: '#fff',
    padding: 24,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  transactionTypeIconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  transactionTypeText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  transactionAmount: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  transactionDate: {
    fontSize: 16,
    color: '#666',
  },
  detailCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    margin: 16,
    marginBottom: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  assetRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  assetInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  assetIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  assetName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  assetSymbol: {
    fontSize: 14,
    color: '#666',
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  detailLabel: {
    fontSize: 16,
    color: '#666',
  },
  detailValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  notesText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
  },
  metadataText: {
    fontSize: 14,
    color: '#666',
    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
  },
});
