/**
 * Basitleştirilmiş Döviz Kuru Servisi
 * API: https://api.exchangerate-api.com/v4/latest/TRY
 */

/**
 * Döviz kurlarını API'den çeker
 *
 * @param {string} baseCurrency - Baz para birimi (varsayılan: TRY)
 * @returns {Promise<Object>} Döviz kurları
 */
export const fetchExchangeRates = async (baseCurrency = 'TRY') => {
  try {
    // API'den döviz kurlarını çek
    const response = await fetch(`https://api.exchangerate-api.com/v4/latest/${baseCurrency}`);

    if (!response.ok) {
      throw new Error(`Döviz kurları alınamadı: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Döviz kuru servisi hatası:', error);
    throw error;
  }
};

/**
 * Döviz kurlarını veritabanına kaydeder
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} data - API'den gelen döviz kuru verisi
 * @returns {Promise<void>}
 */
export const saveExchangeRates = async (db, data) => {
  try {
    if (!data || !data.base || !data.date || !data.rates) {
      throw new Error('Geçersiz döviz kuru verisi');
    }

    const { base, date, rates } = data;

    // Veritabanı tablosunu kontrol et ve gerekirse oluştur
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS simple_exchange_rates (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        base_currency TEXT NOT NULL,
        target_currency TEXT NOT NULL,
        rate DECIMAL(20,10) NOT NULL,
        date TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // İndeks oluştur
    await db.execAsync(`
      CREATE INDEX IF NOT EXISTS idx_simple_exchange_rates
      ON simple_exchange_rates (base_currency, date, target_currency)
    `);

    // İşlemi transaction olmadan yap (çünkü zaten bir transaction içinde olabilir)
    try {
      // Önce eski kurları temizle
      await db.runAsync(`
        DELETE FROM simple_exchange_rates
        WHERE base_currency = ? AND date = ?
      `, [base, date]);

      // Yeni kurları ekle
      for (const [currency, rate] of Object.entries(rates)) {
        await db.runAsync(`
          INSERT INTO simple_exchange_rates (
            base_currency, target_currency, rate, date
          )
          VALUES (?, ?, ?, ?)
        `, [base, currency, rate, date]);
      }
    } catch (error) {
      console.error('Simple exchange rates save error:', error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Döviz kurları kaydetme hatası:', error);
    throw error;
  }
};

/**
 * Döviz kurlarını veritabanından alır
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {string} baseCurrency - Baz para birimi
 * @param {string} date - Tarih (YYYY-MM-DD)
 * @returns {Promise<Object>} Döviz kurları
 */
export const getExchangeRates = async (db, baseCurrency = 'TRY', date = null) => {
  try {
    // Tarih belirtilmemişse bugünün tarihini kullan
    const targetDate = date || new Date().toISOString().split('T')[0];

    // Veritabanı tablosunu kontrol et
    const tableExists = await db.getFirstAsync(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='simple_exchange_rates'
    `);

    if (!tableExists) {
      // Tablo yoksa API'den al ve kaydet
      const data = await fetchExchangeRates(baseCurrency);
      await saveExchangeRates(db, data);
      return data.rates;
    }

    // Veritabanından kurları al
    const rates = await db.getAllAsync(`
      SELECT target_currency, rate
      FROM simple_exchange_rates
      WHERE base_currency = ? AND date = ?
    `, [baseCurrency, targetDate]);

    // Sonuçları nesne formatına dönüştür
    const ratesObject = {};
    rates.forEach(rate => {
      ratesObject[rate.target_currency] = rate.rate;
    });

    // Eğer kur bulunamadıysa API'den al ve kaydet
    if (Object.keys(ratesObject).length === 0) {
      const data = await fetchExchangeRates(baseCurrency);
      await saveExchangeRates(db, data);
      return data.rates;
    }

    return ratesObject;
  } catch (error) {
    console.error('Döviz kurları getirme hatası:', error);
    // Hata durumunda boş nesne döndür
    return {};
  }
};

/**
 * Belirli bir para biriminden diğerine dönüşüm yapar
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} amount - Miktar
 * @param {string} fromCurrency - Kaynak para birimi
 * @param {string} toCurrency - Hedef para birimi
 * @param {string} date - Tarih (YYYY-MM-DD)
 * @returns {Promise<number>} Dönüştürülmüş miktar
 */
export const convertCurrency = async (db, amount, fromCurrency, toCurrency, date = null) => {
  try {
    // Parametreleri kontrol et
    if (amount == null || isNaN(amount)) {
      return 0;
    }

    // Aynı para birimi ise doğrudan döndür
    if (fromCurrency === toCurrency) {
      return amount;
    }

    // Tarih belirtilmemişse bugünün tarihini kullan
    const targetDate = date || new Date().toISOString().split('T')[0];

    // Kaynak para birimi için kurları al
    const fromRates = await getExchangeRates(db, fromCurrency, targetDate);

    // Hedef para birimi için dönüşüm yap
    if (fromRates[toCurrency]) {
      return amount * fromRates[toCurrency];
    }

    // Eğer doğrudan dönüşüm yoksa, TRY üzerinden dönüşüm yap
    const tryRates = await getExchangeRates(db, 'TRY', targetDate);

    if (tryRates[fromCurrency] && tryRates[toCurrency]) {
      const amountInTRY = amount / tryRates[fromCurrency];
      return amountInTRY * tryRates[toCurrency];
    }

    // Dönüşüm yapılamadığında orijinal miktarı döndür
    return amount;
  } catch (error) {
    console.error('Para birimi dönüştürme hatası:', error);
    // Hata durumunda orijinal miktarı döndür
    return amount;
  }
};

/**
 * Para birimini formatlar
 *
 * @param {number} amount - Miktar
 * @param {string} currency - Para birimi
 * @returns {string} Formatlanmış para birimi
 */
export const formatCurrency = (amount, currency = 'TRY') => {
  if (amount == null) {
    return '';
  }

  try {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  } catch (error) {
    // Hata durumunda basit formatlama yap
    return `${amount.toFixed(2)} ${currency}`;
  }
};

/**
 * Varsayılan para birimini getirir
 *
 * @returns {string} Varsayılan para birimi kodu
 */
export const getDefaultCurrency = () => 'TRY';

/**
 * Döviz kurlarını günceller
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Array<string>} currencies - Güncellenecek para birimleri
 * @returns {Promise<Object>} Güncellenen kurlar
 */
export const updateExchangeRates = async (db, currencies = ['TRY', 'USD', 'EUR']) => {
  try {
    const results = {};

    for (const currency of currencies) {
      const data = await fetchExchangeRates(currency);
      await saveExchangeRates(db, data);
      results[currency] = data.rates;
    }

    return results;
  } catch (error) {
    console.error('Döviz kurları güncelleme hatası:', error);
    throw error;
  }
};

/**
 * Son güncelleme tarihini döndürür
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {string} baseCurrency - Baz para birimi
 * @returns {Promise<string>} Son güncelleme tarihi
 */
export const getLastUpdateDate = async (db, baseCurrency = 'TRY') => {
  try {
    // Veritabanı tablosunu kontrol et
    const tableExists = await db.getFirstAsync(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='simple_exchange_rates'
    `);

    if (!tableExists) {
      return null;
    }

    const result = await db.getFirstAsync(`
      SELECT MAX(created_at) as last_update
      FROM simple_exchange_rates
      WHERE base_currency = ?
    `, [baseCurrency]);

    return result ? result.last_update : null;
  } catch (error) {
    console.error('Son güncelleme tarihi getirme hatası:', error);
    return null;
  }
};

/**
 * Desteklenen para birimlerini getirir
 *
 * @returns {Array<Object>} Para birimleri listesi
 */
export const getSupportedCurrencies = () => [
  { code: 'TRY', name: 'Türk Lirası' },
  { code: 'USD', name: 'Amerikan Doları' },
  { code: 'EUR', name: 'Euro' },
  { code: 'GBP', name: 'İngiliz Sterlini' },
  { code: 'JPY', name: 'Japon Yeni' },
  { code: 'CHF', name: 'İsviçre Frangı' },
  { code: 'CAD', name: 'Kanada Doları' },
  { code: 'AUD', name: 'Avustralya Doları' },
  { code: 'CNY', name: 'Çin Yuanı' },
  { code: 'RUB', name: 'Rus Rublesi' },
  { code: 'KRW', name: 'Güney Kore Wonu' },
  { code: 'INR', name: 'Hindistan Rupisi' },
  { code: 'BRL', name: 'Brezilya Reali' },
  { code: 'MXN', name: 'Meksika Pesosu' },
  { code: 'ZAR', name: 'Güney Afrika Randı' }
];
