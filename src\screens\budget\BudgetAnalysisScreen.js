/**
 * Bütçe Analiz Ekranı
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md - Stage 4, Phase 3
 * 
 * Kapsamlı bütçe analizi ve raporlama ekranı
 * Maksimum 300 satır - Ana ekran yönetimi
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  StatusBar,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Text,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { useSQLiteContext } from 'expo-sqlite';
import { useTheme } from '../../context/ThemeContext';

// Import analysis components
import {
  BudgetPerformanceChart,
  CategoryAnalysisChart,
  TrendAnalysis,
  SavingsCalculator,
  BudgetSummaryReport,
  CategoryDetailReport,
  ComparativeReport
} from '../../components/budget/Analysis';

/**
 * Bütçe analiz ekranı
 * @param {Object} props - Component props
 * @param {Object} props.navigation - Navigation objesi
 * @param {Object} props.route - Route objesi
 */
const BudgetAnalysisScreen = ({ navigation, route }) => {
  const db = useSQLiteContext();
  const { theme, isDarkMode } = useTheme();

  // State yönetimi
  const [analysisData, setAnalysisData] = useState({
    performanceData: [],
    categoryData: [],
    trendData: [],
    savingsData: {},
    reportData: {},
    comparisonData: []
  });
  const [selectedView, setSelectedView] = useState('overview');
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  /**
   * Analiz verilerini yükle
   */
  const loadAnalysisData = async () => {
    try {
      setLoading(true);

      // Mock data - gerçek implementasyonda database'den gelecek
      const mockAnalysisData = {
        performanceData: [
          { period: '2024-01', target: 10000, actual: 8500, score: 85 },
          { period: '2024-02', target: 10000, actual: 9200, score: 78 },
          { period: '2024-03', target: 10000, actual: 7800, score: 92 },
          { period: '2024-04', target: 10000, actual: 8900, score: 81 },
          { period: '2024-05', target: 10000, actual: 9500, score: 75 },
          { period: '2024-06', target: 10000, actual: 8200, score: 88 }
        ],
        categoryData: [
          { id: '1', name: 'Yemek', amount: 2500, budget: 3000, icon: 'restaurant', color: '#FF6B6B' },
          { id: '2', name: 'Ulaşım', amount: 1500, budget: 1500, icon: 'directions-car', color: '#4ECDC4' },
          { id: '3', name: 'Eğlence', amount: 2200, budget: 2000, icon: 'movie', color: '#45B7D1' },
          { id: '4', name: 'Alışveriş', amount: 1800, budget: 2000, icon: 'shopping-bag', color: '#96CEB4' }
        ],
        trendData: [
          { period: '2024-01', amount: 8500, change: 0, prediction: 8800 },
          { period: '2024-02', amount: 9200, change: 8.2, prediction: 9000 },
          { period: '2024-03', amount: 7800, change: -15.2, prediction: 8500 },
          { period: '2024-04', amount: 8900, change: 14.1, prediction: 8700 },
          { period: '2024-05', amount: 9500, change: 6.7, prediction: 9200 },
          { period: '2024-06', amount: 8200, change: -13.7, prediction: 8800 }
        ],
        savingsData: {
          totalIncome: 15000,
          totalExpenses: 8200,
          currentSavings: 25000,
          savingsGoals: [
            { name: 'Acil Durum Fonu', target: 50000, timeframe: 12 },
            { name: 'Tatil', target: 15000, timeframe: 6 },
            { name: 'Araba', target: 100000, timeframe: 24 }
          ]
        },
        reportData: {
          totalBudget: 10000,
          totalSpent: 8200,
          totalRemaining: 1800,
          budgetUtilization: 82,
          performanceScore: 85,
          categoriesCount: 4,
          overBudgetCategories: 1,
          topCategories: [
            { id: '1', name: 'Yemek', amount: 2500, percentage: 30.5, icon: 'restaurant' },
            { id: '3', name: 'Eğlence', amount: 2200, percentage: 26.8, icon: 'movie' },
            { id: '4', name: 'Alışveriş', amount: 1800, percentage: 22.0, icon: 'shopping-bag' },
            { id: '2', name: 'Ulaşım', amount: 1500, percentage: 18.3, icon: 'directions-car' }
          ],
          monthlyComparison: {
            previousMonth: '2024-05',
            spendingChange: -13.7,
            savingsChange: 15.2
          },
          insights: [
            { type: 'warning', message: 'Eğlence kategorisinde bütçe aşımı tespit edildi.' },
            { type: 'info', message: 'Bu ay geçen aya göre %13.7 daha az harcama yaptınız.' }
          ]
        },
        comparisonData: [
          { period: '2024-06', data: { totalSpent: 8200, totalBudget: 10000, totalSavings: 1800, utilizationRate: 82 } },
          { period: '2024-05', data: { totalSpent: 9500, totalBudget: 10000, totalSavings: 500, utilizationRate: 95 } },
          { period: '2024-04', data: { totalSpent: 8900, totalBudget: 10000, totalSavings: 1100, utilizationRate: 89 } },
          { period: '2024-03', data: { totalSpent: 7800, totalBudget: 10000, totalSavings: 2200, utilizationRate: 78 } },
          { period: '2024-02', data: { totalSpent: 9200, totalBudget: 10000, totalSavings: 800, utilizationRate: 92 } },
          { period: '2024-01', data: { totalSpent: 8500, totalBudget: 10000, totalSavings: 1500, utilizationRate: 85 } }
        ]
      };

      setAnalysisData(mockAnalysisData);

    } catch (error) {
      console.error('Analiz verileri yüklenirken hata:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Component mount edildiğinde verileri yükle
  useEffect(() => {
    loadAnalysisData();
  }, []);

  /**
   * Yenileme işleyicisi
   */
  const handleRefresh = () => {
    setRefreshing(true);
    loadAnalysisData();
  };

  /**
   * Görünüm seçenekleri
   */
  const viewOptions = [
    { key: 'overview', label: 'Genel Bakış', icon: 'dashboard' },
    { key: 'performance', label: 'Performans', icon: 'trending-up' },
    { key: 'categories', label: 'Kategoriler', icon: 'pie-chart' },
    { key: 'trends', label: 'Trendler', icon: 'show-chart' },
    { key: 'savings', label: 'Tasarruf', icon: 'savings' },
    { key: 'comparison', label: 'Karşılaştırma', icon: 'compare' }
  ];

  /**
   * Kategori tıklama işleyicisi
   * @param {Object} category - Kategori objesi
   */
  const handleCategoryPress = (category) => {
    setSelectedCategory(category);
    setSelectedView('categoryDetail');
  };

  /**
   * Görünüm render fonksiyonu
   */
  const renderView = () => {
    switch (selectedView) {
      case 'overview':
        return (
          <BudgetSummaryReport
            reportData={analysisData.reportData}
            currency="TRY"
            period="Haziran 2024"
            theme={theme}
          />
        );

      case 'performance':
        return (
          <BudgetPerformanceChart
            performanceData={analysisData.performanceData}
            currency="TRY"
            period="monthly"
            theme={theme}
          />
        );

      case 'categories':
        return (
          <CategoryAnalysisChart
            categoryData={analysisData.categoryData}
            currency="TRY"
            showBudgetComparison={true}
            onCategoryPress={handleCategoryPress}
            theme={theme}
          />
        );

      case 'trends':
        return (
          <TrendAnalysis
            trendData={analysisData.trendData}
            currency="TRY"
            trendType="spending"
            insights={{
              direction: 'down',
              strength: 'moderate',
              forecast: 8800
            }}
            theme={theme}
          />
        );

      case 'savings':
        return (
          <SavingsCalculator
            totalIncome={analysisData.savingsData.totalIncome}
            totalExpenses={analysisData.savingsData.totalExpenses}
            currentSavings={analysisData.savingsData.currentSavings}
            currency="TRY"
            savingsGoals={analysisData.savingsData.savingsGoals}
            theme={theme}
          />
        );

      case 'comparison':
        return (
          <ComparativeReport
            comparisonData={analysisData.comparisonData}
            currency="TRY"
            comparisonType="monthly"
            theme={theme}
          />
        );

      case 'categoryDetail':
        return selectedCategory ? (
          <CategoryDetailReport
            categoryData={{
              ...selectedCategory,
              utilizationRate: (selectedCategory.amount / selectedCategory.budget) * 100,
              transactionCount: 15,
              averageTransaction: selectedCategory.amount / 15,
              largestTransaction: selectedCategory.amount * 0.3,
              dailyAverage: selectedCategory.amount / 30,
              weeklyTrend: Math.random() * 20 - 10,
              monthlyComparison: Math.random() * 30 - 15
            }}
            transactions={[
              { id: '1', description: 'Market alışverişi', amount: 150, date: '2024-06-15', icon: 'shopping-cart' },
              { id: '2', description: 'Restoran', amount: 280, date: '2024-06-14', icon: 'restaurant' },
              { id: '3', description: 'Kafe', amount: 45, date: '2024-06-13', icon: 'local-cafe' }
            ]}
            currency="TRY"
            period="Haziran 2024"
            theme={theme}
          />
        ) : null;

      default:
        return null;
    }
  };

  return (
    <SafeAreaView 
      style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
      edges={['top']}
    >
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor={theme.BACKGROUND}
      />

      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.SURFACE }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color={theme.TEXT_PRIMARY} />
        </TouchableOpacity>
        
        <Text style={[styles.headerTitle, { color: theme.TEXT_PRIMARY }]}>
          Bütçe Analizi
        </Text>
        
        <TouchableOpacity style={styles.menuButton}>
          <MaterialIcons name="more-vert" size={24} color={theme.TEXT_PRIMARY} />
        </TouchableOpacity>
      </View>

      {/* Görünüm seçici */}
      <View style={[styles.viewSelector, { backgroundColor: theme.SURFACE }]}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles.viewButtons}>
            {viewOptions.map((option) => (
              <TouchableOpacity
                key={option.key}
                style={[
                  styles.viewButton,
                  {
                    backgroundColor: selectedView === option.key 
                      ? theme.PRIMARY 
                      : 'transparent',
                  }
                ]}
                onPress={() => setSelectedView(option.key)}
                activeOpacity={0.7}
              >
                <MaterialIcons 
                  name={option.icon} 
                  size={16} 
                  color={selectedView === option.key ? theme.WHITE : theme.TEXT_SECONDARY} 
                />
                <Text style={[
                  styles.viewButtonText,
                  { 
                    color: selectedView === option.key 
                      ? theme.WHITE 
                      : theme.TEXT_SECONDARY 
                  }
                ]}>
                  {option.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
      </View>

      {/* Ana içerik */}
      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.PRIMARY]}
            tintColor={theme.PRIMARY}
          />
        }
      >
        {renderView()}
      </ScrollView>

      {/* Kategori detayından geri dönüş butonu */}
      {selectedView === 'categoryDetail' && (
        <TouchableOpacity
          style={[styles.backToCategories, { backgroundColor: theme.PRIMARY }]}
          onPress={() => setSelectedView('categories')}
        >
          <MaterialIcons name="arrow-back" size={20} color={theme.WHITE} />
          <Text style={[styles.backToCategoriesText, { color: theme.WHITE }]}>
            Kategorilere Dön
          </Text>
        </TouchableOpacity>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
    textAlign: 'center',
  },
  menuButton: {
    padding: 8,
  },
  viewSelector: {
    paddingVertical: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 1,
  },
  viewButtons: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    gap: 8,
  },
  viewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    gap: 6,
  },
  viewButtonText: {
    fontSize: 12,
    fontWeight: '500',
  },
  content: {
    flex: 1,
  },
  backToCategories: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 25,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    gap: 8,
  },
  backToCategoriesText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default BudgetAnalysisScreen;
