/**
 * Bütçe Analiz Ekranı
 * Basit ve çalışır bir bütçe analiz görünümü
 */
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useSQLiteContext } from 'expo-sqlite';
import { useTheme } from '../../context/ThemeContext';
import { MaterialIcons } from '@expo/vector-icons';
import { getBudgetById } from '../../services/budget';

const { width } = Dimensions.get('window');

/**
 * Bütçe analiz ekranı
 */
const BudgetAnalysisScreen = ({ navigation, route }) => {
  const db = useSQLiteContext();
  const { theme } = useTheme();
  const { budgetId } = route.params || {};

  const [loading, setLoading] = useState(true);
  const [budget, setBudget] = useState(null);
  const [analysis, setAnalysis] = useState({
    totalSpent: 0,
    remainingAmount: 0,
    percentageUsed: 0,
    daysRemaining: 0,
    averageDailySpending: 0,
    recommendedDailySpending: 0,
  });

  // Get theme-safe styles
  const getSafeThemeValue = (path, fallback) => {
    return theme?.colors?.[path] || theme?.[path] || fallback;
  };

  // Load budget data and analysis
  useEffect(() => {
    if (budgetId) {
      loadBudgetAnalysis();
    }
  }, [budgetId]);

  const loadBudgetAnalysis = async () => {
    try {
      setLoading(true);
      const budgetData = await getBudgetById(db, budgetId);
      setBudget(budgetData);

      // Simple analysis calculation
      const totalSpent = budgetData.spent_amount || 0;
      const totalLimit = budgetData.total_limit || 0;
      const remainingAmount = Math.max(0, totalLimit - totalSpent);
      const percentageUsed = totalLimit > 0 ? (totalSpent / totalLimit) * 100 : 0;

      // Calculate days remaining
      const startDate = new Date(budgetData.start_date);
      const endDate = budgetData.end_date 
        ? new Date(budgetData.end_date) 
        : new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0);
      const today = new Date();
      const daysRemaining = Math.max(0, Math.ceil((endDate - today) / (1000 * 60 * 60 * 24)));

      // Calculate spending averages
      const totalDays = Math.ceil((today - startDate) / (1000 * 60 * 60 * 24)) || 1;
      const averageDailySpending = totalSpent / totalDays;
      const recommendedDailySpending = daysRemaining > 0 ? remainingAmount / daysRemaining : 0;

      setAnalysis({
        totalSpent,
        remainingAmount,
        percentageUsed,
        daysRemaining,
        averageDailySpending,
        recommendedDailySpending,
      });
    } catch (error) {
      console.error('Bütçe analizi yüklenemedi:', error);
    } finally {
      setLoading(false);
    }
  };

  const getProgressColor = (percentage) => {
    if (percentage < 50) return getSafeThemeValue('success', '#34C759');
    if (percentage < 75) return getSafeThemeValue('warning', '#FF9500');
    if (percentage < 90) return getSafeThemeValue('error', '#FF3B30');
    return getSafeThemeValue('error', '#FF3B30');
  };

  const getStatusMessage = (percentage) => {
    if (percentage < 50) return 'Bütçeniz güvenli aralıkta';
    if (percentage < 75) return 'Harcamalarınızı takip edin';
    if (percentage < 90) return 'Dikkatli olun, limite yaklaşıyorsunuz';
    if (percentage < 100) return 'Çok dikkatli olun!';
    return 'Bütçenizi aştınız!';
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: getSafeThemeValue('background', '#ffffff'),
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 20,
      borderBottomWidth: 1,
      borderBottomColor: getSafeThemeValue('border', '#e0e0e0'),
    },
    backButton: {
      padding: 8,
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: getSafeThemeValue('text', '#333333'),
      flex: 1,
      textAlign: 'center',
    },
    content: {
      flex: 1,
      padding: 20,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    budgetName: {
      fontSize: 24,
      fontWeight: 'bold',
      color: getSafeThemeValue('text', '#333333'),
      textAlign: 'center',
      marginBottom: 30,
    },
    progressSection: {
      backgroundColor: getSafeThemeValue('surface', '#f9f9f9'),
      borderRadius: 12,
      padding: 20,
      marginBottom: 20,
      elevation: 2,
    },
    progressTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: getSafeThemeValue('text', '#333333'),
      marginBottom: 15,
    },
    progressBarContainer: {
      height: 12,
      backgroundColor: getSafeThemeValue('border', '#e0e0e0'),
      borderRadius: 6,
      overflow: 'hidden',
      marginBottom: 10,
    },
    progressBar: {
      height: '100%',
      borderRadius: 6,
    },
    progressText: {
      fontSize: 14,
      textAlign: 'center',
      color: getSafeThemeValue('textSecondary', '#666666'),
    },
    statusMessage: {
      fontSize: 16,
      textAlign: 'center',
      fontWeight: '500',
      marginTop: 10,
      padding: 10,
      borderRadius: 8,
      backgroundColor: getSafeThemeValue('border', '#e0e0e0'),
    },
    analyticsGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 15,
    },
    analyticsCard: {
      flex: 1,
      minWidth: (width - 55) / 2,
      backgroundColor: getSafeThemeValue('surface', '#f9f9f9'),
      borderRadius: 12,
      padding: 15,
      elevation: 1,
    },
    analyticsTitle: {
      fontSize: 14,
      color: getSafeThemeValue('textSecondary', '#666666'),
      marginBottom: 8,
    },
    analyticsValue: {
      fontSize: 18,
      fontWeight: 'bold',
      color: getSafeThemeValue('text', '#333333'),
    },
    analyticsSubtext: {
      fontSize: 12,
      color: getSafeThemeValue('textSecondary', '#666666'),
      marginTop: 4,
    },
    sectionTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: getSafeThemeValue('text', '#333333'),
      marginBottom: 15,
      marginTop: 10,
    },
  });

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <MaterialIcons 
              name="arrow-back" 
              size={24} 
              color={getSafeThemeValue('text', '#333333')} 
            />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Bütçe Analizi</Text>
          <View style={{ width: 40 }} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={getSafeThemeValue('primary', '#007AFF')} />
          <Text style={{ marginTop: 10, color: getSafeThemeValue('textSecondary', '#666666') }}>
            Analiz yapılıyor...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!budget) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <MaterialIcons 
              name="arrow-back" 
              size={24} 
              color={getSafeThemeValue('text', '#333333')} 
            />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Bütçe Analizi</Text>
          <View style={{ width: 40 }} />
        </View>
        <View style={styles.loadingContainer}>
          <Text style={{ color: getSafeThemeValue('textSecondary', '#666666') }}>
            Bütçe bulunamadı
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  const progressColor = getProgressColor(analysis.percentageUsed);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons 
            name="arrow-back" 
            size={24} 
            color={getSafeThemeValue('text', '#333333')} 
          />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Bütçe Analizi</Text>
        <View style={{ width: 40 }} />
      </View>
      
      <ScrollView style={styles.content}>
        <Text style={styles.budgetName}>{budget.name}</Text>
        
        {/* Progress Section */}
        <View style={styles.progressSection}>
          <Text style={styles.progressTitle}>Bütçe Kullanımı</Text>
          <View style={styles.progressBarContainer}>
            <View 
              style={[
                styles.progressBar, 
                {
                  width: `${Math.min(100, analysis.percentageUsed)}%`,
                  backgroundColor: progressColor,
                }
              ]} 
            />
          </View>
          <Text style={styles.progressText}>
            {analysis.totalSpent.toLocaleString('tr-TR')} / {budget.total_limit?.toLocaleString('tr-TR')} {budget.currency}
          </Text>
          <Text style={styles.progressText}>
            %{analysis.percentageUsed.toFixed(1)} kullanıldı
          </Text>
          <Text style={[styles.statusMessage, { color: progressColor }]}>
            {getStatusMessage(analysis.percentageUsed)}
          </Text>
        </View>
        
        {/* Analytics Grid */}
        <Text style={styles.sectionTitle}>Detaylar</Text>
        <View style={styles.analyticsGrid}>
          <View style={styles.analyticsCard}>
            <Text style={styles.analyticsTitle}>Kalan Miktar</Text>
            <Text style={[styles.analyticsValue, { color: getSafeThemeValue('success', '#34C759') }]}>
              {analysis.remainingAmount.toLocaleString('tr-TR')} {budget.currency}
            </Text>
          </View>
          
          <View style={styles.analyticsCard}>
            <Text style={styles.analyticsTitle}>Kalan Gün</Text>
            <Text style={styles.analyticsValue}>
              {analysis.daysRemaining}
            </Text>
            <Text style={styles.analyticsSubtext}>gün</Text>
          </View>
          
          <View style={styles.analyticsCard}>
            <Text style={styles.analyticsTitle}>Günlük Ortalama</Text>
            <Text style={styles.analyticsValue}>
              {analysis.averageDailySpending.toLocaleString('tr-TR')} {budget.currency}
            </Text>
            <Text style={styles.analyticsSubtext}>bugüne kadar</Text>
          </View>
          
          <View style={styles.analyticsCard}>
            <Text style={styles.analyticsTitle}>Önerilen Günlük</Text>
            <Text style={[styles.analyticsValue, { color: getSafeThemeValue('primary', '#007AFF') }]}>
              {analysis.recommendedDailySpending.toLocaleString('tr-TR')} {budget.currency}
            </Text>
            <Text style={styles.analyticsSubtext}>kalan dönem için</Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default BudgetAnalysisScreen;