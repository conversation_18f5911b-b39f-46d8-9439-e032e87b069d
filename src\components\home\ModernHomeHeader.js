import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialIcons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTheme } from '../../context/ThemeContext';
import { useAppContext } from '../../context/AppContext';

/**
 * Modern ve interaktif ana sayfa header'ı
 * 
 * @param {Object} props - Component props
 * @param {string} props.userName - Kullanıcı adı
 * @param {string} props.userAvatar - <PERSON>llanıcı avatar ikonu
 * @param {number} props.currentBalance - Mevcut bakiye
 * @param {number} props.unreadNotifications - Okunmamış bildirim sayısı
 * @param {Function} props.onSettingsPress - Ayarlar butonu press handler
 * @param {Function} props.onStatsPress - İstatistikler butonu press handler
 * @param {Function} props.onNotificationPress - Bildirim butonu press handler
 * @param {Function} props.onProfilePress - Profil butonu press handler
 * @param {Object} props.style - Ek stil
 * @returns {JSX.Element} Modern header component
 */
const ModernHomeHeader = ({
  userName = 'Kullanıcı',
  userAvatar = 'person',
  currentBalance = 0,
  unreadNotifications = 0,
  onSettingsPress,
  onStatsPress,
  onNotificationPress,
  onProfilePress,
  style
}) => {
  const insets = useSafeAreaInsets();
  const { theme } = useTheme();
  const { defaultCurrency } = useAppContext();
  const [animatedValue] = useState(new Animated.Value(0));

  /**
   * Zamana göre karşılama mesajı oluşturur
   * @returns {Object} Karşılama mesajı ve ikonu
   */
  const getTimeBasedGreeting = () => {
    const hour = new Date().getHours();
    
    if (hour < 12) {
      return { text: 'Günaydın', icon: '🌅' };
    } else if (hour < 17) {
      return { text: 'İyi günler', icon: '☀️' };
    } else {
      return { text: 'İyi akşamlar', icon: '🌆' };
    }
  };

  /**
   * Bakiye formatlar
   * @param {number} balance - Bakiye
   * @returns {string} Formatlanmış bakiye
   */
  const formatBalance = (balance) => {
    if (Math.abs(balance) >= 1000000) {
      return `₺${(balance / 1000000).toFixed(1)}M`;
    } else if (Math.abs(balance) >= 1000) {
      return `₺${(balance / 1000).toFixed(1)}K`;
    }
    return `₺${balance.toLocaleString('tr-TR')}`;
  };

  // Component mount animasyonu
  useEffect(() => {
    Animated.timing(animatedValue, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();
  }, []);

  const greeting = getTimeBasedGreeting();

  return (
    <View style={[styles.container, { paddingTop: insets.top }, style]}>
      <LinearGradient
        colors={[theme.PRIMARY, theme.PRIMARY_LIGHT, theme.PRIMARY_DARK]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        locations={[0, 0.6, 1]}
        style={styles.gradient}
      >
        <Animated.View
          style={[
            styles.content,
            {
              opacity: animatedValue,
              transform: [
                {
                  translateY: animatedValue.interpolate({
                    inputRange: [0, 1],
                    outputRange: [-15, 0],
                  }),
                },
              ],
            },
          ]}
        >
          {/* Ana header satırı */}
          <View style={styles.mainRow}>
            {/* Sol taraf - Kullanıcı bilgileri */}
            <TouchableOpacity
              style={styles.userSection}
              onPress={onProfilePress}
              activeOpacity={0.8}
            >
              <View style={styles.avatarContainer}>
                <View style={styles.avatarGlow}>
                  <MaterialIcons name={userAvatar} size={32} color="rgba(255,255,255,0.95)" />
                </View>
              </View>
              <View style={styles.userInfo}>
                <Text style={styles.greetingText}>
                  {greeting.icon} {greeting.text}
                </Text>
                <Text style={styles.userNameText}>
                  {userName}
                </Text>
              </View>
            </TouchableOpacity>

            {/* Sağ taraf - Aksiyon butonları */}
            <View style={styles.actionButtons}>
              {/* Bildirim butonu */}
              <TouchableOpacity
                style={[styles.actionButton, styles.notificationButton]}
                onPress={onNotificationPress}
                activeOpacity={0.8}
              >
                <MaterialIcons name="notifications" size={22} color="rgba(255,255,255,0.95)" />
                {unreadNotifications > 0 && (
                  <View style={styles.notificationBadge}>
                    <Text style={styles.badgeText}>
                      {unreadNotifications > 9 ? '9+' : unreadNotifications}
                    </Text>
                  </View>
                )}
              </TouchableOpacity>

              {/* İstatistikler butonu */}
              <TouchableOpacity
                style={[styles.actionButton, styles.statsButton]}
                onPress={onStatsPress}
                activeOpacity={0.8}
              >
                <MaterialIcons name="bar-chart" size={22} color="rgba(255,255,255,0.95)" />
              </TouchableOpacity>

              {/* Ayarlar butonu */}
              <TouchableOpacity
                style={[styles.actionButton, styles.settingsButton]}
                onPress={onSettingsPress}
                activeOpacity={0.8}
              >
                <MaterialIcons name="settings" size={22} color="rgba(255,255,255,0.95)" />
              </TouchableOpacity>
            </View>
          </View>

          {/* Bakiye satırı */}
          <View style={styles.balanceRow}>
            <View style={styles.balanceInfo}>
              <Text style={styles.balanceLabel}>Toplam Bakiye</Text>
              <View style={styles.balanceContainer}>
                <Text style={styles.balanceAmount}>
                  {formatBalance(currentBalance)}
                </Text>
                <View style={styles.balanceAccent} />
              </View>
            </View>
          </View>
        </Animated.View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 12,
  },
  gradient: {
    paddingHorizontal: 20,
    paddingVertical: 18,
  },
  content: {
    gap: 18,
  },
  mainRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  userSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatarContainer: {
    marginRight: 14,
  },
  avatarGlow: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: 'rgba(255,255,255,0.5)',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.8,
    shadowRadius: 8,
    elevation: 4,
  },
  userInfo: {
    flex: 1,
  },
  greetingText: {
    fontSize: 17,
    fontWeight: '600',
    color: 'rgba(255,255,255,0.98)',
    marginBottom: 3,
    letterSpacing: 0.3,
  },
  userNameText: {
    fontSize: 15,
    color: 'rgba(255,255,255,0.85)',
    fontWeight: '500',
    letterSpacing: 0.2,
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  actionButton: {
    width: 42,
    height: 42,
    borderRadius: 21,
    backgroundColor: 'rgba(255,255,255,0.18)',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    shadowColor: 'rgba(255,255,255,0.3)',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.6,
    shadowRadius: 4,
    elevation: 3,
  },
  notificationButton: {
    backgroundColor: 'rgba(255, 193, 7, 0.25)', // Sarı tonlu
  },
  statsButton: {
    backgroundColor: 'rgba(76, 175, 80, 0.25)', // Yeşil tonlu
  },
  settingsButton: {
    backgroundColor: 'rgba(158, 158, 158, 0.25)', // Gri tonlu
  },
  notificationBadge: {
    position: 'absolute',
    top: -3,
    right: -3,
    backgroundColor: '#ff4757',
    borderRadius: 12,
    minWidth: 22,
    height: 22,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'rgba(255,255,255,0.95)',
    shadowColor: '#ff4757',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.6,
    shadowRadius: 4,
    elevation: 6,
  },
  badgeText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: '700',
  },
  balanceRow: {
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255,255,255,0.2)',
  },
  balanceInfo: {
    alignItems: 'center',
  },
  balanceLabel: {
    fontSize: 13,
    color: 'rgba(255,255,255,0.8)',
    fontWeight: '500',
    marginBottom: 6,
    letterSpacing: 0.5,
  },
  balanceContainer: {
    alignItems: 'center',
    position: 'relative',
  },
  balanceAmount: {
    fontSize: 32,
    fontWeight: '800',
    color: 'rgba(255,255,255,0.98)',
    letterSpacing: 0.5,
    textShadowColor: 'rgba(0,0,0,0.2)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  balanceAccent: {
    position: 'absolute',
    bottom: -4,
    width: 40,
    height: 3,
    backgroundColor: 'rgba(255,255,255,0.6)',
    borderRadius: 2,
  },
});

export default ModernHomeHeader;
