/**
 * Real Data Integration Service
 * Replaces mock data with real SQLite-backed data for reporting system
 */

/**
 * Real Data Service for Reports
 * Integrates with SQLite database to provide real data for reports
 */
export class RealDataService {
  constructor(db) {
    this.db = db;
  }

  /**
   * Get monthly income/expense summary
   * @param {Object} params - Query parameters
   * @param {Date} params.startDate - Start date
   * @param {Date} params.endDate - End date
   * @returns {Promise<Object>} Monthly income/expense data
   */
  async getMonthlyIncomeExpense(params = {}) {
    const { startDate, endDate } = this.getDateRange(params);
    
    try {
      // Get income data
      const incomeData = await this.db.getAllAsync(`
        SELECT 
          c.name as category_name,
          c.color as category_color,
          SUM(t.amount) as total_amount,
          COUNT(*) as transaction_count
        FROM transactions t
        LEFT JOIN categories c ON t.category_id = c.id
        WHERE t.type = 'income'
        AND t.date >= ? AND t.date <= ?
        GROUP BY t.category_id, c.name, c.color
        ORDER BY total_amount DESC
      `, [startDate, endDate]);

      // Get expense data
      const expenseData = await this.db.getAllAsync(`
        SELECT 
          c.name as category_name,
          c.color as category_color,
          SUM(t.amount) as total_amount,
          COUNT(*) as transaction_count
        FROM transactions t
        LEFT JOIN categories c ON t.category_id = c.id
        WHERE t.type = 'expense'
        AND t.date >= ? AND t.date <= ?
        GROUP BY t.category_id, c.name, c.color
        ORDER BY total_amount DESC
      `, [startDate, endDate]);

      // Calculate totals
      const totalIncome = incomeData.reduce((sum, item) => sum + item.total_amount, 0);
      const totalExpense = expenseData.reduce((sum, item) => sum + item.total_amount, 0);

      return {
        period: this.formatDateRange(startDate, endDate),
        income: {
          total: totalIncome,
          categories: incomeData.map(item => ({
            name: item.category_name || 'Kategori Yok',
            amount: item.total_amount,
            percentage: totalIncome > 0 ? (item.total_amount / totalIncome) * 100 : 0,
            color: item.category_color || '#007AFF',
            count: item.transaction_count
          }))
        },
        expenses: {
          total: totalExpense,
          categories: expenseData.map(item => ({
            name: item.category_name || 'Kategori Yok',
            amount: item.total_amount,
            percentage: totalExpense > 0 ? (item.total_amount / totalExpense) * 100 : 0,
            color: item.category_color || '#FF6B6B',
            count: item.transaction_count
          }))
        },
        netIncome: totalIncome - totalExpense,
        savings: Math.max(0, totalIncome - totalExpense),
        savingsRate: totalIncome > 0 ? ((totalIncome - totalExpense) / totalIncome) * 100 : 0
      };
    } catch (error) {
      throw new Error(`Aylık gelir-gider verisi alınamadı: ${error.message}`);
    }
  }

  /**
   * Get category distribution data
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Category distribution data
   */
  async getCategoryDistribution(params = {}) {
    const { startDate, endDate, type = 'expense' } = params;
    const dateRange = this.getDateRange({ startDate, endDate });

    try {
      const categoryData = await this.db.getAllAsync(`
        SELECT 
          c.name as category_name,
          c.color as category_color,
          c.icon as category_icon,
          SUM(t.amount) as total_amount,
          COUNT(*) as transaction_count,
          AVG(t.amount) as avg_amount
        FROM transactions t
        LEFT JOIN categories c ON t.category_id = c.id
        WHERE t.type = ?
        AND t.date >= ? AND t.date <= ?
        GROUP BY t.category_id, c.name, c.color, c.icon
        ORDER BY total_amount DESC
      `, [type, dateRange.startDate, dateRange.endDate]);

      const totalAmount = categoryData.reduce((sum, item) => sum + item.total_amount, 0);

      return {
        type,
        period: this.formatDateRange(dateRange.startDate, dateRange.endDate),
        totalAmount,
        categories: categoryData.map(item => ({
          name: item.category_name || 'Kategori Yok',
          amount: item.total_amount,
          percentage: totalAmount > 0 ? (item.total_amount / totalAmount) * 100 : 0,
          color: item.category_color || '#007AFF',
          icon: item.category_icon || '📊',
          count: item.transaction_count,
          avgAmount: item.avg_amount
        }))
      };
    } catch (error) {
      throw new Error(`Kategori dağılımı verisi alınamadı: ${error.message}`);
    }
  }

  /**
   * Get daily transaction trends
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Daily trends data
   */
  async getDailyTrends(params = {}) {
    const { startDate, endDate } = this.getDateRange(params);

    try {
      const trendData = await this.db.getAllAsync(`
        SELECT 
          DATE(t.date) as date,
          SUM(CASE WHEN t.type = 'income' THEN t.amount ELSE 0 END) as income,
          SUM(CASE WHEN t.type = 'expense' THEN t.amount ELSE 0 END) as expense,
          COUNT(*) as transaction_count
        FROM transactions t
        WHERE t.date >= ? AND t.date <= ?
        GROUP BY DATE(t.date)
        ORDER BY DATE(t.date)
      `, [startDate, endDate]);

      const labels = trendData.map(item => 
        new Date(item.date).toLocaleDateString('tr-TR', { 
          day: '2-digit', 
          month: '2-digit' 
        })
      );

      return {
        period: this.formatDateRange(startDate, endDate),
        labels,
        income: trendData.map(item => item.income),
        expenses: trendData.map(item => item.expense),
        netFlow: trendData.map(item => item.income - item.expense),
        transactionCounts: trendData.map(item => item.transaction_count),
        summary: {
          totalIncome: trendData.reduce((sum, item) => sum + item.income, 0),
          totalExpense: trendData.reduce((sum, item) => sum + item.expense, 0),
          avgDailyIncome: trendData.reduce((sum, item) => sum + item.income, 0) / trendData.length,
          avgDailyExpense: trendData.reduce((sum, item) => sum + item.expense, 0) / trendData.length
        }
      };
    } catch (error) {
      throw new Error(`Günlük trend verisi alınamadı: ${error.message}`);
    }
  }

  /**
   * Get top transactions
   * @param {Object} params - Query parameters
   * @returns {Promise<Array>} Top transactions
   */
  async getTopTransactions(params = {}) {
    const { startDate, endDate, type, limit = 10 } = params;
    const dateRange = this.getDateRange({ startDate, endDate });

    try {
      let query = `
        SELECT 
          t.id,
          t.amount,
          t.type,
          t.date,
          t.description,
          c.name as category_name,
          c.color as category_color,
          c.icon as category_icon
        FROM transactions t
        LEFT JOIN categories c ON t.category_id = c.id
        WHERE t.date >= ? AND t.date <= ?
      `;

      const params_arr = [dateRange.startDate, dateRange.endDate];

      if (type) {
        query += ` AND t.type = ?`;
        params_arr.push(type);
      }

      query += ` ORDER BY t.amount DESC LIMIT ?`;
      params_arr.push(limit);

      const transactions = await this.db.getAllAsync(query, params_arr);

      return transactions.map(item => ({
        id: item.id,
        amount: item.amount,
        type: item.type,
        date: item.date,
        description: item.description || 'Açıklama yok',
        category: {
          name: item.category_name || 'Kategori Yok',
          color: item.category_color || '#007AFF',
          icon: item.category_icon || '📊'
        },
        formattedDate: new Date(item.date).toLocaleDateString('tr-TR'),
        formattedAmount: new Intl.NumberFormat('tr-TR', {
          style: 'currency',
          currency: 'TRY'
        }).format(item.amount)
      }));
    } catch (error) {
      throw new Error(`En yüksek işlemler alınamadı: ${error.message}`);
    }
  }

  /**
   * Get financial goals progress
   * @param {Object} params - Query parameters
   * @returns {Promise<Array>} Goals progress data
   */
  async getGoalsProgress(params = {}) {
    try {
      const goals = await this.db.getAllAsync(`
        SELECT 
          g.id,
          g.name,
          g.target_amount,
          g.current_amount,
          g.start_date,
          g.target_date,
          g.is_completed,
          c.name as category_name,
          c.color as category_color,
          c.icon as category_icon
        FROM goals g
        LEFT JOIN categories c ON g.category_id = c.id
        ORDER BY g.target_date ASC
      `);

      return goals.map(goal => {
        const progress = goal.target_amount > 0 ? (goal.current_amount / goal.target_amount) * 100 : 0;
        const remaining = Math.max(0, goal.target_amount - goal.current_amount);
        
        return {
          id: goal.id,
          name: goal.name,
          targetAmount: goal.target_amount,
          currentAmount: goal.current_amount,
          progress: Math.min(progress, 100),
          remaining,
          isCompleted: goal.is_completed === 1,
          startDate: goal.start_date,
          targetDate: goal.target_date,
          category: {
            name: goal.category_name || 'Kategori Yok',
            color: goal.category_color || '#007AFF',
            icon: goal.category_icon || '🎯'
          },
          status: goal.is_completed ? 'completed' : progress >= 100 ? 'achieved' : 'in_progress'
        };
      });
    } catch (error) {
      throw new Error(`Hedef ilerleme verisi alınamadı: ${error.message}`);
    }
  }

  /**
   * Get monthly comparison data
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Monthly comparison data
   */
  async getMonthlyComparison(params = {}) {
    const { months = 6 } = params;
    const endDate = new Date();
    const startDate = new Date(endDate);
    startDate.setMonth(startDate.getMonth() - months);

    try {
      const monthlyData = await this.db.getAllAsync(`
        SELECT 
          strftime('%Y-%m', t.date) as month,
          SUM(CASE WHEN t.type = 'income' THEN t.amount ELSE 0 END) as income,
          SUM(CASE WHEN t.type = 'expense' THEN t.amount ELSE 0 END) as expense,
          COUNT(*) as transaction_count
        FROM transactions t
        WHERE t.date >= ? AND t.date <= ?
        GROUP BY strftime('%Y-%m', t.date)
        ORDER BY month
      `, [startDate.toISOString().split('T')[0], endDate.toISOString().split('T')[0]]);

      const labels = monthlyData.map(item => {
        const [year, month] = item.month.split('-');
        return new Date(year, month - 1).toLocaleDateString('tr-TR', { 
          month: 'short', 
          year: 'numeric' 
        });
      });

      return {
        period: `${months} aylık karşılaştırma`,
        labels,
        income: monthlyData.map(item => item.income),
        expenses: monthlyData.map(item => item.expense),
        netFlow: monthlyData.map(item => item.income - item.expense),
        transactionCounts: monthlyData.map(item => item.transaction_count),
        trends: {
          incomeGrowth: this.calculateGrowthRate(monthlyData.map(item => item.income)),
          expenseGrowth: this.calculateGrowthRate(monthlyData.map(item => item.expense)),
          avgMonthlyIncome: monthlyData.reduce((sum, item) => sum + item.income, 0) / monthlyData.length,
          avgMonthlyExpense: monthlyData.reduce((sum, item) => sum + item.expense, 0) / monthlyData.length
        }
      };
    } catch (error) {
      throw new Error(`Aylık karşılaştırma verisi alınamadı: ${error.message}`);
    }
  }

  /**
   * Get report summary statistics
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Summary statistics
   */
  async getReportSummary(params = {}) {
    const { startDate, endDate } = this.getDateRange(params);

    try {
      const summary = await this.db.getFirstAsync(`
        SELECT 
          COUNT(*) as total_transactions,
          SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END) as total_income,
          SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as total_expense,
          AVG(CASE WHEN type = 'income' THEN amount ELSE NULL END) as avg_income,
          AVG(CASE WHEN type = 'expense' THEN amount ELSE NULL END) as avg_expense,
          MAX(CASE WHEN type = 'income' THEN amount ELSE 0 END) as max_income,
          MAX(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as max_expense
        FROM transactions
        WHERE date >= ? AND date <= ?
      `, [startDate, endDate]);

      const categoryCount = await this.db.getFirstAsync(`
        SELECT COUNT(DISTINCT category_id) as category_count
        FROM transactions
        WHERE date >= ? AND date <= ?
      `, [startDate, endDate]);

      return {
        period: this.formatDateRange(startDate, endDate),
        totalTransactions: summary.total_transactions || 0,
        totalIncome: summary.total_income || 0,
        totalExpense: summary.total_expense || 0,
        netIncome: (summary.total_income || 0) - (summary.total_expense || 0),
        avgIncome: summary.avg_income || 0,
        avgExpense: summary.avg_expense || 0,
        maxIncome: summary.max_income || 0,
        maxExpense: summary.max_expense || 0,
        categoriesUsed: categoryCount.category_count || 0,
        savingsRate: summary.total_income > 0 ? 
          (((summary.total_income || 0) - (summary.total_expense || 0)) / summary.total_income) * 100 : 0,
        avgDailySpending: summary.total_expense > 0 ? 
          summary.total_expense / this.getDaysBetween(startDate, endDate) : 0
      };
    } catch (error) {
      throw new Error(`Rapor özet verisi alınamadı: ${error.message}`);
    }
  }

  /**
   * Get cash flow data
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Cash flow data
   */
  async getCashFlowData(params = {}) {
    const { startDate, endDate } = params;
    const dateRange = this.getDateRange({ startDate, endDate });

    try {
      // Get opening balance (simplified - using first day of period)
      const openingBalance = await this.db.get(`
        SELECT COALESCE(SUM(CASE WHEN type = 'income' THEN amount ELSE -amount END), 0) as balance
        FROM transactions
        WHERE date < ?
      `, [dateRange.startDate]);

      // Get all transactions in the period
      const transactions = await this.db.all(`
        SELECT 
          t.date,
          t.description,
          t.amount,
          t.type,
          c.name as category_name
        FROM transactions t
        LEFT JOIN categories c ON t.category_id = c.id
        WHERE t.date >= ? AND t.date <= ?
        ORDER BY t.date ASC
      `, [dateRange.startDate, dateRange.endDate]);

      // Calculate cash flows
      const cashInflows = transactions.filter(t => t.type === 'income');
      const cashOutflows = transactions.filter(t => t.type === 'expense');
      const totalInflows = cashInflows.reduce((sum, t) => sum + t.amount, 0);
      const totalOutflows = cashOutflows.reduce((sum, t) => sum + t.amount, 0);

      return {
        period: this.formatDateRange(dateRange.startDate, dateRange.endDate),
        openingBalance: openingBalance.balance,
        closingBalance: openingBalance.balance + totalInflows - totalOutflows,
        netCashFlow: totalInflows - totalOutflows,
        cashInflows: cashInflows.map(t => ({
          date: t.date,
          description: t.description,
          amount: t.amount,
          type: t.type,
          category: t.category_name
        })),
        cashOutflows: cashOutflows.map(t => ({
          date: t.date,
          description: t.description,
          amount: t.amount,
          type: t.type,
          category: t.category_name
        }))
      };
    } catch (error) {
      throw new Error(`Nakit akış verisi alınamadı: ${error.message}`);
    }
  }

  /**
   * Get budget vs actual data
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Budget vs actual data
   */
  async getBudgetVsActualData(params = {}) {
    const { startDate, endDate } = params;
    const dateRange = this.getDateRange({ startDate, endDate });

    try {
      // Get actual expenses by category
      const actualExpenses = await this.db.all(`
        SELECT 
          c.name as category_name,
          SUM(t.amount) as actual_amount
        FROM transactions t
        LEFT JOIN categories c ON t.category_id = c.id
        WHERE t.type = 'expense'
        AND t.date >= ? AND t.date <= ?
        GROUP BY t.category_id, c.name
      `, [dateRange.startDate, dateRange.endDate]);

      // For now, create mock budget data since we don't have budget tables
      // In a real app, this would come from a budgets table
      const budgetData = actualExpenses.map(expense => ({
        category_name: expense.category_name,
        budget_amount: expense.actual_amount * 1.2 // Mock: 20% more than actual
      }));

      const totalBudget = budgetData.reduce((sum, item) => sum + item.budget_amount, 0);
      const totalActual = actualExpenses.reduce((sum, item) => sum + item.actual_amount, 0);

      return {
        period: this.formatDateRange(dateRange.startDate, dateRange.endDate),
        totalBudget,
        totalActual,
        variance: totalActual - totalBudget,
        categories: budgetData.map(budget => {
          const actual = actualExpenses.find(a => a.category_name === budget.category_name);
          const actualAmount = actual ? actual.actual_amount : 0;
          return {
            name: budget.category_name || 'Kategori Yok',
            budget: budget.budget_amount,
            actual: actualAmount,
            variance: actualAmount - budget.budget_amount,
            percentage: budget.budget_amount > 0 ? (actualAmount / budget.budget_amount) * 100 : 0
          };
        })
      };
    } catch (error) {
      throw new Error(`Bütçe karşılaştırma verisi alınamadı: ${error.message}`);
    }
  }

  /**
   * Get transaction list data
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Transaction list data
   */
  async getTransactionListData(params = {}) {
    const { startDate, endDate, filterType = 'all', limit = 50 } = params;
    const dateRange = this.getDateRange({ startDate, endDate });

    try {
      let whereClause = 'WHERE t.date >= ? AND t.date <= ?';
      const queryParams = [dateRange.startDate, dateRange.endDate];

      if (filterType !== 'all') {
        whereClause += ' AND t.type = ?';
        queryParams.push(filterType);
      }

      const transactions = await this.db.all(`
        SELECT 
          t.date,
          t.description,
          t.amount,
          t.type,
          c.name as category_name
        FROM transactions t
        LEFT JOIN categories c ON t.category_id = c.id
        ${whereClause}
        ORDER BY t.date DESC
        LIMIT ?
      `, [...queryParams, limit]);

      // Calculate summary
      const totalIncome = transactions
        .filter(t => t.type === 'income')
        .reduce((sum, t) => sum + t.amount, 0);
      
      const totalExpense = transactions
        .filter(t => t.type === 'expense')
        .reduce((sum, t) => sum + t.amount, 0);

      return {
        period: this.formatDateRange(dateRange.startDate, dateRange.endDate),
        transactions: transactions.map(t => ({
          date: t.date,
          description: t.description,
          amount: t.amount,
          type: t.type,
          category: t.category_name || 'Kategori Yok'
        })),
        summary: {
          totalIncome,
          totalExpense,
          netAmount: totalIncome - totalExpense,
          transactionCount: transactions.length
        }
      };
    } catch (error) {
      throw new Error(`İşlem listesi verisi alınamadı: ${error.message}`);
    }
  }

  /**
   * Get basic summary data
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Basic summary data
   */
  async getBasicSummaryData(params = {}) {
    const { startDate, endDate } = params;
    const dateRange = this.getDateRange({ startDate, endDate });

    try {
      // Get basic income/expense summary
      const summary = await this.db.get(`
        SELECT 
          SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END) as total_income,
          SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as total_expense,
          COUNT(*) as transaction_count
        FROM transactions
        WHERE date >= ? AND date <= ?
      `, [dateRange.startDate, dateRange.endDate]);

      // Get category breakdown
      const categoryBreakdown = await this.db.all(`
        SELECT 
          c.name as category_name,
          t.type,
          SUM(t.amount) as total_amount,
          COUNT(*) as transaction_count
        FROM transactions t
        LEFT JOIN categories c ON t.category_id = c.id
        WHERE t.date >= ? AND t.date <= ?
        GROUP BY t.category_id, c.name, t.type
        ORDER BY total_amount DESC
      `, [dateRange.startDate, dateRange.endDate]);

      // Get recent transactions
      const recentTransactions = await this.db.all(`
        SELECT 
          t.date,
          t.description,
          t.amount,
          t.type,
          c.name as category_name
        FROM transactions t
        LEFT JOIN categories c ON t.category_id = c.id
        WHERE t.date >= ? AND t.date <= ?
        ORDER BY t.date DESC
        LIMIT 10
      `, [dateRange.startDate, dateRange.endDate]);

      const totalIncome = summary.total_income || 0;
      const totalExpense = summary.total_expense || 0;
      const netIncome = totalIncome - totalExpense;

      return {
        period: this.formatDateRange(dateRange.startDate, dateRange.endDate),
        summary: {
          totalIncome,
          totalExpense,
          netIncome,
          transactionCount: summary.transaction_count || 0,
          savingsRate: totalIncome > 0 ? (netIncome / totalIncome) * 100 : 0
        },
        categoryBreakdown: categoryBreakdown.map(item => ({
          name: item.category_name || 'Kategori Yok',
          type: item.type,
          amount: item.total_amount,
          count: item.transaction_count
        })),
        recentTransactions: recentTransactions.map(t => ({
          date: t.date,
          description: t.description,
          amount: t.amount,
          type: t.type,
          category: t.category_name || 'Kategori Yok'
        }))
      };
    } catch (error) {
      throw new Error(`Temel özet verisi alınamadı: ${error.message}`);
    }
  }

  /**
   * Get regular income tracking data
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Regular income tracking data
   */
  async getRegularIncomeTracking(params = {}) {
    const { startDate, endDate } = this.getDateRange(params);
    
    try {
      // Get regular income from transactions
      const regularIncomeFromTransactions = await this.db.getAllAsync(`
        SELECT 
          t.date,
          t.amount,
          t.description,
          c.name as category_name,
          c.color as category_color
        FROM transactions t
        LEFT JOIN categories c ON t.category_id = c.id
        WHERE t.type = 'income'
        AND t.date >= ? AND t.date <= ?
        AND (c.name = 'Maaş' OR c.name LIKE '%Maaş%' OR c.name LIKE '%Salary%')
        ORDER BY t.date DESC
      `, [startDate, endDate]);

      // Get work payments if available
      const workPayments = await this.db.getAllAsync(`
        SELECT 
          wp.period_start_date,
          wp.period_end_date,
          wp.regular_hours,
          wp.regular_amount,
          wp.payment_date,
          wp.is_paid
        FROM work_payments wp
        WHERE wp.period_start_date >= ? AND wp.period_end_date <= ?
        ORDER BY wp.period_start_date DESC
      `, [startDate, endDate]);

      // Get work settings for context
      const workSettings = await this.db.getFirstAsync(`
        SELECT 
          hourly_rate,
          weekly_work_hours,
          daily_work_hours,
          currency
        FROM work_settings
        ORDER BY created_at DESC
        LIMIT 1
      `);

      // Calculate summary statistics
      const totalRegularIncome = regularIncomeFromTransactions.reduce((sum, item) => sum + item.amount, 0);
      const totalWorkPayments = workPayments.reduce((sum, item) => sum + item.regular_amount, 0);
      const totalIncome = totalRegularIncome + totalWorkPayments;

      const monthlyAverage = this.calculateMonthlyAverage(totalIncome, startDate, endDate);
      const expectedIncome = workSettings ? 
        (workSettings.hourly_rate * workSettings.weekly_work_hours * 4.33) : 0; // ~4.33 weeks per month

      return {
        period: this.formatDateRange(startDate, endDate),
        summary: {
          totalIncome,
          totalRegularIncome,
          totalWorkPayments,
          monthlyAverage,
          expectedIncome,
          varianceFromExpected: totalIncome - expectedIncome,
          variancePercentage: expectedIncome > 0 ? ((totalIncome - expectedIncome) / expectedIncome) * 100 : 0
        },
        transactions: regularIncomeFromTransactions.map(item => ({
          date: item.date,
          amount: item.amount,
          description: item.description,
          category: item.category_name || 'Kategori Yok',
          color: item.category_color || '#2ECC71'
        })),
        workPayments: workPayments.map(item => ({
          periodStart: item.period_start_date,
          periodEnd: item.period_end_date,
          hours: item.regular_hours,
          amount: item.regular_amount,
          paymentDate: item.payment_date,
          isPaid: Boolean(item.is_paid)
        })),
        settings: workSettings || {
          hourlyRate: 0,
          weeklyWorkHours: 45,
          dailyWorkHours: 9,
          currency: 'TRY'
        }
      };
    } catch (error) {
      throw new Error(`Düzenli gelir takip verisi alınamadı: ${error.message}`);
    }
  }

  /**
   * Get overtime income data
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Overtime income data
   */
  async getOvertimeIncomeData(params = {}) {
    const { startDate, endDate } = this.getDateRange(params);
    
    try {
      // Get overtime work shifts
      const overtimeShifts = await this.db.getAllAsync(`
        SELECT 
          date,
          start_time,
          end_time,
          break_duration,
          is_overtime,
          overtime_multiplier,
          notes,
          status
        FROM work_shifts
        WHERE date >= ? AND date <= ?
        AND (is_overtime = 1 OR overtime_multiplier > 1.0)
        ORDER BY date DESC
      `, [startDate, endDate]);

      // Get overtime payments
      const overtimePayments = await this.db.getAllAsync(`
        SELECT 
          wp.period_start_date,
          wp.period_end_date,
          wp.overtime_hours,
          wp.overtime_amount,
          wp.payment_date,
          wp.is_paid
        FROM work_payments wp
        WHERE wp.period_start_date >= ? AND wp.period_end_date <= ?
        AND wp.overtime_hours > 0
        ORDER BY wp.period_start_date DESC
      `, [startDate, endDate]);

      // Get work settings for calculations
      const workSettings = await this.db.getFirstAsync(`
        SELECT 
          hourly_rate,
          overtime_rate,
          weekly_work_hours,
          daily_work_hours,
          currency
        FROM work_settings
        ORDER BY created_at DESC
        LIMIT 1
      `);

      // Calculate shift durations and earnings
      const processedShifts = overtimeShifts.map(shift => {
        const duration = this.calculateShiftDuration(shift.start_time, shift.end_time, shift.break_duration);
        const overtimeRate = workSettings?.overtime_rate || (workSettings?.hourly_rate * shift.overtime_multiplier) || 0;
        const estimatedEarnings = duration * overtimeRate;

        return {
          date: shift.date,
          startTime: shift.start_time,
          endTime: shift.end_time,
          duration,
          overtimeMultiplier: shift.overtime_multiplier,
          estimatedEarnings,
          notes: shift.notes,
          status: shift.status
        };
      });

      // Calculate summary statistics
      const totalOvertimeHours = overtimePayments.reduce((sum, item) => sum + item.overtime_hours, 0);
      const totalOvertimeAmount = overtimePayments.reduce((sum, item) => sum + item.overtime_amount, 0);
      const totalEstimatedHours = processedShifts.reduce((sum, shift) => sum + shift.duration, 0);
      const totalEstimatedEarnings = processedShifts.reduce((sum, shift) => sum + shift.estimatedEarnings, 0);

      const monthlyAverage = this.calculateMonthlyAverage(totalOvertimeAmount, startDate, endDate);
      const averageHourlyRate = totalOvertimeHours > 0 ? totalOvertimeAmount / totalOvertimeHours : 0;

      return {
        period: this.formatDateRange(startDate, endDate),
        summary: {
          totalOvertimeAmount,
          totalOvertimeHours,
          totalEstimatedEarnings,
          totalEstimatedHours,
          monthlyAverage,
          averageHourlyRate,
          baseHourlyRate: workSettings?.hourly_rate || 0,
          overtimeRate: workSettings?.overtime_rate || 0
        },
        shifts: processedShifts,
        payments: overtimePayments.map(item => ({
          periodStart: item.period_start_date,
          periodEnd: item.period_end_date,
          hours: item.overtime_hours,
          amount: item.overtime_amount,
          paymentDate: item.payment_date,
          isPaid: Boolean(item.is_paid)
        })),
        settings: workSettings || {
          hourlyRate: 0,
          overtimeRate: 0,
          weeklyWorkHours: 45,
          dailyWorkHours: 9,
          currency: 'TRY'
        }
      };
    } catch (error) {
      throw new Error(`Mesai gelir verisi alınamadı: ${error.message}`);
    }
  }

  /**
   * Calculate shift duration in hours
   * @param {string} startTime - Start time (HH:MM)
   * @param {string} endTime - End time (HH:MM)
   * @param {number} breakDuration - Break duration in minutes
   * @returns {number} Duration in hours
   */
  calculateShiftDuration(startTime, endTime, breakDuration = 0) {
    if (!startTime || !endTime) return 0;

    const [startHour, startMinute] = startTime.split(':').map(Number);
    const [endHour, endMinute] = endTime.split(':').map(Number);

    const startMinutes = startHour * 60 + startMinute;
    let endMinutes = endHour * 60 + endMinute;

    // Handle overnight shifts
    if (endMinutes < startMinutes) {
      endMinutes += 24 * 60;
    }

    const totalMinutes = endMinutes - startMinutes - (breakDuration || 0);
    return Math.max(0, totalMinutes / 60);
  }

  /**
   * Calculate monthly average
   * @param {number} total - Total amount
   * @param {string} startDate - Start date
   * @param {string} endDate - End date
   * @returns {number} Monthly average
   */
  calculateMonthlyAverage(total, startDate, endDate) {
    const days = this.getDaysBetween(startDate, endDate);
    const months = days / 30.44; // Average days per month
    return months > 0 ? total / months : 0;
  }

  // Helper methods

  /**
   * Get date range for queries
   * @param {Object} params - Date parameters
   * @returns {Object} Date range
   */
  getDateRange(params = {}) {
    const { startDate, endDate } = params;
    const end = endDate ? new Date(endDate) : new Date();
    const start = startDate ? new Date(startDate) : new Date(end.getFullYear(), end.getMonth(), 1);
    
    return {
      startDate: start.toISOString().split('T')[0],
      endDate: end.toISOString().split('T')[0]
    };
  }

  /**
   * Format date range for display
   * @param {string} startDate - Start date
   * @param {string} endDate - End date
   * @returns {string} Formatted date range
   */
  formatDateRange(startDate, endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    if (start.getMonth() === end.getMonth() && start.getFullYear() === end.getFullYear()) {
      return start.toLocaleDateString('tr-TR', { month: 'long', year: 'numeric' });
    }
    
    return `${start.toLocaleDateString('tr-TR', { month: 'short', year: 'numeric' })} - ${end.toLocaleDateString('tr-TR', { month: 'short', year: 'numeric' })}`;
  }

  /**
   * Calculate growth rate from array of values
   * @param {Array} values - Array of values
   * @returns {number} Growth rate percentage
   */
  calculateGrowthRate(values) {
    if (values.length < 2) return 0;
    
    const first = values[0];
    const last = values[values.length - 1];
    
    if (first === 0) return 0;
    
    return ((last - first) / first) * 100;
  }

  /**
   * Get days between two dates
   * @param {string} startDate - Start date
   * @param {string} endDate - End date
   * @returns {number} Days between dates
   */
  getDaysBetween(startDate, endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const timeDiff = end.getTime() - start.getTime();
    return Math.ceil(timeDiff / (1000 * 3600 * 24));
  }
}

/**
 * Factory function to create RealDataService instance
 * @param {Object} db - Database instance
 * @returns {RealDataService} RealDataService instance
 */
export const createRealDataService = (db) => {
  return new RealDataService(db);
};
