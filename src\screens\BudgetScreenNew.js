/**
 * Modern Budget Screen - Real Database Integration
 * Complete redesign following BUDGET_MANAGEMENT_REDESIGN_PLAN.md
 * 
 * Features:
 * - Real SQLite database integration (no demo data)
 * - Functional budget notifications
 * - Modern UI/UX with dark mode support
 * - Global budget features access
 * - Interactive design with animations
 * - Turkish localization
 * - Multi-currency support
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  ScrollView,
  RefreshControl,
  Alert,
  StyleSheet,
  TouchableOpacity,
  Text,
  ActivityIndicator,
  Animated,
  Dimensions,
  StatusBar
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useFocusEffect } from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialIcons, Ionicons } from '@expo/vector-icons';

// Context imports
import { useTheme } from '../context/ThemeContext';
import { useExchangeRate } from '../context/ExchangeRateProvider';

// Service imports
import * as RealBudgetService from '../services/budget/realBudgetService';
import * as BudgetNotificationService from '../services/budget/budgetNotificationService';

// Component imports
import BudgetCard from '../components/budget/BudgetCard/BudgetCard';
import QuickExpenseEntry from '../components/budget/Tracking/QuickExpenseEntry';
import BudgetAlertBanner from '../components/budget/Alerts/BudgetAlertBanner';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

/**
 * Modern Budget Screen Component
 * @param {Object} navigation - Navigation object
 */
export default function ModernBudgetScreen({ navigation }) {
  const { theme, isDarkMode } = useTheme();
  const { rates, baseCurrency } = useExchangeRate();
  const db = useSQLiteContext();
  
  // State management
  const [budgets, setBudgets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [showQuickExpense, setShowQuickExpense] = useState(false);
  const [selectedBudget, setSelectedBudget] = useState(null);
  
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  /**
   * Loads budgets from database
   */
  const loadBudgets = useCallback(async () => {
    try {
      console.log('📊 Loading budgets from database...');
      
      // Update spending amounts first
      await RealBudgetService.updateBudgetSpending(db);
      
      // Get all active budgets
      const budgetData = await RealBudgetService.getBudgets(db, { 
        active_only: true 
      });
      
      setBudgets(budgetData);
      
      // Check for notifications after loading budgets
      await checkBudgetNotifications(budgetData);
      
      console.log(`✅ Loaded ${budgetData.length} budgets`);
    } catch (error) {
      console.error('❌ Budget loading failed:', error);
      Alert.alert(
        'Hata',
        'Bütçeler yüklenirken bir hata oluştu. Lütfen tekrar deneyin.',
        [{ text: 'Tamam' }]
      );
    }
  }, [db]);

  /**
   * Checks budget notifications
   */
  const checkBudgetNotifications = useCallback(async (budgetData) => {
    try {
      console.log('🔔 Checking budget notifications...');
      
      let allNotifications = [];
      
      // Check thresholds for each active budget
      for (const budget of budgetData) {
        const thresholdNotifications = await BudgetNotificationService.checkBudgetThresholds(db, budget.id);
        const categoryNotifications = await BudgetNotificationService.checkCategoryLimits(db, budget.id);
        
        allNotifications = [...allNotifications, ...thresholdNotifications, ...categoryNotifications];
      }
      
      // Get unread notifications
      const unreadNotifications = await BudgetNotificationService.getUnreadBudgetNotifications(db);
      setNotifications(unreadNotifications);
      
      console.log(`✅ Found ${unreadNotifications.length} unread notifications`);
    } catch (error) {
      console.error('❌ Notification check failed:', error);
    }
  }, [db]);

  /**
   * Handles refresh action
   */
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadBudgets();
    setRefreshing(false);
  }, [loadBudgets]);

  /**
   * Handles navigation to budget creation
   */
  const handleCreateBudget = useCallback(() => {
    navigation.navigate('BudgetCreateScreen');
  }, [navigation]);

  /**
   * Handles budget card press
   */
  const handleBudgetPress = useCallback((budget) => {
    navigation.navigate('BudgetDetailScreen', { budgetId: budget.id });
  }, [navigation]);

  /**
   * Handles quick expense entry
   */
  const handleQuickExpense = useCallback((budget) => {
    setSelectedBudget(budget);
    setShowQuickExpense(true);
  }, []);

  /**
   * Handles expense addition completion
   */
  const handleExpenseAdded = useCallback(async () => {
    setShowQuickExpense(false);
    setSelectedBudget(null);
    await loadBudgets(); // Refresh data after expense is added
  }, [loadBudgets]);

  /**
   * Handles notification banner press
   */
  const handleNotificationPress = useCallback(async (notification) => {
    try {
      // Mark notification as read
      await BudgetNotificationService.markNotificationsAsRead(db, [notification.id]);
      
      // Navigate to budget detail if applicable
      if (notification.budget_id) {
        navigation.navigate('BudgetDetailScreen', { budgetId: notification.budget_id });
      }
      
      // Refresh notifications
      const unreadNotifications = await BudgetNotificationService.getUnreadBudgetNotifications(db);
      setNotifications(unreadNotifications);
    } catch (error) {
      console.error('❌ Notification handling failed:', error);
    }
  }, [db, navigation]);

  /**
   * Load data on screen focus
   */
  useFocusEffect(
    useCallback(() => {
      loadBudgets();
    }, [loadBudgets])
  );

  /**
   * Initial load effect
   */
  useEffect(() => {
    const initializeScreen = async () => {
      setLoading(true);
      await loadBudgets();
      setLoading(false);
      
      // Start animations
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 400,
          useNativeDriver: true,
        }),
      ]).start();
    };
    
    initializeScreen();
  }, [loadBudgets, fadeAnim, slideAnim]);

  /**
   * Calculates overview statistics
   */
  const getOverviewStats = useCallback(() => {
    const activeBudgets = budgets.filter(b => b.status === 'active');
    
    const totalBudget = activeBudgets.reduce((sum, b) => sum + (b.total_limit || 0), 0);
    const totalSpent = activeBudgets.reduce((sum, b) => sum + (b.total_spent || 0), 0);
    const remaining = totalBudget - totalSpent;
    const spendingPercentage = totalBudget > 0 ? (totalSpent / totalBudget) * 100 : 0;
    const overBudgetCount = activeBudgets.filter(b => b.is_over_budget).length;
    
    return {
      totalBudget,
      totalSpent,
      remaining,
      spendingPercentage,
      activeBudgetCount: activeBudgets.length,
      overBudgetCount
    };
  }, [budgets]);

  const stats = getOverviewStats();

  if (loading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.BACKGROUND }]}>
        <ActivityIndicator size="large" color={theme.PRIMARY} />
        <Text style={[styles.loadingText, { color: theme.TEXT_PRIMARY }]}>
          Bütçeler yükleniyor...
        </Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor={theme.BACKGROUND}
      />
      
      {/* Header */}
      <Animated.View 
        style={[
          styles.header, 
          { backgroundColor: theme.PRIMARY },
          { opacity: fadeAnim }
        ]}
      >
        <View style={styles.headerContent}>
          <View>
            <Text style={[styles.headerTitle, { color: theme.ON_PRIMARY }]}>
              Bütçe Yönetimi
            </Text>
            <Text style={[styles.headerSubtitle, { color: theme.ON_PRIMARY }]}>
              {stats.activeBudgetCount} aktif bütçe
            </Text>
          </View>
          
          <View style={styles.headerActions}>
            <TouchableOpacity 
              style={styles.headerButton}
              onPress={handleCreateBudget}
            >
              <Ionicons name="add" size={24} color={theme.ON_PRIMARY} />
            </TouchableOpacity>
          </View>
        </View>
      </Animated.View>

      {/* Notifications */}
      {notifications.length > 0 && (
        <Animated.View style={{ opacity: fadeAnim }}>
          <BudgetAlertBanner
            notifications={notifications}
            onNotificationPress={handleNotificationPress}
            theme={theme}
          />
        </Animated.View>
      )}

      {/* Overview Cards */}
      <Animated.View 
        style={[
          styles.overviewContainer,
          { 
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }]
          }
        ]}
      >
        <View style={styles.overviewRow}>
          <View style={[styles.overviewCard, { backgroundColor: theme.SURFACE }]}>
            <Ionicons name="wallet-outline" size={24} color={theme.SUCCESS} />
            <Text style={[styles.overviewValue, { color: theme.TEXT_PRIMARY }]}>
              {stats.totalBudget.toLocaleString('tr-TR')} ₺
            </Text>
            <Text style={[styles.overviewLabel, { color: theme.TEXT_SECONDARY }]}>
              Toplam Bütçe
            </Text>
          </View>
          
          <View style={[styles.overviewCard, { backgroundColor: theme.SURFACE }]}>
            <Ionicons 
              name="trending-up" 
              size={24} 
              color={stats.spendingPercentage > 90 ? theme.DANGER : theme.INFO} 
            />
            <Text style={[styles.overviewValue, { color: theme.TEXT_PRIMARY }]}>
              {stats.totalSpent.toLocaleString('tr-TR')} ₺
            </Text>
            <Text style={[styles.overviewLabel, { color: theme.TEXT_SECONDARY }]}>
              Harcanan
            </Text>
          </View>
        </View>
        
        <View style={styles.overviewRow}>
          <View style={[styles.overviewCard, { backgroundColor: theme.SURFACE }]}>
            <Ionicons 
              name="cash-outline" 
              size={24} 
              color={stats.remaining >= 0 ? theme.SUCCESS : theme.DANGER} 
            />
            <Text style={[styles.overviewValue, { color: theme.TEXT_PRIMARY }]}>
              {stats.remaining.toLocaleString('tr-TR')} ₺
            </Text>
            <Text style={[styles.overviewLabel, { color: theme.TEXT_SECONDARY }]}>
              Kalan
            </Text>
          </View>
          
          {stats.overBudgetCount > 0 && (
            <View style={[styles.overviewCard, { backgroundColor: theme.DANGER_LIGHT }]}>
              <Ionicons name="warning" size={24} color={theme.DANGER} />
              <Text style={[styles.overviewValue, { color: theme.DANGER }]}>
                {stats.overBudgetCount}
              </Text>
              <Text style={[styles.overviewLabel, { color: theme.DANGER }]}>
                Limit Aşan
              </Text>
            </View>
          )}
        </View>
      </Animated.View>

      {/* Budget List */}
      <Animated.View 
        style={[
          styles.contentContainer,
          { opacity: fadeAnim }
        ]}
      >
        <ScrollView
          style={styles.scrollView}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              tintColor={theme.PRIMARY}
              colors={[theme.PRIMARY]}
            />
          }
          showsVerticalScrollIndicator={false}
        >
          {budgets.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Ionicons name="wallet-outline" size={64} color={theme.TEXT_DISABLED} />
              <Text style={[styles.emptyTitle, { color: theme.TEXT_PRIMARY }]}>
                Henüz bütçe oluşturmadınız
              </Text>
              <Text style={[styles.emptySubtitle, { color: theme.TEXT_SECONDARY }]}>
                Harcamalarınızı kontrol altında tutmak için bütçe oluşturun
              </Text>
              <TouchableOpacity
                style={[styles.createButton, { backgroundColor: theme.PRIMARY }]}
                onPress={handleCreateBudget}
              >
                <Text style={[styles.createButtonText, { color: theme.ON_PRIMARY }]}>
                  İlk Bütçenizi Oluşturun
                </Text>
              </TouchableOpacity>
            </View>
          ) : (
            <View style={styles.budgetsList}>
              {budgets.map((budget, index) => (
                <Animated.View
                  key={budget.id}
                  style={{
                    opacity: fadeAnim,
                    transform: [
                      {
                        translateY: slideAnim.interpolate({
                          inputRange: [0, 50],
                          outputRange: [0, 50 + (index * 10)],
                        }),
                      },
                    ],
                  }}
                >
                  <BudgetCard
                    budget={budget}
                    onPress={() => handleBudgetPress(budget)}
                    onQuickExpense={() => handleQuickExpense(budget)}
                    theme={theme}
                    rates={rates}
                    baseCurrency={baseCurrency}
                  />
                </Animated.View>
              ))}
            </View>
          )}
          
          <View style={{ height: 100 }} />
        </ScrollView>
      </Animated.View>

      {/* Quick Expense Modal */}
      {showQuickExpense && selectedBudget && (
        <QuickExpenseEntry
          visible={showQuickExpense}
          budget={selectedBudget}
          onClose={() => setShowQuickExpense(false)}
          onExpenseAdded={handleExpenseAdded}
          theme={theme}
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerSubtitle: {
    fontSize: 14,
    opacity: 0.8,
    marginTop: 2,
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  overviewContainer: {
    padding: 16,
    gap: 12,
  },
  overviewRow: {
    flexDirection: 'row',
    gap: 12,
  },
  overviewCard: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    gap: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  overviewValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  overviewLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  contentContainer: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  budgetsList: {
    padding: 16,
    gap: 12,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    minHeight: 300,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: 8,
    lineHeight: 20,
  },
  createButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 24,
  },
  createButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});
