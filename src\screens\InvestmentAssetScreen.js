import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  RefreshControl,
  Alert,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useSQLiteContext } from 'expo-sqlite';
import { Colors } from '../constants/colors';

/**
 * Yatırım varlığı detay ekranı
 * @param {Object} props Component props
 * @param {Object} props.navigation Navigation objesi
 * @param {Object} props.route Route objesi
 */
const InvestmentAssetScreen = ({ navigation, route }) => {
  const { asset } = route.params;
  const db = useSQLiteContext();
  
  const [assetData, setAssetData] = useState(asset);
  const [transactions, setTransactions] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);

  // Varlık ve işlemleri yükle
  const loadData = async () => {
    try {
      setRefreshing(true);
      
      // Varlık detaylarını getir
      const assetResult = await db.getAllAsync(`
        SELECT * FROM investment_assets
        WHERE id = ?
      `, [asset.id]);
      
      if (assetResult.length > 0) {
        setAssetData(assetResult[0]);
      }
      
      // Varlığın işlemlerini getir
      const transactionsResult = await db.getAllAsync(`
        SELECT * FROM investment_transactions
        WHERE asset_id = ?
        ORDER BY date DESC, id DESC
      `, [asset.id]);
      
      setTransactions(transactionsResult);
    } catch (error) {
      console.error('Varlık yükleme hatası:', error);
      Alert.alert('Hata', 'Varlık bilgileri yüklenirken bir hata oluştu.');
    } finally {
      setRefreshing(false);
      setLoading(false);
    }
  };

  // İlk yükleme
  useEffect(() => {
    loadData();
  }, []);

  // Para formatı
  const formatCurrency = (value) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };

  // Tarih formatı
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  // İşlem tipini formatla
  const formatTransactionType = (type) => {
    switch (type) {
      case 'buy': return 'Alım';
      case 'sell': return 'Satım';
      case 'dividend': return 'Temettü';
      case 'interest': return 'Faiz';
      case 'transfer': return 'Transfer';
      default: return type;
    }
  };

  // Varlığı sil
  const handleDeleteAsset = () => {
    Alert.alert(
      'Varlığı Sil',
      'Bu varlığı silmek istediğinize emin misiniz? Bu işlem geri alınamaz ve tüm işlemler de silinecektir.',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              // Önce varlığa ait işlemleri sil
              await db.runAsync('DELETE FROM investment_transactions WHERE asset_id = ?', [asset.id]);
              
              // Sonra varlığı sil
              await db.runAsync('DELETE FROM investment_assets WHERE id = ?', [asset.id]);
              
              Alert.alert('Başarılı', 'Varlık başarıyla silindi.');
              navigation.goBack();
            } catch (error) {
              console.error('Varlık silme hatası:', error);
              Alert.alert('Hata', 'Varlık silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <MaterialIcons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.title}>{assetData.name}</Text>
        <View style={styles.headerButtons}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => navigation.navigate('InvestmentAssetForm', { asset: assetData })}
          >
            <MaterialIcons name="edit" size={24} color="#fff" />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={handleDeleteAsset}
          >
            <MaterialIcons name="delete" size={24} color="#fff" />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={loadData} />
        }
      >
        <View style={styles.assetCard}>
          <View style={styles.assetHeader}>
            <View style={styles.assetSymbol}>
              <Text style={styles.assetSymbolText}>{assetData.symbol}</Text>
            </View>
            <Text style={styles.assetType}>{assetData.type}</Text>
          </View>
          
          <View style={styles.assetDetails}>
            <View style={styles.assetDetail}>
              <Text style={styles.detailLabel}>Güncel Fiyat</Text>
              <Text style={styles.detailValue}>{formatCurrency(assetData.current_price)}</Text>
            </View>
            
            <View style={styles.assetDetail}>
              <Text style={styles.detailLabel}>Alış Fiyatı</Text>
              <Text style={styles.detailValue}>{formatCurrency(assetData.purchase_price)}</Text>
            </View>
            
            <View style={styles.assetDetail}>
              <Text style={styles.detailLabel}>Miktar</Text>
              <Text style={styles.detailValue}>{assetData.quantity}</Text>
            </View>
          </View>
          
          <View style={styles.assetSummary}>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>Toplam Değer</Text>
              <Text style={styles.summaryValue}>
                {formatCurrency(assetData.current_price * assetData.quantity)}
              </Text>
            </View>
            
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>Toplam Maliyet</Text>
              <Text style={styles.summaryValue}>
                {formatCurrency(assetData.purchase_price * assetData.quantity)}
              </Text>
            </View>
            
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>Kar/Zarar</Text>
              <Text
                style={[
                  styles.summaryValue,
                  (assetData.current_price - assetData.purchase_price) * assetData.quantity >= 0
                    ? styles.positiveValue
                    : styles.negativeValue
                ]}
              >
                {formatCurrency(
                  (assetData.current_price - assetData.purchase_price) * assetData.quantity
                )}
                {' '}
                ({(((assetData.current_price - assetData.purchase_price) / assetData.purchase_price) * 100).toFixed(2)}%)
              </Text>
            </View>
          </View>
        </View>

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>İşlemler</Text>
            <TouchableOpacity
              onPress={() => navigation.navigate('InvestmentTransactionForm', { asset: assetData })}
            >
              <Text style={styles.sectionAction}>Yeni Ekle</Text>
            </TouchableOpacity>
          </View>

          {transactions.length === 0 ? (
            <View style={styles.emptyState}>
              <MaterialIcons name="receipt-long" size={48} color="#ccc" />
              <Text style={styles.emptyText}>Henüz işlem bulunmuyor</Text>
              <TouchableOpacity
                style={styles.emptyButton}
                onPress={() => navigation.navigate('InvestmentTransactionForm', { asset: assetData })}
              >
                <Text style={styles.emptyButtonText}>İşlem Ekle</Text>
              </TouchableOpacity>
            </View>
          ) : (
            transactions.map((transaction) => (
              <TouchableOpacity
                key={transaction.id}
                style={styles.transactionItem}
                onPress={() => navigation.navigate('InvestmentTransactionDetail', { transaction })}
              >
                <View style={styles.transactionHeader}>
                  <View style={styles.transactionType}>
                    <MaterialIcons
                      name={transaction.type === 'buy' ? 'arrow-downward' : 'arrow-upward'}
                      size={16}
                      color="#fff"
                    />
                    <Text style={styles.transactionTypeText}>
                      {formatTransactionType(transaction.type)}
                    </Text>
                  </View>
                  <Text style={styles.transactionDate}>{formatDate(transaction.date)}</Text>
                </View>
                
                <View style={styles.transactionDetails}>
                  <View style={styles.transactionDetail}>
                    <Text style={styles.transactionLabel}>Miktar</Text>
                    <Text style={styles.transactionValue}>{transaction.quantity}</Text>
                  </View>
                  
                  <View style={styles.transactionDetail}>
                    <Text style={styles.transactionLabel}>Fiyat</Text>
                    <Text style={styles.transactionValue}>{formatCurrency(transaction.price)}</Text>
                  </View>
                  
                  <View style={styles.transactionDetail}>
                    <Text style={styles.transactionLabel}>Toplam</Text>
                    <Text style={styles.transactionValue}>
                      {formatCurrency(transaction.price * transaction.quantity)}
                    </Text>
                  </View>
                </View>
                
                {transaction.notes && (
                  <Text style={styles.transactionNotes}>{transaction.notes}</Text>
                )}
              </TouchableOpacity>
            ))
          )}
        </View>
      </ScrollView>

      <TouchableOpacity
        style={styles.fab}
        onPress={() => navigation.navigate('InvestmentTransactionForm', { asset: assetData })}
      >
        <MaterialIcons name="add" size={24} color="#fff" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.PRIMARY,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  headerButtons: {
    flexDirection: 'row',
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  content: {
    flex: 1,
  },
  assetCard: {
    backgroundColor: '#fff',
    margin: 16,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  assetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  assetSymbol: {
    backgroundColor: Colors.PRIMARY,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  assetSymbolText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  assetType: {
    fontSize: 14,
    color: '#666',
    textTransform: 'capitalize',
  },
  assetDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  assetDetail: {
    flex: 1,
  },
  detailLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  assetSummary: {
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    paddingTop: 16,
  },
  summaryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 14,
    color: '#666',
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  positiveValue: {
    color: Colors.INCOME,
  },
  negativeValue: {
    color: Colors.EXPENSE,
  },
  section: {
    margin: 16,
    marginTop: 0,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  sectionAction: {
    fontSize: 14,
    color: Colors.PRIMARY,
    fontWeight: '500',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
    backgroundColor: '#fff',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    marginTop: 16,
    marginBottom: 16,
  },
  emptyButton: {
    backgroundColor: Colors.PRIMARY,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  emptyButtonText: {
    color: '#fff',
    fontWeight: '500',
  },
  transactionItem: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  transactionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  transactionType: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  transactionTypeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  transactionDate: {
    fontSize: 14,
    color: '#666',
  },
  transactionDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  transactionDetail: {
    flex: 1,
  },
  transactionLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  transactionValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  transactionNotes: {
    fontSize: 14,
    color: '#666',
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    paddingTop: 8,
  },
  fab: {
    position: 'absolute',
    right: 16,
    bottom: 16,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 8,
  },
});

export default InvestmentAssetScreen;
