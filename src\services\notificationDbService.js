/**
 * Bildirim Veritabanı Servisi
 * Bildirim veritabanı işlemlerini yönetir
 */
import { format, parseISO, isAfter, isBefore, addDays, addWeeks, addMonths, addYears } from 'date-fns';
import * as notificationService from './NotificationService';

/**
 * Bildirim oluşturur
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} notification - Bildirim verileri
 * @returns {Promise<number>} Oluşturulan bildirim ID'si
 */
export const createNotification = async (db, notification) => {
  try {
    // Bildirim verilerini doğrula
    if (!notification.title || !notification.message || !notification.type) {
      throw new Error('Geçersiz bildirim verileri');
    }

    // Bildirim ayarlarını kontrol et
    const settings = await getNotificationSettings(db, notification.type);

    if (!settings || !settings.is_enabled) {
      console.log(`${notification.type} tipi bildirimler devre dışı`);
      return null;
    }

    // Bildirim verilerini hazırla
    const notificationData = {
      title: notification.title,
      message: notification.message,
      type: notification.type,
      status: 'pending',
      priority: notification.priority || 'normal',
      scheduled_at: notification.scheduled_at || new Date().toISOString(),
      repeat_type: notification.repeat_type || 'once',
      repeat_interval: notification.repeat_interval || 1,
      repeat_days: notification.repeat_days ? JSON.stringify(notification.repeat_days) : null,
      repeat_months: notification.repeat_months ? JSON.stringify(notification.repeat_months) : null,
      repeat_end_date: notification.repeat_end_date || null,
      category_id: notification.category_id || null,
      related_id: notification.related_id || null,
      related_type: notification.related_type || null,
      data: notification.data ? JSON.stringify(notification.data) : null,
      is_enabled: 1
    };

    // Bildirimi veritabanına kaydet
    const result = await db.runAsync(`
      INSERT INTO notifications (
        title, message, type, status, priority, scheduled_at,
        repeat_type, repeat_interval, repeat_days, repeat_months, repeat_end_date,
        category_id, related_id, related_type, data, is_enabled
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      notificationData.title,
      notificationData.message,
      notificationData.type,
      notificationData.status,
      notificationData.priority,
      notificationData.scheduled_at,
      notificationData.repeat_type,
      notificationData.repeat_interval,
      notificationData.repeat_days,
      notificationData.repeat_months,
      notificationData.repeat_end_date,
      notificationData.category_id,
      notificationData.related_id,
      notificationData.related_type,
      notificationData.data,
      notificationData.is_enabled
    ]);

    const notificationId = result.lastInsertRowId;

    // Bildirimi zamanla
    await scheduleNotification(db, notificationId);

    return notificationId;
  } catch (error) {
    console.error('Bildirim oluşturma hatası:', error);
    throw error;
  }
};

/**
 * Bildirimi günceller
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} notificationId - Bildirim ID'si
 * @param {Object} notification - Güncellenecek bildirim verileri
 * @returns {Promise<void>}
 */
export const updateNotification = async (db, notificationId, notification) => {
  try {
    // Mevcut bildirimi getir
    const existingNotification = await getNotificationById(db, notificationId);

    if (!existingNotification) {
      throw new Error('Bildirim bulunamadı');
    }

    // Bildirim verilerini hazırla
    const notificationData = {
      title: notification.title || existingNotification.title,
      message: notification.message || existingNotification.message,
      type: notification.type || existingNotification.type,
      status: notification.status || existingNotification.status,
      priority: notification.priority || existingNotification.priority,
      scheduled_at: notification.scheduled_at || existingNotification.scheduled_at,
      repeat_type: notification.repeat_type || existingNotification.repeat_type,
      repeat_interval: notification.repeat_interval || existingNotification.repeat_interval,
      repeat_days: notification.repeat_days ? JSON.stringify(notification.repeat_days) : existingNotification.repeat_days,
      repeat_months: notification.repeat_months ? JSON.stringify(notification.repeat_months) : existingNotification.repeat_months,
      repeat_end_date: notification.repeat_end_date || existingNotification.repeat_end_date,
      category_id: notification.category_id !== undefined ? notification.category_id : existingNotification.category_id,
      related_id: notification.related_id !== undefined ? notification.related_id : existingNotification.related_id,
      related_type: notification.related_type || existingNotification.related_type,
      data: notification.data ? JSON.stringify(notification.data) : existingNotification.data,
      is_enabled: notification.is_enabled !== undefined ? notification.is_enabled : existingNotification.is_enabled
    };

    // Bildirimi güncelle
    await db.runAsync(`
      UPDATE notifications
      SET title = ?, message = ?, type = ?, status = ?, priority = ?,
          scheduled_at = ?, repeat_type = ?, repeat_interval = ?,
          repeat_days = ?, repeat_months = ?, repeat_end_date = ?,
          category_id = ?, related_id = ?, related_type = ?,
          data = ?, is_enabled = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [
      notificationData.title,
      notificationData.message,
      notificationData.type,
      notificationData.status,
      notificationData.priority,
      notificationData.scheduled_at,
      notificationData.repeat_type,
      notificationData.repeat_interval,
      notificationData.repeat_days,
      notificationData.repeat_months,
      notificationData.repeat_end_date,
      notificationData.category_id,
      notificationData.related_id,
      notificationData.related_type,
      notificationData.data,
      notificationData.is_enabled,
      notificationId
    ]);

    // Bildirimi yeniden zamanla
    if (notificationData.status === 'pending' && notificationData.is_enabled) {
      await scheduleNotification(db, notificationId);
    } else {
      // Bildirimi iptal et
      await cancelScheduledNotification(db, notificationId);
    }
  } catch (error) {
    console.error('Bildirim güncelleme hatası:', error);
    throw error;
  }
};

/**
 * Bildirimi siler
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} notificationId - Bildirim ID'si
 * @returns {Promise<void>}
 */
export const deleteNotification = async (db, notificationId) => {
  try {
    // Bildirimi iptal et
    await cancelScheduledNotification(db, notificationId);

    // Bildirimi sil
    await db.runAsync(`
      DELETE FROM notifications
      WHERE id = ?
    `, [notificationId]);
  } catch (error) {
    console.error('Bildirim silme hatası:', error);
    throw error;
  }
};

/**
 * Bildirimi ID'ye göre getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} notificationId - Bildirim ID'si
 * @returns {Promise<Object>} Bildirim
 */
export const getNotificationById = async (db, notificationId) => {
  try {
    const notification = await db.getFirstAsync(`
      SELECT * FROM notifications
      WHERE id = ?
    `, [notificationId]);

    if (!notification) {
      return null;
    }

    // JSON alanlarını parse et
    if (notification.data) {
      try {
        notification.data = JSON.parse(notification.data);
      } catch (e) {
        notification.data = {};
      }
    }

    if (notification.repeat_days) {
      try {
        notification.repeat_days = JSON.parse(notification.repeat_days);
      } catch (e) {
        notification.repeat_days = [];
      }
    }

    if (notification.repeat_months) {
      try {
        notification.repeat_months = JSON.parse(notification.repeat_months);
      } catch (e) {
        notification.repeat_months = [];
      }
    }

    return notification;
  } catch (error) {
    console.error('Bildirim getirme hatası:', error);
    throw error;
  }
};

/**
 * Bildirimleri getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} options - Sorgu seçenekleri
 * @param {string} options.type - Bildirim tipi
 * @param {string} options.status - Bildirim durumu
 * @param {boolean} options.onlyEnabled - Sadece etkin bildirimleri getir
 * @param {number} options.limit - Limit
 * @param {number} options.offset - Offset
 * @returns {Promise<Array>} Bildirimler
 */
export const getNotifications = async (db, options = {}) => {
  try {
    let query = `
      SELECT n.*, c.name as category_name, c.icon as category_icon, c.color as category_color
      FROM notifications n
      LEFT JOIN categories c ON n.category_id = c.id
      WHERE 1=1
    `;

    const params = [];

    if (options.type) {
      query += ' AND n.type = ?';
      params.push(options.type);
    }

    if (options.status) {
      query += ' AND n.status = ?';
      params.push(options.status);
    }

    if (options.onlyEnabled) {
      query += ' AND n.is_enabled = 1';
    }

    query += ' ORDER BY n.scheduled_at DESC';

    if (options.limit) {
      query += ' LIMIT ?';
      params.push(options.limit);

      if (options.offset) {
        query += ' OFFSET ?';
        params.push(options.offset);
      }
    }

    const notifications = await db.getAllAsync(query, params);

    // JSON alanlarını parse et
    return notifications.map(notification => {
      if (notification.data) {
        try {
          notification.data = JSON.parse(notification.data);
        } catch (e) {
          notification.data = {};
        }
      }

      if (notification.repeat_days) {
        try {
          notification.repeat_days = JSON.parse(notification.repeat_days);
        } catch (e) {
          notification.repeat_days = [];
        }
      }

      if (notification.repeat_months) {
        try {
          notification.repeat_months = JSON.parse(notification.repeat_months);
        } catch (e) {
          notification.repeat_months = [];
        }
      }

      return notification;
    });
  } catch (error) {
    console.error('Bildirimleri getirme hatası:', error);
    throw error;
  }
};

/**
 * Bildirim ayarlarını getirir
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {string} type - Bildirim tipi
 * @returns {Promise<Object>} Bildirim ayarları
 */
export const getNotificationSettings = async (db, type) => {
  try {
    return await db.getFirstAsync(`
      SELECT * FROM notification_settings
      WHERE type = ?
    `, [type]);
  } catch (error) {
    console.error('Bildirim ayarları getirme hatası:', error);
    throw error;
  }
};

/**
 * Bildirim ayarlarını günceller
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {string} type - Bildirim tipi
 * @param {Object} settings - Ayarlar
 * @returns {Promise<void>}
 */
export const updateNotificationSettings = async (db, type, settings) => {
  try {
    await db.runAsync(`
      UPDATE notification_settings
      SET is_enabled = ?, sound_enabled = ?, vibration_enabled = ?,
          quiet_hours_start = ?, quiet_hours_end = ?, quiet_hours_enabled = ?,
          updated_at = CURRENT_TIMESTAMP
      WHERE type = ?
    `, [
      settings.is_enabled ? 1 : 0,
      settings.sound_enabled ? 1 : 0,
      settings.vibration_enabled ? 1 : 0,
      settings.quiet_hours_start || null,
      settings.quiet_hours_end || null,
      settings.quiet_hours_enabled ? 1 : 0,
      type
    ]);
  } catch (error) {
    console.error('Bildirim ayarları güncelleme hatası:', error);
    throw error;
  }
};

/**
 * Bildirimi zamanlar
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} notificationId - Bildirim ID'si
 * @returns {Promise<string>} Expo bildirim ID'si
 */
export const scheduleNotification = async (db, notificationId) => {
  try {
    // Bildirimi getir
    const notification = await getNotificationById(db, notificationId);

    if (!notification || !notification.is_enabled || notification.status !== 'pending') {
      return null;
    }

    // Bildirim ayarlarını kontrol et
    const settings = await getNotificationSettings(db, notification.type);

    if (!settings || !settings.is_enabled) {
      console.log(`${notification.type} tipi bildirimler devre dışı`);
      return null;
    }

    // Sessiz saatleri kontrol et
    if (settings.quiet_hours_enabled && settings.quiet_hours_start && settings.quiet_hours_end) {
      const now = new Date();
      const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;

      if (isTimeInRange(currentTime, settings.quiet_hours_start, settings.quiet_hours_end)) {
        console.log('Sessiz saatler içinde, bildirim gönderilmeyecek');
        return null;
      }
    }

    // Zamanlanmış tarihi kontrol et
    const scheduledDate = parseISO(notification.scheduled_at);

    if (isBefore(scheduledDate, new Date()) && notification.repeat_type === 'once') {
      // Geçmiş tarihli tek seferlik bildirim, durumu güncelle
      await db.runAsync(`
        UPDATE notifications
        SET status = 'cancelled', updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `, [notificationId]);

      console.log(`Bildirim #${notificationId} geçmiş tarihli, iptal edildi`);
      return null;
    }

    // Bildirim içeriğini hazırla
    const notificationContent = {
      title: notification.title,
      body: notification.message,
      data: {
        notificationId: notification.id,
        type: notification.type,
        relatedId: notification.related_id,
        relatedType: notification.related_type,
        ...notification.data
      }
    };

    // Tetikleyiciyi hazırla
    let trigger = null;

    if (notification.repeat_type === 'once') {
      // Tek seferlik bildirim
      trigger = {
        type: 'date',
        timestamp: scheduledDate.getTime()
      };
    } else {
      // Tekrarlanan bildirim
      trigger = {
        type: 'daily',
        hour: scheduledDate.getHours(),
        minute: scheduledDate.getMinutes(),
        repeats: true
      };

      // Tekrarlama tipine göre tetikleyiciyi ayarla
      switch (notification.repeat_type) {
        case 'daily':
          // Günlük bildirim, ek ayar gerekmez
          break;
        case 'weekly':
          // Haftalık bildirim, haftanın günlerini ayarla
          trigger.type = 'weekly';
          if (notification.repeat_days && notification.repeat_days.length > 0) {
            trigger.weekday = notification.repeat_days[0]; // Expo sadece tek gün destekliyor
          } else {
            trigger.weekday = scheduledDate.getDay();
          }
          break;
        case 'monthly':
          // Aylık bildirim, ayın gününü ayarla
          trigger.type = 'monthly';
          trigger.day = scheduledDate.getDate();
          break;
        case 'yearly':
          // Yıllık bildirim, ayın gününü ve ayı ayarla
          trigger.type = 'yearly';
          trigger.day = scheduledDate.getDate();
          trigger.month = scheduledDate.getMonth() + 1; // 1-12 arası
          break;
      }
    }

    // Bildirimi zamanla
    const expoNotificationId = await notificationService.sendNotification({
      title: notificationContent.title,
      body: notificationContent.body,
      data: notificationContent.data,
      trigger
    });

    // Expo bildirim ID'sini kaydet
    await db.runAsync(`
      UPDATE notifications
      SET expo_notification_id = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [expoNotificationId, notificationId]);

    return expoNotificationId;
  } catch (error) {
    console.error('Bildirim zamanlama hatası:', error);
    throw error;
  }
};

/**
 * Zamanlanmış bildirimi iptal eder
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} notificationId - Bildirim ID'si
 * @returns {Promise<void>}
 */
export const cancelScheduledNotification = async (db, notificationId) => {
  try {
    // Bildirimi getir
    const notification = await getNotificationById(db, notificationId);

    if (!notification) {
      return;
    }

    // Expo bildirim ID'si varsa iptal et
    if (notification.expo_notification_id) {
      await notificationService.cancelNotification(notification.expo_notification_id);
    }

    // Bildirim durumunu güncelle
    await db.runAsync(`
      UPDATE notifications
      SET status = 'cancelled', updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [notificationId]);
  } catch (error) {
    console.error('Zamanlanmış bildirim iptal hatası:', error);
    throw error;
  }
};

/**
 * Bildirimi okundu olarak işaretler
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} notificationId - Bildirim ID'si
 * @returns {Promise<void>}
 */
export const markNotificationAsRead = async (db, notificationId) => {
  try {
    // Bildirimi getir
    const notification = await getNotificationById(db, notificationId);

    if (!notification) {
      return;
    }

    // Bildirim durumunu güncelle
    await db.runAsync(`
      UPDATE notifications
      SET status = 'read', updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [notificationId]);

    // Bildirim geçmişine ekle
    await db.runAsync(`
      INSERT INTO notification_history (
        notification_id, title, message, status, sent_at, read_at, data
      )
      VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?)
    `, [
      notificationId,
      notification.title,
      notification.message,
      'read',
      notification.scheduled_at,
      notification.data ? JSON.stringify(notification.data) : null
    ]);
  } catch (error) {
    console.error('Bildirim okundu işaretleme hatası:', error);
    throw error;
  }
};

/**
 * Bekleyen bildirimleri kontrol eder ve zamanlar
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<number>} Zamanlanan bildirim sayısı
 */
export const checkPendingNotifications = async (db) => {
  try {
    // Bekleyen bildirimleri getir
    const pendingNotifications = await db.getAllAsync(`
      SELECT id FROM notifications
      WHERE status = 'pending' AND is_enabled = 1
    `);

    let scheduledCount = 0;

    // Her bildirimi zamanla
    for (const notification of pendingNotifications) {
      try {
        const expoNotificationId = await scheduleNotification(db, notification.id);

        if (expoNotificationId) {
          scheduledCount++;
        }
      } catch (error) {
        console.error(`Bildirim #${notification.id} zamanlama hatası:`, error);
      }
    }

    return scheduledCount;
  } catch (error) {
    console.error('Bekleyen bildirimleri kontrol hatası:', error);
    throw error;
  }
};

/**
 * Belirli bir zaman aralığında olup olmadığını kontrol eder
 *
 * @param {string} time - Kontrol edilecek zaman (HH:MM)
 * @param {string} start - Başlangıç zamanı (HH:MM)
 * @param {string} end - Bitiş zamanı (HH:MM)
 * @returns {boolean} Zaman aralığında mı
 */
const isTimeInRange = (time, start, end) => {
  // 24 saat formatında karşılaştır
  if (start <= end) {
    return time >= start && time <= end;
  } else {
    // Gece yarısını geçen aralık (örn. 22:00 - 06:00)
    return time >= start || time <= end;
  }
};
