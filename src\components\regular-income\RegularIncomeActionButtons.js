import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ActivityIndicator } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../../constants/colors';

/**
 * Düzenli gelir eylem butonları bileşeni
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {boolean} props.isEditMode - Düzenleme modu mu?
 * @param {boolean} props.isSubmitting - Form gönderiliyor mu?
 * @param {Function} props.handleSubmit - Gönderme işleyici
 * @param {Function} props.handleCancel - İptal işleyici
 * @returns {JSX.Element} Eylem butonları bileşeni
 */
const RegularIncomeActionButtons = React.memo(({ 
  isEditMode, 
  isSubmitting, 
  handleSubmit, 
  handleCancel 
}) => {
  return (
    <View style={styles.buttonContainer}>
      <TouchableOpacity 
        style={[
          styles.button, 
          isEditMode ? styles.updateButton : styles.saveButton,
          isSubmitting && styles.buttonDisabled
        ]} 
        onPress={handleSubmit}
        disabled={isSubmitting}
        accessibilityLabel={isEditMode ? "Geliri güncelle" : "Geliri kaydet"}
        accessibilityRole="button"
      >
        {isSubmitting ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color="#fff" />
            <Text style={styles.buttonText}>
              {isEditMode ? 'Güncelleniyor...' : 'Kaydediliyor...'}
            </Text>
          </View>
        ) : (
          <>
            <MaterialIcons 
              name={isEditMode ? 'edit' : 'save'} 
              size={20} 
              color="#fff" 
            />
            <Text style={styles.buttonText}>
              {isEditMode ? 'Geliri Güncelle' : 'Geliri Kaydet'}
            </Text>
          </>
        )}
      </TouchableOpacity>
      
      <TouchableOpacity 
        style={[styles.button, styles.cancelButton, isSubmitting && styles.buttonDisabled]} 
        onPress={handleCancel}
        disabled={isSubmitting}
        accessibilityLabel="İptal et"
        accessibilityRole="button"
      >
        <MaterialIcons name="cancel" size={20} color="#fff" />
        <Text style={styles.buttonText}>İptal</Text>
      </TouchableOpacity>
    </View>
  );
});

// Enhanced styles with Material Design 3.0 principles
const styles = StyleSheet.create({
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
    marginBottom: 32,
    paddingHorizontal: 8,
  },
  button: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    transform: [{ scale: 1 }],
  },
  saveButton: {
    backgroundColor: Colors.SUCCESS,
    marginRight: 12,
    shadowColor: Colors.SUCCESS,
  },
  updateButton: {
    backgroundColor: Colors.PRIMARY,
    marginRight: 12,
    shadowColor: Colors.PRIMARY,
  },
  cancelButton: {
    backgroundColor: Colors.GRAY_500,
    shadowColor: Colors.GRAY_500,
  },
  buttonDisabled: {
    opacity: 0.6,
    elevation: 1,
    transform: [{ scale: 0.98 }],
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    letterSpacing: 0.5,
    marginLeft: 8,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  }
});

export default RegularIncomeActionButtons;
