import React, { useState, useEffect, useMemo } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated, ScrollView, Platform } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { Picker } from '@react-native-picker/picker';
import { format, addDays, addMonths, addYears, isValid } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Colors } from '../../constants/colors';

/**
 * Düzenli gelir için tekrarlama ayarları bileşeni
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {string} props.recurrenceType - Tekrarlama tipi (daily, weekly, monthly, yearly, custom)
 * @param {Function} props.setRecurrenceType - Tekrarlama tipi değiştirme fonksiyonu
 * @param {string} props.recurrenceInterval - Tekrarlama aralığı
 * @param {Function} props.setRecurrenceInterval - Tekrarlama aralığı değiştirme fonksiyonu
 * @param {string} props.paymentDay - Ödeme günü
 * @param {Function} props.setPaymentDay - Ödeme günü değiştirme fonksiyonu
 * @param {Date} props.nextPaymentDate - Bir sonraki ödeme tarihi
 * @returns {JSX.Element} Tekrarlama ayarları bileşeni
 */
const RegularIncomeRecurrenceSettings = React.memo(({
  recurrenceType,
  setRecurrenceType,
  recurrenceInterval,
  setRecurrenceInterval,
  paymentDay,
  setPaymentDay,
  nextPaymentDate
}) => {
  // Yardım bölümü için animasyon değeri
  const helpAnimation = React.useRef(new Animated.Value(0)).current;
  
  // Yardım görünürlüğü durumu
  const [showHelp, setShowHelp] = useState(false);

  /**
   * Tarihi güvenli bir şekilde formatlar
   * @param {Date|string|null} date - Formatlanacak tarih
   * @param {string} formatStr - Format string
   * @returns {string} Formatlanmış tarih veya boş string
   */
  const safeFormatDate = (date, formatStr = 'dd MMMM yyyy') => {
    if (!date) return '';
    
    try {
      const dateObj = date instanceof Date ? date : new Date(date);
      if (!isValid(dateObj)) return '';
      
      return format(dateObj, formatStr, { locale: tr });
    } catch (error) {
      console.warn('Date formatting error:', error);
      return '';
    }
  };
  
  // Tekrarlama tipi seçeneğini değiştirdiğinde interval sıfırlama
  useEffect(() => {
    if (recurrenceType !== 'custom') {
      setRecurrenceInterval('1');
    }
  }, [recurrenceType, setRecurrenceInterval]);
  
  // Yardım görünürlüğü değiştiğinde animasyonu çalıştır
  useEffect(() => {
    Animated.timing(helpAnimation, {
      toValue: showHelp ? 1 : 0,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [showHelp, helpAnimation]);
  
  // Animasyon değerlerini hesapla
  const helpContainerHeight = helpAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 180], // Tahmini yükseklik
  });
  
  const helpOpacity = helpAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 1],
  });
  
  // Yardım görünürlüğünü değiştir
  const toggleHelp = () => {
    setShowHelp(!showHelp);
  };
  
  // Tekrarlama seçenekleri
  const recurrenceOptions = useMemo(() => [
    { value: 'monthly', label: 'Aylık', icon: 'date-range' },
    { value: 'daily', label: 'Günlük', icon: 'today' },
    { value: 'weekly', label: 'Haftalık', icon: 'view-week' },
    { value: 'yearly', label: 'Yıllık', icon: 'event' },
    { value: 'custom', label: 'Özel', icon: 'tune' }
  ], []);
  
  // Seçili tekrarlama bilgilerini al
  const selectedRecurrence = useMemo(() => 
    recurrenceOptions.find(option => option.value === recurrenceType) || recurrenceOptions[0],
  [recurrenceType, recurrenceOptions]);
  
  // Aralık için picker seçenekleri
  const intervalOptions = useMemo(() => {
    const options = [];
    const max = recurrenceType === 'yearly' ? 10 : recurrenceType === 'monthly' ? 24 : 30;
    
    for (let i = 1; i <= max; i++) {
      options.push({
        value: String(i),
        label: String(i)
      });
    }
    return options;
  }, [recurrenceType]);
  
  // Ödeme günleri için picker seçenekleri
  const paymentDayOptions = useMemo(() => {
    const options = [];
    
    // Özel durum olarak son gün seçeneği ekle
    options.push({
      value: '0',
      label: 'Ayın son günü'
    });
    
    // 1'den 31'e kadar günleri ekle
    for (let i = 1; i <= 31; i++) {
      options.push({
        value: String(i),
        label: `${i}. gün`
      });
    }
    return options;
  }, []);
  
  // Tekrarlama tipine göre aralık metni
  const getIntervalLabel = useMemo(() => {
    const labels = {
      daily: 'gün',
      weekly: 'hafta',
      monthly: 'ay',
      yearly: 'yıl',
      custom: 'gün'
    };
    
    return labels[recurrenceType] || 'ay';
  }, [recurrenceType]);
  
  return (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <View style={styles.sectionTitleContainer}>
          <View style={styles.sectionIconContainer}>
            <MaterialIcons name="repeat" size={20} color={Colors.PRIMARY} />
          </View>
          <Text style={styles.sectionTitleText}>Tekrarlama Ayarları</Text>
          
          {/* Tamamlanma durumunu göster */}
          {recurrenceType && (recurrenceType !== 'monthly' || paymentDay) ? (
            <View style={styles.completionBadge}>
              <MaterialIcons name="check-circle" size={16} color={Colors.SUCCESS} />
              <Text style={styles.completionBadgeText}>Tamamlandı</Text>
            </View>
          ) : (
            <View style={styles.incompleteBadge}>
              <MaterialIcons name="error-outline" size={16} color={Colors.WARNING} />
              <Text style={styles.incompleteBadgeText}>Eksik Bilgi</Text>
            </View>
          )}
          
          {/* Yardım butonu */}
          <TouchableOpacity 
            style={[styles.helpButton, showHelp && styles.helpButtonActive]} 
            onPress={toggleHelp}
          >
            <MaterialIcons 
              name={showHelp ? "close" : "help-outline"} 
              size={20} 
              color={showHelp ? Colors.PRIMARY : Colors.GRAY_600} 
            />
          </TouchableOpacity>
        </View>
        <View style={styles.sectionHeaderDivider} />
      </View>
      
      {/* Yardım metni - animasyonlu */}
      <Animated.View style={[
        styles.helpContainer, 
        { 
          height: helpContainerHeight, 
          opacity: helpOpacity,
          overflow: 'hidden'
        }
      ]}>
        <Text style={styles.helpTitle}>Tekrarlama Ayarları Hakkında</Text>
        <Text style={styles.helpText}>
          Bu bölümde düzenli gelirinizin ne sıklıkta tekrarlanacağını belirleyebilirsiniz. 
          Aylık tekrarlama için ayın hangi gününde ödeme alacağınızı seçmelisiniz.
        </Text>
        <View style={styles.helpTipContainer}>
          <MaterialIcons name="lightbulb-outline" size={16} color={Colors.WARNING} style={{marginRight: 6}} />
          <Text style={styles.helpTip}>
            İpucu: Eğer düzensiz aralıklarla gelir alıyorsanız, "Özel" seçeneğini kullanabilirsiniz.
          </Text>
        </View>
      </Animated.View>
      
      <View style={styles.inputGroup}>
        <Text style={styles.label}>Tekrarlama Sıklığı</Text>
        <View style={styles.pickerContainer}>
          <View style={styles.pickerIconContainer}>
            <MaterialIcons name={selectedRecurrence.icon} size={18} color={Colors.PRIMARY} />
          </View>
          <Picker
            selectedValue={recurrenceType}
            onValueChange={(itemValue) => setRecurrenceType(itemValue)}
            style={styles.picker}
            accessibilityLabel="Tekrarlama sıklığı seçici"
            mode="dropdown"
          >
            {recurrenceOptions.map((option) => (
              <Picker.Item 
                key={option.value} 
                label={option.label}
                value={option.value}
              />
            ))}
          </Picker>
        </View>
      </View>
      
      {recurrenceType === 'monthly' && (
        <View style={styles.inputGroup}>
          <Text style={styles.label}>
            Ödeme Günü
            <Text style={styles.requiredStar}> *</Text>
          </Text>
          <View style={styles.pickerContainer}>
            <View style={styles.pickerIconContainer}>
              <MaterialIcons name="calendar-today" size={18} color={Colors.PRIMARY} />
            </View>
            <Picker
              selectedValue={paymentDay}
              onValueChange={(itemValue) => setPaymentDay(itemValue)}
              style={styles.picker}
              accessibilityLabel="Ödeme günü seçici"
              mode="dropdown"
            >
              <Picker.Item label="Ödeme günü seçin" value="" />
              {paymentDayOptions.map((option) => (
                <Picker.Item 
                  key={option.value} 
                  label={option.label}
                  value={option.value}
                />
              ))}
            </Picker>
          </View>
            {!paymentDay && (
            <View style={styles.helperTextContainer}>
              <MaterialIcons name="info-outline" size={14} color={Colors.GRAY_600} style={{marginRight: 5}} />
              <Text style={styles.helperText}>
                Lütfen ayın hangi günü ödeneceğini seçin
              </Text>
            </View>
          )}
          
          {paymentDay && (
            <View style={styles.infoTextContainer}>
              <MaterialIcons name="calendar-today" size={18} color={Colors.PRIMARY} style={styles.infoIcon} />
              <View style={styles.infoTextContent}>                <Text style={styles.intervalHelperText}>
                  {paymentDay === '0' 
                    ? `Her ayın son günü tekrarlanacak`                    : `Her ayın ${paymentDay}. günü tekrarlanacak`}
                </Text>
                {nextPaymentDate && safeFormatDate(nextPaymentDate) && (
                  <View style={styles.nextPaymentContainer}>
                    <MaterialIcons name="event-available" size={14} color={Colors.SUCCESS} style={{marginRight: 4}} />
                    <Text style={styles.nextPaymentText}>
                      <Text>Sonraki ödeme: </Text><Text style={styles.nextPaymentDate}>{safeFormatDate(nextPaymentDate)}</Text>
                    </Text>
                  </View>
                )}
              </View>
            </View>
          )}
        </View>
      )}
      
      {recurrenceType !== 'monthly' && (
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Aralık</Text>
          <View style={styles.rowContainer}>
            <View style={styles.pickerContainerSmall}>
              <Picker
                selectedValue={recurrenceInterval}
                onValueChange={(itemValue) => setRecurrenceInterval(itemValue)}
                style={styles.pickerSmall}
                accessibilityLabel="Tekrarlama aralığı seçici"
                mode="dropdown"
              >
                {intervalOptions.map((option) => (
                  <Picker.Item 
                    key={option.value} 
                    label={option.label}
                    value={option.value}
                  />
                ))}
              </Picker>
            </View>
            <View style={styles.intervalTextContainer}>
              <Text style={styles.intervalText}>
                {getIntervalLabel} aralıklarla
              </Text>
            </View>            </View>
          
          <View style={styles.infoTextContainer}>
            <MaterialIcons
              name={recurrenceType === 'daily' ? 'today' :
                     recurrenceType === 'weekly' ? 'view-week' :
                     recurrenceType === 'yearly' ? 'event' : 
                     recurrenceType === 'custom' ? 'tune' : 'repeat'}
              size={20}
              color={Colors.PRIMARY}
              style={styles.infoIcon}
            />            <View style={styles.infoTextContent}>
                <Text style={styles.intervalHelperText}>
                  {recurrenceType === 'daily' 
                    ? `Her ${recurrenceInterval} günde bir tekrarlanacak`
                    : recurrenceType === 'weekly' 
                      ? `Her ${recurrenceInterval} haftada bir tekrarlanacak`
                      : recurrenceType === 'yearly' 
                        ? `Her ${recurrenceInterval} yılda bir tekrarlanacak`                        : `Her ${recurrenceInterval} günde bir tekrarlanacak`}
                </Text>                {nextPaymentDate && safeFormatDate(nextPaymentDate) && (
                  <View style={styles.nextPaymentContainer}>
                    <MaterialIcons name="event-available" size={14} color={Colors.SUCCESS} style={{marginRight: 4}} />
                    <Text style={styles.nextPaymentText}>
                      <Text>Sonraki ödeme: </Text><Text style={styles.nextPaymentDate}>{safeFormatDate(nextPaymentDate)}</Text>
                    </Text>
                  </View>
                )}
              </View>
            </View>
        </View>
      )}
    </View>
  );
});

/**
 * Enhanced modern styles for RegularIncomeRecurrenceSettings component
 * Following Material Design 3.0 principles with improved visual hierarchy
 */
const styles = StyleSheet.create({
  // === Section Container - Enhanced ===
  section: {
    backgroundColor: Colors.WHITE,
    borderRadius: 20,
    marginBottom: 28,
    paddingVertical: 28,
    paddingHorizontal: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.12,
    shadowRadius: 16,
    elevation: 8,
    borderWidth: 1,
    borderColor: Colors.GRAY_100,
    overflow: 'hidden',
  },
  
  // === Section Header - Enhanced ===
  sectionHeader: {
    marginBottom: 24,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Colors.PRIMARY + '15',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    shadowColor: Colors.PRIMARY,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
    borderWidth: 2,
    borderColor: Colors.PRIMARY + '25',
  },
  sectionTitleText: {
    fontSize: 20,
    fontWeight: '800',
    color: Colors.GRAY_900,
    letterSpacing: 0.3,
    flex: 1,
    lineHeight: 28,
  },
  sectionHeaderDivider: {
    height: 3,
    backgroundColor: Colors.PRIMARY + '20',
    marginTop: 18,
    borderRadius: 2,
    shadowColor: Colors.PRIMARY,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  
  // === Completion Badges - Enhanced ===
  completionBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.SUCCESS + '18',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginLeft: 'auto',
    borderWidth: 1,
    borderColor: Colors.SUCCESS + '30',
    shadowColor: Colors.SUCCESS,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  completionBadgeText: {
    color: Colors.SUCCESS,
    fontSize: 12,
    fontWeight: '700',
    marginLeft: 6,
    letterSpacing: 0.2,
  },
  incompleteBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.WARNING + '18',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginLeft: 'auto',
    borderWidth: 1,
    borderColor: Colors.WARNING + '30',
    shadowColor: Colors.WARNING,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  incompleteBadgeText: {
    color: Colors.WARNING,
    fontSize: 12,
    fontWeight: '700',
    marginLeft: 6,
    letterSpacing: 0.2,
  },
  
  // === Help Button and Content - Enhanced ===
  helpButton: {
    marginLeft: 12,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.GRAY_100,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  helpButtonActive: {
    backgroundColor: Colors.PRIMARY + '18',
    borderWidth: 2,
    borderColor: Colors.PRIMARY + '30',
  },
  helpContainer: {
    backgroundColor: Colors.GRAY_50,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    borderLeftWidth: 4,
    borderLeftColor: Colors.PRIMARY,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  helpTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: Colors.GRAY_800,
    marginBottom: 10,
    letterSpacing: 0.2,
  },
  helpText: {
    fontSize: 14,
    color: Colors.GRAY_700,
    lineHeight: 22,
    marginBottom: 12,
    fontWeight: '500',
  },
  helpTipContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.WARNING + '12',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: Colors.WARNING + '25',
  },
  helpTip: {
    fontSize: 13,
    color: Colors.GRAY_800,
    flex: 1,
    lineHeight: 20,
    fontWeight: '500',
    marginLeft: 8,
  },
  
  // === Form Labels - Enhanced ===
  label: {
    fontSize: 16,
    color: Colors.GRAY_800,
    marginBottom: 10,
    fontWeight: '700',
    letterSpacing: 0.3,
  },
  requiredStar: {
    color: Colors.DANGER,
    fontWeight: '800',
    fontSize: 18,
  },
  
  // === Input Groups - Enhanced ===
  inputGroup: {
    marginBottom: 20,
  },
  
  // === Picker Containers - Enhanced ===
  pickerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: Colors.GRAY_200,
    borderRadius: 16,
    backgroundColor: Colors.WHITE,
    marginBottom: 12,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.08,
    shadowRadius: 6,
  },
  pickerContainerSmall: {
    width: 100,
    borderWidth: 2,
    borderColor: Colors.GRAY_200,
    borderRadius: 12,
    backgroundColor: Colors.WHITE,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 4,
  },
  pickerIconContainer: {
    paddingHorizontal: 16,
    height: 56,
    justifyContent: 'center',
    alignItems: 'center',
    borderRightWidth: 2,
    borderRightColor: Colors.GRAY_200,
    backgroundColor: Colors.GRAY_50,
  },
  picker: {
    flex: 1,
    height: 56,
    color: Colors.GRAY_800,
    fontSize: 16,
    fontWeight: '500',
  },
  pickerSmall: {
    height: 48,
    color: Colors.GRAY_800,
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  
  // === Row Layout - Enhanced ===
  rowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 16,
  },// Bilgi metinleri konteyneri - iyileştirilmiş görünüm
  infoTextContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginTop: 8,
    marginBottom: 10,
    backgroundColor: Colors.GRAY_50,
    borderRadius: 10,
    padding: 14,
    borderWidth: 1,
    borderColor: Colors.GRAY_200,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  infoIcon: {
    marginRight: 10,
    alignSelf: 'flex-start',
    marginTop: 2,
  },  intervalTextContainer: {
    marginLeft: 10,
  },
  intervalText: {
    fontSize: 15,
    color: Colors.GRAY_800,
  },
  infoTextContent: {
    flex: 1,
  },
  intervalHelperText: {
    fontSize: 14.5,
    color: Colors.GRAY_800,
    fontWeight: '500',
    marginBottom: 8,
    lineHeight: 20,
  },
  // Sonraki ödeme bilgisi konteynerı - daha çekici görünüm
  nextPaymentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.SUCCESS + '10',
    paddingVertical: 7,
    paddingHorizontal: 10,
    borderRadius: 8,
    alignSelf: 'flex-start',
    borderWidth: 1,
    borderColor: Colors.SUCCESS + '30',
    marginTop: 8,
    shadowColor: Colors.SUCCESS,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    elevation: 1,
  },
  nextPaymentText: {
    fontSize: 13,
    fontWeight: '500',
    color: Colors.SUCCESS,
    marginLeft: 4,
  },
  nextPaymentDate: {
    fontSize: 13,
    color: Colors.SUCCESS,
    fontWeight: '700',
  },
  helperTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: -5,
    marginBottom: 8,
  },
  helperText: {
    fontSize: 13,
    color: Colors.GRAY_600,
  },
});

export default RegularIncomeRecurrenceSettings;