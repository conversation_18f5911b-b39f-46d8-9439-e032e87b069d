import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { MaterialIcons } from '@expo/vector-icons';
import { formatLocaleDate } from '../../utils/dateFormatters';

/**
 * <PERSON><PERSON><PERSON> aralığı seçici bileşeni
 * @param {Object} props Component props
 * @param {Date|null} props.startDate - Başlangıç tarihi
 * @param {Date|null} props.endDate - Bitiş tarihi
 * @param {Function} props.onStartDateChange - Başlangıç tarihi değişim fonksiyonu
 * @param {Function} props.onEndDateChange - Bitiş tarihi değişim fonksiyonu
 * @param {boolean} [props.disabled] - Devre dışı durumu
 * @returns {JSX.Element} DateRangePicker bileşeni
 */
const DateRangePicker = ({
    startDate,
    endDate,
    onStartDateChange,
    onEndDateChange,
    disabled = false
}) => {
    const [showStartPicker, setShowStartPicker] = useState(false);
    const [showEndPicker, setShowEndPicker] = useState(false);

    const handleStartDateChange = (event, selectedDate) => {
        setShowStartPicker(false);
        if (selectedDate) {
            if (endDate && selectedDate > endDate) {
                onEndDateChange(null);
            }
            onStartDateChange(selectedDate);
        }
    };

    const handleEndDateChange = (event, selectedDate) => {
        setShowEndPicker(false);
        if (selectedDate) {
            if (startDate && selectedDate < startDate) return;
            onEndDateChange(selectedDate);
        }
    };

    const handleQuickRange = (days) => {
        const end = new Date();
        const start = new Date();
        start.setDate(start.getDate() - days);
        onStartDateChange(start);
        onEndDateChange(end);
    };

    return (
        <View style={styles.container}>
            <View style={styles.dateContainer}>
                <TouchableOpacity
                    style={[styles.dateButton, startDate && styles.dateButtonSelected]}
                    onPress={() => !disabled && setShowStartPicker(true)}
                    disabled={disabled}
                >
                    <MaterialIcons name="calendar-today" size={20} color="#3498db" />
                    <Text style={styles.dateText}>
                        {startDate ? formatLocaleDate(startDate) : 'Başlangıç Tarihi'}
                    </Text>
                </TouchableOpacity>

                <MaterialIcons name="arrow-forward" size={20} color="#666" />

                <TouchableOpacity
                    style={[styles.dateButton, endDate && styles.dateButtonSelected]}
                    onPress={() => !disabled && startDate && setShowEndPicker(true)}
                    disabled={disabled || !startDate}
                >
                    <MaterialIcons name="calendar-today" size={20} color="#3498db" />
                    <Text style={styles.dateText}>
                        {endDate ? formatLocaleDate(endDate) : 'Bitiş Tarihi'}
                    </Text>
                </TouchableOpacity>
            </View>

            <View style={styles.quickButtonsContainer}>
                <TouchableOpacity
                    style={styles.quickButton}
                    onPress={() => handleQuickRange(7)}
                >
                    <Text style={styles.quickButtonText}>Son 7 Gün</Text>
                </TouchableOpacity>
                <TouchableOpacity
                    style={styles.quickButton}
                    onPress={() => handleQuickRange(30)}
                >
                    <Text style={styles.quickButtonText}>Son 30 Gün</Text>
                </TouchableOpacity>
            </View>

            {showStartPicker && (
                <DateTimePicker
                    value={startDate || new Date()}
                    mode="date"
                    display="default"
                    onChange={handleStartDateChange}
                    maximumDate={new Date()}
                />
            )}

            {showEndPicker && (
                <DateTimePicker
                    value={endDate || new Date()}
                    mode="date"
                    display="default"
                    onChange={handleEndDateChange}
                    minimumDate={startDate}
                    maximumDate={new Date()}
                />
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        backgroundColor: '#fff',
        padding: 16,
        borderRadius: 8,
        elevation: 2,
    },
    dateContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: 16
    },
    dateButton: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        padding: 12,
        backgroundColor: '#f8f9fa',
        borderRadius: 8,
        marginHorizontal: 8
    },
    dateButtonSelected: {
        backgroundColor: '#e8f4ff',
        borderColor: '#3498db',
        borderWidth: 1
    },
    dateText: {
        marginLeft: 8,
        color: '#2c3e50',
        fontSize: 14
    },
    quickButtonsContainer: {
        flexDirection: 'row',
        justifyContent: 'flex-start',
        marginTop: 8
    },
    quickButton: {
        paddingHorizontal: 12,
        paddingVertical: 6,
        backgroundColor: '#f1f5f9',
        borderRadius: 16,
        marginRight: 8
    },
    quickButtonText: {
        color: '#3498db',
        fontSize: 12,
        fontWeight: '500'
    }
});

export default DateRangePicker;