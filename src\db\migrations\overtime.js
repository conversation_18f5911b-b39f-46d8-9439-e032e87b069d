/**
 * <PERSON><PERSON> tabloları için migrasyon
 *
 * @param {object} db - SQLite veritabanı nesnesi
 * @returns {Promise<void>}
 */
export const migrateOvertime = async (db) => {
  try {
    console.log('<PERSON>i tabloları migrasyonu başlatılıyor...');

    // Eski tabloları temizle
    await db.execAsync(`DROP TABLE IF EXISTS overtime`);
    
    // overtime tablosunu oluştur
    const hasOvertimeTable = await db.getFirstAsync(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='overtime'
    `);

    if (!hasOvertimeTable) {
      console.log('overtime tablosu oluşturuluyor...');

      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS overtime (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          title TEXT NOT NULL,
          description TEXT,
          date TEXT NOT NULL,
          start_time TEXT NOT NULL,
          end_time TEXT NOT NULL,
          duration DECIMAL(10,2) NOT NULL,
          hourly_rate DECIMAL(10,2) DEFAULT 0,
          currency TEXT NOT NULL DEFAULT 'TRY',
          is_paid INTEGER DEFAULT 0,
          payment_date TEXT,
          transaction_id INTEGER,
          category_id INTEGER,
          color TEXT,
          notes TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (transaction_id) REFERENCES transactions (id) ON DELETE SET NULL,
          FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE SET NULL
        )
      `);

      // İndeks oluştur
      await db.execAsync(`
        CREATE INDEX IF NOT EXISTS idx_overtime_date
        ON overtime (date)
      `);

      console.log('overtime tablosu başarıyla oluşturuldu.');
    } else {
      console.log('overtime tablosu zaten mevcut.');

      // Tabloda eksik sütunlar var mı kontrol et
      const columns = await db.getAllAsync(`PRAGMA table_info(overtime)`);

      // Sütun adlarını bir diziye dönüştür
      const columnNames = columns.map(col => col.name);

      // Eksik sütunları kontrol et ve ekle
      if (!columnNames.includes('currency')) {
        console.log('currency sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE overtime ADD COLUMN currency TEXT NOT NULL DEFAULT 'TRY'`);
      }

      if (!columnNames.includes('is_paid')) {
        console.log('is_paid sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE overtime ADD COLUMN is_paid INTEGER DEFAULT 0`);
      }

      if (!columnNames.includes('payment_date')) {
        console.log('payment_date sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE overtime ADD COLUMN payment_date TEXT`);
      }

      if (!columnNames.includes('transaction_id')) {
        console.log('transaction_id sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE overtime ADD COLUMN transaction_id INTEGER REFERENCES transactions(id) ON DELETE SET NULL`);
      }

      if (!columnNames.includes('category_id')) {
        console.log('category_id sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE overtime ADD COLUMN category_id INTEGER REFERENCES categories(id) ON DELETE SET NULL`);
      }

      if (!columnNames.includes('color')) {
        console.log('color sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE overtime ADD COLUMN color TEXT`);
      }

      if (!columnNames.includes('notes')) {
        console.log('notes sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE overtime ADD COLUMN notes TEXT`);
      }

      if (!columnNames.includes('updated_at')) {
        console.log('updated_at sütunu ekleniyor...');
        await db.execAsync(`ALTER TABLE overtime ADD COLUMN updated_at DATETIME DEFAULT CURRENT_TIMESTAMP`);
      }
    }

    console.log('Mesai tabloları migrasyonu tamamlandı.');
  } catch (error) {
    console.error('Mesai tabloları migrasyon hatası:', error);
    throw error;
  }
};
