/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> şablonları tablosu için migrasyon
 *
 * @param {object} db - SQLite veritabanı nesnesi
 * @returns {Promise<void>}
 */
export const migrateReminderTemplates = async (db) => {
  try {
    console.log('Hatırlatıcı şablonları tablosu migrasyonu başlatılıyor...');

    // Şablonlar tablosunu kontrol et
    const hasTemplatesTable = await db.getFirstAsync(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='reminder_templates'
    `);

    if (!hasTemplatesTable) {
      console.log('reminder_templates tablosu oluşturuluyor...');

      // Şablonlar tablosunu oluştur
      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS reminder_templates (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          title TEXT NOT NULL,
          message TEXT,
          repeat_type TEXT DEFAULT 'once',
          repeat_interval INTEGER DEFAULT 1,
          repeat_days TEXT,
          repeat_months TEXT,
          priority TEXT DEFAULT 'normal',
          category_id INTEGER,
          group_id INTEGER,
          icon TEXT,
          color TEXT,
          data TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
          FOREIGN KEY (group_id) REFERENCES reminder_groups(id) ON DELETE SET NULL
        )
      `);

      // Şablon-etiket ilişki tablosunu oluştur
      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS reminder_template_tags (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          template_id INTEGER NOT NULL,
          tag_id INTEGER NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (template_id) REFERENCES reminder_templates(id) ON DELETE CASCADE,
          FOREIGN KEY (tag_id) REFERENCES reminder_tags(id) ON DELETE CASCADE,
          UNIQUE(template_id, tag_id)
        )
      `);

      // Varsayılan şablonları ekle
      await db.runAsync(`
        INSERT INTO reminder_templates (name, title, message, repeat_type, priority, icon, color)
        VALUES ('Fatura Ödemesi', 'Fatura Ödemesi', 'Fatura ödeme zamanı geldi', 'monthly', 'high', 'receipt', '#e74c3c')
      `);

      await db.runAsync(`
        INSERT INTO reminder_templates (name, title, message, repeat_type, priority, icon, color)
        VALUES ('Doğum Günü', 'Doğum Günü Hatırlatıcısı', 'Doğum günü bugün', 'yearly', 'normal', 'cake', '#3498db')
      `);

      await db.runAsync(`
        INSERT INTO reminder_templates (name, title, message, repeat_type, priority, icon, color)
        VALUES ('Toplantı', 'Toplantı Hatırlatıcısı', 'Toplantı zamanı', 'once', 'normal', 'people', '#2ecc71')
      `);

      console.log('reminder_templates tablosu başarıyla oluşturuldu.');
    } else {
      console.log('reminder_templates tablosu zaten mevcut.');
    }

    console.log('Hatırlatıcı şablonları migrasyonu tamamlandı.');
  } catch (error) {
    console.error('Hatırlatıcı şablonları migrasyon hatası:', error);
    throw error;
  }
};
