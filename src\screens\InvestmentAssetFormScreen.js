import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useSQLiteContext } from 'expo-sqlite';
import { Colors } from '../constants/colors';

/**
 * Yatırım varlığı ekleme/düzenleme ekranı
 * @param {Object} props Component props
 * @param {Object} props.navigation Navigation objesi
 * @param {Object} props.route Route objesi
 */
const InvestmentAssetFormScreen = ({ navigation, route }) => {
  const { asset } = route.params || {};
  const db = useSQLiteContext();
  
  const [name, setName] = useState(asset?.name || '');
  const [symbol, setSymbol] = useState(asset?.symbol || '');
  const [type, setType] = useState(asset?.type || 'stock');
  const [currentPrice, setCurrentPrice] = useState(asset?.current_price?.toString() || '');
  const [purchasePrice, setPurchasePrice] = useState(asset?.purchase_price?.toString() || '');
  const [quantity, setQuantity] = useState(asset?.quantity?.toString() || '');
  const [decimalPlaces, setDecimalPlaces] = useState(asset?.decimal_places?.toString() || '2');
  const [notes, setNotes] = useState(asset?.notes || '');
  const [isLoading, setIsLoading] = useState(false);

  // Varlığı kaydet
  const saveAsset = async () => {
    try {
      // Validasyon
      if (!name.trim()) {
        Alert.alert('Hata', 'Lütfen bir varlık adı girin.');
        return;
      }
      
      if (!symbol.trim()) {
        Alert.alert('Hata', 'Lütfen bir sembol girin.');
        return;
      }
      
      if (!currentPrice || isNaN(parseFloat(currentPrice)) || parseFloat(currentPrice) <= 0) {
        Alert.alert('Hata', 'Lütfen geçerli bir güncel fiyat girin.');
        return;
      }
      
      if (!purchasePrice || isNaN(parseFloat(purchasePrice)) || parseFloat(purchasePrice) <= 0) {
        Alert.alert('Hata', 'Lütfen geçerli bir alış fiyatı girin.');
        return;
      }
      
      if (!quantity || isNaN(parseFloat(quantity)) || parseFloat(quantity) <= 0) {
        Alert.alert('Hata', 'Lütfen geçerli bir miktar girin.');
        return;
      }
      
      if (!decimalPlaces || isNaN(parseInt(decimalPlaces)) || parseInt(decimalPlaces) < 0) {
        Alert.alert('Hata', 'Lütfen geçerli bir ondalık basamak sayısı girin.');
        return;
      }
      
      setIsLoading(true);
      
      const parsedCurrentPrice = parseFloat(currentPrice);
      const parsedPurchasePrice = parseFloat(purchasePrice);
      const parsedQuantity = parseFloat(quantity);
      const parsedDecimalPlaces = parseInt(decimalPlaces);
      
      if (asset?.id) {
        // Mevcut varlığı güncelle
        await db.runAsync(`
          UPDATE investment_assets 
          SET name = ?, symbol = ?, type = ?, current_price = ?, purchase_price = ?, 
              quantity = ?, decimal_places = ?, notes = ?
          WHERE id = ?
        `, [
          name, 
          symbol, 
          type, 
          parsedCurrentPrice, 
          parsedPurchasePrice, 
          parsedQuantity, 
          parsedDecimalPlaces, 
          notes,
          asset.id
        ]);
      } else {
        // Yeni varlık ekle
        await db.runAsync(`
          INSERT INTO investment_assets 
          (name, symbol, type, current_price, purchase_price, quantity, decimal_places, notes)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          name, 
          symbol, 
          type, 
          parsedCurrentPrice, 
          parsedPurchasePrice, 
          parsedQuantity, 
          parsedDecimalPlaces, 
          notes
        ]);
      }
      
      navigation.goBack();
    } catch (error) {
      console.error('Varlık kaydetme hatası:', error);
      Alert.alert('Hata', 'Varlık kaydedilirken bir hata oluştu.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <MaterialIcons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.title}>
          {asset?.id ? 'Varlık Düzenle' : 'Yeni Varlık Ekle'}
        </Text>
        <View style={{ width: 24 }} />
      </View>

      <KeyboardAvoidingView
        style={styles.container}
        behavior={'padding'}
        keyboardVerticalOffset={64}
      >
        <ScrollView style={styles.content}>
          <View style={styles.formGroup}>
            <Text style={styles.label}>Varlık Adı</Text>
            <TextInput
              style={styles.input}
              value={name}
              onChangeText={setName}
              placeholder="Örn: Akbank"
            />
          </View>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Sembol</Text>
            <TextInput
              style={styles.input}
              value={symbol}
              onChangeText={setSymbol}
              placeholder="Örn: AKBNK"
              autoCapitalize="characters"
            />
          </View>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Varlık Türü</Text>
            <View style={styles.typeSelector}>
              <TouchableOpacity
                style={[
                  styles.typeButton,
                  type === 'stock' && styles.activeTypeButton
                ]}
                onPress={() => setType('stock')}
              >
                <Text
                  style={[
                    styles.typeButtonText,
                    type === 'stock' && styles.activeTypeButtonText
                  ]}
                >
                  Hisse Senedi
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.typeButton,
                  type === 'crypto' && styles.activeTypeButton
                ]}
                onPress={() => setType('crypto')}
              >
                <Text
                  style={[
                    styles.typeButtonText,
                    type === 'crypto' && styles.activeTypeButtonText
                  ]}
                >
                  Kripto Para
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.typeButton,
                  type === 'forex' && styles.activeTypeButton
                ]}
                onPress={() => setType('forex')}
              >
                <Text
                  style={[
                    styles.typeButtonText,
                    type === 'forex' && styles.activeTypeButtonText
                  ]}
                >
                  Döviz
                </Text>
              </TouchableOpacity>
            </View>
            
            <View style={styles.typeSelector}>
              <TouchableOpacity
                style={[
                  styles.typeButton,
                  type === 'commodity' && styles.activeTypeButton
                ]}
                onPress={() => setType('commodity')}
              >
                <Text
                  style={[
                    styles.typeButtonText,
                    type === 'commodity' && styles.activeTypeButtonText
                  ]}
                >
                  Emtia
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.typeButton,
                  type === 'fund' && styles.activeTypeButton
                ]}
                onPress={() => setType('fund')}
              >
                <Text
                  style={[
                    styles.typeButtonText,
                    type === 'fund' && styles.activeTypeButtonText
                  ]}
                >
                  Fon
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.typeButton,
                  type === 'other' && styles.activeTypeButton
                ]}
                onPress={() => setType('other')}
              >
                <Text
                  style={[
                    styles.typeButtonText,
                    type === 'other' && styles.activeTypeButtonText
                  ]}
                >
                  Diğer
                </Text>
              </TouchableOpacity>
            </View>
          </View>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Güncel Fiyat</Text>
            <View style={styles.inputWithIcon}>
              <Text style={styles.inputIcon}>₺</Text>
              <TextInput
                style={styles.inputWithIconField}
                value={currentPrice}
                onChangeText={setCurrentPrice}
                keyboardType="numeric"
                placeholder="0.00"
              />
            </View>
          </View>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Alış Fiyatı</Text>
            <View style={styles.inputWithIcon}>
              <Text style={styles.inputIcon}>₺</Text>
              <TextInput
                style={styles.inputWithIconField}
                value={purchasePrice}
                onChangeText={setPurchasePrice}
                keyboardType="numeric"
                placeholder="0.00"
              />
            </View>
          </View>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Miktar</Text>
            <TextInput
              style={styles.input}
              value={quantity}
              onChangeText={setQuantity}
              keyboardType="numeric"
              placeholder="0.00"
            />
          </View>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Ondalık Basamak Sayısı</Text>
            <TextInput
              style={styles.input}
              value={decimalPlaces}
              onChangeText={setDecimalPlaces}
              keyboardType="numeric"
              placeholder="2"
            />
          </View>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Notlar</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={notes}
              onChangeText={setNotes}
              placeholder="Varlık hakkında notlar..."
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>
        </ScrollView>
        
        <View style={styles.footer}>
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.cancelButtonText}>İptal</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.saveButton}
            onPress={saveAsset}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Text style={styles.saveButtonText}>Kaydet</Text>
            )}
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.PRIMARY,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  textArea: {
    minHeight: 100,
  },
  inputWithIcon: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  inputIcon: {
    paddingHorizontal: 12,
    fontSize: 16,
    color: '#666',
  },
  inputWithIconField: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
  },
  typeSelector: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  typeButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    marginRight: 8,
    backgroundColor: '#fff',
  },
  activeTypeButton: {
    backgroundColor: Colors.PRIMARY,
    borderColor: Colors.PRIMARY,
  },
  typeButtonText: {
    fontSize: 14,
    color: '#333',
  },
  activeTypeButtonText: {
    color: '#fff',
  },
  footer: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
    backgroundColor: '#fff',
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    marginRight: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#333',
  },
  saveButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
    borderRadius: 8,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
  },
});

export default InvestmentAssetFormScreen;
