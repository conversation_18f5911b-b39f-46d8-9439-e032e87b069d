/**
 * Ta<PERSON><PERSON><PERSON> He<PERSON>playıcısı Komponenti
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md - Stage 4, Phase 1
 * 
 * Tasarruf potansiyeli hesaplama ve öneriler
 * Maksimum 200 satır - Tek sorumluluk prensibi
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../../../context/ThemeContext';
import { formatTRY, formatUSD, formatEUR } from '../../../utils/formatters';

/**
 * Tasarruf hesaplayıcısı komponenti
 * @param {Object} props - Component props
 * @param {number} props.totalIncome - Toplam gelir
 * @param {number} props.totalExpenses - Toplam gider
 * @param {number} props.currentSavings - Mevcut tasarruf
 * @param {string} props.currency - Para birimi ('TRY', 'USD', 'EUR')
 * @param {Array} props.savingsGoals - Tasarruf hedefleri [{name, target, timeframe}]
 * @param {Function} props.onGoalPress - Hedef tıklama callback
 * @param {Object} props.theme - Tema objesi (opsiyonel, context'ten alınır)
 */
const SavingsCalculator = ({ 
  totalIncome = 0, 
  totalExpenses = 0, 
  currentSavings = 0,
  currency = 'TRY',
  savingsGoals = [],
  onGoalPress,
  theme: propTheme 
}) => {
  const { theme, isDarkMode } = useTheme();
  const currentTheme = propTheme || theme;

  // State yönetimi
  const [selectedTimeframe, setSelectedTimeframe] = useState('monthly');

  /**
   * Para birimi formatı
   * @param {number} value - Formatlanacak değer
   * @returns {string} Formatlanmış değer
   */
  const formatCurrency = (value) => {
    switch (currency) {
      case 'USD':
        return formatUSD(value);
      case 'EUR':
        return formatEUR(value);
      default:
        return formatTRY(value);
    }
  };

  /**
   * Para birimi sembolü
   * @returns {string} Para birimi sembolü
   */
  const getCurrencySymbol = () => {
    switch (currency) {
      case 'USD':
        return '$';
      case 'EUR':
        return '€';
      default:
        return '₺';
    }
  };

  /**
   * Tasarruf hesaplamaları
   * @returns {Object} Hesaplama sonuçları
   */
  const calculateSavings = () => {
    const netSavings = totalIncome - totalExpenses;
    const savingsRate = totalIncome > 0 ? (netSavings / totalIncome) * 100 : 0;
    const potentialSavings = Math.max(netSavings, 0);
    
    // Önerilen tasarruf oranı %20
    const recommendedSavings = totalIncome * 0.2;
    const savingsGap = recommendedSavings - potentialSavings;

    return {
      netSavings,
      savingsRate,
      potentialSavings,
      recommendedSavings,
      savingsGap: Math.max(savingsGap, 0),
      isOnTrack: savingsRate >= 20
    };
  };

  /**
   * Tasarruf durumu rengi
   * @param {number} rate - Tasarruf oranı
   * @returns {string} Renk kodu
   */
  const getSavingsColor = (rate) => {
    if (rate >= 20) return currentTheme.SUCCESS;
    if (rate >= 10) return currentTheme.WARNING;
    if (rate >= 0) return currentTheme.INFO;
    return currentTheme.ERROR;
  };

  /**
   * Tasarruf durumu ikonu
   * @param {number} rate - Tasarruf oranı
   * @returns {string} İkon adı
   */
  const getSavingsIcon = (rate) => {
    if (rate >= 20) return 'savings';
    if (rate >= 10) return 'account-balance';
    if (rate >= 0) return 'trending-up';
    return 'trending-down';
  };

  /**
   * Tasarruf durumu mesajı
   * @param {number} rate - Tasarruf oranı
   * @returns {string} Durum mesajı
   */
  const getSavingsMessage = (rate) => {
    if (rate >= 20) return 'Mükemmel tasarruf oranı!';
    if (rate >= 10) return 'İyi tasarruf yapıyorsunuz';
    if (rate >= 0) return 'Tasarruf artırılabilir';
    return 'Giderlerinizi azaltın';
  };

  /**
   * Hedef tamamlanma süresi hesaplama
   * @param {Object} goal - Tasarruf hedefi
   * @returns {string} Tamamlanma süresi
   */
  const calculateGoalCompletion = (goal) => {
    const calculations = calculateSavings();
    if (calculations.potentialSavings <= 0) return 'Belirsiz';

    const monthsNeeded = Math.ceil((goal.target - currentSavings) / calculations.potentialSavings);
    
    if (monthsNeeded <= 0) return 'Tamamlandı';
    if (monthsNeeded <= 12) return `${monthsNeeded} ay`;
    
    const years = Math.floor(monthsNeeded / 12);
    const months = monthsNeeded % 12;
    return months > 0 ? `${years} yıl ${months} ay` : `${years} yıl`;
  };

  const calculations = calculateSavings();
  const savingsColor = getSavingsColor(calculations.savingsRate);
  const savingsIcon = getSavingsIcon(calculations.savingsRate);
  const savingsMessage = getSavingsMessage(calculations.savingsRate);
  const currencySymbol = getCurrencySymbol();

  return (
    <View style={[styles.container, { backgroundColor: currentTheme.SURFACE }]}>
      {/* Header */}
      <View style={styles.header}>
        <MaterialIcons name="savings" size={20} color={currentTheme.PRIMARY} />
        <Text style={[styles.title, { color: currentTheme.TEXT_PRIMARY }]}>
          Tasarruf Hesaplayıcısı
        </Text>
      </View>

      {/* Ana tasarruf göstergesi */}
      <View style={[styles.mainSavings, { backgroundColor: savingsColor + '20' }]}>
        <View style={styles.savingsHeader}>
          <MaterialIcons name={savingsIcon} size={32} color={savingsColor} />
          <View style={styles.savingsInfo}>
            <Text style={[styles.savingsRate, { color: savingsColor }]}>
              %{calculations.savingsRate.toFixed(1)}
            </Text>
            <Text style={[styles.savingsLabel, { color: currentTheme.TEXT_SECONDARY }]}>
              Tasarruf Oranı
            </Text>
          </View>
        </View>

        <Text style={[styles.savingsMessage, { color: currentTheme.TEXT_SECONDARY }]}>
          {savingsMessage}
        </Text>

        <View style={styles.savingsAmount}>
          <Text style={[styles.amountLabel, { color: currentTheme.TEXT_SECONDARY }]}>
            Aylık Tasarruf Potansiyeli
          </Text>
          <Text style={[styles.amountValue, { color: savingsColor }]}>
            {currencySymbol}{calculations.potentialSavings.toLocaleString('tr-TR')}
          </Text>
        </View>
      </View>

      {/* Tasarruf detayları */}
      <View style={styles.detailsSection}>
        <Text style={[styles.detailsTitle, { color: currentTheme.TEXT_PRIMARY }]}>
          Tasarruf Detayları
        </Text>
        
        <View style={styles.detailsGrid}>
          <View style={styles.detailItem}>
            <Text style={[styles.detailLabel, { color: currentTheme.TEXT_SECONDARY }]}>
              Toplam Gelir
            </Text>
            <Text style={[styles.detailValue, { color: currentTheme.TEXT_PRIMARY }]}>
              {formatCurrency(totalIncome)}
            </Text>
          </View>

          <View style={styles.detailItem}>
            <Text style={[styles.detailLabel, { color: currentTheme.TEXT_SECONDARY }]}>
              Toplam Gider
            </Text>
            <Text style={[styles.detailValue, { color: currentTheme.TEXT_PRIMARY }]}>
              {formatCurrency(totalExpenses)}
            </Text>
          </View>

          <View style={styles.detailItem}>
            <Text style={[styles.detailLabel, { color: currentTheme.TEXT_SECONDARY }]}>
              Net Tasarruf
            </Text>
            <Text style={[
              styles.detailValue, 
              { color: calculations.netSavings >= 0 ? currentTheme.SUCCESS : currentTheme.ERROR }
            ]}>
              {formatCurrency(Math.abs(calculations.netSavings))}
              {calculations.netSavings < 0 && ' (açık)'}
            </Text>
          </View>

          <View style={styles.detailItem}>
            <Text style={[styles.detailLabel, { color: currentTheme.TEXT_SECONDARY }]}>
              Mevcut Birikim
            </Text>
            <Text style={[styles.detailValue, { color: currentTheme.PRIMARY }]}>
              {formatCurrency(currentSavings)}
            </Text>
          </View>
        </View>
      </View>

      {/* Öneriler */}
      <View style={styles.recommendationsSection}>
        <Text style={[styles.recommendationsTitle, { color: currentTheme.TEXT_PRIMARY }]}>
          Öneriler
        </Text>
        
        {!calculations.isOnTrack && (
          <View style={[styles.recommendationItem, { backgroundColor: currentTheme.WARNING + '10' }]}>
            <MaterialIcons name="lightbulb" size={16} color={currentTheme.WARNING} />
            <Text style={[styles.recommendationText, { color: currentTheme.TEXT_SECONDARY }]}>
              Önerilen %20 tasarruf oranına ulaşmak için aylık {currencySymbol}{calculations.savingsGap.toLocaleString('tr-TR')} daha tasarruf yapın.
            </Text>
          </View>
        )}

        {calculations.isOnTrack && (
          <View style={[styles.recommendationItem, { backgroundColor: currentTheme.SUCCESS + '10' }]}>
            <MaterialIcons name="check-circle" size={16} color={currentTheme.SUCCESS} />
            <Text style={[styles.recommendationText, { color: currentTheme.TEXT_SECONDARY }]}>
              Harika! Önerilen tasarruf oranına ulaştınız. Bu tempoyu koruyun.
            </Text>
          </View>
        )}
      </View>

      {/* Tasarruf hedefleri */}
      {savingsGoals.length > 0 && (
        <View style={styles.goalsSection}>
          <Text style={[styles.goalsTitle, { color: currentTheme.TEXT_PRIMARY }]}>
            Tasarruf Hedefleri
          </Text>
          
          {savingsGoals.slice(0, 3).map((goal, index) => {
            const completionTime = calculateGoalCompletion(goal);
            const progress = currentSavings / goal.target;
            
            return (
              <TouchableOpacity
                key={index}
                style={[styles.goalItem, { backgroundColor: currentTheme.BACKGROUND }]}
                onPress={() => onGoalPress && onGoalPress(goal)}
                activeOpacity={0.7}
              >
                <View style={styles.goalHeader}>
                  <Text style={[styles.goalName, { color: currentTheme.TEXT_PRIMARY }]}>
                    {goal.name}
                  </Text>
                  <Text style={[styles.goalTarget, { color: currentTheme.PRIMARY }]}>
                    {formatCurrency(goal.target)}
                  </Text>
                </View>
                
                <View style={styles.goalProgress}>
                  <View style={[styles.progressTrack, { backgroundColor: currentTheme.BORDER }]}>
                    <View 
                      style={[
                        styles.progressFill,
                        {
                          backgroundColor: currentTheme.PRIMARY,
                          width: `${Math.min(progress * 100, 100)}%`
                        }
                      ]} 
                    />
                  </View>
                  <Text style={[styles.progressText, { color: currentTheme.TEXT_SECONDARY }]}>
                    %{(progress * 100).toFixed(1)}
                  </Text>
                </View>
                
                <Text style={[styles.completionTime, { color: currentTheme.TEXT_SECONDARY }]}>
                  Tahmini süre: {completionTime}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '500',
  },
  mainSavings: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    alignItems: 'center',
  },
  savingsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 12,
  },
  savingsInfo: {
    alignItems: 'center',
  },
  savingsRate: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  savingsLabel: {
    fontSize: 12,
  },
  savingsMessage: {
    fontSize: 14,
    marginBottom: 12,
    textAlign: 'center',
  },
  savingsAmount: {
    alignItems: 'center',
  },
  amountLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  amountValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  detailsSection: {
    marginBottom: 16,
  },
  detailsTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 12,
  },
  detailsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  detailItem: {
    width: '48%',
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  recommendationsSection: {
    marginBottom: 16,
  },
  recommendationsTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 12,
  },
  recommendationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  recommendationText: {
    fontSize: 13,
    flex: 1,
    lineHeight: 18,
  },
  goalsSection: {
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
    paddingTop: 16,
  },
  goalsTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 12,
  },
  goalItem: {
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  goalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  goalName: {
    fontSize: 14,
    fontWeight: '500',
  },
  goalTarget: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  goalProgress: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    gap: 8,
  },
  progressTrack: {
    flex: 1,
    height: 6,
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 12,
    minWidth: 40,
    textAlign: 'right',
  },
  completionTime: {
    fontSize: 12,
    textAlign: 'center',
  },
});

export default SavingsCalculator;
