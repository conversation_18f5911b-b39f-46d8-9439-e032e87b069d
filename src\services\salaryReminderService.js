/**
 * <PERSON><PERSON><PERSON> hatırlatıcıları için servis fonksiyonları
 */
import { format, parseISO, addDays, addMonths, isLastDayOfMonth, lastDayOfMonth, isValid } from 'date-fns';
import * as reminderService from './reminderService';
import * as notificationDbService from './notificationDbService';
import * as exchangeRateService from './exchangeRateService';

/**
 * Güvenli tarih formatting fonksiyonu - geçersiz tarihleri ele alır
 * @param {Date|string} date - Format edilecek tarih
 * @param {string} formatStr - Format string'i
 * @returns {string} Formatlanmış tarih veya hata mesajı
 */
const safeFormatDate = (date, formatStr = 'dd.MM.yyyy') => {
  try {
    const dateObj = date instanceof Date ? date : new Date(date);
    if (!isValid(dateObj)) {
      return 'Geçersiz tarih';
    }
    return format(dateObj, formatStr);
  } catch (error) {
    console.warn('Date formatting error:', error);
    return 'Geçersiz tarih';
  }
};

/**
 * Maaş hatırlatıcısı ekler
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} salaryReminder - Maaş hatırlatıcı verileri
 * @param {string} salaryReminder.title - Başlık
 * @param {string} salaryReminder.description - Açıklama
 * @param {number} salaryReminder.amount - Miktar
 * @param {string} salaryReminder.currency - Para birimi
 * @param {number} salaryReminder.payment_day - Ödeme günü (1-31, 0=ayın son günü)
 * @param {number} salaryReminder.remind_days_before - Kaç gün önce hatırlatılacak
 * @param {string} salaryReminder.salary_type - Maaş türü (regular, bonus, overtime)
 * @param {number} salaryReminder.category_id - Kategori ID
 * @param {Array<number>} tagIds - Etiket ID'leri
 * @returns {Promise<number>} Eklenen hatırlatıcı ID'si
 */
export const addSalaryReminder = async (db, salaryReminder, tagIds = []) => {
  try {
    // Ödeme gününü kontrol et
    const paymentDay = salaryReminder.payment_day || 1;
    
    // Bir sonraki ödeme tarihini hesapla
    const now = new Date();
    let nextPaymentDate = new Date(now.getFullYear(), now.getMonth(), paymentDay);
    
    // Eğer ödeme günü 0 ise, ayın son günü olarak ayarla
    if (paymentDay === 0) {
      nextPaymentDate = lastDayOfMonth(nextPaymentDate);
    }
    
    // Eğer bugün ödeme gününden sonra ise, bir sonraki aya geç
    if (now.getDate() > paymentDay || (paymentDay === 0 && isLastDayOfMonth(now))) {
      nextPaymentDate = addMonths(nextPaymentDate, 1);
    }
    
    // Hatırlatma tarihini hesapla (ödeme tarihinden X gün önce)
    const remindDaysBefore = salaryReminder.remind_days_before || 1;
    const reminderDate = addDays(nextPaymentDate, -remindDaysBefore);
    
    // Hatırlatıcı başlığını oluştur
    let title = salaryReminder.title;
    if (!title) {
      switch (salaryReminder.salary_type) {
        case 'bonus':
          title = 'Bonus Ödemesi';
          break;
        case 'overtime':
          title = 'Mesai Ödemesi';
          break;
        default:
          title = 'Maaş Ödemesi';
      }
    }
    
    // Hatırlatıcı mesajını oluştur
    let message = salaryReminder.description;
    if (!message) {
      const formattedAmount = new Intl.NumberFormat('tr-TR', {
        style: 'currency',
        currency: salaryReminder.currency || 'TRY'
      }).format(salaryReminder.amount);
      
      message = `${formattedAmount} tutarındaki maaş ödemeniz ${safeFormatDate(nextPaymentDate, 'dd.MM.yyyy')} tarihinde yapılacak.`;
    }
    
    // Hatırlatıcı verilerini hazırla
    const reminderData = {
      title,
      message,
      date: reminderDate,
      time: '09:00', // Varsayılan saat
      priority: salaryReminder.priority || 'normal',
      category_id: salaryReminder.category_id,
      repeat_type: 'monthly',
      repeat_interval: 1,
      data: {
        isSalaryReminder: true,
        salaryType: salaryReminder.salary_type || 'regular',
        amount: salaryReminder.amount,
        currency: salaryReminder.currency || 'TRY',
        paymentDay: paymentDay,
        remindDaysBefore,
        nextPaymentDate: nextPaymentDate.toISOString()
      }
    };
    
    // Hatırlatıcıyı ekle
    return await reminderService.addReminder(db, reminderData, tagIds);
  } catch (error) {
    console.error('Maaş hatırlatıcısı ekleme hatası:', error);
    throw error;
  }
};

/**
 * Maaş hatırlatıcısını günceller
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} reminderId - Hatırlatıcı ID
 * @param {Object} salaryReminder - Maaş hatırlatıcı verileri
 * @param {Array<number>} tagIds - Etiket ID'leri
 * @returns {Promise<boolean>} Başarılı ise true
 */
export const updateSalaryReminder = async (db, reminderId, salaryReminder, tagIds = null) => {
  try {
    // Mevcut hatırlatıcıyı getir
    const existingReminder = await reminderService.getReminderById(db, reminderId);
    if (!existingReminder) {
      throw new Error('Hatırlatıcı bulunamadı');
    }
    
    // Mevcut verileri parse et
    const existingData = existingReminder.data ? 
      (typeof existingReminder.data === 'string' ? JSON.parse(existingReminder.data) : existingReminder.data) : 
      {};
    
    // Ödeme gününü kontrol et
    const paymentDay = salaryReminder.payment_day !== undefined ? 
      salaryReminder.payment_day : 
      existingData.paymentDay || 1;
    
    // Bir sonraki ödeme tarihini hesapla
    const now = new Date();
    let nextPaymentDate = new Date(now.getFullYear(), now.getMonth(), paymentDay);
    
    // Eğer ödeme günü 0 ise, ayın son günü olarak ayarla
    if (paymentDay === 0) {
      nextPaymentDate = lastDayOfMonth(nextPaymentDate);
    }
    
    // Eğer bugün ödeme gününden sonra ise, bir sonraki aya geç
    if (now.getDate() > paymentDay || (paymentDay === 0 && isLastDayOfMonth(now))) {
      nextPaymentDate = addMonths(nextPaymentDate, 1);
    }
    
    // Hatırlatma tarihini hesapla (ödeme tarihinden X gün önce)
    const remindDaysBefore = salaryReminder.remind_days_before !== undefined ? 
      salaryReminder.remind_days_before : 
      existingData.remindDaysBefore || 1;
    const reminderDate = addDays(nextPaymentDate, -remindDaysBefore);
    
    // Hatırlatıcı başlığını oluştur
    let title = salaryReminder.title || existingReminder.title;
    
    // Hatırlatıcı mesajını oluştur
    let message = salaryReminder.description || existingReminder.message;
    if (salaryReminder.amount && salaryReminder.amount !== existingData.amount) {
      const formattedAmount = new Intl.NumberFormat('tr-TR', {
        style: 'currency',
        currency: salaryReminder.currency || existingData.currency || 'TRY'
      }).format(salaryReminder.amount);
      
      message = `${formattedAmount} tutarındaki maaş ödemeniz ${safeFormatDate(nextPaymentDate, 'dd.MM.yyyy')} tarihinde yapılacak.`;
    }
    
    // Hatırlatıcı verilerini hazırla
    const reminderData = {
      title,
      message,
      date: reminderDate,
      time: existingReminder.time || '09:00',
      priority: salaryReminder.priority || existingReminder.priority,
      category_id: salaryReminder.category_id || existingReminder.category_id,
      repeat_type: 'monthly',
      repeat_interval: 1,
      data: {
        ...existingData,
        isSalaryReminder: true,
        salaryType: salaryReminder.salary_type || existingData.salaryType || 'regular',
        amount: salaryReminder.amount || existingData.amount,
        currency: salaryReminder.currency || existingData.currency || 'TRY',
        paymentDay: paymentDay,
        remindDaysBefore,
        nextPaymentDate: nextPaymentDate.toISOString()
      }
    };
    
    // Hatırlatıcıyı güncelle
    return await reminderService.updateReminder(db, reminderId, reminderData, tagIds);
  } catch (error) {
    console.error('Maaş hatırlatıcısı güncelleme hatası:', error);
    throw error;
  }
};

/**
 * Maaş hatırlatıcılarını getirir
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {Object} options - Sorgu seçenekleri
 * @returns {Promise<Array>} Maaş hatırlatıcıları listesi
 */
export const getSalaryReminders = async (db, options = {}) => {
  try {
    // Doğrudan veritabanından maaş hatırlatıcılarını getir
    const query = `
      SELECT * FROM notifications
      WHERE related_type = 'user_reminder'
      AND data LIKE '%isSalaryReminder":true%'
      ORDER BY scheduled_at ASC
    `;
    
    const reminders = await db.getAllAsync(query);
    
    // Sonuçları işle
    return reminders.map(reminder => {
      // Tarih ve saat bilgilerini ayır
      const scheduledDate = parseISO(reminder.scheduled_at);
      const date = safeFormatDate(scheduledDate, 'yyyy-MM-dd');
      const time = safeFormatDate(scheduledDate, 'HH:mm');

      // Tekrarlama bilgilerini parse et
      let repeatDays = null;
      let repeatMonths = null;
      let data = {};
      
      try {
        if (reminder.repeat_days) repeatDays = JSON.parse(reminder.repeat_days);
        if (reminder.repeat_months) repeatMonths = JSON.parse(reminder.repeat_months);
        if (reminder.data) {
          if (typeof reminder.data === 'string') {
            data = JSON.parse(reminder.data);
          } else {
            data = reminder.data;
          }
        }
      } catch (parseError) {
        console.error('JSON parse hatası:', parseError);
      }

      return {
        ...reminder,
        date,
        time,
        repeat_days: repeatDays,
        repeat_months: repeatMonths,
        data,
        salary_type: data.salaryType,
        amount: data.amount,
        currency: data.currency,
        payment_day: data.paymentDay,
        remind_days_before: data.remindDaysBefore
      };
    });
  } catch (error) {
    console.error('Maaş hatırlatıcıları getirme hatası:', error);
    return []; // Hata durumunda boş dizi döndür
  }
};

/**
 * Belirli bir maaş hatırlatıcısını getirir
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} reminderId - Hatırlatıcı ID
 * @returns {Promise<Object>} Maaş hatırlatıcısı
 */
export const getSalaryReminderById = async (db, reminderId) => {
  try {
    // Doğrudan veritabanından maaş hatırlatıcısını getir
    const query = `
      SELECT * FROM notifications
      WHERE id = ?
      AND related_type = 'user_reminder'
    `;
    
    const reminder = await db.getFirstAsync(query, [reminderId]);
    if (!reminder) return null;
    
    // Tarih ve saat bilgilerini ayır
    const scheduledDate = parseISO(reminder.scheduled_at);
    const date = safeFormatDate(scheduledDate, 'yyyy-MM-dd');
    const time = safeFormatDate(scheduledDate, 'HH:mm');

    // Tekrarlama bilgilerini parse et
    let repeatDays = null;
    let repeatMonths = null;
    let data = {};
    
    try {
      if (reminder.repeat_days) repeatDays = JSON.parse(reminder.repeat_days);
      if (reminder.repeat_months) repeatMonths = JSON.parse(reminder.repeat_months);
      if (reminder.data) {
        if (typeof reminder.data === 'string') {
          data = JSON.parse(reminder.data);
        } else {
          data = reminder.data;
        }
      }
    } catch (parseError) {
      console.error('JSON parse hatası:', parseError);
      return null;
    }
    
    // Maaş hatırlatıcısı değilse null döndür
    if (!data.isSalaryReminder) return null;
    
    return {
      ...reminder,
      date,
      time,
      repeat_days: repeatDays,
      repeat_months: repeatMonths,
      data,
      salary_type: data.salaryType,
      amount: data.amount,
      currency: data.currency,
      payment_day: data.paymentDay,
      remind_days_before: data.remindDaysBefore
    };
  } catch (error) {
    console.error('Maaş hatırlatıcısı getirme hatası:', error);
    return null; // Hata durumunda null döndür
  }
};

/**
 * Yaklaşan maaş hatırlatıcılarını getirir
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {number} days - Kaç gün içindeki hatırlatıcılar
 * @param {number} limit - Limit
 * @returns {Promise<Array>} Yaklaşan maaş hatırlatıcıları listesi
 */
export const getUpcomingSalaryReminders = async (db, days = 7, limit = 10) => {
  try {
    const now = new Date();
    const endDate = addDays(now, days);
    
    // Doğrudan veritabanından yaklaşan maaş hatırlatıcılarını getir
    const query = `
      SELECT * FROM notifications
      WHERE related_type = 'user_reminder'
      AND status = 'pending'
      AND scheduled_at BETWEEN ? AND ?
      AND is_enabled = 1
      AND data LIKE '%isSalaryReminder":true%'
      ORDER BY scheduled_at ASC
      LIMIT ?
    `;
    
    const reminders = await db.getAllAsync(query, [now.toISOString(), endDate.toISOString(), limit]);
    
    // Sonuçları işle
    return reminders.map(reminder => {
      // Tarih ve saat bilgilerini ayır
      const scheduledDate = parseISO(reminder.scheduled_at);
      const date = safeFormatDate(scheduledDate, 'yyyy-MM-dd');
      const time = safeFormatDate(scheduledDate, 'HH:mm');

      // Tekrarlama bilgilerini parse et
      let repeatDays = null;
      let repeatMonths = null;
      let data = {};
      
      try {
        if (reminder.repeat_days) repeatDays = JSON.parse(reminder.repeat_days);
        if (reminder.repeat_months) repeatMonths = JSON.parse(reminder.repeat_months);
        if (reminder.data) {
          if (typeof reminder.data === 'string') {
            data = JSON.parse(reminder.data);
          } else {
            data = reminder.data;
          }
        }
      } catch (parseError) {
        console.error('JSON parse hatası:', parseError);
      }

      return {
        ...reminder,
        date,
        time,
        repeat_days: repeatDays,
        repeat_months: repeatMonths,
        data,
        salary_type: data.salaryType,
        amount: data.amount,
        currency: data.currency,
        payment_day: data.paymentDay,
        remind_days_before: data.remindDaysBefore
      };
    });
  } catch (error) {
    console.error('Yaklaşan maaş hatırlatıcıları getirme hatası:', error);
    return []; // Hata durumunda boş dizi döndür
  }
};

/**
 * Maaş türlerini getirir
 * 
 * @returns {Array} Maaş türleri
 */
export const getSalaryTypes = () => {
  return [
    { id: 'regular', name: 'Normal Maaş', icon: 'account-balance-wallet', color: '#2ecc71' },
    { id: 'bonus', name: 'Bonus', icon: 'card-giftcard', color: '#3498db' },
    { id: 'overtime', name: 'Mesai', icon: 'schedule', color: '#e74c3c' },
    { id: 'other', name: 'Diğer', icon: 'attach-money', color: '#f39c12' }
  ];
};

/**
 * Ödeme günü seçeneklerini getirir
 * 
 * @returns {Array} Ödeme günü seçenekleri
 */
export const getPaymentDayOptions = () => {
  const options = [];
  
  // Ayın son günü
  options.push({ value: 0, label: 'Ayın son günü' });
  
  // 1-31 arası günler
  for (let i = 1; i <= 31; i++) {
    options.push({ value: i, label: `Ayın ${i}. günü` });
  }
  
  return options;
};

/**
 * Hatırlatma gün seçeneklerini getirir
 * 
 * @returns {Array} Hatırlatma gün seçenekleri
 */
export const getRemindDaysOptions = () => {
  return [
    { value: 0, label: 'Aynı gün' },
    { value: 1, label: '1 gün önce' },
    { value: 2, label: '2 gün önce' },
    { value: 3, label: '3 gün önce' },
    { value: 5, label: '5 gün önce' },
    { value: 7, label: '1 hafta önce' }
  ];
};
