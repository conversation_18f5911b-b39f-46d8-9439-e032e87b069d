/**
 * Gelişmiş Bütçe Yönetimi Servisi
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md Stage 1 implementasyonu
 * 
 * Bu servis gelişmiş bütçe yönetimi için temel CRUD operasyonlarını sağlar
 */

/**
 * Yeni bütçe oluşturur
 * @param {Object} db - SQLite database instance
 * @param {Object} budgetData - Bütçe verileri
 * @param {Array} categories - Kategori limitleri
 * @returns {Promise<number>} Oluşturulan bütçe ID'si
 */
export const createBudget = async (db, budgetData, categories = []) => {
  try {
    return await db.withTransactionAsync(async () => {
      // Ana bütçe kaydını oluştur
      const result = await db.runAsync(`
        INSERT INTO budgets_enhanced 
        (name, description, type, period_type, start_date, end_date, total_limit, currency, status)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        budgetData.name,
        budgetData.description || null,
        budgetData.type || 'category_based',
        budgetData.period_type || 'monthly',
        budgetData.start_date,
        budgetData.end_date || null,
        budgetData.total_limit || 0,
        budgetData.currency || 'TRY',
        budgetData.status || 'active'
      ]);

      const budgetId = result.lastInsertRowId;

      // Kategori limitlerini ekle
      if (categories && categories.length > 0) {
        for (const category of categories) {
          await db.runAsync(`
            INSERT INTO budget_categories_enhanced 
            (budget_id, category_id, limit_amount, currency)
            VALUES (?, ?, ?, ?)
          `, [
            budgetId,
            category.category_id,
            category.limit_amount,
            budgetData.currency || 'TRY'
          ]);
        }
      }

      // Varsayılan uyarı ayarlarını oluştur
      await db.runAsync(`
        INSERT INTO budget_alert_settings 
        (budget_id, threshold_75, threshold_90, threshold_100, daily_limit_exceeded, category_limit_exceeded)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [budgetId, 1, 1, 1, 1, 1]);

      console.log(`✅ Yeni bütçe oluşturuldu: ${budgetData.name} (ID: ${budgetId})`);
      return budgetId;
    });
  } catch (error) {
    console.error('❌ Bütçe oluşturma hatası:', error);
    throw error;
  }
};

/**
 * Bütçe listesini getirir
 * @param {Object} db - SQLite database instance
 * @param {Object} options - Filtreleme seçenekleri
 * @returns {Promise<Array>} Bütçe listesi
 */
export const getBudgets = async (db, options = {}) => {
  try {
    let query = `
      SELECT 
        b.*,
        COUNT(bc.id) as category_count,
        SUM(bc.limit_amount) as total_allocated,
        SUM(bc.spent_amount) as total_spent
      FROM budgets_enhanced b
      LEFT JOIN budget_categories_enhanced bc ON b.id = bc.budget_id
      WHERE 1=1
    `;

    const params = [];

    // Filtreleme seçenekleri
    if (options.status) {
      query += ' AND b.status = ?';
      params.push(options.status);
    }

    if (options.type) {
      query += ' AND b.type = ?';
      params.push(options.type);
    }

    if (options.period_type) {
      query += ' AND b.period_type = ?';
      params.push(options.period_type);
    }

    if (options.active_only) {
      query += ' AND b.status = "active"';
    }

    if (options.current_period) {
      const today = new Date().toISOString().split('T')[0];
      query += ' AND b.start_date <= ? AND (b.end_date IS NULL OR b.end_date >= ?)';
      params.push(today, today);
    }

    query += `
      GROUP BY b.id
      ORDER BY b.created_at DESC
    `;

    const budgets = await db.getAllAsync(query, params);

    // Her bütçe için ilerleme hesapla
    return budgets.map(budget => ({
      ...budget,
      progress: budget.total_allocated > 0 ? 
        Math.min((budget.total_spent / budget.total_allocated) * 100, 100) : 0,
      remaining: budget.total_allocated - budget.total_spent,
      is_over_budget: budget.total_spent > budget.total_allocated
    }));

  } catch (error) {
    console.error('❌ Bütçe listesi getirme hatası:', error);
    throw error;
  }
};

/**
 * Belirli bir bütçenin detaylarını getirir
 * @param {Object} db - SQLite database instance
 * @param {number} budgetId - Bütçe ID'si
 * @returns {Promise<Object>} Bütçe detayları
 */
export const getBudgetDetails = async (db, budgetId) => {
  try {
    // Ana bütçe bilgilerini getir
    const budget = await db.getFirstAsync(`
      SELECT * FROM budgets_enhanced WHERE id = ?
    `, [budgetId]);

    if (!budget) {
      throw new Error('Bütçe bulunamadı');
    }

    // Kategori detaylarını getir
    const categories = await db.getAllAsync(`
      SELECT 
        bc.*,
        c.name as category_name,
        c.icon as category_icon,
        c.color as category_color
      FROM budget_categories_enhanced bc
      JOIN categories c ON bc.category_id = c.id
      WHERE bc.budget_id = ?
      ORDER BY bc.limit_amount DESC
    `, [budgetId]);

    // Her kategori için harcama verilerini hesapla
    const categoriesWithSpending = await Promise.all(
      categories.map(async (category) => {
        const spending = await calculateCategorySpending(db, budgetId, category.category_id);
        return {
          ...category,
          spent_amount: spending,
          remaining: category.limit_amount - spending,
          progress: category.limit_amount > 0 ? 
            Math.min((spending / category.limit_amount) * 100, 100) : 0,
          is_over_budget: spending > category.limit_amount
        };
      })
    );

    // Uyarı ayarlarını getir
    const alertSettings = await db.getFirstAsync(`
      SELECT * FROM budget_alert_settings WHERE budget_id = ?
    `, [budgetId]);

    // Toplam değerleri hesapla
    const totalAllocated = categoriesWithSpending.reduce((sum, cat) => sum + cat.limit_amount, 0);
    const totalSpent = categoriesWithSpending.reduce((sum, cat) => sum + cat.spent_amount, 0);

    return {
      ...budget,
      categories: categoriesWithSpending,
      alert_settings: alertSettings,
      total_allocated: totalAllocated,
      total_spent: totalSpent,
      total_remaining: totalAllocated - totalSpent,
      total_progress: totalAllocated > 0 ? 
        Math.min((totalSpent / totalAllocated) * 100, 100) : 0,
      is_over_budget: totalSpent > totalAllocated
    };

  } catch (error) {
    console.error('❌ Bütçe detayları getirme hatası:', error);
    throw error;
  }
};

/**
 * Belirli kategori için harcama miktarını hesaplar
 * @param {Object} db - SQLite database instance
 * @param {number} budgetId - Bütçe ID'si
 * @param {string} categoryId - Kategori ID'si
 * @returns {Promise<number>} Harcama miktarı
 */
const calculateCategorySpending = async (db, budgetId, categoryId) => {
  try {
    // Bütçe tarih aralığını getir
    const budget = await db.getFirstAsync(`
      SELECT start_date, end_date FROM budgets_enhanced WHERE id = ?
    `, [budgetId]);

    if (!budget) return 0;

    // Harcamaları hesapla
    const result = await db.getFirstAsync(`
      SELECT SUM(amount) as total
      FROM transactions
      WHERE category_id = ? 
        AND type = 'expense'
        AND date >= ?
        AND (? IS NULL OR date <= ?)
    `, [
      categoryId,
      budget.start_date,
      budget.end_date,
      budget.end_date
    ]);

    return result?.total || 0;
  } catch (error) {
    console.error('❌ Kategori harcama hesaplama hatası:', error);
    return 0;
  }
};

/**
 * Bütçeyi günceller
 * @param {Object} db - SQLite database instance
 * @param {number} budgetId - Bütçe ID'si
 * @param {Object} updateData - Güncellenecek veriler
 * @returns {Promise<boolean>} Başarı durumu
 */
export const updateBudget = async (db, budgetId, updateData) => {
  try {
    const result = await db.runAsync(`
      UPDATE budgets_enhanced 
      SET name = ?, description = ?, type = ?, period_type = ?, 
          start_date = ?, end_date = ?, total_limit = ?, currency = ?, 
          status = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [
      updateData.name,
      updateData.description,
      updateData.type,
      updateData.period_type,
      updateData.start_date,
      updateData.end_date,
      updateData.total_limit,
      updateData.currency,
      updateData.status,
      budgetId
    ]);

    console.log(`✅ Bütçe güncellendi: ${updateData.name} (ID: ${budgetId})`);
    return result.changes > 0;
  } catch (error) {
    console.error('❌ Bütçe güncelleme hatası:', error);
    throw error;
  }
};

/**
 * Bütçeyi siler
 * @param {Object} db - SQLite database instance
 * @param {number} budgetId - Bütçe ID'si
 * @returns {Promise<boolean>} Başarı durumu
 */
export const deleteBudget = async (db, budgetId) => {
  try {
    return await db.withTransactionAsync(async () => {
      // İlişkili verileri sil (CASCADE ile otomatik silinir ama manuel kontrol)
      await db.runAsync('DELETE FROM budget_alert_settings WHERE budget_id = ?', [budgetId]);
      await db.runAsync('DELETE FROM budget_categories_enhanced WHERE budget_id = ?', [budgetId]);
      await db.runAsync('DELETE FROM budget_history WHERE budget_id = ?', [budgetId]);
      
      // Ana bütçe kaydını sil
      const result = await db.runAsync('DELETE FROM budgets_enhanced WHERE id = ?', [budgetId]);
      
      console.log(`✅ Bütçe silindi (ID: ${budgetId})`);
      return result.changes > 0;
    });
  } catch (error) {
    console.error('❌ Bütçe silme hatası:', error);
    throw error;
  }
};

/**
 * Tek bir bütçeyi ID ile getirir
 * @param {Object} db - SQLite database instance
 * @param {number} budgetId - Bütçe ID'si
 * @returns {Promise<Object>} Bütçe detayları
 */
export const getBudgetById = async (db, budgetId) => {
  try {
    const budget = await db.getFirstAsync(`
      SELECT * FROM budgets_enhanced WHERE id = ?
    `, [budgetId]);

    if (!budget) {
      throw new Error('Bütçe bulunamadı');
    }

    return budget;
  } catch (error) {
    console.error('❌ Bütçe getirme hatası:', error);
    throw error;
  }
};

/**
 * Bütçe uyarılarını kontrol eder
 * @param {Object} db - SQLite database instance
 * @returns {Promise<Array>} Uyarı listesi
 */
export const checkBudgetAlerts = async (db) => {
  try {
    const alerts = await db.getAllAsync(`
      SELECT 
        b.id,
        b.name,
        b.total_limit,
        (SELECT COALESCE(SUM(t.amount), 0) 
         FROM transactions t 
         WHERE t.type = 'expense' 
         AND t.date >= b.start_date 
         AND (b.end_date IS NULL OR t.date <= b.end_date)) as spent_amount
      FROM budgets_enhanced b
      WHERE b.status = 'active'
    `);

    const alertList = [];
    
    alerts.forEach(budget => {
      const percentage = budget.total_limit > 0 ? (budget.spent_amount / budget.total_limit) * 100 : 0;
      
      if (percentage >= 100) {
        alertList.push({
          id: budget.id,
          type: 'budget_exceeded',
          severity: 'high',
          message: `"${budget.name}" bütçenizi aştınız! (%${percentage.toFixed(1)})`,
          budget_name: budget.name,
          percentage: percentage
        });
      } else if (percentage >= 90) {
        alertList.push({
          id: budget.id,
          type: 'budget_warning',
          severity: 'medium',
          message: `"${budget.name}" bütçenizin %${percentage.toFixed(1)}'i kullanıldı.`,
          budget_name: budget.name,
          percentage: percentage
        });
      }
    });

    return alertList;
  } catch (error) {
    console.error('❌ Bütçe uyarı kontrolü hatası:', error);
    return [];
  }
};

/**
 * Tüm bütçeleri getirir (getAllBudgets alias for getBudgets)
 * @param {Object} db - SQLite database instance
 * @param {Object} options - Query options
 * @returns {Promise<Array>} Bütçe listesi
 */
export const getAllBudgets = async (db, options = {}) => {
  return await getBudgets(db, options);
};

/**
 * Bütçe uyarılarını getirir (getBudgetAlerts alias for checkBudgetAlerts)
 * @param {Object} db - SQLite database instance
 * @returns {Promise<Array>} Uyarı listesi
 */
export const getBudgetAlerts = async (db) => {
  return await checkBudgetAlerts(db);
};

/**
 * Bütçe istatistiklerini hesaplar ve getirir
 * @param {Object} db - SQLite database instance
 * @returns {Promise<Object>} Bütçe istatistikleri
 */
export const getBudgetStatistics = async (db) => {
  try {
    // Aktif bütçeleri al
    const budgets = await getBudgets(db, { status: 'active' });
    
    let totalBudgets = budgets.length;
    let totalLimit = 0;
    let totalSpent = 0;
    let overBudgetCount = 0;
    let totalUsagePercentage = 0;

    budgets.forEach(budget => {
      totalLimit += budget.total_limit || 0;
      totalSpent += budget.total_spent || 0;
      
      const percentage = budget.total_limit > 0 ? (budget.total_spent / budget.total_limit) * 100 : 0;
      totalUsagePercentage += percentage;
      
      if (percentage >= 100) {
        overBudgetCount++;
      }
    });

    const averageUsagePercentage = totalBudgets > 0 ? totalUsagePercentage / totalBudgets : 0;
    const remainingAmount = totalLimit - totalSpent;

    return {
      totalBudgets,
      totalLimit,
      totalSpent,
      remainingAmount,
      averageUsagePercentage,
      overBudgetCount,
    };
  } catch (error) {
    console.error('❌ Bütçe istatistikleri hesaplama hatası:', error);
    return {
      totalBudgets: 0,
      totalLimit: 0,
      totalSpent: 0,
      remainingAmount: 0,
      averageUsagePercentage: 0,
      overBudgetCount: 0,
    };
  }
};
