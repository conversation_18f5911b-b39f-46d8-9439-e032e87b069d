import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
  Switch
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import * as reminderPatternService from '../services/reminderPatternService';

/**
 * Özel Tekrarlama Deseni Form Ekranı
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @param {Object} props.route - Route nesnesi
 * @returns {JSX.Element} Özel Tekrarlama Deseni Form Ekranı
 */
const ReminderPatternFormScreen = ({ navigation, route }) => {
  const db = useSQLiteContext();
  const patternId = route.params?.patternId;
  const isEditing = !!patternId;
  
  // Form durumu
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [patternType, setPatternType] = useState('monthly_day');
  const [patternValue, setPatternValue] = useState({ day: 1 });
  
  // UI durumu
  const [loading, setLoading] = useState(isEditing);
  const [saving, setSaving] = useState(false);
  
  // Verileri yükle
  const loadData = useCallback(async () => {
    if (!isEditing) return;
    
    try {
      setLoading(true);
      
      // Desen detaylarını getir
      const patternDetails = await reminderPatternService.getPatternById(db, patternId);
      
      if (patternDetails) {
        setName(patternDetails.name);
        setDescription(patternDetails.description || '');
        setPatternType(patternDetails.pattern_type);
        setPatternValue(patternDetails.pattern_value || {});
      }
      
      setLoading(false);
    } catch (error) {
      console.error('Desen detayları yükleme hatası:', error);
      Alert.alert('Hata', 'Desen detayları yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  }, [db, patternId, isEditing]);
  
  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [loadData])
  );
  
  // Desen tipini değiştir
  const handlePatternTypeChange = (type) => {
    setPatternType(type);
    
    // Desen tipine göre varsayılan değerleri ayarla
    switch (type) {
      case 'monthly_day':
        setPatternValue({ day: 1 });
        break;
      case 'monthly_last_day':
        setPatternValue({});
        break;
      case 'monthly_last_business_day':
        setPatternValue({});
        break;
      case 'monthly_weekday':
        setPatternValue({ weekday: 1, occurrence: 1 });
        break;
      case 'interval_months':
        setPatternValue({ months: 1 });
        break;
      case 'interval_weeks':
        setPatternValue({ weeks: 1 });
        break;
      case 'weekdays':
        setPatternValue({ days: [1, 2, 3, 4, 5] });
        break;
      default:
        setPatternValue({});
    }
  };
  
  // Desen değerini güncelle
  const updatePatternValue = (key, value) => {
    setPatternValue(prev => ({
      ...prev,
      [key]: value
    }));
  };
  
  // Haftanın günü seçimini güncelle
  const toggleWeekday = (day) => {
    const currentDays = patternValue.days || [];
    
    if (currentDays.includes(day)) {
      updatePatternValue('days', currentDays.filter(d => d !== day));
    } else {
      updatePatternValue('days', [...currentDays, day]);
    }
  };
  
  // Deseni kaydet
  const savePattern = async () => {
    try {
      // Form doğrulama
      if (!name.trim()) {
        Alert.alert('Hata', 'Lütfen bir desen adı girin.');
        return;
      }
      
      setSaving(true);
      
      // Desen verilerini hazırla
      const patternData = {
        name,
        description,
        pattern_type: patternType,
        pattern_value: patternValue
      };
      
      if (isEditing) {
        // Deseni güncelle
        await reminderPatternService.updatePattern(db, patternId, patternData);
        Alert.alert('Başarılı', 'Desen güncellendi.');
      } else {
        // Yeni desen ekle
        await reminderPatternService.addPattern(db, patternData);
        Alert.alert('Başarılı', 'Desen eklendi.');
      }
      
      setSaving(false);
      navigation.goBack();
    } catch (error) {
      console.error('Desen kaydetme hatası:', error);
      Alert.alert('Hata', 'Desen kaydedilirken bir hata oluştu.');
      setSaving(false);
    }
  };
  
  // Desen tipi açıklaması
  const getPatternTypeDescription = (type) => {
    switch (type) {
      case 'monthly_day':
        return 'Her ayın belirli günü';
      case 'monthly_last_day':
        return 'Her ayın son günü';
      case 'monthly_last_business_day':
        return 'Her ayın son iş günü';
      case 'monthly_weekday':
        return 'Her ayın belirli haftasının belirli günü';
      case 'interval_months':
        return 'Belirli ay aralıklarıyla';
      case 'interval_weeks':
        return 'Belirli hafta aralıklarıyla';
      case 'weekdays':
        return 'Haftanın belirli günleri';
      default:
        return 'Özel tekrarlama deseni';
    }
  };
  
  // Yükleniyor durumu
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={styles.loadingText}>Yükleniyor...</Text>
      </View>
    );
  }
  
  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>
          {isEditing ? 'Deseni Düzenle' : 'Yeni Desen'}
        </Text>
      </View>
      
      {/* Desen Adı */}
      <View style={styles.formGroup}>
        <Text style={styles.label}>Desen Adı</Text>
        <TextInput
          style={styles.input}
          value={name}
          onChangeText={setName}
          placeholder="Desen adını girin"
          placeholderTextColor={Colors.GRAY_500}
        />
      </View>
      
      {/* Desen Açıklaması */}
      <View style={styles.formGroup}>
        <Text style={styles.label}>Açıklama (Opsiyonel)</Text>
        <TextInput
          style={[styles.input, styles.textArea]}
          value={description}
          onChangeText={setDescription}
          placeholder="Desen açıklamasını girin"
          placeholderTextColor={Colors.GRAY_500}
          multiline
          numberOfLines={3}
          textAlignVertical="top"
        />
      </View>
      
      {/* Desen Tipi */}
      <View style={styles.formGroup}>
        <Text style={styles.label}>Desen Tipi</Text>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.patternTypesContainer}
        >
          {[
            'monthly_day',
            'monthly_last_day',
            'monthly_last_business_day',
            'monthly_weekday',
            'interval_months',
            'interval_weeks',
            'weekdays'
          ].map(type => (
            <TouchableOpacity
              key={type}
              style={[
                styles.patternTypeButton,
                patternType === type && styles.patternTypeButtonActive
              ]}
              onPress={() => handlePatternTypeChange(type)}
            >
              <Text style={[
                styles.patternTypeText,
                patternType === type && styles.patternTypeTextActive
              ]}>
                {getPatternTypeDescription(type)}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
      
      {/* Desen Değeri */}
      <View style={styles.formGroup}>
        <Text style={styles.label}>Desen Değeri</Text>
        
        {/* Her ayın belirli günü */}
        {patternType === 'monthly_day' && (
          <View style={styles.valueContainer}>
            <Text style={styles.valueLabel}>Her ayın</Text>
            <View style={styles.dayPickerContainer}>
              <TouchableOpacity
                style={styles.dayPickerButton}
                onPress={() => updatePatternValue('day', Math.max(1, (patternValue.day || 1) - 1))}
              >
                <MaterialIcons name="remove" size={20} color={Colors.TEXT_DARK} />
              </TouchableOpacity>
              <Text style={styles.dayPickerValue}>{patternValue.day || 1}.</Text>
              <TouchableOpacity
                style={styles.dayPickerButton}
                onPress={() => updatePatternValue('day', Math.min(31, (patternValue.day || 1) + 1))}
              >
                <MaterialIcons name="add" size={20} color={Colors.TEXT_DARK} />
              </TouchableOpacity>
            </View>
            <Text style={styles.valueLabel}>günü</Text>
          </View>
        )}
        
        {/* Her ayın belirli haftasının belirli günü */}
        {patternType === 'monthly_weekday' && (
          <View style={styles.valueContainer}>
            <Text style={styles.valueLabel}>Her ayın</Text>
            <View style={styles.pickerContainer}>
              <TouchableOpacity
                style={styles.pickerButton}
                onPress={() => {
                  const current = patternValue.occurrence || 1;
                  const newValue = current <= -3 ? 1 : (current === 1 ? -1 : current - 1);
                  updatePatternValue('occurrence', newValue);
                }}
              >
                <MaterialIcons name="arrow-left" size={20} color={Colors.TEXT_DARK} />
              </TouchableOpacity>
              <Text style={styles.pickerValue}>
                {patternValue.occurrence === 1 ? 'ilk' :
                 patternValue.occurrence === 2 ? 'ikinci' :
                 patternValue.occurrence === 3 ? 'üçüncü' :
                 patternValue.occurrence === 4 ? 'dördüncü' :
                 patternValue.occurrence === 5 ? 'beşinci' :
                 patternValue.occurrence === -1 ? 'son' :
                 patternValue.occurrence === -2 ? 'sondan bir önceki' :
                 patternValue.occurrence === -3 ? 'sondan ikinci' :
                 patternValue.occurrence || 'ilk'}
              </Text>
              <TouchableOpacity
                style={styles.pickerButton}
                onPress={() => {
                  const current = patternValue.occurrence || 1;
                  const newValue = current >= 5 ? -3 : (current === -1 ? 1 : current + 1);
                  updatePatternValue('occurrence', newValue);
                }}
              >
                <MaterialIcons name="arrow-right" size={20} color={Colors.TEXT_DARK} />
              </TouchableOpacity>
            </View>
            
            <View style={styles.pickerContainer}>
              <TouchableOpacity
                style={styles.pickerButton}
                onPress={() => {
                  const current = patternValue.weekday || 1;
                  updatePatternValue('weekday', current === 0 ? 6 : current - 1);
                }}
              >
                <MaterialIcons name="arrow-left" size={20} color={Colors.TEXT_DARK} />
              </TouchableOpacity>
              <Text style={styles.pickerValue}>
                {patternValue.weekday === 0 ? 'Pazar' :
                 patternValue.weekday === 1 ? 'Pazartesi' :
                 patternValue.weekday === 2 ? 'Salı' :
                 patternValue.weekday === 3 ? 'Çarşamba' :
                 patternValue.weekday === 4 ? 'Perşembe' :
                 patternValue.weekday === 5 ? 'Cuma' :
                 patternValue.weekday === 6 ? 'Cumartesi' :
                 'Pazartesi'}
              </Text>
              <TouchableOpacity
                style={styles.pickerButton}
                onPress={() => {
                  const current = patternValue.weekday || 1;
                  updatePatternValue('weekday', current === 6 ? 0 : current + 1);
                }}
              >
                <MaterialIcons name="arrow-right" size={20} color={Colors.TEXT_DARK} />
              </TouchableOpacity>
            </View>
            <Text style={styles.valueLabel}>günü</Text>
          </View>
        )}
        
        {/* Belirli ay aralıklarıyla */}
        {patternType === 'interval_months' && (
          <View style={styles.valueContainer}>
            <Text style={styles.valueLabel}>Her</Text>
            <View style={styles.dayPickerContainer}>
              <TouchableOpacity
                style={styles.dayPickerButton}
                onPress={() => updatePatternValue('months', Math.max(1, (patternValue.months || 1) - 1))}
              >
                <MaterialIcons name="remove" size={20} color={Colors.TEXT_DARK} />
              </TouchableOpacity>
              <Text style={styles.dayPickerValue}>{patternValue.months || 1}</Text>
              <TouchableOpacity
                style={styles.dayPickerButton}
                onPress={() => updatePatternValue('months', (patternValue.months || 1) + 1)}
              >
                <MaterialIcons name="add" size={20} color={Colors.TEXT_DARK} />
              </TouchableOpacity>
            </View>
            <Text style={styles.valueLabel}>ayda bir</Text>
          </View>
        )}
        
        {/* Belirli hafta aralıklarıyla */}
        {patternType === 'interval_weeks' && (
          <View style={styles.valueContainer}>
            <Text style={styles.valueLabel}>Her</Text>
            <View style={styles.dayPickerContainer}>
              <TouchableOpacity
                style={styles.dayPickerButton}
                onPress={() => updatePatternValue('weeks', Math.max(1, (patternValue.weeks || 1) - 1))}
              >
                <MaterialIcons name="remove" size={20} color={Colors.TEXT_DARK} />
              </TouchableOpacity>
              <Text style={styles.dayPickerValue}>{patternValue.weeks || 1}</Text>
              <TouchableOpacity
                style={styles.dayPickerButton}
                onPress={() => updatePatternValue('weeks', (patternValue.weeks || 1) + 1)}
              >
                <MaterialIcons name="add" size={20} color={Colors.TEXT_DARK} />
              </TouchableOpacity>
            </View>
            <Text style={styles.valueLabel}>haftada bir</Text>
          </View>
        )}
        
        {/* Haftanın belirli günleri */}
        {patternType === 'weekdays' && (
          <View style={styles.weekdaysContainer}>
            {[
              { id: 1, name: 'Pzt' },
              { id: 2, name: 'Sal' },
              { id: 3, name: 'Çar' },
              { id: 4, name: 'Per' },
              { id: 5, name: 'Cum' },
              { id: 6, name: 'Cmt' },
              { id: 0, name: 'Paz' }
            ].map(day => (
              <TouchableOpacity
                key={day.id}
                style={[
                  styles.weekdayButton,
                  (patternValue.days || []).includes(day.id) && styles.weekdayButtonActive
                ]}
                onPress={() => toggleWeekday(day.id)}
              >
                <Text style={[
                  styles.weekdayText,
                  (patternValue.days || []).includes(day.id) && styles.weekdayTextActive
                ]}>
                  {day.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        )}
        
        {/* Her ayın son günü veya son iş günü */}
        {(patternType === 'monthly_last_day' || patternType === 'monthly_last_business_day') && (
          <View style={styles.valueContainer}>
            <Text style={styles.valueInfo}>
              {patternType === 'monthly_last_day' 
                ? 'Bu desen, her ayın son gününde tekrarlanır.' 
                : 'Bu desen, her ayın son iş gününde tekrarlanır (hafta sonu hariç).'}
            </Text>
          </View>
        )}
      </View>
      
      {/* Önizleme */}
      <View style={styles.formGroup}>
        <Text style={styles.label}>Önizleme</Text>
        <View style={styles.previewContainer}>
          <Text style={styles.previewText}>
            {reminderPatternService.getPatternDescription(patternType, patternValue)}
          </Text>
        </View>
      </View>
      
      {/* Kaydet Butonu */}
      <TouchableOpacity
        style={styles.saveButton}
        onPress={savePattern}
        disabled={saving}
      >
        {saving ? (
          <ActivityIndicator size="small" color="#fff" />
        ) : (
          <>
            <MaterialIcons name="save" size={20} color="#fff" />
            <Text style={styles.saveButtonText}>
              {isEditing ? 'Deseni Güncelle' : 'Deseni Kaydet'}
            </Text>
          </>
        )}
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 32,
  },
  header: {
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: Colors.PRIMARY,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    marginBottom: 8,
  },
  input: {
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: Colors.TEXT_DARK,
  },
  textArea: {
    minHeight: 80,
  },
  patternTypesContainer: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  patternTypeButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: Colors.GRAY_300,
    borderRadius: 8,
    marginRight: 8,
    backgroundColor: Colors.GRAY_100,
  },
  patternTypeButtonActive: {
    backgroundColor: Colors.PRIMARY,
    borderColor: Colors.PRIMARY,
  },
  patternTypeText: {
    fontSize: 14,
    color: Colors.TEXT_DARK,
  },
  patternTypeTextActive: {
    color: '#fff',
    fontWeight: 'bold',
  },
  valueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    padding: 16,
  },
  valueLabel: {
    fontSize: 16,
    color: Colors.TEXT_DARK,
    marginHorizontal: 8,
  },
  dayPickerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 4,
    borderWidth: 1,
    borderColor: Colors.GRAY_300,
  },
  dayPickerButton: {
    padding: 8,
  },
  dayPickerValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    minWidth: 30,
    textAlign: 'center',
  },
  pickerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 4,
    borderWidth: 1,
    borderColor: Colors.GRAY_300,
    marginVertical: 8,
    marginHorizontal: 4,
  },
  pickerButton: {
    padding: 8,
  },
  pickerValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    minWidth: 120,
    textAlign: 'center',
  },
  weekdaysContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    padding: 16,
  },
  weekdayButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 20,
    borderWidth: 1,
    borderColor: Colors.GRAY_300,
  },
  weekdayButtonActive: {
    backgroundColor: Colors.PRIMARY,
    borderColor: Colors.PRIMARY,
  },
  weekdayText: {
    fontSize: 14,
    color: Colors.TEXT_DARK,
  },
  weekdayTextActive: {
    color: '#fff',
    fontWeight: 'bold',
  },
  valueInfo: {
    fontSize: 16,
    color: Colors.TEXT_DARK,
    textAlign: 'center',
    padding: 8,
  },
  previewContainer: {
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    padding: 16,
  },
  previewText: {
    fontSize: 16,
    color: Colors.TEXT_DARK,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  saveButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
    borderRadius: 8,
    padding: 16,
    marginTop: 16,
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
});

export default ReminderPatternFormScreen;
