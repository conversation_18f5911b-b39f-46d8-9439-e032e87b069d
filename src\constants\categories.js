/**
 * <PERSON><PERSON><PERSON> ka<PERSON> listesi
 * @constant {Array<Object>}
 */
export const EXPENSE_CATEGORIES = [
    { label: 'Gıda', value: 'food' },
    { label: 'Ulaşım', value: 'transportation' },
    { label: 'Kira', value: 'rent' },
    { label: 'Faturalar', value: 'bills' },
    { label: 'Alışveriş', value: 'shopping' },
    { label: 'Sağlık', value: 'health' },
    { label: 'Eğitim', value: 'education' },
    { label: 'Eğlence', value: 'entertainment' },
    { label: 'Spor', value: 'sports' },
    { label: 'Bakım', value: 'maintenance' },
    { label: 'Diğer', value: 'other' }
];

/**
 * <PERSON><PERSON><PERSON> renklerini tanımlar
 * @constant {Object}
 */
export const CATEGORY_COLORS = {
    food: '#FF6B6B',
    transportation: '#4ECDC4',
    rent: '#45B7D1',
    bills: '#96CEB4',
    shopping: '#FFEEAD',
    health: '#D4A5A5',
    education: '#9B9B9B',
    entertainment: '#FFD93D',
    sports: '#6BCB77',
    maintenance: '#4D96FF',
    other: '#95A5A6'
};

/**
 * Kategori ikonlarını tanımlar
 * @constant {Object}
 */
export const CATEGORY_ICONS = {
    food: 'restaurant',
    transportation: 'directions-car',
    rent: 'home',
    bills: 'receipt',
    shopping: 'shopping-cart',
    health: 'medical-services',
    education: 'school',
    entertainment: 'movie',
    sports: 'sports',
    maintenance: 'build',
    other: 'more-horiz'
};
