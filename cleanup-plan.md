# Temizleme Planı

## Silinecek Dosyalar

### Eski Navigasyon Dosyaları
- `src/navigation/AppNavigator.js` - Expo Router kullanıldığı için gereksiz
- `src/navigation/TabsNavigator.js` - Expo Router kullanıldığı için gereksiz
- `src/navigation/AuthNavigator.js` (varsa) - Expo Router kullanıldığı için gereksiz

### Kullanılmayan veya Tekrarlanan Ekranlar
- `src/screens/HomeScreen.js` - `app/(tabs)/home.js` ile değiştirildi
- `src/screens/DashboardScreen.js` - Artık kullanılmıyor
- `src/screens/SavingsScreen.js` - Yeni yapıda kullanılmayacak
- `src/screens/InvestmentScreen.js` - Yeni yapıda kullanılmayacak

### Eski Veritabanı Dosyaları
- `src/db/migrations/initialMigration.js` - `src/db/initialMigration.js` ile birleştirildi
- Diğer eski migrasyon dosyaları

### Gereksiz Bileşenler
- `src/components/home/<USER>
- `src/components/home/<USER>

## Güncellenecek Dosyalar

### Veritabanı Yapısı
- `src/db/initialMigration.js` - Tek bir tutarlı veritabanı şeması kullanılacak
- `src/db/schema.js` - Tüm tablo tanımları burada olacak

### Tab Bar ve Navigasyon
- `app/(tabs)/_layout.js` - Yeni tab bar yapısı
- `app/_layout.js` - Ana navigasyon yapısı

### Ana Ekranlar
- `app/(tabs)/home.js` - Ana sayfa
- `app/(tabs)/transactions.js` - İşlemler sayfası
- `app/(tabs)/stats.js` - İstatistikler sayfası
- `app/(tabs)/settings.js` - Ayarlar sayfası

## Yeni Oluşturulacak Dosyalar

### Yeni Ekranlar
- `app/(tabs)/stats.js` - İstatistikler ekranı
- `src/screens/StatsScreen.js` - İstatistikler ekranı içeriği

### Yeni Bileşenler
- `src/components/stats/StatsSummaryCard.js` - İstatistik özeti kartı
- `src/components/stats/ExpenseChart.js` - Gider grafiği
- `src/components/stats/IncomeChart.js` - Gelir grafiği
- `src/components/stats/CategoryDistributionChart.js` - Kategori dağılım grafiği
