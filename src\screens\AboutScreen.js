import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Linking,
  Alert
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import { useAppContext } from '../context/AppContext';

/**
 * Hakkında Ekranı
 * Uygulama bilgileri, versiyon, geliştirici bilgileri
 */
export default function AboutScreen({ navigation }) {
  const insets = useSafeAreaInsets();
  const { theme } = useAppContext();

  // Uygulama bilgileri
  const appInfo = {
    name: 'Finansal Takip',
    version: '1.0.0',
    buildNumber: '1',
    description: 'Kişisel finans yönetimi için kapsamlı bir uygulama',
    developer: 'Berkcan Yüzer',
    email: '<EMAIL>',
    github: 'https://github.com/BerkcanYuzer',
    website: 'https://github.com/BerkcanYuzer/maas-sqlite-expo'
  };

  // Link açma fonksiyonu
  const openLink = async (url) => {
    try {
      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      } else {
        Alert.alert('Hata', 'Bu bağlantı açılamıyor');
      }
    } catch (error) {
      console.error('Link açma hatası:', error);
      Alert.alert('Hata', 'Bağlantı açılırken bir hata oluştu');
    }
  };

  // Email gönderme
  const sendEmail = () => {
    const subject = encodeURIComponent('Finansal Takip Uygulaması Hakkında');
    const body = encodeURIComponent('Merhaba,\n\nFinansal Takip uygulaması hakkında...');
    openLink(`mailto:${appInfo.email}?subject=${subject}&body=${body}`);
  };

  // Özellikler listesi
  const features = [
    { icon: 'account-balance-wallet', title: 'Gelir/Gider Takibi', desc: 'Detaylı finansal işlem yönetimi' },
    { icon: 'work', title: 'Maaş Takibi', desc: 'Aylık maaş ve ek ödemeler' },
    { icon: 'schedule', title: 'Mesai Takibi', desc: 'Overtime ve vardiya yönetimi' },
    { icon: 'savings', title: 'Birikim Hedefleri', desc: 'Hedef odaklı tasarruf planları' },
    { icon: 'account-balance', title: 'Bütçe Yönetimi', desc: 'Aylık ve kategorik bütçe kontrolü' },
    { icon: 'notifications', title: 'Hatırlatıcılar', desc: 'Özel bildirim sistemi' },
    { icon: 'currency-exchange', title: 'Döviz Takibi', desc: 'Gerçek zamanlı kur bilgileri' },
    { icon: 'trending-up', title: 'Yatırım Takibi', desc: 'Portföy ve yatırım analizi' },
    { icon: 'dark-mode', title: 'Dark Mode', desc: 'Göz dostu karanlık tema' },
    { icon: 'security', title: 'Güvenlik', desc: 'PIN ve biometrik koruma' }
  ];

  return (
    <View style={[styles.container, { paddingTop: insets.top, backgroundColor: theme.BACKGROUND }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.PRIMARY }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color={theme.WHITE} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.WHITE }]}>Hakkında</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.content}>
        {/* Uygulama Bilgileri */}
        <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
          <View style={styles.appIconContainer}>
            <View style={[styles.appIcon, { backgroundColor: theme.PRIMARY }]}>
              <MaterialIcons name="account-balance-wallet" size={48} color={theme.WHITE} />
            </View>
          </View>
          
          <Text style={[styles.appName, { color: theme.TEXT_PRIMARY }]}>{appInfo.name}</Text>
          <Text style={[styles.appVersion, { color: theme.TEXT_SECONDARY }]}>
            Versiyon {appInfo.version} (Build {appInfo.buildNumber})
          </Text>
          <Text style={[styles.appDescription, { color: theme.TEXT_SECONDARY }]}>
            {appInfo.description}
          </Text>
        </View>

        {/* Özellikler */}
        <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>Özellikler</Text>
          
          {features.map((feature, index) => (
            <View key={index} style={styles.featureItem}>
              <View style={[styles.featureIcon, { backgroundColor: `${theme.PRIMARY}20` }]}>
                <MaterialIcons name={feature.icon} size={24} color={theme.PRIMARY} />
              </View>
              <View style={styles.featureText}>
                <Text style={[styles.featureTitle, { color: theme.TEXT_PRIMARY }]}>
                  {feature.title}
                </Text>
                <Text style={[styles.featureDesc, { color: theme.TEXT_SECONDARY }]}>
                  {feature.desc}
                </Text>
              </View>
            </View>
          ))}
        </View>

        {/* Geliştirici Bilgileri */}
        <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>Geliştirici</Text>
          
          <View style={styles.developerInfo}>
            <View style={[styles.developerIcon, { backgroundColor: theme.PRIMARY }]}>
              <MaterialIcons name="person" size={32} color={theme.WHITE} />
            </View>
            <View style={styles.developerText}>
              <Text style={[styles.developerName, { color: theme.TEXT_PRIMARY }]}>
                {appInfo.developer}
              </Text>
              <Text style={[styles.developerRole, { color: theme.TEXT_SECONDARY }]}>
                Full Stack Developer
              </Text>
            </View>
          </View>

          {/* İletişim Linkleri */}
          <TouchableOpacity style={styles.contactItem} onPress={sendEmail}>
            <MaterialIcons name="email" size={24} color={theme.PRIMARY} />
            <Text style={[styles.contactText, { color: theme.TEXT_PRIMARY }]}>
              {appInfo.email}
            </Text>
            <MaterialIcons name="open-in-new" size={20} color={theme.TEXT_SECONDARY} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.contactItem} onPress={() => openLink(appInfo.github)}>
            <MaterialIcons name="code" size={24} color={theme.PRIMARY} />
            <Text style={[styles.contactText, { color: theme.TEXT_PRIMARY }]}>
              GitHub Profili
            </Text>
            <MaterialIcons name="open-in-new" size={20} color={theme.TEXT_SECONDARY} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.contactItem} onPress={() => openLink(appInfo.website)}>
            <MaterialIcons name="language" size={24} color={theme.PRIMARY} />
            <Text style={[styles.contactText, { color: theme.TEXT_PRIMARY }]}>
              Proje Deposu
            </Text>
            <MaterialIcons name="open-in-new" size={20} color={theme.TEXT_SECONDARY} />
          </TouchableOpacity>
        </View>

        {/* Teknoloji Stack */}
        <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>Teknoloji</Text>
          
          <View style={styles.techGrid}>
            <View style={styles.techItem}>
              <Text style={[styles.techName, { color: theme.TEXT_PRIMARY }]}>React Native</Text>
              <Text style={[styles.techDesc, { color: theme.TEXT_SECONDARY }]}>Mobil Framework</Text>
            </View>
            <View style={styles.techItem}>
              <Text style={[styles.techName, { color: theme.TEXT_PRIMARY }]}>Expo</Text>
              <Text style={[styles.techDesc, { color: theme.TEXT_SECONDARY }]}>Development Platform</Text>
            </View>
            <View style={styles.techItem}>
              <Text style={[styles.techName, { color: theme.TEXT_PRIMARY }]}>SQLite</Text>
              <Text style={[styles.techDesc, { color: theme.TEXT_SECONDARY }]}>Local Database</Text>
            </View>
            <View style={styles.techItem}>
              <Text style={[styles.techName, { color: theme.TEXT_PRIMARY }]}>AsyncStorage</Text>
              <Text style={[styles.techDesc, { color: theme.TEXT_SECONDARY }]}>Data Persistence</Text>
            </View>
          </View>
        </View>

        {/* Telif Hakkı */}
        <View style={[styles.section, { backgroundColor: theme.SURFACE }]}>
          <Text style={[styles.copyright, { color: theme.TEXT_SECONDARY }]}>
            © 2024 {appInfo.developer}. Tüm hakları saklıdır.
          </Text>
          <Text style={[styles.copyright, { color: theme.TEXT_SECONDARY }]}>
            Bu uygulama kişisel finans yönetimi amacıyla geliştirilmiştir.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  headerTitle: {
    flex: 1,
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000', // Keep shadow color for compatibility
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  appIconContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  appIcon: {
    width: 80,
    height: 80,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  appName: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  appVersion: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 12,
  },
  appDescription: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  featureIcon: {
    width: 48,
    height: 48,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  featureText: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  featureDesc: {
    fontSize: 14,
  },
  developerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  developerIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  developerText: {
    flex: 1,
  },
  developerName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  developerRole: {
    fontSize: 14,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    // Border color set dynamically with theme
  },
  contactText: {
    flex: 1,
    fontSize: 16,
    marginLeft: 16,
  },
  techGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  techItem: {
    width: '48%',
    marginBottom: 16,
  },
  techName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  techDesc: {
    fontSize: 14,
  },
  copyright: {
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 18,
    marginBottom: 8,
  },
});
