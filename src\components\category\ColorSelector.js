import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Modal
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../../constants/colors';
import { useTheme } from '../../context/ThemeContext';

// <PERSON><PERSON><PERSON> renkleri
const CATEGORY_COLORS = [
  // Kırmızı tonları
  '#e74c3c', '#c0392b', '#e57373', '#f44336', '#d32f2f', '#b71c1c',
  // <PERSON>run<PERSON> tonları
  '#e67e22', '#d35400', '#ff9800', '#f57c00', '#ef6c00', '#e65100',
  // Sarı tonları
  '#f1c40f', '#f39c12', '#ffeb3b', '#fdd835', '#fbc02d', '#f57f17',
  // <PERSON><PERSON><PERSON> tonları
  '#2ecc71', '#27ae60', '#4caf50', '#43a047', '#388e3c', '#2e7d32',
  // Mavi tonlar<PERSON>
  '#3498db', '#2980b9', '#2196f3', '#1e88e5', '#1976d2', '#1565c0',
  // Mor tonları
  '#9b59b6', '#8e44ad', '#9c27b0', '#8e24aa', '#7b1fa2', '#6a1b9a',
  // Pembe tonları
  '#e91e63', '#d81b60', '#c2185b', '#ad1457', '#880e4f', '#ff80ab',
  // Turkuaz tonları
  '#1abc9c', '#16a085', '#26a69a', '#00897b', '#00796b', '#00695c',
  // Gri tonları
  '#95a5a6', '#7f8c8d', '#9e9e9e', '#757575', '#616161', '#424242',
  // Kahverengi tonları
  '#795548', '#6d4c41', '#5d4037', '#4e342e', '#3e2723', '#d7ccc8',
];

/**
 * Renk seçici bileşeni
 *
 * @param {Object} props - Bileşen props'ları
 * @param {boolean} props.visible - Modal görünürlüğü
 * @param {string} props.selectedColor - Seçili renk
 * @param {Function} props.onSelectColor - Renk seçildiğinde çağrılacak fonksiyon
 * @param {Function} props.onClose - Modal kapatıldığında çağrılacak fonksiyon
 * @returns {JSX.Element} Renk seçici bileşeni
 */
const ColorSelector = ({ visible, selectedColor, onSelectColor, onClose }) => {
  const { theme } = useTheme();

  // Renk öğesi
  const renderColorItem = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.colorItem,
        { backgroundColor: item },
        selectedColor === item && styles.selectedColorItem
      ]}
      onPress={() => onSelectColor(item)}
    >
      {selectedColor === item && (
        <MaterialIcons name="check" size={20} color="#fff" />
      )}
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={visible}
      animationType="fade"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContainer, { backgroundColor: theme.SURFACE }]}>
          {/* Başlık */}
          <View style={[styles.header, { borderBottomColor: theme.BORDER }]}>
            <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>Renk Seç</Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={onClose}
            >
              <MaterialIcons name="close" size={24} color={theme.TEXT_PRIMARY} />
            </TouchableOpacity>
          </View>

          {/* Renk Listesi */}
          <FlatList
            data={CATEGORY_COLORS}
            renderItem={renderColorItem}
            keyExtractor={(item, index) => `${item}_${index}`}
            numColumns={5}
            contentContainerStyle={styles.colorList}
          />

          {/* Alt Butonlar */}
          <View style={[styles.footer, { borderTopColor: theme.BORDER }]}>
            <TouchableOpacity
              style={[styles.cancelButton, { backgroundColor: theme.SURFACE_VARIANT }]}
              onPress={onClose}
            >
              <Text style={[styles.cancelButtonText, { color: theme.TEXT_PRIMARY }]}>Kapat</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 16,
  },
  modalContainer: {
    width: '90%',
    maxHeight: '80%',
    borderRadius: 12,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  closeButton: {
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  colorList: {
    padding: 12,
  },
  colorItem: {
    width: '18%',
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
    margin: '1%',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  selectedColorItem: {
    borderWidth: 2,
    borderColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 12,
    borderTopWidth: 1,
  },
  cancelButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 4,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
});

export default ColorSelector;
