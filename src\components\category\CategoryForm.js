import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, StyleSheet, TouchableOpacity, Modal, ScrollView, Alert } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useSQLiteContext } from 'expo-sqlite';
import { Colors } from '../../constants/colors';
import { useAppContext } from '../../context/AppContext';
import { useTheme } from '../../context/ThemeContext';
import ColorSelector from './ColorSelector';
import IconSelector from './IconSelector';

/**
 * Kategori formu bileşeni
 * @param {Object} props Component props
 * @  saveButton: {
    backgroundColor: Colors.PRIMARY,
    paddingHorizontal: 32,
    paddingVertical: 14,
    borderRadius: 16,
    shadowColor: Colors.PRIMARY,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  saveButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '700',
    letterSpacing: 0.5,
  },
});an} props.visible Modal görünürlüğü
 * @param {Function} props.onClose Modal kapatma fonksiyonu
 * @param {Object} props.category Düzenlenecek kategori (yoksa yeni kategori)
 * @param {string} props.type Kategori tipi ('income', 'expense', 'both')
 * @param {Function} props.onSave Kategori kaydetme fonksiyonu
 */
const CategoryForm = ({ visible, onClose, category, type = 'both', onSave }) => {
  const db = useSQLiteContext();
  const { theme } = useAppContext();
  const themeContext = useTheme();
  const currentTheme = themeContext?.theme || theme;

  const [name, setName] = useState('');
  const [selectedType, setSelectedType] = useState(type);
  const [selectedColor, setSelectedColor] = useState('#3498db');
  const [selectedIcon, setSelectedIcon] = useState('category');
  const [showColorSelector, setShowColorSelector] = useState(false);
  const [showIconSelector, setShowIconSelector] = useState(false);
  const [isDefault, setIsDefault] = useState(false);

  // Kategori düzenleme durumunda verileri yükle
  useEffect(() => {
    if (category) {
      setName(category.name || '');
      setSelectedType(category.type || type);
      setSelectedColor(category.color || '#3498db');
      setSelectedIcon(category.icon || 'category');
      setIsDefault(category.is_default === 1);
    } else {
      // Yeni kategori
      setName('');
      setSelectedType(type);
      setSelectedColor('#3498db');
      setSelectedIcon('category');
      setIsDefault(false);
    }
  }, [category, type]);

  // Renk seçici modalını aç/kapat
  const toggleColorSelector = () => {
    setShowColorSelector(!showColorSelector);
  };

  // İkon seçici modalını aç/kapat
  const toggleIconSelector = () => {
    setShowIconSelector(!showIconSelector);
  };

  // Renk seç
  const selectColor = (color) => {
    setSelectedColor(color);
    setShowColorSelector(false);
  };

  // İkon seç
  const selectIcon = (icon) => {
    setSelectedIcon(icon);
    setShowIconSelector(false);
  };

  // Kategori tipini değiştir
  const changeType = (type) => {
    setSelectedType(type);
  };

  // Kategoriyi kaydet
  const saveCategory = async () => {
    try {
      // Validasyon
      if (!name.trim()) {
        Alert.alert('Hata', 'Kategori adı boş olamaz');
        return;
      }

      // Kategori verilerini hazırla
      const categoryData = {
        name: name.trim(),
        type: selectedType,
        color: selectedColor,
        icon: selectedIcon,
        is_default: isDefault ? 1 : 0
      };

      // Kategori düzenleme
      if (category && category.id) {
        await db.runAsync(`
          UPDATE categories
          SET name = ?, type = ?, color = ?, icon = ?, is_default = ?
          WHERE id = ?
        `, [
          categoryData.name,
          categoryData.type,
          categoryData.color,
          categoryData.icon,
          categoryData.is_default,
          category.id
        ]);

        // Eğer varsayılan kategori olarak işaretlendiyse, diğer varsayılan kategorileri güncelle
        if (categoryData.is_default) {
          await db.runAsync(`
            UPDATE categories
            SET is_default = 0
            WHERE id != ? AND type = ?
          `, [category.id, categoryData.type]);
        }

        if (onSave) onSave(categoryData);
        onClose();
      }
      // Yeni kategori ekleme
      else {
        // Eğer varsayılan kategori olarak işaretlendiyse, diğer varsayılan kategorileri güncelle
        if (categoryData.is_default) {
          await db.runAsync(`
            UPDATE categories
            SET is_default = 0
            WHERE type = ?
          `, [categoryData.type]);
        }

        const result = await db.runAsync(`
          INSERT INTO categories (name, type, color, icon, is_default)
          VALUES (?, ?, ?, ?, ?)
        `, [
          categoryData.name,
          categoryData.type,
          categoryData.color,
          categoryData.icon,
          categoryData.is_default
        ]);

        categoryData.id = result.lastInsertRowId;

        if (onSave) onSave(categoryData);
        onClose();
      }
    } catch (error) {
      console.error('Kategori kaydetme hatası:', error);
      Alert.alert('Hata', 'Kategori kaydedilirken bir hata oluştu');
    }
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={[styles.modalContent, { backgroundColor: currentTheme.SURFACE }]}>
          <View style={[styles.modalHeader, { borderBottomColor: currentTheme.BORDER }]}>
            <Text style={[styles.modalTitle, { color: currentTheme.TEXT_PRIMARY }]}>
              {category ? 'Kategori Düzenle' : 'Yeni Kategori'}
            </Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <MaterialIcons name="close" size={24} color={currentTheme.TEXT_PRIMARY} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.formContainer} showsVerticalScrollIndicator={false}>
            {/* Kategori Önizleme */}
            <View style={[styles.previewContainer, { backgroundColor: currentTheme.SURFACE_VARIANT }]}>
              <View style={[styles.previewIcon, { backgroundColor: selectedColor }]}>
                <MaterialIcons name={selectedIcon} size={32} color="#fff" />
              </View>
              <Text style={[styles.previewName, { color: currentTheme.TEXT_PRIMARY }]}>
                {name || 'Yeni Kategori'}
              </Text>
              <View style={[styles.previewType, {
                backgroundColor: selectedType === 'income'
                  ? Colors.SUCCESS
                  : selectedType === 'expense'
                    ? Colors.DANGER
                    : Colors.WARNING
              }]}>
                <Text style={styles.previewTypeText}>
                  {selectedType === 'income'
                    ? 'Gelir'
                    : selectedType === 'expense'
                      ? 'Gider'
                      : 'Her İkisi'}
                </Text>
              </View>
            </View>

            {/* Kategori Adı */}
            <View style={styles.inputContainer}>
              <Text style={[styles.inputLabel, { color: currentTheme.TEXT_PRIMARY }]}>Kategori Adı</Text>
              <TextInput
                style={[styles.textInput, { 
                  backgroundColor: currentTheme.SURFACE_VARIANT,
                  color: currentTheme.TEXT_PRIMARY,
                  borderColor: currentTheme.BORDER
                }]}
                value={name}
                onChangeText={setName}
                placeholder="Kategori adı girin"
                placeholderTextColor={currentTheme.TEXT_SECONDARY}
              />
            </View>

            {/* Kategori Tipi */}
            <View style={styles.inputContainer}>
              <Text style={[styles.inputLabel, { color: currentTheme.TEXT_PRIMARY }]}>Kategori Tipi</Text>
              <View style={styles.typeContainer}>
                <TouchableOpacity
                  style={[
                    styles.typeButton,
                    { backgroundColor: currentTheme.SURFACE_VARIANT, borderColor: currentTheme.BORDER },
                    selectedType === 'income' && { backgroundColor: Colors.SUCCESS, borderColor: Colors.SUCCESS }
                  ]}
                  onPress={() => changeType('income')}
                >
                  <MaterialIcons
                    name="arrow-upward"
                    size={18}
                    color={selectedType === 'income' ? '#fff' : Colors.SUCCESS}
                  />
                  <Text
                    style={[
                      styles.typeButtonText,
                      { color: selectedType === 'income' ? '#fff' : currentTheme.TEXT_PRIMARY }
                    ]}
                  >
                    Gelir
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.typeButton,
                    { backgroundColor: currentTheme.SURFACE_VARIANT, borderColor: currentTheme.BORDER },
                    selectedType === 'expense' && { backgroundColor: Colors.DANGER, borderColor: Colors.DANGER }
                  ]}
                  onPress={() => changeType('expense')}
                >
                  <MaterialIcons
                    name="arrow-downward"
                    size={18}
                    color={selectedType === 'expense' ? '#fff' : Colors.DANGER}
                  />
                  <Text
                    style={[
                      styles.typeButtonText,
                      { color: selectedType === 'expense' ? '#fff' : currentTheme.TEXT_PRIMARY }
                    ]}
                  >
                    Gider
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.typeButton,
                    { backgroundColor: currentTheme.SURFACE_VARIANT, borderColor: currentTheme.BORDER },
                    selectedType === 'both' && { backgroundColor: Colors.WARNING, borderColor: Colors.WARNING }
                  ]}
                  onPress={() => changeType('both')}
                >
                  <MaterialIcons
                    name="swap-vert"
                    size={18}
                    color={selectedType === 'both' ? '#fff' : Colors.WARNING}
                  />
                  <Text
                    style={[
                      styles.typeButtonText,
                      { color: selectedType === 'both' ? '#fff' : currentTheme.TEXT_PRIMARY }
                    ]}
                  >
                    Her İkisi
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.rowContainer}>
              {/* Renk Seçimi */}
              <View style={[styles.inputContainer, { flex: 1 }]}>
                <Text style={[styles.inputLabel, { color: currentTheme.TEXT_PRIMARY }]}>Renk</Text>
                <TouchableOpacity
                  style={[styles.colorButton, { backgroundColor: currentTheme.SURFACE_VARIANT, borderColor: currentTheme.BORDER }]}
                  onPress={toggleColorSelector}
                >
                  <View style={[styles.colorPreview, { backgroundColor: selectedColor }]} />
                  <Text style={[styles.colorButtonText, { color: currentTheme.TEXT_PRIMARY }]}>{selectedColor}</Text>
                  <MaterialIcons name="palette" size={20} color={currentTheme.TEXT_SECONDARY} />
                </TouchableOpacity>
              </View>

              {/* İkon Seçimi */}
              <View style={[styles.inputContainer, { flex: 1 }]}>
                <Text style={[styles.inputLabel, { color: currentTheme.TEXT_PRIMARY }]}>İkon</Text>
                <TouchableOpacity
                  style={[styles.iconButton, { backgroundColor: currentTheme.SURFACE_VARIANT, borderColor: currentTheme.BORDER }]}
                  onPress={toggleIconSelector}
                >
                  <MaterialIcons name={selectedIcon} size={24} color={selectedColor} />
                  <Text style={[styles.iconButtonText, { color: currentTheme.TEXT_PRIMARY }]}>{selectedIcon}</Text>
                  <MaterialIcons name="more-horiz" size={20} color={currentTheme.TEXT_SECONDARY} />
                </TouchableOpacity>
              </View>
            </View>

            {/* Varsayılan Kategori */}
            <View style={styles.checkboxContainer}>
              <TouchableOpacity
                style={styles.checkbox}
                onPress={() => setIsDefault(!isDefault)}
              >
                <MaterialIcons
                  name={isDefault ? 'check-box' : 'check-box-outline-blank'}
                  size={24}
                  color={Colors.PRIMARY}
                />
              </TouchableOpacity>
              <Text style={[styles.checkboxLabel, { color: currentTheme.TEXT_PRIMARY }]}>
                Varsayılan kategori olarak ayarla
              </Text>
            </View>
          </ScrollView>

          <View style={[styles.buttonContainer, { borderTopColor: currentTheme.BORDER }]}>
            <TouchableOpacity
              style={[styles.cancelButton, { backgroundColor: currentTheme.SURFACE_VARIANT, borderColor: currentTheme.BORDER }]}
              onPress={onClose}
            >
              <Text style={[styles.cancelButtonText, { color: currentTheme.TEXT_PRIMARY }]}>İptal</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.saveButton}
              onPress={saveCategory}
            >
              <Text style={styles.saveButtonText}>Kaydet</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {/* Renk Seçici Modal */}
      <ColorSelector
        visible={showColorSelector}
        onClose={toggleColorSelector}
        onSelectColor={selectColor}
        selectedColor={selectedColor}
      />

      {/* İkon Seçici Modal */}
      <IconSelector
        visible={showIconSelector}
        onClose={toggleIconSelector}
        onSelectIcon={selectIcon}
        selectedIcon={selectedIcon}
        color={selectedColor}
      />
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
  },
  modalContent: {
    width: '92%',
    maxHeight: '85%',
    borderRadius: 24,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 15,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 22,
    fontWeight: '700',
    letterSpacing: 0.5,
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  formContainer: {
    padding: 20,
  },
  previewContainer: {
    alignItems: 'center',
    marginBottom: 28,
    paddingVertical: 24,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  previewIcon: {
    width: 72,
    height: 72,
    borderRadius: 36,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  previewName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  previewType: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
  },
  previewTypeText: {
    color: '#fff',
    fontWeight: '500',
    fontSize: 12,
  },
  rowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    letterSpacing: 0.3,
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 16,
    fontWeight: '500',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  typeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 8,
  },
  typeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderRadius: 16,
    flex: 1,
    marginHorizontal: 4,
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  typeButtonText: {
    marginLeft: 6,
    fontSize: 14,
    fontWeight: '600',
    letterSpacing: 0.3,
  },
  colorButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 14,
    borderWidth: 1,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  colorPreview: {
    width: 28,
    height: 28,
    borderRadius: 14,
    marginRight: 12,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.8)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
  },
  colorButtonText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
  },
  iconButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 14,
    borderWidth: 1,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  iconButtonText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 12,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    marginBottom: 24,
    paddingHorizontal: 4,
  },
  checkbox: {
    marginRight: 12,
  },
  checkboxLabel: {
    fontSize: 16,
    fontWeight: '500',
    letterSpacing: 0.3,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    padding: 24,
    paddingTop: 16,
    borderTopWidth: 1,
  },
  cancelButton: {
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderRadius: 16,
    marginRight: 16,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    letterSpacing: 0.3,
  },
  saveButton: {
    backgroundColor: Colors.PRIMARY,
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 16,
    shadowColor: Colors.PRIMARY,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  saveButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '700',
    letterSpacing: 0.5,
  },
});

export default CategoryForm;
