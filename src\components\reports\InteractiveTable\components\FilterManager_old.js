import React, { useState } from 'react';
import { View, Text, TouchableOpacity, FlatList, Modal, TextInput, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useTheme } from '../../../../context/ThemeContext';

/**
 * Filtre yönetimi bileşeni - tablo filtrelerini oluşturma ve yönetme
 * @param {Object} props - Bileşen props'ları
 * @param {Array} props.filters - Mevcut filtreler
 * @param {Function} props.onFiltersChange - Filtreler değiştiğinde çağrılacak fonksiyon
 * @param {Array} props.columns - Filtrelenebilir sütunlar
 * @param {Boolean} props.visible - Modal görünürlüğü
 * @param {Function} props.onClose - Modal kapatma fonksiyonu
 */
const FilterManager = ({ 
  filters = [], 
  onFiltersChange, 
  columns = [], 
  visible = false, 
  onClose 
}) => {
  const { theme } = useTheme();
  const [showAddModal, setShowAddModal] = useState(false);
  const [newFilter, setNewFilter] = useState({
    column: '',
    operator: 'contains',
    value: '',
    type: 'text'
  });
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [datePickerMode, setDatePickerMode] = useState('date');

  // Tema güvenlik kontrolü
  if (!theme) {
    console.error('FilterManager: Theme objesi undefined!');
    return null;
  }

  const styles = {
    container: {
      flex: 1,
      backgroundColor: theme.BACKGROUND || theme.colors?.background || '#fff',
      padding: 16,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 24,
      paddingBottom: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.BORDER || theme.colors?.border || '#e0e0e0',
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.TEXT_PRIMARY || theme.colors?.text || '#333',
    },
    closeButton: {
      padding: 8,
    },
    section: {
      marginBottom: 24,
    },
    sectionTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.TEXT_PRIMARY || theme.colors?.text || '#333',
      marginBottom: 12,
    },
    filterItem: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 12,
      backgroundColor: theme.colors.surface,
      borderRadius: 8,
      marginBottom: 8,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    filterInfo: {
      flex: 1,
      marginRight: 12,
    },
    filterText: {
      fontSize: 14,
      fontWeight: '500',
      color: theme.colors.text,
    },
    filterDescription: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginTop: 2,
    },
    removeButton: {
      padding: 8,
    },
    addButton: {
      backgroundColor: theme.colors.primary,
      padding: 12,
      borderRadius: 8,
      alignItems: 'center',
      marginBottom: 16,
    },
    addButtonText: {
      color: theme.colors.onPrimary,
      fontSize: 14,
      fontWeight: '600',
    },
    clearButton: {
      backgroundColor: theme.colors.error,
      padding: 12,
      borderRadius: 8,
      alignItems: 'center',
    },
    clearButtonText: {
      color: theme.colors.onError,
      fontSize: 14,
      fontWeight: '600',
    },
    modalContainer: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContent: {
      backgroundColor: theme.colors.surface,
      padding: 24,
      borderRadius: 12,
      width: '90%',
      maxWidth: 400,
    },
    modalTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 16,
      textAlign: 'center',
    },
    formGroup: {
      marginBottom: 16,
    },
    label: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 8,
    },
    input: {
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: 8,
      padding: 12,
      fontSize: 16,
      color: theme.colors.text,
    },
    picker: {
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: 8,
      padding: 12,
      backgroundColor: theme.colors.surface,
    },
    pickerText: {
      fontSize: 16,
      color: theme.colors.text,
    },
    modalActions: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: 16,
    },
    modalButton: {
      flex: 1,
      padding: 12,
      borderRadius: 8,
      alignItems: 'center',
      marginHorizontal: 4,
    },
    modalButtonCancel: {
      backgroundColor: theme.colors.surfaceVariant,
    },
    modalButtonConfirm: {
      backgroundColor: theme.colors.primary,
    },
    modalButtonText: {
      fontSize: 14,
      fontWeight: '600',
    },
    modalButtonTextCancel: {
      color: theme.colors.onSurfaceVariant,
    },
    modalButtonTextConfirm: {
      color: theme.colors.onPrimary,
    },
    operatorContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
    },
    operatorChip: {
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    operatorChipActive: {
      backgroundColor: theme.colors.primary,
      borderColor: theme.colors.primary,
    },
    operatorChipText: {
      fontSize: 12,
      color: theme.colors.text,
    },
    operatorChipTextActive: {
      color: theme.colors.onPrimary,
    },
  };

  const filterOperators = {
    text: [
      { value: 'contains', label: 'İçerir' },
      { value: 'equals', label: 'Eşittir' },
      { value: 'startsWith', label: 'İle başlar' },
      { value: 'endsWith', label: 'İle biter' },
      { value: 'notContains', label: 'İçermez' },
    ],
    number: [
      { value: 'equals', label: 'Eşittir' },
      { value: 'greaterThan', label: 'Büyüktür' },
      { value: 'lessThan', label: 'Küçüktür' },
      { value: 'greaterThanOrEqual', label: 'Büyük eşittir' },
      { value: 'lessThanOrEqual', label: 'Küçük eşittir' },
      { value: 'between', label: 'Arasında' },
    ],
    date: [
      { value: 'equals', label: 'Eşittir' },
      { value: 'after', label: 'Sonra' },
      { value: 'before', label: 'Önce' },
      { value: 'between', label: 'Arasında' },
      { value: 'thisWeek', label: 'Bu hafta' },
      { value: 'thisMonth', label: 'Bu ay' },
      { value: 'thisYear', label: 'Bu yıl' },
    ],
  };

  /**
   * Filtre silme işlemi
   * @param {number} index - Silinecek filtre indeksi
   */
  const handleRemoveFilter = (index) => {
    const updatedFilters = filters.filter((_, i) => i !== index);
    onFiltersChange(updatedFilters);
  };

  /**
   * Tüm filtreleri temizleme
   */
  const handleClearAll = () => {
    Alert.alert(
      'Tüm Filtreleri Temizle',
      'Tüm filtreleri silmek istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        { 
          text: 'Temizle', 
          style: 'destructive',
          onPress: () => onFiltersChange([])
        }
      ]
    );
  };

  /**
   * Yeni filtre ekleme
   */
  const handleAddFilter = () => {
    if (!newFilter.column || !newFilter.value) {
      Alert.alert('Hata', 'Lütfen tüm alanları doldurun');
      return;
    }

    const filter = {
      id: `filter_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      column: newFilter.column,
      operator: newFilter.operator,
      value: newFilter.value,
      type: newFilter.type,
    };

    onFiltersChange([...filters, filter]);
    setNewFilter({ column: '', operator: 'contains', value: '', type: 'text' });
    setShowAddModal(false);
  };

  /**
   * Sütun seçimi değiştiğinde
   * @param {string} columnId - Seçilen sütun ID'si
   */
  const handleColumnChange = (columnId) => {
    const column = columns.find(col => col.id === columnId);
    const columnType = column?.type || 'text';
    const defaultOperator = filterOperators[columnType]?.[0]?.value || 'contains';
    
    setNewFilter({
      ...newFilter,
      column: columnId,
      type: columnType,
      operator: defaultOperator,
    });
  };

  /**
   * Filtre açıklaması oluşturma
   * @param {Object} filter - Filtre objesi
   * @returns {string} Filtre açıklaması
   */
  const getFilterDescription = (filter) => {
    const column = columns.find(col => col.id === filter.column);
    const operator = filterOperators[filter.type]?.find(op => op.value === filter.operator);
    
    return `${column?.name || filter.column} ${operator?.label || filter.operator} "${filter.value}"`;
  };

  /**
   * Tarih seçici açma
   * @param {string} mode - Tarih seçici modu
   */
  const openDatePicker = (mode = 'date') => {
    setDatePickerMode(mode);
    setShowDatePicker(true);
  };

  /**
   * Tarih seçimi
   * @param {Object} event - Tarih seçimi eventi
   * @param {Date} selectedDate - Seçilen tarih
   */
  const handleDateChange = (event, selectedDate) => {
    setShowDatePicker(false);
    if (selectedDate) {
      setNewFilter({
        ...newFilter,
        value: selectedDate.toISOString().split('T')[0],
      });
    }
  };

  /**
   * Filtre öğesi render fonksiyonu
   * @param {Object} param0 - Render parametreleri
   * @returns {JSX.Element} Filtre öğesi
   */
  const renderFilterItem = ({ item, index }) => (
    <View style={styles.filterItem}>
      <View style={styles.filterInfo}>
        <Text style={styles.filterText}>{getFilterDescription(item)}</Text>
      </View>
      <TouchableOpacity
        style={styles.removeButton}
        onPress={() => handleRemoveFilter(index)}
      >
        <Ionicons name="close" size={20} color={theme.colors.error} />
      </TouchableOpacity>
    </View>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Filtre Yönetimi</Text>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Ionicons name="close" size={24} color={theme.colors.text} />
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Aktif Filtreler ({filters.length})</Text>
          {filters.length > 0 ? (
            <>
              <FlatList
                data={filters}
                renderItem={renderFilterItem}
                keyExtractor={(item, index) => `${item.id || index}`}
                showsVerticalScrollIndicator={false}
              />
              <TouchableOpacity
                style={styles.clearButton}
                onPress={handleClearAll}
              >
                <Text style={styles.clearButtonText}>Tüm Filtreleri Temizle</Text>
              </TouchableOpacity>
            </>
          ) : (
            <Text style={styles.filterDescription}>Henüz filtre eklenmemiş</Text>
          )}
        </View>

        <View style={styles.section}>
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => setShowAddModal(true)}
          >
            <Text style={styles.addButtonText}>+ Yeni Filtre Ekle</Text>
          </TouchableOpacity>
        </View>

        {/* Yeni Filtre Ekleme Modal */}
        <Modal
          visible={showAddModal}
          transparent={true}
          animationType="fade"
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <Text style={styles.modalTitle}>Yeni Filtre Ekle</Text>
              
              <View style={styles.formGroup}>
                <Text style={styles.label}>Sütun</Text>
                <TouchableOpacity style={styles.picker}>
                  <Text style={styles.pickerText}>
                    {columns.find(col => col.id === newFilter.column)?.name || 'Sütun seçin...'}
                  </Text>
                </TouchableOpacity>
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.label}>Koşul</Text>
                <View style={styles.operatorContainer}>
                  {filterOperators[newFilter.type]?.map((operator) => (
                    <TouchableOpacity
                      key={operator.value}
                      style={[
                        styles.operatorChip,
                        newFilter.operator === operator.value && styles.operatorChipActive
                      ]}
                      onPress={() => setNewFilter({ ...newFilter, operator: operator.value })}
                    >
                      <Text style={[
                        styles.operatorChipText,
                        newFilter.operator === operator.value && styles.operatorChipTextActive
                      ]}>
                        {operator.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.label}>Değer</Text>
                {newFilter.type === 'date' ? (
                  <TouchableOpacity
                    style={styles.picker}
                    onPress={() => openDatePicker()}
                  >
                    <Text style={styles.pickerText}>
                      {newFilter.value || 'Tarih seçin...'}
                    </Text>
                  </TouchableOpacity>
                ) : (
                  <TextInput
                    style={styles.input}
                    value={newFilter.value}
                    onChangeText={(text) => setNewFilter({ ...newFilter, value: text })}
                    placeholder="Değer girin..."
                    placeholderTextColor={theme.colors.textSecondary}
                    keyboardType={newFilter.type === 'number' ? 'numeric' : 'default'}
                  />
                )}
              </View>

              <View style={styles.modalActions}>
                <TouchableOpacity
                  style={[styles.modalButton, styles.modalButtonCancel]}
                  onPress={() => {
                    setShowAddModal(false);
                    setNewFilter({ column: '', operator: 'contains', value: '', type: 'text' });
                  }}
                >
                  <Text style={[styles.modalButtonText, styles.modalButtonTextCancel]}>İptal</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.modalButton, styles.modalButtonConfirm]}
                  onPress={handleAddFilter}
                >
                  <Text style={[styles.modalButtonText, styles.modalButtonTextConfirm]}>Ekle</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>

        {/* Tarih Seçici */}
        {showDatePicker && (
          <DateTimePicker
            value={new Date()}
            mode={datePickerMode}
            display="default"
            onChange={handleDateChange}
          />
        )}
      </View>
    </Modal>
  );
};

export default FilterManager;
