import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
  Alert
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import * as workService from '../services/workService';
import { formatCurrency } from '../utils/formatters';

/**
 * Vardiya Ödemeleri Ekranı
 *
 * Bu ekran, vardiya ödemelerini yönetmeyi sağlar:
 * - Ödemeleri listeleme
 * - Ödeme durumunu değiştirme
 * - Ödemeleri filtreleme (tümü, ödenenler, ödenmeyenler)
 * - Ödeme detaylarını görüntüleme
 * - Ödemeleri silme
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @returns {JSX.Element} Vardiya ödemeleri ekranı
 */
export default function WorkPaymentsScreen({ navigation }) {
  const db = useSQLiteContext();
  const insets = useSafeAreaInsets();

  // Durum değişkenleri
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [payments, setPayments] = useState([]);
  const [filter, setFilter] = useState('all'); // all, paid, unpaid

  // Verileri yükle
  const loadData = useCallback(async () => {
    try {
      setLoading(true);

      // Ödemeleri getir
      const options = {};

      if (filter === 'paid') {
        options.isPaid = true;
      } else if (filter === 'unpaid') {
        options.isPaid = false;
      }

      const paymentsData = await workService.getWorkPayments(db, options);
      setPayments(paymentsData);

      setLoading(false);
    } catch (error) {
      console.error('Ödemeler yükleme hatası:', error);
      setLoading(false);
    }
  }, [db, filter]);

  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [loadData])
  );

  // Yenile
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  }, [loadData]);

  // Ödeme detaylarını görüntüle
  const viewPaymentDetails = (paymentId) => {
    navigation.navigate('WorkPaymentDetail', { paymentId });
  };

  // Ödeme durumunu güncelle
  const togglePaymentStatus = async (payment) => {
    try {
      const updatedPayment = {
        ...payment,
        is_paid: payment.is_paid === 1 ? 0 : 1,
        payment_date: payment.is_paid === 0 ? new Date().toISOString().split('T')[0] : null
      };

      await workService.updateWorkPayment(db, payment.id, updatedPayment);
      await loadData();
    } catch (error) {
      console.error('Ödeme durumu güncelleme hatası:', error);
      Alert.alert('Hata', 'Ödeme durumu güncellenirken bir hata oluştu.');
    }
  };

  // Ödemeyi sil
  const deletePayment = async (paymentId) => {
    Alert.alert(
      'Ödemeyi Sil',
      'Bu ödemeyi silmek istediğinize emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await workService.deleteWorkPayment(db, paymentId);
              await loadData();
              Alert.alert('Başarılı', 'Ödeme başarıyla silindi.');
            } catch (error) {
              console.error('Ödeme silme hatası:', error);
              Alert.alert('Hata', 'Ödeme silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };

  // Tarih aralığını formatla
  const formatDateRange = (startDate, endDate) => {
    const start = new Date(startDate);
    const end = new Date(endDate);

    const startMonth = start.toLocaleDateString('tr-TR', { month: 'long' });
    const endMonth = end.toLocaleDateString('tr-TR', { month: 'long' });

    return startMonth === endMonth
      ? `${startMonth} ${start.getFullYear()}`
      : `${startMonth} - ${endMonth} ${start.getFullYear()}`;
  };

  /**
   * Ödeme öğesini render eder
   *
   * @param {Object} params - Render parametreleri
   * @param {Object} params.item - Ödeme nesnesi
   * @returns {JSX.Element} Ödeme öğesi
   */
  const renderPaymentItem = ({ item }) => {
    // Ödeme durumuna göre renk ve metin belirle
    const statusColor = item.is_paid === 1 ? Colors.SUCCESS : Colors.WARNING;
    const statusText = item.is_paid === 1 ? 'Ödendi' : 'Ödenmedi';
    const actionText = item.is_paid === 1 ? 'Ödenmedi Yap' : 'Ödendi Yap';
    const actionIcon = item.is_paid === 1 ? 'money-off' : 'payments';
    const actionColor = item.is_paid === 1 ? Colors.WARNING : Colors.SUCCESS;

    return (
      <TouchableOpacity
        style={styles.paymentItem}
        onPress={() => viewPaymentDetails(item.id)}
      >
        {/* Ödeme Başlığı */}
        <View style={styles.paymentHeader}>
          <View style={styles.periodContainer}>
            <MaterialIcons name="date-range" size={20} color="#666" />
            <Text style={styles.paymentPeriod}>
              {formatDateRange(item.period_start_date, item.period_end_date)}
            </Text>
          </View>

          <View style={[styles.paymentStatus, { backgroundColor: statusColor }]}>
            <Text style={styles.paymentStatusText}>{statusText}</Text>
          </View>
        </View>

        {/* Ödeme Detayları */}
        <View style={styles.paymentDetails}>
          <View style={styles.paymentDetail}>
            <View style={styles.paymentDetailLabelContainer}>
              <MaterialIcons name="access-time" size={18} color="#666" />
              <Text style={styles.paymentDetailLabel}>Normal Çalışma</Text>
            </View>
            <Text style={styles.paymentDetailValue}>
              {item.regular_hours.toFixed(2)} saat
            </Text>
          </View>

          <View style={styles.paymentDetail}>
            <View style={styles.paymentDetailLabelContainer}>
              <MaterialIcons name="timer" size={18} color="#666" />
              <Text style={styles.paymentDetailLabel}>Fazla Mesai</Text>
            </View>
            <Text style={styles.paymentDetailValue}>
              {item.overtime_hours.toFixed(2)} saat
            </Text>
          </View>

          <View style={[styles.paymentDetail, styles.totalRow]}>
            <View style={styles.paymentDetailLabelContainer}>
              <MaterialIcons name="account-balance-wallet" size={18} color="#666" />
              <Text style={styles.paymentDetailLabel}>Toplam Kazanç</Text>
            </View>
            <Text style={styles.totalValue}>
              {formatCurrency(item.total_amount, 'TRY')}
            </Text>
          </View>
        </View>

        {/* Ödeme Tarihi */}
        {item.payment_date && (
          <View style={styles.paymentDateContainer}>
            <MaterialIcons name="event" size={16} color="#666" />
            <Text style={styles.paymentDate}>
              Ödeme Tarihi: {new Date(item.payment_date).toLocaleDateString('tr-TR')}
            </Text>
          </View>
        )}

        {/* Ödeme İşlemleri */}
        <View style={styles.paymentActions}>
          <TouchableOpacity
            style={[styles.paymentActionButton, { backgroundColor: actionColor }]}
            onPress={() => togglePaymentStatus(item)}
          >
            <MaterialIcons name={actionIcon} size={16} color="#fff" />
            <Text style={styles.paymentActionButtonText}>{actionText}</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.paymentActionButton, styles.deleteButton]}
            onPress={() => deletePayment(item.id)}
          >
            <MaterialIcons name="delete" size={16} color="#fff" />
            <Text style={styles.paymentActionButtonText}>Sil</Text>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Vardiya Ödemeleri</Text>
        <View style={{ width: 40 }} />
      </View>

      {/* Filtreler */}
      <View style={styles.filtersContainer}>
        <TouchableOpacity
          style={[
            styles.filterButton,
            filter === 'all' && styles.activeFilterButton
          ]}
          onPress={() => setFilter('all')}
        >
          <MaterialIcons
            name="list"
            size={16}
            color={filter === 'all' ? '#fff' : '#666'}
            style={styles.filterIcon}
          />
          <Text style={[
            styles.filterButtonText,
            filter === 'all' && styles.activeFilterButtonText
          ]}>
            Tümü
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.filterButton,
            filter === 'paid' && styles.activeFilterButton
          ]}
          onPress={() => setFilter('paid')}
        >
          <MaterialIcons
            name="check-circle"
            size={16}
            color={filter === 'paid' ? '#fff' : '#666'}
            style={styles.filterIcon}
          />
          <Text style={[
            styles.filterButtonText,
            filter === 'paid' && styles.activeFilterButtonText
          ]}>
            Ödenenler
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.filterButton,
            filter === 'unpaid' && styles.activeFilterButton
          ]}
          onPress={() => setFilter('unpaid')}
        >
          <MaterialIcons
            name="schedule"
            size={16}
            color={filter === 'unpaid' ? '#fff' : '#666'}
            style={styles.filterIcon}
          />
          <Text style={[
            styles.filterButtonText,
            filter === 'unpaid' && styles.activeFilterButtonText
          ]}>
            Ödenmeyenler
          </Text>
        </TouchableOpacity>
      </View>

      {loading && !refreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.PRIMARY} />
          <Text style={styles.loadingText}>Ödemeler yükleniyor...</Text>
        </View>
      ) : (
        <FlatList
          data={payments}
          renderItem={renderPaymentItem}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[Colors.PRIMARY]}
              tintColor={Colors.PRIMARY}
            />
          }
          ListEmptyComponent={
            <View style={styles.emptyState}>
              <MaterialIcons name="money-off" size={64} color={Colors.GRAY_400} />
              <Text style={styles.emptyStateText}>
                {filter === 'all' ? 'Henüz ödeme bulunmuyor' :
                 filter === 'paid' ? 'Henüz ödenmiş ödeme bulunmuyor' :
                 'Henüz ödenmemiş ödeme bulunmuyor'}
              </Text>
              <Text style={styles.emptyStateHint}>
                {filter === 'all' ?
                  'Vardiya ödemeleri, vardiya takibi ekranından oluşturulabilir.' :
                  'Farklı bir filtre seçerek diğer ödemeleri görüntüleyebilirsiniz.'}
              </Text>
              <TouchableOpacity
                style={styles.emptyStateButton}
                onPress={() => navigation.navigate('WorkScreen')}
              >
                <MaterialIcons name="work" size={20} color="#fff" />
                <Text style={styles.emptyStateButtonText}>Vardiya Takibine Git</Text>
              </TouchableOpacity>
            </View>
          }
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: Colors.PRIMARY,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  headerTitle: {
    flex: 1,
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  filtersContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  filterButton: {
    flexDirection: 'row',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 25,
    backgroundColor: '#f0f0f0',
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    flex: 1,
    marginHorizontal: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  activeFilterButton: {
    backgroundColor: Colors.PRIMARY,
  },
  filterIcon: {
    marginRight: 6,
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#666',
  },
  activeFilterButtonText: {
    color: '#fff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  listContent: {
    padding: 16,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
    backgroundColor: '#fff',
    borderRadius: 12,
    margin: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    marginBottom: 12,
    textAlign: 'center',
  },
  emptyStateHint: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 20,
  },
  emptyStateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  emptyStateButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14,
    marginLeft: 8,
  },
  paymentItem: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  paymentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    paddingBottom: 12,
  },
  periodContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  paymentPeriod: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 8,
  },
  paymentStatus: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 20,
  },
  paymentStatusText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#fff',
  },
  paymentDetails: {
    marginBottom: 16,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 12,
  },
  paymentDetail: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  paymentDetailLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  paymentDetailLabel: {
    fontSize: 14,
    color: '#333',
    marginLeft: 8,
  },
  paymentDetailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  totalRow: {
    borderBottomWidth: 0,
    marginTop: 4,
    paddingTop: 8,
  },
  totalValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.INCOME,
  },
  paymentDateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  paymentDate: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  paymentActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  paymentActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.SUCCESS,
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    flex: 1,
    marginHorizontal: 4,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  deleteButton: {
    backgroundColor: Colors.DANGER,
  },
  paymentActionButtonText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#fff',
    marginLeft: 6,
  },
});
