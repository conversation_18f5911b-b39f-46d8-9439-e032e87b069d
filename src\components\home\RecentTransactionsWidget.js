import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';
import { useAppContext } from '../../context/AppContext';

/**
 * Son işlemler widget'ı - Son 5 işlemi liste halinde gösterir
 * Modern kart tasarımı ile son işlemleri görü<PERSON>ü<PERSON>
 *
 * @param {Object} props - Bileşen props'ları
 * @param {Array} props.recentTransactions - <PERSON> işlem<PERSON> listesi
 * @param {Function} props.onPress - Widget'a tıklandığında çalışacak fonksiyon
 * @param {Function} props.onTransactionPress - İşleme tıklandığında çalışacak fonksiyon
 * @param {Function} props.onViewAllPress - Tümünü gör'e tıklandığında çalışacak fonksiyon
 * @returns {JSX.Element} RecentTransactionsWidget bileşeni
 */
const RecentTransactionsWidget = ({
  recentTransactions = [],
  onPress,
  onTransactionPress,
  onViewAllPress,
}) => {
  const { theme } = useTheme();
  const { defaultCurrency } = useAppContext();

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: defaultCurrency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
      return 'Bugün';
    } else if (diffDays === 2) {
      return 'Dün';
    } else if (diffDays <= 7) {
      return `${diffDays - 1} gün önce`;
    } else {
      return date.toLocaleDateString('tr-TR', {
        day: 'numeric',
        month: 'short',
      });
    }
  };

  const getTransactionIcon = (transaction) => {
    if (transaction.category_icon) {
      return transaction.category_icon;
    }
    return transaction.type === 'income' ? 'add-circle' : 'remove-circle';
  };

  const getTransactionColor = (transaction) => {
    if (transaction.category_color) {
      return transaction.category_color;
    }
    return transaction.type === 'income' ? theme.SUCCESS : theme.DANGER;
  };

  const renderTransactionItem = ({ item }) => (
    <TouchableOpacity
      style={[styles.transactionItem, { borderBottomColor: theme.BORDER }]}
      onPress={() => onTransactionPress?.(item)}
      activeOpacity={0.7}
    >
      <View style={styles.transactionLeft}>
        <View style={[
          styles.transactionIcon,
          { backgroundColor: getTransactionColor(item) + '15' }
        ]}>
          <MaterialIcons
            name={getTransactionIcon(item)}
            size={20}
            color={getTransactionColor(item)}
          />
        </View>
        <View style={styles.transactionInfo}>
          <Text style={[styles.transactionTitle, { color: theme.TEXT_PRIMARY }]}>
            {item.category_name || (item.type === 'income' ? 'Gelir' : 'Gider')}
          </Text>
          <Text style={[styles.transactionDate, { color: theme.TEXT_SECONDARY }]}>
            {formatDate(item.date)}
          </Text>
        </View>
      </View>
      <View style={styles.transactionRight}>
        <Text style={[
          styles.transactionAmount,
          { color: item.type === 'income' ? theme.SUCCESS : theme.DANGER }
        ]}>
          {item.type === 'income' ? '+' : '-'}{formatCurrency(Math.abs(item.amount))}
        </Text>
        {item.description && (
          <Text style={[styles.transactionDescription, { color: theme.TEXT_SECONDARY }]}>
            {item.description.length > 20 ? item.description.substring(0, 20) + '...' : item.description}
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <MaterialIcons name="receipt-long" size={48} color={theme.TEXT_SECONDARY} />
      <Text style={[styles.emptyTitle, { color: theme.TEXT_SECONDARY }]}>
        Henüz işlem yok
      </Text>
      <Text style={[styles.emptySubtitle, { color: theme.TEXT_SECONDARY }]}>
        İlk işleminizi ekleyerek başlayın
      </Text>
    </View>
  );

  return (
    <TouchableOpacity
      style={[styles.container, { backgroundColor: theme.CARD }]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <View style={[styles.header, { borderBottomColor: theme.BORDER }]}>
        <View style={styles.headerLeft}>
          <MaterialIcons name="receipt-long" size={24} color={theme.PRIMARY} />
          <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
            Son İşlemler
          </Text>
        </View>
        <TouchableOpacity
          style={styles.viewAllButton}
          onPress={onViewAllPress}
          activeOpacity={0.7}
        >
          <Text style={[styles.viewAllText, { color: theme.PRIMARY }]}>
            Tümünü Gör
          </Text>
          <MaterialIcons name="chevron-right" size={16} color={theme.PRIMARY} />
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        {recentTransactions.length > 0 ? (
          <FlatList
            data={recentTransactions.slice(0, 5)}
            renderItem={renderTransactionItem}
            keyExtractor={(item) => item.id?.toString() || Math.random().toString()}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
          />
        ) : (
          renderEmptyState()
        )}
      </View>

      {recentTransactions.length > 0 && (
        <View style={styles.footer}>
          <Text style={[styles.footerText, { color: theme.TEXT_SECONDARY }]}>
            Son {Math.min(recentTransactions.length, 5)} işlem gösteriliyor
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '500',
    marginRight: 4,
  },
  content: {
    minHeight: 120,
  },
  transactionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  transactionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  transactionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 2,
  },
  transactionDate: {
    fontSize: 12,
  },
  transactionRight: {
    alignItems: 'flex-end',
  },
  transactionAmount: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  transactionDescription: {
    fontSize: 11,
    fontStyle: 'italic',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 32,
  },
  emptyTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginTop: 12,
    marginBottom: 4,
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: 'center',
  },
  footer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 12,
    fontStyle: 'italic',
  },
});

export default RecentTransactionsWidget;
