import * as LocalAuthentication from 'expo-local-authentication';
import AsyncStorage from '@react-native-async-storage/async-storage';

// AsyncStorage anahtarları
const AUTH_ENABLED_KEY = 'auth_enabled';
const AUTH_TYPE_KEY = 'auth_type';

/**
 * Biyometrik kimlik doğrulama servisi
 */
export const authService = {
  /**
   * Cihazın biyometrik kimlik doğrulamayı destekleyip desteklemediğini kontrol eder
   *
   * @returns {Promise<boolean>} Cihaz biyometrik kimlik doğrulamayı destekliyorsa true, aksi halde false
   */
  isBiometricAvailable: async () => {
    try {
      const isHardwareAvailable = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();

      return isHardwareAvailable && isEnrolled;
    } catch (error) {
      console.error('Biyometrik doğrulama kontrolü hatası:', error);
      return false;
    }
  },

  /**
   * Desteklenen biyometrik kimlik doğrulama türlerini getirir
   *
   * @returns {Promise<Array>} Desteklenen biyometrik kimlik doğrulama türleri
   */
  getSupportedAuthTypes: async () => {
    try {
      const types = await LocalAuthentication.supportedAuthenticationTypesAsync();
      return types.map(type => {
        switch (type) {
          case LocalAuthentication.AuthenticationType.FINGERPRINT:
            return { id: type, name: 'Parmak İzi' };
          case LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION:
            return { id: type, name: 'Yüz Tanıma' };
          case LocalAuthentication.AuthenticationType.IRIS:
            return { id: type, name: 'İris Tarama' };
          default:
            return { id: type, name: 'Diğer' };
        }
      });
    } catch (error) {
      console.error('Kimlik doğrulama türleri hatası:', error);
      return [];
    }
  },

  /**
   * Biyometrik kimlik doğrulama işlemini başlatır
   *
   * @param {string} promptMessage - Kullanıcıya gösterilecek mesaj
   * @returns {Promise<boolean>} Kimlik doğrulama başarılıysa true, aksi halde false
   */
  authenticate: async (promptMessage = 'Lütfen kimliğinizi doğrulayın') => {
    try {
      const result = await LocalAuthentication.authenticateAsync({
        promptMessage,
        fallbackLabel: 'Şifre kullan',
        cancelLabel: 'İptal',
        disableDeviceFallback: false,
      });

      return result.success;
    } catch (error) {
      console.error('Kimlik doğrulama hatası:', error);
      return false;
    }
  },

  /**
   * Biyometrik kimlik doğrulamanın etkin olup olmadığını kontrol eder
   *
   * @returns {Promise<boolean>} Biyometrik kimlik doğrulama etkinse true, aksi halde false
   */
  isAuthEnabled: async () => {
    try {
      const enabled = await AsyncStorage.getItem(AUTH_ENABLED_KEY);
      return enabled === 'true';
    } catch (error) {
      console.error('Kimlik doğrulama durumu kontrolü hatası:', error);
      return false;
    }
  },

  /**
   * Biyometrik kimlik doğrulamanın etkin olup olmadığını kontrol eder
   * (isAuthEnabled ile aynı işlevi görür, uyumluluk için eklenmiştir)
   *
   * @returns {Promise<boolean>} Biyometrik kimlik doğrulama etkinse true, aksi halde false
   */
  isBiometricEnabled: async () => {
    try {
      const enabled = await AsyncStorage.getItem(AUTH_ENABLED_KEY);
      return enabled === 'true';
    } catch (error) {
      console.error('Biyometrik etkinlik kontrolü hatası:', error);
      return false;
    }
  },

  /**
   * Biyometrik kimlik doğrulamayı etkinleştirir veya devre dışı bırakır
   *
   * @param {boolean} enabled - Etkinleştirmek için true, devre dışı bırakmak için false
   * @returns {Promise<void>}
   */
  setAuthEnabled: async (enabled) => {
    try {
      await AsyncStorage.setItem(AUTH_ENABLED_KEY, enabled ? 'true' : 'false');
    } catch (error) {
      console.error('Kimlik doğrulama durumu ayarlama hatası:', error);
    }
  },

  /**
   * Biyometrik kimlik doğrulamayı etkinleştirir
   *
   * @returns {Promise<boolean>} İşlem başarılı mı
   */
  enableBiometric: async () => {
    try {
      await AsyncStorage.setItem(AUTH_ENABLED_KEY, 'true');
      return true;
    } catch (error) {
      console.error('Biyometrik etkinleştirme hatası:', error);
      return false;
    }
  },

  /**
   * Biyometrik kimlik doğrulamayı devre dışı bırakır
   *
   * @returns {Promise<boolean>} İşlem başarılı mı
   */
  disableBiometric: async () => {
    try {
      await AsyncStorage.setItem(AUTH_ENABLED_KEY, 'false');
      return true;
    } catch (error) {
      console.error('Biyometrik devre dışı bırakma hatası:', error);
      return false;
    }
  },

  /**
   * Kullanılan kimlik doğrulama türünü getirir
   *
   * @returns {Promise<string>} Kimlik doğrulama türü
   */
  getAuthType: async () => {
    try {
      return await AsyncStorage.getItem(AUTH_TYPE_KEY) || 'biometric';
    } catch (error) {
      console.error('Kimlik doğrulama türü getirme hatası:', error);
      return 'biometric';
    }
  },

  /**
   * Kimlik doğrulama türünü ayarlar
   *
   * @param {string} type - Kimlik doğrulama türü ('biometric' veya 'pin')
   * @returns {Promise<void>}
   */
  setAuthType: async (type) => {
    try {
      await AsyncStorage.setItem(AUTH_TYPE_KEY, type);
    } catch (error) {
      console.error('Kimlik doğrulama türü ayarlama hatası:', error);
    }
  }
};