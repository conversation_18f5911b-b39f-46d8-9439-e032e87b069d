import React, { useState } from 'react';
import { StyleSheet, View, TouchableOpacity, Text, Modal, FlatList, Pressable } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';

/**
 * Modern custom select component
 */
const Select = ({ items = [], selectedValue, onValueChange, containerStyle }) => {
  const [visible, setVisible] = useState(false);
  const selectedItem = items.find(item => item.value === selectedValue);

  return (
    <View style={[styles.container, containerStyle]}>
      <TouchableOpacity
        style={styles.selectButton}
        onPress={() => setVisible(true)}
      >
        <Text style={[
          styles.selectedText,
          !selectedValue && styles.placeholderText
        ]}>
          {selectedItem?.label || 'Seçiniz...'}
        </Text>
        <MaterialIcons name="arrow-drop-down" size={24} color="#3498db" />
      </TouchableOpacity>

      <Modal
        visible={visible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setVisible(false)}
      >
        <Pressable 
          style={styles.modalOverlay}
          onPress={() => setVisible(false)}
        >
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Seçiniz</Text>
              <TouchableOpacity 
                onPress={() => setVisible(false)}
                style={styles.closeButton}
              >
                <MaterialIcons name="close" size={24} color="#95a5a6" />
              </TouchableOpacity>
            </View>
            <FlatList
              data={items}
              keyExtractor={(item) => item.value}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    styles.option,
                    item.value === selectedValue && styles.selectedOption
                  ]}
                  onPress={() => {
                    onValueChange(item.value);
                    setVisible(false);
                  }}
                >
                  {item.icon && (
                    <MaterialIcons 
                      name={item.icon} 
                      size={20} 
                      color={item.value === selectedValue ? '#3498db' : '#7f8c8d'} 
                      style={styles.optionIcon}
                    />
                  )}
                  <Text style={[
                    styles.optionText,
                    item.value === selectedValue && styles.selectedOptionText
                  ]}>
                    {item.label}
                  </Text>
                  {item.value === selectedValue && (
                    <MaterialIcons name="check" size={20} color="#3498db" />
                  )}
                </TouchableOpacity>
              )}
              style={styles.optionsList}
            />
          </View>
        </Pressable>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  selectButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: 46,
    paddingHorizontal: 8,
  },
  selectedText: {
    fontSize: 16,
    color: '#2c3e50',
    flex: 1,
  },
  placeholderText: {
    color: '#95a5a6',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 12,
    width: '100%',
    maxHeight: '80%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2c3e50',
  },
  closeButton: {
    padding: 4,
  },
  optionsList: {
    maxHeight: 300,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  selectedOption: {
    backgroundColor: '#f8f9fa',
  },
  optionIcon: {
    marginRight: 12,
  },
  optionText: {
    fontSize: 16,
    color: '#2c3e50',
    flex: 1,
  },
  selectedOptionText: {
    color: '#3498db',
    fontWeight: '500',
  },
});

export default Select;
