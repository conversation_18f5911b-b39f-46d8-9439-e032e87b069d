/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> bütçe yönetimi için veritabanı migrasyonu
 *
 * @param {object} db - SQLite veritabanı nesnesi
 * @returns {Promise<void>}
 */
export const migrateBudgetsEnhanced = async (db) => {
  try {
    console.log('Gelişmiş bütçe yönetimi migrasyonu başlatılıyor...');

    // Bütçe tablosunu kontrol et
    const hasBudgetsTable = await db.getFirstAsync(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='budgets'
    `);

    if (!hasBudgetsTable) {
      console.log('budgets tablosu oluşturuluyor...');

      // Bütçe tablosunu oluştur
      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS budgets (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          description TEXT,
          amount REAL NOT NULL,
          start_date TEXT NOT NULL,
          end_date TEXT NOT NULL,
          category_id INTEGER,
          is_active INTEGER DEFAULT 1,
          repeat_type TEXT DEFAULT 'monthly',
          currency TEXT DEFAULT 'TRY',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
        )
      `);

      console.log('budgets tablosu başarıyla oluşturuldu.');
    } else {
      console.log('budgets tablosu zaten mevcut.');

      // Yeni alanları kontrol et ve ekle
      const columns = await db.getAllAsync(`PRAGMA table_info(budgets)`);

      // repeat_type alanını kontrol et
      if (!columns.some(col => col.name === 'repeat_type')) {
        try {
          await db.execAsync(`ALTER TABLE budgets ADD COLUMN repeat_type TEXT DEFAULT 'monthly'`);
          console.log('budgets tablosuna repeat_type alanı eklendi.');
        } catch (error) {
          console.warn('repeat_type alanı eklenirken hata:', error.message);
        }
      }

      // currency alanını kontrol et
      if (!columns.some(col => col.name === 'currency')) {
        try {
          await db.execAsync(`ALTER TABLE budgets ADD COLUMN currency TEXT DEFAULT 'TRY'`);
          console.log('budgets tablosuna currency alanı eklendi.');
        } catch (error) {
          console.warn('currency alanı eklenirken hata:', error.message);
        }
      }
    }

    // Bütçe kategorileri tablosunu kontrol et
    const hasBudgetCategoriesTable = await db.getFirstAsync(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='budget_categories'
    `);

    if (!hasBudgetCategoriesTable) {
      console.log('budget_categories tablosu oluşturuluyor...');

      // Bütçe kategorileri tablosunu oluştur
      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS budget_categories (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          budget_id INTEGER NOT NULL,
          category_id INTEGER NOT NULL,
          amount REAL NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (budget_id) REFERENCES budgets(id) ON DELETE CASCADE,
          FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
          UNIQUE(budget_id, category_id)
        )
      `);

      console.log('budget_categories tablosu başarıyla oluşturuldu.');
    } else {
      console.log('budget_categories tablosu zaten mevcut.');
    }

    // Bütçe hedefleri tablosunu kontrol et
    const hasBudgetGoalsTable = await db.getFirstAsync(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='budget_goals'
    `);

    if (!hasBudgetGoalsTable) {
      console.log('budget_goals tablosu oluşturuluyor...');

      // Bütçe hedefleri tablosunu oluştur
      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS budget_goals (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          description TEXT,
          target_amount REAL NOT NULL,
          current_amount REAL DEFAULT 0,
          start_date TEXT NOT NULL,
          target_date TEXT NOT NULL,
          category_id INTEGER,
          is_completed INTEGER DEFAULT 0,
          currency TEXT DEFAULT 'TRY',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
        )
      `);

      console.log('budget_goals tablosu başarıyla oluşturuldu.');
    } else {
      console.log('budget_goals tablosu zaten mevcut.');
    }

    // Bütçe uyarıları tablosunu kontrol et
    const hasBudgetAlertsTable = await db.getFirstAsync(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='budget_alerts'
    `);

    if (!hasBudgetAlertsTable) {
      console.log('budget_alerts tablosu oluşturuluyor...');

      // Bütçe uyarıları tablosunu oluştur
      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS budget_alerts (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          budget_id INTEGER NOT NULL,
          threshold_percent INTEGER NOT NULL,
          is_enabled INTEGER DEFAULT 1,
          is_triggered INTEGER DEFAULT 0,
          last_triggered_at DATETIME,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (budget_id) REFERENCES budgets(id) ON DELETE CASCADE
        )
      `);

      console.log('budget_alerts tablosu başarıyla oluşturuldu.');
    } else {
      console.log('budget_alerts tablosu zaten mevcut.');
    }

    // Bütçe raporları tablosunu kontrol et
    const hasBudgetReportsTable = await db.getFirstAsync(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='budget_reports'
    `);

    if (!hasBudgetReportsTable) {
      console.log('budget_reports tablosu oluşturuluyor...');

      // Bütçe raporları tablosunu oluştur
      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS budget_reports (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          budget_id INTEGER NOT NULL,
          report_date TEXT NOT NULL,
          planned_amount REAL NOT NULL,
          actual_amount REAL NOT NULL,
          variance_amount REAL NOT NULL,
          variance_percent REAL NOT NULL,
          notes TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (budget_id) REFERENCES budgets(id) ON DELETE CASCADE
        )
      `);

      console.log('budget_reports tablosu başarıyla oluşturuldu.');
    } else {
      console.log('budget_reports tablosu zaten mevcut.');
    }

    console.log('Gelişmiş bütçe yönetimi migrasyonu tamamlandı.');
  } catch (error) {
    console.error('Gelişmiş bütçe yönetimi migrasyon hatası:', error);
    throw error;
  }
};
