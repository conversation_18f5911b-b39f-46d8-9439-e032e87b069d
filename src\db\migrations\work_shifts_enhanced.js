import { executeSql } from '../../utils/dbUtils';

/**
 * Transaction durumunu kontrol eder
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<boolean>} Transaction aktif mi
 */
const checkTransactionStatus = async (db) => {
  try {
    // Transaction başlatmayı dene
    await db.execAsync('BEGIN TRANSACTION');
    // Eğer başarılıysa, hemen geri al ve false döndür
    await db.execAsync('ROLLBACK');
    return false; // Transaction içinde değiliz
  } catch (error) {
    if (error.message?.includes('cannot start a transaction within a transaction')) {
      return true; // Transaction içindeyiz
    }
    // Diğer hatalar için false döndür
    return false;
  }
};

/**
 * Gelişmiş vardiya takibi için veritabanı migrasyonu
 * - work_shift_types tablosunu günceller (hourly_rate sütunu ekler)
 * - work_shift_schedules tablosunu günceller (days_of_week sütunu ekler, name sütunu ekler)
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<void>}
 */
export const migrateWorkShiftsEnhanced = async (db) => {
  try {
    console.log('Gelişmiş vardiya takibi migrasyonu başlatılıyor...');

    // Transaction kullanmadan migration yap
    // Her işlemi ayrı ayrı çalıştır

    // work_shift_types tablosunu güncelle
    const shiftTypesExists = await db.getFirstAsync(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='work_shift_types'
    `);

    if (shiftTypesExists) {
      // Sütunları kontrol et
      const columns = await db.getAllAsync(`PRAGMA table_info(work_shift_types)`);
      const columnNames = columns.map(col => col.name);

      // hourly_rate sütunu ekle
      if (!columnNames.includes('hourly_rate')) {
        await db.execAsync(`ALTER TABLE work_shift_types ADD COLUMN hourly_rate REAL`);

        // Varsayılan saatlik ücretleri güncelle
        const workSettings = await db.getFirstAsync(`SELECT * FROM work_settings LIMIT 1`);
        if (workSettings) {
          const defaultHourlyRate = workSettings.default_hourly_rate || 100;
          await db.execAsync(`UPDATE work_shift_types SET hourly_rate = ?`, [defaultHourlyRate]);
        }
      }
    }

    // work_shift_schedules tablosunu güncelle
    const shiftSchedulesExists = await db.getFirstAsync(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='work_shift_schedules'
    `);

    if (shiftSchedulesExists) {
      // Sütunları kontrol et
      const columns = await db.getAllAsync(`PRAGMA table_info(work_shift_schedules)`);
      const columnNames = columns.map(col => col.name);

      // Önce sütunları kontrol et
      const hasNameColumn = columnNames.includes('name');
      const hasDaysOfWeekColumn = columnNames.includes('days_of_week');

      // name sütunu ekle
      if (!hasNameColumn) {
        await db.execAsync(`ALTER TABLE work_shift_schedules ADD COLUMN name TEXT DEFAULT 'Vardiya Planı'`);
      }

      // days_of_week sütunu ekle
      if (!hasDaysOfWeekColumn) {
        await db.execAsync(`ALTER TABLE work_shift_schedules ADD COLUMN days_of_week TEXT DEFAULT '1,2,3,4,5'`);

        // Eğer repeat_days sütunu varsa, değerleri days_of_week sütununa kopyala
        const hasRepeatDaysColumn = columnNames.includes('repeat_days');
        if (hasRepeatDaysColumn) {
          await db.execAsync(`UPDATE work_shift_schedules SET days_of_week = repeat_days WHERE repeat_days IS NOT NULL`);
        }
      }
    } else {
      // Tablo yoksa oluştur
      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS work_shift_schedules (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL DEFAULT 'Vardiya Planı',
          shift_type_id INTEGER NOT NULL,
          days_of_week TEXT NOT NULL DEFAULT '1,2,3,4,5',
          start_date TEXT NOT NULL,
          end_date TEXT,
          is_active INTEGER DEFAULT 1,
          notes TEXT,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP,
          updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (shift_type_id) REFERENCES work_shift_types (id) ON DELETE CASCADE
        )
      `);
    }

    console.log('Gelişmiş vardiya takibi migrasyonu tamamlandı.');
  } catch (error) {
    console.error('Gelişmiş vardiya takibi migrasyonu hatası:', error);
    throw error;
  }
};
