import {
  DEFAULT_CATEGORIES,
  DEFAULT_INVESTMENT_ASSETS,
  DEFAULT_WORK_SETTINGS,
  ALL_TABLES
} from './schema';

/**
 * Veritabanı şemasını oluşturan ve başlangıç verilerini yükleyen fonksiyon
 *
 * @param {SQLiteDatabase} db - SQLite veritabanı nesnesi
 * @param {boolean} resetDatabase - Veritabanını sıfırlama bayrağı
 * @returns {Promise<void>}
 */
export const initializeDatabase = async (db, resetDatabase = false) => {
  try {
    // WAL modu ve foreign keys'i aktifleştir
    await db.execAsync('PRAGMA journal_mode = WAL');
    await db.execAsync('PRAGMA foreign_keys = ON');

    // Mevcut tabloları temizle (resetDatabase true ise)
    if (resetDatabase) {
      // Tabloları ters sırada sil (foreign key kısıtlamaları nedeniyle)
      await db.execAsync('DROP TABLE IF EXISTS investment_price_history');
      await db.execAsync('DROP TABLE IF EXISTS investment_portfolio');
      await db.execAsync('DROP TABLE IF EXISTS investment_transactions');
      await db.execAsync('DROP TABLE IF EXISTS investment_assets');
      await db.execAsync('DROP TABLE IF EXISTS work_payments');
      await db.execAsync('DROP TABLE IF EXISTS work_shifts');
      await db.execAsync('DROP TABLE IF EXISTS work_settings');
      await db.execAsync('DROP TABLE IF EXISTS transactions');
      await db.execAsync('DROP TABLE IF EXISTS categories');
    }

    // Tüm tabloları oluştur
    for (const tableSchema of ALL_TABLES) {
      await db.execAsync(tableSchema);
    }

    // Kategorileri kontrol et
    const categoriesCount = await db.getFirstAsync('SELECT COUNT(*) as count FROM categories');

    // Eğer kategori yoksa, varsayılan kategorileri ekle
    if (categoriesCount.count === 0) {
      // Varsayılan kategorileri ekle
      for (const category of DEFAULT_CATEGORIES) {
        await db.runAsync(`
          INSERT INTO categories (name, type, icon, color, is_default)
          VALUES (?, ?, ?, ?, ?)
        `, [category.name, category.type, category.icon, category.color, category.is_default]);
      }
    }

    // Yatırım varlıklarını kontrol et
    const assetsCount = await db.getFirstAsync('SELECT COUNT(*) as count FROM investment_assets');

    // Eğer yatırım varlığı yoksa, varsayılan varlıkları ekle
    if (assetsCount.count === 0) {
      // Varsayılan yatırım varlıklarını ekle
      for (const asset of DEFAULT_INVESTMENT_ASSETS) {
        await db.runAsync(`
          INSERT INTO investment_assets (name, symbol, type, icon, color)
          VALUES (?, ?, ?, ?, ?)
        `, [asset.name, asset.symbol, asset.type, asset.icon, asset.color]);
      }
    }

    // Mesai ayarlarını kontrol et
    const workSettingsCount = await db.getFirstAsync('SELECT COUNT(*) as count FROM work_settings');

    // Eğer mesai ayarı yoksa, varsayılan ayarları ekle
    if (workSettingsCount.count === 0) {
      // Varsayılan mesai ayarlarını ekle
      await db.runAsync(`
        INSERT INTO work_settings (
          hourly_rate, overtime_rate, weekly_work_hours,
          daily_work_hours, currency, work_days
        )
        VALUES (?, ?, ?, ?, ?, ?)
      `, [
        DEFAULT_WORK_SETTINGS.hourly_rate,
        DEFAULT_WORK_SETTINGS.overtime_rate,
        DEFAULT_WORK_SETTINGS.weekly_work_hours,
        DEFAULT_WORK_SETTINGS.daily_work_hours,
        DEFAULT_WORK_SETTINGS.currency,
        DEFAULT_WORK_SETTINGS.work_days
      ]);
    }

    // Örnek işlemler ekle (sadece veritabanı boşsa)
    const transactionsCount = await db.getFirstAsync('SELECT COUNT(*) as count FROM transactions');

    if (transactionsCount.count === 0) {
      // Gelir kategorisini bul
      const incomeCategory = await db.getFirstAsync('SELECT id FROM categories WHERE type = ? LIMIT 1', ['income']);

      // Gider kategorisini bul
      const expenseCategory = await db.getFirstAsync('SELECT id FROM categories WHERE type = ? LIMIT 1', ['expense']);

      // Bugünün tarihi
      const today = new Date().toISOString().split('T')[0];

      // Bir hafta önceki tarih
      const oneWeekAgo = new Date();
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
      const oneWeekAgoStr = oneWeekAgo.toISOString().split('T')[0];

      // İki hafta önceki tarih
      const twoWeeksAgo = new Date();
      twoWeeksAgo.setDate(twoWeeksAgo.getDate() - 14);
      const twoWeeksAgoStr = twoWeeksAgo.toISOString().split('T')[0];

      // Örnek işlemler
      const sampleTransactions = [
        { type: 'income', amount: 5000, description: 'Maaş', date: oneWeekAgoStr, category_id: incomeCategory?.id },
        { type: 'expense', amount: 1500, description: 'Kira', date: today, category_id: expenseCategory?.id },
        { type: 'expense', amount: 200, description: 'Market alışverişi', date: today, category_id: expenseCategory?.id },
        { type: 'expense', amount: 100, description: 'Akşam yemeği', date: oneWeekAgoStr, category_id: expenseCategory?.id },
        { type: 'expense', amount: 50, description: 'Ulaşım', date: twoWeeksAgoStr, category_id: expenseCategory?.id },
        { type: 'income', amount: 1000, description: 'Ek gelir', date: twoWeeksAgoStr, category_id: incomeCategory?.id }
      ];

      // İşlemleri ekle
      for (const transaction of sampleTransactions) {
        await db.runAsync(`
          INSERT INTO transactions (type, amount, description, date, category_id)
          VALUES (?, ?, ?, ?, ?)
        `, [transaction.type, transaction.amount, transaction.description, transaction.date, transaction.category_id]);
      }
    }

    return true;
  } catch (error) {
    console.error('Veritabanı başlatma hatası:', error);
    throw error;
  }
};
