import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { formatCurrency } from '../utils/shiftUtils';
import { shiftStyles } from '../styles/shiftStyles';

/**
 * Vardiya Türü Öğesi Bileşeni
 *
 * Bu bileşen, vardiya türleri listesinde her bir vardiya türünü temsil eder.
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.shiftType - Vardiya türü verisi
 * @param {Function} props.onPress - Vardiya türüne tıklandığında çalışacak fonksiyon
 * @param {Function} props.onDelete - Silme butonuna tıklandığında çalışacak fonksiyon
 * @returns {JSX.Element} Vardiya türü öğesi
 */
const ShiftTypeItem = ({ shiftType, onPress, onDelete }) => {
  return (
    <TouchableOpacity
      style={styles.container}
      onPress={() => onPress(shiftType)}
    >
      {/* Vardiya Türü Başlığı */}
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <View style={[styles.colorDot, { backgroundColor: shiftType.color }]} />
          <Text style={styles.title}>{shiftType.name}</Text>
        </View>

        {/* Saatlik Ücret */}
        {shiftType.hourly_rate && (
          <Text style={styles.hourlyRate}>
            {formatCurrency(shiftType.hourly_rate)}
          </Text>
        )}
      </View>

      {/* Vardiya Türü Detayları */}
      <View style={styles.details}>
        {/* Çalışma Saatleri */}
        {(shiftType.start_time && shiftType.end_time) && (
          <View style={styles.infoItem}>
            <MaterialIcons name="schedule" size={16} color="#666" />
            <Text style={styles.infoText}>
              {shiftType.start_time} - {shiftType.end_time}
            </Text>
          </View>
        )}

        {/* Mola Süresi */}
        {shiftType.break_duration > 0 && (
          <View style={styles.infoItem}>
            <MaterialIcons name="free-breakfast" size={16} color="#666" />
            <Text style={styles.infoText}>
              {shiftType.break_duration} dk mola
            </Text>
          </View>
        )}
      </View>

      {/* Silme Butonu */}
      {onDelete && (
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => onDelete(shiftType.id)}
          hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
        >
          <MaterialIcons name="delete-outline" size={20} color="#ff5252" />
        </TouchableOpacity>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    ...shiftStyles.listItem,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  colorDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  hourlyRate: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#666',
    marginRight: 30, // Silme butonuna yer açmak için sağ margin ekle
  },
  details: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    marginBottom: 4,
  },
  infoText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 6,
  },
  deleteButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    padding: 4,
  },
});

export default ShiftTypeItem;
