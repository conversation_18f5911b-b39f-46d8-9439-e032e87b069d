/**
 * Work shifts tablosunu güncelleyen migrasyon
 * - status sütunu ekler (active, completed, planned)
 * - break_duration sütunu ekler
 * - notes sütunu ekler
 * - shift_type_id sütunu ekler
 * - schedule_id sütunu ekler
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<void>}
 */
export const migrateWorkShiftsUpdate = async (db) => {
  try {
    // Tablo var mı kontrol et
    const tableExists = await db.getFirstAsync(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='work_shifts'
    `);

    if (tableExists) {
      // Sütunları kontrol et
      const columns = await db.getAllAsync(`PRAGMA table_info(work_shifts)`);
      const columnNames = columns.map(col => col.name);

      // Status sütunu ekle
      if (!columnNames.includes('status')) {
        await db.execAsync(`ALTER TABLE work_shifts ADD COLUMN status TEXT DEFAULT 'completed'`);
      }

      // Break duration sütunu ekle
      if (!columnNames.includes('break_duration')) {
        await db.execAsync(`ALTER TABLE work_shifts ADD COLUMN break_duration INTEGER DEFAULT 0`);
      }

      // Notes sütunu ekle
      if (!columnNames.includes('notes')) {
        await db.execAsync(`ALTER TABLE work_shifts ADD COLUMN notes TEXT`);

        // Mevcut description sütununu notes sütununa taşı
        if (columnNames.includes('description')) {
          await db.execAsync(`UPDATE work_shifts SET notes = description WHERE description IS NOT NULL`);
        }
      }

      // Shift type id sütunu ekle
      if (!columnNames.includes('shift_type_id')) {
        await db.execAsync(`ALTER TABLE work_shifts ADD COLUMN shift_type_id INTEGER`);
      }

      // Schedule id sütunu ekle
      if (!columnNames.includes('schedule_id')) {
        await db.execAsync(`ALTER TABLE work_shifts ADD COLUMN schedule_id INTEGER`);
      }
    }
  } catch (error) {
    console.error('Work shifts tablosu güncelleme hatası:', error);
    throw error;
  }
};
