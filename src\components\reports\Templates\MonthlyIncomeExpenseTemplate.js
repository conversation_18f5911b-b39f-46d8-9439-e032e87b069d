import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useTheme } from '../../../context/ThemeContext';
import { ExportManager } from '../Export';

/**
 * MonthlyIncomeExpenseTemplate - A comprehensive template for monthly income and expense reporting
 * @param {Object} props - Component props
 * @param {Object} props.data - Monthly income and expense data
 * @param {Object} props.filters - Applied filters
 * @param {Function} props.onFilterChange - Filter change handler
 * @param {Object} props.config - Template configuration
 * @returns {JSX.Element} The rendered template
 */
export const MonthlyIncomeExpenseTemplate = ({ data, filters, onFilterChange, config }) => {
  const { theme } = useTheme();
  const [isLoading, setIsLoading] = useState(false);
  const [exportStatus, setExportStatus] = useState({ success: false, message: '' });

  // Generate mock data if data is not provided
  const mockData = data || {
    income: {
      total: 15000,
      breakdown: {
        'Maaş': 12000,
        'Bonus': 2000,
        'Diğer': 1000
      }
    },
    expenses: {
      total: 8500,
      breakdown: {
        'Konut': 3000,
        'Ulaşım': 1500,
        'Yemek': 2000,
        'Diğer': 2000
      }
    },
    netIncome: 6500,
    month: 'Ocak 2024',
    comparisonData: {
      previousMonth: {
        income: 14000,
        expenses: 8000,
        netIncome: 6000
      }
    }
  };

  /**
   * Get safe theme value
   * @param {string} property - Theme property
   * @param {string} fallback - Fallback value
   * @returns {string} Safe theme value
   */
  const getSafeThemeValue = (property, fallback) => {
    return theme?.colors?.[property] || fallback;
  };

  /**
   * Handles export operations
   * @param {string} type - Export type (pdf, excel, powerpoint, image, email)
   */
  const handleExport = async (type) => {
    setIsLoading(true);
    setExportStatus({ success: false, message: '' });

    try {
      const exportData = {
        title: 'Aylık Gelir-Gider Raporu',
        data: mockData,
        filters,
        config
      };

      await ExportManager.exportReport(type, exportData);
      setExportStatus({ success: true, message: `${type.toUpperCase()} export başarılı!` });
    } catch (error) {
      setExportStatus({ success: false, message: `Export hatası: ${error.message}` });
    } finally {
      setIsLoading(false);
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: getSafeThemeValue('background', '#ffffff'),
      padding: 16,
    },
    header: {
      marginBottom: 24,
      alignItems: 'center',
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      color: getSafeThemeValue('text', '#333333'),
      marginBottom: 8,
    },
    subtitle: {
      fontSize: 16,
      color: getSafeThemeValue('textSecondary', '#666666'),
    },
    summaryCard: {
      backgroundColor: getSafeThemeValue('cardBackground', '#f9f9f9'),
      borderRadius: 12,
      padding: 16,
      marginBottom: 16,
      shadowColor: getSafeThemeValue('shadow', '#000000'),
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    summaryTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: getSafeThemeValue('text', '#333333'),
      marginBottom: 12,
    },
    summaryRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 8,
    },
    summaryLabel: {
      fontSize: 14,
      color: getSafeThemeValue('textSecondary', '#666666'),
    },
    summaryValue: {
      fontSize: 16,
      fontWeight: '600',
      color: getSafeThemeValue('text', '#333333'),
    },
    incomeValue: {
      color: getSafeThemeValue('success', '#28a745'),
    },
    expenseValue: {
      color: getSafeThemeValue('error', '#dc3545'),
    },
    netIncomeValue: {
      color: getSafeThemeValue('primary', '#007AFF'),
      fontSize: 18,
      fontWeight: 'bold',
    },
    breakdownCard: {
      backgroundColor: getSafeThemeValue('cardBackground', '#f9f9f9'),
      borderRadius: 12,
      padding: 16,
      marginBottom: 16,
      shadowColor: getSafeThemeValue('shadow', '#000000'),
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    breakdownTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: getSafeThemeValue('text', '#333333'),
      marginBottom: 12,
    },
    breakdownItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 8,
      borderBottomWidth: 1,
      borderBottomColor: getSafeThemeValue('border', '#e0e0e0'),
    },
    breakdownLabel: {
      fontSize: 14,
      color: getSafeThemeValue('text', '#333333'),
    },
    breakdownValue: {
      fontSize: 14,
      fontWeight: '600',
      color: getSafeThemeValue('text', '#333333'),
    },
    exportContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-around',
      marginTop: 16,
      padding: 16,
      backgroundColor: getSafeThemeValue('cardBackground', '#f9f9f9'),
      borderRadius: 12,
    },
    exportButton: {
      backgroundColor: getSafeThemeValue('primary', '#007AFF'),
      paddingVertical: 8,
      paddingHorizontal: 16,
      borderRadius: 8,
      margin: 4,
    },
    exportButtonText: {
      color: getSafeThemeValue('onPrimary', '#ffffff'),
      fontSize: 12,
      fontWeight: '600',
    },
    statusContainer: {
      padding: 12,
      marginTop: 8,
      borderRadius: 8,
      alignItems: 'center',
    },
    statusText: {
      fontSize: 14,
      fontWeight: '600',
    },
    successStatus: {
      backgroundColor: getSafeThemeValue('success', '#28a745') + '20',
    },
    errorStatus: {
      backgroundColor: getSafeThemeValue('error', '#dc3545') + '20',
    },
    successText: {
      color: getSafeThemeValue('success', '#28a745'),
    },
    errorText: {
      color: getSafeThemeValue('error', '#dc3545'),
    },
  });

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>📊 Aylık Gelir-Gider Raporu</Text>
        <Text style={styles.subtitle}>{mockData.month}</Text>
      </View>

      {/* Summary Card */}
      <View style={styles.summaryCard}>
        <Text style={styles.summaryTitle}>Özet</Text>
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Toplam Gelir</Text>
          <Text style={[styles.summaryValue, styles.incomeValue]}>
            ₺{mockData.income.total.toLocaleString('tr-TR')}
          </Text>
        </View>
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Toplam Gider</Text>
          <Text style={[styles.summaryValue, styles.expenseValue]}>
            ₺{mockData.expenses.total.toLocaleString('tr-TR')}
          </Text>
        </View>
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Net Gelir</Text>
          <Text style={[styles.summaryValue, styles.netIncomeValue]}>
            ₺{mockData.netIncome.toLocaleString('tr-TR')}
          </Text>
        </View>
      </View>

      {/* Income Breakdown */}
      <View style={styles.breakdownCard}>
        <Text style={styles.breakdownTitle}>Gelir Detayları</Text>
        {Object.entries(mockData.income.breakdown).map(([category, amount]) => (
          <View key={category} style={styles.breakdownItem}>
            <Text style={styles.breakdownLabel}>{category}</Text>
            <Text style={[styles.breakdownValue, styles.incomeValue]}>
              ₺{amount.toLocaleString('tr-TR')}
            </Text>
          </View>
        ))}
      </View>

      {/* Expense Breakdown */}
      <View style={styles.breakdownCard}>
        <Text style={styles.breakdownTitle}>Gider Detayları</Text>
        {Object.entries(mockData.expenses.breakdown).map(([category, amount]) => (
          <View key={category} style={styles.breakdownItem}>
            <Text style={styles.breakdownLabel}>{category}</Text>
            <Text style={[styles.breakdownValue, styles.expenseValue]}>
              ₺{amount.toLocaleString('tr-TR')}
            </Text>
          </View>
        ))}
      </View>

      {/* Export Options */}
      <View style={styles.exportContainer}>
        <TouchableOpacity
          style={styles.exportButton}
          onPress={() => handleExport('pdf')}
          disabled={isLoading}
        >
          <Text style={styles.exportButtonText}>PDF</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.exportButton}
          onPress={() => handleExport('excel')}
          disabled={isLoading}
        >
          <Text style={styles.exportButtonText}>Excel</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.exportButton}
          onPress={() => handleExport('powerpoint')}
          disabled={isLoading}
        >
          <Text style={styles.exportButtonText}>PowerPoint</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.exportButton}
          onPress={() => handleExport('image')}
          disabled={isLoading}
        >
          <Text style={styles.exportButtonText}>Resim</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.exportButton}
          onPress={() => handleExport('email')}
          disabled={isLoading}
        >
          <Text style={styles.exportButtonText}>E-posta</Text>
        </TouchableOpacity>
      </View>

      {/* Loading Indicator */}
      {isLoading && (
        <View style={styles.statusContainer}>
          <ActivityIndicator size="large" color={getSafeThemeValue('primary', '#007AFF')} />
          <Text style={[styles.statusText, { color: getSafeThemeValue('primary', '#007AFF') }]}>
            Export işlemi devam ediyor...
          </Text>
        </View>
      )}

      {/* Export Status */}
      {exportStatus.message && (
        <View style={[
          styles.statusContainer,
          exportStatus.success ? styles.successStatus : styles.errorStatus
        ]}>
          <Text style={[
            styles.statusText,
            exportStatus.success ? styles.successText : styles.errorText
          ]}>
            {exportStatus.message}
          </Text>
        </View>
      )}
    </ScrollView>
  );
};

export default MonthlyIncomeExpenseTemplate;
