import { useState, useEffect, useCallback } from 'react';
import { useSQLiteContext } from 'expo-sqlite';
import { createRealDataService } from '../services/reports/realDataService';

/**
 * Custom hook for real data integration in reports
 * Provides access to real SQLite data for reporting components
 */
export const useRealData = () => {
  const db = useSQLiteContext();
  const [realDataService, setRealDataService] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Initialize real data service
  useEffect(() => {
    if (db) {
      const service = createRealDataService(db);
      setRealDataService(service);
    }
  }, [db]);

  /**
   * Execute data operation with loading and error handling
   * @param {Function} operation - Data operation function
   * @returns {Promise<any>} Operation result
   */
  const executeOperation = useCallback(async (operation) => {
    if (!realDataService) {
      throw new Error('Real data service not initialized');
    }

    setLoading(true);
    setError(null);

    try {
      const result = await operation(realDataService);
      return result;
    } catch (err) {
      setError(err.message || 'Veri yüklenirken hata oluştu');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [realDataService]);

  /**
   * Get monthly income/expense data
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Monthly data
   */
  const getMonthlyIncomeExpense = useCallback(async (params = {}) => {
    return executeOperation(async (service) => {
      return await service.getMonthlyIncomeExpense(params);
    });
  }, [executeOperation]);

  /**
   * Get category distribution data
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Category distribution
   */
  const getCategoryDistribution = useCallback(async (params = {}) => {
    return executeOperation(async (service) => {
      return await service.getCategoryDistribution(params);
    });
  }, [executeOperation]);

  /**
   * Get daily trends data
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Daily trends
   */
  const getDailyTrends = useCallback(async (params = {}) => {
    return executeOperation(async (service) => {
      return await service.getDailyTrends(params);
    });
  }, [executeOperation]);

  /**
   * Get top transactions
   * @param {Object} params - Query parameters
   * @returns {Promise<Array>} Top transactions
   */
  const getTopTransactions = useCallback(async (params = {}) => {
    return executeOperation(async (service) => {
      return await service.getTopTransactions(params);
    });
  }, [executeOperation]);

  /**
   * Get financial goals progress
   * @param {Object} params - Query parameters
   * @returns {Promise<Array>} Goals progress
   */
  const getGoalsProgress = useCallback(async (params = {}) => {
    return executeOperation(async (service) => {
      return await service.getGoalsProgress(params);
    });
  }, [executeOperation]);

  /**
   * Get monthly comparison data
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Monthly comparison
   */
  const getMonthlyComparison = useCallback(async (params = {}) => {
    return executeOperation(async (service) => {
      return await service.getMonthlyComparison(params);
    });
  }, [executeOperation]);

  /**
   * Get report summary statistics
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Summary statistics
   */
  const getReportSummary = useCallback(async (params = {}) => {
    return executeOperation(async (service) => {
      return await service.getReportSummary(params);
    });
  }, [executeOperation]);

  /**
   * Get chart data for different chart types
   * @param {string} chartType - Chart type (line, bar, pie, etc.)
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Chart data
   */
  const getChartData = useCallback(async (chartType, params = {}) => {
    return executeOperation(async (service) => {
      switch (chartType) {
        case 'line':
        case 'bar':
          const trends = await service.getDailyTrends(params);
          return {
            labels: trends.labels,
            datasets: [
              {
                data: params.dataType === 'income' ? trends.income : 
                      params.dataType === 'expense' ? trends.expenses : 
                      trends.netFlow,
                color: (opacity = 1) => `rgba(0, 122, 255, ${opacity})`,
                strokeWidth: 2
              }
            ]
          };

        case 'pie':
          const distribution = await service.getCategoryDistribution(params);
          return distribution.categories.map(cat => ({
            name: cat.name,
            population: cat.amount,
            color: cat.color,
            legendFontColor: '#333333',
            legendFontSize: 12
          }));

        case 'progress':
          const goals = await service.getGoalsProgress(params);
          return {
            labels: goals.map(goal => goal.name),
            data: goals.map(goal => goal.progress / 100)
          };

        default:
          throw new Error(`Desteklenmeyen grafik türü: ${chartType}`);
      }
    });
  }, [executeOperation]);

  /**
   * Get table data for interactive table builder
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Table data
   */
  const getTableData = useCallback(async (params = {}) => {
    return executeOperation(async (service) => {
      const { dataSource = 'transactions', ...otherParams } = params;

      switch (dataSource) {
        case 'transactions':
          const transactions = await service.getTopTransactions({ 
            ...otherParams, 
            limit: otherParams.limit || 100 
          });
          return {
            columns: [
              { key: 'date', label: 'Tarih', type: 'date' },
              { key: 'description', label: 'Açıklama', type: 'text' },
              { key: 'category.name', label: 'Kategori', type: 'text' },
              { key: 'amount', label: 'Tutar', type: 'number' },
              { key: 'type', label: 'Tür', type: 'text' }
            ],
            rows: transactions.map(t => ({
              date: t.date,
              description: t.description,
              'category.name': t.category.name,
              amount: t.amount,
              type: t.type === 'income' ? 'Gelir' : 'Gider'
            }))
          };

        case 'categories':
          const categoryDist = await service.getCategoryDistribution(otherParams);
          return {
            columns: [
              { key: 'name', label: 'Kategori', type: 'text' },
              { key: 'amount', label: 'Tutar', type: 'number' },
              { key: 'percentage', label: 'Yüzde', type: 'number' },
              { key: 'count', label: 'İşlem Sayısı', type: 'number' }
            ],
            rows: categoryDist.categories.map(cat => ({
              name: cat.name,
              amount: cat.amount,
              percentage: cat.percentage,
              count: cat.count
            }))
          };

        case 'monthly':
          const monthlyData = await service.getMonthlyComparison(otherParams);
          return {
            columns: [
              { key: 'month', label: 'Ay', type: 'text' },
              { key: 'income', label: 'Gelir', type: 'number' },
              { key: 'expense', label: 'Gider', type: 'number' },
              { key: 'netFlow', label: 'Net Akış', type: 'number' }
            ],
            rows: monthlyData.labels.map((label, index) => ({
              month: label,
              income: monthlyData.income[index],
              expense: monthlyData.expenses[index],
              netFlow: monthlyData.netFlow[index]
            }))
          };

        default:
          throw new Error(`Desteklenmeyen veri kaynağı: ${dataSource}`);
      }
    });
  }, [executeOperation]);

  /**
   * Check if real data is available
   * @returns {Promise<boolean>} True if data exists
   */
  const hasRealData = useCallback(async () => {
    if (!realDataService) return false;

    try {
      const summary = await realDataService.getReportSummary();
      return summary.totalTransactions > 0;
    } catch (error) {
      return false;
    }
  }, [realDataService]);

  /**
   * Get data freshness info
   * @returns {Promise<Object>} Data freshness information
   */
  const getDataFreshness = useCallback(async () => {
    return executeOperation(async (service) => {
      const latestTransaction = await service.db.getFirstAsync(`
        SELECT MAX(date) as latest_date, COUNT(*) as total_count
        FROM transactions
      `);

      const latestDate = latestTransaction?.latest_date 
        ? new Date(latestTransaction.latest_date)
        : null;

      const daysSinceLatest = latestDate 
        ? Math.floor((new Date() - latestDate) / (1000 * 60 * 60 * 24))
        : null;

      return {
        latestTransactionDate: latestDate?.toLocaleDateString('tr-TR'),
        daysSinceLatest,
        totalTransactions: latestTransaction?.total_count || 0,
        freshness: daysSinceLatest === null ? 'no_data' :
                  daysSinceLatest === 0 ? 'very_fresh' :
                  daysSinceLatest <= 7 ? 'fresh' :
                  daysSinceLatest <= 30 ? 'moderate' : 'stale'
      };
    });
  }, [executeOperation]);

  /**
   * Get regular income tracking data
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Regular income tracking data
   */
  const getRegularIncomeTracking = useCallback(async (params = {}) => {
    return executeOperation(async (service) => {
      return await service.getRegularIncomeTracking(params);
    });
  }, [executeOperation]);

  /**
   * Get overtime income data
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Overtime income data
   */
  const getOvertimeIncomeData = useCallback(async (params = {}) => {
    return executeOperation(async (service) => {
      return await service.getOvertimeIncomeData(params);
    });
  }, [executeOperation]);

  return {
    // Data methods
    getMonthlyIncomeExpense,
    getCategoryDistribution,
    getDailyTrends,
    getTopTransactions,
    getGoalsProgress,
    getMonthlyComparison,
    getReportSummary,
    getChartData,
    getTableData,
    getRegularIncomeTracking,
    getOvertimeIncomeData,
    
    // Utility methods
    hasRealData,
    getDataFreshness,
    
    // State
    loading,
    error,
    isReady: !!realDataService
  };
};
