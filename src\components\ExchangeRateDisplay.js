import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { Colors } from '../constants/colors';
import { useExchangeRate } from '../context/ExchangeRateContext';
import { formatCurrency } from '../utils/formatters';

/**
 * Döviz kuru gösterimi bileşeni
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {string} props.baseCurrency - Baz para birimi
 * @param {Array<string>} props.targetCurrencies - Hedef para birimleri
 * @param {string} props.date - Tarih (varsayılan: bugün)
 * @returns {JSX.Element} Döviz kuru gösterimi bileşeni
 */
const ExchangeRateDisplay = ({
  baseCurrency = 'TRY',
  targetCurrencies = ['USD', 'EUR', 'GBP'],
  date = null
}) => {
  // ExchangeRateContext'ten değerleri al
  const { rates: allRates, loading: contextLoading, lastUpdate: contextLastUpdate } = useExchangeRate();

  const [rates, setRates] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdate, setLastUpdate] = useState(null);

  useEffect(() => {
    try {
      setLoading(true);
      setError(null);

      // Context'ten gelen kurları kullan
      if (allRates && allRates[baseCurrency]) {
        setRates(allRates[baseCurrency]);
        setLastUpdate(contextLastUpdate);
        setLoading(false);
      } else {
        setError('Döviz kurları bulunamadı');
        setLoading(false);
      }
    } catch (err) {
      console.error('Döviz kurları işleme hatası:', err);
      setError('Döviz kurları alınamadı');
      setLoading(false);
    }
  }, [baseCurrency, allRates, contextLastUpdate]);

  // Yükleniyor durumu
  if (loading) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="small" color={Colors.PRIMARY} />
      </View>
    );
  }

  // Hata durumu
  if (error) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>{error}</Text>
      </View>
    );
  }

  // Döviz kurları
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Güncel Döviz Kurları</Text>
        {lastUpdate && (
          <Text style={styles.lastUpdate}>
            Son güncelleme: {new Date(lastUpdate).toLocaleString('tr-TR')}
          </Text>
        )}
      </View>

      <View style={styles.ratesContainer}>
        {targetCurrencies.map(currency => (
          <View key={currency} style={styles.rateItem}>
            <Text style={styles.currencyCode}>{currency}</Text>
            <Text style={styles.rateValue}>
              {rates[currency] ? `${formatCurrency(1, baseCurrency)} = ${formatCurrency(rates[currency], currency)}` : '-'}
            </Text>
          </View>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  header: {
    marginBottom: 12,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.GRAY_800,
    marginBottom: 4,
  },
  lastUpdate: {
    fontSize: 12,
    color: Colors.GRAY_500,
  },
  ratesContainer: {
    marginTop: 8,
  },
  rateItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200,
  },
  currencyCode: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.GRAY_700,
  },
  rateValue: {
    fontSize: 14,
    color: Colors.GRAY_800,
  },
  errorText: {
    fontSize: 14,
    color: Colors.DANGER,
  }
});

export default ExchangeRateDisplay;
