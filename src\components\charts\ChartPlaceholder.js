import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from '../../context/ThemeContext';

/**
 * Basit chart placeholder - chart componentleri olmadığında gösterilir
 */
const ChartPlaceholder = ({ 
  title = 'Grafik',
  width = '100%',
  height = 200,
  type = 'chart'
}) => {
  const { theme } = useTheme();

  const getChartIcon = () => {
    switch (type) {
      case 'line':
        return '📈';
      case 'bar':
        return '📊';
      case 'pie':
        return '🥧';
      default:
        return '📋';
    }
  };

  return (
    <View style={[
      styles.container, 
      { 
        backgroundColor: theme.SURFACE,
        width: typeof width === 'string' ? width : width,
        height: height
      }
    ]}>
      <Text style={[styles.icon]}>
        {getChartIcon()}
      </Text>
      <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
        {title}
      </Text>
      <Text style={[styles.subtitle, { color: theme.TEXT_SECONDARY }]}>
        <PERSON>ik geliştiriliyor...
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12,
    margin: 8,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  icon: {
    fontSize: 32,
    marginBottom: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 12,
    textAlign: 'center',
    opacity: 0.7,
  },
});

export default ChartPlaceholder;
