import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Dimensions
} from 'react-native';
import { useTheme } from '../../../context/ThemeContext';
import { useDataIntegration } from '../../../context/DataIntegrationContext';

const { width: screenWidth } = Dimensions.get('window');

/**
 * Temel Özet Raporu Template
 * Genel finansal durumun hızlı özetini gösteren kapsamlı rapor
 */
const BasicSummaryTemplate = ({ 
  templateConfig, 
  customParams = {}, 
  onExport, 
  onSave 
}) => {
  const { theme } = useTheme();
  const { reportDataService, isLoading: contextLoading } = useDataIntegration();
  
  const [loading, setLoading] = useState(true);
  const [reportData, setReportData] = useState(null);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('overview'); // 'overview', 'income', 'expenses', 'goals'

  useEffect(() => {
    loadReportData();
  }, [reportDataService]);

  /**
   * Rapor verilerini yükle
   */
  const loadReportData = async () => {
    if (!reportDataService) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const params = {
        dateRange: customParams.dateRange || 'current_month',
        includeGoals: customParams.includeGoals || true,
        includeComparisons: customParams.includeComparisons || true,
        detailLevel: customParams.detailLevel || 'medium',
        ...customParams
      };
      
      const data = await reportDataService.getBasicSummaryData(params);
      setReportData(data);
    } catch (error) {
      setError('Temel özet verileri yüklenemedi: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Loading state
  if (loading || contextLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.BACKGROUND }]}>
        <ActivityIndicator size="large" color={theme.PRIMARY} />
        <Text style={[styles.loadingText, { color: theme.TEXT_PRIMARY }]}>
          Temel özet verileri yükleniyor...
        </Text>
      </View>
    );
  }

  // Error state
  if (error) {
    return (
      <View style={[styles.errorContainer, { backgroundColor: theme.BACKGROUND }]}>
        <Text style={[styles.errorText, { color: theme.ERROR }]}>
          {error}
        </Text>
        <TouchableOpacity 
          style={[styles.retryButton, { backgroundColor: theme.PRIMARY }]}
          onPress={loadReportData}
        >
          <Text style={[styles.retryText, { color: theme.SURFACE }]}>
            Tekrar Dene
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  /**
   * Tab bar render
   */
  const renderTabBar = () => {
    const tabs = [
      { id: 'overview', name: 'Genel Bakış', icon: '📊' },
      { id: 'income', name: 'Gelirler', icon: '💰' },
      { id: 'expenses', name: 'Giderler', icon: '💸' },
      { id: 'goals', name: 'Hedefler', icon: '🎯' }
    ];

    return (
      <View style={[styles.tabBar, { backgroundColor: theme.CARD }]}>
        {tabs.map(tab => (
          <TouchableOpacity
            key={tab.id}
            style={[
              styles.tabButton,
              { backgroundColor: activeTab === tab.id ? theme.primary : 'transparent' }
            ]}
            onPress={() => setActiveTab(tab.id)}
          >
            <Text style={styles.tabIcon}>{tab.icon}</Text>
            <Text style={[
              styles.tabText,
              { color: activeTab === tab.id ? (theme.surface || theme.SURFACE || theme.colors?.surface || '#f9f9f9') : (theme.TEXT_PRIMARY || theme.colors?.text || '#000') }
            ]}>
              {tab.name}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  /**
   * Ana özet kartları render
   */
  const renderMainSummaryCards = () => {
    const summary = reportData?.summary || {};
    
    const cards = [
      {
        title: 'Toplam Gelir',
        value: `₺${summary.totalIncome?.toLocaleString('tr-TR') || '0'}`,
        color: theme.success,
        icon: '💰',
        change: summary.incomeChange || 0
      },
      {
        title: 'Toplam Gider',
        value: `₺${summary.totalExpenses?.toLocaleString('tr-TR') || '0'}`,
        color: theme.error,
        icon: '💸',
        change: summary.expenseChange || 0
      },
      {
        title: 'Net Kazanç',
        value: `₺${summary.netProfit?.toLocaleString('tr-TR') || '0'}`,
        color: summary.netProfit >= 0 ? theme.success : theme.error,
        icon: summary.netProfit >= 0 ? '📈' : '📉',
        change: summary.netChange || 0
      },
      {
        title: 'Tasarruf Oranı',
        value: `${summary.savingsRate || 0}%`,
        color: theme.info,
        icon: '🏦',
        change: summary.savingsRateChange || 0
      }
    ];

    return (
      <View style={styles.summaryContainer}>
        {cards.map((card, index) => (
          <View key={index} style={[styles.summaryCard, { backgroundColor: theme.CARD }]}>
            <View style={styles.cardHeader}>
              <Text style={styles.summaryIcon}>{card.icon}</Text>
              <Text style={[
                styles.changeIndicator,
                { color: card.change >= 0 ? theme.success : theme.error }
              ]}>
                {card.change >= 0 ? '↗' : '↘'} {Math.abs(card.change)}%
              </Text>
            </View>
            <Text style={[styles.summaryTitle, { color: theme.TEXT_SECONDARY }]}>
              {card.title}
            </Text>
            <Text style={[styles.summaryValue, { color: card.color }]}>
              {card.value}
            </Text>
          </View>
        ))}
      </View>
    );
  };

  /**
   * Gelir-gider dağılımı pie chart render
   */
  const renderIncomeExpensePieChart = () => {
    const summary = reportData?.summary || {};
    
    if (!summary.totalIncome && !summary.totalExpenses) {
      return (
        <View style={styles.noDataContainer}>
          <Text style={[styles.noDataText, { color: theme.TEXT_SECONDARY }]}>
            Gelir-gider dağılım verisi bulunmamaktadır
          </Text>
        </View>
      );
    }

    const data = [
      {
        name: 'Gelir',
        amount: summary.totalIncome || 0,
        color: '#2ecc71',
        legendFontColor: theme.TEXT_PRIMARY,
        legendFontSize: 14
      },
      {
        name: 'Gider',
        amount: summary.totalExpenses || 0,
        color: '#e74c3c',
        legendFontColor: theme.TEXT_PRIMARY,
        legendFontSize: 14
      }
    ];

    return (
      <View style={styles.chartContainer}>
        <Text style={[styles.chartTitle, { color: theme.TEXT_PRIMARY }]}>
          Gelir - Gider Dağılımı
        </Text>
        <PieChart
          data={data}
          width={screenWidth - 40}
          height={220}
          chartConfig={{
            backgroundColor: theme.CARD,
            backgroundGradientFrom: theme.CARD,
            backgroundGradientTo: theme.CARD,
            color: (opacity = 1) => `rgba(255, 255, 255, ${opacity})`,
            labelColor: (opacity = 1) => theme.TEXT_PRIMARY,
            style: {
              borderRadius: 16
            }
          }}
          accessor="amount"
          backgroundColor="transparent"
          paddingLeft="15"
          absolute
          style={styles.chart}
        />
      </View>
    );
  };

  /**
   * Kategori bazlı gelir analizi render
   */
  const renderIncomeAnalysis = () => {
    const incomeData = reportData?.incomeBreakdown || [];
    
    if (incomeData.length === 0) {
      return (
        <View style={styles.noDataContainer}>
          <Text style={[styles.noDataText, { color: theme.TEXT_SECONDARY }]}>
            Gelir analizi verisi bulunmamaktadır
          </Text>
        </View>
      );
    }

    return (
      <View style={styles.analysisContainer}>
        <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
          💰 Gelir Analizi
        </Text>
        
        {incomeData.map((item, index) => (
          <View key={index} style={[styles.analysisCard, { backgroundColor: theme.CARD }]}>
            <View style={styles.analysisHeader}>
              <Text style={[styles.analysisName, { color: theme.TEXT_PRIMARY }]}>
                {item.category}
              </Text>
              <Text style={[styles.analysisAmount, { color: theme.success }]}>
                ₺{item.amount.toLocaleString('tr-TR')}
              </Text>
            </View>
            
            <View style={styles.analysisDetails}>
              <Text style={[styles.analysisPercentage, { color: theme.TEXT_SECONDARY }]}>
                %{item.percentage.toFixed(1)} (Toplam gelirin)
              </Text>
              <Text style={[styles.analysisCount, { color: theme.TEXT_SECONDARY }]}>
                {item.transactionCount} işlem
              </Text>
            </View>
            
            <View style={styles.progressContainer}>
              <View style={[styles.progressBar, { backgroundColor: theme.BACKGROUND }]}>
                <View style={[
                  styles.progressFill,
                  { 
                    width: `${item.percentage}%`,
                    backgroundColor: theme.success
                  }
                ]} />
              </View>
            </View>
          </View>
        ))}
      </View>
    );
  };

  /**
   * Kategori bazlı gider analizi render
   */
  const renderExpenseAnalysis = () => {
    const expenseData = reportData?.expenseBreakdown || [];
    
    if (expenseData.length === 0) {
      return (
        <View style={styles.noDataContainer}>
          <Text style={[styles.noDataText, { color: theme.TEXT_SECONDARY }]}>
            Gider analizi verisi bulunmamaktadır
          </Text>
        </View>
      );
    }

    return (
      <View style={styles.analysisContainer}>
        <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
          💸 Gider Analizi
        </Text>
        
        {expenseData.map((item, index) => (
          <View key={index} style={[styles.analysisCard, { backgroundColor: theme.CARD }]}>
            <View style={styles.analysisHeader}>
              <Text style={[styles.analysisName, { color: theme.TEXT_PRIMARY }]}>
                {item.category}
              </Text>
              <Text style={[styles.analysisAmount, { color: theme.error }]}>
                ₺{item.amount.toLocaleString('tr-TR')}
              </Text>
            </View>
            
            <View style={styles.analysisDetails}>
              <Text style={[styles.analysisPercentage, { color: theme.TEXT_SECONDARY }]}>
                %{item.percentage.toFixed(1)} (Toplam giderin)
              </Text>
              <Text style={[styles.analysisCount, { color: theme.TEXT_SECONDARY }]}>
                {item.transactionCount} işlem
              </Text>
            </View>
            
            <View style={styles.progressContainer}>
              <View style={[styles.progressBar, { backgroundColor: theme.BACKGROUND }]}>
                <View style={[
                  styles.progressFill,
                  { 
                    width: `${item.percentage}%`,
                    backgroundColor: theme.error
                  }
                ]} />
              </View>
            </View>
          </View>
        ))}
      </View>
    );
  };

  /**
   * Hedef takibi render
   */
  const renderGoalTracking = () => {
    const goals = reportData?.goals || [];
    
    if (goals.length === 0) {
      return (
        <View style={styles.noDataContainer}>
          <Text style={[styles.noDataText, { color: theme.TEXT_SECONDARY }]}>
            Hedef takip verisi bulunmamaktadır
          </Text>
        </View>
      );
    }

    return (
      <View style={styles.goalsContainer}>
        <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
          🎯 Hedef Takibi
        </Text>
        
        {goals.map((goal, index) => (
          <View key={index} style={[styles.goalCard, { backgroundColor: theme.CARD }]}>
            <View style={styles.goalHeader}>
              <Text style={[styles.goalName, { color: theme.TEXT_PRIMARY }]}>
                {goal.name}
              </Text>
              <Text style={[styles.goalProgress, { color: theme.info }]}>
                {goal.progress}%
              </Text>
            </View>
            
            <View style={styles.goalDetails}>
              <Text style={[styles.goalAmount, { color: theme.TEXT_SECONDARY }]}>
                ₺{goal.current.toLocaleString('tr-TR')} / ₺{goal.target.toLocaleString('tr-TR')}
              </Text>
              <Text style={[styles.goalStatus, { color: 
                goal.progress >= 100 ? theme.success : 
                goal.progress >= 75 ? theme.warning : theme.error 
              }]}>
                {goal.progress >= 100 ? 'Tamamlandı' : 
                 goal.progress >= 75 ? 'Hedefte' : 'Düşük'}
              </Text>
            </View>
            
            <View style={styles.progressContainer}>
              <View style={[styles.progressBar, { backgroundColor: theme.BACKGROUND }]}>
                <View style={[
                  styles.progressFill,
                  { 
                    width: `${Math.min(goal.progress, 100)}%`,
                    backgroundColor: goal.progress >= 100 ? theme.success : 
                                   goal.progress >= 75 ? theme.warning : theme.error
                  }
                ]} />
              </View>
            </View>
          </View>
        ))}
      </View>
    );
  };

  /**
   * Hızlı insights render
   */
  const renderQuickInsights = () => {
    const insights = reportData?.insights || [];
    
    if (insights.length === 0) {
      return null;
    }

    return (
      <View style={styles.insightsContainer}>
        <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
          💡 Hızlı Analizler
        </Text>
        
        {insights.map((insight, index) => (
          <View key={index} style={[styles.insightCard, { backgroundColor: theme.CARD }]}>
            <Text style={styles.insightIcon}>{insight.icon}</Text>
            <View style={styles.insightContent}>
              <Text style={[styles.insightTitle, { color: theme.TEXT_PRIMARY }]}>
                {insight.title}
              </Text>
              <Text style={[styles.insightDescription, { color: theme.TEXT_SECONDARY }]}>
                {insight.description}
              </Text>
            </View>
          </View>
        ))}
      </View>
    );
  };

  /**
   * Tab içeriği render
   */
  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <View>
            {renderMainSummaryCards()}
            {renderIncomeExpensePieChart()}
            {renderQuickInsights()}
          </View>
        );
      case 'income':
        return (
          <View>
            {renderIncomeAnalysis()}
          </View>
        );
      case 'expenses':
        return (
          <View>
            {renderExpenseAnalysis()}
          </View>
        );
      case 'goals':
        return (
          <View>
            {renderGoalTracking()}
          </View>
        );
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.BACKGROUND }]}>
        <ActivityIndicator size="large" color={theme.primary} />
        <Text style={[styles.loadingText, { color: theme.TEXT_PRIMARY }]}>
          Temel özet verileri yükleniyor...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.CARD }]}>
        <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
          📊 Temel Özet Raporu
        </Text>
        <Text style={[styles.subtitle, { color: theme.TEXT_SECONDARY }]}>
          {reportData?.params?.dateRange === 'current_month' ? 'Bu Ay' : 
           reportData?.params?.dateRange === 'current_year' ? 'Bu Yıl' : 
           'Özel Tarih Aralığı'}
        </Text>
      </View>

      {/* Tab Bar */}
      {renderTabBar()}

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderTabContent()}
      </ScrollView>

      {/* Footer */}
      <View style={[styles.footer, { backgroundColor: theme.CARD }]}>
        <TouchableOpacity
          style={[styles.footerButton, { backgroundColor: theme.primary || theme.PRIMARY || theme.colors?.primary || '#007bff' }]}
          onPress={() => onExport && onExport('pdf', reportData, 'Temel Finansal Özet', 'basic-summary')}
        >
          <Text style={[styles.footerButtonText, { color: theme.surface || theme.SURFACE || theme.colors?.surface || '#f9f9f9' }]}>
            📄 PDF
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.footerButton, { backgroundColor: theme.success || theme.SUCCESS || theme.colors?.success || '#28a745' }]}
          onPress={() => onExport && onExport('excel', reportData, 'Temel Finansal Özet', 'basic-summary')}
        >
          <Text style={[styles.footerButtonText, { color: theme.surface || theme.SURFACE || theme.colors?.surface || '#f9f9f9' }]}>
            📊 Excel
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.footerButton, { backgroundColor: theme.warning || theme.WARNING || theme.colors?.warning || '#ffc107' }]}
          onPress={() => onExport && onExport('email', reportData, 'Temel Finansal Özet', 'basic-summary')}
        >
          <Text style={[styles.footerButtonText, { color: theme.surface || theme.SURFACE || theme.colors?.surface || '#f9f9f9' }]}>
            📧 E-posta
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.footerButton, { backgroundColor: theme.info || theme.INFO || theme.colors?.info || '#17a2b8' }]}
          onPress={() => onSave && onSave(reportData)}
        >
          <Text style={[styles.footerButtonText, { color: theme.surface || theme.SURFACE || theme.colors?.surface || '#f9f9f9' }]}>
            💾 Kaydet
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  header: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    opacity: 0.8,
  },
  tabBar: {
    flexDirection: 'row',
    padding: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  tabButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 8,
    marginHorizontal: 4,
    gap: 6,
  },
  tabIcon: {
    fontSize: 16,
  },
  tabText: {
    fontSize: 12,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  summaryContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 20,
  },
  summaryCard: {
    flex: 1,
    minWidth: (screenWidth - 60) / 2,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryIcon: {
    fontSize: 24,
  },
  changeIndicator: {
    fontSize: 12,
    fontWeight: '600',
  },
  summaryTitle: {
    fontSize: 12,
    textAlign: 'center',
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '700',
    textAlign: 'center',
  },
  chartContainer: {
    marginBottom: 20,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    textAlign: 'center',
  },
  chart: {
    borderRadius: 16,
  },
  noDataContainer: {
    padding: 40,
    alignItems: 'center',
  },
  noDataText: {
    fontSize: 16,
    textAlign: 'center',
  },
  analysisContainer: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  analysisCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  analysisHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  analysisName: {
    fontSize: 16,
    fontWeight: '600',
  },
  analysisAmount: {
    fontSize: 16,
    fontWeight: '700',
  },
  analysisDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  analysisPercentage: {
    fontSize: 12,
  },
  analysisCount: {
    fontSize: 12,
  },
  progressContainer: {
    marginTop: 8,
  },
  progressBar: {
    height: 6,
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  goalsContainer: {
    marginBottom: 20,
  },
  goalCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  goalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  goalName: {
    fontSize: 16,
    fontWeight: '600',
  },
  goalProgress: {
    fontSize: 16,
    fontWeight: '700',
  },
  goalDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  goalAmount: {
    fontSize: 12,
  },
  goalStatus: {
    fontSize: 12,
    fontWeight: '600',
  },
  insightsContainer: {
    marginBottom: 20,
  },
  insightCard: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  insightIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  insightContent: {
    flex: 1,
  },
  insightTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  insightDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  footer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 16,
    gap: 8,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  footerButton: {
    flex: 1,
    minWidth: '20%',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 4,
  },
  footerButtonText: {
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
  // Loading and error states
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryText: {
    fontSize: 14,
    fontWeight: '600',
  },
});

export default BasicSummaryTemplate;
