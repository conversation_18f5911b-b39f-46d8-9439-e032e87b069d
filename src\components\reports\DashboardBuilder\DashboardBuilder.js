import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Dimensions,
} from 'react-native';
import { useTheme } from '../../../context/ThemeContext';
import { useDashboardBuilder } from './hooks/useDashboardBuilder';
import DashboardBuilderHeader from './components/DashboardBuilderHeader';
import DashboardCanvas from './components/DashboardCanvas';
import WidgetLibrary from './components/WidgetLibrary';
import LayoutManager from './components/LayoutManager';
import DragDropManager from './components/DragDropManager';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

/**
 * Dashboard Builder - Sürükle-bırak ile interaktif dashboard oluşturma
 * Widget yerleştirme, düzenleme ve konfigürasyon
 */
const DashboardBuilder = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { dashboardId = null } = route.params || {};
  
  // Dashboard builder hook
  const {
    widgets,
    selectedWidget,
    canUndo,
    canRedo,
    addWidget,
    removeWidget,
    updateWidget,
    selectWidget,
    moveWidget,
    resizeWidget,
    saveDashboard,
    loadDashboard,
    undo,
    redo,
    clear,
    exportDashboard,
    isLoading,
    error,
  } = useDashboardBuilder(dashboardId);

  // UI state
  const [showWidgetLibrary, setShowWidgetLibrary] = useState(false);
  const [showLayoutManager, setShowLayoutManager] = useState(false);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [draggedWidget, setDraggedWidget] = useState(null);

  // Theme kontrolü
  if (!theme) {
    return (
      <View style={styles.loadingContainer}>
        <Text>Theme yükleniyor...</Text>
      </View>
    );
  }

  /**
   * Güvenli tema değeri alma
   * @param {string} property - Tema özelliği
   * @param {string} fallback - Varsayılan değer
   * @returns {string} Güvenli tema değeri
   */
  const getSafeThemeValue = (property, fallback) => {
    return theme[property] || theme.colors?.[property.toLowerCase()] || fallback;
  };

  /**
   * Widget ekleme
   */
  const handleAddWidget = useCallback((widgetType) => {
    const newWidget = {
      id: Date.now().toString(),
      type: widgetType.type,
      title: widgetType.title,
      position: { x: 50, y: 50 },
      size: { width: 300, height: 200 },
      config: widgetType.defaultConfig || {},
      data: null,
      isVisible: true,
      zIndex: widgets.length + 1,
    };
    
    addWidget(newWidget);
    setShowWidgetLibrary(false);
  }, [addWidget, widgets.length]);

  /**
   * Widget silme
   */
  const handleRemoveWidget = useCallback((widgetId) => {
    Alert.alert(
      'Widget Sil',
      'Bu widget\'ı silmek istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        { text: 'Sil', onPress: () => removeWidget(widgetId), style: 'destructive' },
      ]
    );
  }, [removeWidget]);

  /**
   * Dashboard kaydetme
   */
  const handleSaveDashboard = useCallback(async () => {
    try {
      await saveDashboard({
        title: 'Dashboard',
        description: 'Özel dashboard',
        widgets,
        layout: {
          width: screenWidth,
          height: screenHeight,
        },
        settings: {
          theme: theme.isDarkMode ? 'dark' : 'light',
          autoRefresh: true,
          refreshInterval: 30000,
        },
      });
      
      Alert.alert('Başarılı', 'Dashboard kaydedildi');
    } catch (error) {
      Alert.alert('Hata', 'Dashboard kaydedilirken hata oluştu');
    }
  }, [saveDashboard, widgets, theme.isDarkMode]);

  /**
   * Dashboard dışa aktarma
   */
  const handleExportDashboard = useCallback(async (format) => {
    try {
      await exportDashboard(format, {
        widgets,
        layout: {
          width: screenWidth,
          height: screenHeight,
        },
        title: 'Dashboard Export',
        includeData: true,
      });
      
      Alert.alert('Başarılı', `Dashboard ${format} formatında dışa aktarıldı`);
    } catch (error) {
      Alert.alert('Hata', 'Dashboard dışa aktarılırken hata oluştu');
    }
  }, [exportDashboard, widgets]);

  /**
   * Önizleme modu toggle
   */
  const togglePreviewMode = useCallback(() => {
    setIsPreviewMode(prev => !prev);
  }, []);

  if (isLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: getSafeThemeValue('BACKGROUND', '#fff') }]}>
        <Text style={[styles.loadingText, { color: getSafeThemeValue('TEXT_PRIMARY', '#000') }]}>
          Dashboard yükleniyor...
        </Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={[styles.errorContainer, { backgroundColor: getSafeThemeValue('BACKGROUND', '#fff') }]}>
        <Text style={[styles.errorText, { color: getSafeThemeValue('ERROR', '#dc3545') }]}>
          Hata: {error}
        </Text>
        <TouchableOpacity
          style={[styles.retryButton, { backgroundColor: getSafeThemeValue('PRIMARY', '#007AFF') }]}
          onPress={() => loadDashboard(dashboardId)}
        >
          <Text style={[styles.retryButtonText, { color: getSafeThemeValue('SURFACE', '#fff') }]}>
            Yeniden Dene
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: getSafeThemeValue('BACKGROUND', '#fff') }]}>
      {/* Header */}
      <DashboardBuilderHeader
        title="Dashboard Builder"
        canUndo={canUndo}
        canRedo={canRedo}
        onUndo={undo}
        onRedo={redo}
        onSave={handleSaveDashboard}
        onExport={handleExportDashboard}
        onPreview={togglePreviewMode}
        onWidgetLibrary={() => setShowWidgetLibrary(true)}
        onLayoutManager={() => setShowLayoutManager(true)}
        onClear={clear}
        isPreviewMode={isPreviewMode}
      />

      {/* Main Content */}
      <View style={styles.mainContent}>
        {/* Canvas */}
        <DashboardCanvas
          widgets={widgets}
          selectedWidget={selectedWidget}
          onSelectWidget={selectWidget}
          onMoveWidget={moveWidget}
          onResizeWidget={resizeWidget}
          onRemoveWidget={handleRemoveWidget}
          onUpdateWidget={updateWidget}
          draggedWidget={draggedWidget}
          onDragStart={setDraggedWidget}
          onDragEnd={() => setDraggedWidget(null)}
          isPreviewMode={isPreviewMode}
        />

        {/* Drag Drop Manager */}
        <DragDropManager
          widgets={widgets}
          onWidgetMove={moveWidget}
          onWidgetResize={resizeWidget}
          isEnabled={!isPreviewMode}
        />
      </View>

      {/* Widget Library Modal */}
      {showWidgetLibrary && (
        <WidgetLibrary
          visible={showWidgetLibrary}
          onClose={() => setShowWidgetLibrary(false)}
          onAddWidget={handleAddWidget}
        />
      )}

      {/* Layout Manager Modal */}
      {showLayoutManager && (
        <LayoutManager
          visible={showLayoutManager}
          onClose={() => setShowLayoutManager(false)}
          widgets={widgets}
          onUpdateLayout={updateWidget}
        />
      )}

      {/* Status Bar */}
      <View style={[styles.statusBar, { backgroundColor: getSafeThemeValue('SURFACE', '#f9f9f9') }]}>
        <Text style={[styles.statusText, { color: getSafeThemeValue('TEXT_SECONDARY', '#666') }]}>
          {widgets.length} widget • {selectedWidget ? `Seçili: ${selectedWidget.title}` : 'Hiçbiri seçili değil'}
        </Text>
        {isPreviewMode && (
          <Text style={[styles.previewIndicator, { color: getSafeThemeValue('PRIMARY', '#007AFF') }]}>
            Önizleme Modu
          </Text>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  mainContent: {
    flex: 1,
    position: 'relative',
  },
  statusBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  statusText: {
    fontSize: 12,
  },
  previewIndicator: {
    fontSize: 12,
    fontWeight: '600',
  },
});

export default DashboardBuilder;
