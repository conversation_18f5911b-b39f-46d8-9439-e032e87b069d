/**
 * Transaction Service
 * İşlem (transaction) yönetimi için servis fonksiyonları
 */

/**
 * <PERSON><PERSON> iş<PERSON> ekle
 * @param {Object} db - SQLite database instance
 * @param {Object} transactionData - İşlem verisi
 * @param {string} transactionData.type - İşlem tipi ('income' | 'expense')
 * @param {number} transactionData.amount - Tutar
 * @param {string} transactionData.description - Açıklama
 * @param {number} transactionData.category_id - Kategori ID
 * @param {string} transactionData.date - Tarih (ISO string)
 * @param {string} transactionData.currency - Para birimi
 * @param {number} [transactionData.budget_id] - Bütçe ID (opsiyonel)
 * @returns {Promise<Object>} Eklenen işlem verisi
 */
export const addTransaction = async (db, transactionData) => {
  try {
    console.log('💰 Yeni işlem ekleniyor:', transactionData);

    const {
      type,
      amount,
      description,
      category_id,
      date,
      currency = 'TRY',
      budget_id = null,
    } = transactionData;

    // Validation
    if (!type || !amount || !category_id || !date) {
      throw new Error('Gerekli alanlar eksik: type, amount, category_id, date');
    }

    if (!['income', 'expense'].includes(type)) {
      throw new Error('Geçersiz işlem tipi. income veya expense olmalı.');
    }

    if (amount <= 0) {
      throw new Error('Tutar 0\'dan büyük olmalı.');
    }

    const currentDate = new Date().toISOString();

    // Insert transaction
    const result = await db.runAsync(
      `INSERT INTO transactions (
        type, amount, description, category_id, date, currency, budget_id, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [type, amount, description, category_id, date, currency, budget_id, currentDate, currentDate]
    );

    console.log('✅ İşlem başarıyla eklendi, ID:', result.lastInsertRowId);

    // Get the inserted transaction
    const insertedTransaction = await db.getFirstAsync(
      'SELECT * FROM transactions WHERE id = ?',
      [result.lastInsertRowId]
    );

    // Update budget if budget_id is provided and type is expense
    if (budget_id && type === 'expense') {
      try {
        await updateBudgetSpentAmount(db, budget_id, amount, 'add');
        console.log('✅ Bütçe harcanan tutarı güncellendi');
      } catch (error) {
        console.warn('⚠️ Bütçe güncelleme hatası:', error);
        // Don't fail the transaction if budget update fails
      }
    }

    return insertedTransaction;
  } catch (error) {
    console.error('❌ İşlem ekleme hatası:', error);
    throw error;
  }
};

/**
 * İşlemi güncelle
 * @param {Object} db - SQLite database instance
 * @param {number} transactionId - İşlem ID
 * @param {Object} updateData - Güncellenecek veriler
 * @returns {Promise<Object>} Güncellenen işlem verisi
 */
export const updateTransaction = async (db, transactionId, updateData) => {
  try {
    console.log('📝 İşlem güncelleniyor:', transactionId, updateData);

    // Get current transaction for budget calculations
    const currentTransaction = await db.getFirstAsync(
      'SELECT * FROM transactions WHERE id = ?',
      [transactionId]
    );

    if (!currentTransaction) {
      throw new Error('İşlem bulunamadı');
    }

    const updateFields = [];
    const updateValues = [];

    // Build update query dynamically
    Object.entries(updateData).forEach(([key, value]) => {
      if (key !== 'id' && value !== undefined) {
        updateFields.push(`${key} = ?`);
        updateValues.push(value);
      }
    });

    if (updateFields.length === 0) {
      throw new Error('Güncellenecek alan bulunamadı');
    }

    updateFields.push('updated_at = ?');
    updateValues.push(new Date().toISOString());
    updateValues.push(transactionId);

    // Update transaction
    await db.runAsync(
      `UPDATE transactions SET ${updateFields.join(', ')} WHERE id = ?`,
      updateValues
    );

    // Get updated transaction
    const updatedTransaction = await db.getFirstAsync(
      'SELECT * FROM transactions WHERE id = ?',
      [transactionId]
    );

    // Handle budget updates if amount or budget changed
    if (currentTransaction.budget_id && currentTransaction.type === 'expense') {
      const oldAmount = currentTransaction.amount;
      const newAmount = updateData.amount || oldAmount;
      const amountDiff = newAmount - oldAmount;

      if (amountDiff !== 0) {
        try {
          const operation = amountDiff > 0 ? 'add' : 'subtract';
          await updateBudgetSpentAmount(db, currentTransaction.budget_id, Math.abs(amountDiff), operation);
          console.log('✅ Bütçe harcanan tutarı güncellendi');
        } catch (error) {
          console.warn('⚠️ Bütçe güncelleme hatası:', error);
        }
      }
    }

    console.log('✅ İşlem başarıyla güncellendi');
    return updatedTransaction;
  } catch (error) {
    console.error('❌ İşlem güncelleme hatası:', error);
    throw error;
  }
};

/**
 * İşlemi sil
 * @param {Object} db - SQLite database instance
 * @param {number} transactionId - İşlem ID
 * @returns {Promise<boolean>} Başarı durumu
 */
export const deleteTransaction = async (db, transactionId) => {
  try {
    console.log('🗑️ İşlem siliniyor:', transactionId);

    // Get transaction for budget calculations
    const transaction = await db.getFirstAsync(
      'SELECT * FROM transactions WHERE id = ?',
      [transactionId]
    );

    if (!transaction) {
      throw new Error('İşlem bulunamadı');
    }

    // Delete transaction
    await db.runAsync('DELETE FROM transactions WHERE id = ?', [transactionId]);

    // Update budget if needed
    if (transaction.budget_id && transaction.type === 'expense') {
      try {
        await updateBudgetSpentAmount(db, transaction.budget_id, transaction.amount, 'subtract');
        console.log('✅ Bütçe harcanan tutarı güncellendi');
      } catch (error) {
        console.warn('⚠️ Bütçe güncelleme hatası:', error);
      }
    }

    console.log('✅ İşlem başarıyla silindi');
    return true;
  } catch (error) {
    console.error('❌ İşlem silme hatası:', error);
    throw error;
  }
};

/**
 * İşlemleri listele
 * @param {Object} db - SQLite database instance
 * @param {Object} options - Filtreleme seçenekleri
 * @param {number} [options.limit] - Maksimum kayıt sayısı
 * @param {number} [options.offset] - Başlangıç pozisyonu
 * @param {string} [options.type] - İşlem tipi filtresi
 * @param {number} [options.category_id] - Kategori ID filtresi
 * @param {number} [options.budget_id] - Bütçe ID filtresi
 * @param {string} [options.startDate] - Başlangıç tarihi
 * @param {string} [options.endDate] - Bitiş tarihi
 * @returns {Promise<Array>} İşlem listesi
 */
export const getTransactions = async (db, options = {}) => {
  try {
    console.log('📋 İşlemler listeleniyor:', options);

    const {
      limit = 50,
      offset = 0,
      type,
      category_id,
      budget_id,
      startDate,
      endDate,
    } = options;

    let query = `
      SELECT 
        t.*,
        c.name as category_name,
        c.icon as category_icon,
        c.color as category_color
      FROM transactions t
      LEFT JOIN categories c ON t.category_id = c.id
      WHERE 1=1
    `;
    const params = [];

    // Add filters
    if (type) {
      query += ' AND t.type = ?';
      params.push(type);
    }

    if (category_id) {
      query += ' AND t.category_id = ?';
      params.push(category_id);
    }

    if (budget_id) {
      query += ' AND t.budget_id = ?';
      params.push(budget_id);
    }

    if (startDate) {
      query += ' AND t.date >= ?';
      params.push(startDate);
    }

    if (endDate) {
      query += ' AND t.date <= ?';
      params.push(endDate);
    }

    query += ' ORDER BY t.date DESC, t.created_at DESC';
    query += ' LIMIT ? OFFSET ?';
    params.push(limit, offset);

    const transactions = await db.getAllAsync(query, params);

    console.log(`✅ ${transactions.length} işlem listelendi`);
    return transactions;
  } catch (error) {
    console.error('❌ İşlem listeleme hatası:', error);
    throw error;
  }
};

/**
 * İşlem detayını getir
 * @param {Object} db - SQLite database instance
 * @param {number} transactionId - İşlem ID
 * @returns {Promise<Object|null>} İşlem detayı
 */
export const getTransaction = async (db, transactionId) => {
  try {
    console.log('🔍 İşlem detayı getiriliyor:', transactionId);

    const transaction = await db.getFirstAsync(
      `SELECT 
        t.*,
        c.name as category_name,
        c.icon as category_icon,
        c.color as category_color,
        b.name as budget_name
      FROM transactions t
      LEFT JOIN categories c ON t.category_id = c.id
      LEFT JOIN budgets b ON t.budget_id = b.id
      WHERE t.id = ?`,
      [transactionId]
    );

    if (transaction) {
      console.log('✅ İşlem detayı bulundu');
    } else {
      console.log('❌ İşlem bulunamadı');
    }

    return transaction;
  } catch (error) {
    console.error('❌ İşlem detayı getirme hatası:', error);
    throw error;
  }
};

/**
 * İşlem istatistiklerini getir
 * @param {Object} db - SQLite database instance
 * @param {Object} options - Filtreleme seçenekleri
 * @returns {Promise<Object>} İstatistik verileri
 */
export const getTransactionStats = async (db, options = {}) => {
  try {
    console.log('📊 İşlem istatistikleri getiriliyor:', options);

    const {
      startDate = new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString(),
      endDate = new Date().toISOString(),
      type,
    } = options;

    let query = `
      SELECT 
        COUNT(*) as total_count,
        SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END) as total_income,
        SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as total_expense,
        AVG(amount) as avg_amount,
        MIN(amount) as min_amount,
        MAX(amount) as max_amount
      FROM transactions 
      WHERE date >= ? AND date <= ?
    `;
    const params = [startDate, endDate];

    if (type) {
      query += ' AND type = ?';
      params.push(type);
    }

    const stats = await db.getFirstAsync(query, params);

    const result = {
      totalCount: stats.total_count || 0,
      totalIncome: stats.total_income || 0,
      totalExpense: stats.total_expense || 0,
      netAmount: (stats.total_income || 0) - (stats.total_expense || 0),
      avgAmount: stats.avg_amount || 0,
      minAmount: stats.min_amount || 0,
      maxAmount: stats.max_amount || 0,
    };

    console.log('✅ İşlem istatistikleri hazırlandı');
    return result;
  } catch (error) {
    console.error('❌ İşlem istatistikleri hatası:', error);
    throw error;
  }
};

/**
 * Bütçe harcanan tutarını güncelle (helper function)
 * @param {Object} db - SQLite database instance
 * @param {number} budgetId - Bütçe ID
 * @param {number} amount - Tutar
 * @param {string} operation - İşlem ('add' | 'subtract')
 * @returns {Promise<void>}
 */
const updateBudgetSpentAmount = async (db, budgetId, amount, operation) => {
  try {
    const sql = operation === 'add' 
      ? 'UPDATE budgets SET spent_amount = spent_amount + ? WHERE id = ?'
      : 'UPDATE budgets SET spent_amount = spent_amount - ? WHERE id = ?';
    
    await db.runAsync(sql, [amount, budgetId]);
    
    // Update progress percentage
    await db.runAsync(`
      UPDATE budgets 
      SET progress_percentage = CASE 
        WHEN total_limit > 0 THEN (spent_amount * 100.0 / total_limit)
        ELSE 0 
      END
      WHERE id = ?
    `, [budgetId]);
    
  } catch (error) {
    console.error('❌ Bütçe tutar güncelleme hatası:', error);
    throw error;
  }
};
