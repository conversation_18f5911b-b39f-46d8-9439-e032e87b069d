import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Switch,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useSQLiteContext } from 'expo-sqlite';
import { Colors } from '../constants/colors';
import ColorSelector from '../components/common/ColorSelector';
import IconSelector from '../components/common/IconSelector';

/**
 * Kategori ekleme/düzenleme ekranı
 * @param {Object} props Component props
 * @param {Object} props.navigation Navigation objesi
 * @param {Object} props.route Route objesi
 */
const CategoryEditScreen = ({ navigation, route }) => {
  const db = useSQLiteContext();
  const { category } = route.params || {};
  
  const [name, setName] = useState(category?.name || '');
  const [type, setType] = useState(category?.type || 'expense');
  const [color, setColor] = useState(category?.color || '#3498db');
  const [icon, setIcon] = useState(category?.icon || 'category');
  const [isDefault, setIsDefault] = useState(category?.is_default === 1);
  const [isLoading, setIsLoading] = useState(false);
  const [showColorSelector, setShowColorSelector] = useState(false);
  const [showIconSelector, setShowIconSelector] = useState(false);

  // Kategoriyi kaydet
  const saveCategory = async () => {
    try {
      if (!name.trim()) {
        Alert.alert('Hata', 'Lütfen bir kategori adı girin.');
        return;
      }
      
      setIsLoading(true);
      
      // Eğer varsayılan kategori olarak işaretlendiyse, aynı türdeki diğer varsayılan kategorileri güncelle
      if (isDefault) {
        await db.runAsync(
          "UPDATE categories SET is_default = 0 WHERE type = ? OR type = 'both'",
          [type]
        );
      }
      
      if (category?.id) {
        // Mevcut kategoriyi güncelle
        await db.runAsync(
          'UPDATE categories SET name = ?, type = ?, color = ?, icon = ?, is_default = ? WHERE id = ?',
          [name, type, color, icon, isDefault ? 1 : 0, category.id]
        );
      } else {
        // Yeni kategori ekle
        await db.runAsync(
          'INSERT INTO categories (name, type, color, icon, is_default) VALUES (?, ?, ?, ?, ?)',
          [name, type, color, icon, isDefault ? 1 : 0]
        );
      }
      
      navigation.goBack();
    } catch (error) {
      console.error('Kategori kaydetme hatası:', error);
      Alert.alert('Hata', 'Kategori kaydedilirken bir hata oluştu.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content}>
        <View style={styles.formGroup}>
          <Text style={styles.label}>Kategori Adı</Text>
          <TextInput
            style={styles.input}
            value={name}
            onChangeText={setName}
            placeholder="Kategori adı girin"
          />
        </View>
        
        <View style={styles.formGroup}>
          <Text style={styles.label}>Kategori Türü</Text>
          <View style={styles.typeSelector}>
            <TouchableOpacity
              style={[
                styles.typeButton,
                type === 'expense' && styles.activeTypeButton
              ]}
              onPress={() => setType('expense')}
            >
              <MaterialIcons
                name="arrow-downward"
                size={20}
                color={type === 'expense' ? '#fff' : Colors.EXPENSE}
              />
              <Text
                style={[
                  styles.typeButtonText,
                  type === 'expense' && styles.activeTypeButtonText
                ]}
              >
                Gider
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.typeButton,
                type === 'income' && styles.activeIncomeButton
              ]}
              onPress={() => setType('income')}
            >
              <MaterialIcons
                name="arrow-upward"
                size={20}
                color={type === 'income' ? '#fff' : Colors.INCOME}
              />
              <Text
                style={[
                  styles.typeButtonText,
                  type === 'income' && styles.activeTypeButtonText
                ]}
              >
                Gelir
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.typeButton,
                type === 'both' && styles.activeBothButton
              ]}
              onPress={() => setType('both')}
            >
              <MaterialIcons
                name="swap-vert"
                size={20}
                color={type === 'both' ? '#fff' : Colors.PRIMARY}
              />
              <Text
                style={[
                  styles.typeButtonText,
                  type === 'both' && styles.activeTypeButtonText
                ]}
              >
                Her İkisi
              </Text>
            </TouchableOpacity>
          </View>
        </View>
        
        <View style={styles.formGroup}>
          <Text style={styles.label}>Renk</Text>
          <TouchableOpacity
            style={styles.colorSelector}
            onPress={() => setShowColorSelector(true)}
          >
            <View style={[styles.colorPreview, { backgroundColor: color }]} />
            <Text style={styles.colorText}>{color}</Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.formGroup}>
          <Text style={styles.label}>İkon</Text>
          <TouchableOpacity
            style={styles.iconSelector}
            onPress={() => setShowIconSelector(true)}
          >
            <View style={[styles.iconPreview, { backgroundColor: color }]}>
              <MaterialIcons name={icon} size={24} color="#fff" />
            </View>
            <Text style={styles.iconText}>{icon}</Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.formGroup}>
          <View style={styles.switchContainer}>
            <Text style={styles.switchLabel}>Varsayılan Kategori</Text>
            <Switch
              value={isDefault}
              onValueChange={setIsDefault}
              trackColor={{ false: '#ddd', true: Colors.PRIMARY }}
              thumbColor="#fff"
            />
          </View>
          <Text style={styles.switchDescription}>
            Bu kategori, yeni işlemler için varsayılan olarak seçilecektir.
          </Text>
        </View>
      </ScrollView>
      
      <View style={styles.footer}>
        <TouchableOpacity
          style={styles.cancelButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.cancelButtonText}>İptal</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.saveButton}
          onPress={saveCategory}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <Text style={styles.saveButtonText}>Kaydet</Text>
          )}
        </TouchableOpacity>
      </View>
      
      {showColorSelector && (
        <ColorSelector
          selectedColor={color}
          onSelectColor={(selectedColor) => {
            setColor(selectedColor);
            setShowColorSelector(false);
          }}
          onClose={() => setShowColorSelector(false)}
        />
      )}
      
      {showIconSelector && (
        <IconSelector
          selectedIcon={icon}
          onSelectIcon={(selectedIcon) => {
            setIcon(selectedIcon);
            setShowIconSelector(false);
          }}
          onClose={() => setShowIconSelector(false)}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  typeSelector: {
    flexDirection: 'row',
  },
  typeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#eee',
    borderRadius: 8,
    marginRight: 8,
    backgroundColor: '#fff',
  },
  activeTypeButton: {
    backgroundColor: Colors.EXPENSE,
    borderColor: Colors.EXPENSE,
  },
  activeIncomeButton: {
    backgroundColor: Colors.INCOME,
    borderColor: Colors.INCOME,
  },
  activeBothButton: {
    backgroundColor: Colors.PRIMARY,
    borderColor: Colors.PRIMARY,
  },
  typeButtonText: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  activeTypeButtonText: {
    color: '#fff',
  },
  colorSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    backgroundColor: '#fff',
  },
  colorPreview: {
    width: 24,
    height: 24,
    borderRadius: 12,
    marginRight: 12,
  },
  colorText: {
    fontSize: 16,
    color: '#333',
  },
  iconSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    backgroundColor: '#fff',
  },
  iconPreview: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  iconText: {
    fontSize: 16,
    color: '#333',
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  switchLabel: {
    fontSize: 16,
    color: '#333',
  },
  switchDescription: {
    fontSize: 14,
    color: '#666',
  },
  footer: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
    backgroundColor: '#fff',
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    marginRight: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#333',
  },
  saveButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
    borderRadius: 8,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
  },
});

export default CategoryEditScreen;
