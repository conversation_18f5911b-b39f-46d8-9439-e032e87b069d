/**
 * Bütçe Özet Raporu Komponenti
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md - Stage 4, Phase 2
 * 
 * <PERSON><PERSON><PERSON>lı bütçe özet raporu ve istatistikler
 * Maksimum 300 satır - Tek sorumluluk prensibi
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../../../context/ThemeContext';
import { formatTRY, formatUSD, formatEUR } from '../../../utils/formatters';

/**
 * Bütçe özet raporu komponenti
 * @param {Object} props - Component props
 * @param {Object} props.reportData - Rapor verileri
 * @param {string} props.currency - Para birimi ('TRY', 'USD', 'EUR')
 * @param {string} props.period - <PERSON><PERSON> dönemi
 * @param {Object} props.theme - <PERSON><PERSON> objesi (opsiyonel, context'ten alınır)
 */
const BudgetSummaryReport = ({ 
  reportData = {}, 
  currency = 'TRY',
  period = 'Bu Ay',
  theme: propTheme 
}) => {
  const { theme, isDarkMode } = useTheme();
  const currentTheme = propTheme || theme;

  /**
   * Para birimi formatı
   * @param {number} value - Formatlanacak değer
   * @returns {string} Formatlanmış değer
   */
  const formatCurrency = (value) => {
    switch (currency) {
      case 'USD':
        return formatUSD(value);
      case 'EUR':
        return formatEUR(value);
      default:
        return formatTRY(value);
    }
  };

  /**
   * Para birimi sembolü
   * @returns {string} Para birimi sembolü
   */
  const getCurrencySymbol = () => {
    switch (currency) {
      case 'USD':
        return '$';
      case 'EUR':
        return '€';
      default:
        return '₺';
    }
  };

  /**
   * Performans skoru rengi
   * @param {number} score - Performans skoru (0-100)
   * @returns {string} Renk kodu
   */
  const getPerformanceColor = (score) => {
    if (score >= 80) return currentTheme.SUCCESS;
    if (score >= 60) return currentTheme.WARNING;
    return currentTheme.ERROR;
  };

  /**
   * Varsayılan rapor verileri
   */
  const defaultData = {
    totalBudget: 0,
    totalSpent: 0,
    totalRemaining: 0,
    budgetUtilization: 0,
    performanceScore: 0,
    categoriesCount: 0,
    overBudgetCategories: 0,
    topCategories: [],
    monthlyComparison: {},
    insights: []
  };

  const data = { ...defaultData, ...reportData };
  const currencySymbol = getCurrencySymbol();
  const performanceColor = getPerformanceColor(data.performanceScore);

  return (
    <View style={[styles.container, { backgroundColor: currentTheme.SURFACE }]}>
      {/* Header */}
      <View style={styles.header}>
        <MaterialIcons name="assessment" size={20} color={currentTheme.PRIMARY} />
        <Text style={[styles.title, { color: currentTheme.TEXT_PRIMARY }]}>
          Bütçe Özet Raporu
        </Text>
        <Text style={[styles.period, { color: currentTheme.TEXT_SECONDARY }]}>
          {period}
        </Text>
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Ana metrikler */}
        <View style={styles.metricsSection}>
          <View style={styles.metricsGrid}>
            <View style={[styles.metricCard, { backgroundColor: currentTheme.PRIMARY + '20' }]}>
              <MaterialIcons name="account-balance-wallet" size={24} color={currentTheme.PRIMARY} />
              <Text style={[styles.metricValue, { color: currentTheme.TEXT_PRIMARY }]}>
                {currencySymbol}{data.totalBudget.toLocaleString('tr-TR')}
              </Text>
              <Text style={[styles.metricLabel, { color: currentTheme.TEXT_SECONDARY }]}>
                Toplam Bütçe
              </Text>
            </View>

            <View style={[styles.metricCard, { backgroundColor: currentTheme.WARNING + '20' }]}>
              <MaterialIcons name="shopping-cart" size={24} color={currentTheme.WARNING} />
              <Text style={[styles.metricValue, { color: currentTheme.TEXT_PRIMARY }]}>
                {currencySymbol}{data.totalSpent.toLocaleString('tr-TR')}
              </Text>
              <Text style={[styles.metricLabel, { color: currentTheme.TEXT_SECONDARY }]}>
                Toplam Harcama
              </Text>
            </View>

            <View style={[styles.metricCard, { backgroundColor: currentTheme.SUCCESS + '20' }]}>
              <MaterialIcons name="savings" size={24} color={currentTheme.SUCCESS} />
              <Text style={[styles.metricValue, { color: currentTheme.TEXT_PRIMARY }]}>
                {currencySymbol}{data.totalRemaining.toLocaleString('tr-TR')}
              </Text>
              <Text style={[styles.metricLabel, { color: currentTheme.TEXT_SECONDARY }]}>
                Kalan Bütçe
              </Text>
            </View>

            <View style={[styles.metricCard, { backgroundColor: performanceColor + '20' }]}>
              <MaterialIcons name="star" size={24} color={performanceColor} />
              <Text style={[styles.metricValue, { color: currentTheme.TEXT_PRIMARY }]}>
                {data.performanceScore}/100
              </Text>
              <Text style={[styles.metricLabel, { color: currentTheme.TEXT_SECONDARY }]}>
                Performans Skoru
              </Text>
            </View>
          </View>
        </View>

        {/* Bütçe kullanım oranı */}
        <View style={styles.utilizationSection}>
          <Text style={[styles.sectionTitle, { color: currentTheme.TEXT_PRIMARY }]}>
            Bütçe Kullanım Oranı
          </Text>
          
          <View style={styles.utilizationCard}>
            <View style={styles.utilizationHeader}>
              <Text style={[styles.utilizationPercentage, { color: currentTheme.TEXT_PRIMARY }]}>
                %{data.budgetUtilization.toFixed(1)}
              </Text>
              <Text style={[styles.utilizationLabel, { color: currentTheme.TEXT_SECONDARY }]}>
                kullanıldı
              </Text>
            </View>
            
            <View style={[styles.progressTrack, { backgroundColor: currentTheme.BORDER }]}>
              <View 
                style={[
                  styles.progressFill,
                  {
                    backgroundColor: data.budgetUtilization > 90 
                      ? currentTheme.ERROR 
                      : data.budgetUtilization > 75 
                        ? currentTheme.WARNING 
                        : currentTheme.SUCCESS,
                    width: `${Math.min(data.budgetUtilization, 100)}%`
                  }
                ]} 
              />
            </View>
            
            <View style={styles.utilizationDetails}>
              <Text style={[styles.utilizationText, { color: currentTheme.TEXT_SECONDARY }]}>
                {currencySymbol}{data.totalSpent.toLocaleString('tr-TR')} / {currencySymbol}{data.totalBudget.toLocaleString('tr-TR')}
              </Text>
            </View>
          </View>
        </View>

        {/* Kategori özeti */}
        <View style={styles.categoriesSection}>
          <Text style={[styles.sectionTitle, { color: currentTheme.TEXT_PRIMARY }]}>
            Kategori Özeti
          </Text>
          
          <View style={styles.categoriesStats}>
            <View style={styles.categoryStatItem}>
              <MaterialIcons name="category" size={20} color={currentTheme.INFO} />
              <Text style={[styles.categoryStatValue, { color: currentTheme.TEXT_PRIMARY }]}>
                {data.categoriesCount}
              </Text>
              <Text style={[styles.categoryStatLabel, { color: currentTheme.TEXT_SECONDARY }]}>
                Toplam Kategori
              </Text>
            </View>

            <View style={styles.categoryStatItem}>
              <MaterialIcons name="warning" size={20} color={currentTheme.ERROR} />
              <Text style={[styles.categoryStatValue, { color: currentTheme.TEXT_PRIMARY }]}>
                {data.overBudgetCategories}
              </Text>
              <Text style={[styles.categoryStatLabel, { color: currentTheme.TEXT_SECONDARY }]}>
                Aşan Kategori
              </Text>
            </View>

            <View style={styles.categoryStatItem}>
              <MaterialIcons name="check-circle" size={20} color={currentTheme.SUCCESS} />
              <Text style={[styles.categoryStatValue, { color: currentTheme.TEXT_PRIMARY }]}>
                {data.categoriesCount - data.overBudgetCategories}
              </Text>
              <Text style={[styles.categoryStatLabel, { color: currentTheme.TEXT_SECONDARY }]}>
                Hedefte Kategori
              </Text>
            </View>
          </View>
        </View>

        {/* En çok harcama yapılan kategoriler */}
        {data.topCategories.length > 0 && (
          <View style={styles.topCategoriesSection}>
            <Text style={[styles.sectionTitle, { color: currentTheme.TEXT_PRIMARY }]}>
              En Çok Harcama Yapılan Kategoriler
            </Text>
            
            {data.topCategories.slice(0, 5).map((category, index) => (
              <View key={category.id} style={styles.topCategoryItem}>
                <View style={styles.topCategoryLeft}>
                  <Text style={[styles.topCategoryRank, { color: currentTheme.PRIMARY }]}>
                    {index + 1}
                  </Text>
                  <MaterialIcons 
                    name={category.icon || 'category'} 
                    size={20} 
                    color={currentTheme.TEXT_PRIMARY} 
                  />
                  <Text style={[styles.topCategoryName, { color: currentTheme.TEXT_PRIMARY }]}>
                    {category.name}
                  </Text>
                </View>
                
                <View style={styles.topCategoryRight}>
                  <Text style={[styles.topCategoryAmount, { color: currentTheme.TEXT_PRIMARY }]}>
                    {currencySymbol}{category.amount.toLocaleString('tr-TR')}
                  </Text>
                  <Text style={[styles.topCategoryPercentage, { color: currentTheme.TEXT_SECONDARY }]}>
                    %{category.percentage.toFixed(1)}
                  </Text>
                </View>
              </View>
            ))}
          </View>
        )}

        {/* Aylık karşılaştırma */}
        {data.monthlyComparison.previousMonth && (
          <View style={styles.comparisonSection}>
            <Text style={[styles.sectionTitle, { color: currentTheme.TEXT_PRIMARY }]}>
              Önceki Ay Karşılaştırması
            </Text>
            
            <View style={styles.comparisonGrid}>
              <View style={styles.comparisonItem}>
                <Text style={[styles.comparisonLabel, { color: currentTheme.TEXT_SECONDARY }]}>
                  Harcama Değişimi
                </Text>
                <View style={styles.comparisonValue}>
                  <MaterialIcons 
                    name={data.monthlyComparison.spendingChange > 0 ? 'arrow-upward' : 'arrow-downward'} 
                    size={16} 
                    color={data.monthlyComparison.spendingChange > 0 ? currentTheme.ERROR : currentTheme.SUCCESS} 
                  />
                  <Text style={[
                    styles.comparisonText,
                    { color: data.monthlyComparison.spendingChange > 0 ? currentTheme.ERROR : currentTheme.SUCCESS }
                  ]}>
                    %{Math.abs(data.monthlyComparison.spendingChange).toFixed(1)}
                  </Text>
                </View>
              </View>

              <View style={styles.comparisonItem}>
                <Text style={[styles.comparisonLabel, { color: currentTheme.TEXT_SECONDARY }]}>
                  Tasarruf Değişimi
                </Text>
                <View style={styles.comparisonValue}>
                  <MaterialIcons 
                    name={data.monthlyComparison.savingsChange > 0 ? 'arrow-upward' : 'arrow-downward'} 
                    size={16} 
                    color={data.monthlyComparison.savingsChange > 0 ? currentTheme.SUCCESS : currentTheme.ERROR} 
                  />
                  <Text style={[
                    styles.comparisonText,
                    { color: data.monthlyComparison.savingsChange > 0 ? currentTheme.SUCCESS : currentTheme.ERROR }
                  ]}>
                    %{Math.abs(data.monthlyComparison.savingsChange).toFixed(1)}
                  </Text>
                </View>
              </View>
            </View>
          </View>
        )}

        {/* Önemli bulgular */}
        {data.insights.length > 0 && (
          <View style={styles.insightsSection}>
            <Text style={[styles.sectionTitle, { color: currentTheme.TEXT_PRIMARY }]}>
              Önemli Bulgular
            </Text>
            
            {data.insights.map((insight, index) => (
              <View 
                key={index} 
                style={[
                  styles.insightItem,
                  { backgroundColor: insight.type === 'warning' ? currentTheme.WARNING + '10' : currentTheme.INFO + '10' }
                ]}
              >
                <MaterialIcons 
                  name={insight.type === 'warning' ? 'warning' : 'info'} 
                  size={16} 
                  color={insight.type === 'warning' ? currentTheme.WARNING : currentTheme.INFO} 
                />
                <Text style={[styles.insightText, { color: currentTheme.TEXT_SECONDARY }]}>
                  {insight.message}
                </Text>
              </View>
            ))}
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '500',
    flex: 1,
  },
  period: {
    fontSize: 12,
  },
  metricsSection: {
    marginBottom: 20,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  metricCard: {
    width: '48%',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginBottom: 8,
  },
  metricValue: {
    fontSize: 16,
    fontWeight: 'bold',
    marginVertical: 4,
  },
  metricLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  utilizationSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 12,
  },
  utilizationCard: {
    alignItems: 'center',
  },
  utilizationHeader: {
    alignItems: 'center',
    marginBottom: 8,
  },
  utilizationPercentage: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  utilizationLabel: {
    fontSize: 12,
  },
  progressTrack: {
    width: '100%',
    height: 8,
    borderRadius: 4,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  utilizationDetails: {
    alignItems: 'center',
  },
  utilizationText: {
    fontSize: 12,
  },
  categoriesSection: {
    marginBottom: 20,
  },
  categoriesStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  categoryStatItem: {
    alignItems: 'center',
  },
  categoryStatValue: {
    fontSize: 18,
    fontWeight: 'bold',
    marginVertical: 4,
  },
  categoryStatLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  topCategoriesSection: {
    marginBottom: 20,
  },
  topCategoryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  topCategoryLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 8,
  },
  topCategoryRank: {
    fontSize: 14,
    fontWeight: 'bold',
    minWidth: 20,
  },
  topCategoryName: {
    fontSize: 14,
    flex: 1,
  },
  topCategoryRight: {
    alignItems: 'flex-end',
  },
  topCategoryAmount: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  topCategoryPercentage: {
    fontSize: 12,
  },
  comparisonSection: {
    marginBottom: 20,
  },
  comparisonGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  comparisonItem: {
    alignItems: 'center',
  },
  comparisonLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  comparisonValue: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  comparisonText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  insightsSection: {
    marginBottom: 20,
  },
  insightItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    gap: 8,
  },
  insightText: {
    fontSize: 13,
    flex: 1,
    lineHeight: 18,
  },
});

export default BudgetSummaryReport;
