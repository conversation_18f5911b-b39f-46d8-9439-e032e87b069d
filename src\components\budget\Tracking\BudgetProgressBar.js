/**
 * <PERSON><PERSON><PERSON><PERSON>e İlerleme Çubuğu Komponenti
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md - Stage 3, Phase 2
 * 
 * Detaylı bütçe ilerleme göstergesi
 * Maksimum 200 satır - Tek sorumluluk prensibi
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../../../context/ThemeContext';

/**
 * Bütçe ilerleme çubuğu komponenti
 * @param {Object} props - Component props
 * @param {number} props.spent - Harcanan miktar
 * @param {number} props.limit - Limit miktar
 * @param {string} props.label - Çubuk etiketi
 * @param {boolean} props.showPercentage - Yüzde gösterilsin mi
 * @param {boolean} props.showAmounts - Miktarlar gösterilsin mi
 * @param {string} props.currency - Para birimi ('TRY', 'USD', 'EUR')
 * @param {Object} props.theme - <PERSON><PERSON> obje<PERSON> (opsiyonel, context'ten alınır)
 */
const BudgetProgressBar = ({ 
  spent = 0, 
  limit = 0, 
  label = '',
  showPercentage = true,
  showAmounts = true,
  currency = 'TRY',
  theme: propTheme 
}) => {
  const { theme, isDarkMode } = useTheme();
  const currentTheme = propTheme || theme;

  /**
   * Para birimi sembolü
   * @returns {string} Para birimi sembolü
   */
  const getCurrencySymbol = () => {
    switch (currency) {
      case 'USD':
        return '$';
      case 'EUR':
        return '€';
      default:
        return '₺';
    }
  };

  /**
   * Harcama yüzdesi hesaplama
   * @returns {number} Harcama yüzdesi (0-100)
   */
  const getSpentPercentage = () => {
    if (limit === 0) return 0;
    return Math.min((spent / limit) * 100, 100);
  };

  /**
   * Durum rengi belirleme
   * @returns {string} Durum rengi
   */
  const getProgressColor = () => {
    const percentage = getSpentPercentage();
    
    if (percentage >= 100) return currentTheme.ERROR;
    if (percentage >= 90) return currentTheme.WARNING;
    if (percentage >= 75) return currentTheme.INFO;
    return currentTheme.SUCCESS;
  };

  /**
   * Durum ikonu belirleme
   * @returns {string} İkon adı
   */
  const getStatusIcon = () => {
    const percentage = getSpentPercentage();
    
    if (percentage >= 100) return 'error';
    if (percentage >= 90) return 'warning';
    if (percentage >= 75) return 'info';
    return 'check-circle';
  };

  /**
   * Kalan miktar hesaplama
   * @returns {number} Kalan miktar
   */
  const getRemainingAmount = () => {
    return Math.max(limit - spent, 0);
  };

  const spentPercentage = getSpentPercentage();
  const progressColor = getProgressColor();
  const statusIcon = getStatusIcon();
  const remainingAmount = getRemainingAmount();
  const currencySymbol = getCurrencySymbol();
  const isOverBudget = spent > limit;

  return (
    <View style={[styles.container, { backgroundColor: currentTheme.SURFACE }]}>
      {/* Header */}
      {label && (
        <View style={styles.header}>
          <Text style={[styles.label, { color: currentTheme.TEXT_PRIMARY }]}>
            {label}
          </Text>
          <MaterialIcons name={statusIcon} size={16} color={progressColor} />
        </View>
      )}

      {/* İlerleme çubuğu */}
      <View style={styles.progressContainer}>
        <View style={[styles.progressTrack, { backgroundColor: currentTheme.BORDER }]}>
          <View 
            style={[
              styles.progressFill,
              {
                backgroundColor: progressColor,
                width: `${Math.min(spentPercentage, 100)}%`
              }
            ]} 
          />
          
          {/* Aşım göstergesi */}
          {isOverBudget && (
            <View 
              style={[
                styles.overBudgetIndicator,
                {
                  backgroundColor: currentTheme.ERROR + '40',
                  width: `${Math.min((spent / limit) * 100 - 100, 50)}%`,
                  left: '100%'
                }
              ]} 
            />
          )}
        </View>

        {/* Yüzde göstergesi */}
        {showPercentage && (
          <Text style={[styles.percentageText, { color: progressColor }]}>
            %{spentPercentage.toFixed(1)}
          </Text>
        )}
      </View>

      {/* Miktar bilgileri */}
      {showAmounts && (
        <View style={styles.amountInfo}>
          <View style={styles.amountItem}>
            <Text style={[styles.amountLabel, { color: currentTheme.TEXT_SECONDARY }]}>
              Harcanan
            </Text>
            <Text style={[styles.amountValue, { color: progressColor }]}>
              {currencySymbol}{spent.toLocaleString('tr-TR')}
            </Text>
          </View>

          <View style={styles.amountItem}>
            <Text style={[styles.amountLabel, { color: currentTheme.TEXT_SECONDARY }]}>
              Limit
            </Text>
            <Text style={[styles.amountValue, { color: currentTheme.TEXT_PRIMARY }]}>
              {currencySymbol}{limit.toLocaleString('tr-TR')}
            </Text>
          </View>

          <View style={styles.amountItem}>
            <Text style={[styles.amountLabel, { color: currentTheme.TEXT_SECONDARY }]}>
              {isOverBudget ? 'Aşım' : 'Kalan'}
            </Text>
            <Text style={[
              styles.amountValue, 
              { color: isOverBudget ? currentTheme.ERROR : currentTheme.SUCCESS }
            ]}>
              {currencySymbol}{(isOverBudget ? spent - limit : remainingAmount).toLocaleString('tr-TR')}
            </Text>
          </View>
        </View>
      )}

      {/* Durum mesajı */}
      {isOverBudget && (
        <View style={[styles.statusMessage, { backgroundColor: currentTheme.ERROR + '20' }]}>
          <MaterialIcons name="warning" size={14} color={currentTheme.ERROR} />
          <Text style={[styles.statusText, { color: currentTheme.ERROR }]}>
            Bütçe {currencySymbol}{(spent - limit).toLocaleString('tr-TR')} aşıldı
          </Text>
        </View>
      )}

      {!isOverBudget && spentPercentage >= 90 && (
        <View style={[styles.statusMessage, { backgroundColor: currentTheme.WARNING + '20' }]}>
          <MaterialIcons name="info" size={14} color={currentTheme.WARNING} />
          <Text style={[styles.statusText, { color: currentTheme.WARNING }]}>
            Bütçe sınırına yaklaşıldı
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 12,
  },
  progressTrack: {
    flex: 1,
    height: 12,
    borderRadius: 6,
    overflow: 'hidden',
    position: 'relative',
  },
  progressFill: {
    height: '100%',
    borderRadius: 6,
  },
  overBudgetIndicator: {
    position: 'absolute',
    top: 0,
    height: '100%',
    borderRadius: 6,
  },
  percentageText: {
    fontSize: 14,
    fontWeight: 'bold',
    minWidth: 50,
    textAlign: 'right',
  },
  amountInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  amountItem: {
    alignItems: 'center',
  },
  amountLabel: {
    fontSize: 12,
    marginBottom: 2,
  },
  amountValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  statusMessage: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 10,
    borderRadius: 6,
    gap: 6,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
});

export default BudgetProgressBar;
