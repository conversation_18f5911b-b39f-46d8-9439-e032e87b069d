/**
 * Vardiya planlaması tablosunu oluşturan migrasyon
 *
 * @param {object} db - SQLite veritabanı nesnesi
 * @returns {Promise<void>}
 */
export const migrateWorkShiftSchedules = async (db) => {
  try {
    console.log('Vardiya planlaması tablosu migrasyonu başlatılıyor...');

    // Vardiya planlaması tablosunu oluştur
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS work_shift_schedules (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        shift_type_id INTEGER NOT NULL,
        start_date TEXT NOT NULL,
        end_date TEXT,
        repeat_type TEXT NOT NULL, -- daily, weekly, monthly, custom
        repeat_interval INTEGER DEFAULT 1,
        repeat_days TEXT, -- Haft<PERSON>ık tekrar için gün listesi (1,2,3,4,5,6,7)
        repeat_months TEXT, -- <PERSON><PERSON><PERSON><PERSON> tekrar için ay listesi (1,2,3,...,12)
        repeat_week_of_month INTEGER, -- <PERSON><PERSON><PERSON><PERSON> kaçıncı haftası (1-5)
        repeat_day_of_week INTEGER, -- Haftanın hangi günü (1-7)
        repeat_day_of_month INTEGER, -- Ayın hangi günü (1-31)
        is_active INTEGER DEFAULT 1,
        notes TEXT,
        created_at TEXT DEFAULT '2023-01-01 00:00:00',
        updated_at TEXT DEFAULT '2023-01-01 00:00:00',
        FOREIGN KEY (shift_type_id) REFERENCES work_shift_types (id) ON DELETE CASCADE
      )
    `);

    // work_shift_notifications tablosu ayrı migration'da oluşturuluyor

    // work_shifts tablosuna shift_type_id ve schedule_id sütunlarını ekle
    const workShiftsColumns = await db.getAllAsync(`PRAGMA table_info(work_shifts)`);
    const columnNames = workShiftsColumns.map(col => col.name);

    if (!columnNames.includes('shift_type_id')) {
      await db.execAsync(`ALTER TABLE work_shifts ADD COLUMN shift_type_id INTEGER REFERENCES work_shift_types(id)`);
    }

    if (!columnNames.includes('schedule_id')) {
      await db.execAsync(`ALTER TABLE work_shifts ADD COLUMN schedule_id INTEGER REFERENCES work_shift_schedules(id)`);
    }

    console.log('Vardiya planlaması tablosu migrasyonu tamamlandı.');
  } catch (error) {
    console.error('Vardiya planlaması tablosu migrasyonu hatası:', error);
    throw error;
  }
};
