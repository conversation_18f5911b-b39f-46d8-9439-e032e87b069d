/**
 * Gelişmiş Bütçeler Ekranı
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md Stage 1 implementasyonu
 * 
 * Yeni bütçe yönetimi sistemi ile gelişmiş bütçe ekranı
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  Alert,
  StatusBar
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useFocusEffect } from '@react-navigation/native';
import { useAppContext } from '../context/AppContext';
import { SafeAreaView } from 'react-native-safe-area-context';

// Yeni bütçe servisleri
import {
  getBudgets,
  deleteBudget,
  checkBudgetAlerts
} from '../services/budget';

// Yeni bütçe bileşenleri
import BudgetList from '../components/budget/BudgetList';

/**
 * <PERSON><PERSON>şmiş Bütçeler Ekranı
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @returns {JSX.Element} Gelişmiş Bütçeler Ekranı
 */
export default function BudgetsScreenEnhanced({ navigation }) {
  const db = useSQLiteContext();
  const { theme, isDarkMode } = useAppContext();

  // State yönetimi
  const [budgets, setBudgets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [alerts, setAlerts] = useState([]);

  // Verileri yükle
  const loadData = useCallback(async () => {
    try {
      setLoading(true);

      // Bütçeleri getir
      const budgetList = await getBudgets(db, {
        active_only: false // Tüm bütçeleri getir
      });
      setBudgets(budgetList);

      // Uyarıları kontrol et
      const budgetAlerts = await checkBudgetAlerts(db);
      setAlerts(budgetAlerts);

      console.log(`✅ ${budgetList.length} bütçe yüklendi`);
      if (budgetAlerts.length > 0) {
        console.log(`⚠️ ${budgetAlerts.length} uyarı bulundu`);
      }

    } catch (error) {
      console.error('❌ Bütçe verileri yükleme hatası:', error);
      Alert.alert(
        'Hata',
        'Bütçe verileri yüklenirken bir hata oluştu.',
        [{ text: 'Tamam' }]
      );
    } finally {
      setLoading(false);
    }
  }, [db]);

  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [loadData])
  );

  // Yenileme işlemi
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  // Bütçe detaylarını görüntüle
  const handleBudgetPress = (budget) => {
    navigation.navigate('BudgetDetail', { 
      budgetId: budget.id,
      budgetName: budget.name 
    });
  };

  // Yeni bütçe ekle
  const handleAddBudget = () => {
    navigation.navigate('BudgetForm', {
      mode: 'create'
    });
  };

  // Bütçeyi düzenle
  const handleEditBudget = (budget) => {
    navigation.navigate('BudgetForm', {
      mode: 'edit',
      budgetId: budget.id,
      budget: budget
    });
  };

  // Bütçeyi sil
  const handleDeleteBudget = (budget) => {
    Alert.alert(
      'Bütçeyi Sil',
      `"${budget.name}" bütçesini silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.`,
      [
        {
          text: 'İptal',
          style: 'cancel'
        },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteBudget(db, budget.id);
              Alert.alert('Başarılı', 'Bütçe başarıyla silindi.');
              loadData(); // Listeyi yenile
            } catch (error) {
              console.error('❌ Bütçe silme hatası:', error);
              Alert.alert('Hata', 'Bütçe silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };

  // Uyarıları göster
  useEffect(() => {
    if (alerts.length > 0) {
      const highPriorityAlerts = alerts.filter(alert => alert.severity === 'high');
      if (highPriorityAlerts.length > 0) {
        const alert = highPriorityAlerts[0];
        Alert.alert(
          'Bütçe Uyarısı',
          alert.message,
          [{ text: 'Tamam' }]
        );
      }
    }
  }, [alerts]);

  return (
    <SafeAreaView 
      style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
      edges={['top']}
    >
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor={theme.BACKGROUND}
      />

      <BudgetList
        budgets={budgets}
        loading={loading}
        refreshing={refreshing}
        onRefresh={handleRefresh}
        onBudgetPress={handleBudgetPress}
        onEditBudget={handleEditBudget}
        onDeleteBudget={handleDeleteBudget}
        onAddBudget={handleAddBudget}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

// Navigation options
BudgetsScreenEnhanced.navigationOptions = {
  title: 'Bütçe Yönetimi',
  headerShown: false, // SafeAreaView kullandığımız için header'ı gizle
};
