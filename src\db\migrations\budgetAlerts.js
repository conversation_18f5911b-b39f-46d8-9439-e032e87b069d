/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> uyarıları için migrasyon
 *
 * @param {object} db - SQLite veritabanı nesnesi
 * @returns {Promise<void>}
 */
export const migrateBudgetAlerts = async (db) => {
  try {
    // budget_alerts tablosunu oluştur
    const hasBudgetAlertsTable = await db.getFirstAsync(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='budget_alerts'
    `);

    if (!hasBudgetAlertsTable) {
      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS budget_alerts (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          budget_id INTEGER NOT NULL,
          threshold_type TEXT NOT NULL DEFAULT 'percentage',
          threshold_value DECIMAL(10,2) NOT NULL,
          is_active INTEGER DEFAULT 1,
          notification_type TEXT NOT NULL DEFAULT 'once',
          notification_message TEXT,
          is_triggered INTEGER DEFAULT 0,
          last_triggered_at DATETIME,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (budget_id) REFERENCES budgets (id) ON DELETE CASCADE
        )
      `);
    } else {
      // Tabloda eksik sütunlar var mı kontrol et
      const columns = await db.getAllAsync(`PRAGMA table_info(budget_alerts)`);

      // Sütun adlarını bir diziye dönüştür
      const columnNames = columns.map(col => col.name);

      // Eksik sütunları kontrol et ve ekle
      if (!columnNames.includes('threshold_type')) {
        await db.execAsync(`ALTER TABLE budget_alerts ADD COLUMN threshold_type TEXT NOT NULL DEFAULT 'percentage'`);
      }

      if (!columnNames.includes('threshold_value')) {
        await db.execAsync(`ALTER TABLE budget_alerts ADD COLUMN threshold_value DECIMAL(10,2) NOT NULL DEFAULT 80`);
      }

      if (!columnNames.includes('is_active')) {
        await db.execAsync(`ALTER TABLE budget_alerts ADD COLUMN is_active INTEGER DEFAULT 1`);
      }

      if (!columnNames.includes('notification_type')) {
        await db.execAsync(`ALTER TABLE budget_alerts ADD COLUMN notification_type TEXT NOT NULL DEFAULT 'once'`);
      }

      if (!columnNames.includes('notification_message')) {
        await db.execAsync(`ALTER TABLE budget_alerts ADD COLUMN notification_message TEXT`);
      }

      if (!columnNames.includes('is_triggered')) {
        await db.execAsync(`ALTER TABLE budget_alerts ADD COLUMN is_triggered INTEGER DEFAULT 0`);
      }

      if (!columnNames.includes('last_triggered_at')) {
        await db.execAsync(`ALTER TABLE budget_alerts ADD COLUMN last_triggered_at DATETIME`);
      }

      if (!columnNames.includes('updated_at')) {
        await db.execAsync(`ALTER TABLE budget_alerts ADD COLUMN updated_at DATETIME DEFAULT CURRENT_TIMESTAMP`);
      }
    }
  } catch (error) {
    console.error('Bütçe uyarıları migrasyon hatası:', error);
    throw error;
  }
};
