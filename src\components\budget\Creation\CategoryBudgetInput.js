/**
 * <PERSON><PERSON><PERSON> Bütçe Girişi Komponenti
 * BUDGET_MANAGEMENT_REDESIGN_PLAN.md - Stage 2, Phase 2
 * 
 * Her kategori için ayrı bütçe limit girişi
 * Maksimum 250 satır - Tek sorumluluk prensibi
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../../../context/ThemeContext';
import { formatTRY, formatUSD, formatEUR } from '../../../utils/formatters';

/**
 * Kate<PERSON>i bütçe girişi komponenti
 * @param {Object} props - Component props
 * @param {Object} props.category - Kate<PERSON>i objesi
 * @param {number} props.amount - Mevcut miktar
 * @param {string} props.currency - Para birimi ('TRY', 'USD', 'EUR')
 * @param {Function} props.onAmountChange - Miktar değ<PERSON>im callback fonksiyonu
 * @param {Function} props.onRemove - Kategori kaldırma callback fonksiyonu
 * @param {Object} props.theme - Tema objesi (opsiyonel, context'ten alınır)
 */
const CategoryBudgetInput = ({ 
  category, 
  amount, 
  currency, 
  onAmountChange, 
  onRemove,
  theme: propTheme 
}) => {
  const { theme } = useTheme();
  const currentTheme = propTheme || theme;

  // State yönetimi
  const [inputValue, setInputValue] = useState(amount ? amount.toString() : '');
  const [isFocused, setIsFocused] = useState(false);
  const [showPercentage, setShowPercentage] = useState(false);

  // Amount prop değiştiğinde input'u güncelle
  useEffect(() => {
    if (amount !== undefined && amount !== null) {
      setInputValue(amount.toString());
    }
  }, [amount]);

  /**
   * Miktar değişim işleyicisi
   * @param {string} text - Girilen metin
   */
  const handleAmountChange = (text) => {
    // Sadece sayı ve nokta karakterlerine izin ver
    const cleanText = text.replace(/[^0-9.,]/g, '');
    
    // Virgülü noktaya çevir (Türkçe klavye desteği)
    const normalizedText = cleanText.replace(',', '.');
    
    // Birden fazla nokta kontrolü
    const dotCount = (normalizedText.match(/\./g) || []).length;
    if (dotCount > 1) {
      return;
    }

    setInputValue(normalizedText);

    // Geçerli sayı kontrolü ve callback çağırma
    const numericValue = parseFloat(normalizedText);
    if (!isNaN(numericValue) && numericValue >= 0) {
      if (onAmountChange) {
        onAmountChange(category.id, numericValue);
      }
    } else if (normalizedText === '' || normalizedText === '.') {
      if (onAmountChange) {
        onAmountChange(category.id, 0);
      }
    }
  };

  /**
   * Kategori kaldırma işleyicisi
   */
  const handleRemove = () => {
    Alert.alert(
      'Kategoriyi Kaldır',
      `${category.name} kategorisini bütçeden kaldırmak istediğinizden emin misiniz?`,
      [
        {
          text: 'İptal',
          style: 'cancel',
        },
        {
          text: 'Kaldır',
          style: 'destructive',
          onPress: () => {
            if (onRemove) {
              onRemove(category.id);
            }
          },
        },
      ]
    );
  };

  /**
   * Hızlı miktar seçim işleyicisi
   * @param {number} quickAmount - Hızlı seçim miktarı
   */
  const handleQuickAmountSelect = (quickAmount) => {
    setInputValue(quickAmount.toString());
    if (onAmountChange) {
      onAmountChange(category.id, quickAmount);
    }
  };

  /**
   * Para birimi formatı
   * @param {number} value - Formatlanacak değer
   * @param {string} currencyCode - Para birimi kodu
   * @returns {string} Formatlanmış değer
   */
  const formatCurrency = (value, currencyCode) => {
    switch (currencyCode) {
      case 'USD':
        return formatUSD(value);
      case 'EUR':
        return formatEUR(value);
      default:
        return formatTRY(value);
    }
  };

  /**
   * Para birimi sembolü
   * @param {string} currencyCode - Para birimi kodu
   * @returns {string} Para birimi sembolü
   */
  const getCurrencySymbol = (currencyCode) => {
    switch (currencyCode) {
      case 'USD':
        return '$';
      case 'EUR':
        return '€';
      default:
        return '₺';
    }
  };

  // Hızlı seçim miktarları (para birimine göre)
  const getQuickAmounts = () => {
    switch (currency) {
      case 'USD':
        return [50, 100, 200];
      case 'EUR':
        return [50, 100, 200];
      default: // TRY
        return [500, 1000, 2000];
    }
  };

  const quickAmounts = getQuickAmounts();
  const currencySymbol = getCurrencySymbol(currency);

  return (
    <View style={[styles.container, { backgroundColor: currentTheme.SURFACE }]}>
      {/* Kategori header */}
      <View style={styles.categoryHeader}>
        <View style={styles.categoryInfo}>
          <View style={[styles.categoryIcon, { backgroundColor: currentTheme.PRIMARY + '20' }]}>
            <MaterialIcons 
              name={category.icon || 'category'} 
              size={20} 
              color={currentTheme.PRIMARY} 
            />
          </View>
          <Text style={[styles.categoryName, { color: currentTheme.TEXT_PRIMARY }]}>
            {category.name}
          </Text>
        </View>

        <TouchableOpacity
          style={styles.removeButton}
          onPress={handleRemove}
        >
          <MaterialIcons name="close" size={20} color={currentTheme.ERROR} />
        </TouchableOpacity>
      </View>

      {/* Miktar girişi */}
      <View style={[
        styles.inputContainer,
        {
          backgroundColor: currentTheme.BACKGROUND,
          borderColor: isFocused ? currentTheme.PRIMARY : currentTheme.BORDER,
        }
      ]}>
        <Text style={[styles.currencySymbol, { color: currentTheme.PRIMARY }]}>
          {currencySymbol}
        </Text>

        <TextInput
          style={[styles.amountInput, { color: currentTheme.TEXT_PRIMARY }]}
          value={inputValue}
          onChangeText={handleAmountChange}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholder="0"
          placeholderTextColor={currentTheme.TEXT_SECONDARY}
          keyboardType="numeric"
          maxLength={8}
        />
      </View>

      {/* Formatlanmış değer */}
      {amount > 0 && (
        <View style={styles.formattedValue}>
          <Text style={[styles.formattedText, { color: currentTheme.TEXT_SECONDARY }]}>
            {formatCurrency(amount, currency)}
          </Text>
        </View>
      )}

      {/* Hızlı seçim butonları */}
      <View style={styles.quickSelection}>
        {quickAmounts.map((quickAmount) => (
          <TouchableOpacity
            key={quickAmount}
            style={[
              styles.quickButton,
              {
                backgroundColor: amount === quickAmount 
                  ? currentTheme.PRIMARY 
                  : currentTheme.BACKGROUND,
                borderColor: currentTheme.BORDER,
              }
            ]}
            onPress={() => handleQuickAmountSelect(quickAmount)}
          >
            <Text style={[
              styles.quickButtonText,
              {
                color: amount === quickAmount 
                  ? currentTheme.WHITE 
                  : currentTheme.TEXT_PRIMARY
              }
            ]}>
              {currencySymbol}{quickAmount.toLocaleString('tr-TR')}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  categoryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 8,
  },
  categoryIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '500',
    flex: 1,
  },
  removeButton: {
    padding: 4,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 8,
    borderWidth: 1,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginBottom: 8,
  },
  currencySymbol: {
    fontSize: 18,
    fontWeight: 'bold',
    marginRight: 8,
  },
  amountInput: {
    flex: 1,
    fontSize: 18,
    fontWeight: '500',
    textAlign: 'center',
    paddingVertical: 4,
  },
  formattedValue: {
    alignItems: 'center',
    marginBottom: 8,
  },
  formattedText: {
    fontSize: 12,
  },
  quickSelection: {
    flexDirection: 'row',
    gap: 8,
  },
  quickButton: {
    flex: 1,
    paddingHorizontal: 8,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    alignItems: 'center',
  },
  quickButtonText: {
    fontSize: 12,
    fontWeight: '500',
  },
});

export default CategoryBudgetInput;
