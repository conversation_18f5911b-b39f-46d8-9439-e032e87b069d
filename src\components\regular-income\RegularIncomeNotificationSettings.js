import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, TextInput, Switch, Platform, Dimensions, Animated } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { format, isValid } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Colors } from '../../constants/colors';

const { width: screenWidth } = Dimensions.get('window');

/**
 * Güvenli tarih formatting fonksiyonu - geçersiz tarihleri ele alır
 * @param {Date|string} date - Format edilecek tarih
 * @param {string} formatStr - Format string'i
 * @returns {string} Formatlanmış tarih veya hata mesajı
 */
const safeFormatTime = (date, formatStr = 'HH:mm') => {
  try {
    const dateObj = date instanceof Date ? date : new Date(date);
    if (!isValid(dateObj)) {
      return '09:00';
    }
    return format(dateObj, formatStr);
  } catch (error) {
    console.warn('Time formatting error:', error);
    return '09:00';
  }
};

/**
 * Düzenli gelir bildirim ayarları bileşeni
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {boolean} props.notificationEnabled - Bildirimler etkin mi?
 * @param {Function} props.setNotificationEnabled - Bildirim etkinlik değiştirme fonksiyonu
 * @param {string} props.notificationDaysBefore - Bildirim gün sayısı
 * @param {Function} props.setNotificationDaysBefore - Bildirim gün sayısı değiştirme fonksiyonu
 * @param {Date} props.notificationTime - Bildirim saati
 * @param {Function} props.setNotificationTime - Bildirim saati değiştirme fonksiyonu
 * @param {boolean} props.showTimePicker - Saat seçici gösteriliyor mu?
 * @param {Function} props.setShowTimePicker - Saat seçici görünürlük değiştirme fonksiyonu
 * @param {Function} props.handleTimeChange - Saat değişimi işleyici
 * @param {Function} props.getInputStyle - Input stil işleyici
 * @param {Function} props.handleInputFocus - Input odak işleyici
 * @param {Function} props.handleInputBlur - Input odak kaybı işleyici
 * @param {Function} props.getFieldErrorText - Hata metni işleyici
 * @param {Object} props.fieldErrors - Alan hataları
 * @returns {JSX.Element} Bildirim ayarları bileşeni
 */
const RegularIncomeNotificationSettings = ({
  notificationEnabled,
  setNotificationEnabled,
  notificationDaysBefore,
  setNotificationDaysBefore,
  notificationTime,
  setNotificationTime,
  showTimePicker,
  setShowTimePicker,
  handleTimeChange,
  getInputStyle = () => ({}),
  handleInputFocus = () => {},
  handleInputBlur = () => {},
  getFieldErrorText = () => null,
  fieldErrors = {}
}) => {
  
  /**
   * Bildirim gün sayısını ayarla
   * @param {string} action - "increment" veya "decrement"
   */
  const adjustNotificationDays = (action) => {
    const currentDays = parseInt(notificationDaysBefore, 10) || 0;
    const newDays = action === 'increment' 
      ? Math.min(currentDays + 1, 30) 
      : Math.max(currentDays - 1, 0);
    setNotificationDaysBefore(String(newDays));
  };
  
  return (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Bildirim Ayarları</Text>
      
      <View style={styles.notificationSwitchContainer}>
        <View style={styles.notificationSwitchContent}>
          <View style={styles.notificationIconContainer}>
            <MaterialIcons 
              name={notificationEnabled ? "notifications-active" : "notifications-off"} 
              size={30} 
              color={notificationEnabled ? Colors.PRIMARY : Colors.GRAY_500} 
            />
          </View>
          
          <View style={styles.notificationTextContainer}>
            <Text 
              style={[
                styles.notificationTitle, 
                { color: notificationEnabled ? Colors.PRIMARY : Colors.GRAY_700 }
              ]}
            >
              {notificationEnabled ? 'Bildirimler Açık' : 'Bildirimler Kapalı'}
            </Text>
            <Text 
              style={[
                styles.notificationDescription,
                { color: notificationEnabled ? Colors.GRAY_800 : Colors.GRAY_600 }
              ]}            >
              {notificationEnabled 
                ? 'Ödeme günü yaklaştığında size bildirim göndereceğiz.' 
                : 'Ödeme bildirimleri devre dışı bırakıldı.'}
            </Text>
          </View>
          
          <Switch
            value={notificationEnabled}
            onValueChange={setNotificationEnabled}
            trackColor={{ false: Colors.GRAY_300, true: Colors.PRIMARY + '70' }}
            thumbColor={notificationEnabled ? Colors.PRIMARY : Colors.GRAY_500}
            ios_backgroundColor={Colors.GRAY_300}
            accessibilityLabel="Bildirim ayarını aç/kapat"
            accessibilityHint={notificationEnabled ? 'Bildirimleri kapatmak için dokunun' : 'Bildirimleri açmak için dokunun'}
          />
        </View>
      </View>
      
      {notificationEnabled && (
        <View style={styles.notificationSettingsContainer}>
          <View style={styles.notificationSettingsContent}>
            <Text style={styles.notificationSettingsLabel}>
              Ödemeden kaç gün önce bildirim gönderilsin?
            </Text>
              <View style={styles.notificationDaysContainer}>
              <TouchableOpacity 
                style={[
                  styles.stepperButton,
                  parseInt(notificationDaysBefore, 10) <= 0 && styles.stepperButtonDisabled
                ]}
                onPress={() => adjustNotificationDays('decrement')}
                disabled={parseInt(notificationDaysBefore, 10) <= 0}
                accessibilityLabel="Bildirim gün sayısını azalt"
                accessibilityHint="Bildirim gönderilecek gün sayısını bir azaltır"
              >
                <MaterialIcons 
                  name="remove" 
                  size={20} 
                  color={parseInt(notificationDaysBefore, 10) <= 0 ? Colors.GRAY_400 : Colors.PRIMARY} 
                />
              </TouchableOpacity>
              
              <View style={styles.notificationDaysInputContainer}>
                <TextInput
                  style={[getInputStyle('notificationDaysBefore'), styles.notificationDaysInput]}
                  value={notificationDaysBefore}
                  onChangeText={(value) => {
                    // Ensure number is between 0-30
                    const numValue = parseInt(value, 10);
                    if (value === '' || isNaN(numValue)) {
                      setNotificationDaysBefore(value);
                    } else {
                      const limitedValue = Math.min(Math.max(numValue, 0), 30);
                      setNotificationDaysBefore(String(limitedValue));
                    }
                  }}
                  onBlur={() => {
                    // When blurring, ensure value is valid
                    const numValue = parseInt(notificationDaysBefore, 10);
                    if (notificationDaysBefore === '' || isNaN(numValue)) {
                      setNotificationDaysBefore('3');
                    }
                    handleInputBlur('notificationDaysBefore');
                  }}
                  onFocus={() => handleInputFocus('notificationDaysBefore')}
                  placeholder="3"
                  keyboardType="number-pad"
                  maxLength={2}
                  textAlign="center"
                  accessibilityLabel="Bildirim gün sayısı"
                  accessibilityHint="0 ile 30 arasında bir değer girin"
                />
                <Text style={styles.notificationDaysLabel}>gün önce</Text>
              </View>
              
              <TouchableOpacity 
                style={[
                  styles.stepperButton,
                  parseInt(notificationDaysBefore, 10) >= 30 && styles.stepperButtonDisabled
                ]}
                onPress={() => adjustNotificationDays('increment')}
                disabled={parseInt(notificationDaysBefore, 10) >= 30}
                accessibilityLabel="Bildirim gün sayısını artır"
                accessibilityHint="Bildirim gönderilecek gün sayısını bir artırır"
              >
                <MaterialIcons 
                  name="add" 
                  size={20} 
                  color={parseInt(notificationDaysBefore, 10) >= 30 ? Colors.GRAY_400 : Colors.PRIMARY} 
                />
              </TouchableOpacity>
            </View>
            
            {getFieldErrorText('notificationDaysBefore', 'Geçerli bir bildirim gün sayısı girin (0-30)')}
              <View style={styles.notificationHelperTextContainer}>
              <MaterialIcons name="info-outline" size={14} color={Colors.GRAY_600} />
              <Text style={styles.notificationHelperText}>
                {parseInt(notificationDaysBefore, 10) === 0 
                  ? 'Bildirim ödeme günü gönderilecek'
                  : `Bildirim, ödemeden ${notificationDaysBefore} gün önce gönderilecek`}
              </Text>
            </View>
              <Text style={styles.notificationSettingsLabel}>
              Bildirim saati
            </Text>
            
            <TouchableOpacity 
              onPress={() => setShowTimePicker(true)}
              style={[
                styles.timePickerButton,
                handleInputFocus === 'notificationTime' && styles.timePickerButtonFocused
              ]}
              accessibilityLabel="Bildirim saatini değiştir"
              accessibilityHint="Bildirim gönderilecek saati seçmek için dokunun"
            >              <MaterialIcons name="access-time" size={18} color={Colors.PRIMARY} />
              <Text style={styles.timePickerText}>
                {safeFormatTime(notificationTime, 'HH:mm')}
              </Text>
              <View style={{ flexDirection: 'row', alignItems: 'center', marginLeft: 'auto' }}>
                <MaterialIcons name="touch-app" size={16} color={Colors.GRAY_500} />
                <Text style={{ fontSize: 12, color: Colors.GRAY_500, marginLeft: 4 }}>
                  Değiştirmek için dokunun
                </Text>
              </View>
            </TouchableOpacity>
            
            {showTimePicker && (
              <DateTimePicker
                testID="timePicker"
                value={notificationTime}
                mode="time"
                is24Hour={true}
                display="spinner"
                onChange={handleTimeChange}
                style={styles.dateTimePicker}
                textColor={Colors.GRAY_800}
                themeVariant="light"
              />
            )}
          </View>
        </View>
      )}
      
      <View style={styles.notificationTipsContainer}>        <View style={styles.notificationTipsHeader}>
          <MaterialIcons name="lightbulb" size={18} color={Colors.PRIMARY} />
          <Text style={styles.notificationTipsTitle}>Bildirimler Hakkında</Text>
        </View>
        
        <View style={styles.notificationTipsList}>
          <View style={styles.notificationTipItem}>
            <MaterialIcons name="info" size={16} color={Colors.PRIMARY} />
            <Text style={styles.notificationTipText}>
              Bildirimler belirlediğiniz saatte, ödeme tarihinden önce gönderilir.
            </Text>
          </View>
          
          <View style={styles.notificationTipItem}>
            <MaterialIcons name="info" size={16} color={Colors.PRIMARY} />
            <Text style={styles.notificationTipText}>
              Uygulamayı kaldırsanız bile zamanlanmış bildirimler gösterilecektir.
            </Text>
          </View>
          
          <View style={[styles.notificationTipItem, styles.notificationTipItemLast]}>
            <MaterialIcons name="info" size={16} color={Colors.PRIMARY} />
            <Text style={styles.notificationTipText}>
              Bildirimlere izin vermek için telefon ayarlarınızdan uygulamaya bildirim izni vermelisiniz.
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  // Ana bölüm stili - modern kart görünümü
  section: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 20,
    padding: screenWidth < 375 ? 16 : 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 4,
    borderLeftWidth: 4,
    borderLeftColor: Colors.PRIMARY,
  },
  
  // Bölüm başlığı
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.GRAY_800,
    marginBottom: 20,
    letterSpacing: 0.5,
  },
  
  // Bildirim switch konteynırı - modern görünüm
  notificationSwitchContainer: {
    backgroundColor: Colors.PRIMARY + '08',
    borderRadius: 16,
    padding: screenWidth < 375 ? 16 : 20,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: Colors.PRIMARY + '20',
  },
    notificationSwitchContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  
  // Bildirim ikon konteynırı
  notificationIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.PRIMARY + '15',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  
  // Bildirim metin konteynırı
  notificationTextContainer: {
    flex: 1,
    marginRight: 16,
  },
  
  notificationTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.GRAY_800,
    marginBottom: 6,
    letterSpacing: 0.3,
  },
  
  notificationDescription: {
    fontSize: 14,
    color: Colors.GRAY_600,
    lineHeight: 20,
    letterSpacing: 0.2,
  },
  
  // Bildirim ayarları ana konteynır
  notificationSettingsContainer: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: screenWidth < 375 ? 16 : 20,
    marginBottom: 20,
  },
  
  notificationSettingsContent: {
    gap: 20,
  },
  
  notificationSettingsLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.GRAY_800,
    marginBottom: 12,
    letterSpacing: 0.2,
  },
  
  // Gün sayısı konteynırı - modern stepper tasarımı
  notificationDaysContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  
  // Stepper butonları - modern tasarım
  stepperButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Colors.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.PRIMARY,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 3,
    transform: [{ scale: 1 }],
  },
  
  stepperButtonDisabled: {
    backgroundColor: Colors.GRAY_300,
    shadowOpacity: 0,
    elevation: 0,
    transform: [{ scale: 0.95 }],
  },
  
  // Gün input konteynırı
  notificationDaysInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 20,
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
    notificationDaysInput: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.PRIMARY,
    textAlign: 'center',
    minWidth: 60,
    backgroundColor: 'transparent',
    borderWidth: 0,
    padding: 0,
    margin: 0,
  },
  
  notificationDaysLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.GRAY_700,
    marginLeft: 12,
    letterSpacing: 0.2,
  },
  
  // Yardımcı metin konteynırı
  notificationHelperTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.PRIMARY + '05',
    padding: 12,
    borderRadius: 8,
    marginBottom: 20,
  },
  
  // Yardımcı metin
  notificationHelperText: {
    fontSize: 14,
    color: Colors.GRAY_600,
    textAlign: 'center',
    fontStyle: 'italic',
    lineHeight: 20,
    marginLeft: 8,
    flex: 1,
  },
  
  // Saat seçici butonu
  timePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: Colors.GRAY_200,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
    minHeight: 56,
  },
  
  timePickerButtonFocused: {
    borderColor: Colors.PRIMARY,
    backgroundColor: Colors.PRIMARY + '05',
    shadowColor: Colors.PRIMARY,
    shadowOpacity: 0.15,
    transform: [{ scale: 1.02 }],
  },
  
  timePickerText: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.GRAY_800,
    marginLeft: 12,
  },
  
  // DateTime picker
  dateTimePicker: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 20,
    overflow: 'hidden',
  },
  
  // İpuçları konteynırı - modern kart tasarımı
  notificationTipsContainer: {
    backgroundColor: '#fff',
    borderRadius: 16,
    marginTop: 20,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 4,
    borderLeftWidth: 4,
    borderLeftColor: Colors.WARNING,
  },
  
  notificationTipsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.WARNING + '08',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.WARNING + '15',
  },
  
  notificationTipsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.GRAY_800,
    marginLeft: 12,
    letterSpacing: 0.2,
  },
  
  notificationTipsList: {
    padding: screenWidth < 375 ? 16 : 20,
  },
  
  notificationTipItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_100,
  },
  
  // Son öğe için border kaldır
  notificationTipItemLast: {
    marginBottom: 0,
    paddingBottom: 0,
    borderBottomWidth: 0,
  },
  
  notificationTipText: {
    fontSize: 14,
    color: Colors.GRAY_700,
    marginLeft: 12,
    flex: 1,
    lineHeight: 22,
    letterSpacing: 0.2,
  },
});

export default RegularIncomeNotificationSettings;
