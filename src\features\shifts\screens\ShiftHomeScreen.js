import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../../../constants/colors';
import { useAppContext } from '../../../context/AppContext';
import { shiftStyles } from '../styles/shiftStyles';
import { formatDate } from '../utils/shiftUtils';
import * as shiftService from '../services/shiftService';
import ShiftItem from '../components/ShiftItem';
import ShiftFormModal from '../components/ShiftFormModal';

/**
 * Vardiya Ana Ekranı
 * 
 * Bu ekran, vardiya takibi modülünün ana ekranıdır.
 * Aktif vardiyaları, bugünkü vardiyaları ve yaklaşan vardiyaları gösterir.
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @returns {JSX.Element} Vardiya ana ekranı
 */
const ShiftHomeScreen = ({ navigation }) => {
  const db = useSQLiteContext();
  const insets = useSafeAreaInsets();
  const { theme } = useAppContext();
  
  // Durum değişkenleri
  const [loading, setLoading] = useState(true);
  const [activeShifts, setActiveShifts] = useState([]);
  const [todayShifts, setTodayShifts] = useState([]);
  const [upcomingShifts, setUpcomingShifts] = useState([]);
  const [shiftTypes, setShiftTypes] = useState([]);
  const [settings, setSettings] = useState(null);
  
  // Modal durumları
  const [showAddShiftModal, setShowAddShiftModal] = useState(false);
  
  // Verileri yükle
  useEffect(() => {
    loadData();
  }, []);
  
  // Verileri yükle
  const loadData = async () => {
    try {
      setLoading(true);
      
      // Vardiya türlerini yükle
      const types = await shiftService.getShiftTypes(db);
      setShiftTypes(types);
      
      // Ayarları yükle
      const workSettings = await shiftService.getWorkSettings(db);
      setSettings(workSettings);
      
      // Aktif vardiyaları yükle
      const active = await shiftService.getActiveShifts(db);
      setActiveShifts(active);
      
      // Bugünkü vardiyaları yükle
      const today = new Date().toISOString().split('T')[0];
      const todayData = await shiftService.getShiftsByDate(db, today);
      setTodayShifts(todayData);
      
      // Yaklaşan vardiyaları yükle
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      const tomorrowStr = tomorrow.toISOString().split('T')[0];
      
      const nextWeek = new Date();
      nextWeek.setDate(nextWeek.getDate() + 7);
      const nextWeekStr = nextWeek.toISOString().split('T')[0];
      
      const upcoming = await shiftService.getShiftsByDateRange(db, tomorrowStr, nextWeekStr);
      setUpcomingShifts(upcoming.slice(0, 5)); // Sadece ilk 5 vardiyayı göster
      
      setLoading(false);
    } catch (error) {
      console.error('Veri yükleme hatası:', error);
      Alert.alert('Hata', 'Veriler yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  };
  
  // Vardiya ekle/güncelle
  const handleSaveShift = async (id, shiftData) => {
    try {
      if (id) {
        // Vardiyayı güncelle
        await shiftService.updateShift(db, id, shiftData);
        Alert.alert('Başarılı', 'Vardiya başarıyla güncellendi.');
      } else {
        // Yeni vardiya ekle
        await shiftService.addShift(db, shiftData);
        Alert.alert('Başarılı', 'Yeni vardiya başarıyla eklendi.');
      }
      
      // Verileri yeniden yükle
      loadData();
    } catch (error) {
      console.error('Vardiya kaydetme hatası:', error);
      Alert.alert('Hata', 'Vardiya kaydedilirken bir hata oluştu.');
    }
  };
  
  // Vardiya detayına git
  const handleShiftPress = (shift) => {
    navigation.navigate('ShiftDetail', { shiftId: shift.id });
  };
  
  // Vardiya türünü bul
  const getShiftType = (shiftTypeId) => {
    return shiftTypes.find(type => type.id === shiftTypeId) || null;
  };
  
  if (loading) {
    return (
      <View style={[shiftStyles.loadingContainer, { paddingTop: insets.top }]}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={shiftStyles.loadingText}>Vardiyalar yükleniyor...</Text>
      </View>
    );
  }
  
  return (
    <View style={[styles.container, { paddingTop: insets.top, backgroundColor: theme.BACKGROUND }]}>
      {/* Başlık */}
      <View style={[styles.header, { backgroundColor: theme.PRIMARY }]}>
        <Text style={[styles.headerTitle, { color: theme.WHITE }]}>Vardiya Takibi</Text>
        <TouchableOpacity
          style={styles.headerButton}
          onPress={() => navigation.navigate('ShiftSettings')}
        >
          <MaterialIcons name="settings" size={24} color={theme.WHITE} />
        </TouchableOpacity>
      </View>
      
      <ScrollView style={styles.content}>
        {/* Aktif Vardiyalar */}
        {activeShifts.length > 0 && (
          <View style={styles.activeShiftBanner}>
            <View style={styles.activeShiftInfo}>
              <MaterialIcons name="timer" size={32} color="#fff" />
              <View style={styles.activeShiftTextContainer}>
                <Text style={styles.activeShiftLabel}>Aktif Vardiya</Text>
                <Text style={styles.activeShiftText}>
                  {activeShifts[0].start_time.substring(0, 5)} başladı
                </Text>
              </View>
            </View>
            
            <TouchableOpacity
              style={styles.endShiftButton}
              onPress={() => handleShiftPress(activeShifts[0])}
            >
              <MaterialIcons name="timer-off" size={16} color={Colors.WARNING} />
              <Text style={styles.endShiftButtonText}>Bitir</Text>
            </TouchableOpacity>
          </View>
        )}
        
        {/* Hızlı Vardiya Ekle */}
        <TouchableOpacity
          style={styles.addShiftButton}
          onPress={() => setShowAddShiftModal(true)}
        >
          <MaterialIcons name="add" size={24} color="#fff" />
          <Text style={styles.addShiftButtonText}>Yeni Vardiya Ekle</Text>
        </TouchableOpacity>
        
        {/* Bugünkü Vardiyalar */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Bugünkü Vardiyalar</Text>
            <TouchableOpacity
              style={styles.sectionButton}
              onPress={() => navigation.navigate('ShiftList')}
            >
              <Text style={styles.sectionButtonText}>Tümünü Gör</Text>
              <MaterialIcons name="chevron-right" size={16} color={Colors.PRIMARY} />
            </TouchableOpacity>
          </View>
          
          {todayShifts.length > 0 ? (
            todayShifts.map(shift => (
              <ShiftItem
                key={shift.id}
                shift={shift}
                shiftType={getShiftType(shift.shift_type_id)}
                onPress={() => handleShiftPress(shift)}
              />
            ))
          ) : (
            <View style={styles.emptyState}>
              <MaterialIcons name="event-busy" size={48} color={Colors.GRAY_400} />
              <Text style={styles.emptyStateText}>Bugün için vardiya yok</Text>
            </View>
          )}
        </View>
        
        {/* Yaklaşan Vardiyalar */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Yaklaşan Vardiyalar</Text>
            <TouchableOpacity
              style={styles.sectionButton}
              onPress={() => navigation.navigate('ShiftList')}
            >
              <Text style={styles.sectionButtonText}>Tümünü Gör</Text>
              <MaterialIcons name="chevron-right" size={16} color={Colors.PRIMARY} />
            </TouchableOpacity>
          </View>
          
          {upcomingShifts.length > 0 ? (
            upcomingShifts.map(shift => (
              <ShiftItem
                key={shift.id}
                shift={shift}
                shiftType={getShiftType(shift.shift_type_id)}
                onPress={() => handleShiftPress(shift)}
              />
            ))
          ) : (
            <View style={styles.emptyState}>
              <MaterialIcons name="event-busy" size={48} color={Colors.GRAY_400} />
              <Text style={styles.emptyStateText}>Yaklaşan vardiya yok</Text>
            </View>
          )}
        </View>
        
        {/* Hızlı Erişim Butonları */}
        <View style={styles.quickAccessContainer}>
          <TouchableOpacity
            style={styles.quickAccessButton}
            onPress={() => navigation.navigate('ShiftTypes')}
          >
            <MaterialIcons name="category" size={24} color={Colors.PRIMARY} />
            <Text style={styles.quickAccessText}>Vardiya Türleri</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.quickAccessButton}
            onPress={() => navigation.navigate('ShiftSchedule')}
          >
            <MaterialIcons name="schedule" size={24} color={Colors.PRIMARY} />
            <Text style={styles.quickAccessText}>Vardiya Planlaması</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
      
      {/* Vardiya Ekleme Modalı */}
      <ShiftFormModal
        visible={showAddShiftModal}
        onClose={() => setShowAddShiftModal(false)}
        onSave={handleSaveShift}
        shiftTypes={shiftTypes}
        settings={settings}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...shiftStyles.container,
  },
  header: {
    ...shiftStyles.header,
    justifyContent: 'space-between',
  },
  headerTitle: {
    ...shiftStyles.headerTitle,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  activeShiftBanner: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: Colors.WARNING,
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  activeShiftInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  activeShiftTextContainer: {
    marginLeft: 12,
    flex: 1,
  },
  activeShiftLabel: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  activeShiftText: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 14,
    marginTop: 2,
  },
  endShiftButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
  },
  endShiftButtonText: {
    color: Colors.WARNING,
    fontWeight: 'bold',
    fontSize: 14,
    marginLeft: 4,
  },
  addShiftButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  addShiftButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 8,
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  sectionButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionButtonText: {
    fontSize: 14,
    color: Colors.PRIMARY,
    marginRight: 4,
  },
  emptyState: {
    ...shiftStyles.emptyState,
    padding: 24,
  },
  emptyStateText: {
    ...shiftStyles.emptyStateText,
  },
  quickAccessContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  quickAccessButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    marginHorizontal: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  quickAccessText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 8,
  },
});

export default ShiftHomeScreen;
